package negocio.facade.jdbc.basico.webservice;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.atualizadb.processo.AplicarMatriculaImportacao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.controle.json.sesice.TiposNecessidadesEspeciaisSesiCeEnum;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.integracao.importacao.AcessoClienteJSON;
import br.com.pactosolucoes.integracao.importacao.ClienteJSON;
import br.com.pactosolucoes.integracao.importacao.ContratoJSON;
import br.com.pactosolucoes.integracao.importacao.ContratoOperacaoJSON;
import br.com.pactosolucoes.integracao.importacao.ModalidadeJSON;
import br.com.pactosolucoes.integracao.importacao.PagamentoJSON;
import br.com.pactosolucoes.integracao.importacao.TelefoneJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.basico.ClienteControle;
import controle.contrato.CancelamentoContratoControle;
import controle.contrato.ContratoControle;
import controle.contrato.RetornoTrancamentoContratoControle;
import controle.financeiro.MovParcelaControle;
import controle.contrato.TrancamentoContratoControle;
import importador.UteisImportacao;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.GrauInstrucaoVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.DadoResumoPeriodoEnum;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.*;
import negocio.comuns.crm.optin.OptinVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Categoria;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Endereco;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.GrauInstrucao;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.basico.Pais;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Profissao;
import negocio.facade.jdbc.basico.Questionario;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.basico.webservice.sesice.AlunoSesiCeJSON;
import negocio.facade.jdbc.basico.webservice.sesice.TiposStatusClienteSesiCeEnum;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.JustificativaOperacao;
import negocio.facade.jdbc.crm.optin.Optin;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.plano.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.basico.ResumoPeriodoCallable;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servlet.caixaemaberto.PagamentoDTO;

import java.sql.Array;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IntegracaoImportacao extends IntegracaoCadastros {


    public IntegracaoImportacao() throws Exception {
    }

    public IntegracaoImportacao(Connection conexao) throws Exception {
        super(conexao);
    }

    public PaisVO obterPais(Connection con, ClienteJSON clienteJSON) throws Exception {
        Pais paisDAO = new Pais(con);
        String pais = "BRASIL";
        if (clienteJSON.getEndereco() != null) {
            if (!UteisValidacao.emptyString(clienteJSON.getEndereco().getPais())) {
                pais = clienteJSON.getEndereco().getPais();
            }
        }
        return paisDAO.consultarPorNome(pais, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    public EstadoVO obterEstado(Connection con, String uf, PaisVO paisVO) throws Exception {
        Estado estadoDao = new Estado(con);
        EstadoVO estadoVO = estadoDao.consultarPorSiglaUf(uf, paisVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        estadoDao = null;
        return estadoVO;
    }

    public CidadeVO obterCidade(Connection con, ClienteJSON clienteJSON, EstadoVO estadoVO, String uf) throws Exception {
        Cidade cidadeDao = new Cidade(con);
        CidadeVO cidadeVO = new CidadeVO();
        String cidade = clienteJSON.getEndereco() == null ? "" : clienteJSON.getEndereco().getCidade();
        if (!UteisValidacao.emptyString(uf) && !UteisValidacao.emptyString(cidade)) {
            cidadeVO = cidadeDao.consultarPorNomeCidadeSiglaEstado(cidade, uf);
        } else if (!UteisValidacao.emptyString(cidade)) {
            cidadeVO = cidadeDao.consultarPorNome(cidade, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }
        if (!UteisValidacao.emptyString(uf) && !UteisValidacao.emptyString(cidade) && UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {

            if (!UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                cidadeVO = new CidadeVO();
                cidadeVO.setEstado(estadoVO);
                cidadeVO.setPais(new PaisVO());
                cidadeVO.getPais().setCodigo(estadoVO.getPais());
                cidadeVO.setNome(cidade);
                cidadeDao.incluir(cidadeVO);
            }
        }
        cidadeDao = null;
        return cidadeVO;
    }

    public List<Integer> codigoPessoaCpf(String cpf) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from pessoa where cfp = '" + cpf + "' or cfp = '" + Uteis.removerMascara(cpf) + "'", con);
        List<Integer> codigos = new ArrayList<Integer>();
        int cont = 0;
        int codigopessoa = 0;
        boolean utilizaConfiguracaoSesc = false;

        ResultSet rsConfiguracaoSistema = SuperFacadeJDBC.criarConsulta("SELECT * FROM ConfiguracaoSistema WHERE codigo = 1", con);
        while (rsConfiguracaoSistema.next()) {
            utilizaConfiguracaoSesc = rsConfiguracaoSistema.getBoolean("sesc");
        }

        while (rs.next()) {
            cont++;
            codigopessoa = rs.getInt("codigo");

            if (utilizaConfiguracaoSesc == true) {
                codigos.add(codigopessoa);
            }
        }
        if (cont == 0) {
            throw new Exception("Não foi encontrado aluno com esse CPF.");
        } else if (utilizaConfiguracaoSesc == false) {
            throw new Exception("Unidade informada não utiliza configuração SESC, entre em contato com o suporte.");
        }
        return codigos;
    }

    public List<Integer> codigoPessoaCpfMatriculaSesc(String cpf, String matricula) throws Exception {
        boolean utilizaConfiguracaoSesc = false;

        try (ResultSet rsConfiguracaoSistema = SuperFacadeJDBC.criarConsulta("SELECT sesc FROM ConfiguracaoSistema WHERE codigo = 1", con)) {
            while (rsConfiguracaoSistema.next()) {
                utilizaConfiguracaoSesc = rsConfiguracaoSistema.getBoolean("sesc");
            }
            if (!utilizaConfiguracaoSesc) {
                throw new Exception("Unidade informada no utiliza configurao SESC, entre em contato com o suporte.");
            }
        }

        StringBuilder sbQuery = new StringBuilder();
        sbQuery.append("SELECT p.codigo FROM cliente c\n");
        sbQuery.append("inner join pessoa p on p.codigo = c.pessoa\n");
        if (!UteisValidacao.emptyString(cpf)) {
            sbQuery.append("WHERE p.cfp = ?\n");
        } else if (!UteisValidacao.emptyString(matricula)) {
            sbQuery.append("WHERE c.matriculasesc = ?\n");
        } else {
            throw new Exception("CPF ou Matricula devem ser informados");
        }

        try (PreparedStatement stmt = con.prepareStatement(sbQuery.toString())) {
            int i = 0;

            if (!UteisValidacao.emptyString(cpf)) {
                stmt.setString(++i, cpf);
            } else if (!UteisValidacao.emptyString(matricula)) {
                stmt.setString(++i, matricula);
            }

            try (ResultSet rs = stmt.executeQuery()) {
                List<Integer> codigos = new ArrayList<>();
                while (rs.next()) {
                    codigos.add(rs.getInt("codigo"));
                }
                if (codigos.isEmpty()) {
                    throw new Exception("Não foi encontrado aluno com esse CPF ou matrícula externa.");
                }
                return codigos;
            }
        }
    }

    public JSONObject persistirClienteJSONImportacao(final ClienteJSON clienteJSON, final Integer empresa, final boolean forcarEmpresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        try {
            con.setAutoCommit(false);
            Integer clienteCod = null;
            Integer pessoaCod = null;
            String matricula = null;
            String codacessoalternativo = null;
            Integer codigoMatricula = null;

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            empresaDao = null;
//            if (!empresaVO.isEmImportacao() && !UteisValidacao.emptyNumber(clienteJSON.getIdExterno()) && !forcarEmpresa) {
//                objRetorno.put("status", "error");
//                objRetorno.put("codigoRegistroZW", 0);
//                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importaç");
//                return objRetorno;
//            }

            String uf = clienteJSON.getEndereco() == null ? "" : clienteJSON.getEndereco().getUf();
            PaisVO paisVO = obterPais(con, clienteJSON);
            EstadoVO estadoVO = obterEstado(con, uf, paisVO);
            CidadeVO cidadeVO = obterCidade(con, clienteJSON, estadoVO, uf);

            PessoaVO pessoaVO = new PessoaVO();

            for (TelefoneJSON telJSON : clienteJSON.getTelefones()) {
                if (telJSON.getTipo().equals("CELULAR") && telJSON.getNumero().equals("")) {
                    telJSON.setNumero("(99)99999999");
                }
                TipoTelefoneEnum tipoTelefoneEnum = TipoTelefoneEnum.obterPorSigla(telJSON.getTipo());
                povoarTelefonePessoa(pessoaVO.getTelefoneVOs(), telJSON.getNumero(), tipoTelefoneEnum, telJSON.getDescricao());
            }

            povoarEndereco(pessoaVO.getEnderecoVOs(), clienteJSON.getEndereco().getLogradouro(),
                    clienteJSON.getEndereco().getComplemento(),
                    clienteJSON.getEndereco().getNumero(),
                    clienteJSON.getEndereco().getBairro(),
                    clienteJSON.getEndereco().getCep());

            pessoaVO.setNome(clienteJSON.getNomeCompleto());
            pessoaVO.setNacionalidade(clienteJSON.getNacionalidade());
            pessoaVO.setNaturalidade(clienteJSON.getNaturalidade());
            pessoaVO.setRg(clienteJSON.getRg());
            pessoaVO.setRgUf(clienteJSON.getRguf());
            pessoaVO.setRgOrgao(clienteJSON.getOrgaoEmissaoRG());
            pessoaVO.setCidade(cidadeVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setIdExterno(clienteJSON.getIdExterno());
            try {
                pessoaVO.setDataCadastro(Uteis.getDate(clienteJSON.getDataCadastro()));
            } catch (Exception ignored) {
                pessoaVO.setDataCadastro(Calendario.hoje());
            }

            pessoaVO.setNomeMae(clienteJSON.getNomeMae());
            pessoaVO.setNomePai(clienteJSON.getNomePai());
            pessoaVO.setEstadoCivil(clienteJSON.getEstadoCivil());
            pessoaVO.setPais(cidadeVO == null || cidadeVO.getPais() == null ? new PaisVO() : cidadeVO.getPais());
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(clienteJSON.getEmail());
            emailVO.setEmailCorrespondencia(true);
            pessoaVO.getEmailVOs().add(emailVO);
            pessoaVO.setEmail(clienteJSON.getEmail());

            pessoaVO.setCfp(clienteJSON.getCpf());

            pessoaVO.setSexo(clienteJSON.getSexo());
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

            pessoaVO.setDataNasc(obterDataNascimento(clienteJSON));
            pessoaVO.setCpfMae(clienteJSON.getCpfMae());
            pessoaVO.setCpfPai(clienteJSON.getCpfPai());

            ClienteVO clienteVO = new ClienteVO();

            ColaboradorVO colaboradorVO = obterConsultor(clienteJSON.getConsultor(), empresaVO, !UteisValidacao.emptyNumber(clienteJSON.getIdExterno()));
            if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                adicionarConsultor(clienteVO, colaboradorVO);
            }
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);
            clienteVO.setSesc(clienteJSON.isSesc());
            clienteVO.setCategoria(obterCategoria(clienteJSON.getCategoria()));
            clienteVO.setMatriculaExterna(clienteJSON.getMatriculaExterna());

            if (clienteJSON.getCategoria().equals("")) {
                clienteVO.setCategoria(obterCategoria("AL"));
            } else {
                clienteVO.setCategoria(obterCategoria(clienteJSON.getCategoria()));
            }

            clienteVO.setMatriculaExterna(clienteJSON.getMatriculaExterna());

            if (!UteisValidacao.emptyString(clienteJSON.getCodigoAcessoAlternativo())) {
                clienteVO.setCodAcessoAlternativo(clienteJSON.getCodigoAcessoAlternativo());
            }
            if (!UteisValidacao.emptyString(codacessoalternativo)) {
                clienteVO.setCodAcessoAlternativo(codacessoalternativo);
            }


            if (UteisValidacao.emptyNumber(clienteCod)) {
                Cliente cliente = new Cliente(con);
                cliente.gerarNumeroMatricula(clienteVO, empresaVO, null);
                incluirClienteImportacao(clienteVO);
                cliente = null;
            } else {
                clienteVO.setCodigoMatricula(codigoMatricula);
                clienteVO.setMatricula(matricula);
                clienteVO.setCodigo(clienteCod);
                clienteVO.getPessoa().setCodigo(pessoaCod);
                alterarClienteImportacao(clienteVO);
            }
            if (clienteJSON.getClassificacoes() != null) {
                incluirClassificacao(clienteVO.getCodigo(), clienteJSON.getClassificacoes());
            }

            incluirVinculo(clienteVO, clienteVO.getVinculoVOs());

            Usuario usuarioDao = new Usuario(con);
            UsuarioVO usuarioAdmin = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            usuarioDao = null;

            con.commit();
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", clienteVO.getCodigo());
            objRetorno.put("mensagem", "Cliente cadastrado com sucesso");

            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }


    public JSONObject persistirClienteJSON(final String chave, final String json, final Integer empresa) throws Exception {
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json, "IntegracaoImportacao.persistirClienteJSON");
        ClienteJSON clienteJSON = new ClienteJSON(new JSONObject(json));
        String resultado = "";
        try {
            JSONObject objRetorno = persistirClienteJSON(chave, clienteJSON, empresa, false);
            resultado = objRetorno.toString();
            return objRetorno;
        } catch (Exception e) {
            resultado = e.getMessage();
            throw e;
        } finally {
            alterarLogIntegracao(logIntegracoesVO, resultado);
        }

    }

    public JSONObject persistirClienteJSON(String chave, final ClienteJSON clienteJSON, final Integer empresa, final boolean forcarEmpresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        try {
            con.setAutoCommit(false);
            Integer clienteCod = null;
            Integer pessoaCod = null;
            String matricula = null;
            String codacessoalternativo = null;
            Integer codigoMatricula = null;
            OptinVO optinVO = new OptinVO();
            Optin opt = new Optin(con);

            String cpfFormatado = Uteis.formatarCpfCnpj(clienteJSON.getCpf(), false);

            String condicao = clienteJSON.isSesc() ? " cfp = '" + cpfFormatado + "'" +
                    (UteisValidacao.emptyString(clienteJSON.getMatriculasesc()) ? "" : (" or matriculasesc = '" + clienteJSON.getMatriculasesc() + "' ")) :
                    " pessoa.idExterno = " + clienteJSON.getIdExterno() + " and cliente.empresa = " + empresa;
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigomatricula, matricula, pessoa, cliente.codigo as cliente, codacessoalternativo from cliente \n" +
                    "inner join pessoa on pessoa.codigo = cliente.pessoa where " + condicao, con);
            int cont = 0;
            while (rs.next()) {
                cont++;
                clienteCod = rs.getInt("cliente");
                if (!UteisValidacao.emptyNumber(clienteJSON.getIdExterno())) {
                    objRetorno.put("status", "warning");
                    objRetorno.put("codigoRegistroZW", clienteCod);
                    objRetorno.put("mensagem", "Cliente já importado");
                    return objRetorno;
                }
                pessoaCod = rs.getInt("pessoa");
                codigoMatricula = rs.getInt("codigomatricula");
                matricula = rs.getString("matricula");
                codacessoalternativo = rs.getString("codacessoalternativo");
            }

            if (cont > 1) {
                throw new Exception("Mais de uma pessoa com mesmo CPF " + clienteJSON.getCpf() +
                        (UteisValidacao.emptyString(clienteJSON.getMatriculasesc()) ? "" : (" ou matrícula do SESC " + clienteJSON.getMatriculasesc() + " "))
                        + ", verifique no ZW o problema.");
            }

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            empresaDao = null;
//            if (!empresaVO.isEmImportacao() && !UteisValidacao.emptyNumber(clienteJSON.getIdExterno()) && !forcarEmpresa) {
//                objRetorno.put("status", "error");
//                objRetorno.put("codigoRegistroZW", 0);
//                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
//                return objRetorno;
//            }
            String uf = clienteJSON.getEndereco() == null || clienteJSON.getEndereco().getUf() == null ? "" : clienteJSON.getEndereco().getUf();
            PaisVO paisVO = obterPais(con, clienteJSON);
            EstadoVO estadoVO = obterEstado(con, uf, paisVO);
            CidadeVO cidadeVO = obterCidade(con, clienteJSON, estadoVO, uf);

            validarCPF(clienteJSON);
            PessoaVO pessoaVO = new PessoaVO();

            for (TelefoneJSON telJSON : clienteJSON.getTelefones()) {
                TipoTelefoneEnum tipoTelefoneEnum = TipoTelefoneEnum.obterPorSigla(telJSON.getTipo());
                povoarTelefonePessoa(pessoaVO.getTelefoneVOs(), telJSON.getNumero(), tipoTelefoneEnum, telJSON.getDescricao());
            }

            povoarEndereco(pessoaVO.getEnderecoVOs(), clienteJSON.getEndereco().getLogradouro(),
                    clienteJSON.getEndereco().getComplemento(),
                    clienteJSON.getEndereco().getNumero(),
                    clienteJSON.getEndereco().getBairro(),
                    clienteJSON.getEndereco().getCep());

            pessoaVO.setNome(clienteJSON.getNomeCompleto());
            pessoaVO.setNacionalidade(clienteJSON.getNacionalidade());
            pessoaVO.setNaturalidade(clienteJSON.getNaturalidade());
            pessoaVO.setRg(clienteJSON.getRg());
            pessoaVO.setRgUf(clienteJSON.getRguf());
            pessoaVO.setRgOrgao(clienteJSON.getOrgaoEmissaoRG());
            pessoaVO.setCidade(cidadeVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setIdExterno(clienteJSON.getIdExterno());
            try {
                pessoaVO.setDataCadastro(Uteis.getDate(clienteJSON.getDataCadastro()));
            } catch (Exception ignored) {
                pessoaVO.setDataCadastro(Calendario.hoje());
            }

            if (!UteisValidacao.emptyString(clienteJSON.getProfissao())) {
                pessoaVO.setProfissao(obterProfissao(clienteJSON.getProfissao().trim().toUpperCase()));
            }
            pessoaVO.setNomePai(clienteJSON.getNomePai());
            pessoaVO.setNomeMae(clienteJSON.getNomeMae());
            if (!UteisValidacao.emptyString(clienteJSON.getCpfMae())){
                pessoaVO.setCpfMae(Uteis.formatarCpfCnpj(clienteJSON.getCpfMae(), false));
            }
            if (!UteisValidacao.emptyString(clienteJSON.getCpfPai())){
                pessoaVO.setCpfPai(Uteis.formatarCpfCnpj(clienteJSON.getCpfPai(), false));
            }

            if(!UteisValidacao.emptyString(clienteJSON.getResponsavelFinanceiro())) {
                pessoaVO.setNomeRespFinanceiro(clienteJSON.getResponsavelFinanceiro());
            }
            if (!UteisValidacao.emptyString(clienteJSON.getCpfResponsavel())){
                pessoaVO.setCpfRespFinanceiro(clienteJSON.getCpfResponsavel());
            }
            pessoaVO.setEstadoCivil(clienteJSON.getEstadoCivil());
            pessoaVO.setPais(cidadeVO == null || cidadeVO.getPais() == null ? new PaisVO() : cidadeVO.getPais());
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(clienteJSON.getEmail());
            emailVO.setEmailCorrespondencia(true);
            pessoaVO.getEmailVOs().add(emailVO);
            pessoaVO.setEmail(clienteJSON.getEmail());

            pessoaVO.setCfp(cpfFormatado);

            pessoaVO.setSexo(clienteJSON.getSexo());
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

            pessoaVO.setDataNasc(obterDataNascimento(clienteJSON));

            if (!UteisValidacao.emptyString(clienteJSON.getEscolaridade())) {
                GrauInstrucao grauInstrucaoDao = new GrauInstrucao(con);
                List<GrauInstrucaoVO> list = grauInstrucaoDao.consultarPorDescricao(clienteJSON.getEscolaridade(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                GrauInstrucaoVO grauInstrucaoVO = new GrauInstrucaoVO();
                if (UteisValidacao.emptyList(list)) {
                    grauInstrucaoVO.setDescricao(clienteJSON.getEscolaridade());
                    grauInstrucaoDao.incluir(grauInstrucaoVO);
                } else {
                    grauInstrucaoVO = list.get(0);
                }
                pessoaVO.setGrauInstrucao(grauInstrucaoVO);
                grauInstrucaoDao = null;
            }

            ClienteVO clienteVO = new ClienteVO();
            ColaboradorVO colaboradorVO = obterConsultor(clienteJSON.getConsultor(), empresaVO, !UteisValidacao.emptyNumber(clienteJSON.getIdExterno()));
            if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                adicionarConsultor(clienteVO, colaboradorVO);
            }
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);
            clienteVO.setSesc(clienteJSON.isSesc());
            clienteVO.setCategoria(obterCategoria(clienteJSON.getCategoria()));
            clienteVO.setMatriculaExterna(clienteJSON.getMatriculaExterna());

            if (!UteisValidacao.emptyString(clienteJSON.getCodigoAcessoAlternativo())) {
                clienteVO.setCodAcessoAlternativo(clienteJSON.getCodigoAcessoAlternativo());
            }
            if (!UteisValidacao.emptyString(codacessoalternativo)) {
                clienteVO.setCodAcessoAlternativo(codacessoalternativo);
            }

            if (clienteJSON.isSesc()) {
                if (!UteisValidacao.emptyString(clienteJSON.getValidadeCartaoSesc())) {
                    try {
                        clienteVO.setDataValidadeCarteirinha(Uteis.getDate(clienteJSON.getValidadeCartaoSesc()));
                    } catch (Exception ex) {
                        throw new Exception("Data de validade cartão sesc inválida.");
                    }
                }
                clienteVO.setMatriculaSesc(clienteJSON.getMatriculasesc());
                clienteVO.setRenda(clienteJSON.getRenda());
                clienteVO.setNomeSocial(clienteJSON.getNomeSocial());
            }


            if (UteisValidacao.emptyNumber(clienteCod)) {
                Cliente cliente = new Cliente(con);
                boolean existeCliente = false;
                do {
                    cliente.gerarNumeroMatricula(clienteVO, empresaVO, null);
                    existeCliente = cliente.existeClientePorCodigoMatricula(clienteVO.getCodigoMatricula());
                    if (existeCliente) {
                        cliente.corrigirNumeroMatricula();
                        clienteVO.setMatricula("");
                        clienteVO.setCodigoMatricula(0);
                    }
                } while (existeCliente);

                incluirClienteImportacao(clienteVO);
                if(clienteVO.getSesc()){
                    //se não enviar o e-mail, cria um padrão, para ser atulizado posteriormente.
                    optinVO.setCliente(clienteVO);
                    optinVO.setEmpresa(empresaVO);
                    optinVO.setEmail( Util.isEmptyString( clienteVO.getPessoa().getEmail())  ? "<EMAIL>" : clienteVO.getPessoa().getEmail());
                    optinVO.setBloqueadoBounce(!clienteJSON.isOptin());
                    opt.incluir(optinVO);
                }
                cliente = null;
            } else {
                clienteVO.setCodigoMatricula(codigoMatricula);
                clienteVO.setMatricula(matricula);
                clienteVO.setCodigo(clienteCod);
                clienteVO.getPessoa().setCodigo(pessoaCod);
                alterarClienteImportacao(clienteVO);
                atualizaoptin(clienteJSON.isOptin(), optinVO, opt, empresaVO.getCodigo(), clienteVO, clienteVO.getPessoa().getEmail());
            }
            if (clienteJSON.getClassificacoes() != null) {
                incluirClassificacao(clienteVO.getCodigo(), clienteJSON.getClassificacoes());
            }

            incluirVinculo(clienteVO, clienteVO.getVinculoVOs());
            if (UteisValidacao.emptyNumber(clienteCod) && colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
                Questionario questionario = new Questionario(con);
                QuestionarioVO questionarioVO = questionario.consultarPorDescricao("BV MATRICULA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                questionarioClienteVO.setCliente(clienteVO);
                questionarioClienteVO.setResponsavel(new UsuarioVO());
                questionarioClienteVO.getResponsavel().setNome("Administrador");
                questionarioClienteVO.setQuestionario(questionarioVO);
                questionarioClienteVO.setConsultor(colaboradorVO);
                questionarioClienteVO.setData(clienteVO.getPessoa().getDataCadastro());
                questionarioClienteVO.setTipoBV(TipoBVEnum.MA);

                questionarioClienteVO.setQuestionarioPerguntaClienteVOs(new ArrayList());
                for (QuestionarioPerguntaVO questionarioPerguntaVO : questionarioVO.getQuestionarioPerguntaVOs()) {
                    // criar as perguntas do questionario do cliente
                    PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
                    perguntaClienteVO.setTipoPergunta(questionarioPerguntaVO.getPergunta().getTipoPergunta());
                    perguntaClienteVO.setDescricao(questionarioPerguntaVO.getPergunta().getDescricao());
                    perguntaClienteVO.setMultipla(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("ME"));
                    perguntaClienteVO.setSimples(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("SE") || questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("SN"));
                    perguntaClienteVO.setTextual(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("TE"));

                    perguntaClienteVO.setRespostaPergClienteVOs(new ArrayList());
                    for (RespostaPerguntaVO respostaPerguntaVO : questionarioPerguntaVO.getPergunta().getRespostaPerguntaVOs()) {
                        // criar as respostas das perguntas do questionario do cliente
                        RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
                        respostaPergClienteVO.setRespostaOpcao(false);
                        respostaPergClienteVO.setDescricaoRespota(respostaPerguntaVO.getDescricaoRespota());
                        perguntaClienteVO.getRespostaPergClienteVOs().add(respostaPergClienteVO);
                    }
                    // criar o questionario do cliente.
                    QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
                    questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioClienteVO.getCodigo());
                    questionarioPerguntaClienteVO.setPerguntaCliente(perguntaClienteVO);
                    questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(questionarioPerguntaClienteVO);
                }

                QuestionarioCliente questionarioCliente = new QuestionarioCliente(con);
                questionarioCliente.incluirSemComit(questionarioClienteVO, false, false);
                questionarioCliente = null;
                questionario = null;
            }


            try {
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zwFacade = null;
            } catch (SinteticoException se) {
                Uteis.logar(null, "Problema ao processar sintético do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresaVO.getNome());
            }

            Usuario usuarioDao = new Usuario(con);
            UsuarioVO usuarioAdmin = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            usuarioDao = null;

            Log logDao = new Log(con);
            logDao.incluirLogInclusaoClienteImportacao(clienteVO.getCodigo(), usuarioAdmin);
            logDao = null;


            con.commit();
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", clienteVO.getCodigo());
            objRetorno.put("mensagem", "Cliente cadastrado com sucesso");

            try {
                if(chave == null){
                    chave = DAO.resolveKeyFromConnection(con);
                }
                UsuarioMovel usuarioMovelDao = new UsuarioMovel(con);
                usuarioMovelDao.verificarEnvioAlunoParaTW(chave, clienteVO);
            }catch (Exception e){
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ERRO persistirClienteJSON - " + e.getMessage());
                Uteis.logar(e, IntegracaoImportacao.class);
            }

            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void atualizaoptin(Boolean optin, OptinVO optinVO, Optin opt, Integer empresa, ClienteVO clienteVO, String email) throws Exception {
        Empresa empresaDao = new Empresa(con);
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        optinVO.setCliente(clienteVO);
        optinVO.setEmpresa(empresaVO);
        optinVO.setEmail(clienteVO.getPessoa().getEmail());
        optinVO.setBloqueadoBounce(!optin);
        opt.alterar(optinVO, true, email);
    }

    public void incluirClassificacao(Integer cliente, List<Integer> classificacoes) throws Exception {
        if (UteisValidacao.emptyList(classificacoes)) {
            return;
        }
        executarConsulta("delete from clienteclassificacao where cliente = " + cliente, con);
        for (Integer cla : classificacoes) {
            executarConsulta("insert into clienteclassificacao (cliente, classificacao) " +
                    "values (" + cliente + ", " + cla + ");", con);
        }
    }

    public void incluirVinculo(ClienteVO cliente, List<VinculoVO> vinculos) throws Exception {
        for (VinculoVO v : vinculos) {
            if (v.getTipoColaboradorVinculo().equals(TipoColaboradorEnum.CONSULTOR)) {
                ResultSet rs = criarConsulta("select codigo,colaborador from vinculo where tipovinculo = 'CO' AND cliente = " + cliente.getCodigo(), con);
                boolean adicionar = true;
                if (rs.next()) {
                    if (rs.getInt("colaborador") != v.getColaborador().getCodigo()) {
                        executarConsulta("delete from vinculo where codigo = " + rs.getInt("codigo"), con);
                        executarConsulta("INSERT INTO historicovinculo(dataregistro, tipohistoricovinculo, tipocolaborador, cliente, colaborador, origem) values "
                                + "('" + Uteis.getDataJDBC(Calendario.hoje()) + "','SD','CO'," + cliente.getCodigo() + "," + rs.getInt("colaborador") + ",'Integração');", con);
                    } else {
                        adicionar = false;
                    }
                }
                if (adicionar) {
                    executarConsulta("insert into vinculo (cliente, tipovinculo, colaborador) " +
                            "values (" + cliente.getCodigo() + ", 'CO', " + v.getColaborador().getCodigo() + ");", con);
                    if (!UteisValidacao.emptyNumber(cliente.getPessoa().getIdExterno())) {
                        executarConsulta("INSERT INTO historicovinculo(dataregistro, tipohistoricovinculo, tipocolaborador, cliente, colaborador, origem) values "
                                + "('" + Uteis.getDataJDBC(cliente.getPessoa().getDataCadastro()) + "','EN','CO'," + cliente.getCodigo() + "," + v.getColaborador().getCodigo() + ",'IMPORTAÇÃO');", con);
                    } else {
                        executarConsulta("INSERT INTO historicovinculo(dataregistro, tipohistoricovinculo, tipocolaborador, cliente, colaborador, origem) values "
                                + "('" + Uteis.getDataJDBC(Calendario.hoje()) + "','EN','CO'," + cliente.getCodigo() + "," + v.getColaborador().getCodigo() + ",'INTEGRAÇÃO');", con);
                    }
                }

            }
        }
    }

    public void incluirClienteImportacao(ClienteVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        incluirPessoaSimplificado(obj.getPessoa());
        String sql = "INSERT INTO Cliente(pessoa, situacao, matricula, categoria, codAcesso, banco, "
                + "agencia, agenciaDigito, conta, contaDigito, identificadorParaCobranca, codAcessoAlternativo, "
                + "empresa, codigoMatricula, freePass, responsavelFreePass, parqpositivo, pessoaresponsavel, sesc, renda, "
                + "matriculasesc, nomeSocial, dataValidadeCarteirinha, matriculaExterna, "
                + "necessidadesEspeciaisSesiCe, dataValidadeCadastroSesiCe, razaoSocialEmpresaSesiCe, statusMatriculaSesiCe) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPessoa().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getPessoa().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        sqlInserir.setString(2, obj.getSituacao());
        sqlInserir.setString(3, obj.getMatricula());
        if (obj.getCategoria().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getCategoria().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }
        Cliente clienteDao = new Cliente(con);
        sqlInserir.setString(5, clienteDao.gerarCodigoAcesso(obj, 0));
        sqlInserir.setString(6, obj.getBanco());
        sqlInserir.setString(7, obj.getAgencia());
        sqlInserir.setString(8, obj.getAgenciaDigito());
        sqlInserir.setString(9, obj.getConta());
        sqlInserir.setString(10, obj.getContaDigito());
        sqlInserir.setString(11, obj.getIdentificadorParaCobranca());
        sqlInserir.setString(12, obj.getCodAcessoAlternativo());
        sqlInserir.setInt(13, obj.getEmpresa().getCodigo());
        sqlInserir.setInt(14, obj.getCodigoMatricula());
        if (obj.getFreePass().getCodigo() != 0) {
            sqlInserir.setInt(15, obj.getFreePass().getCodigo());
        } else {
            sqlInserir.setNull(15, 0);
        }
        if (obj.getResponsavelFreePass() != 0) {
            sqlInserir.setInt(16, obj.getResponsavelFreePass());
        } else {
            sqlInserir.setNull(16, 0);
        }
        sqlInserir.setBoolean(17, obj.isParqPositivo());
        if (!UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())) {
            sqlInserir.setInt(18, obj.getPessoaResponsavel().getCodigo());
        } else {
            sqlInserir.setNull(18, 0);
        }

        sqlInserir.setBoolean(19, obj.getSesc());
        if (obj.getRenda() == null) {
            sqlInserir.setNull(20, 0);
        } else {
            sqlInserir.setDouble(20, obj.getRenda());
        }

        if (UteisValidacao.emptyString(obj.getMatriculaSesc())) {
            sqlInserir.setNull(21, 0);
        } else {
            sqlInserir.setString(21, obj.getMatriculaSesc());
        }

        if (obj.getNomeSocial() == null) {
            sqlInserir.setNull(22, 0);
        } else {
            sqlInserir.setString(22, obj.getNomeSocial());
        }

        if (obj.getValidadeCartaoSesc() == null) {
            sqlInserir.setNull(23, 0);
        } else {
            sqlInserir.setDate(23, Uteis.getDataJDBC(obj.getValidadeCartaoSesc()));
        }
        if (UteisValidacao.emptyNumber(obj.getMatriculaExterna())) {
            sqlInserir.setNull(24, 0);
        } else {
            sqlInserir.setLong(24, obj.getMatriculaExterna());
        }
        if (UteisValidacao.emptyString(obj.getNecessidadesEspeciaisSesiCe())) {
            sqlInserir.setNull(25, 0);
        } else {
            sqlInserir.setString(25, obj.getNecessidadesEspeciaisSesiCe());
        }
        if (obj.getDataValidadeCadastroSesiCe() == null) {
            sqlInserir.setNull(26, 0);
        } else {
            sqlInserir.setDate(26, Uteis.getDataJDBC(obj.getDataValidadeCadastroSesiCe()));
        }
        if (UteisValidacao.emptyString(obj.getRazaoSocialEmpresaSesiCe())) {
            sqlInserir.setNull(27, 0);
        } else {
            sqlInserir.setString(27, obj.getRazaoSocialEmpresaSesiCe());
        }
        if (UteisValidacao.emptyString(obj.getStatusMatriculaSesiCe())) {
            sqlInserir.setNull(28, 0);
        } else {
            sqlInserir.setString(28, obj.getStatusMatriculaSesiCe());
        }
        sqlInserir.execute();
        obj.setCodigo(Conexao.obterUltimoCodigoGeradoTabela(con, "cliente"));
        if (obj.getVinculoVOs() != null && !obj.getVinculoVOs().isEmpty()) {

            Vinculo vinculo = new Vinculo(con);
            vinculo.incluirVinculo(obj.getCodigo(), obj.getVinculoVOs(), "CLIENTE", null, false);
            vinculo = null;
        }
        obj.setNovoObj(false);
        clienteDao.atualizarMatriculaAluno(obj.getCodigoMatricula());
    }

    public void incluirPessoaSimplificado(PessoaVO obj) throws Exception {
        PessoaVO.validarDadosSimplificado(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Pessoa(dataCadastro, nome, dataNasc, cfp, "
                + "cidade, estado, pais, sexo, nomepai, nomemae, estadocivil, rg, grauinstrucao, rgOrgao, nacionalidade, naturalidade, profissao, "
                + "rguf, idexterno, cpfmae, nomerespfinanceiro, cpfrespfinanceiro, cpfpai) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
        sqlInserir.setString(2, Uteis.getStringNormalizada(obj.getNome()));
        if (obj.getDataNasc() != null) {
            sqlInserir.setDate(3, Uteis.getDataJDBC(obj.getDataNasc()));
        } else {
            sqlInserir.setNull(3, 0);
        }
        if (!obj.getCfp().isEmpty()) {
            sqlInserir.setString(4, obj.getCfp());
        } else {
            sqlInserir.setNull(4, 0);
        }
        if (obj.getCidade().getCodigo() != 0) {
            sqlInserir.setInt(5, obj.getCidade().getCodigo());
        } else {
            sqlInserir.setNull(5, 0);
        }
        if (obj.getEstadoVO().getCodigo() != 0) {
            sqlInserir.setInt(6, obj.getEstadoVO().getCodigo());
        } else {
            sqlInserir.setNull(6, 0);
        }
        if (obj.getPais().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getPais().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        if (!obj.getSexo().isEmpty()) {
            sqlInserir.setString(8, obj.getSexo());
        } else {
            sqlInserir.setNull(8, 0);
        }

        if (UteisValidacao.emptyString(obj.getNomePai())) {
            sqlInserir.setNull(9, 0);
        } else {
            sqlInserir.setString(9, obj.getNomePai());
        }

        if (UteisValidacao.emptyString(obj.getNomeMae())) {
            sqlInserir.setNull(10, 0);
        } else {
            sqlInserir.setString(10, obj.getNomeMae());
        }

        if (UteisValidacao.emptyString(obj.getEstadoCivil())) {
            sqlInserir.setNull(11, 0);
        } else {
            sqlInserir.setString(11, obj.getEstadoCivil());
        }

        if (UteisValidacao.emptyString(obj.getRg())) {
            sqlInserir.setNull(12, 0);
        } else {
            sqlInserir.setString(12, obj.getRg());
        }

        if (obj.getGrauInstrucao() != null && !UteisValidacao.emptyNumber(obj.getGrauInstrucao().getCodigo())) {
            sqlInserir.setInt(13, obj.getGrauInstrucao().getCodigo());
        } else {
            sqlInserir.setNull(13, 0);

        }

        if (UteisValidacao.emptyString(obj.getRgOrgao())) {
            sqlInserir.setNull(14, 0);
        } else {
            sqlInserir.setString(14, obj.getRgOrgao());
        }
        sqlInserir.setString(15, obj.getNacionalidade());
        sqlInserir.setString(16, obj.getNaturalidade());
        if (obj.getProfissao() == null || UteisValidacao.emptyNumber(obj.getProfissao().getCodigo())) {
            sqlInserir.setNull(17, 0);
        } else {
            sqlInserir.setInt(17, obj.getProfissao().getCodigo());
        }
        sqlInserir.setString(18, obj.getRgUf());
        if (UteisValidacao.emptyNumber(obj.getIdExterno())) {
            sqlInserir.setNull(19, 0);
        } else {
            sqlInserir.setLong(19, obj.getIdExterno());
        }
        if (UteisValidacao.emptyString(obj.getCpfMae())) {
            sqlInserir.setNull(20, 0);
        } else {
            sqlInserir.setString(20, obj.getCpfMae());
        }
        if (UteisValidacao.emptyString(obj.getNomeRespFinanceiro())) {
            sqlInserir.setNull(21, 0);
        } else {
            sqlInserir.setString(21, obj.getNomeRespFinanceiro());
        }
        if (UteisValidacao.emptyString(obj.getCpfRespFinanceiro())) {
            sqlInserir.setNull(22, 0);
        } else {
            sqlInserir.setString(22, obj.getCpfRespFinanceiro());
        }
        if (UteisValidacao.emptyString(obj.getCpfPai())) {
            sqlInserir.setNull(23, 0);
        } else {
            sqlInserir.setString(23, obj.getCpfPai());
        }


        sqlInserir.execute();
        obj.setCodigo(Conexao.obterUltimoCodigoGeradoTabela(con, "pessoa"));
        obj.setNovoObj(false);
        Endereco endereco = new Endereco(con);
        endereco.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        endereco = null;
        Telefone telefone = new Telefone(con);
        telefone.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs());
        telefone = null;
        Email email = new Email(con);
        email.incluirEmails(obj.getCodigo(), obj.getEmailVOs());
        email = null;
    }


    public void alterarClienteImportacao(ClienteVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        alterarPessoaSimplificado(obj.getPessoa());
        String sql = "UPDATE Cliente SET pessoa = ?, situacao = ?, matricula = ?, categoria = ?, codAcesso = ?, banco = ?, "
                + "agencia = ?, agenciaDigito = ?, conta = ?, contaDigito = ?, identificadorParaCobranca = ?, codAcessoAlternativo = ?, "
                + "empresa = ?, codigoMatricula = ?, freePass = ?, responsavelFreePass = ?, " +
                "parqpositivo = ?, pessoaresponsavel = ?, sesc = ?, renda = ?, matriculasesc = ?, nomeSocial = ?, dataValidadeCarteirinha = ? "
                + " where CODIGO = ?";
        PreparedStatement sqlAlter = con.prepareStatement(sql);
        if (obj.getPessoa().getCodigo() != 0) {
            sqlAlter.setInt(1, obj.getPessoa().getCodigo());
        } else {
            sqlAlter.setNull(1, 0);
        }
        sqlAlter.setString(2, obj.getSituacao());
        sqlAlter.setString(3, obj.getMatricula());
        if (obj.getCategoria().getCodigo() != 0) {
            sqlAlter.setInt(4, obj.getCategoria().getCodigo());
        } else {
            sqlAlter.setNull(4, 0);
        }
        Cliente clienteDao = new Cliente(con);
        sqlAlter.setString(5, clienteDao.gerarCodigoAcesso(obj, 0));
        sqlAlter.setString(6, obj.getBanco());
        sqlAlter.setString(7, obj.getAgencia());
        sqlAlter.setString(8, obj.getAgenciaDigito());
        sqlAlter.setString(9, obj.getConta());
        sqlAlter.setString(10, obj.getContaDigito());
        sqlAlter.setString(11, obj.getIdentificadorParaCobranca());
        sqlAlter.setString(12, obj.getCodAcessoAlternativo());
        sqlAlter.setInt(13, obj.getEmpresa().getCodigo());
        sqlAlter.setInt(14, obj.getCodigoMatricula());
        if (obj.getFreePass().getCodigo() != 0) {
            sqlAlter.setInt(15, obj.getFreePass().getCodigo());
        } else {
            sqlAlter.setNull(15, 0);
        }
        if (obj.getResponsavelFreePass() != 0) {
            sqlAlter.setInt(16, obj.getResponsavelFreePass());
        } else {
            sqlAlter.setNull(16, 0);
        }
        sqlAlter.setBoolean(17, obj.isParqPositivo());
        if (!UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())) {
            sqlAlter.setInt(18, obj.getPessoaResponsavel().getCodigo());
        } else {
            sqlAlter.setNull(18, 0);
        }

        sqlAlter.setBoolean(19, obj.getSesc());
        if (obj.getRenda() == null) {
            sqlAlter.setNull(20, 0);
        } else {
            sqlAlter.setDouble(20, obj.getRenda());
        }

        if (UteisValidacao.emptyString(obj.getMatriculaSesc())) {
            sqlAlter.setNull(21, 0);
        } else {
            sqlAlter.setString(21, obj.getMatriculaSesc());
        }

        if (obj.getNomeSocial() == null) {
            sqlAlter.setNull(22, 0);
        } else {
            sqlAlter.setString(22, obj.getNomeSocial());
        }

        if (obj.getValidadeCartaoSesc() == null) {
            sqlAlter.setNull(23, 0);
        } else {
            sqlAlter.setDate(23, Uteis.getDataJDBC(obj.getValidadeCartaoSesc()));
        }
        sqlAlter.setInt(24, obj.getCodigo());
        sqlAlter.execute();
        obj.setNovoObj(false);
    }

    public void alterarPessoaSimplificado(PessoaVO obj) throws Exception {
        PessoaVO.validarDadosSimplificado(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Pessoa set nome = ?, dataNasc = ?, cfp = ?, "
                + "cidade = ?, estado = ?, pais = ?, sexo = ?, nomepai = ?, nomemae = ?, " +
                " estadocivil = ?, rg = ?, grauinstrucao = ?, rgorgao = ?, nacionalidade = ?, naturalidade = ?, profissao = ?, " +
                " rguf = ?, nomerespfinanceiro = ?, cpfrespfinanceiro = ? WHERE codigo = ? ";
        PreparedStatement sqlAlter = con.prepareStatement(sql);
        int i = 1;
        sqlAlter.setString(i++, Uteis.getStringNormalizada(obj.getNome()));
        if (obj.getDataNasc() != null) {
            sqlAlter.setDate(i++, Uteis.getDataJDBC(obj.getDataNasc()));
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (!obj.getCfp().isEmpty()) {
            sqlAlter.setString(i++, obj.getCfp());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (obj.getCidade().getCodigo() != 0) {
            sqlAlter.setInt(i++, obj.getCidade().getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (obj.getEstadoVO().getCodigo() != 0) {
            sqlAlter.setInt(i++, obj.getEstadoVO().getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (obj.getPais().getCodigo() != 0) {
            sqlAlter.setInt(i++, obj.getPais().getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (!obj.getSexo().isEmpty()) {
            sqlAlter.setString(i++, obj.getSexo());
        } else {
            sqlAlter.setNull(i++, 0);
        }

        if (UteisValidacao.emptyString(obj.getNomePai())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getNomePai());
        }

        if (UteisValidacao.emptyString(obj.getNomeMae())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getNomeMae());
        }

        if (UteisValidacao.emptyString(obj.getEstadoCivil())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getEstadoCivil());
        }

        if (UteisValidacao.emptyString(obj.getRg())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getRg());
        }

        if (obj.getGrauInstrucao() != null && !UteisValidacao.emptyNumber(obj.getGrauInstrucao().getCodigo())) {
            sqlAlter.setInt(i++, obj.getGrauInstrucao().getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }

        if (UteisValidacao.emptyString(obj.getRgOrgao())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getRgOrgao());
        }
        sqlAlter.setString(i++, obj.getNacionalidade());
        sqlAlter.setString(i++, obj.getNaturalidade());
        if (obj.getProfissao() == null || UteisValidacao.emptyNumber(obj.getProfissao().getCodigo())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setInt(i++, obj.getProfissao().getCodigo());
        }
        if (UteisValidacao.emptyString(obj.getNomeRespFinanceiro())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getNomeRespFinanceiro());
        }
        if (UteisValidacao.emptyString(obj.getCpfRespFinanceiro())) {
            sqlAlter.setNull(i++, 0);
        } else {
            sqlAlter.setString(i++, obj.getCpfRespFinanceiro());
        }
        sqlAlter.setString(i++, obj.getRgUf());
        sqlAlter.setInt(i++, obj.getCodigo());
        sqlAlter.execute();

        SuperFacadeJDBC.executarConsulta("delete from endereco where pessoa = " + obj.getCodigo(), con);
        Endereco endereco = new Endereco(con);
        endereco.incluirEnderecos(obj.getCodigo(), obj.getEnderecoVOs());
        endereco = null;
        SuperFacadeJDBC.executarConsulta("delete from telefone where pessoa = " + obj.getCodigo(), con);
        Telefone telefone = new Telefone(con);
        telefone.incluirTelefones(obj.getCodigo(), obj.getTelefoneVOs());
        telefone = null;
        SuperFacadeJDBC.executarConsulta("delete from email where pessoa = " + obj.getCodigo(), con);
        Email email = new Email(con);
        email.incluirEmails(obj.getCodigo(), obj.getEmailVOs());
        email = null;
    }

    private String RemoverCaracter(String texto, String caracterRegex) {
        if (UteisValidacao.emptyString(caracterRegex)) {
            caracterRegex = "['`´'^~]";
        }

        return texto.replaceAll(caracterRegex, "");
    }

    private String RemoverCaracter(String texto) {
        return RemoverCaracter(texto, null);
    }

    public JSONObject resumoPeriodo(Date inicio, Date fim, boolean vencimento) throws Exception {
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Calendario.getDefaultLocale());
        final String dtInicio = sdf.format(inicio) + " 00:00:00";
        final String dtFim = sdf.format(fim) + " 23:59:59";

        Uteis.logarDebug("## Inicio ResumoPeriodo para "+dtInicio+" - "+dtFim);
        long millisInicioResumo = System.currentTimeMillis();

        JSONObject retorno = new JSONObject();
        List<ResumoPeriodoCallable> callableTasks = new ArrayList<>();
        for (DadoResumoPeriodoEnum dado : DadoResumoPeriodoEnum .values()) {
            if((vencimento && (dado.equals(DadoResumoPeriodoEnum.PARCELAS_VENDAAVULSA_VENCIMENTO) ||dado.equals(DadoResumoPeriodoEnum.PARCELAS_CONTRATO_VENCIMENTO))) //para vencimento, apenas esses dois itens são permitidos
                || (!vencimento && (!dado.equals(DadoResumoPeriodoEnum.PARCELAS_VENDAAVULSA_VENCIMENTO) && !dado.equals(DadoResumoPeriodoEnum.PARCELAS_CONTRATO_VENCIMENTO)))){//sem ser vencimento, apenas esses dois itens não são permitidos
                callableTasks.add(new ResumoPeriodoCallable(con, dado, retorno, dtInicio, dtFim));
            }
        }

        ExecutorService executorService = Executors.newFixedThreadPool(callableTasks.size());
        executorService.invokeAll(callableTasks);
        executorService.shutdown();
        executorService = null;
        long millisResumo= System.currentTimeMillis() - millisInicioResumo;
        Uteis.logarDebug(String.format(
                "## Fim ResumoPeriodo para "+dtInicio+" - "+dtFim+".Tempo de execução: %d milissegundos.", millisResumo));
//        retorno.put("contratos", resumoPeriodoContratos(dtInicio,dtFim));
//        retorno.put("pagamentos", resumoPeriodoPagamentos(dtInicio,dtFim));
//        retorno.put("estornos", resumoPeriodoEstornoPagamentos(dtInicio,dtFim));
//        retorno.put("estornosContrato", resumoPeriodoEstornoContratos(dtInicio,dtFim));
//        retorno.put("parcelasCanceladas", resumoPeriodoParcelasCanceladas(dtInicio,dtFim));
//        retorno.put("vendaAvulsa", resumoPeriodoVendasAvulsas(dtInicio,dtFim));
//        retorno.put("manutencaoModalidade", resumoPeriodoManutencaoModalidade(dtInicio,dtFim));
//        retorno.put("operacoes", resumoPeriodoOperacoes(dtInicio,dtFim));

        return retorno;
    }

    public List<OperacoesContratoResumidoJSON> resumoPeriodoOperacoes(final String dtInicio, final String dtFim) throws Exception {
        List<OperacoesContratoResumidoJSON> operacoes = new ArrayList<>();
        StringBuilder sqlOperacoes = new StringBuilder();
        sqlOperacoes.append("SELECT rec.codigo is null transferencia, cop.contrato, cop.dataoperacao, cop.tipooperacao, usu.colaborador codConsultor, usu.nome consultor, emp.nome as empresa, jop.descricao justificativa, cop.descricaocalculo, cop.observacao,\n")
                .append("cli.codigo as codigoCliente, cli.matriculasesc, pe.nome as nomealuno, pe.cfp, \n")
                .append("(select  array_agg(codigo||';'||descricao||';'||valorparcela||';'||datavencimento||';'||situacao||';'||dataregistro||';'||dataAlteracaoSituacao)  from\n")
                .append("(select codigo, descricao, valorparcela, TO_CHAR(datavencimento, 'DD/MM/YYYY') as datavencimento, TO_CHAR(dataregistro, 'DD/MM/YYYY') as dataregistro \n")
                .append(",situacao, TO_CHAR(dataAlteracaoSituacao , 'DD/MM/YYYY') as dataAlteracaoSituacao from movparcela \n")
                .append(" where contrato = con.codigo order by codigo\n")
                .append(" ) as fooParcelas) as parcelas\n")
                .append("FROM contratooperacao cop\n")
                .append("inner join usuario usu on  cop.responsavel = usu.codigo\n")
                .append("inner join contrato con on cop.contrato = con.codigo\n")
                .append("inner join cliente cli on con.pessoa = cli.pessoa \n")
                .append("inner join pessoa pe on pe.codigo = cli.pessoa \n")
                .append("inner join empresa emp on emp.codigo = con.empresa\n")
                .append("inner join justificativaoperacao jop on cop.tipojustificativa = jop.codigo\n")
                .append("left join recibodevolucao rec on rec.contrato = cop.contrato\n")
                //.append("WHERE cop.tipooperacao in ('CA', 'IM', 'AM', 'EM')\n")
                .append("WHERE cop.dataoperacao BETWEEN '").append(dtInicio)
                .append("' and '")
                .append(dtFim)
                .append("'\n;");

        ResultSet rsOperacoes = SuperFacadeJDBC.criarConsulta(sqlOperacoes.toString(), con);
        while (rsOperacoes.next()) {
            try {
                OperacoesContratoResumidoJSON operacao = new OperacoesContratoResumidoJSON();
                operacao.setContrato(rsOperacoes.getInt("contrato"));
                ClienteResumidoJSON aluno = new ClienteResumidoJSON();
                aluno.setCodigo(rsOperacoes.getInt("codigoCliente"));
                aluno.setCpf(rsOperacoes.getString("cfp"));
                aluno.setNome(rsOperacoes.getString("nomealuno"));
                aluno.setMatriculaSesc(rsOperacoes.getString("matriculasesc"));
                operacao.setAluno(aluno);
                operacao.setData(Uteis.getData(rsOperacoes.getDate("dataoperacao")));
                operacao.setTipooOperacao(TipoOperacaoContratoEnum.obterPorSigla(rsOperacoes.getString("tipooperacao")).toString());
                operacao.setCodConsultor(rsOperacoes.getInt("codConsultor"));
                operacao.setConsultor(rsOperacoes.getString("consultor"));
                operacao.setEmpresa(rsOperacoes.getString("empresa"));
                operacao.setJustificativa(rsOperacoes.getString("justificativa"));
                operacao.setObservacao(rsOperacoes.getString("observacao"));

                String descricaoCalculo = rsOperacoes.getString("descricaoCalculo");

                operacao.setDescricaoCalculo(descricaoCalculo);

                String tipoCancelamento = null;
                if (descricaoCalculo.toUpperCase().contains("TRANSFERÊNCIA")) {
                    tipoCancelamento = "transferência";
                } else if (descricaoCalculo.toUpperCase().contains("DEVOLUÇÃO")) {
                    tipoCancelamento = "devolução";
                }
                operacao.setTipoCancelamento(tipoCancelamento);

                String valorDevolvido = "0.0";
                String valorMultaCancelamento = "0.0";
                for (String texto : descricaoCalculo.split("\n")) {
                    if (texto.contains("VALOR DEVOLVIDO EM DINHEIRO:")) {
                        valorDevolvido = formataValorTabelaLog(texto.substring(29));
                        continue;
                    } else if (texto.contains("VALOR DA MULTA DE CANCELAMENTO:")) {
                        valorMultaCancelamento = formataValorTabelaLog(texto.substring(32));
                        continue;
                    }

                    // VALOR DA MULTA DE CANCELAMENTO:
                }
                if (UteisValidacao.somenteNumerosEPontos(valorDevolvido))
                    operacao.setValorDevolvido(Double.valueOf(valorDevolvido));

                if (UteisValidacao.somenteNumerosEPontos(valorMultaCancelamento))
                    operacao.setValorMultaCancelamento(Double.valueOf(valorMultaCancelamento));

                Array arrayParcelas = rsOperacoes.getArray("parcelas");
                if(arrayParcelas != null) {
                    String[] valoresArrayParcelas = (String[]) arrayParcelas.getArray();
                    for (String valor : valoresArrayParcelas) {
                        String[] dadosParcela = valor.split(";");
                        ParcelaResumidaJSON parcela = new ParcelaResumidaJSON();
                        parcela.setCodigo(Integer.valueOf(dadosParcela[0]));
                        parcela.setDescricao(dadosParcela[1]);
                        parcela.setValor(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosParcela[2])));
                        parcela.setVencimento(dadosParcela[3]);
                        parcela.setSituacao(dadosParcela[4]);
                        parcela.setDataLancamento(dadosParcela[5]);
                        parcela.setDataAlteracaoSituacao(dadosParcela[6]);
                        operacao.getParcelas().add(parcela);
                    }
                }
                operacoes.add(operacao);
            }catch (Exception e ){
                throw e;
            }
        }
        return operacoes;
    }

    public List<ManutencaoModalidadeJSON> resumoPeriodoManutencaoModalidade(final String dtInicio, final String dtFim) throws Exception {
        StringBuilder sqlManutencaoModalidade = new StringBuilder();
        sqlManutencaoModalidade.append("select cop.contrato, cop.dataoperacao, cop.tipooperacao, cop.responsavel, pes.nome")
                .append(" from contratooperacao cop")
                .append(" inner join usuario usu on cop.responsavel = usu.codigo")
                .append(" inner join colaborador col on col.codigo = usu.colaborador")
                .append(" inner join pessoa pes on col.pessoa = pes.codigo")
                .append(" where cop.tipooperacao in ('IM', 'AM', 'EM')")
                .append(" and cop.dataoperacao between '")
                .append(dtInicio)
                .append("' and '")
                .append(dtFim)
                .append("'");

        List<ManutencaoModalidadeJSON> listManutencaoModalidade = new ArrayList<>();
        ResultSet rsManutencaoModalidade = SuperFacadeJDBC.criarConsulta(sqlManutencaoModalidade.toString(), con);
        while (rsManutencaoModalidade.next()) {
            ManutencaoModalidadeJSON manutencaoModalidadeJSON = new ManutencaoModalidadeJSON();
            manutencaoModalidadeJSON.setContrato(rsManutencaoModalidade.getInt("contrato"));
            manutencaoModalidadeJSON.setDataOperacao(Uteis.getData(rsManutencaoModalidade.getDate("dataoperacao")));
            manutencaoModalidadeJSON.setTipoOperacao(TipoOperacaoContratoEnum.obterPorSigla(rsManutencaoModalidade.getString("tipooperacao")).name());
            manutencaoModalidadeJSON.setCodResponsavel(rsManutencaoModalidade.getInt("responsavel"));
            manutencaoModalidadeJSON.setNomeResponsavel(rsManutencaoModalidade.getString("nome"));
            listManutencaoModalidade.add(manutencaoModalidadeJSON);
        }
        return listManutencaoModalidade;
    }

    public List<VendaAvulsaJSON> resumoPeriodoVendasAvulsas(final String dtInicio, final String dtFim, boolean vencimento) throws Exception {
        StringBuilder sqlVendaAvulsa = new StringBuilder();
        sqlVendaAvulsa.append("select vav.codigo, pro.codigo codigoProduto, pro.descricao produto, vav.cliente codigoAluno, vav.colaborador as codigoColaborador, vav.nomecomprador aluno, cli.matriculasesc, pe.nome, pe.cfp, pe.datanasc, pe.nomemae, pe.cpfmae, pe.nomepai, pe.cpfpai, vav.dataregistro, mpr.valordesconto, usu.colaborador codConsultor, usu.nome consultor, emp.nome unidade, emp.codigo unidadeCodigo, mpr.totalfinal, cc.nome nomeCentroCusto, cc.codigoCentroCustos, \n")
                .append("(select array_agg(codigo||';'||situacao||';'||descricao||';'||TO_CHAR(dataregistro, 'DD/MM/YYYY')||';'||TO_CHAR(datavencimento, 'DD/MM/YYYY')||';'||valorparcela||';' ||TO_CHAR(dataAlteracaoSituacao, 'DD/MM/YYYY')||';'||coalesce(parcelasrenegociadas,'')) \n")
                .append("from movparcela where codigo in (select movparcela from movprodutoparcela where movproduto = mpr.codigo)) as parcelas\n")
                .append("from vendaavulsa vav\n")
                .append("inner join movproduto mpr on mpr.vendaavulsa = vav.codigo\n")
                .append("inner join produto pro on mpr.produto = pro.codigo\n")
                .append("inner join empresa emp on vav.empresa = emp.codigo\n")
                .append("inner join usuario usu on vav.responsavel = usu.codigo\n")
                .append("left join movprodutoparcela mpp on mpp.movproduto = mpr.codigo\n")
                .append("left join movparcela mpa on mpa.codigo = mpp.movparcela\n")
                .append("left join cliente cli on vav.cliente = cli.codigo\n")
                .append("left join colaborador col on vav.colaborador = col.codigo\n")
                .append("left join pessoa pe on pe.codigo = cli.pessoa or pe.codigo = col.pessoa\n")
                .append("left join rateiointegracao ri on ri.produto = mpr.produto\n")
                .append("left join centrocusto cc on cc.codigo = ri.centrocusto \n");
                if(vencimento){
                    sqlVendaAvulsa.append("where (mpa.datavencimento  between '").append(dtInicio).append("' and '").append(dtFim).append("')\n");
                } else {
                    sqlVendaAvulsa.append("where (vav.dataregistro between '").append(dtInicio)
                            .append("' and '").append(dtFim).append("')\n")
                            .append("and pro.tipoproduto not in ('MJ')\n")
                            .append("or (mpa.dataregistro between '").append(dtInicio).append("' and '").append(dtFim).append("')\n")
                            .append("or mpa.codigo::text in\n")
                            .append("(select chaveprimaria codigoMovParcela\n")
                            .append("from log lg\n")
                            .append("where upper(lg.nomeentidade) = 'MOVPARCELA'\n")
                            .append("and (lg.dataalteracao between '").append(dtInicio)
                            .append("' and '").append(dtFim).append("'))\n");
                }
                sqlVendaAvulsa.append(" and (ri is null or (ri.planoconta is null and ri.empresa = vav.empresa)) ");

        List<VendaAvulsaJSON> listVendaAvulsa = new ArrayList<VendaAvulsaJSON>();
        ResultSet rsVendaAvulsa = SuperFacadeJDBC.criarConsulta(sqlVendaAvulsa.toString(), con);
        while (rsVendaAvulsa.next()) {
            VendaAvulsaJSON vendaAvulsaJSON = new VendaAvulsaJSON();
            vendaAvulsaJSON.setCodigo(rsVendaAvulsa.getInt("codigo"));
            vendaAvulsaJSON.setCodigoProduto(rsVendaAvulsa.getInt("codigoProduto"));
            vendaAvulsaJSON.setProduto(rsVendaAvulsa.getString("produto"));
            vendaAvulsaJSON.setCentroDeCustos(!UteisValidacao.emptyString(rsVendaAvulsa.getString("codigoCentroCustos")) ? (rsVendaAvulsa.getString("codigoCentroCustos") + " - " + rsVendaAvulsa.getString("nomeCentroCusto").toUpperCase()) : "");
            ClienteResumidoJSON aluno = new ClienteResumidoJSON();
            if(UteisValidacao.emptyNumber(rsVendaAvulsa.getInt("codigoAluno"))){
                aluno.setNome(rsVendaAvulsa.getString("aluno"));
                if(UteisValidacao.emptyNumber(rsVendaAvulsa.getInt("codigoColaborador"))){
                    aluno.setNome(aluno.getNome() + " (VENDA CONSUMIDOR)");
                } else {
                    aluno.setCpf(rsVendaAvulsa.getString("cfp"));
                }
            } else {
                aluno.setCodigo(rsVendaAvulsa.getInt("codigoAluno"));
                aluno.setCpf(rsVendaAvulsa.getString("cfp"));
                aluno.setNome(rsVendaAvulsa.getString("nome"));
                if (!UteisValidacao.emptyString(rsVendaAvulsa.getString("datanasc"))) {
                    aluno.setDataNascimento(Uteis.getData(rsVendaAvulsa.getDate("datanasc"), "br"));
                }
                aluno.setMatriculaSesc(rsVendaAvulsa.getString("matriculasesc"));
            }
            if (!UteisValidacao.emptyString(rsVendaAvulsa.getString("nomemae"))) {
                aluno.setResponsavel(rsVendaAvulsa.getString("nomemae"));
                aluno.setCpfResponsavel(rsVendaAvulsa.getString("cpfmae"));
            } else {
                aluno.setResponsavel(rsVendaAvulsa.getString("nomepai"));
                aluno.setCpfResponsavel(rsVendaAvulsa.getString("cpfpai"));
            }
            vendaAvulsaJSON.setAluno(aluno);
            vendaAvulsaJSON.setDataRegistro(Uteis.getData(rsVendaAvulsa.getDate("dataregistro")));
            vendaAvulsaJSON.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(rsVendaAvulsa.getDouble("valordesconto")));
            vendaAvulsaJSON.setCodConsultor(rsVendaAvulsa.getInt("codConsultor"));
            vendaAvulsaJSON.setConsultor(rsVendaAvulsa.getString("consultor"));
            vendaAvulsaJSON.setUnidade(rsVendaAvulsa.getString("unidade"));
            vendaAvulsaJSON.setUnidadeCodigo(rsVendaAvulsa.getInt("unidadeCodigo"));
            vendaAvulsaJSON.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(rsVendaAvulsa.getDouble("totalfinal")));

            vendaAvulsaJSON.setVendaAvulsaParcela(new ArrayList<>());

            Array arrayParcelas = rsVendaAvulsa.getArray("parcelas");
            if(arrayParcelas != null) {
                String[] valoresArrayParcelas = (String[]) arrayParcelas.getArray();
                for (String valor : valoresArrayParcelas) {
                    String[] dadosParcela = valor.split(";");
                    VendaAvulsaParcelaJSON vendaAvulsaParcela = new VendaAvulsaParcelaJSON();
                    vendaAvulsaParcela.setCodigo(Integer.valueOf(dadosParcela[0]));
                    vendaAvulsaParcela.setSituacao(dadosParcela[1]);
                    vendaAvulsaParcela.setDescricao(dadosParcela[2]);
                    vendaAvulsaParcela.setDataLancamento(dadosParcela[3]);
                    vendaAvulsaParcela.setDataVencimento(dadosParcela[4]);
                    vendaAvulsaParcela.setValor(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosParcela[5])));
                    vendaAvulsaParcela.setDataAlteracaoSituacao(dadosParcela[6]);
                    if (dadosParcela.length > 7) {
                        String parcelasRenegociadas = dadosParcela[7];
                        if (!UteisValidacao.emptyString(parcelasRenegociadas)) {
                            JSONObject jsonParcelas = new JSONObject(parcelasRenegociadas);
                            for (int i = 0; i < jsonParcelas.getJSONArray("listaMovParcelaRenegociar").length(); i++) {

                                JSONObject j = new JSONObject(jsonParcelas.getJSONArray("listaMovParcelaRenegociar").get(i).toString());
                                vendaAvulsaParcela.setCodigoParcelaAnterior(j.getInt("codigo"));
                                break;
                            }
                        }
                    }

                    vendaAvulsaJSON.getVendaAvulsaParcela().add(vendaAvulsaParcela);
                }
            }
            vendaAvulsaJSON.setQtdParcela(vendaAvulsaJSON.getVendaAvulsaParcela().size());
            listVendaAvulsa.add(vendaAvulsaJSON);
        }
        return listVendaAvulsa;
    }

    public List<ParcelasProRataJSON> resumoPeriodoParcelasProRata(final String dtInicio, final String dtFim) throws Exception {
        List<ParcelasProRataJSON> listParcelasProRataJson = new ArrayList<>();
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT codigo,descricao,valorparcela,datavencimento,situacao,dataregistro,dataalteracaosituacao FROM movparcela ");
        sql.append("WHERE (descricao ILIKE '%PRO%RATA%' ");
        sql.append("OR descricao ILIKE '%PRO%-RATA%' ");
        sql.append("OR descricao ILIKE '%PRORATA%' ");
        sql.append("OR descricao ILIKE '%PRO % RATA%') ");
        sql.append("AND dataAlteracaoSituacao BETWEEN '").append(dtInicio).append("' AND '").append(dtFim).append("';");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        try {
            while (rs.next()) {
                ParcelasProRataJSON parcela = new ParcelasProRataJSON();

                parcela.setCodigo(rs.getInt("codigo"));
                parcela.setDescricao(rs.getString("descricao"));
                parcela.setValor(rs.getDouble("valorparcela"));
                parcela.setVencimento(rs.getString("datavencimento"));
                parcela.setSituacao(rs.getString("situacao"));
                // parcela.setValorJuro(rs.getDouble("valorJuro"));
                // parcela.setValorMulta(rs.getDouble("valorMulta"));
                // parcela.setParcelaOriginalMultaJuros(rs.getInt("parcelaOriginalMultaJuros"));
                parcela.setDataLancamento(rs.getString("dataRegistro"));
                parcela.setDataAlteracaoSituacao(rs.getString("dataAlteracaoSituacao"));

                listParcelasProRataJson.add(parcela);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            throw new Exception("Erro ao consultar parcelas pro rata", e);
        }

        return listParcelasProRataJson;
    }



    public List<ParcelasCanceladasJSON> resumoPeriodoParcelasCanceladas(final String dtInicio, final String dtFim) throws Exception {
        List<ParcelasCanceladasJSON> listParcelasCanceladasJSON = new ArrayList<>();
        StringBuilder sqlParcelasCanceladas = new StringBuilder();
        sqlParcelasCanceladas.append("SELECT lg.valorcampoalterado, cli.matricula, lg.dataalteracao\n")
                .append("FROM log lg\n")
                .append("LEFT JOIN pessoa pes on pes.codigo = lg.pessoa\n")
                .append("LEFT JOIN cliente cli on cli.pessoa = pes.codigo\n")
                .append("WHERE upper(lg.nomeentidade) = 'MOVPARCELA'\n")
                .append("AND upper(lg.operacao) = 'CANCELAMENTO - PARCELA'\n")
                .append("AND lg.dataalteracao between '").append(dtInicio).append("' and '").append(dtFim).append("' ");

        ResultSet rsParcelasCanceladas = SuperFacadeJDBC.criarConsulta(sqlParcelasCanceladas.toString(), con);
        try {
            while (rsParcelasCanceladas.next()) {
                String valorAlterado = rsParcelasCanceladas.getString("valorcampoalterado");

                ParcelasCanceladasJSON parcelasCanceladasJSON = new ParcelasCanceladasJSON();

                String[] partesValorAlterado = valorAlterado.split("\n");
                for (String linha : partesValorAlterado) {
                    if (linha.toUpperCase().contains("CONTRATO= ")) {
                        parcelasCanceladasJSON.setContrato(Integer.valueOf(linha.toUpperCase().split("CONTRATO= ")[1]));
                        continue;
                    } else if (linha.toUpperCase().contains("CÓDIGO DA PARCELA = ")) {
                        parcelasCanceladasJSON.setCodigoParcela(Integer.valueOf(linha.toUpperCase().split("CÓDIGO DA PARCELA = ")[1]));
                        continue;
                    } else if (linha.toUpperCase().contains("DATA VENCIMENTO = ")) {
                        parcelasCanceladasJSON.setDataVencimento(linha.toUpperCase().split("DATA VENCIMENTO = ")[1]);
                        continue;
                    } else if (linha.toUpperCase().contains("VALOR TOTAL = ")) {
                        parcelasCanceladasJSON.setValorTotal(Double.valueOf(formataValorTabelaLog(linha.toUpperCase().split("VALOR TOTAL = ")[1])));
                        continue;
                    } else if (linha.toUpperCase().contains("NOME DO CLIENTE = ")) {
                        parcelasCanceladasJSON.setNomeCliente(linha.toUpperCase().split("NOME DO CLIENTE = ")[1]);
                        continue;
                    } else if (linha.toUpperCase().contains("RESPONSÁVEL LANÇAMENTO = ")) {
                        parcelasCanceladasJSON.setResponsavelLancamento(linha.toUpperCase().split("RESPONSÁVEL LANÇAMENTO = ")[1]);
                        continue;
                    }
                }
                parcelasCanceladasJSON.setDataCancelamento(Uteis.getData(rsParcelasCanceladas.getDate("dataalteracao")));
                parcelasCanceladasJSON.setMatricula(rsParcelasCanceladas.getString("matricula"));
                listParcelasCanceladasJSON.add(parcelasCanceladasJSON);
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao preencher parcelasCanceladas: " + e.getMessage());
        }
        return listParcelasCanceladasJSON;
    }

    public List<EstornoResumidoContratoJSON> resumoPeriodoEstornoContratos(final String dtInicio, final String dtFim) throws Exception {
        List<EstornoResumidoContratoJSON> estornosContrato = new ArrayList<>();
        StringBuilder sqlEstorno = new StringBuilder();
        sqlEstorno.append("SELECT * FROM log\n")
                .append("WHERE operacao LIKE '%ESTORNO - CONTRATO%'\n")
                .append("AND dataalteracao BETWEEN '").append(dtInicio)
                .append("' and '")
                .append(dtFim)
                .append("'\n;");

        ResultSet rsEstornos = SuperFacadeJDBC.criarConsulta(sqlEstorno.toString(), con);
        try {
            while (rsEstornos.next()) {
                String valorAlterado = rsEstornos.getString("valorcampoalterado");
                String codContrato = null;
                String unidade = null;
                String valorContrato = null;
                String plano = null;
                String responsavelContrato = null;
                String codigoCliente = null;
                String matricula = null;
                String nomeCliente = null;
                String valorPagoEstornado = null;

                List<ModalidadeEstornoJSON> listModalidadeEstornoJSONS = new ArrayList<>();

                List<PagamentoEstornoJSON> listPagamentoEstornoJSON = new ArrayList<>();
                PagamentoEstornoJSON pagamentoEstornoJSON = new PagamentoEstornoJSON();
                List<ReciboEstornoJSON> listReciboEstornoJSON = new ArrayList<>();
                ReciboEstornoJSON reciboEstornoJSON = new ReciboEstornoJSON();
                List<ParcelaEstornoJSON> listParcelaEstornoJSON = new ArrayList<>();
                ParcelaEstornoJSON parcelaEstornoJSON = new ParcelaEstornoJSON();

                String[] partesValorAlterado = valorAlterado.split("\n");
                for (String linha : partesValorAlterado) {
                    if (linha.toUpperCase().contains("CÓDIGO DO CONTRATO = ") && UteisValidacao.emptyString(codContrato)) {
                        codContrato = linha.toUpperCase().split("CÓDIGO DO CONTRATO = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("EMPRESA = ") && UteisValidacao.emptyString(unidade)) {
                        unidade = linha.toUpperCase().split("EMPRESA = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("VALOR CONTRATO = ") && UteisValidacao.emptyString(valorContrato)) {
                        valorContrato = linha.toUpperCase().split("VALOR CONTRATO = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("VALOR = ")) {
                        valorPagoEstornado = linha.toUpperCase().split("VALOR = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("PLANO = ") && UteisValidacao.emptyString(plano)) {
                        plano = linha.toUpperCase().split("PLANO = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("RESPONSÁVEL PELO CONTRATO = ") && UteisValidacao.emptyString(responsavelContrato)) {
                        responsavelContrato = linha.toUpperCase().split("RESPONSÁVEL PELO CONTRATO = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("CÓDIGO DO CLIENTE = ") && UteisValidacao.emptyString(codigoCliente)) {
                        codigoCliente = linha.toUpperCase().split("CÓDIGO DO CLIENTE = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("MATRICULA = ") && UteisValidacao.emptyString(matricula)) {
                        matricula = linha.toUpperCase().split("MATRICULA = ")[1];
                        continue;
                    } else if (linha.toUpperCase().contains("NOME DO CLIENTE = ") && UteisValidacao.emptyString(nomeCliente)) {
                        nomeCliente = linha.toUpperCase().split("NOME DO CLIENTE = ")[1];
                        continue;

                    } else if (linha.toUpperCase().contains("MODALIDADE = ")) {
                        ModalidadeEstornoJSON modalidadeEstornoJSONS = new ModalidadeEstornoJSON();
                        modalidadeEstornoJSONS.setModalidade(linha.toUpperCase().split("MODALIDADE = ")[1]);
                        listModalidadeEstornoJSONS.add(modalidadeEstornoJSONS);
                        continue;

                    } else if (linha.toUpperCase().contains("CÓDIGO DO RECIBO = ")) {
                        reciboEstornoJSON = new ReciboEstornoJSON();
                        reciboEstornoJSON.setCodigoRecibo(formataNumeroTabelaLog(linha.toUpperCase().split("CÓDIGO DO RECIBO = ")[1]));
                        continue;
                    } else if (linha.toUpperCase().contains("DATA ENTRADA NO CAIXA = ")) {
                        reciboEstornoJSON.setDataEntradaCaixa(linha.toUpperCase().split("DATA ENTRADA NO CAIXA = ")[1]);
                        listReciboEstornoJSON.add(reciboEstornoJSON);
                        continue;

                    } else if (linha.toUpperCase().contains("CÓDIGO DA PARCELA = ")) {
                        parcelaEstornoJSON = new ParcelaEstornoJSON();
                        parcelaEstornoJSON.setCodigoParcela(formataNumeroTabelaLog(linha.toUpperCase().split("CÓDIGO DA PARCELA = ")[1]));
                        continue;
                    } else if (linha.toUpperCase().contains("DESCRIÇÃO PARCELA = ")) {
                        parcelaEstornoJSON.setDescricaoParcela(linha.toUpperCase().split("DESCRIÇÃO PARCELA = ")[1]);
                        continue;
                    } else if (linha.toUpperCase().contains("VALOR DA PARCELA = ")) {
                        parcelaEstornoJSON.setValorParcela(Double.valueOf(formataValorTabelaLog(linha.toUpperCase().split("VALOR DA PARCELA = ")[1])));
                        continue;
                    } else if (linha.toUpperCase().contains("NUMERO CONTRATO = ")) {
                        parcelaEstornoJSON.setNumeroContrato(linha.toUpperCase().split("NUMERO CONTRATO = ")[1]);
                        continue;
                    } else if (linha.toUpperCase().contains("SITUAÇÃO = ")) {
                        parcelaEstornoJSON.setSituacao(linha.toUpperCase().split("SITUAÇÃO = ")[1]);
                        listParcelaEstornoJSON.add(parcelaEstornoJSON);
                        continue;

                    } else if (linha.toUpperCase().contains("PAGAMENTO EM = ")) {
                        pagamentoEstornoJSON = new PagamentoEstornoJSON();
                        String dataPagamento = linha.toUpperCase().split("PAGAMENTO EM = ")[1];
                        if (dataPagamento.length() >= 10)
                            dataPagamento = dataPagamento.substring(0, 10);
                        pagamentoEstornoJSON.setDataPagamento(dataPagamento);
                        continue;
                    } else if (linha.toUpperCase().contains("FORMA DE PAGAMENTO = ")) {
                        pagamentoEstornoJSON.setFormaPagamento(linha.toUpperCase().split("FORMA DE PAGAMENTO = ")[1]);
                        continue;
                    }
                }

                pagamentoEstornoJSON.setReciboEstorno(listReciboEstornoJSON);
                pagamentoEstornoJSON.setParcelaEstorno(listParcelaEstornoJSON);
                listPagamentoEstornoJSON.add(pagamentoEstornoJSON);

                EstornoResumidoContratoJSON estornoResumidoContratoJSON = new EstornoResumidoContratoJSON();
                estornoResumidoContratoJSON.setModalidadeEstorno(listModalidadeEstornoJSONS);
                estornoResumidoContratoJSON.setPagamentoEstorno(listPagamentoEstornoJSON);
                estornoResumidoContratoJSON.setCodigoContrato(formataNumeroTabelaLog(codContrato));
                estornoResumidoContratoJSON.setDataEstorno(Uteis.getData(rsEstornos.getDate("dataalteracao")));
                estornoResumidoContratoJSON.setUnidade(unidade);
                estornoResumidoContratoJSON.setValorTotalContrato(Double.valueOf(formataValorTabelaLog(valorContrato)));
                estornoResumidoContratoJSON.setValorPagoEstornado(Double.valueOf(formataValorTabelaLog(valorPagoEstornado)));
                estornoResumidoContratoJSON.setPlano(plano);
                estornoResumidoContratoJSON.setResponsavelContrato(responsavelContrato);
                estornoResumidoContratoJSON.setCodigoCliente(formataNumeroTabelaLog(codigoCliente));
                estornoResumidoContratoJSON.setMatricula(matricula);
                estornoResumidoContratoJSON.setNomeCliente(nomeCliente);
                estornoResumidoContratoJSON.setConsultor(rsEstornos.getString("responsavelalteracao"));

//            estornoResumidoContratoJSON
                estornosContrato.add(estornoResumidoContratoJSON);
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao preencher estornosContrato: " + e.getMessage());
        }
        return estornosContrato;
    }

    public List<EstornoResumidoJSON> resumoPeriodoEstornoPagamentos(final String dtInicio, final String dtFim) throws Exception {
        List<EstornoResumidoJSON> estornos = new ArrayList<>();
        StringBuilder sqlEstorno = new StringBuilder();
        sqlEstorno.append("SELECT * FROM log\n")
                .append("WHERE operacao LIKE '%ESTORNO - RECIBO PAGAMENTO%'\n")
                .append("AND dataalteracao BETWEEN '").append(dtInicio)
                .append("' and '")
                .append(dtFim)
                .append("'\n;");

        ResultSet rsEstornos = SuperFacadeJDBC.criarConsulta(sqlEstorno.toString(), con);
        while (rsEstornos.next()) {
            String valorAlterado = rsEstornos.getString("valorcampoalterado");
            String codRecibo = null;
            String unidade = null;
            String pgtoEstornado = null;
            String[] partesValorAlterado = valorAlterado.split("\n");
            EstornoResumidoJSON estornoResumidoJSON = new EstornoResumidoJSON();
            EstornoResumidoParcelaJSON estornoResumidoParcelaJSON = null;
            for (String linha : partesValorAlterado) {
                if (linha.toUpperCase().contains("CÓDIGO DO RECIBO = ") && UteisValidacao.emptyString(codRecibo)) {
                    codRecibo = linha.toUpperCase().split("CÓDIGO DO RECIBO = ")[1];
                    continue;
                } else if (linha.toUpperCase().contains("EMPRESA = ") && UteisValidacao.emptyString(unidade)) {
                    unidade = linha.toUpperCase().split("EMPRESA = ")[1];
                    continue;
                } else if (linha.toUpperCase().contains("VALOR TOTAL = ") && UteisValidacao.emptyString(pgtoEstornado)) {
                    pgtoEstornado = linha.toUpperCase().split("VALOR TOTAL = ")[1];
                    continue;
                } else if (linha.toUpperCase().contains("CÓDIGO DA PARCELA = ")) {
                    estornoResumidoParcelaJSON = new EstornoResumidoParcelaJSON();
                    estornoResumidoParcelaJSON.setCodigoParcela(formataNumeroTabelaLog(linha.toUpperCase().split("CÓDIGO DA PARCELA = ")[1]));
                    continue;
                } else if (linha.toUpperCase().contains("DESCRIÇÃO PARCELA = ")) {
                    estornoResumidoParcelaJSON.setDescricao(linha.toUpperCase().split("DESCRIÇÃO PARCELA = ")[1]);
                    continue;
                } else if (linha.toUpperCase().contains("VALOR DA PARCELA = ")) {
                    estornoResumidoParcelaJSON.setValorParcela(Double.valueOf(formataValorTabelaLog(linha.toUpperCase().split("VALOR DA PARCELA = ")[1])));
                    estornoResumidoJSON.getEstornoResumidoParcela().add(estornoResumidoParcelaJSON);
                    continue;
                }
            }

            estornoResumidoJSON.setCodigoRecibo(formataNumeroTabelaLog(codRecibo));
            estornoResumidoJSON.setData(Uteis.getData(rsEstornos.getDate("dataalteracao")));
            estornoResumidoJSON.setUnidade(unidade);
            estornoResumidoJSON.setPgtoEstornado(Double.valueOf(formataValorTabelaLog(pgtoEstornado)));
            estornoResumidoJSON.setConsultor(RemoverCaracter(rsEstornos.getString("responsavelalteracao")));
            estornos.add(estornoResumidoJSON);
        }
        return estornos;
    }

    public List<ReciboResumidoJSON> resumoPeriodoPagamentos(final String dtInicio, final String dtFim) throws Exception {
        StringBuilder sqlRec = new StringBuilder();
        sqlRec.append(" select r.data, e.codigo as empresacodigo, e.nome as empresa, r.codigo, \n");
        sqlRec.append(" r.responsavellancamento, u.nome as responsavellancamentonome, \n");
        sqlRec.append(" cli.codigo as cliente, p.nome, p.cfp, cli.matriculasesc, \n");
        sqlRec.append("  (select array_agg(codigo||';'||descricao||';'||valorparcela||';'||datavencimento||';'||situacao||';'||multa||';'||juros||';'||movparcelaoriginalmultajuros||';'||dataregistro||';'||dataAlteracaoSituacao||';'||codigoContrato) \n");
        sqlRec.append("  from (select distinct mpa.codigo, mpa.descricao, mpa.valorparcela, TO_CHAR(mpa.datavencimento, 'DD/MM/YYYY') as datavencimento, TO_CHAR(mpa.dataregistro, 'DD/MM/YYYY') as dataregistro, mpa.situacao, coalesce(mpr.multa,0.0) as multa, \n");
        sqlRec.append(" coalesce(mpr.juros,0.0)as juros, coalesce(mpp.movparcelaoriginalmultajuros,0)as movparcelaoriginalmultajuros, TO_CHAR(mpa.dataAlteracaoSituacao, 'DD/MM/YYYY') dataAlteracaoSituacao, COALESCE(mpa.contrato, 0) as codigoContrato  from movparcela mpa  \n");
        sqlRec.append("  left join movprodutoparcela mpp on mpp.movparcela = mpa.codigo left join movproduto mpr on mpp.movproduto = mpr.codigo and (multa > 0 or juros > 0)  \n");
        sqlRec.append("  where mpa.codigo in(  select movparcela from pagamentomovparcela where recibopagamento = r.codigo) order by mpa.codigo) \n");
        sqlRec.append("  as fooParcelas) as parcelas, \n");
        sqlRec.append("  (select array_agg(fp.descricao||';'||mpag.valortotal||';'||mpag.credito) from movpagamento mpag \n");
        sqlRec.append("  inner join formapagamento fp on mpag.formapagamento = fp.codigo \n");
        sqlRec.append("  where recibopagamento = r.codigo) as formas from recibopagamento r \n");
        sqlRec.append(" inner join empresa e on e.codigo = r.empresa \n");
        sqlRec.append(" inner join pessoa p on r.pessoapagador = p.codigo \n");
        sqlRec.append(" inner join cliente cli on cli.pessoa = p.codigo \n");
        sqlRec.append("  inner join usuario u on  r.responsavellancamento = u.codigo \n");
        sqlRec.append(" where r.data between '");
        sqlRec.append(dtInicio);
        sqlRec.append("' and '");
        sqlRec.append(dtFim).append("' ");

        ResultSet rsrec = SuperFacadeJDBC.criarConsulta(sqlRec.toString(), con);
        List<ReciboResumidoJSON> recibos = new ArrayList<>();
        while (rsrec.next()) {
            ReciboResumidoJSON recibo = new ReciboResumidoJSON();
            recibo.setResponsavelLancamentoCodigo(rsrec.getInt("responsavellancamento"));
            recibo.setResponsavelLancamento(rsrec.getString("responsavellancamentonome"));
            recibo.setCodigo(rsrec.getInt("codigo"));
            recibo.setData(Uteis.getData(rsrec.getDate("data")));
            ClienteResumidoJSON aluno = new ClienteResumidoJSON();
            aluno.setCodigo(rsrec.getInt("cliente"));
            aluno.setCpf(rsrec.getString("cfp"));
            aluno.setNome(rsrec.getString("nome"));
            aluno.setMatriculaSesc(rsrec.getString("matriculasesc"));
            recibo.setAluno(aluno);
            recibo.setUnidade(rsrec.getString("empresa"));
            recibo.setUnidadeCodigo(rsrec.getInt("empresacodigo"));
            recibo.setParcelasPagas(new ArrayList<>());

            Array arrayParcelas = rsrec.getArray("parcelas");
            if (arrayParcelas != null) {
                String[] valoresArrayParcelas = (String[]) arrayParcelas.getArray();
                for (String valor : valoresArrayParcelas) {
                    String[] dadosParcela = valor.split(";");
                    ParcelasPagasJSON parcela = new ParcelasPagasJSON();
                    parcela.setCodigo(Integer.valueOf(dadosParcela[0]));
                    parcela.setDescricao(dadosParcela[1]);
                    parcela.setValor(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosParcela[2])));
                    parcela.setVencimento(dadosParcela[3]);
                    parcela.setSituacao(dadosParcela[4]);
                    parcela.setValorJuro(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosParcela[6])));
                    parcela.setValorMulta(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosParcela[5])));
                    parcela.setParcelaOriginalMultaJuros(Integer.valueOf(dadosParcela[7]));
                    parcela.setDataLancamento(dadosParcela[8]);
                    parcela.setDataAlteracaoSituacao(dadosParcela[9]);
                    try {
                        final int codigoContrato = Integer.parseInt(dadosParcela[10]);
                        parcela.setCodigoContrato(codigoContrato != 0 ? codigoContrato : null);
                    } catch (Exception e) {
                        parcela.setCodigoContrato(null);
                    }

                    recibo.getParcelasPagas().add(parcela);
                }
            }

            recibo.setFormas(new ArrayList<PagamentoResumidoJSON>());
            Array arrayFormas = rsrec.getArray("formas");
            if(arrayFormas  != null) {
                String[] valoresArrayFormas = (String[]) arrayFormas.getArray();
                for (String valor : valoresArrayFormas) {
                    String[] dadosForma = valor.split(";");
                    PagamentoResumidoJSON pag = new PagamentoResumidoJSON();
                    if(dadosForma[2].equals("true")){
                        pag.setFormaPagamento("CREDITO CONTA CLIENTE");
                    } else {
                        pag.setFormaPagamento(dadosForma[0]);
                    }

                    pag.setValor(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosForma[1])));
                    recibo.getFormas().add(pag);
                }
            }
            recibos.add(recibo);
        }
        return recibos;
    }

    public List<ContratoResumidoJSON> resumoPeriodoContratos(final String dtInicio, final String dtFim, boolean vencimento ) throws Exception {
        String sqlContrato = "select c.consultor codConsultor, c.codigo, vigenciade, vigenciaate, vigenciaateajustada, datalancamento, numeromeses, p.nome, p.cfp, p.datanasc, p.nomemae, p.cpfmae, p.nomepai, p.cpfpai, \n" +
                "cli.matriculasesc, cli.codigo as cliente, e.codigo as empresacodigo, e.nome as empresa, c.valordescontoespecifico, c.valordescontoporcentagem, \n" +
                "c.responsavelcontrato, c.renovavelautomaticamente as renovavelautomaticamente, c.situacaocontrato as situacaocontrato, u.nome as responsavelcontratonome, \n" +
                "(select pes.nome consultor from colaborador col inner join pessoa pes on pes.codigo = col.pessoa where col.codigo = c.consultor), \n" +
                "(select array_agg(m.codigo||';'||m.nome||';'||cm.valormodalidade::numeric ||';'||cm.valorfinalmodalidade::numeric|| ';' || (CASE WHEN cc.codigocentrocustos is null THEN '' ELSE cc.codigocentrocustos END) || (CASE WHEN cc.nome is null THEN ' ' ELSE (' - ' || cc.nome) END) || '|' ||(\n" +
                " select coalesce(array_agg(ht.horainicial||';'||ht.diasemana||';'||t.descricao||';'||t.codigo||';'||pe.nome)::text,'') from matriculaalunohorarioturma ma\n" +
                " inner join horarioturma ht on ht.codigo = ma.horarioturma \n" +
                " inner join turma t on t.codigo = ht.turma \n" +
                "inner join colaborador cl on cl.codigo = ht.professor \n" +
                "inner join pessoa pe on pe.codigo = cl.pessoa \n" +
                "where ma.contrato  =   cm.contrato  and t.modalidade =  m.codigo \n" +
                ")) from modalidade m \n" +
                "inner join contratomodalidade cm on cm.modalidade = m.codigo \n" +
                "left join rateiointegracao ri on ri.modalidade = m.codigo and ri.empresa = c.empresa\n" +
                "left join centrocusto cc on cc.codigo = ri.centrocusto \n" +
                " where cm.contrato = c.codigo and (ri is null or (ri.planoconta is null))) as modalidades, \n" +
                "(select array_agg(codigo||';'||descricao||';'||valorparcela||';'||datavencimento||';'||situacao||';'||parcelasrenegociadas||';'||dataregistro ||';'||dataAlteracaoSituacao) \n" +
                " from (select codigo,descricao,valorparcela,TO_CHAR(datavencimento, 'DD/MM/YYYY') as datavencimento,TO_CHAR(dataregistro, 'DD/MM/YYYY') as dataregistro, situacao,coalesce(parcelasrenegociadas,'') as parcelasrenegociadas, TO_CHAR(dataAlteracaoSituacao, 'DD/MM/YYYY') as dataAlteracaoSituacao " +
                " from movparcela where contrato = c.codigo  ";
            if(vencimento){
                sqlContrato +=" and datavencimento  between '" + dtInicio + "' and '" + dtFim + "'  \n";
            }

            sqlContrato +=" order by codigo ) as fooParcela) as parcelas \n" +
                "from contrato c \n" +
                "inner join contratoduracao cd on c.codigo = cd.contrato \n" +
                "inner join pessoa p on c.pessoa = p.codigo \n" +
                "inner join cliente cli on cli.pessoa = p.codigo \n" +
                "inner join empresa e on e.codigo = c.empresa \n" +
                "inner join usuario u on u.codigo = c.responsavelcontrato \n";
            if(vencimento) {
                sqlContrato += "where c.codigo  in (select contrato from movparcela mvp where " +
                        " mvp.datavencimento  between '" + dtInicio + "' and '" + dtFim + "' )";
            } else {
                sqlContrato += "where c.datalancamento between '" + dtInicio + "' and '" + dtFim + "' " +
                        "or c.codigo = (select contrato from movparcela mvp where mvp.contrato = c.codigo " +
                        "and mvp.dataregistro between '" + dtInicio + "' and '" + dtFim + "' limit 1)";
            }

        ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sqlContrato, con);
        List<ContratoResumidoJSON> contratos = new ArrayList<>();
        while (resultSet.next()) {
            ContratoResumidoJSON contrato = new ContratoResumidoJSON();
            contrato.setResponsavelLancamentoCodigo(resultSet.getInt("responsavelcontrato"));
            contrato.setResponsavelLancamento(resultSet.getString("responsavelcontratonome"));
            contrato.setCodigo(resultSet.getInt("codigo"));
            contrato.setVersao("1.0");
            contrato.setInicio(Uteis.getData(resultSet.getDate("vigenciade")));
            contrato.setFim(Uteis.getData(resultSet.getDate("vigenciaateajustada")));
            contrato.setDataTerminoOriginal(Uteis.getData(resultSet.getDate("vigenciaate")));
            contrato.setLancamento(Uteis.getData(resultSet.getDate("datalancamento")));
            contrato.setDuracao(resultSet.getInt("numeromeses"));
            contrato.setValordescontoespecifico(Uteis.arredondarForcando2CasasDecimais(resultSet.getDouble("valordescontoespecifico")));
            contrato.setValordescontoporcentagem(Uteis.arredondarForcando2CasasDecimais(resultSet.getDouble("valordescontoporcentagem")));
            contrato.setCodConsultor(resultSet.getInt("codConsultor"));
            contrato.setConsultor(resultSet.getString("consultor"));
            ClienteResumidoJSON aluno = new ClienteResumidoJSON();
            aluno.setCodigo(resultSet.getInt("cliente"));
            aluno.setCpf(resultSet.getString("cfp"));
            aluno.setNome(resultSet.getString("nome"));
            if (!UteisValidacao.emptyString(resultSet.getString("datanasc"))) {
                aluno.setDataNascimento(Uteis.getData(resultSet.getDate("datanasc"), "br"));
            }
            aluno.setMatriculaSesc(resultSet.getString("matriculasesc"));
            if (!UteisValidacao.emptyString(resultSet.getString("nomemae"))) {
                aluno.setResponsavel(resultSet.getString("nomemae"));
                aluno.setCpfResponsavel(resultSet.getString("cpfmae"));
            } else {
                aluno.setResponsavel(resultSet.getString("nomepai"));
                aluno.setCpfResponsavel(resultSet.getString("cpfpai"));
            }
            contrato.setAluno(aluno);
            contrato.setUnidade(resultSet.getString("empresa"));
            contrato.setUnidadeCodigo(resultSet.getInt("empresacodigo"));
            contrato.setRenovadoAutomaticamente(contrato.getResponsavelLancamentoCodigo() == 3 && resultSet.getString("situacaocontrato").equals("RN"));
            contrato.setParcelas(new ArrayList<>());
            contrato.setModalidades(new ArrayList<>());

            //modalidades
            Array arrayModalidades = resultSet.getArray("modalidades");
            if(arrayModalidades  != null) {
                String[] valoresArrayModalidades = (String[]) arrayModalidades.getArray();
                for (String valor : valoresArrayModalidades) {
                    String[] partesInformacao = valor.split("\\|");
                    String[] dadosModalidade = partesInformacao[0].split(";");
                    ModalidadeResumidaJSON modalidade = new ModalidadeResumidaJSON();
                    modalidade.setCodigoModalidade(Integer.valueOf(dadosModalidade[0]));
                    modalidade.setModalidade(dadosModalidade[1]);
                    modalidade.setValorModalidade(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosModalidade[2])));
                    modalidade.setValorFinalModalidade(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosModalidade[3])));
                    modalidade.setCentroDeCustos(UteisValidacao.emptyString(dadosModalidade[4].trim()) ? dadosModalidade[4].trim() : dadosModalidade[4].toUpperCase());
                    if (partesInformacao.length > 1) {
                        String[] dadosTurmas = partesInformacao[1].replaceAll("[{}\"]", "").split(",");
                        for (String valoresturma : dadosTurmas) {
                            String[] dadoTurma = valoresturma.split(";");
                            TurmaResumidaJSON turma = new TurmaResumidaJSON();
                            if (dadoTurma.length == 1) {
                                turma.setDiaSemana(dadoTurma[1]);
                            } else if (dadoTurma.length == 2) {
                                turma.setDiaSemana(dadoTurma[1]);
                                turma.setHorario(dadoTurma[0]);
                            } else if (dadoTurma.length == 3) {
                                turma.setDiaSemana(dadoTurma[1]);
                                turma.setHorario(dadoTurma[0]);
                                turma.setTurma(dadoTurma[2]);
                            } else if (dadoTurma.length == 4) {
                                turma.setDiaSemana(dadoTurma[1]);
                                turma.setHorario(dadoTurma[0]);
                                turma.setTurma(dadoTurma[2]);
                                turma.setProfessor(dadoTurma[4]);
                            } else if (dadoTurma.length == 5) {
                                turma.setDiaSemana(dadoTurma[1]);
                                turma.setHorario(dadoTurma[0]);
                                turma.setTurma(dadoTurma[2]);
                                turma.setProfessor(dadoTurma[4]);
                                turma.setCodigoTurma(Integer.valueOf(dadoTurma[3]));
                            }
                            modalidade.getTurmas().add(turma);
                        }
                    }
                    contrato.getModalidades().add(modalidade);
                }
            }

            //parcelas
            int qtdParcelas = 0;
            Array arrayParcelas = resultSet.getArray("parcelas");
            if(arrayParcelas  != null) {
                String[] valoresArrayParcelas = (String[]) arrayParcelas.getArray();
                for (String valor : valoresArrayParcelas) {
                    String[] dadosParcela = valor.split(";");
                    List<ParcelasRenegociadasJSON> parcelasRenegociadas = new ArrayList<>();
                    if (!UteisValidacao.emptyString(dadosParcela[5])) {
                        JSONObject jsonParcelas = new JSONObject(dadosParcela[5]);
                        for (int i = 0; i < jsonParcelas.getJSONArray("listaMovParcelaRenegociar").length(); i++) {

                            JSONObject j = new JSONObject(jsonParcelas.getJSONArray("listaMovParcelaRenegociar").get(i).toString());

                            ParcelasRenegociadasJSON parcelasRenegociadasJSON = new ParcelasRenegociadasJSON();
                            parcelasRenegociadasJSON.setCodigo(j.getInt("codigo"));
                            parcelasRenegociadasJSON.setDataVencimento(j.getString("dataVencimento"));
                            parcelasRenegociadasJSON.setDescricao(j.getString("descricao"));
                            parcelasRenegociadasJSON.setValorParcela(Uteis.arredondarForcando2CasasDecimais(j.getDouble("valorParcela")));
                            parcelasRenegociadas.add(parcelasRenegociadasJSON);
                        }
                    }

                    ParcelaResumidaJSON parcela = new ParcelaResumidaJSON();
                    parcela.setCodigo(Integer.valueOf(dadosParcela[0]));
                    parcela.setDescricao(dadosParcela[1]);
                    parcela.setValor(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(dadosParcela[2])));
                    parcela.setVencimento(dadosParcela[3]);
                    parcela.setSituacao(dadosParcela[4]);
                    parcela.setParcelasRenegociadas(parcelasRenegociadas);
                    parcela.setDataLancamento(dadosParcela[6]);
                    parcela.setDataAlteracaoSituacao(dadosParcela[7]);
                    contrato.getParcelas().add(parcela);

                    if (!dadosParcela[4].equals("RG"))
                        qtdParcelas++;
                }
            }
            contrato.setQtdParcelas(qtdParcelas);
            contratos.add(contrato);
        }
        return contratos;
    }

    public String formataValorTabelaLog(String valor) {
        if (UteisValidacao.emptyString(valor)) {
            valor = "0.0";
        } else {
            try {
                valor = valor.replaceAll("[R$;]", "").replace(",", ".").trim();
                valor = String.valueOf(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(valor.trim())));
            } catch (Exception e) {
                valor = "0.0";
            }
        }

        return valor;
    }

    public int formataNumeroTabelaLog(String valor) {
        if (UteisValidacao.emptyString(valor))
            return 0;

        try {
            return Integer.valueOf(valor.trim());
        } catch (Exception e) {
            return 0;
        }
    }

    public JSONArray categorias() throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, nome from categoria ", con);
        JSONArray array = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("codigo", rs.getInt("codigo"));
            obj.put("descricao", rs.getString("nome"));
            array.put(obj);
        }
        return array;
    }

    public JSONArray profissoes() throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, descricao from profissao  ", con);
        JSONArray array = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("codigo", rs.getInt("codigo"));
            obj.put("descricao", rs.getString("descricao"));
            array.put(obj);
        }
        return array;
    }

    public JSONArray classificacoes() throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, nome from classificacao", con);
        JSONArray array = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("codigo", rs.getInt("codigo"));
            obj.put("descricao", rs.getString("nome"));
            array.put(obj);
        }
        return array;
    }

    public JSONObject persistirContratoJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.persistirContratoJSON");
        try {

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            con.setAutoCommit(false);
            ContratoJSON contratoJSON = new ContratoJSON(new JSONObject(json));
            validarDadosBasicosContrato(contratoJSON);
            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select codigo from contrato where idexterno = " + contratoJSON.getIdExterno() + " and empresa = " + empresa, con);

            if (rsContrato.next()) {
                objRetorno.put("status", "warning");
                objRetorno.put("codigoRegistroZW", rsContrato.getInt("codigo"));
                objRetorno.put("mensagem", "Contrato já importado");
                return objRetorno;
            }

            StringBuilder sqlCliente = new StringBuilder();
            sqlCliente.append("select c.codigo as cliente, p.codigo as pessoa, p.datanasc as dataNascimento from cliente c \n");
            sqlCliente.append("inner join pessoa p on p.codigo = c.pessoa \n");

            if (!UteisValidacao.emptyNumber(contratoJSON.getCodigoMatricula())) {
                sqlCliente.append("where c.codigomatricula = ").append(contratoJSON.getCodigoMatricula()).append(" \n");
            } else {
               sqlCliente.append("where p.idExterno = '").append(contratoJSON.getIdClienteExterno()).append("' \n");
            }
            ResultSet rsCliente = SuperFacadeJDBC.criarConsulta(sqlCliente.toString(), con);
            ClienteVO clienteVO = new ClienteVO();
            if (rsCliente.next()) {
                clienteVO.setCodigo(rsCliente.getInt("cliente"));
                clienteVO.getPessoa().setCodigo(rsCliente.getInt("pessoa"));
                clienteVO.getPessoa().setDataNasc(rsCliente.getDate("dataNascimento"));
            } else {
                objRetorno.put("status", "error");
                objRetorno.put("mensagem", "Não existe aluno com o id informado");
                return objRetorno;
            }

            Plano planoDao = new Plano(con);
            PlanoVO planoVO = null;
            if(!UteisValidacao.emptyNumber(contratoJSON.getCodigoPlano())){
                planoVO = planoDao.consultarPorChavePrimaria(contratoJSON.getCodigoPlano(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }else{
                planoVO = planoDao.consultarPorDescricao("PLANO IMPORTACAO", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (UteisValidacao.emptyObject(planoVO) || UteisValidacao.emptyNumber(planoVO.getCodigo())) {
                objRetorno.put("status", "error");
                objRetorno.put("mensagem", "Não foi encontrado o plano informado.");
                return objRetorno;
            }

            Map<Integer, PlanoModalidadeVezesSemanaVO> modalidades = consultarModalidadesContrato(contratoJSON.getModalidades(), planoVO.getCodigo(), empresaVO);

            HorarioTurma horarioTurmaDAO = new HorarioTurma(con);
            Turma turmaDAO = new Turma(con);
            List<TurmaVO> turmasHorariosSelecionados = new ArrayList<>();
            if (contratoJSON.getContratoComTurma()) {
                montarHorariosTurmas(contratoJSON, horarioTurmaDAO, turmaDAO, clienteVO, turmasHorariosSelecionados, empresaVO);
                if(turmasHorariosSelecionados.isEmpty()){
                    throw new Exception("Nenhuma turma encontrada para modalidade informada");
                }
            }
            horarioTurmaDAO = null;
            turmaDAO = null;

            UsuarioVO consultor = obterUsuarioConsultor(contratoJSON.getConsultor(), empresaVO, true);
            Contrato contratoDao = new Contrato(con);
            ContratoControle contratoControle = contratoDao.obterContratoControle(planoVO.getCodigo(), clienteVO.getCodigo(), false, empresa, new ContratoVO(), new ContratoVO());

            contratoControle.getContratoVO().setEmpresa(empresaVO);

            ContratoVO contratoASerRenovado = null;
            if (contratoJSON.isRenovacaoContrato()) {
                List<ContratoVO> listaContratos = contratoDao.consultarContratosRenovar(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                if (listaContratos != null && !listaContratos.isEmpty()) {
                    contratoASerRenovado = listaContratos.get(0);
                }
            }

            contratoControle.getContratoVO().setImportacao(true);

            processarModalidadesSelecionadas(contratoControle, modalidades);

            if (contratoJSON.getContratoComTurma()) {
                processarTurmasModalidades(contratoControle, turmasHorariosSelecionados);
            }

            processarHorario(contratoControle, planoVO.getCodigo(), contratoJSON.getContratoComTurma());
            configurarDuracaoCondicaoPagamento(contratoControle, planoVO.getCodigo(), contratoJSON.getDuracao(), contratoJSON.getDuracao());
            if (!UteisValidacao.emptyNumber(contratoJSON.getQtdParcelas())) {
                contratoControle.getContratoVO().getPlanoCondicaoPagamento().getCondicaoPagamento().setNrParcelas(contratoJSON.getQtdParcelas());
            }
            contratoControle.getContratoVO().setVigenciaDe(Uteis.getDate(contratoJSON.getDataInicio()));
            contratoControle.getContratoVO().setVigenciaAte(Uteis.getDate(contratoJSON.getDataFinal()));
            contratoControle.getContratoVO().setVigenciaAteAjustada(Uteis.getDate(contratoJSON.getDataFinal()));
            contratoControle.setDataInicioContrato(Uteis.getDate(contratoJSON.getDataInicio()));
            contratoControle.getContratoVO().setValorBaseCalculo(contratoJSON.getValorContrato());
            contratoControle.getContratoVO().setValorFinal(contratoJSON.getValorContrato() + contratoJSON.getValorMatricula());

            // ZERAR OS VALORES DOS PRODUTOS DOS PLANOS
            validarSituacaoContrato(contratoControle.getContratoVO(), contratoDao);
            for (Object obj : contratoControle.getContratoVO().getPlano().getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO) obj;
                if (contratoJSON.getValorMatricula() > 0.0 && ((planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("MA") && contratoControle.getContratoVO().getSituacaoContrato().equals("MA"))
                        || (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("RE") && contratoControle.getContratoVO().getSituacaoContrato().equals("RE"))
                        || (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("RN") && contratoControle.getContratoVO().getSituacaoContrato().equals("RN")))) {
                    planoProdutoSugeridoVO.setValorProduto(contratoJSON.getValorMatricula());
                    planoProdutoSugeridoVO.getProduto().setValorFinal(contratoJSON.getValorMatricula());
                } else {
                    planoProdutoSugeridoVO.setValorProduto(0.0);
                    planoProdutoSugeridoVO.getProduto().setValorFinal(0.0);
                }
            }


            String fecharNegociacao = contratoControle.fecharNegociacao(null, true);
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            contratoControle.getContratoVO().setConsultor(consultor.getColaboradorVO());
            contratoControle.getContratoVO().setIdExterno(contratoJSON.getIdExterno());
            contratoControle.getContratoVO().setObservacao(contratoJSON.getObservacaoContrato());
            if (json.contains("versao:")) {
                contratoControle.getContratoVO().setOrigemSistema(OrigemSistemaEnum.IMPORTACAO_API);
            }

            if (contratoASerRenovado != null) {
                contratoControle.setVigenciaAteDoUltimoContrato((Date) contratoASerRenovado.getVigenciaAte().clone());
                contratoControle.setVigenciaAteAjustadaDoUltimoContrato((Date) contratoASerRenovado.getVigenciaAteAjustada().clone());
                contratoControle.setVigenciaDeDoUltimoContrato((Date) contratoASerRenovado.getVigenciaDe().clone());

                contratoControle.getContratoVO().setSituacaoContrato(SituacaoContratoEnum.RENOVACAO.getCodigo());
                contratoControle.getContratoVO().setRenovarContrato(true);
                contratoControle.getContratoVO().setContratoOrigemRenovacao(contratoASerRenovado);
                contratoControle.getContratoVO().setContratoBaseadoRenovacao(contratoASerRenovado.getCodigo());
                contratoControle.getContratoVO().setDataMatricula(contratoASerRenovado.getDataMatricula());

                int dias = (int) Uteis.nrDiasEntreDatas(Uteis.getDate(contratoJSON.getDataInicio()), contratoASerRenovado.getVigenciaAteAjustada()) + 1;

                contratoControle.getContratoVO().setVigenciaDe(Uteis.somarDias(Uteis.getDate(contratoJSON.getDataInicio()), dias));
                contratoControle.getContratoVO().setVigenciaAteAjustada(Uteis.somarDias(Uteis.getDate(contratoJSON.getDataFinal()), dias));
                contratoControle.getContratoVO().setDataMatricula(Uteis.somarDias(Uteis.getDate(contratoJSON.getDataCadastro()), dias));
                contratoControle.getContratoVO().setDataLancamento(Uteis.somarDias(Uteis.getDate(contratoJSON.getDataCadastro()), dias));
                contratoControle.getContratoVO().setDataPrimeiraParcela(Uteis.somarDias(Uteis.getDate(contratoJSON.getDataInicio()), dias));
            } else {
                contratoControle.getContratoVO().setVigenciaAteAjustada(Uteis.getDate(contratoJSON.getDataFinal()));
                contratoControle.getContratoVO().setDataMatricula(Uteis.getDate(contratoJSON.getDataCadastro()));
                contratoControle.getContratoVO().setDataLancamento(Uteis.getDate(contratoJSON.getDataCadastro()));
                contratoControle.getContratoVO().setDataPrimeiraParcela(Uteis.getDate(contratoJSON.getDataInicio()));
            }

            String gravarContrato = contratoControle.gravar(usuarioVO, false, null, false, false, true);
            if (gravarContrato.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }

            JSONObject jsonV2 = new JSONObject(json);
            if (jsonV2.has("versao") && jsonV2.getString("versao").equals("2")) {
                MovPagamento movPagamento = new MovPagamento(con);
                JSONArray parcelasPagar = jsonV2.getJSONArray("parcelasPagar");
                for (int i = 0; i < parcelasPagar.length(); i++) {
                    JSONObject parcela = parcelasPagar.getJSONObject(i);
                    for (MovParcelaVO movParcela : contratoControle.getContratoVO().getMovParcelaVOs()) {
                        if (movParcela.getDescricao().equalsIgnoreCase(parcela.optString("descricao"))) {

                            MovParcelaVO movParcelaPagar = verificarValorParcelaRenegociarSeNecessario(movParcela, parcela.optJSONArray("formasPagamento"), usuarioVO);

                            if (!isCodigoFormaPagamentoInformado(parcela.optJSONArray("formasPagamento"))) {
                                continue;
                            }

                            Date dataPagamento = obterDataPagamentoParcelaContratoImportacao(parcela, contratoControle.getContratoVO().getDataLancamento());

                            parcela.put("contrato", contratoControle.getContratoVO().getCodigo());
                            parcela.put("usuario", new Usuario(con).getUsuarioRecorrencia().getCodigo());
                            parcela.put("parcelasContrato", new JSONArray(new String[]{String.valueOf(movParcelaPagar.getCodigo())}));
                            parcela.put("formasPagamento", parcela.getJSONArray("formasPagamento"));
                            PagamentoDTO pagamentoDTO = new PagamentoDTO(parcela, true);

                            movPagamento.validarParametros(pagamentoDTO, true);

                            movPagamento.geraReciboContratoImportacao(pagamentoDTO, dataPagamento);
                        }
                    }
                }
            }

            atualizarDataLancamentoParcelasProdutosContrato(contratoControle.getContratoVO(), con);

            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", contratoControle.getContratoVO().getCodigo());

            String mensagem = "Contrato importado com sucesso!";
            String nomeConsultorPadrao = "PACTO - MÉTODO DE GESTÃO";

            if (nomeConsultorPadrao.equals(consultor.getNome())
                    && !nomeConsultorPadrao.equals(contratoJSON.getConsultor())
                    && !UteisValidacao.emptyString(contratoJSON.getConsultor())) {
                mensagem += " Obs: O consultor informado '"
                        + contratoJSON.getConsultor()
                        + "' é inexistente ou inválido. O contrato foi lançado para consultor PACTO.";
            }

            objRetorno.put("mensagem", mensagem);

            return objRetorno;

        } catch (Exception e) {
            if (!con.getAutoCommit()) {
                con.rollback();
                con.setAutoCommit(true);
            }
            objRetorno.put("status", "error");
            objRetorno.put("mensagem","#### ERRO persistirContratoJSON - " + e.getMessage());
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### ERRO persistirContratoJSON - " + e.getMessage());
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    private void montarHorariosTurmas(ContratoJSON contratoJSON, HorarioTurma horarioTurmaDAO, Turma turmaDAO, ClienteVO clienteVO, List<TurmaVO> turmasHorariosSelecionados, EmpresaVO empresa) throws Exception {
        for (Integer codigoHorario : contratoJSON.getHorariosTurma()) {
            HorarioTurmaVO horario = horarioTurmaDAO.consultarPorCodigo(codigoHorario, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            for (ModalidadeJSON modalidade : contratoJSON.getModalidades()) {
                TurmaVO turma = turmaDAO.consultarPorChavePrimaria(horario.getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (empresa != null && !UteisValidacao.emptyNumber(empresa.getCodigo()) &&
                        turma.getEmpresa() != null && !UteisValidacao.emptyNumber(turma.getEmpresa().getCodigo()) &&
                        Objects.equals(turma.getEmpresa().getCodigo(), empresa.getCodigo())) {
                    if (turma.getModalidade().getCodigo().equals(modalidade.getCodigo())) {
                        if (clienteVO.getPessoa().getDataNasc() != null) {
                            Integer idadeAluno = Uteis.calcularIdadePessoa(Calendario.hoje(), clienteVO.getPessoa().getDataNasc());
                            if (idadeAluno >= turma.getIdadeMinima() && idadeAluno <= turma.getIdadeMaxima()) {
                                if (!turmasHorariosSelecionados.isEmpty()) {
                                    boolean achouTurma = false;
                                    for (TurmaVO turmaHorario : turmasHorariosSelecionados) {
                                        if (turmaHorario.getCodigo().equals(turma.getCodigo())) {
                                            achouTurma = true;
                                            turmaHorario.getHorarioTurmaVOs().add(horario);
                                        }
                                    }
                                    if (!achouTurma) {
                                        turma.setHorarioTurmaVOs(new ArrayList());
                                        turma.getHorarioTurmaVOs().add(horario);
                                        turmasHorariosSelecionados.add(turma);
                                    }
                                } else {
                                    turma.setHorarioTurmaVOs(new ArrayList());
                                    turma.getHorarioTurmaVOs().add(horario);
                                    turmasHorariosSelecionados.add(turma);
                                }
                            } else {
                                throw new Exception("O horario " + horario.getDiaSemana_Apresentar() + " - " + horario.getHoraInicial() + " às " + horario.getHoraFinal() + " não é compatível com a idade do aluno");
                            }
                        } else {
                            throw new Exception("Data de nascimento do aluno inválida");
                        }
                    }
                } else {
                    throw new Exception(String.format("O horário %d, não pertence a empresa: %s", horario.getCodigo(), (empresa == null ? "Não Encontrada" : empresa.getNome())));
                }
            }
        }
    }

    private void processarTurmasModalidades(ContratoControle contratoControle, List<TurmaVO> turmasHorariosSelecionados) {
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
            for (TurmaVO turma : turmasHorariosSelecionados) {
                if(contratoModalidadeVO.getModalidade().getCodigo().equals(turma.getModalidade().getCodigo())) {
                    ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = new ContratoModalidadeTurmaVO();
                    List<ContratoModalidadeHorarioTurmaVO> contratoModalidadeHorariosTurmaVO = new ArrayList<>();
                    contratoModalidadeTurmaVO.setContratoModalidade(contratoModalidadeTurmaVO.getCodigo());
                    turma.setTurmaEscolhida(true);
                    contratoModalidadeTurmaVO.setTurma(turma);
                    for (HorarioTurmaVO horario : turma.getHorarioTurmaVOs()) {
                        ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO = new ContratoModalidadeHorarioTurmaVO();
                        horario.setHorarioTurmaEscolhida(true);
                        contratoModalidadeHorarioTurmaVO.setHorarioTurma(horario);
                        contratoModalidadeHorariosTurmaVO.add(contratoModalidadeHorarioTurmaVO);
                    }
                    contratoModalidadeTurmaVO.setContratoModalidadeHorarioTurmaVOs(contratoModalidadeHorariosTurmaVO);
                    if(contratoModalidadeVO.getContratoModalidadeTurmaVOs() == null){
                        contratoModalidadeVO.setContratoModalidadeTurmaVOs(new ArrayList<>());
                    }
                    contratoModalidadeVO.getContratoModalidadeTurmaVOs().add(contratoModalidadeTurmaVO);


                    ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO = new ContratoModalidadeVezesSemanaVO();
                    contratoModalidadeVezesSemanaVO.setContratoModalidade(contratoModalidadeVO.getCodigo());
                    contratoModalidadeVezesSemanaVO.setNrVezes(contratoModalidadeVO.getModalidade().getNrVezes());
                    contratoModalidadeVezesSemanaVO.setVezeSemanaEscolhida(true);
                    contratoModalidadeVO.setContratoModalidadeVezesSemanaVO(contratoModalidadeVezesSemanaVO);
                }
            }
        }
    }

    private Date obterDataPagamentoParcelaContratoImportacao(JSONObject parcela, Date dataLancamentoContrato) throws Exception {
        Date dataPagamento = Calendario.hoje();
        if (!UteisValidacao.emptyString(parcela.optString("dataPagamento"))) {
            try {
                dataPagamento = Calendario.getDate("dd/MM/yyyy", parcela.optString("dataPagamento"));
            } catch (Exception e) {
                throw new Exception(String.format("Data de pagamento inválida: %d - %s", parcela.optString("descricao"),  parcela.optString("dataPagamento")));
            }
        }
        if (Calendario.menor(dataPagamento, dataLancamentoContrato)) {
            String msg = String.format("Não foi possivel lançar o pagamento da parcela: %s, porque a data de pagamento %s não pode ser anterior a data de lançamento do contrato: %s", parcela.getString("descricao"), parcela.optString("dataPagamento"), Calendario.getData(dataLancamentoContrato, "dd/MM/yyyy"));
            throw new Exception(msg);
        }
        return dataPagamento;
    }

    private void atualizarDataLancamentoParcelasProdutosContrato(ContratoVO contratoVO, Connection con) {
        try {
            String updateParcela = "update movparcela set dataregistro = ? where contrato = ?";
            PreparedStatement pstm = con.prepareStatement(updateParcela);
            pstm.setDate(1, Uteis.getDataJDBC(contratoVO.getDataLancamento()));
            pstm.setInt(2, contratoVO.getCodigo());
            pstm.execute();

            String updateProdutos = "update movproduto set datalancamento = ? where contrato = ?";
            pstm = con.prepareStatement(updateProdutos);
            pstm.setDate(1, Uteis.getDataJDBC(contratoVO.getDataLancamento()));
            pstm.setInt(2, contratoVO.getCodigo());
            pstm.execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean isCodigoFormaPagamentoInformado(JSONArray pagamentos) {
        for (int i = 0; i < pagamentos.length(); i++) {
            if (!UteisValidacao.emptyNumber(pagamentos.getJSONObject(i).optInt("codigoFormaPagamento"))) {
                return true;
            }
        }
        return false;
    }

    private MovParcelaVO verificarValorParcelaRenegociarSeNecessario(MovParcelaVO movParcela, JSONArray pagamentos, UsuarioVO usuarioVO) throws Exception {
        Double valorTotal = 0.0;

        for (int j = 0; j < pagamentos.length(); j++) {
            valorTotal += pagamentos.getJSONObject(j).optDouble("valor");
        }

        if (valorTotal <= 0.0) {
            return movParcela;
        }

        Double valorTotalParcelas = Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela());
        valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal);

        if (!valorTotalParcelas.equals(valorTotal)) {
            return renegociarParcela(movParcela, valorTotal, usuarioVO);
        }
        return movParcela;
    }

    public MovParcelaVO renegociarParcela(MovParcelaVO movParcelaVO, Double valorTotalPgamentos, UsuarioVO usuarioVO) throws Exception {
        Double valorDiferencaRenegociar = valorTotalPgamentos - movParcelaVO.getValorParcela();
        MovParcelaVO parcelaTaxa = new MovParcelaVO();
        parcelaTaxa.setDescricao("");
        parcelaTaxa.setDataVencimento(Calendario.hoje());

        MovParcelaVO parcelaDesconto = new MovParcelaVO();
        parcelaDesconto.setDescricao("");
        parcelaDesconto.setDataVencimento(Calendario.hoje());

        String tipoProdutoExtra;

        String descricaoParcela = "";

        Double valorFinalNovaParcela = movParcelaVO.getValorParcela();
        if (valorDiferencaRenegociar < 0.0) {
            tipoProdutoExtra = "DE";
            descricaoParcela = "DESCONTO";
            parcelaDesconto.setValorParcela(valorDiferencaRenegociar * -1);
            valorFinalNovaParcela -= parcelaDesconto.getValorParcela();
        } else {
            tipoProdutoExtra = "TX";
            descricaoParcela = "ACRESCIMO";
            parcelaTaxa.setValorParcela(valorDiferencaRenegociar);
            valorFinalNovaParcela += parcelaTaxa.getValorParcela();
        }

        MovParcela movParcelaDAO = new MovParcela(con);

        List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
        parcelasRenegociar.add(movParcelaVO);

        // parcela desconto\acrescimo
        MovParcelaVO parcelaRenegociar = new MovParcelaVO();
        parcelaRenegociar.setDescricao(descricaoParcela);
        parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorDiferencaRenegociar));
        parcelaRenegociar.setDataVencimento(Calendario.hoje());
        parcelasRenegociar.add(parcelaRenegociar);

        // Parcelas Renegociadas
        List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
        MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
        novaParcela.setDescricao("PARCELA RENEGOCIADA");
        novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorFinalNovaParcela));
        novaParcela.setDataRegistro(Calendario.hoje());
        parcelasRenegociadas.add(novaParcela);

        movParcelaDAO.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, parcelaTaxa, tipoProdutoExtra, false, null, null, 0.0, false, usuarioVO, true, false, true, null, null);
        List<MovParcelaVO> parcelasNovas = new ArrayList<>();
        parcelasNovas.addAll(parcelasRenegociadas);

        movParcelaDAO = null;
        return novaParcela;
    }

    private LogIntegracoesVO inserirLogIntegracao(String json, String operacao) {
        LogIntegracoesVO logIntegracoesVO = new LogIntegracoesVO();
        try {
            LogIntegracoes logIntegracoesDao = new LogIntegracoes(con);
            logIntegracoesVO.setDadosRecebidos(json);
            logIntegracoesVO.setDataLancamento(Calendario.hoje());
            logIntegracoesVO.setServico(operacao);
            logIntegracoesDao.incluir(logIntegracoesVO);
            logIntegracoesDao =null;

        } catch (Exception e){
            Uteis.logar("Erro ao inserir log integracao  "+operacao +": "+ e.getMessage() + " # Dados recebido: "+json);
        }
        return logIntegracoesVO;
    }

    private void alterarLogIntegracao(LogIntegracoesVO logIntegracoesVO, String resultado) {
        try {
            LogIntegracoes logIntegracoesDao = new LogIntegracoes(con);
            logIntegracoesVO.setResultado(resultado);
            logIntegracoesDao.alterar(logIntegracoesVO);
            logIntegracoesDao =null;
        } catch (Exception e){
            Uteis.logar("Erro ao alterar log integracao  "+logIntegracoesVO.getCodigo() +": "+ e.getMessage());
        }
    }

    public ColaboradorVO obterConsultor(String nomeConsultor, EmpresaVO empresa, boolean importacao) throws Exception {
        Colaborador colaboradorDao = new Colaborador(con);
        ColaboradorVO colaboradorVO = null;

        if (!UteisValidacao.emptyString(nomeConsultor)) {
            if(importacao) {
                colaboradorVO = colaboradorDao.consultarPorNomeColaboradorImportacao(nomeConsultor.trim(), empresa.getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                colaboradorVO = colaboradorDao.consultarPorNomeColaborador(nomeConsultor.trim(), empresa.getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }

        if ((colaboradorVO == null || UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) && !UteisValidacao.emptyString(nomeConsultor) && !importacao) {
            colaboradorVO = new ColaboradorVO();
            TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
            tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
            colaboradorVO.getPessoa().setNome(nomeConsultor);
            colaboradorVO.getPessoa().setDataNasc(Calendario.getDate("dd/MM/yyyy", "01/01/2000"));
            colaboradorVO.setEmpresa(empresa);
            colaboradorVO.setSituacao("AT");
            colaboradorVO.setDiaVencimento(1);
            colaboradorVO.setPorcComissao(1.0);
            colaboradorVO.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
            colaboradorDao.incluirSemCommit(colaboradorVO);
        }

        if (colaboradorVO == null && importacao) {
            colaboradorVO = colaboradorDao.consultarPorNomeColaborador("PACTO", 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }

        return colaboradorVO;
    }

    private ProfissaoVO obterProfissao(final String profissao) {
        ProfissaoVO profissaoVO = new ProfissaoVO();
        try {
            if (UteisValidacao.emptyString("profissao")) {
                return profissaoVO;
            }
            try {
                profissaoVO.setCodigo(Integer.valueOf(profissao));
            } catch (NumberFormatException e) {
                Profissao profissaoDao = new Profissao(con);
                profissaoVO = profissaoDao.criarOuConsultarProfissaoPorDescricao(profissao, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao preencher Profissão: " + e.getMessage());
        }
        return profissaoVO;
    }

    public CategoriaVO obterCategoria(final String categoria) {
        CategoriaVO categoriaVO = new CategoriaVO();
        try {
            if (UteisValidacao.emptyString("categoria")) {
                return categoriaVO;
            }
            try {
                categoriaVO.setCodigo(Integer.valueOf(categoria));
            } catch (NumberFormatException e) {
                Categoria categoriaDao = new Categoria(con);
                categoriaVO = categoriaDao.criarOuConsultarCategoriaPorNome(categoria, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                categoriaDao = null;
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao preencher categoria: " + e.getMessage());
        }
        return categoriaVO;
    }

    private GrauInstrucaoVO obterGrauInstrucao(String escolaridade) {
        GrauInstrucaoVO grauInstrucaoVO = new GrauInstrucaoVO();
        try {
            GrauInstrucao grauInstrucaoDao = new GrauInstrucao(con);

            List<GrauInstrucaoVO> list = grauInstrucaoDao.consultarPorDescricao(escolaridade, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UteisValidacao.emptyList(list)) {
                grauInstrucaoVO.setDescricao(escolaridade);
                grauInstrucaoDao.incluir(grauInstrucaoVO);
            } else {
                grauInstrucaoVO = list.get(0);
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoImportacao.class.getName()).log(Level.SEVERE, null, ex);
        }
        return grauInstrucaoVO;
    }

    private void validarCPF(ClienteJSON clienteJSON) throws Exception {
        if (UteisValidacao.emptyNumber(clienteJSON.getIdExterno())) { //validação não realizada para importação
            ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            boolean validaCPF = configuracaoSistemaVO.isCpfValidar();
            List pessoas = new ArrayList();
            if (validaCPF) {
                if (!UteisValidacao.isValidCPF(clienteJSON.getCpf())) {
                    throw new Exception("CPF inválido.");
                }
            }
        }
    }

    public Map<Integer, PlanoModalidadeVezesSemanaVO> consultarModalidadesContrato(List<ModalidadeJSON> modalidades, Integer plano, EmpresaVO empresaVO) throws Exception {
        Map<Integer, PlanoModalidadeVezesSemanaVO> selecionadas = new HashMap<Integer, PlanoModalidadeVezesSemanaVO>();
        Modalidade modalidadeDAO = new Modalidade(con);
        for (ModalidadeJSON modalidadeJSON : modalidades) {
            //String nomeModalidade = dadosImportarTO.getNomeModalidade() + " " + dadosImportarTO.getNrVezesSemanaTurma();

            ModalidadeVO modalidadeVO = null;
            if (!UteisValidacao.emptyNumber(modalidadeJSON.getCodigo())) {
                modalidadeVO = modalidadeDAO.consultarPorChavePrimaria(modalidadeJSON.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                modalidadeVO = modalidadeDAO.consultarPorNomeModalidade(modalidadeJSON.getNome(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (!UtilReflection.objetoMaiorQueZero(modalidadeVO, "getCodigo()")) {
                modalidadeVO = new ModalidadeVO();
                modalidadeVO.setNome(modalidadeJSON.getNome());
                modalidadeVO.setUtilizarTurma(false);
                modalidadeVO.setValidarDados(false);
                modalidadeVO.setValorMensal(0.0);
                modalidadeVO.setModalidadeDefault(false);
                modalidadeVO.setUtilizarProduto(false);
                modalidadeVO.setUsaTreino(false);
                modalidadeVO.setModalidadeEmpresaVOs(new ArrayList());
                ModalidadeEmpresaVO modalidadeEmpresaVO = new ModalidadeEmpresaVO();
                modalidadeEmpresaVO.setEmpresa(empresaVO);
                modalidadeVO.setModalidadeEmpresaVOs(new ArrayList());
                modalidadeVO.getModalidadeEmpresaVOs().add(modalidadeEmpresaVO);
                modalidadeVO.setNrVezes(modalidadeJSON.getVezesPorSemana());
                modalidadeVO.setCrossfit(false);
                modalidadeVO.setAtivo(true);
                FacadeManager.getFacade().getModalidade().incluirSemCommit(modalidadeVO);
            } else {
                modalidadeVO.setNrVezes(modalidadeJSON.getVezesPorSemana());
            }
            selecionadas.put(modalidadeVO.getCodigo(), validarModalidadeNoPlano(modalidadeVO, modalidadeJSON.getVezesPorSemana(), plano));
        }
        if (selecionadas.isEmpty()) {
            ModalidadeVO modalidadeImportacao = modalidadeDAO.consultarPorNomeModalidade("IMPORTAÇÃO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            selecionadas.put(modalidadeImportacao.getCodigo(), validarModalidadeNoPlano(modalidadeImportacao, 7, plano));
        }
        return selecionadas;

    }

    private PlanoModalidadeVezesSemanaVO validarModalidadeNoPlano(ModalidadeVO modalidadeVO, final Integer vezesSemana, final Integer plano) throws Exception {
        Set<Integer> listaCodigo = new HashSet<Integer>();
        PlanoModalidade planoModalidadeDAO = new PlanoModalidade(con);
        PlanoModalidadeVezesSemana planoVezesSemanaDAO = new PlanoModalidadeVezesSemana(con);

        PlanoModalidadeVO planoModalidadeVO = planoModalidadeDAO.consultar(plano, modalidadeVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UtilReflection.objetoMaiorQueZero(planoModalidadeVO, "getCodigo()")) {
            planoModalidadeVO = new PlanoModalidadeVO();
            planoModalidadeVO.setValidarDados(false);
            planoModalidadeVO.setModalidade(new ModalidadeVO());
            planoModalidadeVO.getModalidade().setCodigo(modalidadeVO.getCodigo());
            planoModalidadeVO.setPlano(plano);
            planoModalidadeVO.setPlanoModalidadeVezesSemanaVOs(new ArrayList());
            planoModalidadeDAO.incluir(planoModalidadeVO);
        }
        listaCodigo.add(planoModalidadeVO.getCodigo());

        PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO = planoVezesSemanaDAO.consultar(planoModalidadeVO.getCodigo(), vezesSemana, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (planoModalidadeVezesSemanaVO == null) {
            planoModalidadeVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
            planoModalidadeVezesSemanaVO.setValidarDados(false);
            planoModalidadeVezesSemanaVO.setPlanoModalidade(planoModalidadeVO.getCodigo());
            planoModalidadeVezesSemanaVO.setNrVezes(vezesSemana);
            planoModalidadeVezesSemanaVO.setPercentualDesconto(0.0);
            planoModalidadeVezesSemanaVO.setValorEspecifico(0.0);
            planoModalidadeVezesSemanaVO.setTipoValor("");
            planoModalidadeVezesSemanaVO.setReferencia(false);
            planoVezesSemanaDAO.incluir(planoModalidadeVezesSemanaVO);
        }

        for (Integer codigo : listaCodigo) {
            PlanoModalidadeVO planoModalidadeDescVO = planoModalidadeDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            StringBuilder descVzSemana = new StringBuilder();
            for (Object obj : planoModalidadeDescVO.getPlanoModalidadeVezesSemanaVOs()) {
                PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaDescVO = (PlanoModalidadeVezesSemanaVO) obj;
                String vP;
                if (planoModalidadeVezesSemanaDescVO.getNrVezes() == 1) {
                    vP = " Vez";
                } else {
                    vP = " Vezes";
                }
                DecimalFormat decimalFormat = new DecimalFormat("R$ #,##0.00");
                if (descVzSemana.length() <= 0) {
                    descVzSemana.append(planoModalidadeVezesSemanaVO.getNrVezes()).append(vP).append(" - " + decimalFormat.format(0));
                } else {
                    descVzSemana.append(", ").append(planoModalidadeVezesSemanaVO.getNrVezes()).append(vP).append(" - " + decimalFormat.format(0));
                }
            }
            Statement st = planoModalidadeDAO.getCon().createStatement();
            st.execute("update planoModalidade set listaVezesSemana = '" + descVzSemana.toString() + "' where codigo =" + codigo);
        }
        return planoModalidadeVezesSemanaVO;
    }


    public void configurarDuracaoCondicaoPagamento(ContratoControle contratoControle, final Integer plano, final Integer duracao, Integer nrParcelas) throws Exception {
        PlanoDuracao planoDuracaoDAO = new PlanoDuracao(con);
        PlanoCondicaoPagamento planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);
        CondicaoPagamento condicaoDAO = new CondicaoPagamento(con);
        CondicaoPagamentoVO condicaoPagamentoVO = condicaoDAO.criarOuConsultarSeExistePorNome(nrParcelas);
        PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();

        // INFORMAR A DURAÇÃO DO CONTRATO
        PlanoDuracaoVO planoDuracaoVO = planoDuracaoDAO.consultarPorNumeroMesesPlano(duracao, plano, Uteis.NIVELMONTARDADOS_TODOS);
        if (planoDuracaoVO == null) {
            planoDuracaoVO = new PlanoDuracaoVO();
            planoDuracaoVO.setPlano(plano);
            planoDuracaoVO.setNumeroMeses(duracao);
            planoDuracaoVO.setNrMaximoParcelasCondPagamento(duracao);
            planoDuracaoVO.setTipoValor("VE");
            planoDuracaoVO.setValorDesejado(0.0);
            planoDuracaoDAO.incluir(planoDuracaoVO);
            contratoControle.getContratoVO().getPlano().getPlanoDuracaoVOs().add(planoDuracaoVO);

        }
        try {
            planoCondicaoPagamentoVO = planoCondicaoPagamentoDAO.consultarPorPlanoDuracaoCondicao(planoDuracaoVO.getCodigo(), condicaoPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception ignored) {
            planoCondicaoPagamentoVO.setPlanoDuracao(planoDuracaoVO.getCodigo());
            planoCondicaoPagamentoVO.setCondicaoPagamento(condicaoPagamentoVO);
            planoCondicaoPagamentoVO.setQtdParcela(nrParcelas);
            planoCondicaoPagamentoDAO.incluir(planoCondicaoPagamentoVO);
            planoDuracaoVO.getPlanoCondicaoPagamentoVOs().add(planoCondicaoPagamentoVO);
        }

        Iterator i = contratoControle.getContratoVO().getPlano().getPlanoDuracaoVOs().iterator();
        while (i.hasNext()) {
            PlanoDuracaoVO planoDuracao = (PlanoDuracaoVO) i.next();
            if (planoDuracao.getCodigo().equals(planoDuracaoVO.getCodigo())) {
                planoDuracao.setDuracaoEscolhida(true);
            }
        }
        contratoControle.selecionarDuracao(planoDuracaoVO);
        Iterator j = contratoControle.getContratoVO().getPlanoDuracao().getPlanoCondicaoPagamentoVOs().iterator();
        while (j.hasNext()) {
            PlanoCondicaoPagamentoVO planoCondicaoPagamento = (PlanoCondicaoPagamentoVO) j.next();
            if (planoCondicaoPagamento.getCondicaoPagamento().getCodigo().equals(planoCondicaoPagamentoVO.getCondicaoPagamento().getCodigo())) {
                planoCondicaoPagamento.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
            }
        }
        contratoControle.selecionarCondicaoPagamento(planoCondicaoPagamentoVO);
    }

    public void processarHorario(ContratoControle contratoControle, final Integer plano, final boolean contratoComTurma) throws Exception {
        PlanoHorario planohorarioDAO = new PlanoHorario(con);
        // INFORMAR O HORÁRIO
        List<PlanoHorarioVO> listaPlanoHorario = planohorarioDAO.consultarPlanoHorarios(plano, Uteis.NIVELMONTARDADOS_TODOS);
        PlanoHorarioVO planoHorarioSel = null;
        for (PlanoHorarioVO planoHorarioVO : listaPlanoHorario) {
            if (!contratoComTurma && planoHorarioVO.getHorario().getDescricao().contains("LIVRE")) {
                planoHorarioSel = planoHorarioVO;
                break;
            }
            if (contratoComTurma && planoHorarioVO.getHorario().getDescricao().contains("TURMA")) {
                planoHorarioSel = planoHorarioVO;
                break;
            }
        }
        contratoControle.getContratoVO().setPlanoHorario(planoHorarioSel);
    }

    public UsuarioVO obterUsuarioConsultor(String consultor, EmpresaVO empresaVO, boolean importacao) throws Exception {
        ColaboradorVO colaborador = obterConsultor(consultor, empresaVO, true);
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorColaboradorEmpresa(colaborador.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            PerfilAcesso perfilDAO = new PerfilAcesso(con);
            List<PerfilAcessoVO> perfis = perfilDAO.consultarPorNome("CONSULTOR",
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioPerfilAcessoVO uperf = new UsuarioPerfilAcessoVO();
            uperf.setPerfilAcesso(perfis.get(0));
            uperf.setEmpresa(empresaVO);
            usuarioVO.setNome(consultor.toUpperCase());
            // senha padrao que ser? inserida para todos os usuarios
            usuarioVO.setSenha("123456");
            usuarioVO.setUsername(Formatador.removerEspacoUser(consultor));
            // N?O ser?o inseridos como administradores
            usuarioVO.setAdministrador(false);
            usuarioVO.setColaboradorVO(colaborador);
            usuarioVO.setTipoUsuario("CE");
            usuarioVO.getUsuarioPerfilAcessoVOs().add(uperf);
            usuarioVO.setValidarDados(false);
            usuarioDAO.incluirSemCommit(usuarioVO);
        }

        return usuarioVO;
    }


    public JSONObject persistirPagamentosContratoJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.persistirPagamentosContratoJSON");
        try {

            con.setAutoCommit(false);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isEmImportacao()) {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
                return objRetorno;
            }

            JSONObject objJSON = new JSONObject(json);
            int idExternoContrato = objJSON.optInt("idExterno");
            ResultSet rsExistente = SuperFacadeJDBC.criarConsulta("select recibopagamento from movpagamento where id_recebe = " + idExternoContrato + " and empresa = " + empresa, con);
            if (rsExistente.next()) {
                objRetorno.put("status", "warning");
                objRetorno.put("codigoRegistroZW", rsExistente.getInt("recibopagamento"));
                objRetorno.put("mensagem", "Já foi importado pagamento para o contrato de idExterno: " + idExternoContrato);
                return objRetorno;
            }
            List<PagamentoJSON> listaPagamentoJSON = new ArrayList<PagamentoJSON>();
            JSONArray pagamentos = objJSON.optJSONArray("pagamentos");
            for (int i = 0; i < pagamentos.length(); i++) {
                listaPagamentoJSON.add(new PagamentoJSON(idExternoContrato, pagamentos.optJSONObject(i)));
            }

            validarDadosBasicosPagamentos(listaPagamentoJSON);

            Date dataLancamentoContrato = null;
            ContratoVO contratoVO = new ContratoVO();
            Integer pessoa = 0;
            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select codigo as contrato, datalancamento, pessoa from contrato where idexterno = " + idExternoContrato + " and empresa = " + empresa, con);
            if (rsContrato.next()) {
                contratoVO.setCodigo(rsContrato.getInt("contrato"));
                dataLancamentoContrato = rsContrato.getDate("dataLancamento");
                pessoa = rsContrato.getInt("pessoa");
            } else {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Não foi encontrado contrato com o idExterno informado: " + listaPagamentoJSON.get(0).getIdExternoContrato());
                return objRetorno;
            }
            Map<Date, List<PagamentoJSON>> mapRecibos = new HashMap<Date, List<PagamentoJSON>>();
            mapRecibos.put(Uteis.getDataComHoraZerada(dataLancamentoContrato), new ArrayList<PagamentoJSON>());
            for (PagamentoJSON pagJSON : listaPagamentoJSON) {
                if (pagJSON.getDataCadastro() == null) {
                    pagJSON.setDataCadastro(dataLancamentoContrato);
                }
                if (!mapRecibos.containsKey(Uteis.getDataComHoraZerada(pagJSON.getDataCadastro()))) {
                    mapRecibos.put(Uteis.getDataComHoraZerada(pagJSON.getDataCadastro()), new ArrayList<PagamentoJSON>());
                }
                mapRecibos.get(Uteis.getDataComHoraZerada(pagJSON.getDataCadastro())).add(pagJSON);
            }

            Pessoa pessoaDAO = new Pessoa();
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            List<MovPagamentoVO> pagamentosRecibo = null;
            UsuarioVO responsavel = new UsuarioVO();
            responsavel.setCodigo(1);
            ReciboPagamento reciboDAO = new ReciboPagamento(con);
            MovPagamento movpagamentoDAO = new MovPagamento(con);
            List<MovParcelaVO> parcelas = null;
            String recibos = "";
            SortedSet<Date> keys = new TreeSet<Date>(mapRecibos.keySet());
            for (Date key : keys) {
                List<PagamentoJSON> listaPagamentos = mapRecibos.get(key);
                if (listaPagamentos.isEmpty()) {
                    continue;
                }
                pagamentosRecibo = processarPagamentosRecibo(listaPagamentos, empresaVO, pessoaVO, responsavel, idExternoContrato);
                ReciboPagamentoVO reciboPagamento = new ReciboPagamentoVO();
                movpagamentoDAO.inicializarDadosReciboPagamento(pagamentosRecibo, reciboPagamento, contratoVO);
                reciboDAO.incluir(reciboPagamento);
                parcelas = processarParcelasAPagarPorRecibo(contratoVO.getCodigo(), reciboPagamento.getValorTotal());
                movpagamentoDAO.incluirListaPagamento(pagamentosRecibo, parcelas, null, contratoVO, false, 0.0, false, reciboPagamento);
                recibos += "," + reciboPagamento.getCodigo();
            }
            con.commit();
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", recibos.replaceFirst(",", ""));
            objRetorno.put("mensagem", "Contrato importado com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    public JSONObject persistirOperacoesContratoJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.persistirOperacoesContratoJSON");
        try {

            con.setAutoCommit(false);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            if (!empresaVO.isEmImportacao()) {
//                objRetorno.put("status", "error");
//                objRetorno.put("codigoRegistroZW", 0);
//                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
//                return objRetorno;
//            }
            JSONObject objJSON = new JSONObject(json);
            int idExternoContrato = objJSON.optInt("idExternoContrato");
            Boolean externo = objJSON.optBoolean("externo");

            ContratoVO contratoVO = new ContratoVO();
            ClienteVO clienteVO = new ClienteVO();

            ResultSet rsContrato;
            if (externo) {
                rsContrato = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato , datalancamento,vigenciade, vigenciaateajustada, c.situacao, cl.pessoa, cl.codigo as cliente from contrato c inner join cliente cl on c.pessoa = cl.pessoa where c.idexterno = " + idExternoContrato + " and c.empresa = " + empresa, con);
            } else {
                rsContrato = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato , datalancamento,vigenciade, vigenciaateajustada, c.situacao, cl.pessoa, cl.codigo as cliente from contrato c inner join cliente cl on c.pessoa = cl.pessoa where c.id_recebe = " + idExternoContrato + " and c.empresa = " + empresa, con);
            }
            if (rsContrato.next()) {
                contratoVO.setCodigo(rsContrato.getInt("contrato"));
                contratoVO.setVigenciaDe(rsContrato.getDate("vigenciade"));
                contratoVO.setVigenciaAteAjustada(rsContrato.getDate("vigenciaateajustada"));
                contratoVO.setSituacao(rsContrato.getString("situacao"));
                PessoaVO pessoaVO = new PessoaVO();
                pessoaVO.setCodigo(rsContrato.getInt("pessoa"));
                contratoVO.setPessoa(pessoaVO);
                clienteVO.setCodigo(rsContrato.getInt("cliente"));
            } else {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Não foi encontrado contrato com o idExterno informado: " + idExternoContrato);
                return objRetorno;
            }

            if (contratoVO.getSituacao().equalsIgnoreCase("IN")) {
                throw new Exception("O contrato idExterno: " + idExternoContrato + " está inativo!");
            }

            if (contratoVO.getSituacao().equalsIgnoreCase("CA")) {
                throw new Exception("O contrato idExterno: " + idExternoContrato + " está cancelado!");
            }

            ResultSet rsExistente = SuperFacadeJDBC.criarConsulta("select codigo from contratooperacao where contrato = " + contratoVO.getCodigo(), con);
            if (rsExistente.next()) {
                objRetorno.put("status", "warning");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Já foi importado operação de contrato para o contrato de idExterno: " + idExternoContrato);
                return objRetorno;
            }

            List<ContratoOperacaoJSON> listaOperacoesJSON = new ArrayList<ContratoOperacaoJSON>();
            JSONArray operacoes = objJSON.getJSONArray("operacoes");
            for (int i = 0; i < operacoes.length(); i++) {
                listaOperacoesJSON.add(new ContratoOperacaoJSON(idExternoContrato, operacoes.optJSONObject(i)));
            }

            validarDadosBasicosOperacao(listaOperacoesJSON);
            Ordenacao.ordenarLista(listaOperacoesJSON, "dataInicioDate");
            UsuarioVO responsavel = new UsuarioVO();
            responsavel.setCodigo(1);
            ContratoOperacaoVO contratoOperacaoVO = new ContratoOperacaoVO();
            contratoOperacaoVO.setContrato(contratoVO.getCodigo());
            contratoOperacaoVO.setTipoJustificativa(obterJustificativaOperacao(empresaVO));
            contratoOperacaoVO.setResponsavel(responsavel);
            contratoOperacaoVO.setOperacaoPaga(false);
            contratoOperacaoVO.setDescricaoCalculo("IMPORTAÇÃO");
            contratoOperacaoVO.setObservacao("IMPORTAÇÃO");
            ContratoOperacao contratoOperacaoDAO = new ContratoOperacao(con);
            String codigoOperacoes = "";
            for (ContratoOperacaoJSON opeJSON : listaOperacoesJSON) {
                contratoOperacaoVO.setDataOperacao(Uteis.getDate(opeJSON.getDataCadastro()));
                contratoOperacaoVO.setTipoOperacao(opeJSON.getTipoOperacao());

                if (opeJSON.getTipoOperacao().equals(TipoOperacaoContratoEnum.CANCELAMENTO.getSigla())) {
                    CancelamentoContratoVO cancelamentoContratoVO = new CancelamentoContratoVO();
                    cancelamentoContratoVO.setContratoOperacaoVO(contratoOperacaoVO);
                    cancelamentoContratoVO.setTipoJustificativaOperacao(contratoOperacaoVO.getTipoJustificativa().getCodigo());

                    if (contratoVO != null && contratoVO.getContratoRecorrenciaVO().isCancelamentoProporcional()) {
                        getFacade().getZWFacade().calcularCancelamentoProporcional(cancelamentoContratoVO, contratoVO, Uteis.getDate(opeJSON.getDataCadastro()), false);
                    } else if (contratoVO != null && contratoVO.isCancelamentoAvaliandoParcelas()) {
                        getFacade().getZWFacade().calcularCancelamentoAvaliandoParcelas(cancelamentoContratoVO, contratoVO, Uteis.getDate(opeJSON.getDataCadastro()), false);
                    }

                    UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
                    cancelamentoContratoVO.setResponsavelCancelamento(usuarioVO);
                    cancelamentoContratoVO.setContratoCancelar(contratoVO);
                    cancelamentoContratoVO.setDataCancelamento(Uteis.getDate(opeJSON.getDataCadastro()));

                    getFacade().getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                            contratoVO, cancelamentoContratoVO.getResponsavelCancelamento(),
                            null, new ArrayList<>(), null, true);
                    con.setAutoCommit(false);
                    ResultSet rsContratoOperacao = SuperFacadeJDBC.criarConsulta("select cp.codigo from contratooperacao cp where cp.contrato = " + contratoVO.getCodigo() + " and cp.tipooperacao = '" + TipoOperacaoContratoEnum.CANCELAMENTO.getSigla() + "' ORDER BY codigo DESC limit 1", con);
                    if (rsContratoOperacao.next()) {
                        codigoOperacoes += "," + rsContratoOperacao.getInt("codigo");
                    }
                } else {
                    contratoOperacaoVO.setDataInicioEfetivacaoOperacao(opeJSON.getDataInicioDate());
                    contratoOperacaoVO.setDataFimEfetivacaoOperacao(Uteis.getDate(opeJSON.getDataFinal()));
                    contratoOperacaoDAO.incluirSemCommit(contratoOperacaoVO, false);
                    codigoOperacoes += "," + contratoOperacaoVO.getCodigo();
                }

                if (Calendario.maior(contratoOperacaoVO.getDataFimEfetivacaoOperacao(), Calendario.hoje())) {
                    ajustarHistoricoContrato(contratoOperacaoVO, contratoVO);
                    ajustarPeriodoAcessoContrato(contratoOperacaoVO, contratoVO);
                    try {
                        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                        zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);
                        zwFacade = null;
                    } catch (SinteticoException se) {
                        Uteis.logar(null, "Problema ao processar sintético do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresaVO.getNome());
                    }

                }
            }

            con.commit();
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", codigoOperacoes.replaceFirst(",", ""));
            objRetorno.put("mensagem", "Operações de Contrato importadas com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    public void cancelarContratoSesiCe(Integer codigoContrato, Date dataCancelamento, boolean controleTransacao) throws Exception {
        ZillyonWebFacade zwFacadeDAO = new ZillyonWebFacade(con);
        Usuario usuarioDAO = new Usuario(con);
        Contrato contratoDAO = new Contrato(con);
        Empresa empresaDAO = new Empresa(con);
        ConfiguracaoSistema configuracaoSistemaDao = new ConfiguracaoSistema(con);

        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDao.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!configuracaoSistemaVO.getSesiCe()) {
            throw new Exception("Integração não está habilitada.");
        }

        ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (contratoVO == null) {
            throw new Exception("Contrato não encontrado.");
        }

        if (!contratoVO.getSituacao().equalsIgnoreCase("AT")) {
            throw new Exception("O contrato não está ativo.");
        }

        if (contratoVO.getValorFinal() > 0) {
            throw new Exception("Contrato não cancelado, pois possui valor final.");
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from contratooperacao \n" +
                "where contrato = " + contratoVO.getCodigo() + " \n" +
                "and tipooperacao = 'CA'", con);
        if (rs.next()) {
            throw new Exception("Contrato já cancelado.");
        }

        if (Calendario.maior(dataCancelamento, Calendario.hoje())) {
            throw new Exception("Data de cancelamento não pode ser maior que a data atual.");
        }

        if (Calendario.menor(dataCancelamento, contratoVO.getVigenciaDe())) {
            throw new Exception("Data de cancelamento não pode ser menor que a data de vigência.");
        }
        if (Calendario.maior(dataCancelamento, contratoVO.getVigenciaAteAjustada())) {
            throw new Exception("Data de cancelamento não pode ser maior que a data de vigência.");
        }
        if (Calendario.igual(dataCancelamento, contratoVO.getVigenciaDe())) {
            dataCancelamento = Calendario.somarDias(dataCancelamento, 1);
            Calendario.setDateThread(dataCancelamento);
        }

        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);

        ContratoOperacaoVO contratoOperacaoVO = new ContratoOperacaoVO();
        contratoOperacaoVO.setContrato(contratoVO.getCodigo());
        contratoOperacaoVO.setTipoJustificativa(obterJustificativaOperacao(empresaVO));
        contratoOperacaoVO.setResponsavel(usuarioVO);
        contratoOperacaoVO.setOperacaoPaga(false);
        contratoOperacaoVO.setDescricaoCalculo("Opera\u00E7\u00E3o importada");
        contratoOperacaoVO.setObservacao("Opera\u00E7\u00E3o importada");
        contratoOperacaoVO.setDataOperacao(dataCancelamento);
        contratoOperacaoVO.setTipoOperacao(TipoOperacaoContratoEnum.CANCELAMENTO.getSigla());

        CancelamentoContratoVO cancelamentoContratoVO = new CancelamentoContratoVO();
        cancelamentoContratoVO.setContratoOperacaoVO(contratoOperacaoVO);
        cancelamentoContratoVO.setTipoJustificativaOperacao(contratoOperacaoVO.getTipoJustificativa().getCodigo());

        if (contratoVO != null && contratoVO.getContratoRecorrenciaVO().isCancelamentoProporcional()) {
            zwFacadeDAO.calcularCancelamentoProporcional(cancelamentoContratoVO, contratoVO, dataCancelamento, false);
        } else if (contratoVO != null && contratoVO.isCancelamentoAvaliandoParcelas()) {
            zwFacadeDAO.calcularCancelamentoAvaliandoParcelas(cancelamentoContratoVO, contratoVO, dataCancelamento, false);
        }

        cancelamentoContratoVO.setResponsavelCancelamento(usuarioVO);
        cancelamentoContratoVO.setContratoCancelar(contratoVO);
        cancelamentoContratoVO.setDataCancelamento(dataCancelamento);

        zwFacadeDAO.incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                contratoVO, cancelamentoContratoVO.getResponsavelCancelamento(),
                null, new ArrayList<>(), null, controleTransacao);

        usuarioDAO = null;
        zwFacadeDAO = null;
        contratoDAO = null;
        empresaDAO = null;
        configuracaoSistemaDao = null;
    }

    public JSONObject persistirCancelarContratoJSON(final String json, final Integer empresa, String key) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json, "IntegracaoImportacao.persistirCancelarContratoJSON");
        try {
            JSONObject objJSON = new JSONObject(json);
            int idExternoContrato = objJSON.optInt("idExternoContrato");
            int codigoContrato = objJSON.optInt("codigoContrato");
            String observacao = objJSON.optString("observacao");

            // Validacao dos campos
            if (codigoContrato <= 0 && idExternoContrato <= 0) {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "É necessário informar pelo menos o código do contrato ou o ID externo.");
                return objRetorno;
            }

            Date dataCancelamento;
            try {
                dataCancelamento = Uteis.getDate(objJSON.optString("dataCancelamento"), "dd/MM/yyyy");
            } catch (Exception e) {
                dataCancelamento = Calendario.hoje();
            }

            ContratoVO contratoVO;
            ClienteVO clienteVO = new ClienteVO();

            String sql = "SELECT c.codigo AS contrato, cl.codigo AS cliente " +
                    "FROM contrato c " +
                    "INNER JOIN cliente cl ON c.pessoa = cl.pessoa " +
                    "WHERE " +
                    (codigoContrato > 0 ? "c.codigo = ?" : "COALESCE(c.idexterno, c.id_externo) = ?") +
                    " AND c.empresa = ?";

            try (PreparedStatement ps = con.prepareStatement(sql)) {
                if (codigoContrato > 0) {
                    ps.setInt(1, codigoContrato);
                } else {
                    ps.setInt(1, idExternoContrato);
                }
                ps.setInt(2, empresa);

                try (ResultSet rsContrato = ps.executeQuery()) {
                    if (rsContrato.next()) {
                        contratoVO = getFacade().getContrato().consultarPorChavePrimaria(rsContrato.getInt("contrato"), Uteis.NIVELMONTARDADOS_TODOS);
                        clienteVO.setCodigo(rsContrato.getInt("cliente"));
                    } else {
                        objRetorno.put("status", "erro");
                        objRetorno.put("codigoRegistroZW", 0);
                        String msgErro = codigoContrato > 0 ? "codigoContrato informado: " + codigoContrato : "idExterno informado: " + idExternoContrato;
                        objRetorno.put("mensagem", "Não foi encontrado contrato com o " + msgErro);
                        return objRetorno;
                    }
                }
            }

            // Validar se contrato ja foi cancelado anteriormente
            if (getFacade().getContrato().consultarContratoCancelado(contratoVO.getCodigo())) {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Contrato está cancelado");
                return objRetorno;
            }

            boolean contratoEstavaTrancado = false;
            //se trancado, destrancar o contrato primeiro
            if (contratoVO.getSituacao().equals("TR")) {
                contratoEstavaTrancado = true;
                RetornoTrancamentoContratoControle retornoTrancamentoContratoControle = new RetornoTrancamentoContratoControle();
                retornoTrancamentoContratoControle.setTrancamentoContratoVO(new TrancamentoContratoVO());
                retornoTrancamentoContratoControle.getTrancamentoContratoVO().setContratoVO(contratoVO);
                retornoTrancamentoContratoControle.getTrancamentoContratoVO().setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(1));
                retornoTrancamentoContratoControle.setListaContratoVOs(new ArrayList<ContratoVO>());
                retornoTrancamentoContratoControle.getListaContratoVOs().add(contratoVO);
                retornoTrancamentoContratoControle.obterUltimoTracamentoContrato();
                retornoTrancamentoContratoControle.tratarHorariosTurmasOcupacao();
                if (retornoTrancamentoContratoControle.getNecessitaManutencao()) {
                    objRetorno.put("status", "erro");
                    objRetorno.put("codigoRegistroZW", 0);
                    objRetorno.put("mensagem", "Não foi possível destrancar o contrato, é necessário validar disponibilidade da turma");
                    return objRetorno;
                }
                if (retornoTrancamentoContratoControle.getTrancamentoContratoVO().getApresentarPanelClienteRetornoForaPrazo()) {
                    objRetorno.put("status", "erro");
                    objRetorno.put("codigoRegistroZW", 0);
                    objRetorno.put("mensagem", "Aluno trancado vencido, é necessário comparecer à academia");
                    return objRetorno;
                }
                try {
                    retornoTrancamentoContratoControle.getTrancamentoContratoVO().setResponsavelOperacao(DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().getUsuarioRecorrencia());
                    retornoTrancamentoContratoControle.gravarRetornoSemInteracaoComUsuario();
                    if(UteisValidacao.isMesmoDia(dataCancelamento, Calendario.hoje())) {
                        dataCancelamento = Calendario.amanha();
                    }

                } catch (Exception e) {
                    objRetorno.put("status", "erro");
                    objRetorno.put("codigoRegistroZW", 0);
                    objRetorno.put("mensagem", "Erro na tentativa de destrancar o contrato antes do cancelamento: " + e.toString());
                    return objRetorno;

                }

            }

            if (contratoVO.getSituacao().equals("IN")
                    || (!contratoVO.getSituacao().equals("TR")
                        && Calendario.menor(contratoVO.getVigenciaAteAjustada(), Calendario.hoje()))) {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "O contrato não pode ser cancelado, pois não está mais vigente!");
                return objRetorno;
            }

            CancelamentoContratoVO cancelamentoContratoVO = new CancelamentoContratoVO();

            // Justificativa de operacao para ID externo
            if (idExternoContrato > 0) {
                String justificativaSql = "SELECT codigo FROM JustificativaOperacao WHERE descricao ILIKE '%CANCELAMENTO%' AND (descricao ILIKE '%IMPORTACAO%' OR descricao ILIKE '%IMPORTAÇÃO%') AND empresa = ?";

                try (PreparedStatement psJustificativa = con.prepareStatement(justificativaSql)) {
                    psJustificativa.setInt(1, empresa);

                    try (ResultSet rsJustificativa = psJustificativa.executeQuery()) {
                        if (rsJustificativa.next()) {
                            cancelamentoContratoVO.setTipoJustificativaOperacao(rsJustificativa.getInt("codigo"));
                        } else {
                            objRetorno.put("status", "erro");
                            objRetorno.put("codigoRegistroZW", 0);
                            objRetorno.put("mensagem", "Não foi encontrada a justificativa de operação para o ID externo: " + idExternoContrato);
                            return objRetorno;
                        }
                    }
                }
            }

            CancelamentoContratoControle cancelamentoContratoControle = new CancelamentoContratoControle();
            cancelamentoContratoControle.setContratoVO(contratoVO);
            cancelamentoContratoControle.setCancelamentoContratoVO(cancelamentoContratoVO);

            if (contratoVO != null && contratoVO.getContratoRecorrenciaVO().isCancelamentoProporcional()) {
                getFacade().getZWFacade().calcularCancelamentoProporcional(cancelamentoContratoVO, contratoVO, dataCancelamento, false);
            } else if (contratoVO != null && contratoVO.isCancelamentoAvaliandoParcelas()) {
                getFacade().getZWFacade().calcularCancelamentoAvaliandoParcelas(cancelamentoContratoVO, contratoVO, dataCancelamento, false);
            }

            UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            cancelamentoContratoVO.setResponsavelCancelamento(usuarioVO);
            cancelamentoContratoVO.setContratoCancelar(contratoVO);
            cancelamentoContratoVO.setDataCancelamento(dataCancelamento);

            if (!UteisValidacao.emptyString(observacao)) {
                cancelamentoContratoVO.setObservacao(observacao);
            }

            getFacade().getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                    contratoVO, cancelamentoContratoVO.getResponsavelCancelamento(),
                    null, new ArrayList<ContaVO>(), null, true);

            String operacaoSql = "SELECT codigo FROM contratooperacao WHERE tipooperacao = 'CA' AND contrato = ? ORDER BY codigo DESC";
            int codigoOperacao = 0;

            try (PreparedStatement psOperacao = con.prepareStatement(operacaoSql)) {
                psOperacao.setInt(1, contratoVO.getCodigo());

                try (ResultSet rsOperacao = psOperacao.executeQuery()) {
                    if (rsOperacao.next()) {
                        codigoOperacao = rsOperacao.getInt("codigo");
                    }
                }
            }

            //cancelando parcelas em aberto

            List<MovParcelaVO> parcelasCancelar = getFacade().getMovParcela().consultarParcelasFuturasEmAberto(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            MovParcelaControle movParcelaControle = new MovParcelaControle(true);
            movParcelaControle.setListaParcelasPagar(parcelasCancelar);
            movParcelaControle.setJustificativaCancelamento("CANCELADO PELA API - ENDPOINT DE CANCELAMENTO DE CONTRATO");
            movParcelaControle.cancelarParcelas(usuarioVO);

            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", codigoOperacao);
            String msgSucesso = "Cancelamento do contrato realizado com sucesso";
            if(contratoEstavaTrancado) {
                SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                String dataCancelamentoFormatada = formatter.format(dataCancelamento);
                msgSucesso = "Operação de cancelamento agendada para " + dataCancelamentoFormatada +  ". O contrato estava trancado e foi destrancado com sucesso, mas há um limite de uma operação por dia.";
            }
            objRetorno.put("mensagem", msgSucesso);
            return objRetorno;
        } catch (Exception e) {
            throw e;
        } finally {
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    public JSONObject persistirTrancarContratoJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json, "IntegracaoImportacao.persistirCancelarContratoJSON");
        try {
            JSONObject objJSON = new JSONObject(json);

            // CAMPOS OBRIGATÓRIOS
            String codigoContrato = objJSON.optString("codigoContrato");
            String codigoProdutoTrancamento = objJSON.optString("codigoProdutoTrancamento");
            String observacao = objJSON.optString("observacao");
            Integer numeroDias = objJSON.optInt("numeroDias");
            Boolean alterarVencimentoParcelas = objJSON.optBoolean("alterarVencimentoParcelas", false);
            String dataTrancamentoRetroativo = objJSON.optString("dataTrancamentoRetroativo", "");


            // CAMPOS PADRÃO
            String descricaoProdTrancamento = "PRODUTO TRANCAMENTO API";

            // VALIDANDO OS CAMPOS OBRIGATÓRIOS
            if (UteisValidacao.emptyString(codigoContrato)) {
                objRetorno.put("mensagem", "O código do contrato está vazio.");
                objRetorno.put("status", "erro");
                return objRetorno;
            }

            if (!codigoContrato.matches("\\d+")) {
                objRetorno.put("mensagem", "O código do contrato é inválido, informe apenas números.");
                objRetorno.put("status", "erro");
                return objRetorno;
            }

            if (numeroDias < 1 || numeroDias > 730) {
                objRetorno.put("mensagem", "O número de dias deve ser um valor numérico entre 1 e 730.");
                objRetorno.put("status", "erro");
                return objRetorno;
            }

            ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(
                    Integer.valueOf(codigoContrato), Uteis.NIVELMONTARDADOS_TODOS);

            if (contratoVO != null) {
                // Valida se contrato já foi cancelado
                if (getFacade().getContrato().consultarContratoCancelado(contratoVO.getCodigo())) {
                    objRetorno.put("status", "erro");
                    objRetorno.put("codigoRegistroZW", 0);
                    objRetorno.put("mensagem", "Contrato já cancelado");
                    return objRetorno;
                }

                // Tranca contrato
                try {
                    ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(contratoVO);
                    if (getFacade().getContrato().existeRenovacaoContrato(contratoVO.getCodigo())) {
                        throw new ConsistirException("Trancamento não pode ser feito, pois esse contrato já foi renovado");
                    }

                    TrancamentoContratoVO trancamentoContratoVO = new TrancamentoContratoVO();

                    trancamentoContratoVO.setAlterarVencimentoparcelas(alterarVencimentoParcelas);

                    boolean trancamentoRetroativo = !UteisValidacao.emptyString(dataTrancamentoRetroativo);

                    if (trancamentoRetroativo) {
                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                            sdf.setLenient(false);
                            Date dataTrancamento = sdf.parse(dataTrancamentoRetroativo);
                            if(!UteisValidacao.dataMenorDataAtualSemHora(dataTrancamento)) {
                                objRetorno.put("mensagem", "A data de trancamento retroativo precisa ser uma data anterior à atual.");
                                objRetorno.put("status", "erro");
                                return objRetorno;
                            }
                            trancamentoContratoVO.setDataTrancamento(dataTrancamento);
                        } catch (ParseException e) {
                            objRetorno.put("mensagem", "A data de trancamento retroativo precisa estar no formato dd/MM/yyyy.");
                            objRetorno.put("status", "erro");
                            return objRetorno;
                        }
                    } else {
                        trancamentoContratoVO.setDataTrancamento(Calendario.hoje());
                    }

                    trancamentoContratoVO.setContratoVO(contratoVO);

                    if (!UteisValidacao.emptyString(observacao)) {
                        trancamentoContratoVO.setObservacao(observacao);
                    }

                    ProdutoVO produtoTrancamento = obterProdutoTrancamento(codigoProdutoTrancamento, descricaoProdTrancamento, observacao, numeroDias, objRetorno);
                    if (produtoTrancamento == null) {
                        return objRetorno;
                    }
                    produtoTrancamento.setNrDiasVigencia(numeroDias);
                    trancamentoContratoVO.setProdutoTrancamento(produtoTrancamento);

                    UsuarioVO responsavel = new UsuarioVO();
                    responsavel.setCodigo(1);
                    trancamentoContratoVO.setResponsavelOperacao(responsavel);

                    String descricaoJustificativa = "TRANCAMENTO SOLICITADO ONLINE";
                    JustificativaOperacaoVO justificativaOperacaoVO = obterJustificativaOperacaoTrancamento(descricaoJustificativa, empresa);
                    trancamentoContratoVO.setTipoJustificativa(justificativaOperacaoVO.getCodigo());

                    // Cálculo do trancamento
                    boolean sucesso = calcularTrancamento(trancamentoContratoVO, numeroDias, objRetorno);
                    if (!sucesso) {
                        return objRetorno;
                    }

                    getFacade().getTrancamentoContrato().incluir(trancamentoContratoVO, responsavel);

                    objRetorno.put("status", "success");
                    objRetorno.put("mensagem", "Trancamento do contrato " + contratoVO.getCodigo() + " realizado com sucesso");
                    return objRetorno;

                } catch (Exception e) {
                    objRetorno.put("status", "erro");
                    objRetorno.put("codigoRegistroZW", 0);
                    objRetorno.put("mensagem", "Erro ao trancar contrato: " + e.getMessage());
                    return objRetorno;
                }
            } else {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Contrato não encontrado.");
                return objRetorno;
            }
        } catch (Exception e) {
            throw e;
        } finally {
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    private ProdutoVO obterProdutoTrancamento(String codigoProdutoTrancamento, String descricaoProdTrancamento,
                                              String observacao, Integer numeroDias, JSONObject objRetorno) {
        ProdutoVO produtoTrancamento = null;

        try {
            if (!UteisValidacao.emptyString(codigoProdutoTrancamento) && !codigoProdutoTrancamento.matches("\\d+")) {
                objRetorno.put("mensagem", "O código do produto trancamento é inválido, informe apenas números.");
                objRetorno.put("status", "erro");
                return null;
            } else if (!UteisValidacao.emptyString(codigoProdutoTrancamento)) {
                produtoTrancamento = getFacade().getProduto().consultarPorChavePrimaria(
                        Integer.valueOf(codigoProdutoTrancamento), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!"TR".equals(produtoTrancamento.getTipoProduto())) {
                    objRetorno.put("mensagem", String.format(
                            "O produto trancamento (cod: %s) precisa ser do tipo TR (trancamento). O tipo é: %s (%s).",
                            codigoProdutoTrancamento, produtoTrancamento.getTipoProduto(),
                            produtoTrancamento.getTipoProduto_Apresentar()));
                    objRetorno.put("status", "erro");
                    return null;
                }
            } else {
                produtoTrancamento = getFacade().getProduto().consultarPorDescricao(
                        descricaoProdTrancamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (produtoTrancamento == null) {
                    ProdutoVO novoProdutoTrancamentoApi = new ProdutoVO();
                    CategoriaProdutoVO catProdutoServico = getFacade().getCategoriaProduto().obterCategoriaPadraoServico();
                    novoProdutoTrancamentoApi.setCategoriaProduto(catProdutoServico);
                    novoProdutoTrancamentoApi.setTipoProduto("TR");
                    novoProdutoTrancamentoApi.setObservacao(observacao);
                    novoProdutoTrancamentoApi.setNrDiasVigencia(numeroDias);
                    novoProdutoTrancamentoApi.setDescricao(descricaoProdTrancamento);
                    getFacade().getProduto().incluir(novoProdutoTrancamentoApi, true);
                    produtoTrancamento = novoProdutoTrancamentoApi;
                }
            }
        } catch (Exception e) {
            objRetorno.put("mensagem", "Erro ao obter ou criar o produto de trancamento: " + e.getMessage());
            objRetorno.put("status", "erro");
            return null;
        }

        return produtoTrancamento;
    }

    private JustificativaOperacaoVO obterJustificativaOperacaoTrancamento(String descricaoJustificativa, Integer empresa) throws Exception {
        JustificativaOperacaoVO justificativaOperacaoVO;
        List<JustificativaOperacaoVO> listConsultaJustificativa = getFacade().getJustificativaOperacao()
                .consultarPorDescricao(descricaoJustificativa, empresa, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (listConsultaJustificativa.isEmpty()) {
            justificativaOperacaoVO = new JustificativaOperacaoVO();
            justificativaOperacaoVO.setTipoOperacao("TR");
            justificativaOperacaoVO.setDescricao(descricaoJustificativa);
            justificativaOperacaoVO.setAtiva(true);
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(
                    empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            justificativaOperacaoVO.setEmpresa(empresaVO);
            getFacade().getJustificativaOperacao().incluir(justificativaOperacaoVO);
        } else {
            justificativaOperacaoVO = listConsultaJustificativa.get(0);
        }

        return justificativaOperacaoVO;
    }

    private boolean calcularTrancamento(TrancamentoContratoVO trancamentoContratoVO, Integer numeroDias, JSONObject objRetorno) {
        try {
            trancamentoContratoVO.setNrDiasContrato(
                    getFacade().getZWFacade().obterNrDiasContrato(trancamentoContratoVO.getContratoVO()));

            if (!trancamentoContratoVO.getContratoVO().isVendaCreditoTreino()) {
                trancamentoContratoVO.setNrDiasBonus(getFacade().getZWFacade()
                        .obterNrDiasOperacoesBonusNoContratoParaDias(trancamentoContratoVO.getContratoVO(),
                                trancamentoContratoVO.getDataTrancamento()));
            }

            // Inicializa com o número de dias informado pelo cliente
            trancamentoContratoVO.setNrDiasCongelado(numeroDias);

            // Dias utilizados pelo cliente
            Integer nrDiasUtilizados = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(
                    trancamentoContratoVO.getContratoVO(),
                    Uteis.obterDataAnterior(trancamentoContratoVO.getDataTrancamento(), 1));
            trancamentoContratoVO.setNrDiasUtilizadosPeloClienteContrato(nrDiasUtilizados);

            // Ajusta os dias restantes considerando bônus
            Integer diasRestantes = trancamentoContratoVO.getContratoVO()
                    .obterNrDiasRestantesProFinalDoContrato(trancamentoContratoVO.getNrDiasContrato(),
                            nrDiasUtilizados);
            trancamentoContratoVO.setNrDiasCongelado(diasRestantes + trancamentoContratoVO.getNrDiasBonus());

            // Valida se o trancamento é viável
            if (trancamentoContratoVO.getNrDiasCongelado() <= 0) {
                objRetorno.put("status", "erro");
                objRetorno.put("mensagem", "Não é possível realizar esta operação de trancamento, pois o contrato já expirou.");
                return false;
            }

            // Calcula a data de fim do trancamento como hoje + dias congelados
            trancamentoContratoVO.setDataFimTrancamento(Uteis.obterDataFutura2(trancamentoContratoVO.getDataTrancamento(), (numeroDias - 1)));
            trancamentoContratoVO.setDataRetorno(Uteis.obterDataFutura2(trancamentoContratoVO.getDataTrancamento(), (numeroDias)));
            return true;

        } catch (Exception e) {
            objRetorno.put("status", "erro");
            objRetorno.put("mensagem", "Erro no cálculo do trancamento: " + e.getMessage());
            return false;
        }
    }

    public void validarSituacaoContrato(ContratoVO contratoVO, Contrato contratoDAO) throws Exception {
        if (Calendario.maiorOuIgual(contratoVO.getVigenciaAteAjustada(), Calendario.hoje())) {
            contratoVO.setSituacao("AT");
        } else {
            contratoVO.setSituacao("IN");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo as contratobase, vigenciaateajustada,datamatricula," +
                " (exists (SELECT codigo FROM HistoricoContrato hc WHERE hc.contrato = c.codigo and descricao = 'DESISTENTE')) as isDesistente," +
                " (exists (SELECT codigo FROM HistoricoContrato hc WHERE hc.contrato = c.codigo and descricao = 'CANCELADO')) as isCancelado," +
                " (exists (SELECT codigo FROM HistoricoContrato hc WHERE hc.contrato = c.codigo and descricao = 'VENCIDO')) as isVencido" +
                " from contrato c where c.pessoa =  " + contratoVO.getPessoa().getCodigo()
                + " and c.contratoresponsavelrenovacaomatricula  = 0 and c.contratoresponsavelrematriculamatricula  = 0 and c.vigenciaateajustada <   '" + Uteis.getDataJDBC(contratoVO.getVigenciaDe())
                + "'order by c.vigenciaateajustada  desc limit 1 ", con);

        if (rs.next()) {
            if (Calendario.igual(rs.getDate("vigenciaateajustada"), Uteis.somarDias(contratoVO.getVigenciaDe(), -1)) && !rs.getBoolean("isCancelado") &&
                    !rs.getBoolean("isDesistente") && rs.getBoolean("isVencido")) {
                contratoVO.setContratoBaseadoRenovacao(rs.getInt("contratobase"));
                contratoVO.setContratoOrigemRenovacao(contratoDAO.consultarPorChavePrimaria(rs.getInt("contratobase"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                contratoVO.setSituacaoContrato("RN");
                SuperFacadeJDBC.executarConsultaUpdate("delete from historicocontrato where contrato =  " + rs.getInt("contratobase") + " and tipohistorico in ('VE', 'DE')", con);
            } else {
                contratoVO.setContratoBaseadoRematricula(rs.getInt("contratobase"));
                contratoVO.setSituacaoContrato("RE");
            }
            contratoVO.setDataMatricula(rs.getDate("datamatricula"));
        } else {
            contratoVO.setSituacaoContrato("MA");
        }
    }

    public void processarModalidadesSelecionadas(ContratoControle contratoControle, Map<Integer, PlanoModalidadeVezesSemanaVO> modalidades) {
        boolean modalidadeSelecionada = false;
        for (ContratoModalidadeVO contratoModalidade : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
            modalidadeSelecionada = false;
            for (Map.Entry<Integer, PlanoModalidadeVezesSemanaVO> entrada : modalidades.entrySet()) {
                if (contratoModalidade.getModalidade().getCodigo().equals(entrada.getKey())) {
                    contratoModalidade.getModalidade().setNrVezes(entrada.getValue().getNrVezes());
                    contratoModalidade.setPlanoVezesSemanaVO(entrada.getValue());
                    modalidadeSelecionada = true;
                    break;
                }
            }
            contratoModalidade.getModalidade().setModalidadeEscolhida(modalidadeSelecionada);
            contratoControle.selecionarModalidade(contratoModalidade);
        }
    }

    @Override
    public void incluir(String idEntidade) throws Exception {
        super.incluir(idEntidade);
    }

    public void validarDadosBasicosContrato(ContratoJSON contratoJSON) throws Exception {
        if (UteisValidacao.emptyNumber(contratoJSON.getIdClienteExterno()) && UteisValidacao.emptyNumber(contratoJSON.getCodigoMatricula())) {
            throw new Exception("id externo ou codigo matricula do cliente não informado");
        }
        if (UteisValidacao.emptyNumber(contratoJSON.getIdExterno())) {
            throw new Exception("id externo não informado");
        }
        try {
            Uteis.getDate(contratoJSON.getDataInicio());
        } catch (Exception e) {
            throw new Exception("Data de inicio do contrato é inválida, devendo ser no formato dd/MM/yyyy");
        }
        try {
            Uteis.getDate(contratoJSON.getDataFinal());
        } catch (Exception e) {
            throw new Exception("Data de final do contrato é inválida, devendo ser no formato dd/MM/yyyy");
        }
        try {
            Uteis.getDate(contratoJSON.getDataCadastro());
        } catch (Exception e) {
            throw new Exception("Data de cadastro do contrato é inválida, devendo ser no formato dd/MM/yyyy");
        }
        try {
            Uteis.getDate(contratoJSON.getDataCadastro());
        } catch (Exception e) {
            throw new Exception("Data de cadastro do contrato é inválida, devendo ser no formato dd/MM/yyyy");
        }
        if (UteisValidacao.emptyNumber(contratoJSON.getDuracao())) {
            throw new Exception("Duração do contrato não informada");
        }

    }

    public void validarDadosBasicosPagamentos(List<PagamentoJSON> listaPagamentoJSON) throws Exception {
        Integer idExterno = 0;
        if (listaPagamentoJSON.isEmpty()) {
            throw new Exception("nenhum pagamento foi reconhecido");
        } else {
            for (PagamentoJSON pagJSON : listaPagamentoJSON) {
                if (idExterno > 0 && !pagJSON.getIdExternoContrato().equals(idExterno)) {
                    throw new Exception("Pagamentos Reconhecidos pagam mais de um contrato");
                } else {
                    idExterno = pagJSON.getIdExternoContrato();
                }
                if (UteisValidacao.emptyNumber(pagJSON.getValor())) {
                    throw new Exception("Um dos Pagamentos Reconhecidos não tem valor informado");
                }
                if (UteisValidacao.emptyString(pagJSON.getFormaPagamento())) {
                    throw new Exception("Um dos Pagamentos Reconhecidos não tem forma de pagamento");
                }
            }
        }
    }

    private void validarDadosBasicosOperacao(List<ContratoOperacaoJSON> listaOperacoes) throws Exception {
        Integer idExterno = 0;
        if (listaOperacoes.isEmpty()) {
            throw new Exception("nenhuma operação foi reconhecido");
        } else {
            for (ContratoOperacaoJSON opeJSON : listaOperacoes) {
                if (!opeJSON.getTipoOperacao().equals("CA")) {
                    if (UteisValidacao.emptyString(opeJSON.getDataInicio())) {
                        throw new Exception("Uma das Operacoes não tem data de inicio informado");
                    }
                    if (UteisValidacao.emptyString(opeJSON.getDataFinal())) {
                        throw new Exception("Uma das Operacoes não tem data de final informada");
                    }
                    if (UteisValidacao.emptyString(opeJSON.getDataCadastro())) {
                        opeJSON.setDataCadastro(opeJSON.getDataInicio());
                    }
                    if (opeJSON.getDataInicioDate().after(Uteis.getDate(opeJSON.getDataFinal()))) {
                        throw new Exception("Uma das Operações está com a data inicio maior que a data final");
                    }
                } else {
                    if (UteisValidacao.emptyString(opeJSON.getDataCadastro())) {
                        if (!UteisValidacao.emptyString(opeJSON.getDataInicio())) {
                            opeJSON.setDataCadastro(opeJSON.getDataInicio());
                        } else {
                            throw new Exception("Operação de cancelamento sem data de cadastro ou inicio");
                        }
                    }
                    opeJSON.setDataInicio(opeJSON.getDataCadastro());
                }
                if (!opeJSON.getTipoOperacao().equals("AT") && !opeJSON.getTipoOperacao().equals("CR")
                        && !opeJSON.getTipoOperacao().equals("CA")) {
                    throw new Exception("Uma das Operações não tem é do tipo Atestado(AT) ou Férias(FR) ou Cancelamento(CA)");
                }
            }
        }
    }

    private void preencherChequeImportacao(MovPagamentoVO pagamentoVO, PagamentoJSON pagJSON, BancoVO bancoVO) throws Exception {
        ChequeVO cheque = new ChequeVO();
        cheque.setValor(pagJSON.getValor());
        cheque.setValorTotal(pagJSON.getValor());
        cheque.setNumero(pagJSON.getNumero());
        if (bancoVO == null || !bancoVO.getCodigoBanco().equals(pagJSON.getBanco())) {
            Banco bancoDAO = new Banco(con);
            if (UteisValidacao.emptyNumber(pagJSON.getBanco())) {
                bancoVO = bancoDAO.consultarOuCriaPorCodigoBanco(999, "BANCO IMPORTAÇÃO");
            } else {
                bancoVO = bancoDAO.consultarCodigoBanco(pagJSON.getBanco(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyNumber(bancoVO.getCodigo())) {
                    bancoVO = bancoDAO.consultarOuCriaPorCodigoBanco(999, "BANCO IMPORTAÇÃO");
                }
            }
        }
        cheque.setBanco(bancoVO);
        cheque.setDataCompensacao(pagJSON.getDataCompensacao());
        cheque.setVistaOuPrazo(cheque.getDataCompensacao().compareTo(Uteis.somarDias(pagamentoVO.getDataLancamento(), pagamentoVO.getEmpresa().getNrDiasChequeAVista())) == 1 ? "PR" : "AV");
        cheque.setNomeNoCheque(pagJSON.getNomeCheque());
        cheque.setConta(UteisValidacao.emptyString(pagJSON.getConta()) ? "impor" : pagJSON.getConta());
        cheque.setAgencia(UteisValidacao.emptyString(pagJSON.getAgencia()) ? "impor" : pagJSON.getConta());
        cheque.setSituacao("EA");
        pagamentoVO.getChequeVOs().add(cheque);
    }

    private void preencherCartaoImportacao(MovPagamentoVO pagamentoVO, PagamentoJSON pagJSON) throws Exception {
        CartaoCreditoVO cartaoCredito = new CartaoCreditoVO();
        cartaoCredito.setValor(pagJSON.getValor());
        cartaoCredito.setValorTotal(pagJSON.getValor());
        cartaoCredito.setOperadora(pagamentoVO.getOperadoraCartaoVO());
        cartaoCredito.setDataCompensacao(pagJSON.getDataCompensacao());
        cartaoCredito.setSituacao("EA");
        pagamentoVO.getCartaoCreditoVOs().add(cartaoCredito);
        pagamentoVO.setNrParcelaCartaoCredito(pagamentoVO.getNrParcelaCartaoCredito() + 1);
    }

    private OperadoraCartaoVO preencherOperadoraCartao(MovPagamentoVO pagamentoVO, PagamentoJSON pagJSON) throws Exception {
        OperadoraCartaoVO operadoraCartaoVO = null;
        String operadora = pagJSON.getOperadoraCartao();
        OperadoraCartao operadoraDAO = new OperadoraCartao(con);
        if (operadora.equals("MASTER CARD CRÉDITO")) {
            operadoraCartaoVO = operadoraDAO.consultarOuCriarPorCodigoOperadora(904, operadora, pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CA"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else if (operadora.equals("MASTER CARD DÉBITO")) {
            operadoraCartaoVO = operadoraDAO.consultarOuCriarPorCodigoOperadora(907, operadora, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else if (operadora.equals("VISA DÉBITO")) {
            operadoraCartaoVO = operadoraDAO.consultarOuCriarPorCodigoOperadora(901, operadora, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else if (operadora.equals("VISA CRÉDITO")) {
            operadoraCartaoVO = operadoraDAO.consultarOuCriarPorCodigoOperadora(904, operadora, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else {
            if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                operadoraCartaoVO = operadoraDAO.consultarOuCriarPorCodigoOperadora(99999, "OPERADORA CREDITO IMPORTACAO", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                operadoraCartaoVO = operadoraDAO.consultarOuCriarPorCodigoOperadora(88888, "OPERADORA DEBITO IMPORTACAO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
        return operadoraCartaoVO;
    }

    public List<MovPagamentoVO> processarPagamentosRecibo(List<PagamentoJSON> listaPagamentos, EmpresaVO empresaVO, PessoaVO pessoaVO, UsuarioVO responsavel, Integer idExternoContrato) throws Exception {
        List<MovPagamentoVO> pagamentosRecibo = new ArrayList<MovPagamentoVO>();
        boolean formaexistente = false;
        FormaPagamento formapagamentoDAO = new FormaPagamento(con);
        BancoVO bancoVO = null;
        for (PagamentoJSON pagJSON : listaPagamentos) {
            formaexistente = false;
            for (MovPagamentoVO pagamentoVO : pagamentosRecibo) {
                if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(pagJSON.getFormaPagamento())) {
                    if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CD") && !pagamentoVO.getAutorizacaoCartao().equals(pagJSON.getAutorizacaoCartao())) {
                        continue;
                    }
                    formaexistente = true;
                    pagamentoVO.setValor(pagamentoVO.getValor() + pagJSON.getValor());
                    pagamentoVO.setValorTotal(pagamentoVO.getValor());
                    if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        preencherChequeImportacao(pagamentoVO, pagJSON, bancoVO);
                    }
                    if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        preencherCartaoImportacao(pagamentoVO, pagJSON);
                    }
                    break;
                }
            }
            if (!formaexistente) {
                MovPagamentoVO pagamentoVO = new MovPagamentoVO();
                pagamentoVO.setFormaPagamento(formapagamentoDAO.consultarPorTipoFormaPagamentoAtiva(pagJSON.getFormaPagamento(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                if (UteisValidacao.emptyNumber(pagamentoVO.getFormaPagamento().getCodigo())) {
                    throw new Exception("Forma de Pagamento não encontrada:" + pagJSON.getFormaPagamento());
                }
                pagamentoVO.setValor(pagJSON.getValor());
                pagamentoVO.setDataLancamento(pagJSON.getDataCadastro());
                if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CD") && pagJSON.getDataCompensacao() != null) {
                    pagamentoVO.setDataPagamento(pagJSON.getDataCompensacao());
                } else {
                    pagamentoVO.setDataPagamento(pagamentoVO.getDataLancamento());
                }
                pagamentoVO.setDataQuitacao(pagamentoVO.getDataPagamento());
                pagamentoVO.setAutorizacaoCartao(pagJSON.getAutorizacaoCartao());
                pagamentoVO.setObservacao(pagJSON.getObservacao());
                pagamentoVO.setEmpresa(empresaVO);
                pagamentoVO.setPessoa(pessoaVO);
                pagamentoVO.setId_recebe(pagJSON.getIdExternoContrato());
                pagamentoVO.setNomePagador(pessoaVO.getNome());
                pagamentoVO.setResponsavelPagamento(responsavel);
                pagamentoVO.setCredito(Boolean.FALSE);
                pagamentoVO.setDepositoCC(Boolean.FALSE);
                pagamentoVO.setMovPagamentoEscolhida(true);
                if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    preencherChequeImportacao(pagamentoVO, pagJSON, bancoVO);
                }
                if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CA") || pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CD")) {
                    pagamentoVO.setOperadoraCartaoVO(preencherOperadoraCartao(pagamentoVO, pagJSON));
                }
                if (pagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    preencherCartaoImportacao(pagamentoVO, pagJSON);
                }
                pagamentoVO.setValorTotal(pagamentoVO.getValor());
                pagamentoVO.setId_recebe(idExternoContrato);
                pagamentosRecibo.add(pagamentoVO);
            }
        }
        return pagamentosRecibo;
    }

    public List<MovParcelaVO> processarParcelasAPagarPorRecibo(Integer contrato, Double valorTotal) throws Exception {
        MovParcela parcelaDAO = new MovParcela(con);
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        List<MovParcelaVO> parcelasSelecionadas = new ArrayList<MovParcelaVO>();
        List<MovParcelaVO> parcelasEmAberto = parcelaDAO.consultarEmAbertoPorContrato(contrato, Uteis.NIVELMONTARDADOS_PARCELA);
        List<MovProdutoParcelaVO> mppTransferir = new ArrayList<MovProdutoParcelaVO>();
        for (MovParcelaVO parcelaVO : parcelasEmAberto) {
            if (Uteis.arredondarForcando2CasasDecimais(valorTotal) >= Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorParcela())) {
                parcelasSelecionadas.add(parcelaVO);
                valorTotal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorTotal - parcelaVO.getValorParcela());
            } else if (Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorTotal) > 0.00) {
                valorTotal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorTotal - parcelaVO.getValorParcela());
                parcelaVO.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorParcela() + valorTotal));
                parcelaDAO.alterarSemCommit(parcelaVO);
                parcelasSelecionadas.add(parcelaVO);
                List<MovProdutoParcelaVO> mppParcela = mppDAO.consultarMovProdutoParcelasPorParcelas(parcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                Double novoValor = parcelaVO.getValorParcela();
                for (MovProdutoParcelaVO mppVO : mppParcela) {
                    if (Uteis.arredondarForcando2CasasDecimais(novoValor) >= Uteis.arredondarForcando2CasasDecimais(mppVO.getValorPago())) {
                        novoValor = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(novoValor - mppVO.getValorPago());
                    } else if (Uteis.arredondarForcando2CasasDecimais(novoValor) > 0.00) {
                        MovProdutoParcelaVO mppClone = (MovProdutoParcelaVO) mppVO.getClone(true);
                        mppVO.setValorPago(Uteis.arredondarForcando2CasasDecimais(novoValor));
                        mppDAO.alterar(mppVO);
                        mppClone.setValorPago(Uteis.arredondarForcando2CasasDecimais(mppClone.getValorPago() - mppVO.getValorPago()));
                        mppTransferir.add(mppClone);
                        novoValor = mppClone.getValorPago() * -1;
                    } else {
                        mppTransferir.add(mppVO);
                        mppDAO.excluir(mppVO);
                    }
                }
            } else {
                parcelaVO.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorParcela() + (valorTotal * -1)));
                parcelaDAO.alterarSemCommit(parcelaVO);
                for (MovProdutoParcelaVO mppVO : mppTransferir) {
                    mppVO.setMovParcela(parcelaVO.getCodigo());
                    mppDAO.incluir(mppVO);
                }
                break;
            }
            if (Uteis.arredondarForcando2CasasDecimais(valorTotal) == 0.00) {
                break;
            }
        }
        return parcelasSelecionadas;
    }

    public JSONObject persistirAcessosJSON(final String json, final Integer empresa, String key) throws Exception {
        JSONObject objRetorno = new JSONObject();
        try {
            con.setAutoCommit(false);
            JSONObject objJSON = new JSONObject(json);
            Long idExternoCliente = objJSON.optLong("idExterno");
            Integer matriculaCliente = null;
            if(objJSON.optBoolean("v2")) {
                matriculaCliente = objJSON.optInt("codigoMatricula");
            }

            StringBuilder sqlCliente = new StringBuilder();
            sqlCliente.append("select c.codigo as cliente, p.codigo as pessoa, p.datanasc as dataNascimento from cliente c \n");
            sqlCliente.append("inner join pessoa p on p.codigo = c.pessoa \n");

            if (objJSON.optBoolean("v2") && !UteisValidacao.emptyNumber(matriculaCliente)) {
                sqlCliente.append("where c.codigomatricula = ").append(matriculaCliente).append(" \n");
            } else {
                sqlCliente.append("where  p.idExterno = '").append(idExternoCliente).append("' \n");
            }
            ResultSet rsCliente = SuperFacadeJDBC.criarConsulta(sqlCliente.toString(), con);
            ClienteVO clienteVO = new ClienteVO();
            if (rsCliente.next()) {
                clienteVO.setCodigo(rsCliente.getInt("cliente"));
                clienteVO.getPessoa().setCodigo(rsCliente.getInt("pessoa"));
                clienteVO.getPessoa().setDataNasc(rsCliente.getDate("dataNascimento"));
            } else {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", "0");
                objRetorno.put("mensagem", "Não existe aluno com o id informado");
                return objRetorno;
            }

            if (!objJSON.optBoolean("v2")) {
                ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("select codigo from acessocliente where cliente = " + clienteVO.getCodigo() + " and meioidentificacaoentrada = " + MeioIdentificacaoEnum.IMPORTACAO.getCodigo(), con);
                if (rsExiste.next()) {
                    objRetorno.put("status", "warning");
                    objRetorno.put("codigoRegistroZW", "0");
                    objRetorno.put("mensagem", "Acessos já importados para esse aluno");
                    return objRetorno;
                }
            }
            List<AcessoClienteJSON> listaAcessosJSON = new ArrayList<AcessoClienteJSON>();
            JSONArray acessos = objJSON.optJSONArray("acessos");
            for (int i = 0; i < acessos.length(); i++) {
                listaAcessosJSON.add(new AcessoClienteJSON(idExternoCliente, acessos.optJSONObject(i)));
            }
            Ordenacao.ordenarLista(listaAcessosJSON, "data");
            AcessoCliente acessoClienteDAO = new AcessoCliente(con);
            ControleCreditoTreino ccDao = new ControleCreditoTreino(con);
            LocalAcessoVO localacessoVO = new LocalAcessoVO();
            ColetorVO coletorVO = new ColetorVO();
            ResultSet rsLocal = SuperFacadeJDBC.criarConsulta("select codigo from localacesso  where empresa  =  " + empresa, con);
            if (rsLocal.next()) {
                localacessoVO.setCodigo(rsLocal.getInt("codigo"));
            } else {
                throw new Exception("Não existe local de acesso configurado para essa empresa");
            }
            ResultSet rsColetor = SuperFacadeJDBC.criarConsulta("select codigo from coletor  where localacesso  = " + localacessoVO.getCodigo(), con);
            if (rsColetor.next()) {
                coletorVO.setCodigo(rsColetor.getInt("codigo"));
            } else {
                throw new Exception("Não existe coletor configurado para essa empresa");
            }

            for (AcessoClienteJSON acessoJSON : listaAcessosJSON) {
                acessoClienteDAO.registrarAcessoCliente(acessoJSON.getData(), clienteVO, SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, DirecaoAcessoEnum.DA_ENTRADA, localacessoVO, coletorVO, usuario, (objJSON.optBoolean("v2") ? MeioIdentificacaoEnum.AVULSO : MeioIdentificacaoEnum.IMPORTACAO), ccDao, key);
            }
            SuperFacadeJDBC.executarConsultaUpdate("update cliente c set uacodigo  = (select codigo from acessocliente where cliente = c.codigo order BY dthrentrada  desc limit 1) where codigo =" + clienteVO.getCodigo(), con);
            con.commit();
            try {
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);
                zwFacade = null;
            } catch (SinteticoException se) {
                Uteis.logar(null, "Problema ao processar sintético do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresa);
            }
            acessoClienteDAO = null;
            ccDao = null;
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", "0");
            objRetorno.put("mensagem", "Acessos importados com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private JustificativaOperacaoVO obterJustificativaOperacao(EmpresaVO empresaVO) throws Exception {
        JustificativaOperacao justificativaDAO = new JustificativaOperacao(con);
        JustificativaOperacaoVO justificativaVO = new JustificativaOperacaoVO();
        justificativaVO.setDescricao("IMPORTAÇÃO");
        justificativaVO.setEmpresa(empresaVO);
        justificativaVO.setTipoOperacao("--");
        return justificativaDAO.criarOuConsultarSeExistePorNome(justificativaVO);
    }

    private void ajustarHistoricoContrato(ContratoOperacaoVO contratoOperacaoVO, ContratoVO contratoVO) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    private void ajustarPeriodoAcessoContrato(ContratoOperacaoVO contratoOperacaoVO, ContratoVO contratoVO) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    public Integer incluirPagamentoProtheus(String dados) throws Exception {
        JSONObject json = new JSONObject(dados);

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();

        TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaProtheus(json.getString("tipo"));
        FormaPagamentoVO forma = getFacade().getFormaPagamento().consultarPorTipoFormaPagamento(tipoFormaPagto.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (forma == null) {
            forma = getFacade().getFormaPagamento().obterFormaPagamentoRapido(tipoFormaPagto);
        }

        Double valor = 0.0;
        PessoaVO pessoa = null;
        MovParcelaVO parcelaVO = null;
        Integer empresa = 0;
        MovParcela parcelaDao = new MovParcela(con);
        Empresa empresaDao = new Empresa(con);
        MovProdutoParcela produtoParcelaDao = new MovProdutoParcela(con);

        Integer parcela = null;
        ResultSet pedmen = SuperFacadeJDBC.criarConsulta("select codigo from movparcela where tid = '" + json.getString("pedmen") + "'", con);

        if (pedmen.next()) {
            parcela = pedmen.getInt("codigo");
        } else {
            throw new Exception("não foi possível encontrar a parcela desse pedmen");
        }

        MovParcelaVO parc = getFacade().getMovParcela().consultarPorCodigo(parcela, Uteis.NIVELMONTARDADOS_PARCELA);
        parc.setMovProdutoParcelaVOs(produtoParcelaDao.consultarPorCodigoMovParcela(parc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        pessoa = pessoa == null ? parc.getPessoa() : pessoa;
        parcelaVO = parcelaVO == null ? parc : parcelaVO;
        valor += parc.getValorParcela();

        empresa = parc.getEmpresa().getCodigo();
        listaParcelas.add(parc);

        UsuarioVO usuarioVO = getFacade().getZWFacade().getUsuarioRecorrencia();
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Double multa = parcelaDao.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, Calendario.hoje(),
                false, 1.0, null);
        List<MovParcelaVO> multas = new ArrayList<MovParcelaVO>();
        if (!UteisValidacao.emptyNumber(multa)) {
            multas = parcelaDao.criarParcelaMultaJuros(listaParcelas,
                    empresaVO, usuarioVO, Calendario.hoje(), 1.0, null, true);
        }

        listaParcelas.addAll(multas);


        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setFormaPagamento(forma);
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(valor + multa);
        movPagamentoVO.setValorTotal(valor + multa);
        movPagamentoVO.setPessoa(pessoa);
        movPagamentoVO.setObservacao("BAIXA DO PROTHEUS");
        if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)) {
            movPagamentoVO.setNrParcelaCartaoCredito(1);
            List<OperadoraCartaoVO> operadoras = getFacade().getOperadoraCartao().consultarPorDescricao("MASTERCARD", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (operadoras != null && !operadoras.isEmpty()) {
                movPagamentoVO.setOperadoraCartaoVO(operadoras.get(0));
            }
        }

        movPagamentoVO.setNomePagador(pessoa.getNome());
        movPagamentoVO.setResponsavelPagamento(usuarioVO);
        movPagamentoVO.setPessoa(parcelaVO.getPessoa());
        movPagamentoVO.setEmpresa(parcelaVO.getEmpresa());

        listaPagamento.add(movPagamentoVO);

        MovPagamentoVO.validarDados(movPagamentoVO);

        ReciboPagamentoVO reciboObj = getFacade().getMovPagamento().incluirListaPagamento(
                listaPagamento,
                listaParcelas,
                null,
                parcelaVO.getContrato(),
                false, 0.0);
        SuperFacadeJDBC.executarConsulta("update recibopagamento set integrado = true where codigo = " + reciboObj.getCodigo(), con);
        return reciboObj.getCodigo();
    }

    public Date obterDataNascimento(ClienteJSON clienteJSON) throws Exception {
        Date dataNascimento = null;
        try {
            dataNascimento = Uteis.getDate(clienteJSON.getDataNascimento());
        } catch (Exception e) {
        }
        if ((dataNascimento != null) && (dataNascimento.after(Calendario.hoje()))) {
            if (UteisValidacao.emptyNumber(clienteJSON.getIdExterno())) {
                throw new Exception("Data de Nascimento superior a data atual.");
            }
            dataNascimento = null;
        }
        return dataNascimento;
    }


    public JSONObject deletarClienteJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        try {
            con.setAutoCommit(false);
            ClienteJSON clienteJSON = new ClienteJSON(new JSONObject(json));

            Cliente clienteDAO = new Cliente(con);
            ClienteVO clienteVO = clienteDAO.consultarPorIdExterno(clienteJSON.getIdExterno(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO responsavel = new UsuarioVO();
            responsavel.setCodigo(1);
            clienteDAO.excluirClienteETodosSeusRelacionamentos(clienteVO, getUsuario());
            con.commit();
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", 0);
            objRetorno.put("mensagem", "Cliente excluído com sucesso");

            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public JSONObject deletarContratoJson(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.deletarContratoJson");
        try {

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isEmImportacao()) {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
                return objRetorno;
            }
            con.setAutoCommit(false);
            ContratoJSON contratoJSON = new ContratoJSON(new JSONObject(json));
            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select codigo from contrato where idexterno = " + contratoJSON.getIdExterno() + " and empresa = " + empresa, con);
            int contrato = 0;
            if (rsContrato.next()) {
                contrato = rsContrato.getInt("codigo");
                ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_TODOS);
                int contratoSucessor = UteisValidacao.notEmptyNumber(contratoVO.getContratoResponsavelRematriculaMatricula()) ? contratoVO.getContratoResponsavelRematriculaMatricula() : contratoVO.getContratoResponsavelRenovacaoMatricula();
                if (UteisValidacao.notEmptyNumber(contratoSucessor)) {
                    throw new Exception("Estorne primeiro o contrato " + contratoSucessor);
                }
                ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                getFacade().getContrato().estornoContrato(contratoVO, clienteVO, null, "Solicitação Importador OAMD");
                processarClienteEstornoContrato(clienteVO);
            } else {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Contrato não encontrado para exclusão");
                return objRetorno;
            }
            con.commit();
            objRetorno.put("status", "success");
            objRetorno.put("codigoRegistroZW", 0);
            objRetorno.put("mensagem", "Contrato " + contrato + " estornado com sucesso");

            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    public JSONObject deletarPagamentosContratoJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.deletarPagamentosContratoJSON");
        try {

            con.setAutoCommit(false);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isEmImportacao()) {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
                return objRetorno;
            }

            JSONObject objJSON = new JSONObject(json);
            int idExternoContrato = objJSON.optInt("idExternoContrato");
            ResultSet rsExistente = SuperFacadeJDBC.criarConsulta("select codigo from movpagamento where id_recebe = " + idExternoContrato + " and empresa = " + empresa, con);
            if (rsExistente.next()) {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Já foi importado pagamento para o contrato de idExterno: " + idExternoContrato);
                return objRetorno;
            }
            //implementar
            con.commit();
            objRetorno.put("status", "success");
//            objRetorno.put("codigoRegistroZW",recibos.replaceFirst(",", ""));
            objRetorno.put("mensagem", "Contrato importado com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    public JSONObject persistirProdutosJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.persistirProdutosJSON");
        try {

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isEmImportacao()) {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
                return objRetorno;
            }
            con.setAutoCommit(false);
            //implementar
            con.commit();
            objRetorno.put("status", "success");
//            objRetorno.put("codigoRegistroZW",contratoControle.getContratoVO().getCodigo());
            objRetorno.put("mensagem", "Contrato importado com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public JSONObject persistirVendasAvulsasJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.persistirVendasAvulsasJSON");
        try {

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isEmImportacao()) {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
                return objRetorno;
            }
            con.setAutoCommit(false);
            //implementar

            con.commit();
            objRetorno.put("status", "success");
//            objRetorno.put("codigoRegistroZW",contratoControle.getContratoVO().getCodigo());
            objRetorno.put("mensagem", "Contrato importado com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }


    public JSONObject persistirPagamentosVendaAvulsaJSON(final String json, final Integer empresa) throws Exception {
        JSONObject objRetorno = new JSONObject();
        LogIntegracoesVO logIntegracoesVO = inserirLogIntegracao(json,"IntegracaoImportacao.persistirPagamentosVendaAvulsaJSON");
        try {

            con.setAutoCommit(false);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isEmImportacao()) {
                objRetorno.put("status", "error");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Empresa não está habilitada para aceitar dados de importação");
                return objRetorno;
            }

            JSONObject objJSON = new JSONObject(json);
            int idExternoContrato = objJSON.optInt("idExternoContrato");
            ResultSet rsExistente = SuperFacadeJDBC.criarConsulta("select codigo from movpagamento where id_recebe = " + idExternoContrato + " and empresa = " + empresa, con);
            if (rsExistente.next()) {
                objRetorno.put("status", "erro");
                objRetorno.put("codigoRegistroZW", 0);
                objRetorno.put("mensagem", "Já foi importado pagamento para o contrato de idExterno: " + idExternoContrato);
                return objRetorno;
            }
//            List<PagamentoJSON> listaPagamentoJSON = new ArrayList<PagamentoJSON>();
//            JSONArray pagamentos = objJSON.optJSONArray("pagamentos");
//            for(int i = 0; i < pagamentos.length(); i++){
//                listaPagamentoJSON.add(new PagamentoJSON(idExternoContrato, pagamentos.optJSONObject(i)));
//            }
//
//            validarDadosBasicosPagamentos(listaPagamentoJSON);
//
//            Date dataLancamentoContrato = null;
//            ContratoVO contratoVO = new ContratoVO();
//            Integer pessoa = 0;
//            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select codigo as contrato, datalancamento, pessoa from contrato where idexterno = "+idExternoContrato+" and empresa = "+empresa, con);
//            if(rsContrato.next()){
//                contratoVO.setCodigo(rsContrato.getInt("contrato"));
//                dataLancamentoContrato = rsContrato.getDate("dataLancamento");
//                pessoa = rsContrato.getInt("pessoa");
//            } else {
//                objRetorno.put("status", "erro");
//                objRetorno.put("codigoRegistroZW",0);
//                objRetorno.put("mensagem","Não foi encontrado contrato com o idExterno informado: "+listaPagamentoJSON.get(0).getIdExternoContrato());
//                return objRetorno;
//            }
//            Map<Date, List<PagamentoJSON>> mapRecibos = new HashMap<Date, List<PagamentoJSON>>();
//            mapRecibos.put(Uteis.getDataComHoraZerada(dataLancamentoContrato), new ArrayList<PagamentoJSON>());
//            for (PagamentoJSON pagJSON : listaPagamentoJSON){
//                if(pagJSON.getDataCadastro() == null){
//                    pagJSON.setDataCadastro(dataLancamentoContrato);
//                }
//                if(!mapRecibos.containsKey(Uteis.getDataComHoraZerada(pagJSON.getDataCadastro()))){
//                   mapRecibos.put(Uteis.getDataComHoraZerada(pagJSON.getDataCadastro()), new ArrayList<PagamentoJSON>());
//                }
//                mapRecibos.get(Uteis.getDataComHoraZerada(pagJSON.getDataCadastro())).add(pagJSON);
//            }
//
//            Pessoa pessoaDAO = new Pessoa();
//            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
//            List<MovPagamentoVO> pagamentosRecibo = null;
//            UsuarioVO responsavel = new UsuarioVO();
//            responsavel.setCodigo(1);
//            ReciboPagamento reciboDAO = new ReciboPagamento(con);
//            MovPagamento movpagamentoDAO = new MovPagamento(con);
//            List<MovParcelaVO> parcelas = null;
//            String recibos = "";
//            SortedSet<Date> keys = new TreeSet<Date>(mapRecibos.keySet());
//            for (Date key : keys) {
//                List<PagamentoJSON> listaPagamentos = mapRecibos.get(key);
//                if(listaPagamentos.isEmpty()){
//                    continue;
//                }
//                pagamentosRecibo = processarPagamentosRecibo(listaPagamentos, empresaVO, pessoaVO, responsavel, idExternoContrato);
//                ReciboPagamentoVO reciboPagamento  = new ReciboPagamentoVO();
//                movpagamentoDAO.inicializarDadosReciboPagamento(pagamentosRecibo, reciboPagamento, contratoVO);
//                reciboDAO.incluir(reciboPagamento);
//                parcelas = processarParcelasAPagarPorRecibo(contratoVO.getCodigo(), reciboPagamento.getValorTotal());
//                movpagamentoDAO.incluirListaPagamento(pagamentosRecibo, parcelas, null, contratoVO, false, 0.0, false, reciboPagamento);
//                recibos += ","+reciboPagamento.getCodigo();
//            }
            con.commit();
            objRetorno.put("status", "success");
//            objRetorno.put("codigoRegistroZW",recibos.replaceFirst(",", ""));
            objRetorno.put("mensagem", "Contrato importado com sucesso");
            return objRetorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            alterarLogIntegracao(logIntegracoesVO, objRetorno.toString());
        }
    }

    public void processarClienteEstornoContrato(ClienteVO clienteVO) throws Exception {
        // pega o ultimo contrato vigente da pessoa
        ContratoVO contrato = getFacade().getContrato().consultarContratoVigentePorPessoa(clienteVO.getPessoa().getCodigo(), false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        // se contrato encontrado
        if (contrato.getCodigo() != 0) {
            // altera o historico se necessario
            Contrato.gerarHistoricoTemporalUmContrato(contrato.getCodigo());
            // exclui a situacao sintetica do cliente, que ainda é relativa ao contrato estornado
            getFacade().getSituacaoClienteSinteticoDW().excluir(clienteVO.getCodigo());
        }
        // atualiza a situacao do cliente
        getFacade().getZWFacade().atualizarSintetico(clienteVO, Calendario.hoje(),
                SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
    }

    public String atualizaInformacoesAluno(String dadosAluno, Integer codigoMatricula, Integer empresa) throws Exception {
        try {
            Cliente clienteDao = new Cliente(con);
            ClienteVO clienteVO = clienteDao.consultarPorCodigoMatricula(codigoMatricula, empresa, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
            clienteDao = null;

            JSONObject dadosAlunoJson = new JSONObject(dadosAluno);

            atualizaPessoa(dadosAlunoJson, clienteVO.getPessoa().getCodigo());

            atualizaCliente(codigoMatricula, dadosAlunoJson);

            criaVinculo(empresa, dadosAlunoJson, clienteVO);

            atualizaEndereco(dadosAlunoJson.getJSONObject("endereco"), clienteVO.getPessoa().getCodigo());

            atualizaTelefones(dadosAlunoJson.getJSONArray("telefones"), clienteVO.getPessoa().getCodigo());

            atualizaEmail(dadosAlunoJson.optString("email"), clienteVO.getPessoa().getCodigo());

            atualizaClienteSintetico(clienteVO);

            if(dadosAlunoJson.getBoolean("sesc") && !Util.isEmptyString( clienteVO.getPessoa().getEmail() )) {
                Optin opt = new Optin(con);
                OptinVO optinVO = new OptinVO();
                boolean optin = true;
                try{optin = dadosAlunoJson.getBoolean("optin");}
                catch (Exception e){}
                atualizaoptin(optin, optinVO, opt, empresa, clienteVO, dadosAlunoJson.getString("email"));
            }

            return "Dados atualizados com sucesso!";
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private void criaVinculo(Integer empresa, JSONObject dadosAlunoJson, ClienteVO clienteVO) throws Exception {
        Colaborador colaborador = new Colaborador(con);
        List<ColaboradorVO> colaboradorEncontrado = colaborador.consultarPorNomeColaboradorComLimite(dadosAlunoJson.optString("consultor"), empresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(colaboradorEncontrado.size() == 1) {
            Vinculo vinculo = new Vinculo(con);
            boolean jaExisteVinculo = vinculo.jaExisteVinculoEntreOClienteEColaborador(colaboradorEncontrado.get(0).getCodigo(), clienteVO.getCodigo());
            if (!jaExisteVinculo) {
                String sql = "INSERT INTO vinculo VALUES (?, 'CO', ?)";

                PreparedStatement sqlAlter = con.prepareStatement(sql);
                sqlAlter.setInt(1, colaboradorEncontrado.get(0).getCodigo());
                sqlAlter.setInt(2, clienteVO.getCodigo());

                sqlAlter.execute();
            }
        }
    }

    private void atualizaClienteSintetico(ClienteVO clienteVO) throws Exception {
        if(clienteVO != null) {
            ZillyonWebFacade zw = new ZillyonWebFacade(con);
            zw.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        }
    }

    private String preparaDadosAluno(String dadosAluno) {
        dadosAluno = dadosAluno.replaceAll("\\{|\\}", "");
        dadosAluno = dadosAluno.replaceFirst("\\\"numero\\\":\\\"\\(", "\"numeroTelefone\":\"(");
        dadosAluno = dadosAluno.replaceFirst("\\\"endereco\\\"\\:", "");
        dadosAluno = dadosAluno.replaceFirst("\\\"telefones\\\"\\:", "");
        return dadosAluno;
    }

    private void atualizaEmail(String email, Integer codigoPessoa) throws Exception {
        Email emailDAO = new Email(con);
        if (Uteis.isValidEmailAddressRegex(email)) {
            if (emailDAO.verificaSePossuiEmail(codigoPessoa)) {
                String sql = "UPDATE email\n" +
                        "SET email = ? \n" +
                        "WHERE codigo = (select max(e.codigo) from email e where e.pessoa = ?)";
                PreparedStatement sqlAlter = con.prepareStatement(sql);
                sqlAlter.setString(1, email);
                sqlAlter.setInt(2, codigoPessoa);

                sqlAlter.execute();
            } else {
                EmailVO emailVO = new EmailVO();
                emailVO.setEmail(email);
                emailVO.setPessoa(codigoPessoa);
                emailVO.setEmailCorrespondencia(true);
                emailDAO.incluir(emailVO);
            }
        } else {
            throw new Exception(" Email com formado inválido! " + email);
        }
        emailDAO = null;
    }

    private Boolean validaFormatoTelefone(String regex, String telefone) {
        Pattern rgx = Pattern.compile(regex);
        Matcher match = rgx.matcher(telefone);
        if(match.find()) {
            return true;
        }
        return false;
    }

    private void atualizaTelefones(JSONArray telefonesJsonArray, Integer codigoPessoa) throws Exception {
        List<TelefoneVO> telefonesIncluir = new ArrayList<>();
        for (int i = 0; i < telefonesJsonArray.length(); i++) {
            JSONObject telefoneJson = telefonesJsonArray.getJSONObject(i);
            String stringTipoTelefone = telefoneJson.optString("tipo");
            String numeroTelefone = telefoneJson.optString("numero");
            String tipoTelefoneSigla = null;
            for (TipoTelefoneEnum tipoTelefone : TipoTelefoneEnum.values()) {
                if (tipoTelefone.getDescricao().equalsIgnoreCase(stringTipoTelefone) || tipoTelefone.getCodigo().equalsIgnoreCase(stringTipoTelefone)) {
                    tipoTelefoneSigla = tipoTelefone.getCodigo();
                }
            }

            if (tipoTelefoneSigla == null) {
                throw new Exception(" Tipo de telefone inválido! " + stringTipoTelefone);
            }
            if (!validaFormatoTelefone("\\(\\d{2}\\)\\d{8}", numeroTelefone) && !validaFormatoTelefone("\\(\\d{2}\\)\\d{9}", numeroTelefone)) {
                throw new Exception(" Telefone com formado inválido! " + numeroTelefone);
            }

            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setNumero(numeroTelefone);
            telefoneVO.setDescricao(telefoneJson.optString("descricao"));
            telefoneVO.setTipoTelefone(tipoTelefoneSigla);
            telefoneVO.setPessoa(codigoPessoa);

            telefonesIncluir.add(telefoneVO);
        }

        if (telefonesIncluir.size() > 0) {
            SuperFacadeJDBC.executarUpdate("delete from telefone where pessoa = " + codigoPessoa, con);
            Telefone telefoneDAO = new Telefone(con);
            telefoneDAO.incluirTelefones(codigoPessoa, telefonesIncluir);
            telefoneDAO = null;
        }
    }

    private void atualizaEndereco(JSONObject enderecoJSON, Integer codigoPessoa) throws Exception {
        Endereco enderecoDAO = new Endereco(con);
        if (enderecoDAO.verificarSePossuiEndereco(codigoPessoa)) {
            String sql = "UPDATE endereco\n" +
                    "SET cep = ?,\n" +
                    "    endereco = ?,\n" +
                    "    bairro = ?,\n" +
                    "    complemento = ?,\n" +
                    "    numero = ?,\n" +
                    "    dataatualizacao = ?\n" +
                    "WHERE codigo = (SELECT max(e.codigo) FROM endereco e WHERE e.pessoa = ?)";
            PreparedStatement sqlAlter = con.prepareStatement(sql);
            sqlAlter.setString(1, enderecoJSON.optString("cep"));
            sqlAlter.setString(2, enderecoJSON.optString("logradouro"));
            sqlAlter.setString(3, enderecoJSON.optString("bairro"));
            sqlAlter.setString(4, enderecoJSON.optString("complemento"));
            sqlAlter.setString(5, enderecoJSON.optString("numero"));
            sqlAlter.setTimestamp(6, Uteis.getTimestamp());
            sqlAlter.setInt(7, codigoPessoa);

            sqlAlter.execute();
        } else {
            EnderecoVO enderecoVO = new EnderecoVO();
            enderecoVO.setPessoa(codigoPessoa);
            enderecoVO.setEndereco(enderecoJSON.optString("logradouro"));
            enderecoVO.setNumero(enderecoJSON.optString("numero"));
            enderecoVO.setComplemento(enderecoJSON.optString("complemento"));
            enderecoVO.setBairro(enderecoJSON.optString("bairro"));
            enderecoVO.setCep(enderecoJSON.optString("cep"));
            enderecoDAO.incluir(enderecoVO);
        }
        enderecoDAO = null;
    }

    private void atualizaCliente(Integer matricula, JSONObject dadosAlunoJson) throws Exception {
        String sql = "UPDATE cliente\n" +
                "    SET nomesocial = ?,\n" +
                "        renda = ?,\n" +
                "        validadecartaosesc = ?,\n" +
                "        matriculasesc = ?,\n" +
                "        sesc = ?,\n" +
                "        categoria = ?,\n" +
                "        datavalidadecarteirinha = ?\n" +
                "WHERE codigomatricula = ?";
        PreparedStatement sqlAlter = con.prepareStatement(sql);
        sqlAlter.setString(1, dadosAlunoJson.optString("nomesocial"));
        sqlAlter.setDouble(2, dadosAlunoJson.optDouble("renda"));
        sqlAlter.setTimestamp(3, Uteis.getDataJDBCTimestamp(Uteis.getDate(dadosAlunoJson.optString("validadecartaosesc"))));
        sqlAlter.setString(4, dadosAlunoJson.optString("matriculasesc"));
        sqlAlter.setBoolean(5, Boolean.valueOf(dadosAlunoJson.optString("sesc")));


        CategoriaVO categoriaVO = null;
        String categoria = dadosAlunoJson.optString("categoria");
        if (!UteisValidacao.emptyString(categoria)) {
            if (categoria.matches("\\d+")) {
                Categoria categoriaDAO = new Categoria(con);
                categoriaVO = categoriaDAO.consultarPorChavePrimaria(Integer.parseInt(categoria), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                categoriaDAO = null;
            } else {
                categoriaVO = obterCategoria(dadosAlunoJson.optString("categoria"));
            }
        }
        if(categoriaVO != null) {
            sqlAlter.setInt(6, categoriaVO.getCodigo());
        } else {
            sqlAlter.setNull(6, 0);
        }

        sqlAlter.setTimestamp(7, Uteis.getDataJDBCTimestamp(Uteis.getDate(dadosAlunoJson.optString("validadecartaosesc"))));
        sqlAlter.setInt(8, matricula);

        sqlAlter.execute();
    }

    private void atualizaPessoa(JSONObject dadosAlunoJson, Integer codigoPessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE pessoa\n");
        sql.append("SET nome               = ?,\n");
        sql.append("    grauinstrucao      = ?,\n");
        sql.append("    profissao          = ?,\n");
        sql.append("    naturalidade       = ?,\n");
        sql.append("    nacionalidade      = ?,\n");
        sql.append("    rg                 = ?,\n");
        sql.append("    rgorgao            = ?,\n");
        sql.append("    cfp                = ?,\n");
        sql.append("    cpfmae             = ?,\n");
        sql.append("    cpfpai             = ?,\n");
        String senhaAcesso = dadosAlunoJson.optString("senha");
        if (!UteisValidacao.emptyString(senhaAcesso)) {
            sql.append("    senhaacesso        = ?,\n");
        }
        sql.append("    nomepai            = ?,\n");
        sql.append("    nomemae            = ?,\n");
        sql.append("    sexo               = ?,\n");
        sql.append("    estadocivil        = ?,\n");
        sql.append("    datanasc           = ?,\n");
        sql.append("    cidade             = ?,\n");
        sql.append("    estado             = ?,\n");
        sql.append("    pais               = ? \n");
        boolean sesc = dadosAlunoJson.getBoolean("sesc");
        boolean responsavelFinanceiro = false;
        boolean cpfResponsavel = false;
        if (sesc){
            responsavelFinanceiro = dadosAlunoJson.has("responsavelFinanceiro");
            cpfResponsavel = dadosAlunoJson.has("cpfResponsavel");
            if (responsavelFinanceiro){
                sql.append("    ,nomerespfinanceiro = ?\n");
            }
            if (cpfResponsavel) {
                sql.append("    ,cpfrespfinanceiro  = ?\n");
            }
        }
        sql.append(" WHERE codigo = ?");
        PreparedStatement sqlAlter = con.prepareStatement(sql.toString());
        int i = 1;
        sqlAlter.setString(i++, dadosAlunoJson.optString("nomeCompleto"));
        GrauInstrucao grauInstrucao = new GrauInstrucao(con);
        GrauInstrucaoVO grauInstrucaoVO = !dadosAlunoJson.optString("escolaridade").isEmpty() ? obterGrauInstrucao(dadosAlunoJson.optString("escolaridade")) : new GrauInstrucaoVO();
        if(grauInstrucaoVO.getCodigo() > 0) {
            sqlAlter.setInt(i++, grauInstrucaoVO.getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        Profissao profissao = new Profissao(con);
        ProfissaoVO profissaoVO = !dadosAlunoJson.optString("profissao").isEmpty() ? obterProfissao(dadosAlunoJson.optString("profissao")) : new ProfissaoVO();
        if( profissaoVO.getCodigo() > 0) {
            sqlAlter.setInt(i++, profissaoVO.getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        sqlAlter.setString(i++, dadosAlunoJson.optString("naturalidade"));
        sqlAlter.setString(i++, dadosAlunoJson.optString("nacionalidade"));
        sqlAlter.setString(i++, dadosAlunoJson.optString("rg"));
        sqlAlter.setString(i++, dadosAlunoJson.optString("orgaoEmissaoRG").length() > 10 ? dadosAlunoJson.optString("orgaoEmissaoRG").substring(0, 10) : dadosAlunoJson.optString("orgaoEmissaoRG"));
        String cpf = dadosAlunoJson.optString("cpf");
        if (!UteisValidacao.emptyString(cpf) && (!cpf.contains(".") || !cpf.contains("-"))) {
            cpf = Uteis.removerMascara(cpf);
            cpf = Uteis.aplicarMascara(cpf, "999.999.999-99");
        }
        sqlAlter.setString(i++, cpf);
        String cpfMae = "";
        if (!sesc) {
            cpfMae = (UteisValidacao.emptyString(dadosAlunoJson.optString("cpfMae")) ? dadosAlunoJson.optString("cpfResponsavel") : dadosAlunoJson.optString("cpfMae"));
        } else {
            cpfMae = dadosAlunoJson.optString("cpfMae");
        }
        if (!UteisValidacao.emptyString(cpfMae) && (!cpfMae.contains(".") || !cpfMae.contains("-"))) {
            cpfMae = Uteis.removerMascara(cpfMae);
            cpfMae = Uteis.aplicarMascara(cpfMae, "999.999.999-99");
        }
        sqlAlter.setString(i++, cpfMae);
        String cpfPai = dadosAlunoJson.optString("cpfPai");
        if (!UteisValidacao.emptyString(cpfPai) && (!cpfPai.contains(".") || !cpfPai.contains("-"))) {
            cpfPai = Uteis.removerMascara(cpfPai);
            cpfPai = Uteis.aplicarMascara(cpfPai, "999.999.999-99");
        }
        sqlAlter.setString(i++, cpfPai);
        if (!UteisValidacao.emptyString(senhaAcesso)) {
            sqlAlter.setString(i++, Uteis.encriptar(senhaAcesso));
        }
        sqlAlter.setString(i++, dadosAlunoJson.optString("nomePai"));
        sqlAlter.setString(i++, UteisValidacao.emptyString(dadosAlunoJson.optString("nomeMae")) ? dadosAlunoJson.optString("responsavelFinanceiro") : dadosAlunoJson.optString("nomeMae"));
        sqlAlter.setString(i++, dadosAlunoJson.optString("sexo"));
        sqlAlter.setString(i++, dadosAlunoJson.optString("estadoCivil"));
        sqlAlter.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Uteis.getDate(dadosAlunoJson.optString("dataNascimento"))));

        EstadoVO estadoVO = new EstadoVO();
        CidadeVO cidadeVO = new CidadeVO();
        Pais paisDAO = new Pais(con);
        PaisVO paisVO = paisDAO.consultarPorNome("BRASIL", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        JSONObject enderecoJson = dadosAlunoJson.getJSONObject("endereco");
        if (!enderecoJson.optString("uf").trim().isEmpty() && !enderecoJson.optString("cidade").trim().isEmpty()) {
            Estado estadoDao = new Estado(con);
            estadoVO = estadoDao.consultarPorSiglaUf(enderecoJson.optString("uf"), paisVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            estadoDao = null;
            if (estadoVO == null || estadoVO.getCodigo() == 0){
                throw new Exception(" Uf inválido ou não encontrado");
            }
            UteisImportacao uteisImportacao = new UteisImportacao();
            cidadeVO = uteisImportacao.obterCidade(enderecoJson.optString("cidade"), estadoVO, con);
        } else if (enderecoJson.optString("uf").trim().isEmpty() && !enderecoJson.optString("cidade").trim().isEmpty()) {
            throw new Exception(" Uf não informado");
        } else if (enderecoJson.optString("cidade").trim().isEmpty() && !enderecoJson.optString("uf").trim().isEmpty()) {
            throw new Exception(" Cidade não informada");
        }

        if (cidadeVO.getCodigo() > 0) {
            sqlAlter.setInt(i++, cidadeVO.getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (estadoVO.getCodigo() > 0) {
            sqlAlter.setInt(i++, estadoVO.getCodigo());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (estadoVO.getPais() > 0) {
            sqlAlter.setInt(i++, estadoVO.getPais());
        } else {
            sqlAlter.setNull(i++, 0);
        }
        if (responsavelFinanceiro) {
            sqlAlter.setString(i++, dadosAlunoJson.has("responsavelFinanceiro") ? dadosAlunoJson.getString("responsavelFinanceiro") : "");
        }
        if (cpfResponsavel){
            sqlAlter.setString(i++, dadosAlunoJson.has("cpfResponsavel") ? dadosAlunoJson.getString("cpfResponsavel") : "");
        }

        sqlAlter.setInt(i++, codigoPessoa);

        sqlAlter.execute();
    }

    public void atualizarCodigoAfiliadoVitio (Integer codigoPessoa, String codigoAfiliado, Integer empresa) throws Exception {
        String sql = "UPDATE colaborador set codigoAfiliadoVitio = ? WHERE pessoa = ? AND empresa = ?;";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setString(1, codigoAfiliado);
            pst.setInt(2, codigoPessoa);
            pst.setInt(3, empresa);
            pst.execute();
        }
    }

    public ClienteVO processarCadastroAlunoSesiCe(AlunoSesiCeJSON alunoSesiCeJSON, Integer codigoEmpresa) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        ClienteVO clienteVO = null;
        try {
            con.setAutoCommit(false);

            if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                throw new Exception("Empresa não encontrada.");
            }

            clienteVO = obterClientePorMatriculaExternaOuCpf(alunoSesiCeJSON.getIdMatricula(), alunoSesiCeJSON.getCpf(), codigoEmpresa);
            clienteVO = clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())
                    ? new ClienteVO() : clienteVO;

            String cpf = Uteis.formatarCpfCnpj(alunoSesiCeJSON.getCpf(), false);
            String cpfMae = Uteis.formatarCpfCnpj(alunoSesiCeJSON.getCpfTitular(), false);

            // pessoa
            clienteVO.getPessoa().setCategoriaPessoa(TipoPessoa.FISICA);
            clienteVO.getPessoa().setTipoPessoa(TipoPessoa.FISICA.getLabel());
            clienteVO.getPessoa().setNome(alunoSesiCeJSON.getNome().toUpperCase());
            clienteVO.getPessoa().setCfp(cpf);
            clienteVO.getPessoa().setCpfMae(cpfMae);
            clienteVO.getPessoa().setNomeMae(alunoSesiCeJSON.getNomeTitular());
            if (alunoSesiCeJSON.getDataNascimento().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}")) {
                clienteVO.getPessoa().setDataNasc(Calendario.getDate("yyyy-MM-dd", alunoSesiCeJSON.getDataNascimento()));
            }
            clienteVO.getPessoa().setSexo(alunoSesiCeJSON.getSexo());
            clienteVO.getPessoa().setCpfMae(alunoSesiCeJSON.getCpfTitular());
            if (!UteisValidacao.emptyString(alunoSesiCeJSON.getCelular().trim())
                    && Uteis.validarTelefoneCelular(alunoSesiCeJSON.getCelular())) {
                povoarTelefonePessoa(clienteVO.getPessoa().getTelefoneVOs(), alunoSesiCeJSON.getCelular(), TipoTelefoneEnum.CELULAR, "");
            }
            if (!UteisValidacao.emptyString(alunoSesiCeJSON.getEmail())) {
                if (UteisValidacao.validaEmail(alunoSesiCeJSON.getEmail())) {
                    clienteVO.getPessoa().setEmail(alunoSesiCeJSON.getEmail());
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(alunoSesiCeJSON.getEmail());
                    emailVO.setEmailCorrespondencia(true);
                    clienteVO.getPessoa().getEmailVOs().add(emailVO);
                } else {
                    String servico = String.format(String.format("Integração SESI-CE | Unid. Externa: %s", empresaVO.getIdExterno()));
                    String resultado = String.format("O E-mail: %s  é invalido", alunoSesiCeJSON.getEmail());
                    gravarLogIntegracao(servico, alunoSesiCeJSON.toString(), resultado);
                }
            }

            // cliente
            clienteVO.setEmpresa(empresaVO);
            clienteVO.setMatriculaExterna(alunoSesiCeJSON.getIdMatricula().longValue());

            TiposStatusClienteSesiCeEnum tiposStatusClienteSesiCeEnum = TiposStatusClienteSesiCeEnum.obterConsultarPorDescricao(alunoSesiCeJSON.getStatusMatricula());
            clienteVO.setStatusMatriculaSesiCe(tiposStatusClienteSesiCeEnum != null ? tiposStatusClienteSesiCeEnum.getSigla() : null);
            clienteVO.setRazaoSocialEmpresaSesiCe(alunoSesiCeJSON.getRazaoSocial());
            TiposNecessidadesEspeciaisSesiCeEnum tiposNecessidadesEspeciaisSesiCeEnum = TiposNecessidadesEspeciaisSesiCeEnum.obterConsultarPorDescricaoSESICE(alunoSesiCeJSON.getNecessidadesEspeciais());
            clienteVO.setNecessidadesEspeciaisSesiCe(tiposNecessidadesEspeciaisSesiCeEnum != null ? tiposNecessidadesEspeciaisSesiCeEnum.getSigla() : null);
            Date dataValidade = alunoSesiCeJSON.getDataValidadeCadastro().split(" ")[0].matches("\\d{4}-\\d{2}-\\d{2}") ?
                    Calendario.getDate("yyyy-MM-dd", alunoSesiCeJSON.getDataValidadeCadastro()) : null;
            clienteVO.setDataValidadeCadastroSesiCe(dataValidade);

            if (!UteisValidacao.emptyString(alunoSesiCeJSON.getCategoria())) {
                clienteVO.setCategoria(UteisImportacao.obterCategoria(alunoSesiCeJSON.getCategoria(), con));
            }

            clienteVO.getPessoa().realizarUpperCaseDados();
            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                incluirClienteImportacao(clienteVO);
            } else {
                alterPessoaSimplificado(clienteVO.getPessoa());
                alterClienteSimplificado(clienteVO);
            }

            Colaborador colaboradorDAO = new Colaborador(con);
            ColaboradorVO colaboradorPactoBR = colaboradorDAO.consultarPorNomeColaborador("PACTO", codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            colaboradorDAO = null;

            List<VinculoVO> vinculoVOS = new ArrayList<>();
            boolean possuiVinculoConsultor = clienteVO.getVinculoVOs().stream().filter(v -> v.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())).findFirst().isPresent();
            if (!possuiVinculoConsultor) {
                VinculoVO vinculoConsultor = new VinculoVO();
                vinculoConsultor.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
                vinculoConsultor.setColaborador(colaboradorPactoBR);
                vinculoVOS.add(vinculoConsultor);
            }
            boolean possuiVinculoProfessorTW = clienteVO.getVinculoVOs().stream().filter(v -> v.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())).findFirst().isPresent();
            if (!possuiVinculoProfessorTW) {
                VinculoVO vinculoProfessor = new VinculoVO();
                vinculoProfessor.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
                vinculoProfessor.setColaborador(colaboradorPactoBR);
                vinculoVOS.add(vinculoProfessor);
            }
            if (!vinculoVOS.isEmpty()) {
                clienteVO.getVinculoVOs().addAll(vinculoVOS);
                incluirVinculo(clienteVO, clienteVO.getVinculoVOs());
            }

            if (!UteisValidacao.emptyNumber(clienteVO.getMatriculaExterna())
                    && clienteVO.getCodigoMatricula().intValue() != clienteVO.getMatriculaExterna().intValue()) {
                AplicarMatriculaImportacao.processar(con, clienteVO.getCodigo());
            }

            con.commit();

        } catch (Exception ex) {
            if (!con.getAutoCommit()) {
                con.rollback();
            }
            String msg = String.format("Falha ao processar cadastro do aluno. Matricula: %d - ERRO: %s", alunoSesiCeJSON.getIdMatricula(), ex.getMessage() != null ? ex.getMessage() : ex.toString());
            System.out.println(msg);
            ex.printStackTrace();
            throw new Exception(msg);
        } finally {
            con.setAutoCommit(true);
        }
        return clienteVO;
    }

    private void alterClienteSimplificado(ClienteVO clienteVO) throws SQLException {
        String sql = "UPDATE cliente SET matriculaexterna = ?, necessidadesEspeciaisSesiCe = ?, dataValidadeCadastroSesiCe = ?, razaoSocialEmpresaSesiCe = ?, statusMatriculaSesiCe = ? \n" +
                "WHERE codigo = ?";
        PreparedStatement pstm = con.prepareStatement(sql);
        int i = 0;
        if (UteisValidacao.emptyNumber(clienteVO.getMatriculaExterna())) {
            pstm.setNull(++i, 0);
        } else {
            pstm.setInt(++i, clienteVO.getMatriculaExterna().intValue());
        }
        pstm.setString(++i, clienteVO.getNecessidadesEspeciaisSesiCe());
        pstm.setDate(++i, Uteis.getDataJDBC(clienteVO.getDataValidadeCadastroSesiCe()));
        pstm.setString(++i, clienteVO.getRazaoSocialEmpresaSesiCe());
        pstm.setString(++i, clienteVO.getStatusMatriculaSesiCe());
        pstm.setInt(++i, clienteVO.getCodigo());
        pstm.execute();
    }

    private void alterPessoaSimplificado(PessoaVO obj) throws Exception {
        String sql = "UPDATE pessoa SET nome = ?, cfp = ?, dataNasc = ?, sexo = ?, nomeMae = ?, cpfMae = ? WHERE codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        int i = 0;
        pst.setString(++i, obj.getNome());
        pst.setString(++i, obj.getCfp());
        pst.setDate(++i, Uteis.getDataJDBC(obj.getDataNasc()));
        pst.setString(++i, obj.getSexo());
        pst.setString(++i, obj.getNomeMae());
        pst.setString(++i, obj.getCpfMae());
        pst.setInt(++i, obj.getCodigo());
        pst.execute();

        Telefone telefoneDAO = new Telefone(con);
        for (TelefoneVO telVO: obj.getTelefoneVOs()) {
            if (!UteisValidacao.emptyNumber(telVO.getCodigo())) {
                continue;
            }
            List<TelefoneVO> telefoneVOS = telefoneDAO.consultarTelefoneExiste(telVO.getNumero(), obj.getCodigo(), null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(telefoneVOS)) {
                telVO.setPessoa(obj.getCodigo());
                telefoneDAO.incluir(telVO);
            }
        }
        telefoneDAO = null;

        Email emailDAO = new Email(con);
        for (EmailVO emailVO: obj.getEmailVOs()) {
            if (!UteisValidacao.emptyNumber(emailVO.getCodigo())) {
                continue;
            }
            if (!emailDAO.consultarEmailExiste(emailVO.getEmail(), obj.getCodigo())) {
                emailVO.setPessoa(obj.getCodigo());
                emailDAO.incluir(emailVO);
            }
        }
        emailDAO = null;
    }

    private void gravarLogIntegracao(String servico, String dados, String resultado) throws Exception {
        LogIntegracoes logDAO = new LogIntegracoes(con);
        LogIntegracoesVO logVO = new LogIntegracoesVO();
        logVO.setServico(servico);
        logVO.setDadosRecebidos(dados);
        logVO.setResultado(resultado);
        logVO.setDataLancamento(new Date());
        logDAO.incluir(logVO);
        logDAO = null;
    }

    private ClienteVO obterClientePorMatriculaExternaOuCpf(Integer matriculaExterna, String cpf, Integer codigoEmpresa) throws Exception {
        Cliente clienteDAO = new Cliente(con);
        ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(matriculaExterna, codigoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);

        if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyString(cpf)) {
            List<ClienteVO> clientesPorCpf = clienteDAO.consultarPorCPF(cpf, codigoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
            if (!UteisValidacao.emptyList(clientesPorCpf)) {
                if (clientesPorCpf.size() > 1) {
                    throw new Exception(String.format("Mais de um cadastro foi encontrado para esse aluno. Mat: %s - Cpf: %s", matriculaExterna, cpf));
                }
                clienteVO = clientesPorCpf.get(0);
            }
        }

        return clienteVO;
    }


    public void processarContratoAlunoSesiCe(ClienteVO clienteVO, AlunoSesiCeJSON alunoSesiCeJSON, Integer codigoEmpresa) throws Exception {
        try {
            Empresa empresaDAO = new Empresa(con);
            Usuario usuarioDAO = new Usuario(con);
            Contrato contratoDAO = new Contrato(con);
            ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
            Plano planoDAO = new Plano(con);
            Turma turmaDAO = new Turma(con);
            HorarioTurma horarioTurmaDAO = new HorarioTurma(con);
            Modalidade modalidadeDAO = new Modalidade(con);

            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ContratoVO contratoVO = contratoDAO.consultarPorIdExterno(alunoSesiCeJSON.getIdMatricula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);
            if (contratoVO != null) {
                return;
            }

            PlanoVO planoVO = planoDAO.consultarPorDescricao("PLANO IMPORTACAO", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Map<Integer, PlanoModalidadeVezesSemanaVO> modalidades = new HashMap<>();

            List<TurmaVO> turmasHorariosSelecionados = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(alunoSesiCeJSON.getIdTurma())) {
                TurmaVO turmaVO = turmaDAO.consultarPorIdexterno(alunoSesiCeJSON.getIdTurma().toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (turmaVO != null) {
                    turmaVO.setHorarioTurmaVOs(horarioTurmaDAO.consultarPorTurma(turmaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (!UteisValidacao.emptyList(turmaVO.getHorarioTurmaVOs())) {
                        turmasHorariosSelecionados.add(turmaVO);
                        modalidades.put(turmaVO.getModalidade().getCodigo(), validarModalidadeNoPlano(turmaVO.getModalidade(), turmaVO.getHorarioTurmaVOs().size(), planoVO.getCodigo()));
                    }
                }
            }

            if (modalidades.isEmpty()) {
                ModalidadeVO modalidadeVO = UteisImportacao.obterModalidadeImportacao(codigoEmpresa, this.con);
                modalidades.put(modalidadeVO.getCodigo(), validarModalidadeNoPlano(modalidadeVO, modalidadeVO.getNrVezes(), planoVO.getCodigo()));
            }
            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ContratoControle contratoControle = contratoDAO.obterContratoControle(planoVO.getCodigo(), clienteVO.getCodigo(), false, codigoEmpresa, new ContratoVO(), new ContratoVO());
            if (contratoControle == null) {
                throw new Exception("Falha ao obter contrato controle.");
            }

            contratoControle.getContratoVO().setImportacao(true);

            processarModalidadesSelecionadas(contratoControle, modalidades);

            if (!UteisValidacao.emptyList(turmasHorariosSelecionados)) {
                processarTurmasModalidades(contratoControle, turmasHorariosSelecionados);
            }

            Date dataLancamento = new Date();
            Date dataInicioContrato = Calendario.getDate("yyyy-MM-dd", alunoSesiCeJSON.getDataInicioContrato());
            Date dataFimContrato = Calendario.getDate("yyyy-MM-dd", alunoSesiCeJSON.getDataFimContrato());

            Long dias = Uteis.nrDiasEntreDatas(dataInicioContrato, dataFimContrato);
            Integer duracao = dias < 30 ? 1 : new Long(dias / 30).intValue();

            processarHorario(contratoControle, planoVO.getCodigo(), !UteisValidacao.emptyList(turmasHorariosSelecionados));
            configurarDuracaoCondicaoPagamento(contratoControle, planoVO.getCodigo(), duracao, 1);
            contratoControle.getContratoVO().setVigenciaDe(dataInicioContrato);
            contratoControle.getContratoVO().setVigenciaAte(dataFimContrato);
            contratoControle.getContratoVO().setVigenciaAteAjustada(dataFimContrato);
            contratoControle.setDataInicioContrato(dataInicioContrato);
            contratoControle.getContratoVO().setValorBaseCalculo(0.0);
            contratoControle.getContratoVO().setValorFinal(0.0);

            // ZERAR OS VALORES DOS PRODUTOS DOS PLANOS
            validarSituacaoContrato(contratoControle.getContratoVO(), contratoDAO);
            for (Object obj : contratoControle.getContratoVO().getPlano().getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO) obj;
                planoProdutoSugeridoVO.setValorProduto(0.0);
                planoProdutoSugeridoVO.getProduto().setValorFinal(0.0);
            }

            if (!UteisValidacao.emptyNumber(contratoControle.getContratoVO().getContratoBaseadoRenovacao())) {
                ContratoVO contratoASerRenovado = contratoDAO.consultarPorCodigo(contratoControle.getContratoVO().getContratoBaseadoRenovacao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoControle.setVigenciaAteDoUltimoContrato((Date) contratoASerRenovado.getVigenciaAte().clone());
                contratoControle.setVigenciaAteAjustadaDoUltimoContrato((Date) contratoASerRenovado.getVigenciaAteAjustada().clone());
                contratoControle.setVigenciaDeDoUltimoContrato((Date) contratoASerRenovado.getVigenciaDe().clone());
                contratoControle.getContratoVO().setSituacaoContrato(SituacaoContratoEnum.RENOVACAO.getCodigo());
                contratoControle.getContratoVO().setRenovarContrato(true);
                contratoControle.getContratoVO().setContratoOrigemRenovacao(contratoASerRenovado);
                contratoControle.getContratoVO().setDataMatricula(contratoASerRenovado.getDataMatricula());
            }

            String fecharNegociacao = contratoControle.fecharNegociacao(null, true);
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            contratoControle.getContratoVO().setConsultor(usuarioVO.getColaboradorVO());
            contratoControle.getContratoVO().setObservacao("Contrato gerado automaticamente pelo sistema de integração com o SESI-CE.");
            contratoControle.getContratoVO().setVigenciaAteAjustada(dataFimContrato);
            contratoControle.getContratoVO().setDataMatricula(dataLancamento);
            contratoControle.getContratoVO().setDataLancamento(dataLancamento);
            contratoControle.getContratoVO().setDataPrimeiraParcela(dataInicioContrato);

            contratoControle.getContratoVO().getContratoTextoPadrao().setPlanoTextoPadrao(contratoControle.getContratoVO().getPlano().getPlanoTextoPadrao());

            String gravarContrato = contratoControle.gravar(usuarioVO, false, null, false, false, true);

            if (!UteisValidacao.emptyNumber(contratoControle.getContratoVO().getCodigo())) {
                contratoDAO.alterarXnumpro(contratoControle.getContratoVO().getCodigo(), alunoSesiCeJSON.getXnumpro());
                contratoDAO.alterarIdExterno(contratoControle.getContratoVO().getCodigo(), alunoSesiCeJSON.getIdMatricula());
            }

            if (gravarContrato.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }

            List<ContratoModalidadeVO> contratoModalidadeVOS = contratoModalidadeDAO.consultarContratoModalidades(contratoControle.getContratoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(turmasHorariosSelecionados)
                    && !UteisValidacao.emptyNumber(alunoSesiCeJSON.getIdModalidade())
                    && contratoModalidadeVOS.size() == 1) {
                ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorIdExterno(alunoSesiCeJSON.getIdModalidade().toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (modalidadeVO != null) {
                    String sql = "update contratomodalidade set modalidade = ? where codigo = ?";
                    PreparedStatement pstm = this.con.prepareStatement(sql);
                    pstm.setInt(1, modalidadeVO.getCodigo());
                    pstm.setInt(2, contratoModalidadeVOS.get(0).getCodigo());
                    pstm.execute();
                } else {
                    String servico = String.format("Integração SESI-CE | Alunos Matriculas | Unid. Externa: %s", empresaVO.getCodExternoUnidadeSesi());
                    String msg = String.format("Nenhuma modalidade com idexterno: %s não foi encontrada!", alunoSesiCeJSON.getIdModalidade());
                    gravarLogIntegracao(servico, alunoSesiCeJSON.getDadosJson().toString(), msg);
                }
            }

            usuarioDAO = null;
            contratoDAO = null;
            planoDAO = null;
            turmaDAO = null;

        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(String.format("Erro ao processar contrato id externo: %s ERRO: %s", alunoSesiCeJSON.getIdMatricula(), e.getMessage() != null ? e.getMessage() : e.toString()));
        }
    }

    public void atualizarSinteticoCliente(ClienteVO clienteVO) throws Exception {
        try {
            ZillyonWebFacade zwFacadeDAO = new ZillyonWebFacade(con);
            zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            zwFacadeDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(String.format("Erro ao atualizar sintético do cliente. ERRO: %s", e.getMessage()));
        }
    }
}
