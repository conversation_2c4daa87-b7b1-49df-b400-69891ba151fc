package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ClienteObservacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ClienteObservacaoInterfaceFacade;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:10
 * To change this template use File | Settings | File Templates.
 */
public class ClienteObservacao extends SuperEntidade implements ClienteObservacaoInterfaceFacade {

    public ClienteObservacao() throws Exception {
        super();
    }

    public ClienteObservacao(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(ClienteObservacaoVO obj) throws Exception {
        incluir(getIdEntidade());
        String sql = "INSERT INTO clienteobservacao (observacao, cliente, usuarioresponsavel, datacadastro, importante) VALUES (?, ?, ?, ?, ?);";
        try (PreparedStatement sqlInserir = getCon().prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setString(++i, obj.getObservacao());
            sqlInserir.setInt(++i, obj.getClienteVO().getCodigo());
            sqlInserir.setInt(++i, obj.getUsuarioVO().getCodigo());
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlInserir.setBoolean(++i, obj.isImportante());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ClienteObservacaoVO obj) throws Exception {
        alterar(getIdEntidade());
        String sql = "UPDATE clienteobservacao\n" +
                "SET cliente = ?, usuarioresponsavel = ?, observacao = ?, datacadastro = ?, importante = ?, dataAlteracao = ? \n" +
                "WHERE codigo = ?;";
        try (PreparedStatement sqlAlterar = getCon().prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setInt(++i, obj.getClienteVO().getCodigo());
            sqlAlterar.setInt(++i, obj.getUsuarioVO().getCodigo());
            sqlAlterar.setString(++i, obj.getObservacao());
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
            sqlAlterar.setBoolean(++i, obj.isImportante());
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public void excluir(ClienteObservacaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM clienteobservacao WHERE codigo = ?;";
        try (PreparedStatement sqlExcluir = getCon().prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    @Override
    public List<ClienteObservacaoVO> consultar(Integer codCliente, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM clienteobservacao WHERE cliente = " + codCliente + " ORDER BY codigo;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    private List<ClienteObservacaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ClienteObservacaoVO> vetResultado = new ArrayList<ClienteObservacaoVO>();
        while (tabelaResultado.next()) {
            ClienteObservacaoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private ClienteObservacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ClienteObservacaoVO obj = new ClienteObservacaoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        obj.setDataCadastro(dadosSQL.getTimestamp("datacadastro"));
        obj.setNovoObj(false);

        try {
            obj.setImportante(dadosSQL.getBoolean("importante"));
            obj.setDataAlteracao(dadosSQL.getTimestamp("dataAlteracao"));
        } catch (Exception ignored) {
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setUsuarioVO(getFacade().getUsuario().consultarPorCodigoUsuario(dadosSQL.getInt("usuarioresponsavel"), false, Uteis.NIVELMONTARDADOS_MINIMOS));
        }
        
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            obj.setUsuarioVO(getFacade().getUsuario().consultarPorCodigoUsuario(dadosSQL.getInt("usuarioresponsavel"), false, Uteis.NIVELMONTARDADOS_MINIMOS));
            obj.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(dadosSQL.getInt("cliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        return obj;
    }

    public String consultarJSON(String codCliente) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(codCliente).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append((rs.getString("nome"))).append("\",");
                json.append("\"").append(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("datacadastro"), "dd/MM/yyyy HH:mm:ss")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("observacao"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(String codCliente) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT\n" +
                "  co.codigo,\n" +
                "  usu.nome,\n" +
                "  co.datacadastro,\n" +
                "  co.observacao\n" +
                "FROM clienteobservacao co\n" +
                "  INNER JOIN usuario usu\n" +
                "    ON co.usuarioresponsavel = usu.codigo\n");
        if (codCliente != null && !codCliente.equals("0") && !UteisValidacao.emptyString(codCliente)) {
            sql.append("WHERE co.cliente = ").append(codCliente).append("\n");
        }
        sql.append("ORDER BY co.codigo DESC");

        return con.prepareStatement(sql.toString());
    }

    @Override
    public ClienteObservacaoVO consultarChavePrimaria(Integer codigo,Integer nivelMontarDados) throws Exception {
        ClienteObservacaoVO clienteObservacao = new ClienteObservacaoVO();
        String sqlStr = "SELECT * FROM clienteobservacao WHERE codigo = " + codigo + ";";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    clienteObservacao = montarDados(tabelaResultado, nivelMontarDados);
                }
            }
        }
        return clienteObservacao;
    }
    
    @Override
    public Integer consultarQuantidadeListaObservacao(Integer codCliente) throws Exception {
        String sqlStr = "SELECT count(codigo) as quantidade  from clienteobservacao where cliente = " + codCliente + ";";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getInt("quantidade");
            }
        }
    }

    @Override
    public List<ClienteObservacaoVO> consultarObservacaoPaginado(Integer codCliente, int nivelMontarDados, Integer limit, Integer offset) throws Exception {
        String sqlStr = "SELECT * FROM clienteobservacao WHERE cliente = " + codCliente + " ORDER BY codigo DESC limit "+limit+" offset "+offset+";";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }
}
