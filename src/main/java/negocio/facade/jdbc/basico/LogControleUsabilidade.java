package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.util.Date;

import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.interfaces.basico.*;
import negocio.comuns.basico.LogControleUsabilidadeVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.*;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>LogControleUsabilidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>LogControleUsabilidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see LogControleUsabilidadeVO
 * @see SuperEntidade
 */
public class LogControleUsabilidade extends SuperEntidade implements LogControleUsabilidadeInterfaceFacade {

    protected static String idEntidade;

    public LogControleUsabilidade() throws Exception {
        super();
    }

    public LogControleUsabilidade(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>LogControleUsabilidadeVO</code>.
     */
    public LogControleUsabilidadeVO novo() throws Exception {
        return new LogControleUsabilidadeVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>LogControleUsabilidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>LogControleUsabilidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(LogControleUsabilidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            LogControleUsabilidadeVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO LogControleUsabilidade(entidade, maquina, acao, dataRegistro, usuario, empresa, chavePrimaria, useroamd) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 1;
            sqlInserir.setString(i++, obj.getEntidade());
            sqlInserir.setString(i++, obj.getMaquina());
            sqlInserir.setString(i++, obj.getAcao());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setInt(i++, obj.getUsuario().getCodigo().intValue());
            sqlInserir = resolveFKNull(sqlInserir, i++, obj.getEmpresa().getCodigo().intValue());
            sqlInserir.setInt(i++, obj.getCodigo().intValue());
            sqlInserir.setString(i++, obj.getUserOamd());
            sqlInserir.execute();
            obj.setCodigo(this.obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /*
     * 11/03/11 Ulisses... Incluir log sem precisar validar os dados obrigatórios,
     *                    e não precisa verificar se o usuário tem permissão para incluir.
     *    Obs.: Este método foi necessário para o WebService de Validação de Acesso.
     */
    public void incluirLog(LogControleUsabilidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO LogControleUsabilidade(entidade, maquina, acao, dataRegistro, empresa, chavePrimaria, useroamd ) VALUES ( ?, ?, ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 1;
            sqlInserir.setString(i++, obj.getEntidade());
            sqlInserir.setString(i++, obj.getMaquina());
            sqlInserir.setString(i++, obj.getAcao());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            resolveFKNull(sqlInserir, i++, obj.getEmpresa().getCodigo().intValue());
            sqlInserir.setInt(i++, obj.getCodigo().intValue());
            sqlInserir.setString(i++, obj.getUserOamd());
            sqlInserir.execute();
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>LogControleUsabilidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>LogControleUsabilidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(LogControleUsabilidadeVO obj) throws Exception {
        if (!obj.getUsuario().isPseudo()) {
            LogControleUsabilidadeVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO LogControleUsabilidade(entidade, maquina, acao, dataRegistro, usuario, empresa, chavePrimaria, useroamd ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 1;
            sqlInserir.setString(i++, obj.getEntidade());
            sqlInserir.setString(i++, obj.getMaquina());
            sqlInserir.setString(i++, obj.getAcao());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setInt(i++, obj.getUsuario().getCodigo().intValue());
            sqlInserir = resolveFKNull(sqlInserir, i++, obj.getEmpresa().getCodigo().intValue());
            sqlInserir.setInt(i++, obj.getCodigo().intValue());
            sqlInserir.setString(i++, obj.getUserOamd());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        }

    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>LogControleUsabilidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>LogControleUsabilidadeVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(LogControleUsabilidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            LogControleUsabilidadeVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "UPDATE LogControleUsabilidade set entidade=?, maquina=?, acao=?, dataRegistro=?, usuario=?, empresa=?, chavePrimaria=?, useroamd=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 1;
            sqlAlterar.setString(i++, obj.getEntidade());
            sqlAlterar.setString(i++, obj.getMaquina());
            sqlAlterar.setString(i++, obj.getAcao());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlAlterar.setInt(i++, obj.getUsuario().getCodigo().intValue());
            sqlAlterar = resolveFKNull(sqlAlterar, i++, obj.getEmpresa().getCodigo().intValue());
            sqlAlterar.setInt(i++, obj.getCodigo().intValue());
            sqlAlterar.setString(i++, obj.getUserOamd());
            sqlAlterar.setInt(i++, obj.getChavePrimaria().intValue());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>LogControleUsabilidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>LogControleUsabilidadeVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(LogControleUsabilidadeVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM LogControleUsabilidade WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>Integer empresa</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorEmpresa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE empresa >= " + valorConsulta.intValue() + " ORDER BY empresa";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>Integer usuario</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorUsuario(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE usuario >= " + valorConsulta.intValue() + " ORDER BY usuario";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>Date dataRegistro</code>. Retorna os objetos com valores pertecentes ao período informado por parâmetro.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE ((dataRegistro >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataRegistro <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataRegistro";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>String acao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorAcao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE upper( acao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY acao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>String maquina</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorMaquina(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE upper( maquina ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY maquina";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>String entidade</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorEntidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE upper( entidade ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY entidade";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>LogControleUsabilidade</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorChavePrimaria(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM LogControleUsabilidade WHERE chaveprimaria >= " + valorConsulta.intValue() + " ORDER BY chaveprimaria";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorIntervaloDatasUsuario10UltimosAcessos(Integer usuario, String dataInicio, String dataTermino, String acao, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        StringBuffer sql = new StringBuffer();
        if (acao.equals("TO")) {
            sql.append("SELECT * FROM logcontroleusabilidade WHERE usuario = ");
            sql.append(usuario);
            sql.append(" and dataRegistro ");
            sql.append(" between '");
            sql.append(dataInicio + " 00:00:00.000' and '");
            sql.append(dataTermino + " 23:59:59.000' ");
            sql.append("and (entidade like 'LOGOUT' or entidade like 'LOGADO') ");
            sql.append("ORDER BY dataRegistro desc");
        } else if (acao.equals("LA")) {
            sql.append("SELECT * FROM logcontroleusabilidade WHERE usuario = ");
            sql.append(usuario);
            sql.append(" and dataRegistro ");
            sql.append(" between '");
            sql.append(dataInicio + " 00:00:00.000' and '");
            sql.append(dataTermino + " 23:59:59.000' ");
            sql.append("and acao like 'LOGADO' ");
            sql.append("ORDER BY dataRegistro desc");
        } else if (acao.equals("LO")) {
            sql.append("SELECT * FROM logcontroleusabilidade WHERE usuario = ");
            sql.append(usuario);
            sql.append(" and dataRegistro ");
            sql.append(" between '");
            sql.append(dataInicio + " 00:00:00.000' and '");
            sql.append(dataTermino + " 23:59:59.000' ");
            sql.append("and acao like 'LOGOUT'");
            sql.append("ORDER BY dataRegistro desc");
        }
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>LogControleUsabilidadeVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            LogControleUsabilidadeVO obj = new LogControleUsabilidadeVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>LogControleUsabilidadeVO</code>.
     * @return  O objeto da classe <code>LogControleUsabilidadeVO</code> com os dados devidamente montados.
     */
    public static LogControleUsabilidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        LogControleUsabilidadeVO obj = new LogControleUsabilidadeVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setEntidade(dadosSQL.getString("entidade"));
        obj.setMaquina(dadosSQL.getString("maquina"));
        obj.setAcao(dadosSQL.getString("acao"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.getUsuario().setCodigo(new Integer(dadosSQL.getInt("usuario")));
        obj.setUserOamd(dadosSQL.getString("useroamd"));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.setChavePrimaria(dadosSQL.getInt("chavePrimaria"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosEmpresa(obj, nivelMontarDados);
            montarDadosUsuario(obj, nivelMontarDados);
        }
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>UsuarioVO</code> relacionado ao objeto <code>LogControleUsabilidadeVO</code>. Faz
     * uso da chave primária da classe <code>UsuarioVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosUsuario(LogControleUsabilidadeVO obj, int nivelMontarDados) throws Exception {
        if (obj.getUsuario().getCodigo().intValue() == 0) {
            obj.setUsuario(new UsuarioVO());
            return;
        }
        obj.setUsuario(getFacade().getUsuario().consultarPorChavePrimaria(obj.getUsuario().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>EmpresaVO</code> relacionado ao objeto <code>LogControleUsabilidadeVO</code>. Faz
     * uso da chave primária da classe <code>EmpresaVO</code> para realizar a
     * consulta.
     *
     * @param obj
     *            Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosEmpresa(LogControleUsabilidadeVO obj, int nivelMontarDados) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        obj.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>LogControleUsabilidadeVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public LogControleUsabilidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM LogControleUsabilidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( LogControleUsabilidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List consultarAcessosRelatorioPorPeriodo(Integer usuario, Integer colaborador, String dataInicio, String dataTermino, String acao) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT log.codigo, log.dataRegistro, log.usuario, em.nome as empresa,\n");
        sql.append("pes.dataCadastro, pes.nome, pes.dataNasc, pes.tipoPessoa,\n");
        sql.append("pes.cfp, pes.rg, pes.rgOrgao, pes.rgUf,\n");
        sql.append("cid.nome as cidade, est.descricao as estado, pais.nome as pais,\n");
        sql.append("col.codigo as colaborador,\n");
        sql.append("ende.ceps, ende.logradouros, ende.numeros, ende.complementos, ende.bairros, email.emails, tel.telefones\n");
        sql.append("FROM logcontroleusabilidade log\n");
        sql.append("INNER JOIN colaborador col ON col.codigo = ").append(colaborador).append("\n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = col.pessoa\n");
        sql.append("LEFT JOIN empresa em ON em.codigo = log.empresa\n");
        sql.append("LEFT JOIN cidade cid ON cid.codigo = pes.cidade\n");
        sql.append("LEFT JOIN estado est ON est.codigo = cid.estado\n");
        sql.append("LEFT JOIN pais ON pais.codigo = est.pais\n");
        sql.append("LEFT JOIN (\n");
        sql.append("  SELECT pessoa, STRING_AGG(cep, ' | '  ORDER BY codigo DESC) as ceps,\n");
        sql.append("  STRING_AGG(endereco, ' | ' ORDER BY codigo DESC) as logradouros,\n");
        sql.append("  STRING_AGG(numero, ' | ' ORDER BY codigo DESC) as numeros,\n");
        sql.append("  STRING_AGG(complemento, ' | ' ORDER BY codigo DESC) as complementos,\n");
        sql.append("  STRING_AGG(bairro, ' | ' ORDER BY codigo DESC) as bairros\n");
        sql.append("  FROM endereco\n");
        sql.append("  GROUP BY pessoa\n");
        sql.append("  ORDER BY pessoa\n");
        sql.append(") ende ON ende.pessoa = pes.codigo\n");
        sql.append("LEFT JOIN (\n");
        sql.append("  SELECT pessoa, STRING_AGG(email, ' | ' ORDER BY codigo DESC) as emails\n");
        sql.append("  FROM email\n");
        sql.append("  GROUP BY pessoa\n");
        sql.append("  ORDER BY pessoa\n");
        sql.append(") email ON email.pessoa = pes.codigo\n");
        sql.append("LEFT JOIN (\n");
        sql.append("  SELECT pessoa, STRING_AGG(numero, ' | ' ORDER BY codigo DESC) as telefones\n");
        sql.append("  FROM telefone\n");
        sql.append("  GROUP BY pessoa\n");
        sql.append("  ORDER BY pessoa\n");
        sql.append(") tel ON tel.pessoa = pes.codigo\n");
        sql.append("WHERE log.usuario = ").append(usuario).append("\n");
        sql.append("and log.dataRegistro  between '").append(dataInicio + " 00:00:00.000' and '").append(dataTermino + " 23:59:59.000' ");
        sql.append("and acao in (").append(acao.equals("LA") ? "'LOGADO'" : acao.equals("LO") ? "'LOGOUT'" : "'LOGOUT','LOGADO'").append(")\n");
        sql.append("ORDER BY log.dataRegistro desc");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());

        List lista = new ArrayList();
        while (rs.next()) {
            ItemRelatorioTO obj = new ItemRelatorioTO();
            obj.setUltimoAcesso(new Integer(rs.getInt("codigo")));
            obj.setDataUltimoAcesso(rs.getTimestamp("dataRegistro"));
            obj.setCodUsuario(rs.getInt("usuario"));
            obj.setNomeEmpresa(rs.getString("empresa"));
            obj.setNome(rs.getString("nome"));
            obj.setDataCadastro(rs.getTimestamp("dataCadastro"));
            obj.setDataNascimento(rs.getTimestamp("dataNasc"));
            obj.setCategoria(TipoPessoa.obterPorCodigo(rs.getInt("tipoPessoa")) != null ? TipoPessoa.obterPorCodigo(rs.getInt("tipoPessoa")).getLabel() : "");
            obj.setCpf(rs.getString("cfp"));
            obj.setRg(rs.getString("rg"));
            obj.setRgOrgao(rs.getString("rgOrgao"));
            obj.setRgUf(rs.getString("rgUf"));
            obj.setCidade(rs.getString("cidade"));
            obj.setEstado(rs.getString("estado"));
            obj.setPais(rs.getString("pais"));
            obj.setCodColaborador(rs.getInt("colaborador"));
            obj.setCep(rs.getString("ceps"));
            obj.setLogradouro(rs.getString("logradouros"));
            obj.setNumero(rs.getString("numeros"));
            obj.setComplemento(rs.getString("complementos"));
            obj.setBairro(rs.getString("bairros"));
            obj.setEmail(rs.getString("emails"));
            obj.setTelefone(rs.getString("telefones"));
            lista.add(obj);
        }
        return lista;
    }
}
