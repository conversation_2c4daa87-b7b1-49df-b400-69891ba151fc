package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.Iterator;

import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.PessoaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.basico.EnderecoVO;
import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>EnderecoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>EnderecoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see EnderecoVO
 * @see SuperEntidade
 * @see Pessoa
 */
public class Endereco extends SuperEntidade {    

    public Endereco() throws Exception {
        super();        
    }

    public Endereco(Connection conexao) throws Exception {
        super(conexao);
    }


    /**
     * Operação responsável por retornar um novo objeto da classe <code>EnderecoVO</code>.
     */
    public EnderecoVO novo() throws Exception {
        incluir(getIdEntidade());
        EnderecoVO obj = new EnderecoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>EnderecoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>EnderecoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(EnderecoVO obj) throws Exception {
        incluir(obj, true);
    }
    public void incluir(EnderecoVO obj, boolean validarPermissao) throws Exception {
        EnderecoVO.validarDados(obj);
        if(validarPermissao) {
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Endereco( endereco, complemento, numero, bairro, cep, tipoEndereco, pessoa,enderecoCorrespondencia, enfileirado, dataatualizacao ) VALUES ( ?,?, ?, ?, ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getEndereco());
        sqlInserir.setString(2, obj.getComplemento());
        sqlInserir.setString(3, obj.getNumero());
        sqlInserir.setString(4, obj.getBairro());
        sqlInserir.setString(5, obj.getCep());
        sqlInserir.setString(6, obj.getTipoEndereco());
        if (obj.getPessoa().intValue() != 0) {
            sqlInserir.setInt(7, obj.getPessoa().intValue());
        } else {
            sqlInserir.setNull(7, 0);
        }
        sqlInserir.setBoolean(8, obj.getEnderecoCorrespondencia());
        sqlInserir.setBoolean(9, false);
        Date data = new Date();
        sqlInserir.setTimestamp(10, Uteis.getDataJDBCTimestamp(data));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>EnderecoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>EnderecoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(EnderecoVO obj) throws Exception {
        EnderecoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Endereco set endereco=?, complemento=?, numero=?, bairro=?, cep=?, tipoEndereco=?, pessoa=?, enderecoCorrespondencia=?, enfileirado=?, dataatualizacao=?  WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getEndereco());
        sqlAlterar.setString(2, obj.getComplemento());
        sqlAlterar.setString(3, obj.getNumero());
        sqlAlterar.setString(4, obj.getBairro());
        sqlAlterar.setString(5, obj.getCep());
        sqlAlterar.setString(6, obj.getTipoEndereco());
        if (obj.getPessoa() != 0) {
            sqlAlterar.setInt(7, obj.getPessoa());
        } else {
            sqlAlterar.setNull(7, 0);
        }
        sqlAlterar.setBoolean(8, obj.getEnderecoCorrespondencia());
        sqlAlterar.setBoolean(9, false);
        Date data = new Date();
        sqlAlterar.setTimestamp(10, Uteis.getDataJDBCTimestamp(data));
        sqlAlterar.setInt(11, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>EnderecoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>EnderecoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(EnderecoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Endereco WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    public EnderecoVO consultar(Integer codigoPessoa,TipoEnderecoEnum tipoEnderecoEnum, int nivelMontarDados) throws Exception {
        String sql = "select * from endereco where pessoa = ? and tipoEndereco = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoPessoa);
        pst.setString(2, tipoEnderecoEnum.getCodigo());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDados(rs,nivelMontarDados);
        }
        return null;
    }

    /**
     * Responsável por realizar uma consulta de <code>Endereco</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Pessoa</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>EnderecoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<EnderecoVO> consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Endereco.* FROM Endereco, Pessoa WHERE Endereco.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>Endereco</code> através do valor do atributo 
     * <code>String tipoEndereco</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EnderecoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<EnderecoVO> consultarPorTipoEndereco(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Endereco WHERE upper( tipoEndereco ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tipoEndereco";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>Endereco</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>EnderecoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<EnderecoVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Endereco WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>EnderecoVO</code> resultantes da consulta.
     */
    public static List<EnderecoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<EnderecoVO> vetResultado = new ArrayList<EnderecoVO>();
        while (tabelaResultado.next()) {
            EnderecoVO obj = new EnderecoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static EnderecoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        EnderecoVO obj = new EnderecoVO();
        obj.setNovoObj(new Boolean(false));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setCep(dadosSQL.getString("cep"));
        obj.setEndereco(dadosSQL.getString("endereco"));
        obj.setComplemento(dadosSQL.getString("complemento"));
        obj.setBairro(dadosSQL.getString("bairro"));
        obj.setNumero(dadosSQL.getString("numero"));
        obj.setTipoEndereco(dadosSQL.getString("tipoEndereco"));
        obj.setPessoa(new Integer(dadosSQL.getInt("pessoa")));
        obj.setEnderecoCorrespondencia(new Boolean(dadosSQL.getBoolean("enderecoCorrespondencia")));
        try {
            obj.setLtdlng(dadosSQL.getString("ltdlng"));
        }catch (Exception e){
            //ignore
        }
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>EnderecoVO</code>.
     * @return  O objeto da classe <code>EnderecoVO</code> com os dados devidamente montados.
     */
    public static EnderecoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        EnderecoVO obj = montarDadosBasico(dadosSQL);
        obj.registrarObjetoVOAntesDaAlteracao();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>EnderecoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Endereco</code>.
     * @param <code>pessoa</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirEnderecos(Integer pessoa) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Endereco WHERE (pessoa = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>EnderecoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Endereco</code>.
     * @param <code>pessoa</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirEnderecos(Integer pessoa, boolean validarPermissao)  throws Exception {
        if (validarPermissao) {
            excluir(getIdEntidade()); // Faz a validação de permissões
        }
        String sql = "DELETE FROM Endereco WHERE (pessoa = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa.intValue());
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>EnderecoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirEnderecos</code> e <code>incluirEnderecos</code> disponíveis na classe <code>Endereco</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarEnderecos(Integer pessoa, List objetos) throws Exception {
        alterarEnderecos(pessoa, objetos, new PessoaVO());
    }

    public void alterarEnderecos(Integer pessoa, List objetos, PessoaVO pessoaVO) throws Exception {

        String str = "DELETE FROM  Endereco  WHERE pessoa = " + pessoa.intValue();
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            EnderecoVO objeto = (EnderecoVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo().intValue();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            EnderecoVO obj = (EnderecoVO) e.next();
            if (obj.getAdicionarAlterarEndereco() || (pessoaVO != null && !pessoaVO.getFormularioCliente())) {
                obj.setAdicionarAlterarEndereco(false);
                if (obj.getCodigo().equals(new Integer(0))) {
                    obj.setPessoa(pessoa);
                    incluir(obj);
                } else {
                    alterar(obj);
                }
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>EnderecoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>basico.Pessoa</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirEnderecos(Integer pessoaPrm, List objetos) throws Exception {
        incluirEnderecos(pessoaPrm, objetos, true);
    }
    public void incluirEnderecos(Integer pessoaPrm, List objetos, boolean validarPermissao) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            EnderecoVO obj = (EnderecoVO) e.next();
            if(obj.isEmpty()) {
                e.remove();
            }
            else{
                obj.setPessoa(pessoaPrm);
                incluir(obj, validarPermissao);
            }
        }
    }

    /**
     * Operação responsável por consultar todos os <code>EnderecoVO</code> relacionados a um objeto da classe <code>basico.Pessoa</code>.
     * @param pessoa  Atributo de <code>basico.Pessoa</code> a ser utilizado para localizar os objetos da classe <code>EnderecoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>EnderecoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List<EnderecoVO> consultarEnderecos(Integer pessoa, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        List<EnderecoVO> objetos = new ArrayList<EnderecoVO>();
        String sql = "SELECT * FROM Endereco WHERE pessoa = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, pessoa.intValue());
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            EnderecoVO novoObj = new EnderecoVO();
            novoObj = Endereco.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>EnderecoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public EnderecoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Endereco WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Endereco ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }    

    public JSONArray obterEnderecosMapa(Integer empresa) throws  Exception{
        StringBuilder sql =  new StringBuilder();
        sql.append("select p.fotokey, c.situacao, p.nome, e.ltdlng from endereco e ");
        sql.append("INNER JOIN pessoa p on p.codigo = e.pessoa ");
        sql.append("INNER JOIN cliente c on p.codigo = c.pessoa ");
        sql.append("where ltdlng is not null and c.empresa = ").append(empresa);
        JSONArray array = new JSONArray();
        ResultSet rs = criarConsulta(sql.toString(), con);

        while(rs.next()){
            JSONObject j = new JSONObject();
            j.put("situacao", rs.getString("situacao"));
            j.put("nome", rs.getString("nome"));
            j.put("foto", Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
            j.put("posicao", rs.getString("ltdlng"));
            array.put(j);
        }

        return array;
    }

    public List<EnderecoVO> consultarPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
                sqlStr.append("SELECT Endereco.* FROM Endereco WHERE pessoa =").append(valorConsulta);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Retorna a lista de todos os codigos dos enderecos cadastrados na base de dados
     * @return
     * @throws Exception
     */
    public List<Integer> consultarTodosCodigos() throws  Exception{
        String sql = "SELECT codigo FROM Endereco ORDER BY codigo desc";
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while(rs.next()){
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }

    public void atualizarEndereco(Integer codigo, String bairro, String endereco, String cep) throws  Exception{
        String sql = "UPDATE endereco SET bairro = ?, endereco = ?, cep = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, bairro);
        ps.setString(2, endereco);
        ps.setString(3, cep);
        ps.setInt(4, codigo);
        ps.executeUpdate();
    }

    public void atualizarGeolocalizacao(String latitude, String longitude, Integer codigo) throws Exception{
        String update = "update endereco set ltdlng = '"
                +latitude+","+longitude+"' where codigo = "+
                codigo;
        SuperFacadeJDBC.executarConsulta(update, con);

        String updateEnfileirado = "update endereco set enfileirado = true where codigo = "+
                codigo;
        SuperFacadeJDBC.executarConsulta(updateEnfileirado, con);
    }

    public JSONArray enderecosGeolocalizar() throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from endereco where enfileirado = false and dataatualizacao is not null " +
                "order by dataatualizacao asc ", con);
        JSONArray enderecos = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("codigo", rs.getInt("codigo"));
            enderecos.put(obj);
        }
        return enderecos;
    }

    public JSONArray enderecosGeolocalizarAposHabilitarChave() throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from endereco where ltdlng is null and enfileirado = true", con);
        JSONArray enderecos = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("codigo", rs.getInt("codigo"));
            enderecos.put(obj);
        }
        return enderecos;
    }


    public JSONObject obterEndereco(Integer codigo) throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta(" select es.sigla, " +
                "                c.nome as cidade,\n" +
                "                pa.nome as pais,\n" +
                "                e.cep,\n" +
                "                e.dataatualizacao,\n" +
                "                e.bairro,\n" +
                "                e.numero,\n" +
                "                e.complemento,\n" +
                "                e.endereco\n" +
                "                from endereco e\n" +
                "                inner join pessoa p on p.codigo = e.pessoa\n" +
                "                left join cidade c on c.codigo = p.cidade\n" +
                "                left join estado es on es.codigo = p.estado\n" +
                "                left join pais pa on pa.codigo = es.pais\n" +
                "                where e.codigo = " + codigo, con);

        JSONObject json = new JSONObject();
        if(rs.next()){
            json.put("estado", rs.getString("sigla"));
            json.put("cidade", rs.getString("cidade"));
            json.put("pais", rs.getString("pais"));
            json.put("numero", rs.getString("numero"));
            json.put("complemento", rs.getString("complemento"));
            json.put("dataatualizacao", rs.getString("dataatualizacao"));
            json.put("endereco", rs.getString("endereco"));
            json.put("bairro", rs.getString("bairro"));
            String cep = rs.getString("cep");
            if(!cep.contains("-")){
                try {
                    cep = cep.substring(0, 5) + "-" + cep.substring(5, cep.length());
                }catch (Exception e){

                }
            }
            json.put("cep", cep);
        }
        return json;
    }

    public JSONArray obterEnderecoEmpresa() throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta(" select emp.codigo, " +
                "                emp.endereco,\n" +
                "                p.nome as pais,\n" +
                "                c.nomesemacento as cidade,\n" +
                "                e.descricao as estado,\n" +
                "                emp.cep,\n" +
                "                emp.setor,\n" +
                "                emp.numero,\n" +
                "                emp.complemento \n" +
                "                from empresa emp\n" +
                "                left join cidade c on c.codigo = emp.cidade\n" +
                "                left join estado e on e.codigo = emp.estado\n" +
                "                left join pais p on p.codigo = emp.pais", con);

        JSONArray jsonArray = new JSONArray();
        while(rs.next()){
            JSONObject json = new JSONObject();
            json.put("empresa", rs.getString("codigo"));
            json.put("numero", rs.getString("numero"));
            json.put("estado", rs.getString("estado"));
            json.put("endereco", rs.getString("endereco"));
            json.put("cidade", rs.getString("cidade"));
            json.put("pais", rs.getString("pais"));
            json.put("bairro", rs.getString("setor"));
            json.put("complemento", rs.getString("complemento"));
            String cep = rs.getString("cep");
            if(!cep.contains("-")){
                try {
                    cep = cep.substring(0, 5) + "-" + cep.substring(5, cep.length());
                }catch (Exception e){

                }
            }
            json.put("cep", cep);
            jsonArray.put(json);
        }
        return jsonArray;
    }

    public void updateGeolocalizacaoEmpresa(String json){
        try {

            JSONObject jsonObject = new JSONObject(json);
            String latitude = jsonObject.getString("latitude");
            String longitude = jsonObject.getString("longitude");
            Integer codigoEmpresa = jsonObject.getInt("empresa");

            String sql = "UPDATE empresa SET latitude = ?, longitude = ? WHERE codigo = ?";
            PreparedStatement ps = con.prepareStatement(sql);
            ps.setString(1, latitude);
            ps.setString(2, longitude);
            ps.setInt(3, codigoEmpresa);
            ps.executeUpdate();

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public Boolean verificarSePossuiEndereco(Integer codigoPessoa) {
        String sql = "SELECT codigo FROM endereco WHERE pessoa = " + codigoPessoa;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return true;
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return false;
    }
}
