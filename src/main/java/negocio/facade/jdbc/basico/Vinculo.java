package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.Agenda;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.crm.ClienteOrganizadorCarteiraVO;
import negocio.comuns.crm.FaixaHorarioAcessoClienteVO;
import negocio.comuns.crm.FiltroCarteiraTO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.interfaces.basico.VinculoInterfaceFacade;
import relatorio.controle.crm.CarteirasRel;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.treino.dto.SinteticoMsDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>VinculoVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>VinculoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see VinculoVO
 * @see SuperEntidade
 */
public class Vinculo extends SuperEntidade implements VinculoInterfaceFacade {
    
    public Vinculo() throws Exception {
        super();
        setIdEntidade("Cliente");
    }
    
    public Vinculo(Connection con) throws Exception {
        super(con);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     */
    public static List montarDadosConsultaOrganizadorCarteira(ResultSet tabelaResultado, boolean periodoMaisAcesso, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            
            ClienteOrganizadorCarteiraVO obj = new ClienteOrganizadorCarteiraVO();
            obj.setNomeColaboradoreCarteira(tabelaResultado.getString("desmembrarCarteira"));
            obj.setNrCarteira(new Integer(tabelaResultado.getInt("carteira")));
            obj.getCliente().setCodigo(new Integer(tabelaResultado.getInt("cliente")));
            obj.setCodigoColaborador(new Integer(tabelaResultado.getInt("colaborador")));
            obj.setCodigoVinculo(new Integer(tabelaResultado.getInt("vinculo")));
            obj.setTipoVinculo(tabelaResultado.getString("tipovinculo"));
            
            obj.getCliente().setCodigoMatricula(new Integer(tabelaResultado.getInt("codigomatricula")));
            obj.getCliente().getPessoa().setCodigo(new Integer(tabelaResultado.getInt("codPessoa")));
            obj.getCliente().getPessoa().setNome(tabelaResultado.getString("nomePessoa"));
            obj.getCliente().setSituacao(tabelaResultado.getString("cliSituacao"));
            
            if (periodoMaisAcesso) {
                obj.setHoraMaisAcesso(tabelaResultado.getString("periodomaisacesso"));
            }
            montarDadosSituacaoClienteSintetico(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
            if (obj.getCliente().getSituacao() == null || "".equals(obj.getCliente().getSituacao())) {
                obj.getCliente().setSituacao(tabelaResultado.getString("cliSituacao"));
            }
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     */
    @SuppressWarnings("unchecked")
    public static List montarDadosConsultaOrganizadorCarteiraFiltro(ResultSet tabelaResultado, boolean periodoMaisAcesso, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        
        while (tabelaResultado.next()) {
            ClienteOrganizadorCarteiraVO obj = new ClienteOrganizadorCarteiraVO();
            //obj.setNomeColaboradoreCarteira(tabelaResultado.getString("desmembrarCarteira"));
            obj.setNrCarteira(new Integer(tabelaResultado.getInt("carteira")));
            obj.getCliente().setCodigo(new Integer(tabelaResultado.getInt("codCliente")));
            obj.getCliente().setCodigoMatricula(new Integer(tabelaResultado.getInt("codigomatricula")));
            obj.getCliente().getPessoa().setCodigo(new Integer(tabelaResultado.getInt("codPessoa")));
            obj.getCliente().getPessoa().setNome(tabelaResultado.getString("nomePessoa"));
            obj.getCliente().setSituacao(tabelaResultado.getString("cliSituacao"));
//            obj.setTipoVinculo(tabelaResultado.getString("tipovinculo"));
            if (periodoMaisAcesso) {
                obj.setHoraMaisAcesso(tabelaResultado.getString("periodomaisacesso"));
            }
            montarDadosSituacaoClienteSintetico(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
            obj.setNomeColaboradoreCarteira(obj.getCliente().getSituacaoClienteSinteticoVO().getColaboradores());
            if (obj.getCliente().getSituacao() == null || "".equals(obj.getCliente().getSituacao())) {
                obj.getCliente().setSituacao(tabelaResultado.getString("cliSituacao"));
            }
            vetResultado.add(obj);
        }
        
        return vetResultado;
    }
    
    public static void montarDadosSituacaoClienteSintetico(ClienteOrganizadorCarteiraVO obj, int nivelMontarDados, Connection con) throws Exception {
        getFacade().getSituacaoClienteSinteticoDW().consultarClienteOrganizadorCarteiras(obj);
        obj.getCliente().setSituacao(obj.getCliente().getSituacaoClienteSinteticoVO().getSituacao());
        obj.getCliente().obterSituacaoClientePorCLienteEspecifico(obj.getCliente(), con);
        obj.getCliente().getSituacaoClienteSinteticoVO().setDataRematriculaContrato(
                getFacade().getContrato().consultarDataRematricula(obj.getCliente().getPessoa().getCodigo()));
        obj.getCliente().getSituacaoClienteSinteticoVO().setNrDiasUltimoAcesso(getFacade().getHistoricoContato().consultarDiasUltimoAcesso(obj.getCliente().getCodigo()));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     */
    public static List<VinculoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<VinculoVO> vetResultado = new ArrayList<VinculoVO>();
        while (tabelaResultado.next()) {
            VinculoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>VinculoVO</code>.
     *
     * @return O objeto da classe <code>VinculoVO</code> com os dados
     * devidamente montados.
     */
    public static VinculoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        VinculoVO obj = new VinculoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTipoVinculo(dadosSQL.getString("tipoVinculo"));
        obj.getCliente().setCodigo(dadosSQL.getInt("cliente"));
        obj.getColaborador().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        // a entidade organizador de carteira usa esse nivel para montar seus
        // dados.
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_ROBO);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_INDICERENOVACAO) {
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_INDICERENOVACAO, con);
            montarDadosTelefone(obj.getColaborador().getPessoa(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
            return obj;
        }
        montarDadosColaborador(obj, nivelMontarDados, con);
        return obj;
    }

    public static String montarVincloEmpresasDados(ResultSet dadosSQL) throws Exception {
        return "Total de: " + dadosSQL.getString("total") +" "+ dadosSQL.getString("nome");
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>VinculoVO</code>. Faz uso da chave primária da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaborador(VinculoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColaborador().getCodigo().intValue() == 0) {
            obj.setColaborador(new ColaboradorVO());
            return;
        }
        Colaborador colaborador = new Colaborador(con);
        obj.setColaborador(colaborador.consultarPorChavePrimaria(obj.getColaborador().getCodigo(), nivelMontarDados));
        colaborador = null;
    }
    
    public static void montarDadosTelefone(PessoaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCodigo().intValue() == 0) {
            return;
        }
        Telefone telefone = new Telefone(con);
        obj.setTelefoneVOs(telefone.consultarTelefones(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        telefone = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>VinculoVO</code>. Faz uso da chave primária da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(VinculoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        obj.setCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>VinculoVO</code>.
     */
    @Override
    public VinculoVO novo() throws Exception {
        incluir(getIdEntidade());
        VinculoVO obj = new VinculoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>VinculoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>VinculoVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void incluir(VinculoVO obj, String origem, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        incluir(obj, null, origem, true, null, sinteticoSincronizar);
    }
    
    @Override
    public void incluir(VinculoVO obj, Date registro, String origem, boolean controlarLog, UsuarioVO usuarioVO, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        VinculoVO.validarDados(obj);
        if (controlarLog) {
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
        }
        String sql = "INSERT INTO Vinculo( cliente, colaborador, tipoVinculo ) VALUES ( ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getCliente().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getCliente().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getColaborador().getCodigo() != 0) {
                sqlInserir.setInt(2, obj.getColaborador().getCodigo());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setString(3, obj.getTipoVinculo());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        HistoricoVinculoVO hist;
        if (registro == null) {
            hist = new HistoricoVinculoVO(obj.getCliente().getCodigo(), obj.getColaborador().getCodigo(), "EN", obj.getTipoVinculo(), origem, usuarioVO);
        } else {
            hist = new HistoricoVinculoVO(obj.getCliente().getCodigo(), obj.getColaborador().getCodigo(), "EN", obj.getTipoVinculo(), registro, origem, usuarioVO);
        }
        HistoricoVinculo hvDao = new HistoricoVinculo(con);
        hvDao.incluirSemCommit(hist, controlarLog);
        atualizarSintetico(obj, sinteticoSincronizar,
                origem == null || !origem.equals("TREINO") ? SituacaoClienteSinteticoEnum.GRUPO_VINCULO : SituacaoClienteSinteticoEnum.VINCULO_TREINO);
    }

    private void atualizarSintetico(VinculoVO vinculo, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        atualizarSintetico(vinculo, sinteticoSincronizar, SituacaoClienteSinteticoEnum.GRUPO_VINCULO);
    }

    private void atualizarSintetico(VinculoVO vinculo, SinteticoMsDTO sinteticoSincronizar, SituacaoClienteSinteticoEnum tipo) throws Exception {
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        zwFacade.atualizarSintetico(vinculo.getCliente(),
                Calendario.hoje(), tipo, true, sinteticoSincronizar);
        zwFacade = null;
    }
    
    private void atualizarSintetico(List<Integer> clientes, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        ClienteVO cliente = new ClienteVO();
        for(Integer codcliente: clientes ){
            cliente.setCodigo(codcliente);
            cliente.setDadosSinteticoPreparados(false);
            zwFacade.atualizarSintetico(cliente,
                Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_VINCULO, true, sinteticoSincronizar);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>VinculoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>VinculoVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void alterar(VinculoVO obj, String origem, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        VinculoVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE Vinculo set cliente=?, colaborador=?, tipoVinculo=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getCliente().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getColaborador().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getColaborador().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setString(3, obj.getTipoVinculo());
            sqlAlterar.setInt(4, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
        atualizarSintetico(obj, sinteticoSincronizar);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>VinculoVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void organizarCarteira(List<GrupoColaboradorVO> listaGrupoColaborador) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarCRM("OrganizadorCarteira");
            for (GrupoColaboradorVO grupo : listaGrupoColaborador) {
                for (GrupoColaboradorParticipanteVO colaborador : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (colaborador.getGrupoColaboradorParticipanteEscolhido()) {
                        if (UteisValidacao.emptyString(colaborador.getTipoGrupo())) {
                            colaborador.setTipoGrupo(grupo.getTipoGrupo());
                        }
                        excluirVinculoOrganizadorCarteira(colaborador);
                        for (ClienteOrganizadorCarteiraVO obj : colaborador.getColaboradorParticipante().getListaVinculos()) {
                            if (obj.getCodigoVinculo().intValue() == 0) {
                                if (colaborador.getTipoGrupo().equals("CO")) {
                                    excluirVinculoConsultor(obj.getCliente().getCodigo());
                                }
                                if (UteisValidacao.emptyString(obj.getTipoVinculo())) {
                                    obj.setTipoVinculo(colaborador.getTipoGrupo());
                                }
                                if (obj.getCodigoColaborador() == null || obj.getCodigoColaborador() == 0) {
                                    obj.setCodigoColaborador(colaborador.getColaboradorParticipante().getCodigo());
                                }
                                incluirVinculoOrganizadorCarteira(obj, colaborador.getTipoGrupo());
                            }
                        }
                        
                    }
                    
                }
            }
            
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    @Override
    public void incluirVinculoOrganizadorCarteira(ClienteOrganizadorCarteiraVO obj, String tipoVinculo) throws Exception {
        try {
            validarDadosVinculoVindoOrganizadorCarteira(obj);
            VinculoVO vinculo = new VinculoVO();
            vinculo.setCliente(obj.getCliente());
            vinculo.getColaborador().setCodigo(obj.getCodigoColaborador());
            vinculo.setTipoVinculo(tipoVinculo);
            incluir(vinculo, "ORGANIZADOR", null);
        } catch (Exception e) {
            throw e;
        }
        
    }
    
    @Override
    public void validarDadosVinculoVindoOrganizadorCarteira(ClienteOrganizadorCarteiraVO obj) throws ConsistirException {
        if ((obj.getCliente().getCodigo() == null) || (obj.getCliente().getCodigo() == 0)) {
            throw new ConsistirException("O cliente deve ser informado.");
        }
        if (obj.getTipoVinculo() == null || obj.getTipoVinculo().trim().equals("")) {
            throw new ConsistirException("O tipo vinculo deve ser informado.");
        }
        if ((obj.getCodigoColaborador() == null) || (obj.getCodigoColaborador() == 0)) {
            throw new ConsistirException("O colaborador deve ser informado.");
        }
        
    }
    
    public void excluirVinculoConsultor(Integer cliente) throws Exception {
        String str = "SELECT * FROM Vinculo WHERE cliente = " + cliente + " and tipoVinculo = 'CO' ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tr = stm.executeQuery(str)) {
                while (tr.next()) {
                    String sql = "DELETE FROM Vinculo WHERE ((cliente = ?) and (colaborador = ?) and (tipoVinculo = ?))";
                    try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                        if (tr.getInt("cliente") != 0) {
                            sqlExcluir.setInt(1, tr.getInt("cliente"));
                        }
                        if (tr.getInt("colaborador") != 0) {
                            sqlExcluir.setInt(2, tr.getInt("colaborador"));
                        }
                        sqlExcluir.setString(3, tr.getString("tipoVinculo"));
                        sqlExcluir.execute();
                    }
                    HistoricoVinculoVO hist = new HistoricoVinculoVO(tr.getInt("cliente"), tr.getInt("colaborador"), "SD", tr.getString("tipoVinculo"), "ORGANIZADOR", null);
                    getFacade().getHistoricoVinculo().incluirSemCommit(hist);
                }
            }
        }
    }
    
    @Override
    public void excluirVinculoOrganizadorCarteira(GrupoColaboradorParticipanteVO colaborador) throws Exception {
        excluir(getIdEntidade());
        String str;
        if (colaborador.getExiteClienteVinculado() && colaborador.getColaboradorParticipante().getListaVinculos().isEmpty()) {
            str = "SELECT * FROM Vinculo WHERE colaborador = " + colaborador.getColaboradorParticipante().getCodigo().intValue() + " and tipoVinculo = '" + colaborador.getTipoGrupo().toUpperCase() + "' ";
        } else {
            str = "SELECT * FROM Vinculo WHERE colaborador = " + colaborador.getColaboradorParticipante().getCodigo().intValue() + " and tipoVinculo = '" + colaborador.getTipoGrupo().toUpperCase() + "' ";
            for (ClienteOrganizadorCarteiraVO obj : colaborador.getColaboradorParticipante().getListaVinculos()) {
                str += " AND codigo <> " + obj.getCodigoVinculo().intValue();
            }
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tr = stm.executeQuery(str)) {
                while (tr.next()) {
                    String sql = "DELETE FROM Vinculo WHERE ((cliente = ?) and (colaborador = ?) and (tipoVinculo = ?))";
                    try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                        if (tr.getInt("cliente") != 0) {
                            sqlExcluir.setInt(1, tr.getInt("cliente"));
                        }
                        if (tr.getInt("colaborador") != 0) {
                            sqlExcluir.setInt(2, tr.getInt("colaborador"));
                        }
                        sqlExcluir.setString(3, tr.getString("tipoVinculo"));
                        sqlExcluir.execute();
                    }
                    HistoricoVinculoVO hist = new HistoricoVinculoVO(tr.getInt("cliente"), tr.getInt("colaborador"), "SD", tr.getString("tipoVinculo"), "ORGANIZADOR", null);
                    getFacade().getHistoricoVinculo().incluirSemCommit(hist);
                }
            }
        }

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>CidadeVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>CidadeVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public void excluir(VinculoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Vinculo WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }

    }
    
    public void excluir(VinculoVO obj, String origem, boolean controlarLog) throws Exception {
        excluir(obj, null, origem, controlarLog);
    }

    public void excluir(VinculoVO obj, Date registro, String origem, boolean controlarLog) throws Exception {
        VinculoVO.validarDados(obj);
        if (controlarLog) {
            excluir(getIdEntidade());
        }
        String sql = "DELETE FROM vinculo WHERE codigo = ?";

        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }

        HistoricoVinculoVO hist;
        if (registro == null) {
            hist = new HistoricoVinculoVO(obj.getCliente().getCodigo(), obj.getColaborador().getCodigo(), "SD", obj.getTipoVinculo(), origem, null);
        } else {
            hist = new HistoricoVinculoVO(obj.getCliente().getCodigo(), obj.getColaborador().getCodigo(), "SD", obj.getTipoVinculo(), registro, origem, null);
        }
        HistoricoVinculo hvDao = new HistoricoVinculo(con);
        hvDao.incluirSemCommit(hist, controlarLog);
        atualizarSintetico(obj, null,
                origem == null || !origem.equals("TREINO") ? SituacaoClienteSinteticoEnum.GRUPO_VINCULO : SituacaoClienteSinteticoEnum.VINCULO_TREINO);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>VinculoVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public void excluirVinculoPorCodigoColaboradorClienteTipoVinculo(int vinculo, int colaborador, int cliente, String tipoVinculo, String origem, UsuarioVO usuarioVO) throws Exception {
        excluirVinculoPorCodigoColaboradorClienteTipoVinculo(vinculo, colaborador, cliente, tipoVinculo, null, origem, usuarioVO);
    }
    
    @Override
    public void excluirVinculoPorCodigoColaboradorClienteTipoVinculo(int vinculo, int colaborador, int cliente, String tipoVinculo, Date dataRegistro, String origem, UsuarioVO usuarioVO) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Vinculo WHERE codigo = " + vinculo;
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.execute();
        }
        HistoricoVinculoVO histAntigo = getFacade().getHistoricoVinculo().consultarPorMaisRecenteClienteConsultor(cliente, colaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        HistoricoVinculoVO hist;
        if (dataRegistro != null) {
            if (histAntigo.getDataRegistro().compareTo(dataRegistro) >= 0) {
                getFacade().getHistoricoVinculo().excluirSemCommit(histAntigo);
            } else if (!histAntigo.getTipoHistoricoVinculo().equals("SD")) {
                hist = new HistoricoVinculoVO(cliente, colaborador, "SD", tipoVinculo, dataRegistro, origem, usuarioVO);
                getFacade().getHistoricoVinculo().incluirSemCommit(hist);
            }
        } else {
            hist = new HistoricoVinculoVO(cliente, colaborador, "SD", tipoVinculo, origem, usuarioVO);
            getFacade().getHistoricoVinculo().incluirSemCommit(hist);
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Vinculo.* FROM Vinculo, Colaborador WHERE Vinculo.colaborador = Colaborador.codigo and upper( Colaborador.situacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Colaborador.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCodigoCliente(Integer cliente, int nivelMontarDados, boolean contratolarAcesso) throws Exception {
        if (contratolarAcesso) {
            consultar(getIdEntidade(), true);
        }
        String sqlStr = "SELECT  vinculo.* FROM vinculo, cliente WHERE vinculo.cliente = cliente.codigo AND cliente.codigo = '" + cliente.intValue() + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCodigoClienteEmpresa(Integer cliente, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT  vinculo.* FROM vinculo, cliente WHERE vinculo.cliente = cliente.codigo AND cliente.empresa = " + empresa + " AND cliente.codigo = '" + cliente.intValue() + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorSituacaoClienteEmpresa(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT  vinculo.* FROM vinculo, cliente WHERE vinculo.cliente = cliente.codigo AND cliente.empresa = " + empresa + " AND cliente.situacao = '" + valorConsulta.toUpperCase() + "'";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT  vinculo.* FROM vinculo, cliente WHERE vinculo.cliente = cliente.codigo AND cliente.empresa = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>matricula</code> da classe
     * <code>Cliente</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Vinculo.* FROM Vinculo, Cliente WHERE Vinculo.cliente = Cliente.codigo and upper( Cliente.matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.matricula";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Vinculo</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Vinculo WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    
    @Override
    public List consultarPorCodigoGrupoColaboradorParticipante(Integer codigo, boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controleAcesso);
        StringBuilder sb = new StringBuilder();
        
        sb.append("select cliente, colaborador , vi.codigo as vinculo, tipovinculo ");
        sb.append(" , cli.codigomatricula, cli.pessoa as codPessoa, pes.nome as nomePessoa, cli.situacao as cliSituacao ");
        sb.append(" ,(select count (colaborador) from vinculo where cliente = vi.cliente) as carteira, ");
        sb.append(" ARRAY_TO_STRING( ARRAY( select substring(nome from 0 for 10) from vinculo  ");
        sb.append(" inner join colaborador on colaborador.codigo = vinculo.colaborador  ");
        sb.append(" inner join pessoa on pessoa.codigo = colaborador.pessoa   ");
        sb.append(" where vinculo.cliente = vi.cliente ) , ' , ' )  as desmembrarCarteira ");
        sb.append(" from vinculo as vi ");
        sb.append(" inner join cliente cli on cli.codigo = cliente  ");
        sb.append(" inner join pessoa pes on pes.codigo = cli.pessoa ");
        sb.append(" where vi.colaborador = " + codigo + " ");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsultaOrganizadorCarteira(tabelaResultado, false, nivelMontarDados, con));
            }
        }

    }
    
    @Override
    public List consultarPorCodigoGrupoColaboradorParticipanteTipoGrupo(Integer codigo, String tipoGrupo, String tipoVisao, boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controleAcesso);
        StringBuilder sb = new StringBuilder();
        
        sb.append("(select cliente, colaborador , vi.codigo as vinculo, tipovinculo, \n");
        sb.append(" cli.codigomatricula, cli.pessoa as codPessoa, pes.nome as nomePessoa, cli.situacao as cliSituacao, \n");
        sb.append("(select count (colaborador) from vinculo where cliente = vi.cliente) as carteira, \n");
        sb.append("ARRAY_TO_STRING( ARRAY( select substring(nome from 0 for 10) from vinculo \n");
        sb.append("inner join colaborador on colaborador.codigo = vinculo.colaborador  \n");
        sb.append("inner join pessoa on pessoa.codigo = colaborador.pessoa  \n");
        sb.append("where vinculo.cliente = vi.cliente ) , ' , ' )  as desmembrarCarteira \n");
        FaixaHorarioAcessoCliente faixas = new FaixaHorarioAcessoCliente(con);
        List<FaixaHorarioAcessoClienteVO> periodos = faixas.consultarFaixas();
        if (!periodos.isEmpty()) {
            sb.append(" , " + consultaPeriodoMaisAcessadoCliente(periodos, "cli.codigo") + "AS periodomaisacesso \n");
        }
        sb.append("from vinculo as vi \n");
        sb.append(" inner join cliente cli on cli.codigo = cliente  \n");
        sb.append(" inner join pessoa pes on pes.codigo = cli.pessoa \n");
        if (tipoVisao.equals("VI")) {
            sb.append("where vi.colaborador = " + codigo + " )");
        } else {
            sb.append("where vi.colaborador = " + codigo + " and vi.tipovinculo = '" + tipoGrupo + "')");
        }


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsultaOrganizadorCarteira(tabelaResultado, !periodos.isEmpty(), nivelMontarDados, con));
            }
        }

    }
    
    @Override
    public List consultarPorCodigoGrupoColaboradorParticipanteTipoGrupoPeriodo(Integer codigo, String tipoGrupo, String tipoVisao, Date data, boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controleAcesso);
        
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT cliente, colaborador , vi.codigo AS vinculo, tipovinculo  , cli.codigomatricula, cli.pessoa AS codPessoa, \n");
        sb.append("pes.nome AS nomePessoa, cli.situacao AS cliSituacao, (SELECT count (colaborador) FROM vinculo WHERE cliente = vi.cliente) AS carteira, \n");
        sb.append("ARRAY_TO_STRING( ARRAY( select substring(nome from 0 for 10) from vinculo \n");
        sb.append("inner join colaborador on colaborador.codigo = vinculo.colaborador \n");
        sb.append("inner join pessoa on pessoa.codigo = colaborador.pessoa \n");
        sb.append("where vinculo.cliente = vi.cliente ) , ' , ' )  as desmembrarCarteira   \n");
        FaixaHorarioAcessoCliente faixas = new FaixaHorarioAcessoCliente(con);
        List<FaixaHorarioAcessoClienteVO> periodos = faixas.consultarFaixas();
        if (!periodos.isEmpty()) {
            sb.append(" , " + consultaPeriodoMaisAcessadoCliente(periodos, "cli.codigo") + "AS periodomaisacesso  ");
        }
        sb.append(" from vinculo as vi  ");
        sb.append("inner join cliente cli on cli.codigo = cliente \n");
        sb.append("inner join pessoa pes on pes.codigo = cli.pessoa \n");
        sb.append("inner join situacaoclientesinteticodw as sc on sc.codigocliente = vi.cliente \n");
        sb.append("where vi.colaborador = " + codigo);
//        if (!tipoVisao.equals("VI")) {
//            sb.append("   and  vi.tipovinculo = '" + tipoGrupo + "' \n");
//        }
        sb.append(" and (((sc.situacao in ('IN', 'TR') ) and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(data) + " 00:00:00') \n");
        sb.append("	OR  (sc.situacao in ('AT') )  \n");
        sb.append("	OR  (sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(data) + " 00:00:00')) ");


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsultaOrganizadorCarteira(tabelaResultado, !periodos.isEmpty(), nivelMontarDados, con));
            }
        }

    }
    
    @Override
    public List consultarPorCodigoGrupoColaboradorParticipantePeriodo(Integer codigo, Date data, boolean controleAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controleAcesso);
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT cliente, colaborador , vi.codigo AS vinculo, tipovinculo  , cli.codigomatricula, cli.pessoa AS codPessoa, \n");
        sb.append("pes.nome AS nomePessoa, cli.situacao AS cliSituacao,( \n");
        sb.append("SELECT COUNT(colaborador) FROM vinculo WHERE cliente = vi.cliente) AS carteira, \n");
        sb.append("ARRAY_TO_STRING( ARRAY(SELECT SUBSTRING(nome FROM 0 FOR 10) FROM vinculo \n");
        sb.append("INNER JOIN colaborador ON colaborador.codigo = vinculo.colaborador \n");
        sb.append("INNER JOIN pessoa ON pessoa.codigo = colaborador.pessoa    \n");
        sb.append("WHERE vinculo.cliente = vi.cliente ) , ' , ' ) AS desmembrarCarteira \n");
        
        FaixaHorarioAcessoCliente faixas = new FaixaHorarioAcessoCliente(con);
        List<FaixaHorarioAcessoClienteVO> periodos = faixas.consultarFaixas();
        if (!periodos.isEmpty()) {
            sb.append(" , " + consultaPeriodoMaisAcessadoCliente(periodos, "vi.cliente") + "AS periodomaisacesso ");
        }
        
        sb.append(" FROM vinculo AS vi ");
        sb.append("INNER JOIN cliente cli on cli.codigo = cliente ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append("AND cli.empresa = " + empresa);
        }
        sb.append(" INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n");
        sb.append(" INNER JOIN situacaoclientesinteticodw AS sc ON sc.codigocliente = vi.cliente \n");
        sb.append(" WHERE vi.colaborador = " + codigo);
//        if (!UteisValidacao.emptyString(tipoGrupo)){
//            sb.append(" AND vi.tipoVinculo = '").append(tipoGrupo).append("' ");
//        }
        if (data != null) {
            sb.append(" AND (sc.situacao IN('AT') \n");
            sb.append(" OR ( sc.situacao in ('IN', 'TR') and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(data) + " 00:00:00') \n");
            sb.append(" OR ( sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(data) + " 00:00:00')) \n");
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString())) {
                return (montarDadosConsultaOrganizadorCarteira(tabelaResultado, !periodos.isEmpty(), nivelMontarDados, con));
            }
        }

    }
    
    private String consultaPeriodoMaisAcessadoCliente(List<FaixaHorarioAcessoClienteVO> periodos, String cliente) {
        StringBuilder sql = new StringBuilder();
        sql.append("(SELECT periodo FROM ( \n");
        for (FaixaHorarioAcessoClienteVO periodo : periodos) {
            sql.append(" UNION ALL ");
            sql.append(" SELECT '" + periodo.getNomePeriodo() + "' AS periodo,COUNT(dthrentrada) AS acessos, MAX (dthrentrada) AS ultimoacesso  FROM ( \n");
            sql.append(" SELECT dthrentrada FROM (SELECT * FROM acessocliente WHERE cliente = " + cliente);
            sql.append(" ORDER BY dthrentrada DESC LIMIT 30) AS acessocliente  \n");
            sql.append(" WHERE CAST(SUBSTRING(CAST(dthrentrada AS varchar)FROM 12 FOR 5) AS TIME) \n");
            sql.append(" BETWEEN '" + periodo.getHoraInicial() + "' AND '" + periodo.getHoraFinal() + "' ORDER BY dthrentrada DESC LIMIT 30) AS cont \n");
        }
        //pro caso da lista de períodos conter apenas um período cadastrado, é necessário fazer este UNION para evitar um erro do postgres
        if (periodos.size() == 1) {
            sql.append("  UNION ALL SELECT 'Nenhum' AS periodo, -1 AS acessos, NULL AS ultimoacesso ");
        }
        sql.append(")AS periodoacesso WHERE acessos > 0 ORDER BY acessos DESC, ultimoacesso DESC LIMIT 1 )  \n");
        return sql.toString().replaceFirst("UNION ALL ", "");
    }

    /**
     *
     * Autor: Pedro Y. Saito Refactor em 04/03/2011
     */
    @Override
    public List consultarPorClienteSemTipoVinculoOrganizadorCarteira(String tipoVinculo, String campoConsultar, String valorConsultar, String situacao,
            String periodoMaisAcessado, boolean controleAcesso, Integer codEmpresa, Integer codigoColaborador,
            Date dataLimite, boolean ignoraMeses, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controleAcesso);
        StringBuilder sb = new StringBuilder();
        StringBuilder sbFiltro = new StringBuilder();
        
        sb.append("select distinct (cli.pessoa), cli.codigomatricula, cli.pessoa as codPessoa, cli.codigo as codCliente ");
        sb.append(" , pes.nome as nomePessoa, cli.situacao as cliSituacao ");
        sb.append(" ,(select count (colaborador) from vinculo where cli.codigo = vinculo.cliente) as carteira ");
        FaixaHorarioAcessoCliente faixas = new FaixaHorarioAcessoCliente(con);
        List<FaixaHorarioAcessoClienteVO> periodos = faixas.consultarFaixas();
        if (!periodos.isEmpty()) {
            sb.append(" , " + consultaPeriodoMaisAcessadoCliente(periodos, "cli.codigo") + "AS periodomaisacesso ");
        }
        sb.append(" from cliente cli ");
        sb.append(" inner join pessoa pes on pes.codigo = cli.pessoa ");
        if (!UteisValidacao.emptyNumber(codigoColaborador)) {
            sb.append(" INNER JOIN vinculo ON vinculo.cliente = cli.codigo AND vinculo.colaborador = " + codigoColaborador);
        }
        
        if (dataLimite != null && !ignoraMeses) {
            sb.append(" INNER JOIN situacaoclientesinteticodw AS sc ON sc.codigocliente = cli.codigo ");
            sb.append(" AND (sc.situacao IN('AT') \n");
            sb.append(" OR ( sc.situacao in ('IN', 'TR') and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(dataLimite) + " 00:00:00') \n");
            sb.append(" OR ( sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(dataLimite) + " 00:00:00')) \n");
            
        }
        
        if (campoConsultar.equals("PR")) {
            sb.append(" inner  join profissao as pro on pro.codigo = pes.profissao and pro.descricao ilike '" + valorConsultar.toUpperCase() + "%' ");
        } else if (campoConsultar.equals("EM")) {
            sb.append(" inner join  empresa em on em.codigo = cli.empresa and em.nome ilike '" + valorConsultar + "%' ");
        }


        //CRIANDO OS FILTROS
        if (tipoVinculo != null && !"".equals(tipoVinculo)) {
            sbFiltro.append("		cli.codigo not in (");
            sbFiltro.append("                           select cliente from vinculo ");
            sbFiltro.append("							 		where vinculo.tipovinculo ilike '" + tipoVinculo + "'");
            sbFiltro.append("						   )");
        }
        
        if (situacao != null && !"".equals(situacao)) {
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" and cli.situacao = '" + situacao.toUpperCase() + "' ");
            } else {
                sbFiltro.append(" cli.situacao = '" + situacao.toUpperCase() + "' ");
            }
        }
        
        if (campoConsultar.equals("MA")) {
            Integer matricula = new Integer(0);
            String valorConsulta = Uteis.getDesmontarMatricula(valorConsultar);
            matricula = valorConsulta.equals("") ? new Integer(0) : new Integer(valorConsulta);
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" and cli.codigomatricula = " + matricula.intValue());
            } else {
                sbFiltro.append(" cli.codigomatricula = " + matricula.intValue());
            }
        } else if (campoConsultar.equals("CO")) {
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" and " + valorConsultar + " = vi.cliente ");
            } else {
                sbFiltro.append(" " + valorConsultar + " = vi.cliente ");
            }
        } else if (campoConsultar.equals("NO")) {
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" and pes.nome ilike '" + valorConsultar + "%' ");
            } else {
                sbFiltro.append(" pes.nome ilike '" + valorConsultar + "%' ");
            }
        } else if (campoConsultar.equals("CP")) {
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" and pes.cfp ilike '" + valorConsultar + "%' ");
            } else {
                sbFiltro.append(" pes.cfp ilike '" + valorConsultar + "%' ");
            }
        }
        
        if (codEmpresa != null && codEmpresa.intValue() > 0) {
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" and cli.empresa = " + codEmpresa.intValue());
            } else {
                sbFiltro.append(" cli.empresa = " + codEmpresa.intValue());
            }
        }
        if (periodoMaisAcessado != null && !periodoMaisAcessado.equals("semAcesso") && !periodoMaisAcessado.equals("") && !periodos.isEmpty()) {
            if (!"".equals(sbFiltro.toString())) {
                sbFiltro.append(" AND ");
            }
            sbFiltro.append(consultaPeriodoMaisAcessadoCliente(periodos, "cli.codigo"));
            sbFiltro.append(" LIKE '" + periodoMaisAcessado + "' ");
            
        }
        if (periodoMaisAcessado != null && periodoMaisAcessado.equals("semAcesso")) {
            sbFiltro.append(" AND (SELECT COUNT(codigo) FROM acessocliente WHERE cliente = cli.codigo) = 0 ");
        }
        if (!"".equals(sbFiltro.toString())) {
            sb.append(" WHERE ");
            sb.append(sbFiltro.toString());
        }

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sb.toString().trim())) {
                return (montarDadosConsultaOrganizadorCarteiraFiltro(tabelaResultado, !periodos.isEmpty(), nivelMontarDados, con));
            }
        }
    }

    /**
     * Operação responsável por excluir todos os objetos da
     * <code>VinculoVO</code> no BD. Faz uso da operação
     * <code>excluir</code> disponível na classe
     * <code>Vinculo</code>.
     *
     * @param cliente campo chave para exclusão dos objetos no BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public void excluirVinculo(Integer cliente) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Vinculo WHERE (cliente = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, cliente.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da
     * <code>VinculoVO</code> contidos em um Hashtable no BD. Faz uso da
     * operação
     * <code>excluirVinculo</code> e
     * <code>incluirVinculo</code> disponíveis na classe
     * <code>Vinculo</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public void alterarVinculo(Integer cliente, UsuarioVO usuarioVO, List objetos, String origem) throws Exception {
        validarHistoricoVinculoSaida(cliente, usuarioVO, objetos, origem);
        String str = "DELETE FROM Vinculo WHERE cliente = " + cliente;
        for (Object objeto1 : objetos) {
            VinculoVO objeto = (VinculoVO) objeto1;
            str += " AND codigo <> " + objeto.getCodigo();
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(str)) {
            sqlExcluir.execute();
        }
        for (Object objeto : objetos) {
            VinculoVO obj = (VinculoVO) objeto;
            if (obj.isNovoObj()) {
                obj.getCliente().setCodigo(cliente);
                incluir(obj, null, origem, true, usuarioVO, null);
            } else {
                alterar(obj, origem, null);
            }
        }
        // excluirVinculo( cliente );
        // incluirVinculo( cliente, objetos );
    }
    
    @Override
    public void validarHistoricoVinculoSaida(Integer cliente, UsuarioVO usuarioVO, List objetos, String origem) throws Exception {
        String strconsultar = "SELECT * FROM Vinculo WHERE cliente = " + cliente;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            VinculoVO objeto = (VinculoVO) i.next();
            strconsultar += " AND codigo <> " + objeto.getCodigo();
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(strconsultar)) {
                while (tabelaResultado.next()) {
                    HistoricoVinculoVO hist = new HistoricoVinculoVO(tabelaResultado.getInt("cliente"), tabelaResultado.getInt("colaborador"), "SD", tabelaResultado.getString("tipoVinculo"), origem, usuarioVO);
                    getFacade().getHistoricoVinculo().incluirSemCommit(hist);
                }
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da
     * <code>VinculoVO</code> no BD. Garantindo o relacionamento com a entidade
     * principal
     * <code>basico.Cliente</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public void incluirVinculo(Integer clientePrm, List objetos, String origem, UsuarioVO usuarioVO) throws Exception {
        incluirVinculo(clientePrm, objetos, origem, usuarioVO, true);
    }
    public void incluirVinculo(Integer clientePrm, List objetos, String origem, UsuarioVO usuarioVO, boolean controlarAcesso) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            VinculoVO obj = (VinculoVO) e.next();
            obj.getCliente().setCodigo(clientePrm);
            if (obj.getCodigo() == 0) {
                obj.getCliente().setCodigo(clientePrm);
                incluir(obj, null, origem, controlarAcesso, usuarioVO, null);
            } else {
                alterar(obj, origem, null);
            }
        }
    }
    
    public void incluirVinculo(Integer clientePrm, List objetos, String origem) throws Exception {
        incluirVinculo(clientePrm, objetos, origem, null);
    }

    /**
     * Operação responsável por consultar todos os
     * <code>VinculoVO</code> relacionados a um objeto da classe
     * <code>basico.Cliente</code>.
     *
     * @param cliente Atributo de <code>basico.Cliente</code> a ser utilizado
     * para localizar os objetos da classe <code>VinculoVO</code>.
     * @return List Contendo todos os objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public List consultarVinculo(Integer cliente, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM Vinculo WHERE cliente = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, cliente.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    VinculoVO novoObj = new VinculoVO();
                    novoObj = Vinculo.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por consultar todos os
     * <code>VinculoVO</code> relacionados a um objeto da classe
     * <code>basico.Cliente</code>.
     *
     * @return List Contendo todos os objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public List consultarVinculoPorCodigoColaborador(Integer colaborador,  String tipoVinculo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM Vinculo WHERE colaborador = ?";
        if(!UteisValidacao.emptyString(tipoVinculo)){
            sql += " and tipovinculo  = '"+tipoVinculo+"'";
        }
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, colaborador.intValue());
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    VinculoVO novoObj = new VinculoVO();
                    novoObj = Vinculo.montarDados(resultado, nivelMontarDados, this.con);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por consultar todos os
     * <code>VinculoVO</code> relacionados a um objeto da classe
     * <code>basico.Cliente</code>.
     *
     * @param cliente Atributo de <code>basico.Cliente</code> a ser utilizado
     * para localizar os objetos da classe <code>VinculoVO</code>.
     * @return List Contendo todos os objetos da classe <code>VinculoVO</code>
     * resultantes da consulta.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public VinculoVO consultarVinculoPorCodigoColaboradorClienteTipoVinculo(Integer colaborador, Integer cliente, String tipoVinculo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM Vinculo WHERE colaborador = " + colaborador + " and cliente = " + cliente + " and tipovinculo = '" + tipoVinculo.toUpperCase() + "'";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new VinculoVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    
    @Override
    public List<VinculoVO> consultarPorClienteTipoVinculo(int cliente, String tipoVinculo, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Vinculo WHERE cliente = ? AND tipoVinculo = ? ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, cliente);
            sqlConsultar.setString(2, tipoVinculo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public VinculoVO consultarVinculoConsultorAtualCliente(Integer codigoCliente,int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from vinculo \n");
        sql.append("where tipoVinculo = '").append(TipoColaboradorEnum.CONSULTOR.getSigla()).append("' \n");
        sql.append(" and cliente = ").append(codigoCliente);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }
    
    @Override
    public VinculoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Vinculo WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Vinculo ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Consulta de suggestionBox que retorna o nome do colaborador que possui
     * vinculo na carteira e o codigo do colaborador para posterior consulta de
     * clientes vinculados pesquisa por empresa
     *
     * @param empresa
     * @return
     * @throws Exception
     */
    public List consultarTodosVinculosCarteiraComLimite(Integer empresa) throws Exception {
        StringBuffer sql = new StringBuffer();
        if (empresa == 0) {
            sql.append("select vinculo.colaborador, pessoa.nome from vinculo ");
            sql.append("INNER JOIN colaborador on colaborador.codigo = vinculo.colaborador ");
            sql.append("INNER JOIN pessoa on pessoa.codigo = colaborador.pessoa ");
            sql.append("group by pessoa.codigo, pessoa.nome, vinculo.colaborador order by pessoa.nome limit 50 ");
        } else {
            sql.append("select vinculo.colaborador, pessoa.nome from vinculo ");
            sql.append("INNER JOIN colaborador on colaborador.codigo = vinculo.colaborador ");
            sql.append("INNER JOIN pessoa on pessoa.codigo = colaborador.pessoa ");
            sql.append("inner join empresa on empresa.codigo = colaborador.empresa ");
            sql.append("where empresa.codigo = ");
            sql.append(empresa + " group by pessoa.codigo, pessoa.nome, vinculo.colaborador ORDER BY pessoa.nome limit 50");
        }
        List<VinculoVO> listaVinculos;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                listaVinculos = new ArrayList<VinculoVO>();
                while (tabelaResultado.next()) {
                    VinculoVO vinculoVO = new VinculoVO();
                    vinculoVO.setColaborador(new ColaboradorVO());
                    vinculoVO.getColaborador().setCodigo(tabelaResultado.getInt("colaborador"));
                    vinculoVO.getColaborador().setPessoa(new PessoaVO());
                    vinculoVO.getColaborador().getPessoa().setNome(tabelaResultado.getString("nome"));
                    listaVinculos.add(vinculoVO);
                }
            }
        }
        return listaVinculos;
    }

    /**
     * Método usado em SuggestionBox na tela de mailing pesquisando por nome dos
     * colaboradores com limite de 50
     *
     * @throws Exception
     */
    public List consultarPorNomeColaboradorVinculoCarteiraComLimite(Integer empresa, String valorConsulta) throws Exception {
        StringBuffer sql = new StringBuffer();
        if (empresa == 0) {
            sql.append("select vinculo.colaborador, pessoa.nome from vinculo ");
            sql.append("INNER JOIN colaborador on colaborador.codigo = vinculo.colaborador ");
            sql.append("INNER JOIN pessoa on pessoa.codigo = colaborador.pessoa ");
            sql.append("where pessoa.nome ilike '" + valorConsulta + "%' ");
            sql.append("group by pessoa.codigo, pessoa.nome, vinculo.colaborador order by pessoa.nome limit 50 ");
        } else {
            sql.append("select vinculo.colaborador, pessoa.nome from vinculo ");
            sql.append("INNER JOIN colaborador on colaborador.codigo = vinculo.colaborador ");
            sql.append("INNER JOIN pessoa on pessoa.codigo = colaborador.pessoa ");
            sql.append("inner join empresa on empresa.codigo = colaborador.empresa ");
            sql.append("where empresa.codigo = ");
            sql.append(empresa + "and pessoa.nome ilike '" + valorConsulta + "%' ");
            sql.append(" group by pessoa.codigo, pessoa.nome, vinculo.colaborador ORDER BY pessoa.nome limit 50 ");
            
        }
        List<VinculoVO> listaVinculos;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                listaVinculos = new ArrayList<VinculoVO>();
                while (tabelaResultado.next()) {
                    VinculoVO vinculoVO = new VinculoVO();
                    vinculoVO.setColaborador(new ColaboradorVO());
                    vinculoVO.getColaborador().setCodigo(tabelaResultado.getInt("colaborador"));
                    vinculoVO.getColaborador().setPessoa(new PessoaVO());
                    vinculoVO.getColaborador().getPessoa().setNome(tabelaResultado.getString("nome"));
                    listaVinculos.add(vinculoVO);
                }
            }
        }
        return listaVinculos;
    }

    /**
     * Responsável por inserir vinculo de consultor aos alunos que não o possuem
     * mas tem histórico. O vínculo é gerado com o último colaborador que tem
     * uma saída
     *
     * <AUTHOR> Alcides 28/09/2012
     */
    public int restaurarUltimoVinculoConsultor() throws Exception {
        String colaborador = "SELECT c.codigo, p.nome from colaborador c INNER JOIN pessoa p ON p.codigo = c.pessoa "
                + "WHERE c.codigo = (SELECT colaborador FROM historicovinculo WHERE cliente = %s "
                + "AND tipocolaborador LIKE 'CO' ORDER BY dataregistro DESC LIMIT 1 )";
        String sql = "select p.nome, c.matricula, c.codigo from cliente c, pessoa p where p.codigo = c.pessoa and c.codigo not in (select cliente from vinculo where tipovinculo like 'CO')";
        int nrVinculosCriados;
        try (ResultSet dados = con.prepareStatement(sql).executeQuery()) {
            nrVinculosCriados = 0;
            while (dados.next()) {
                try (ResultSet dadosCol = con.prepareStatement(String.format(colaborador, new Object[]{dados.getInt("codigo")})).executeQuery()) {
                    if (dadosCol.next()) {
                        VinculoVO vinculo = new VinculoVO();
                        vinculo.getCliente().setCodigo(dados.getInt("codigo"));
                        vinculo.getCliente().getPessoa().setNome(dados.getString("nome"));
                        vinculo.getColaborador().setCodigo(dadosCol.getInt("codigo"));
                        vinculo.getColaborador().getPessoa().setNome(dadosCol.getString("codigo"));
                        vinculo.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
                        incluir(vinculo, "RESTAURAR", null);
                        nrVinculosCriados++;
                    }
                }
            }
        }

        return nrVinculosCriados;
    }
    
    public List<VinculoVO> consultarTodosVinculosPorTipoVinculo(String tipoVinculo, int nivelMontarDados) throws Exception {
        String consulta = "SELECT\n"
                + "  *\n"
                + "FROM vinculo\n"
                + "WHERE tipovinculo = ?;";
        try (PreparedStatement preparedStatement = con.prepareStatement(consulta)) {
            preparedStatement.setString(1, tipoVinculo);
            try (ResultSet tabelaResultado = preparedStatement.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List<VinculoVO> consultarPorFiltroCarteiraListaClientes(String clientes, FiltroCarteiraTO filtroCarteira, int nivelMontarDados) throws Exception {
        return consultarPorFiltroCarteiraListaClientes(clientes, filtroCarteira, null, nivelMontarDados);
    }

    /**
     * @param quantidadeRegistroConsulta define a quantidade de registros deverão ser tragos na consulta. Caso informado <b>NULL</b>, haverá limite de quantidade.
     */
    public List<VinculoVO> consultarPorFiltroCarteiraListaClientes(String clientes, FiltroCarteiraTO filtroCarteira, Integer quantidadeRegistroConsulta, int nivelMontarDados) throws Exception {
        PreparedStatement ps = null;
        if (filtroCarteira.isClientesSemVinculo()) {

            if (filtroCarteira.getTipoVinculo().equals("TW")){
                StringBuilder sqlConsulta = new StringBuilder("SELECT\n"
                        + " vi.tipovinculo,sdw.empresacliente as codigo,sdw.codigocliente as cliente, vi.colaborador\n"
                        + "FROM situacaoclientesinteticodw sdw\n"
                        + "  INNER JOIN usuariomovel us ON us.cliente = sdw.codigocliente\n"
                        + "  LEFT JOIN vinculo vi ON vi.cliente = sdw.codigocliente AND vi.tipovinculo IN ('TW')\n"
                        + "  WHERE vi.codigo is null AND sdw.empresacliente = ? AND sdw.situacao='AT'\n"
                        + "  GROUP BY vi.tipovinculo, sdw.empresacliente,sdw.codigocliente,vi.colaborador \n");
                sqlConsulta.append( "ORDER BY random()");
                if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                    sqlConsulta.append("\nLIMIT ?");
                }

                ps = getCon().prepareStatement(sqlConsulta.toString());
                ps.setInt(1, filtroCarteira.getEmpresaVO().getCodigo());

                if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                    ps.setInt(2, quantidadeRegistroConsulta);
                }
            }else {
                StringBuilder sqlConsulta = new StringBuilder("SELECT\n"
                        + "vi.tipovinculo,cli.empresa as codigo,cli.codigo as cliente,vi.colaborador\n"
                        + "FROM cliente cli\n");
                if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                    String strVinculo = "'" + filtroCarteira.getTipoVinculo() + "'" +
                            (filtroCarteira.isDesconsiderarVinculoTreino() ? ",'" + TipoColaboradorEnum.PROFESSOR_TREINO.getSigla() + "'" : "");
                    sqlConsulta.append("  LEFT JOIN vinculo vi\n ON (vi.cliente = cli.codigo AND vi.tipovinculo IN (").append(strVinculo).append("))");
                } else {
                    sqlConsulta.append("  LEFT JOIN vinculo vi ON (vi.cliente = cli.codigo AND vi.tipovinculo = '").append(filtroCarteira.getTipoVinculo()).append("')");
                }
                sqlConsulta.append( " \nINNER JOIN situacaoclientesinteticodw sw\n"
                        + "    ON cli.codigo = sw.codigocliente\n"
                        + "WHERE 1 = 1\n"
                        + "      AND tipovinculo IS null\n"
                        + "      AND cli.empresa = ?\n");
                if (filtroCarteira.getTipoVinculo().equals("PR")) {
                    sqlConsulta.append("      AND cli.situacao = 'AT'\n");
                }
                verificarClientesSelecionados(clientes, sqlConsulta);
                sqlConsulta.append("GROUP BY cli.codigo, cli.matricula, sw.nomecliente,\n"
                        + "  cli.situacao, sw.datavigenciade, sw.codigocontrato, vi.colaborador, vi.tipovinculo, vi.cliente, vi.codigo\n"
                        + "ORDER BY random() ");
                if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                    sqlConsulta.append("\nLIMIT ?");
                }

                ps = getCon().prepareStatement(sqlConsulta.toString());
                ps.setInt(1, filtroCarteira.getEmpresaVO().getCodigo());

                if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                    ps.setInt(2, quantidadeRegistroConsulta);
                }
            }
        } else if (filtroCarteira.isClientesInconsistentes()) {
            StringBuilder sqlConsulta = new StringBuilder("SELECT\n"
                    + "  vi.*\n"
                    + "FROM cliente cli\n"
                    + "  LEFT JOIN vinculo vi\n"
                    + "    ON (vi.cliente = cli.codigo AND vi.tipovinculo = ?)\n"
                    + "  LEFT JOIN colaborador col\n"
                    + "    ON vi.colaborador = col.codigo\n"
                    + "  INNER JOIN situacaoclientesinteticodw sw\n"
                    + "    ON cli.codigo = sw.codigocliente\n"
                    + "WHERE 1 = 1\n"
                    + "      AND col.empresa <> cli.empresa\n"
                    + "      AND cli.empresa = ?");

            verificarClientesSelecionados(clientes, sqlConsulta);

            if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                sqlConsulta.append("LIMIT ?");
            }

            ps = getCon().prepareStatement(sqlConsulta.toString());
            ps.setString(1, filtroCarteira.getTipoVinculo());
            ps.setInt(2, filtroCarteira.getEmpresaLogado());

            if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                ps.setInt(3, quantidadeRegistroConsulta);
            }
        } else if (filtroCarteira.isConsultorCarteiraVazia() || filtroCarteira.isConsultorInativoIrregular()) {
            return consultarPorFiltroCarteiraListaClientesComum(clientes, filtroCarteira, quantidadeRegistroConsulta, nivelMontarDados);
        }
        try (ResultSet tabelaResultado = ps.executeQuery()) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
        } finally {
            if (ps != null && !ps.isClosed()) ps.close();
        }
    }
    
    public List<VinculoVO> consultarPorFiltroCarteiraListaClientesComum(String clientes, FiltroCarteiraTO filtroCarteira, Integer quantidadeRegistroConsulta, int nivelMontarDados) throws Exception {
        FaixaHorarioAcessoCliente faixas = new FaixaHorarioAcessoCliente(con);
        List<FaixaHorarioAcessoClienteVO> periodos = faixas.consultarFaixas();
        StringBuilder consulta = new StringBuilder("SELECT\n"
                + "  vi.*\n"
                + "FROM vinculo vi\n"
                + "  INNER JOIN situacaoclientesinteticodw sdw\n"
                + "    ON sdw.codigocliente = vi.cliente\n");
        CarteirasRel.montarFiltroSQLMaisAcessados(consulta, periodos, "sdw.codigocliente", filtroCarteira);
        consulta.append("WHERE 1 = 1\n");
        consulta.append("      AND tipovinculo = '").append(filtroCarteira.getTipoVinculo()).append("'\n");
        if (filtroCarteira.getEmpresaVO().getCodigo() > 0) {
            consulta.append("      AND sdw.empresacliente = ").append(filtroCarteira.getEmpresaVO().getCodigo()).append("\n");
        }
        if (filtroCarteira.getCodigoColaborador() > 0) {
            consulta.append("      AND colaborador = ").append(filtroCarteira.getCodigoColaborador()).append("\n");
        }
        if (!UteisValidacao.emptyString(filtroCarteira.getSituacaoCliente())) {

            String[] situacao = filtroCarteira.getSituacaoCliente().split(",");
            String situalcaoConsulta = "";
            for(int i=0; i<=situacao.length -1; i++){
                situalcaoConsulta += "'" + situacao[i] +"',";
            }
            situalcaoConsulta += "'&')";
            consulta.append("      AND (sdw.situacao in(").append(situalcaoConsulta);
            consulta.append("        OR sdw.situacaocontrato in(").append(situalcaoConsulta).append(")");

        }
        verificarClientesSelecionados(clientes, consulta);

        if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
            consulta.append("LIMIT ?;");
        }

        try (PreparedStatement preparedStatement = con.prepareStatement(consulta.toString())) {
            if (UteisValidacao.notEmptyNumber(quantidadeRegistroConsulta)) {
                preparedStatement.setInt(1, quantidadeRegistroConsulta);
            }
            try (ResultSet tabelaResultado = preparedStatement.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }
    
    private void verificarClientesSelecionados(String clientes, StringBuilder consulta) {
        if (clientes.trim().length() > 0) {
            consulta.append("      AND vi.cliente in (").append(clientes).append(")\n");
        }
    }
    
    public void removerVinculos(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO, boolean atualizarsintetico) throws Exception {
        try {
            con.setAutoCommit(false);
            removerVinculosSemCommit(vinculos, origem, usuarioVO, atualizarsintetico, null, null);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void removerVinculos(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO, boolean atualizarsintetico, Date dataRegistro) throws Exception {
        try {
            con.setAutoCommit(false);
            removerVinculosSemCommit(vinculos, origem, usuarioVO, atualizarsintetico, dataRegistro, null);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void removerVinculosSemCommit(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO, boolean atualizarsintetico, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        List<VinculoVO> vinculosValidos = validarExisteVinculoAntesExcluir(vinculos);
        excluirListaVinculos(vinculosValidos);
        List<Integer> clientesAtualizar = new ArrayList<>();
        for (VinculoVO vinculoVO : vinculosValidos) {
            HistoricoVinculoVO hist = new HistoricoVinculoVO(vinculoVO.getCliente().getCodigo(),
                    vinculoVO.getColaborador().getCodigo(),
                    "SD",
                    vinculoVO.getTipoVinculo(),
                    Calendario.hoje(),
                    origem,
                    usuarioVO);
            getFacade().getHistoricoVinculo().incluirSemCommit(hist, false);
            if (atualizarsintetico && !clientesAtualizar.contains(vinculoVO.getCliente().getCodigo())) {
                clientesAtualizar.add(vinculoVO.getCliente().getCodigo());
            }
        }
        if (!clientesAtualizar.isEmpty()) {
            atualizarSintetico(clientesAtualizar, sinteticoSincronizar);
        }
    }
    
    public void removerVinculosSemCommit(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO, boolean atualizarsintetico, Date dataRegistro, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        List<VinculoVO> vinculosValidos = validarExisteVinculoAntesExcluir(vinculos);
        excluirListaVinculos(vinculosValidos);
        List<Integer> clientesAtualizar = new ArrayList<Integer>();
        for (VinculoVO vinculoVO : vinculosValidos) {
            HistoricoVinculoVO hist = new HistoricoVinculoVO(vinculoVO.getCliente().getCodigo(),
                    vinculoVO.getColaborador().getCodigo(),
                    "SD",
                    vinculoVO.getTipoVinculo(),
                    dataRegistro == null ? Calendario.hoje() : dataRegistro,
                    origem,
                    usuarioVO);
            getFacade().getHistoricoVinculo().incluirSemCommit(hist, false);
            if (atualizarsintetico && !clientesAtualizar.contains(vinculoVO.getCliente().getCodigo())) {
                clientesAtualizar.add(vinculoVO.getCliente().getCodigo());
            }
        }
        if (!clientesAtualizar.isEmpty()) {
            atualizarSintetico(clientesAtualizar, sinteticoSincronizar);
        }
    }

    private List<VinculoVO> validarExisteVinculoAntesExcluir(List<VinculoVO> vinculos) {
        List<VinculoVO> vinculosValidos = new ArrayList<>();
        try {
            for(VinculoVO vinculo : vinculos){
                StringBuilder sql = new StringBuilder("SELECT codigo FROM Vinculo WHERE codigo = "+vinculo.getCodigo());
                ResultSet resultSet = criarConsulta(sql.toString(), con);
                if(resultSet.next()){
                    vinculosValidos.add(vinculo);
                }
            }
        }catch (Exception e){
            e.getStackTrace();
        }
        return vinculosValidos;
    }

    public void incluirVinculos(List<VinculoVO> vinculos, String origem, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);
            for (VinculoVO vinculoVO : vinculos) {
                if (!existe(vinculoVO) && !existeVinculosEspecificos(vinculoVO)) {
                    incluir(vinculoVO, null, origem, false, usuarioVO, null); // insere e atualiza o sintetico
                } else {
                    atualizarSintetico(vinculoVO, null); // apenas atualiza o sintetico
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void transferirVinculos(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir) throws Exception {
        try {
            con.setAutoCommit(false);
            removerVinculosSemCommit(vinculos, origem, usuarioVO, false, null, null);
            
            for (VinculoVO vinculoAntigo : vinculos) {
                VinculoVO vinculoNovo = new VinculoVO();
                vinculoNovo.setCliente(vinculoAntigo.getCliente());
                vinculoNovo.setTipoVinculo(vinculoAntigo.getTipoVinculo());
                vinculoNovo.setColaborador(colaborador);

                //Alternativa para não duplicar os vínculos
                if (!existe(vinculoNovo) && !existeVinculosEspecificos(vinculoNovo)) {
                    colaborador.getTipoColaborador();
                    if(vinculoNovo.getTipoVinculo().equals("")){
                        vinculoNovo.setTipoVinculo("PR");
                    }
                    incluir(vinculoNovo, null, origem, false, usuarioVO, null); // insere e atualiza o sintetico
                } else {
                    atualizarSintetico(vinculoNovo, null); // apenas atualiza o sintetico
                }
            }
            transferirResponsavelAgendas(agendasTransferir, colaborador, con);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void transferirVinculosConsultor(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir) throws Exception {
        transferirVinculos(vinculos, colaborador, origem, usuarioVO, agendasTransferir, TipoColaboradorEnum.CONSULTOR.getSigla());
    }

    public void transferirVinculos(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem,
                                   UsuarioVO usuarioVO, List<Integer> agendasTransferir, final String tipoVinculo) throws Exception {
        try {
            con.setAutoCommit(false);
            removerVinculosSemCommit(vinculos, origem, usuarioVO, false, null, null);
            SinteticoMsDTO sinteticoSincronizar = new SinteticoMsDTO();
            sinteticoSincronizar.setChave(DAO.resolveKeyFromConnection(con));
            sinteticoSincronizar.setUrlRequisitar(PropsService.getPropertyValue(sinteticoSincronizar.getChave(), PropsService.urlTreinoWeb));
            for (VinculoVO vinculoAntigo : vinculos) {
                VinculoVO vinculoNovo = new VinculoVO();
                vinculoNovo.setCliente(vinculoAntigo.getCliente());
                vinculoNovo.setTipoVinculo(vinculoAntigo.getTipoVinculo());
                vinculoNovo.setColaborador(colaborador);

                if(sinteticoSincronizar.getUsuariosAtualizar().size() == 500){
                    atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
                    sinteticoSincronizar.setUsuariosAtualizar(new ArrayList<>());
                }

                //Alternativa para não duplicar os vínculos
                if (!existe(vinculoNovo) && !existeVinculosEspecificos(vinculoNovo)) {
                    colaborador.getTipoColaborador();
                    if(vinculoNovo.getTipoVinculo().equals("")){
                        vinculoNovo.setTipoVinculo(tipoVinculo);
                    }
                    incluir(vinculoNovo, null, origem, false, usuarioVO, "true".equalsIgnoreCase(PropsService.getPropertyValue(PropsService.utilizarSinteticoMs))
                            ? sinteticoSincronizar : null); // insere e atualiza o sintetico
                } else {
                    atualizarSintetico(vinculoNovo, "true".equalsIgnoreCase(PropsService.getPropertyValue(PropsService.utilizarSinteticoMs))
                            ? sinteticoSincronizar : null); // apenas atualiza o sintetico
                }
            }
            transferirResponsavelAgendas(agendasTransferir, colaborador, con);
            atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void atualizarSinteticoTreinoSinteticoMs(SinteticoMsDTO sinteticoSincronizar) throws Exception {
        if("true".equalsIgnoreCase(PropsService.getPropertyValue(PropsService.utilizarSinteticoMs))) {
            Uteis.executeRequestSintetico(Uteis.getUrlDiscovery("sinteticoMs") + "/sintetico/addFila", sinteticoSincronizar.toJSON().toString(), new HashMap<>());
        }
    }

    public void transferirVinculosTreino(List<VinculoVO> vinculos, ColaboradorVO colaborador, String origem, UsuarioVO usuarioVO, List<Integer> agendasTransferir) throws Exception {
        try {
            con.setAutoCommit(false);

            String k = DAO.resolveKeyFromConnection(con);
            colaborador = getFacade().getColaborador().consultarPorChavePrimaria(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            TreinoWSConsumer.sincronizarProfessor(k, colaborador.toProfessorSintetico(), colaborador.getEmpresa().getCodigo());

            SinteticoMsDTO sinteticoSincronizar = new SinteticoMsDTO();
            sinteticoSincronizar.setChave(DAO.resolveKeyFromConnection(con));
            sinteticoSincronizar.setUrlRequisitar(PropsService.getPropertyValue(sinteticoSincronizar.getChave(), PropsService.urlTreinoWeb));

            List<VinculoVO> vinculosTreino = new ArrayList<>();
            //Validar Vínculo existente com o Treino
            for (VinculoVO vinculoVO : vinculos) {
                List<VinculoVO> vinculoVOs = consultarVinculoPorClienteColaboradorTipoVinculo(
                        vinculoVO.getCliente().getCodigo(),
                        TipoColaboradorEnum.PROFESSOR_TREINO.getSigla(),
                        Uteis.NIVELMONTARDADOS_MINIMOS);

                vinculosTreino.addAll(vinculoVOs);
            }

            removerVinculosSemCommit(vinculosTreino, origem, usuarioVO, false, sinteticoSincronizar);
            if (sinteticoSincronizar.getUsuariosAtualizar().size() >= 500) {
                atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
                sinteticoSincronizar.setUsuariosAtualizar(new ArrayList<>());
            }

            for (VinculoVO vinculoAntigo : vinculos) {

                VinculoVO vinculoNovo = new VinculoVO();
                vinculoNovo.setCliente(vinculoAntigo.getCliente());
                vinculoNovo.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
                vinculoNovo.setColaborador(colaborador);

                if (sinteticoSincronizar.getUsuariosAtualizar().size() >= 500) {
                    atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
                    sinteticoSincronizar.setUsuariosAtualizar(new ArrayList<>());
                }

                //Alternativa para não duplicar os vínculos
                if (!existe(vinculoNovo) && (!existeVinculosRestantes(vinculoAntigo) || !tipoVinculoEhIgual(vinculoAntigo))) {
                    incluir(vinculoNovo, null, origem, false, usuarioVO, sinteticoSincronizar); // insere e atualiza o sintetico
                } else {
                    atualizarSintetico(vinculoNovo, sinteticoSincronizar); // apenas atualiza o sintetico
                }
            }
            transferirResponsavelAgendas(agendasTransferir, colaborador, con);
            atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Realiza a alteração do <code>AgendaVO.colaboradorResponsavel</code>
     * @param agendasTransferir Códigos das {@link negocio.comuns.crm.AgendaVO} que se deseja alterar o <code>colaboradorResponsavel</code>
     * @param colaborador Qual {@link ColaboradorVO} para qual as agendas serão transferidas.
     */
    private void transferirResponsavelAgendas(List<Integer> agendasTransferir, ColaboradorVO colaborador, Connection con) throws  Exception{
        if(agendasTransferir != null && !agendasTransferir.isEmpty()) {
            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            UsuarioVO usuario = getFacade().getUsuario().consultarPorCodigoPessoa(colaboradorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(usuario == null || usuario.getCodigo() == 0){
                throw  new Exception("O colaborador não possui um usuário cadastrado no sistema para poder transferir os vinculos futuros");
            }
            Agenda agendaDAO = new Agenda(con);
            agendaDAO.alterarConsultorResponsavelAgenda(agendasTransferir, usuario);
            agendaDAO = null;
        }
    }

    private boolean existe(VinculoVO vinculoVO) throws Exception {
        String sql = "SELECT\n"
                + "  codigo\n"
                + "FROM vinculo\n"
                + "WHERE 1 = 1\n"
                + "      AND cliente = " + vinculoVO.getCliente().getCodigo() + "\n"
                + "      AND colaborador = " + vinculoVO.getColaborador().getCodigo() + "\n"
                + "      AND tipovinculo = '" + vinculoVO.getTipoVinculo() + "'";

        return existe(sql, this.con);
    }

    private boolean existeVinculosEspecificos(VinculoVO vinculosAntigo) throws Exception {
        if (vinculosAntigo.getTipoVinculo().equals("TW")){
            return existeVinculosRestantes(vinculosAntigo);
        }
        return false;
    }

    private boolean existeVinculosRestantes(VinculoVO vinculosAntigo) throws Exception {
        String sql = "SELECT\n"
                + "count(*) as total\n"
                + "FROM vinculo\n"
                + "WHERE 1 = 1\n"
                + "      AND cliente = " + vinculosAntigo.getCliente().getCodigo() + "\n"
                + "      AND tipovinculo = '" + vinculosAntigo.getTipoVinculo() + "'";

        return verificarExistenciaDeVinculos(sql, this.con);
    }

    private boolean tipoVinculoEhIgual(VinculoVO vinculoVO){
       return vinculoVO.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
    }

    private List<VinculoVO> consultarVinculoPorClienteColaboradorTipoVinculo(Integer codCliente, String tipoVinculo, int nivelMontarDados) throws Exception {
        String sql = "SELECT\n"
                + "  *\n"
                + "FROM vinculo\n"
                + "WHERE 1 = 1\n"
                + "      AND cliente = " + codCliente + "\n"
                + "      AND tipovinculo = '" + tipoVinculo + "'";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = preparedStatement.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }
    
    private void excluirListaVinculos(List<VinculoVO> vinculos) throws Exception {
        if (vinculos.size() > 0) {
            StringBuilder sql = new StringBuilder("DELETE FROM Vinculo WHERE codigo in (");
            StringBuilder codigos = new StringBuilder();
            for (VinculoVO vinculoVO : vinculos) {
                codigos.append(",").append(vinculoVO.getCodigo());
            }
            codigos.deleteCharAt(0);
            sql.append(codigos.toString()).append(");");
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
                sqlExcluir.execute();
            }
        }
    }
    
    public boolean existeVinculoCodigoColaboradorTipoVinculo(Integer colaborador, String tipoVinculo) throws Exception {
        String sql = "SELECT * FROM Vinculo WHERE colaborador = " + colaborador + " and tipovinculo = '" + tipoVinculo.toUpperCase() + "'";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return true;
                }
            }
        }
        return false;
    }
    
    @Override
    public List<Integer> obterNumeroNovosNaCarteira(Integer empresa, Date inicio, Date fim, Integer professor, boolean novos) throws Exception {
        List<Integer> lista;
        try (ResultSet rs = criarConsulta(getSqlNovosCarteira(empresa, inicio, fim, professor, novos).toString(), con)) {
            lista = new ArrayList<Integer>();
            while (rs.next()) {
                lista.add(rs.getInt("codigo"));
            }
        }
        return lista;
    } 
    
    public StringBuilder getSqlNovosCarteira(Integer empresa, Date inicio, Date fim, Integer professor, boolean novos) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT distinct cli.codigo ");
        sql.append(" FROM vinculo vi \n");
        sql.append(" INNER JOIN cliente cli ON cli.codigo = vi.cliente AND cli.empresa = ").append(empresa).append(" \n");
        sql.append(" INNER JOIN historicovinculo hv ON vi.tipovinculo = hv.tipocolaborador  \n");
        sql.append(" AND hv.tipohistoricovinculo = 'EN'  \n");
        sql.append(" AND vi.cliente = hv.cliente  \n");
        sql.append(" AND vi.colaborador = hv.colaborador  \n");
        sql.append(" AND vi.colaborador = ").append(professor).append("\n");
        sql.append(" AND vi.tipovinculo = 'TW' \n");
        sql.append(" AND hv.dataregistro::DATE BETWEEN '").append(Uteis.getDataJDBC(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBC(fim)).append("'\n");
        if(novos){
            sql.append(" AND NOT EXISTS ");
        }else{
            sql.append(" AND EXISTS ");
        }
        sql.append("(SELECT codigo FROM historicovinculo WHERE tipohistoricovinculo = 'SD'  AND historicovinculo.tipocolaborador = 'TW' AND historicovinculo.cliente = vi.cliente)\n");
        return sql;
    }

    public void alterarVinculoConsultor(Integer codigoEmpresa, UsuarioVO usuarioVO, ColaboradorVO novoColaborador)throws Exception{
        StringBuilder sql = new StringBuilder();
        List<VinculoVO> listaClienteVinculo = consultarVinculosUsuarioTipoConsultor(usuarioVO.getCodigo(), codigoEmpresa,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (VinculoVO vinculoVO : listaClienteVinculo){
            List<VinculoVO> listaVinculo = consultarPorCodigoCliente(vinculoVO.getCliente().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS,false);
            for (VinculoVO obj: listaVinculo){
                if (obj.getTipoVinculo().equals("CO")){
                    obj.setCodigo(0);
                    obj.setNovoObj(true);
                    obj.setColaborador(novoColaborador);
                }
            }
            alterarVinculo(vinculoVO.getCliente().getCodigo(), usuarioVO.getUsuarioLogado(),listaVinculo,"CADASTRO USUARIO");
        }
        /*sql.append("update vinculo  \n");
        sql.append("set colaborador = ").append(codigoColaboradorConsultor).append(" \n");
        sql.append("where colaborador = (  \n");
        sql.append("	select colEmp.codigo  \n");
        sql.append("	from colaborador col  \n");
        sql.append("	inner join usuario us on us.colaborador = col.codigo  \n");
        sql.append("	inner join colaborador colEmp on colEmp.pessoa = col.pessoa  \n");
        sql.append("	where colEmp.empresa = ").append(codigoEmpresa).append(" and us.codigo = ").append(codigoUsuario).append(" ) \n");
        sql.append("and tipoVinculo = 'CO'  \n");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.execute();*/
    }

    public List<VinculoVO> consultarVinculosUsuarioTipoConsultor(Integer codigoUsuario, Integer codigoEmpresa ,int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select v.*  \n");
        sql.append("from vinculo v   \n");
        sql.append("inner join cliente cli on cli.codigo = v.cliente   \n");
        sql.append("inner join pessoa p on p.codigo = cli.pessoa   \n");
        sql.append("where v.colaborador = (  \n");
        sql.append("	select colEmp.codigo  \n");
        sql.append("	from colaborador col  \n");
        sql.append("	inner join usuario us on us.colaborador = col.codigo  \n");
        sql.append("	inner join colaborador colEmp on colEmp.pessoa = col.pessoa  \n");
        sql.append("	where colEmp.empresa = ").append(codigoEmpresa).append(" and us.codigo = ").append(codigoUsuario).append(" ) \n");
        sql.append("and tipoVinculo = 'CO'  \n");
        sql.append("order by p.nome");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, con);
            }
        }
    }
    
    public static JSONArray montarArray(ResultSet rs) throws Exception {
        JSONArray json = new JSONArray();
        while(rs.next()){
            JSONObject obj = new JSONObject();
            obj.put("matricula", rs.getString("matricula"));
            obj.put("nome", rs.getString("nome"));
            obj.put("codigoPessoa", rs.getInt("codigopessoa"));
            obj.put("nomeProfessor", rs.getString("nomeprofessor"));
            try{
                obj.put("vigenciaate", Uteis.getData(rs.getDate("datavigenciaateajustada")));
                obj.put("codigoProfessor", rs.getString("codigoProfessor"));
                String modalidades = rs.getString("modalidades");
                String[] splitModalidades = modalidades.split("\\|");
                JSONArray modalidadesJSON = new JSONArray();
                for (String mod : splitModalidades) {
                    JSONObject o = new JSONObject();
                    o.put("codigoModalidade", 0);
                    o.put("nome", mod);
                    modalidadesJSON.put(o);
                }
                obj.put("modalidades", modalidadesJSON);
            }catch(Exception e){
                obj.put("modalidades", new JSONArray());   
            }
            json.put(obj);
        }
        return json;
    }
    
    @Override
    public List<VinculoVO> consultarVinculosDiferentesDeConsultorPorCliente(int cliente,int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Vinculo WHERE cliente = ? AND tipoVinculo <> 'CO' ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, cliente);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public List<String> existeVinculoCodigoColaborador(Integer colaborador) throws Exception {
        StringBuilder sql = new StringBuilder();

        List<String> l = new ArrayList<String>();

        sql.append(" select empresa.nome");
        sql.append(" from Vinculo");
        sql.append(" Inner join Cliente on vinculo.cliente = cliente.codigo");
        sql.append(" inner join empresa on cliente.empresa = empresa.codigo");
        sql.append(" where vinculo.colaborador = ").append(colaborador);
        sql.append(" group by empresa.nome");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {

                while (tabelaResultado.next()) {
                    l.add(tabelaResultado.getString("nome"));
                }
            }
        }

        return l;
    }



    @Override
    public VinculoVO consultarVinculoConsultorAtualColaborador(Integer codigoColaborador, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from vinculo \n");
        sql.append("where tipoVinculo = '").append(TipoColaboradorEnum.CONSULTOR.getSigla()).append("' \n");
        sql.append(" and colaborador = ").append(codigoColaborador);
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    @Override
    public String consultarEmpresasVinculadas(Integer codigoColaborador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(1) total, e.nome\n");
        sql.append("from vinculo v inner join cliente c on c.codigo  = v.cliente inner join empresa e on e.codigo  = c.empresa \n");
        sql.append(" and colaborador = ").append(codigoColaborador);
        sql.append("group  by e.codigo ");
        StringBuilder empresas = new StringBuilder();
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                while (rs.next()) {
                    empresas.append( montarVincloEmpresasDados(rs));
                    empresas.append(", ");
                }
            }
        }
        return empresas.toString();
    }

    public boolean jaExisteVinculoEntreOClienteEColaborador (Integer codigoColaborador, Integer codigoCliente) throws Exception{
        String sql = "SELECT v.codigo FROM vinculo v WHERE v.colaborador = " + codigoColaborador + " AND v.cliente = " + codigoCliente;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return true;
            }
            return false;
        }
    }

}
