package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Risco;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.ClienteMensagemInterfaceFacade;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>ClienteMensagemVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>ClienteMensagemVO</code>. Encapsula toda a interação com o banco de
 * dados.
 *
 * @see ClienteMensagemVO
 * @see SuperEntidade
 */
public class ClienteMensagem extends SuperEntidade implements ClienteMensagemInterfaceFacade {

    public ClienteMensagem() throws Exception {
        super();
        setIdEntidade("Cliente");
    }

    public ClienteMensagem(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Cliente");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ClienteMensagemVO</code>.
     */
    public ClienteMensagemVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ClienteMensagemVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ClienteMensagemVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ClienteMensagemVO</code> que será
     * gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(ClienteMensagemVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ClienteMensagemVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ClienteMensagemVO</code> que será
     * gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemCommit(ClienteMensagemVO obj) throws Exception {
        ClienteMensagemVO.validarDados(obj);
        incluir(getIdEntidade());
        String sql = "INSERT INTO ClienteMensagem( mensagem, cliente, tipomensagem, usuario, databloqueio, bloqueio, "
                + "questionariocliente, movparcela, pesorisco, produto, datadesbloqueio, bloqueiocheque, dataRegistro, codigoOperacaoColetiva) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getMensagem());
        if (obj.getCliente().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getCliente().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setString(3, obj.getTipomensagem().getSigla());
        if (obj.getUsuario().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getUsuario().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }
        if (obj.getTipomensagem().equals(TiposMensagensEnum.CATRACA)) {
            if (obj.getDataBloqueio() != null) {
                sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataBloqueio()));
            } else {
                sqlInserir.setNull(5, 0);
            }
        } else {
            sqlInserir.setNull(5, 0);
        }
        sqlInserir.setBoolean(6, obj.getBloqueio());
        if (obj.getQuestionarioCliente().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getQuestionarioCliente().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        if (obj.getMovParcela().getCodigo() != 0) {
            sqlInserir.setInt(8, obj.getMovParcela().getCodigo());
        } else {
            sqlInserir.setNull(8, 0);
        }
        if (obj.getRisco().getPeso() != 0) {
            sqlInserir.setInt(9, obj.getRisco().getPeso());
        } else {
            sqlInserir.setNull(9, 0);
        }
        if (obj.getProduto().getCodigo() != 0) {
            sqlInserir.setInt(10, obj.getProduto().getCodigo());
        } else {
            sqlInserir.setNull(10, 0);
        }
        sqlInserir.setDate(11, Uteis.getDataJDBC(obj.getDataDesbloqueio()));

        sqlInserir.setBoolean(12, obj.isBloqueioCheque());
        sqlInserir.setTimestamp(13, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setInt(14, obj.getCodigoOperacaoColetiva());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ClienteMensagemVO</code> que será
     * alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void alterar(ClienteMensagemVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(ClienteMensagemVO obj) throws Exception {
        ClienteMensagemVO.validarDados(obj);
        alterar(getIdEntidade());
        //obj.realizarUpperCaseDados();
        String sql = "UPDATE ClienteMensagem set mensagem=?, cliente=?, tipomensagem=?, usuario=?, databloqueio=?, bloqueio=?, "
                + "questionariocliente=?, movparcela=?, pesorisco=?, produto=?, datadesbloqueio=?, dataAtualizacao = ?, codigoOperacaoColetiva = ? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getMensagem());
        if (obj.getCliente().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getCliente().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setString(3, obj.getTipomensagem().getSigla());
        if (obj.getUsuario().getCodigo() != 0) {
            sqlAlterar.setInt(4, obj.getUsuario().getCodigo());
        } else {
            sqlAlterar.setNull(4, 0);
        }
        sqlAlterar.setDate(5, Uteis.getDataJDBC(obj.getDataBloqueio()));
        sqlAlterar.setBoolean(6, obj.getBloqueio());
        if (obj.getQuestionarioCliente().getCodigo() != 0) {
            sqlAlterar.setInt(7, obj.getQuestionarioCliente().getCodigo());
        } else {
            sqlAlterar.setNull(7, 0);
        }
        if (obj.getMovParcela().getCodigo() != 0) {
            sqlAlterar.setInt(8, obj.getMovParcela().getCodigo());
        } else {
            sqlAlterar.setNull(8, 0);
        }
        if (obj.getRisco().getPeso() != 0) {
            sqlAlterar.setInt(9, obj.getRisco().getPeso());
        } else {
            sqlAlterar.setNull(9, 0);
        }
        if (obj.getProduto().getCodigo() != 0) {
            sqlAlterar.setInt(10, obj.getProduto().getCodigo());
        } else {
            sqlAlterar.setNull(10, 0);
        }
        sqlAlterar.setDate(11, Uteis.getDataJDBC(obj.getDataDesbloqueio()));
        sqlAlterar.setTimestamp(12, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlAlterar.setInt(13, obj.getCodigoOperacaoColetiva());

        sqlAlterar.setInt(14, obj.getCodigo());
        sqlAlterar.execute();
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ClienteMensagemVO</code> que será
     * alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterarMensagemCadastroCliente(ClienteMensagemVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ClienteMensagemVO.validarDados(obj);
            alterar(getIdEntidade());
            //obj.realizarUpperCaseDados();
            String sql = "UPDATE ClienteMensagem set mensagem=?, dataAtualizacao = ? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 1;
            sqlAlterar.setString(i++, obj.getMensagem());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            obj.setNovoObj(false);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsáel por excluir no BD um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ClienteMensagemVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(ClienteMensagemVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM ClienteMensagem WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            int i = 1;
            sqlExcluir.setInt(i, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param cliente Objeto da classe <code>ClienteMensagemVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirClienteMensagemPorClienteCadastroIncompleto(Integer cliente, boolean controlarAcesso) throws Exception {
        if(controlarAcesso){
            excluir(getIdEntidade());
        }
        String sql = "DELETE FROM ClienteMensagem WHERE ((cliente = ? and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "'))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, cliente);
        sqlExcluir.execute();
    }

    public List<ClienteMensagemVO> consultarPorCodigoOperacaoColetiva(Integer valorConsulta,
                                                                      boolean controlarAcesso,
                                                                      boolean whereDataDesbloqueioUltrapassada,
                                                                      int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        StringBuilder sqlStr = new StringBuilder("SELECT * FROM ClienteMensagem WHERE codigoOperacaoColetiva = " + valorConsulta);

        if (whereDataDesbloqueioUltrapassada) {
            sqlStr.append(" AND dataDesbloqueio <= CURRENT_DATE");
        }

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }


    public void excluirPorCodigoOperacaoColetiva(Integer codigoOperacaoColetiva) throws Exception {
        String sql = "DELETE FROM ClienteMensagem WHERE codigoOperacaoColetiva = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codigoOperacaoColetiva);
            sqlExcluir.executeUpdate();
        }
    }

    public void excluirMultiplasClienteMensagem(List<ClienteMensagemVO> listaClienteMensagemParaExcluir) throws Exception {
        if (listaClienteMensagemParaExcluir == null || listaClienteMensagemParaExcluir.isEmpty()) {
            return;
        }

        StringBuilder sql = new StringBuilder("DELETE FROM ClienteMensagem WHERE codigo IN (");
        for (int i = 0; i < listaClienteMensagemParaExcluir.size(); i++) {
            sql.append("?");
            if (i < listaClienteMensagemParaExcluir.size() - 1) {
                sql.append(", ");
            }
        }
        sql.append(")");

        try (PreparedStatement stmt = con.prepareStatement(sql.toString())) {
            for (int i = 0; i < listaClienteMensagemParaExcluir.size(); i++) {
                stmt.setInt(i + 1, listaClienteMensagemParaExcluir.get(i).getCodigo());
            }
            stmt.executeUpdate();
        }
    }


    @Override
    public void excluirClienteMensagemBVsPendentes(Integer colaborador, Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        if (dataInicial == null && dataFinal != null) {
            throw new Exception("Informe a data inicial");
        }
        if (dataFinal == null && dataInicial != null) {
            throw new Exception("Informe a data final");
        }
        if (dataFinal != null && dataInicial != null && dataInicial.after(dataFinal)) {
            throw new Exception("A data inicial deve ser menor que a data final");
        }
        int i = 0;
        String sql = "DELETE FROM clientemensagem "
                + " WHERE codigo in"
                + " (select distinct clientemensagem.codigo from clientemensagem "
                + " left join vinculo on vinculo.cliente = clientemensagem.cliente"
                + " left join cliente on cliente.codigo = vinculo.cliente"
                + " left join pessoa on pessoa.codigo = cliente.pessoa "
                + "inner join questionariocliente on questionariocliente.codigo = clientemensagem.questionariocliente "
                + "where clientemensagem.tipomensagem ilike 'BP' ";
        if (dataInicial != null && dataFinal != null) {
            sql += " and (questionariocliente.data between ? and ? )";
        }
        if (colaborador != null && colaborador > 0) {
            sql += " and vinculo.colaborador = ?";
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += " and cliente.empresa = ?";
        }
        sql += ")";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        if (dataInicial != null && dataFinal != null) {
            sqlExcluir.setTimestamp(++i, Uteis.getDataHoraJDBC(dataInicial, "00:00:00"));
            sqlExcluir.setTimestamp(++i, Uteis.getDataHoraJDBC(dataFinal, "23:59:59"));
        }
        if (colaborador != null && colaborador > 0) {
            sqlExcluir.setInt(++i, colaborador);
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlExcluir.setInt(++i, empresa);
        }
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param cliente Objeto da classe <code>ClienteMensagemVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirClienteMensagemPorClienteQuestionario(Integer cliente, Integer questionario) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ClienteMensagem WHERE ((cliente = ? and questionariocliente = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i++, cliente);
        sqlExcluir.setInt(i++, questionario);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param movParcela Objeto da classe <code>ClienteMensagemVO</code> que
     * será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirClienteMensagemPorMovParcela(Integer movParcela) throws Exception {
        String sql = "DELETE FROM ClienteMensagem WHERE movParcela = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i, movParcela);
        sqlExcluir.execute();
    }


    public void excluirClienteMensagemPorMovParcelaContrato(Integer contrato) throws Exception {
        String sql = "DELETE FROM ClienteMensagem WHERE movParcela IN (SELECT codigo FROM movparcela WHERE contrato = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i, contrato);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param pesoRisco Objeto da classe <code>ClienteMensagemVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirClienteMensagemRiscoPeso(Integer pesoRisco) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ClienteMensagem WHERE ((cliente = ? and pesorisco = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 1;
        sqlExcluir.setInt(i, pesoRisco);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ClienteMensagemVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param cliente Objeto da classe <code>ClienteMensagemVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluirClienteMensagemRisco(Integer cliente) throws Exception {
        String sql = "DELETE FROM ClienteMensagem WHERE ((cliente = ? and tipomensagem = '"
                + TiposMensagensEnum.RISCO.getSigla() + "'))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, cliente);
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    public List<ClienteMensagemVO> consultarPorCliente(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public Boolean consultarClienteMensagemPorClienteCadastroIncompleto(Integer valorConsulta, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + valorConsulta + " and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "' ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public Boolean consultarClienteMensagemPorClienteQuestionarioCliente(Integer cliente, Integer questionarioCliente, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + cliente
                + " and questionarioCliente = " + questionarioCliente + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public Boolean consultarClienteMensagemPorMovParcela(Integer movParcela, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE movParcela = " + movParcela + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    public void consultarClienteTipoMensagemConsultor(int codigoCliente, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + codigoCliente + " and tipomensagem = '" +TiposMensagensEnum.AVISO_CONSULTOR.getSigla()+"'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()){
            throw new ConsistirException("Já existe uma mensagem para o Consultor!");
        }
    }

    public Boolean consultarClienteMensagemPorProdutoVencido(Integer cliente, Integer produto, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + cliente
                + " and produto = " + produto
                + " and tipomensagem = '" + TiposMensagensEnum.PRODUTO_VENCIDO.getSigla() + "'"
                + " and desabilitado = false "
                + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido.
     *
     * @param cliente Indica o código do cliente a ser pesquisado
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return Boolean Retorna true caso exista mensagem de risco cadastrada
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public Boolean consultarPorCodigoClienteRiscoMaiorQueSeis(Integer cliente, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + cliente
                + " and tipomensagem = '" + TiposMensagensEnum.RISCO.getSigla() + "' ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido.
     *
     * @param cliente Indica o código do cliente a ser pesquisado
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return Boolean Retorna o codigo da mensagem de cadastro incompleto
     * resultado da consulta ao banco para se existir poder alterar os dados
     * desse registro
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemDadosIncompletosPorCodigo(Integer cliente, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT codigo FROM ClienteMensagem WHERE cliente = " + cliente
                + " and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "' ORDER BY codigo";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido.
     *
     * @param cliente Indica o código do cliente a ser pesquisado
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return Boolean Retorna o codigo da mensagem de cadastro incompleto
     * resultado da consulta ao banco para se existir poder alterar os dados
     * desse registro
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemRiscoPorCodigo(Integer cliente, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT codigo FROM ClienteMensagem WHERE cliente = " + cliente
                + " and tipomensagem = '" + TiposMensagensEnum.RISCO.getSigla() + "' ORDER BY codigo";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return ResultSet Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaMovParcela(Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT COUNT (DISTINCT (clienteMensagem.cliente))  as qtd FROM clienteMensagem "
                + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                + "left join vinculo on vinculo.cliente = cliente.codigo "
                + "WHERE  cliente.empresa = " + empresa
                + " and clienteMensagem.tipoMensagem= '" + TiposMensagensEnum.PARCELA_ATRASO.getSigla() + "'";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public Boolean consultarPendenciaClienteMensagemRiscoPeso(Integer cliente, Integer peso, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        Statement stm = con.createStatement();
        String sqlStr = "SELECT codigo FROM ClienteMensagem WHERE cliente = " + cliente
                + " and tipomensagem = '" + TiposMensagensEnum.RISCO.getSigla() + "' "
                + "and pesoRisco =" + peso + " ORDER BY codigo";
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarClienteMensagemPorEmpresaMovParcela(Integer empresa, String sql, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        if (sql.equals("")) {
            sqlStr = "SELECT DISTINCT on (clienteMensagem.cliente) clienteMensagem.cliente as cli , contrato.codigo as contrato FROM clienteMensagem "
                    + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                    + "left join vinculo on vinculo.cliente = cliente.codigo "
                    + "left join contrato on cliente.pessoa = contrato.pessoa "
                    + "WHERE  cliente.empresa = " + empresa
                    + " and clienteMensagem.tipoMensagem= '" + TiposMensagensEnum.PARCELA_ATRASO.getSigla() + "'"
                    + "group by cli, contrato";
        } else {
            sqlStr = "SELECT DISTINCT on (clienteMensagem.cliente) clienteMensagem.cliente as cli , contrato.codigo as contrato FROM clienteMensagem "
                    + "inner join cliente on clienteMensagem.cliente = cliente.codigo "
                    + "inner join vinculo on vinculo.cliente = cliente.codigo and (" + sql + ") "
                    + "left join contrato on cliente.pessoa = contrato.pessoa "
                    + "WHERE  cliente.empresa = " + empresa
                    + " and clienteMensagem.tipoMensagem= '" + TiposMensagensEnum.PARCELA_ATRASO.getSigla() + "'"
                    + "group by cli, contrato";
        }
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet contarClienteMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT COUNT (DISTINCT (clienteMensagem.cliente)) as qtd FROM clienteMensagem "
                + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                + "left join vinculo on vinculo.cliente = cliente.codigo "
                + "WHERE  cliente.empresa = " + empresa
                + " and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "'"
                + " and cliente.situacao = 'AT'";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet contarVisitantesMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT COUNT (DISTINCT (clienteMensagem.cliente)) as qtd FROM clienteMensagem "
                + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                + "left join vinculo on vinculo.cliente = cliente.codigo "
                + "WHERE  cliente.empresa = " + empresa
                + " and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "'"
                + " and cliente.situacao = 'VI'";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet consultarVisitantesMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM clienteMensagem "
                + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                + "left join vinculo on vinculo.cliente = cliente.codigo "
                + "WHERE  cliente.empresa = " + empresa
                + " and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "'"
                + " and cliente.situacao = 'VI'";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet consultarClientesMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM clienteMensagem "
                + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                + "left join vinculo on vinculo.cliente = cliente.codigo "
                + "WHERE  cliente.empresa = " + empresa
                + " and tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "'"
                + " and cliente.situacao = 'AT'";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet consultarClienteMensagemCadastroIncompleto(Integer empresa, String sql, boolean controlarAcesso,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT * FROM  (");
           sqlStr.append("SELECT DISTINCT on (clienteMensagem.cliente) clienteMensagem.cliente as cli , dw.codigocontrato as codContrato,mensagem, ");
           sqlStr.append("dw.nomecliente as nome ,dw.nomePlano,dw.codigopessoa as codPessoa, dw.matricula as matriculacli, mensagem,dw.situacao as situacaoCliente, \n");
           sqlStr.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf\n");
           sqlStr.append("FROM clienteMensagem ");
           sqlStr.append(" INNER JOIN situacaoclientesinteticodw dw on dw.codigocliente = clienteMensagem.cliente ");
           if (sql.equals("")) {
               sqlStr.append("left join vinculo on vinculo.cliente = dw.codigocliente ");
           } else {
               sqlStr.append("inner join vinculo on vinculo.cliente = dw.codigocliente and (" + sql + ") ");
           }
           sqlStr.append(" LEFT JOIN Contrato ct ON  ct.codigo = dw.codigocontrato\n");
           sqlStr.append(" LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
           sqlStr.append(" WHERE ");
           if(empresa != 0){
               sqlStr.append(" dw.empresacliente = " + empresa);
               sqlStr.append(" and ");
           }
           sqlStr.append(" tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "' ");
           sqlStr.append(" and dw.situacao='AT'\n");

           sqlStr.append(" group by cli,matriculaCli,dw.nomePlano,dw.telefonescliente,codPessoa, codContrato, mensagem,situacaoCliente, nome,dataInicio,dataFim,duracaoContrato,ct.nomemodalidades,dw.cpf\n");
        sqlStr.append(") consulta ");
        if(paginacao != null && paginacao.getOrdernar()){
            sqlStr.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            sqlStr.append(" LIMIT ").append(paginacao.getItensPorPagina()).append(" OFFSET ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }

        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr.toString());
    }

    public ResultSet consultarVisitanteMensagemCadastroIncompleto(Integer empresa, String sql, boolean controlarAcesso,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT * FROM  (");
        sqlStr.append("SELECT DISTINCT on (clienteMensagem.cliente) clienteMensagem.cliente as cli , dw.codigocontrato as codContrato,mensagem, ");
        sqlStr.append("dw.nomecliente as nome ,dw.nomePlano,dw.codigopessoa as codPessoa, dw.matricula as matriculaCli, mensagem,dw.situacao as situacaoCliente, \n");
        sqlStr.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf\n");
        sqlStr.append("FROM clienteMensagem ");
        sqlStr.append("INNER JOIN Situacaoclientesinteticodw dw on dw.codigocliente = clienteMensagem.cliente ");
        if (sql.equals("")) {
            sqlStr.append("left join vinculo on vinculo.cliente = dw.codigocliente ");
        } else {
            sqlStr.append("inner join vinculo on vinculo.cliente = dw.codigocliente and (" + sql + ") ");
        }
        sqlStr.append(" LEFT JOIN Contrato ct ON  ct.codigo = dw.codigocontrato\n");
        sqlStr.append(" LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        sqlStr.append(" WHERE ");

        if(empresa != 0){
            sqlStr.append(" dw.empresacliente = " + empresa);
            sqlStr.append(" and ");
        }

        sqlStr.append(" tipomensagem = '" + TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla() + "' ");
        sqlStr.append(" and dw.situacao='VI'\n");
        if (dataBaseInicio != null) {
            StringBuilder subSQL = new StringBuilder();
            subSQL.append(" AND EXISTS(");
            subSQL.append("SELECT cli.codigo FROM cliente cli ");
            subSQL.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ");
            subSQL.append("WHERE cli.codigo = dw.codigocliente AND pes.datacadastro >= '" + Uteis.getData(dataBaseInicio) + " 00:00:00'");
            subSQL.append(") ");
            sqlStr.append(subSQL.toString());
        }
        sqlStr.append(" group by cli,matriculaCli,nomePlano,codPessoa,dw.nomePlano,dw.telefonescliente,codContrato, mensagem,situacaoCliente, nome,dataInicio,dataFim,duracaoContrato,ct.nomemodalidades,dw.cpf\n");
        sqlStr.append(") consulta ");
        if(paginacao != null && paginacao.getOrdernar()){
            sqlStr.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            sqlStr.append(" LIMIT ").append(paginacao.getItensPorPagina()).append(" OFFSET ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr.toString());
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaQuestionarioCliente(Integer empresa, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT COUNT (DISTINCT (clienteMensagem.cliente))  as qtd FROM clienteMensagem "
                + "left join cliente on clienteMensagem.cliente = cliente.codigo "
                + "left join vinculo on vinculo.cliente = cliente.codigo "
                + "WHERE  cliente.empresa = " + empresa
                + " and tipomensagem = '" + TiposMensagensEnum.BOLETIM.getSigla() + "' ";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    public ResultSet consultarClienteMensagemPorEmpresaQuestionarioCliente(boolean contar, Integer empresa, String colaboradores, boolean controlarAcesso,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        if (contar) {
            sqlStr.append(" SELECT count(*) as qtd FROM  (");
        }else {
            sqlStr.append(" SELECT * FROM  (");
        }
        sqlStr.append("SELECT clienteMensagem.cliente as cli, dw.matricula as matriculacli, qc.data as dataBV, qc.tipoBV as tipoBV, \n");
        sqlStr.append("dw.nomecliente as nome ,dw.nomePlano,dw.codigoPessoa as codPessoa, dw.codigocontrato as codContrato, mensagem,dw.situacao as situacaoCliente, \n");
        sqlStr.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente\n");
        sqlStr.append("FROM clienteMensagem\n");
        sqlStr.append("INNER JOIN Situacaoclientesinteticodw dw ON dw.codigocliente = clienteMensagem.cliente\n");
        sqlStr.append("inner join questionariocliente qc on qc.codigo = clientemensagem.questionariocliente \n");
        sqlStr.append("LEFT JOIN Contrato ct ON ct.codigo = dw.codigocontrato\n");
        sqlStr.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = dw.codigocontrato\n");
        sqlStr.append("WHERE true ");
        if(empresa != 0){
            sqlStr.append(" and dw.empresacliente = ").append(empresa);
        }
        if (dataBaseInicio != null) {//Data limite inicial != null
            sqlStr.append(" and qc.data >= '").append(Uteis.getDataJDBC(dataBaseInicio)).append(" 00:00:00'");
        }

        sqlStr.append("  AND clienteMensagem.tipomensagem = 'BP'");
        sqlStr.append("  AND dw.situacao in ('AT', 'VI')");
        if(!colaboradores.equals("true")){
            sqlStr.append(" and exists (select 1 from vinculo  where cliente = dw.codigocliente and true limit 1)");
        }
        sqlStr.append(") consulta ");

        if(paginacao != null && paginacao.getOrdernar() && contar == false){
            sqlStr.append(" order by ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }

        if(paginacao != null &&  paginacao.isExistePaginacao() && contar == false){
            sqlStr.append(" limit ").append(paginacao.getItensPorPagina()).append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }

        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr.toString());
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaColaboradorMovParcela(Integer empresa, String colaboradore, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT COUNT (DISTINCT (clienteMensagem.cliente)) as qtd FROM clienteMensagem "
                + "inner join cliente on clienteMensagem.cliente = cliente.codigo and cliente.empresa = " + empresa + " "
                + "inner join vinculo on vinculo.cliente = cliente.codigo and (" + colaboradore + ") "
                + "WHERE tipomensagem = '" + TiposMensagensEnum.PARCELA_ATRASO.getSigla() + "' ";
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaColaboradorQuestionario(Integer empresa, String colaboradore, boolean controlarAcesso, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = sqlBoletimPendente(" COUNT (clienteMensagem.cliente) as qtd ", empresa, colaboradore, dataBaseInicio);
        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr);
    }

    private String sqlBoletimPendente(String campos, Integer empresa, String colaboradores, Date dataBaseInicio) throws Exception {
        String sqlStr = "SELECT " + campos + " FROM clienteMensagem \n inner join cliente on clienteMensagem.cliente = cliente.codigo ";
        if(empresa != 0){
            sqlStr += "and cliente.empresa = " + empresa + " ";
        }
        sqlStr += "inner join pessoa on cliente.pessoa = pessoa.codigo \n"
                + "inner join vinculo on vinculo.cliente = cliente.codigo and (" + colaboradores+ ") \n"
                + "WHERE tipomensagem = '" + TiposMensagensEnum.BOLETIM.getSigla() + "' \n";
        if (dataBaseInicio != null) {//Data limite inicial != null
            sqlStr += " and pessoa.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00'";
        }
        return sqlStr;
    }

    @Override
    public ResultSet contarPendenciaClienteMensagemPorEmpresaColaboradorCadastroIncompleto(Integer empresa, String colaboradores, String situacao, boolean controlarAcesso, Date dataBaseInicio) throws Exception {
//        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT COUNT (DISTINCT (clienteMensagem.cliente)) as qtd FROM clienteMensagem\n");
        sqlStr.append("inner join cliente on clienteMensagem.cliente = cliente.codigo ");
        if(empresa != 0){
            sqlStr.append(" and cliente.empresa = ").append(empresa).append("\n");
        }
        if (!situacao.trim().isEmpty()) {
            sqlStr.append(" and cliente.situacao= '").append(situacao).append("' ");
        }
        if(!UteisValidacao.emptyString(colaboradores)){
            sqlStr.append("INNER JOIN vinculo on vinculo.cliente = cliente.codigo and (").append(colaboradores).append(")\n");
        }
        sqlStr.append(" WHERE tipomensagem = '").append(TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla()).append("'");
        if (!situacao.trim().isEmpty()) {
            sqlStr.append(" and cliente.situacao= '").append(situacao).append("'");
        }

        if (dataBaseInicio != null) {//Data limite inicial != null
            sqlStr.append(" AND EXISTS(SELECT pes.codigo FROM pessoa pes WHERE pes.codigo = cliente.pessoa AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00') \n");
        }

        Statement stm = con.createStatement();
        return stm.executeQuery(sqlStr.toString());
    }
    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da      * classe <code>ClienteMensagemVO</code> resultantes da consulta.
     */
    public static List<ClienteMensagemVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ClienteMensagemVO> vetResultado = new ArrayList<ClienteMensagemVO>();
        while (tabelaResultado.next()) {
            ClienteMensagemVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static ClienteMensagemVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ClienteMensagemVO obj = new ClienteMensagemVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setMensagem(dadosSQL.getString("mensagem"));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("Cliente")));
        obj.setTipomensagem(TiposMensagensEnum.getConstante(dadosSQL.getString("tipomensagem")));
        obj.getUsuario().setCodigo(new Integer(dadosSQL.getInt("Usuario")));
        obj.setDataBloqueio(dadosSQL.getDate("databloqueio"));
        obj.setBloqueio(dadosSQL.getBoolean("bloqueio"));
        obj.getQuestionarioCliente().setCodigo(new Integer(dadosSQL.getInt("questionarioCliente")));
        obj.getMovParcela().setCodigo(new Integer(dadosSQL.getInt("movParcela")));
        obj.getRisco().setPeso(dadosSQL.getInt("pesorisco"));
        obj.getProduto().setCodigo(new Integer(dadosSQL.getInt("produto")));
        obj.setDataDesbloqueio(dadosSQL.getDate("datadesbloqueio"));
        obj.setCodigoOperacaoColetiva(dadosSQL.getInt("codigooperacaocoletiva"));

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>ClienteMensagemVO</code>.
     *
     * @return O objeto da classe <code>ClienteMensagemVO</code> com os dados
     * devidamente montados.
     */
    public static ClienteMensagemVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {

        ClienteMensagemVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosUsuario(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            return obj;
        }
        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosUsuario(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosQuestionarioCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosMovParcela(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosRisco(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    public static void montarDadosProduto(ClienteMensagemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProduto().getCodigo() == 0) {
            obj.setProduto(new ProdutoVO());
            return;
        }
        Produto produtoDao = new Produto(con);
        obj.setProduto(produtoDao.consultarPorChavePrimaria(obj.getProduto().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosCliente(ClienteMensagemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCliente().getCodigo() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        Cliente clienteDao = new Cliente(con);
        obj.setCliente(clienteDao.consultarPorChavePrimaria(obj.getCliente().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosUsuario(ClienteMensagemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getUsuario().getCodigo() == 0) {
            obj.setUsuario(new UsuarioVO());
            return;
        }
        Usuario usuarioDao = new Usuario(con);
        obj.setUsuario(usuarioDao.consultarPorChavePrimaria(obj.getUsuario().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosQuestionarioCliente(ClienteMensagemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getQuestionarioCliente().getCodigo() == 0) {
            obj.setQuestionarioCliente(new QuestionarioClienteVO());
            return;
        }
        QuestionarioCliente questionarioClienteDao = new QuestionarioCliente(con);
        obj.setQuestionarioCliente(questionarioClienteDao.consultarPorChavePrimaria(obj.getQuestionarioCliente().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosMovParcela(ClienteMensagemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getMovParcela().getCodigo() == 0) {
            obj.setMovParcela(new MovParcelaVO());
            return;
        }
        MovParcela movparcelaDao = new MovParcela(con);
        obj.setMovParcela(movparcelaDao.consultarPorChavePrimaria(obj.getMovParcela().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosRisco(ClienteMensagemVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getRisco().getCodigo() == 0) {
            obj.setRisco(new RiscoVO());
            return;
        }
        Risco riscoDao = new Risco(con);
        obj.setRisco(riscoDao.consultarPorChavePrimaria(obj.getRisco().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>ClienteMensagemVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ClienteMensagemVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ClienteMensagem WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ClienteMensagem ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarPorCodigoCliente(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT ClienteMensagem.* FROM ClienteMensagem, Cliente WHERE ClienteMensagem.cliente = Cliente.codigo  and Cliente.codigo = " + valorConsulta + " ORDER BY Cliente.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public List consultarPorCodigoTipoMensagem(Integer valorConsulta, String prm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT * FROM ClienteMensagem WHERE cliente = " + valorConsulta + " and tipomensagem = '" + prm.toUpperCase() + "'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public List consultarPorCodigoTipoMensagem(String prm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT * FROM ClienteMensagem WHERE tipomensagem = '" + prm.toUpperCase() + "'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public ClienteMensagemVO consultarPorCodigoTipoMensagemReturnObjeto(String prm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT * FROM ClienteMensagem WHERE tipomensagem = '" + prm.toUpperCase() + "'";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new ClienteMensagemVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public ClienteMensagemVO consultarPorCodigoTipoMensagemECliente(Integer valorConsulta, String prm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sql = "SELECT * FROM ClienteMensagem WHERE cliente = " + valorConsulta + " and tipomensagem ='" + prm.toUpperCase() + "' order by codigo desc ";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new ClienteMensagemVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public ClienteMensagemVO consultarPorCodigoTipoMensagemECliente(Integer valorConsulta, String prm, Boolean verificarPermissao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), verificarPermissao);
        String sql = "SELECT * FROM ClienteMensagem WHERE cliente = " + valorConsulta + " and tipomensagem ='" + prm.toUpperCase() + "'";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new ClienteMensagemVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, con));
    }

    public List consultarMensagemProdutoVencidoPorCliente(Integer cliente, Boolean verificarPermissao, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), verificarPermissao);
        String sql = "SELECT * FROM ClienteMensagem WHERE cliente = " + cliente + " and tipomensagem ='"
                + TiposMensagensEnum.PRODUTO_VENCIDO.getSigla() + "'";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public List consultarPorTiposMensagens(Integer valorConsulta, List prm, int nivelMontarDados) throws Exception {
        int aux = 0;
        consultar(getIdEntidade(), true);
        String sql = "SELECT * FROM ClienteMensagem WHERE cliente = " + valorConsulta;
        Iterator i = prm.iterator();
        while (i.hasNext()) {
            String tipo = (String) i.next();
            sql += (aux++ == 0 ? " AND (" : " OR ");
            sql += "tipomensagem = '" + tipo + "'";
        }
        sql += ")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    public ClienteMensagemVO consultarMensagemParcelasConcatenado(ClienteVO cliente) throws Exception {
        ClienteMensagemVO mensagem = new ClienteMensagemVO();
        String sql = "SELECT movparcela FROM clientemensagem WHERE cliente = " + cliente.getCodigo() + " AND tipomensagem LIKE 'PA'";
        ResultSet resultSet = ClienteMensagem.criarConsulta(sql, con);
        String resultado = "";
        while (resultSet.next()) {
            resultado += "," + resultSet.getInt("movparcela");
        }
        if (!resultado.isEmpty()) {
            mensagem.setMensagem("Consta parcela(s) em atraso para este cliente. Parcela(s): " + resultado.replaceFirst(",", ""));
            mensagem.setTipomensagem(TiposMensagensEnum.PARCELA_ATRASO);
            mensagem.setCliente(cliente);
        }
        return mensagem;

    }

    @Override
    public void excluirClienteMensagemProdutoVencido(Integer cliente, Integer produto) throws Exception {
        String sqlStr = "DELETE FROM ClienteMensagem WHERE cliente = " + cliente
                + " and produto = " + produto
                + " and tipomensagem = '" + TiposMensagensEnum.PRODUTO_VENCIDO.getSigla() + "'";
        Statement stm = con.createStatement();
        stm.execute(sqlStr);
    }

    @Override
    public void excluirClienteMensagemCartaoVencido(String cartao, Integer cliente) throws Exception {
        String sqlStr = "DELETE FROM ClienteMensagem WHERE mensagem like '%" + cartao + "%'"
                + " and cliente = " + cliente
                + " and tipomensagem = '" + TiposMensagensEnum.CARTAO_VENCIDO.getSigla() + "'";
        Statement stm = con.createStatement();
        stm.execute(sqlStr);
    }
    public ResultSet contarPendenciaClienteMensagemPorEmpresaCadastroIncompletoPendencia(Integer empresa, String situacao, boolean controlarAcesso) throws Exception {
//        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT (DISTINCT (clienteMensagem.cliente)) as qtd FROM clienteMensagem\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sin on clienteMensagem.cliente = sin.codigocliente and sin.empresacliente = ").append(empresa).append("\n");
        sql.append(" WHERE tipomensagem = '").append(TiposMensagensEnum.DADOS_INCOMPLETOS.getSigla()).append("' ");
        if (!situacao.trim().isEmpty()) {
            sql.append(" AND cliente.situacao= '" + situacao + "'");
        }
        Statement stm = con.createStatement();
        return stm.executeQuery(sql.toString());
    }
    public void lancarBloqueioCatraca(String mensagem, Integer codigoPessoa, UsuarioVO usuario) throws Exception {
        ResultSet rs = criarConsulta("SELECT codigo FROM cliente WHERE pessoa = " + codigoPessoa, con);
        if (rs.next()) {
            ClienteMensagemVO bloqueio = new ClienteMensagemVO();
            bloqueio.setBloqueio(true);
            bloqueio.setDataBloqueio(Calendario.hoje());
            bloqueio.setUsuario(usuario);
            bloqueio.setTipomensagem(TiposMensagensEnum.CATRACA);
            bloqueio.setCliente(new ClienteVO());
            bloqueio.getCliente().setCodigo(rs.getInt("codigo"));
            bloqueio.setMensagem(mensagem);
            incluir(bloqueio);
        }
    }

    public void lancarDesbloqueioCatraca(Integer codigoPessoa) throws Exception {
        ResultSet rs = criarConsulta("SELECT codigo FROM cliente WHERE pessoa = " + codigoPessoa, con);
        if (rs.next()) {
            ClienteMensagemVO obj = consultarPorCodigoTipoMensagemECliente(rs.getInt("codigo"),
                    TiposMensagensEnum.CATRACA.getSigla(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            excluir(obj);
        }
    }

    public void verificarBloqueados(List<MovParcelaVO> parcelas) throws Exception {
        String codigosPessoas = "";
        for (MovParcelaVO parcela : parcelas) {
            codigosPessoas = codigosPessoas + "," + parcela.getPessoa().getCodigo();
        }
        StringBuilder sql = new StringBuilder(" SELECT c.pessoa FROM clientemensagem cm \n");
        sql.append(" INNER JOIN cliente c ON c.codigo = cm.cliente \n");
        sql.append(" and pessoa IN ( ").append(codigosPessoas.replaceFirst(",", "")).append(") \n");
        sql.append(" WHERE bloqueio \n");
        sql.append(" and (databloqueio is null or databloqueio <= ?)\n");
        sql.append(" and (datadesbloqueio is null or datadesbloqueio >= ?)\n");
        sql.append(" GROUP BY c.pessoa");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
        stm.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
        ResultSet rs = stm.executeQuery();
        List<Integer> codigos = new ArrayList<Integer>();

        while (rs.next()) {
            codigos.add(rs.getInt("pessoa"));
        }

        for (MovParcelaVO parcela : parcelas) {
            parcela.setAcessoBloqueado(codigos.contains(parcela.getPessoa().getCodigo()));
        }

    }

    @Override
    public void processarProdutoAtestado(Integer cliente, ProdutoVO produto, Date finalVigencia, UsuarioVO usuario) throws Exception {
        Boolean vencido = finalVigencia != null && Calendario.menor(finalVigencia, Calendario.hoje());
        ClienteMensagemVO mensagem = consultarObjClienteMensagemPorProdutoVencido(cliente, produto.getCodigo(), false);
        if (!vencido && mensagem != null) {
            excluir(mensagem);
        } else if (vencido && mensagem == null) {
            ClienteMensagemVO msg = new ClienteMensagemVO();
            msg.getCliente().setCodigo(cliente);
            msg.setMensagem(TiposMensagensEnum.PRODUTO_VENCIDO.getMensagem().replace("Z", produto.getDescricao()).replace("xx/xx/xx",
                    Uteis.getData(finalVigencia, "br")));
            msg.setTipomensagem(TiposMensagensEnum.PRODUTO_VENCIDO);
            msg.getUsuario().setCodigo(usuario.getCodigo());
            msg.getProduto().setCodigo(produto.getCodigo());
            msg.setBloqueio(produto.getBloqueiaPelaVigencia());
            incluirSemCommit(msg);
        }
    }

    @Override
    public ClienteMensagemVO consultarObjClienteMensagemPorProdutoVencido(Integer cliente, Integer produto, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ClienteMensagem WHERE cliente = " + cliente
                + " and produto = " + produto
                + " and tipomensagem = '" + TiposMensagensEnum.PRODUTO_VENCIDO.getSigla() + "'"
                + " ORDER BY codigo DESC";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public void excluirClienteMensagemRiscoClientesInativos() throws Exception {
        String sql = "DELETE FROM clientemensagem\n" +
                "WHERE codigo IN (SELECT cm.codigo\n" +
                "                 FROM clientemensagem cm\n" +
                "                   INNER JOIN cliente c ON c.codigo = cm.cliente\n" +
                "                   INNER JOIN situacaoclientesinteticodw sdw ON sdw.codigocliente = c.codigo\n" +
                "                 WHERE cm.tipomensagem = '" + TiposMensagensEnum.RISCO.getSigla() + "'\n "+
                "                       AND (sdw.situacaocontrato = 'DE' OR sdw.situacaocontrato = 'CA' OR sdw.situacao = 'VI' OR sdw.situacao = 'TR'))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }
    
    public List<ClienteMensagemVO> consultarTelaCliente(Integer codigo, Integer limit) throws Exception {
        List<ClienteMensagemVO> lista = new ArrayList<ClienteMensagemVO>();
        ResultSet rs = criarConsulta("SELECT tipomensagem, codigo, mensagem, bloqueio, pesorisco,questionariocliente, produto "
                + " FROM clientemensagem "
                + " WHERE not desabilitado and cliente = "+codigo
                + " ORDER BY codigo desc"
                + (UteisValidacao.emptyNumber(limit) ? "" :(" LIMIT "+limit)), con);
        while(rs.next()){
            ClienteMensagemVO obj = new ClienteMensagemVO();
            obj.setNovoObj(false);
            obj.setCodigo(rs.getInt("codigo"));
            obj.setMensagem(rs.getString("mensagem"));
            obj.getCliente().setCodigo(codigo);
            obj.setTipomensagem(TiposMensagensEnum.getConstante(rs.getString("tipomensagem")));
            obj.setBloqueio(rs.getBoolean("bloqueio"));
            obj.getRisco().setPeso(rs.getInt("pesorisco"));
            obj.getQuestionarioCliente().setCodigo(rs.getInt("questionariocliente"));
            obj.getProduto().setCodigo(rs.getInt("produto"));
            lista.add(obj);
        }
        return lista;
    }
    public void desabilitarMensagem(ClienteMensagemVO obj) throws Exception {
        String sqlStr = "UPDATE ClienteMensagem set desabilitado = TRUE, bloqueio = FALSE WHERE codigo = " + obj.getCodigo();
        Statement stm = con.createStatement();
        stm.execute(sqlStr);
    }

    public void desabilitarMensagemArmariosVencidosPorCliente(Integer cliente) throws Exception {
        String sqlStr = "UPDATE ClienteMensagem set desabilitado = TRUE, bloqueio = FALSE WHERE mensagem ILIKE 'Aluguel de armario com validade vencida%' AND tipomensagem = 'PV'AND cliente = " + cliente;
        Statement stm = con.createStatement();
        stm.execute(sqlStr);
    }

    public void excluirMensagemParcelaEmRemessa() throws Exception {
        String sql = "delete from clientemensagem clim \n" +
                "where clim.tipomensagem = 'PA' \n" +
                "and clim.movparcela is not null \n" +
                "and clim.movparcela > 0 \n" +
                "AND EXISTS ( \n" +
                "select  \n" +
                "item.codigo  \n" +
                "from remessaitem item \n" +
                "inner join remessa rem on rem.codigo = item.remessa \n" +
                "left join remessaitemmovparcela rim on rim.remessaitem = item.codigo \n" +
                "where rem.situacaoremessa = "+ SituacaoRemessaEnum.REMESSA_ENVIADA.getId()+" \n" +
                "and (item.movparcela = clim.movparcela \n" +
                "or rim.movparcela = clim.movparcela)) ";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }


    public void excluirMensagemErradaPorMovParcelaVencida() throws Exception {
        String sql = "delete from clientemensagem where codigo in (select cm.codigo from clientemensagem cm inner join movparcela p on p.codigo = cm.movparcela where tipomensagem = 'PA' and p.situacao <> 'EA')";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }

    public void excluirMensagensErradasParcelasNaoVencidasEmAberto(Date dataAvaliar) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM clientemensagem\n")
                .append("WHERE codigo IN (\n")
                .append("   SELECT cm.codigo from clientemensagem cm\n")
                .append("   INNER JOIN movparcela mp ON cm.movparcela = mp.codigo\n")
                .append("   WHERE movparcela > 0\n")
                .append("   AND cm.tipomensagem = 'PA'\n")
                .append("   AND mp.datavencimento >= '").append(Uteis.getDataJDBC(dataAvaliar)).append("' \n")
                .append(");\n");

        PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
        sqlExcluir.execute();
    }

    public void excluirMensagensParcelasVencidasNaPessoaErradaContratosConcedidos() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM clientemensagem\n")
                .append("WHERE codigo IN (\n")
                .append("   SELECT cm.codigo from clientemensagem cm\n")
                .append("   INNER JOIN cliente cli ON cli.codigo = cm.cliente\n")
                .append("   INNER JOIN movparcela mp ON cm.movparcela = mp.codigo\n")
                .append("   INNER JOIN contrato con ON con.codigo = mp.contrato \n")
                .append("   WHERE cm.tipomensagem = 'PA'\n")
                .append("   AND COALESCE(con.pessoaoriginal, 0) <> 0 \n")
                .append("   AND cli.pessoa <> con.pessoaoriginal \n")
                .append(");\n");

        PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
        sqlExcluir.execute();
    }

    public void processarTodosClientesComCartaoCreditoVencido(UsuarioVO usuarioVO, Integer cliente) throws Exception {
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO = null;
        try {
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);

            List<AutorizacaoCobrancaClienteVO> autorizacoes = autorizacaoCobrancaClienteDAO.consultarListaCartoesVencidos(null, null, cliente);
            for (AutorizacaoCobrancaClienteVO auto : autorizacoes) {
                String cartao = auto.getCartaoMascarado();
                if (UteisValidacao.emptyString(cartao)) {
                    cartao = APF.getCartaoMascarado(auto.getNumeroCartao());
                }
                boolean existe = SuperFacadeJDBC.existe(String.format(
                        "select codigo from clientemensagem "
                                + "where cliente = %s and tipomensagem = '%s' "
                                + "and mensagem like('%%%s%%')",
                        new Object[]{auto.getCliente().getCodigo(), "CV", cartao}),
                        this.con);
                if (!existe) {
                    ClienteMensagemVO msg = new ClienteMensagemVO();
                    msg.getCliente().setCodigo(auto.getCliente().getCodigo());
                    msg.setMensagem(TiposMensagensEnum.CARTAO_VENCIDO.getMensagem().replace("Z", APF.getCartaoMascarado(auto.getNumeroCartao())).replace("xx/xxxx",
                            auto.getValidadeCartao()));
                    msg.setTipomensagem(TiposMensagensEnum.CARTAO_VENCIDO);
                    msg.getUsuario().setCodigo(usuarioVO.getCodigo());
                    msg.setBloqueio(false);
                    incluirSemCommit(msg);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            autorizacaoCobrancaClienteDAO = null;
        }
    }

    public void excluirClienteMensagemClienteTipoMensagem(Integer cliente, TiposMensagensEnum tiposMensagensEnum) throws Exception {
        if (tiposMensagensEnum == null) {
            throw new Exception("Tipo Mensagem não informado");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM ClienteMensagem \n");
        sql.append("WHERE 1 = 1 \n");

        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append("and cliente = ").append(cliente).append(" \n");
        }

        if (tiposMensagensEnum != null) {
            sql.append("and tipomensagem = '").append(tiposMensagensEnum.getSigla()).append("' \n");
        }

        Statement stm = con.createStatement();
        stm.execute(sql.toString());
    }

    public void processarMensagensCartaoVencidoCliente(Integer cliente, UsuarioVO usuarioVO) throws Exception {

        if (UteisValidacao.emptyNumber(cliente)) {
            throw new Exception("Cliente não informado");
        }
        if (usuarioVO == null ||
                UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            throw new Exception("Usuário não informado");
        }

        //exclui todas
        excluirClienteMensagemClienteTipoMensagem(cliente, TiposMensagensEnum.CARTAO_VENCIDO);

        processarTodosClientesComCartaoCreditoVencido(usuarioVO, cliente);

    }

    public void excluirClienteMensagemProdutoVencido(ClienteVO clienteVO,
                                                     ClienteMensagemVO clienteMensagemVO,
                                                     UsuarioVO usuarioVO) throws Exception {
        MovProduto movProdutoDAO;
        try {
            movProdutoDAO = new MovProduto(this.con);

            desabilitarMensagem(clienteMensagemVO);
            try {
                LogVO obj = new LogVO();
                obj.setChavePrimaria(clienteMensagemVO.getCliente().getCodigo().toString());
                obj.setNomeEntidade("CLIENTE");
                obj.setNomeEntidadeDescricao("Cliente - Desabilitado Mensagens/Avisos");
                obj.setOperacao("EXCLUSÃO");
                obj.setResponsavelAlteracao(usuarioVO.getNome());
                obj.setUserOAMD(usuarioVO.getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setDataAlteracao(Calendario.hoje());
                obj.setValorCampoAnterior(clienteMensagemVO.getMensagem());
                obj.setValorCampoAlterado("");
                registrarLogObjetoVO(obj, clienteVO.getPessoa().getCodigo(), this.con);
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE", clienteVO.getPessoa().getCodigo(), "ERRO AO GERAR LOG P/ APAGAR A MENSAGEM DE VENCIMENTO DO PRODUTO", usuarioVO.getNome(), usuarioVO.getUserOamd(), this.con);
                e.printStackTrace();
            }

            clienteVO.setListaProdutosComValidade(movProdutoDAO.consultarProdutoComValidadePorCodigoPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_VENDA));

            for (MovProdutoVO movProduto : clienteVO.getListaProdutosComValidade()) {
                if (movProduto.getProduto().getCodigo().equals(clienteMensagemVO.getProduto().getCodigo())) {
                    movProdutoDAO.alterarNaoGerarMensagem(movProduto, true);
                    excluir(clienteMensagemVO);
                    break;
                }
            }
        } finally {
            movProdutoDAO = null;
        }
    }

    public void desbloquearMsgProdutoVencido(ClienteVO clienteVO,
                                             ClienteMensagemVO clienteMensagemVO, UsuarioVO usuarioVO) throws Exception {
        clienteMensagemVO.setBloqueio(false);
        clienteMensagemVO.setDataDesbloqueio(Calendario.hoje());
        alterar(clienteMensagemVO);
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(clienteVO.getCodigo().toString());
            obj.setNomeEntidade("CLIENTE");
            obj.setNomeEntidadeDescricao("Cliente - Desloqueando bloqueio de Mensagens/Avisos");
            obj.setOperacao("DESBLOQUEIO");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAnterior(clienteMensagemVO.getMensagem());
            obj.setValorCampoAlterado("");
            registrarLogObjetoVO(obj, clienteVO.getPessoa().getCodigo(), this.con);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLIENTE", clienteVO.getPessoa().getCodigo(), "ERRO AO GERAR LOG P/ DESBLOQUEAR A MENSAGEM DE VENCIMENTO DO PRODUTO", usuarioVO.getNome(), usuarioVO.getUserOamd(), this.con);
            e.printStackTrace();
        }
    }
}
