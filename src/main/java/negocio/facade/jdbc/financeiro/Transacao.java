/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.atualizadb.processo.GerarReciboTransacao;
import br.com.pactosolucoes.atualizadb.processo.ProcessoPreencharConvenioCobrancaTransacao;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.to.GrupoParcelasTO;
import br.com.pactosolucoes.comuns.to.GrupoTransacoesErrosTO;
import br.com.pactosolucoes.comuns.to.GrupoTransacoesTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.integracao.pactopay.TipoConsultaPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.ParcelaDTO;
import br.com.pactosolucoes.integracao.pactopay.front.transacao.TotalizadorDTO;
import br.com.pactosolucoes.integracao.pactopay.front.transacao.TotalizadorTipoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailPagamentoTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.InformacaoErroTransacao;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.impl.gatewaypagamento.PagamentoService;
import servicos.impl.gatewaypagamento.VerificadorTransacaoService;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import servicos.impl.mundiPagg.TransacaoMundiPaggVO;
import servicos.impl.onepayment.TransacaoOnePaymentVO;
import servicos.impl.pagarMe.TransacaoPagarMeVO;
import servicos.impl.pagbank.TransacaoPagBankVO;
import servicos.impl.redepay.ERedeRetornoEnum;
import servicos.impl.stone.TransacaoStoneOnlineVO;
import servicos.impl.stoneV5.TransacaoStoneOnlineV5VO;
import servicos.impl.stripe.TransacaoStripeVO;
import servicos.impl.pinbank.TransacaoPinBankVO;
import servicos.impl.vindi.VindiService;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
import servicos.interfaces.AprovacaoServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class Transacao extends SuperEntidade implements TransacaoInterfaceFacade {

    public Transacao() throws Exception {
    }

    public Transacao(Connection con) throws Exception {
        super(con);
    }

    /**
     * Método inclui uma transação esperando que esteja dentro de um bloco transacional, omitindo os métodos de 'commit' e 'rollback'
     * @param obj
     * @throws Exception
     */
    @Override
    public void incluir(TransacaoVO obj) throws Exception {
        TransacaoVO.validarDados(obj);
        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresa = empresaDAO.consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

        verificaDataCreditosPacto(obj, empresa);

        verificarMovParcelaEstaAberto(obj);
        verificarExisteTransacaoMovParcelaRecente(obj);
        verificarExisteTransacaoVerificacaoRecente(obj);

        String sql = "INSERT INTO transacao(codigoexterno, situacao, dataprocessamento, "
                + "recibopagamento, valor, paramsenvio, paramsresposta, tipo, "
                + "movpagamento, resultadoCaptura, nomePessoa,"
                + "usuarioResponsavel, resultadoCancelamento, "
                + "dataHoraCancelamento, logEstorno, empresa, permiterepescagem, pessoaPagador, aguardandoConfirmacao, orderid, "
                + "codigoAutorizacao, outrasinformacoes, tokenAragorn, conveniocobranca, ambiente, transacaoVerificarCartao, proximaTentativa, "
                + "dataAtualizacao, tipoOrigem, origem, codigoretornodescricao, desconto, codigoexterno2) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {

            sqlInserir.setString(1, obj.getCodigoExterno());
            sqlInserir.setInt(2, obj.getSituacao().getId());
            sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));

            if (obj.getReciboPagamento() == 0) {
                sqlInserir.setNull(4, Types.NULL);
            } else {
                sqlInserir.setInt(4, obj.getReciboPagamento());
            }

            sqlInserir.setDouble(5, obj.getValor());
            sqlInserir.setString(6, obj.getParamsEnvio());
            sqlInserir.setString(7, obj.getParamsResposta());
            sqlInserir.setInt(8, obj.getTipo().getId());

            if (obj.getMovPagamento() == 0) {
                sqlInserir.setNull(9, Types.NULL);
            } else {
                sqlInserir.setInt(9, obj.getMovPagamento());
            }

            sqlInserir.setString(10, obj.getResultadoCaptura());
            sqlInserir.setString(11, obj.getNomePessoa());
            sqlInserir.setInt(12, obj.getUsuarioResponsavel().getCodigo());
            sqlInserir.setString(13, obj.getResultadoCancelamento());
            sqlInserir.setTimestamp(14, Uteis.getDataJDBCTimestamp(obj.getDataHoraCancelamento()));
            sqlInserir.setString(15, obj.getLogEstorno());

            if (obj.getEmpresa() == 0) {
                sqlInserir.setNull(16, Types.NULL);
            } else {
                sqlInserir.setInt(16, obj.getEmpresa());
            }
            sqlInserir.setBoolean(17, obj.getPermiteRepescagem());
            if (obj.getPessoaPagador() == null || UteisValidacao.emptyNumber(obj.getPessoaPagador().getCodigo())) {
                sqlInserir.setNull(18, Types.INTEGER);
            } else {
                sqlInserir.setInt(18, obj.getPessoaPagador().getCodigo());
            }
            sqlInserir.setBoolean(19, obj.getAguardandoConfirmacao());
            sqlInserir.setString(20, obj.getOrderid());
            sqlInserir.setString(21, obj.getCodigoAutorizacao());
            sqlInserir.setString(22, obj.getOutrasInformacoes());
            sqlInserir.setString(23, obj.getTokenAragorn());
            if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
                sqlInserir.setNull(24, Types.NULL);
            } else {
                sqlInserir.setInt(24, obj.getConvenioCobrancaVO().getCodigo());
            }
            sqlInserir.setInt(25, obj.getAmbiente().getCodigo());
            sqlInserir.setBoolean(26, obj.isTransacaoVerificarCartao());
            sqlInserir.setTimestamp(27, Uteis.getDataJDBCTimestamp(obj.getProximaTentativa()));
            sqlInserir.setTimestamp(28, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));
            sqlInserir.setInt(29, obj.getTipoOrigem().getId());//tipoOrigem
            sqlInserir.setInt(30, obj.getOrigem().getCodigo());
            sqlInserir.setString(31, obj.getCodigoRetornoDescricao());
            sqlInserir.setDouble(32, obj.getDesconto());
            sqlInserir.setString(33, obj.getCodigoExterno2());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        if (!obj.isErrorExisteTransacaoRecenteParaMovParcela()) {
            validarDebitarCreditosDCC(obj, empresa, empresaDAO);
        }

        atualizarCodigoRetorno(obj);
        incluirTransacaoMovParcelas(obj);
        incluirAutorizacaoMovPagamento(obj.getAutorizacao(), obj.getMovPagamento(), obj.getNSU());

        empresaDAO = null;

        if (obj.isErrorExisteTransacaoRecenteParaMovParcela() && !obj.getOrigem().equals(OrigemCobrancaEnum.VERIFICADOR_CARTAO)) {
            throw new Exception("Existe uma cobrança em andamento. Aguarde alguns minutos antes de tentar novamente ou verifique com a academia se o pagamento já foi efetuado.");
        }
        if (obj.isErrorExisteTransacaoRecenteParaMovParcela() && obj.getOrigem().equals(OrigemCobrancaEnum.VERIFICADOR_CARTAO)) {
            throw new Exception("Existe uma verificação do cartão em andamento. Aguarde alguns segundos antes de tentar novamente ou verifique com a academia se seu cartão já foi verificado.");
        }

        if (obj.isErrorMovParcelaDaTransacaoNaoEstaEmAberto()) {
            throw new Exception("A Parcela da Transação não está em aberto.");
        }
    }

    private void verificarExisteTransacaoMovParcelaRecente(TransacaoVO obj) {
        //Como hoje o sistema tem vários fluxos de cobrança, estamos tendo problemas com pagamentos duplicados de fontes destintas com segundos de diferença
        //Ex1: pagamento automático e pagamento retentativa do PacoPay
        //Ex2: pagamento do aluno pelo link de pagamento e da academia pela retentativa na tela do aluno

        TransacaoMovParcela transacaoMovParcelaDAO;
        try {
            List<Integer> idParcelas = new ArrayList<>();
            for (MovParcelaVO movParcelaVO: obj.getListaParcelas()) {
                idParcelas.add(movParcelaVO.getCodigo());
            }

            //Condição para validar apenas se não for do Automático, pois tem as Retentativas.
            if (!UteisValidacao.emptyList(idParcelas) &&
                    (!obj.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO) && !obj.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO_RETENTATIVA))) {
                transacaoMovParcelaDAO = new TransacaoMovParcela(con);
                obj.setErrorExisteTransacaoRecenteParaMovParcela(transacaoMovParcelaDAO.existeTransacaoRecenteParaMovParcela(idParcelas));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            transacaoMovParcelaDAO = null;
        }
    }

    private void verificarExisteTransacaoVerificacaoRecente(TransacaoVO obj) throws Exception {
        if (obj.getOrigem().equals(OrigemCobrancaEnum.VERIFICADOR_CARTAO)) {
            obj.setErrorExisteTransacaoRecenteParaMovParcela(existeTransacaoVerificacaoRecenteParaCartao(APF.getCartaoMascarado(obj.getCartaoCreditoTO().getNumero())));
        }
    }

    private void verificarMovParcelaEstaAberto(TransacaoVO obj) {
        //Como hoje o sistema tem vários fluxos de cobrança, estamos tendo problemas com pagamentos duplicados de fontes destintas com segundos de diferença
        //Ex: Pessoa fez recebimento manual no Caixa em Aberto 20 segundos antes da transacao no automático

        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(con);
            for (MovParcelaVO movParcelaVO: obj.getListaParcelas()) {
                obj.setErrorMovParcelaDaTransacaoNaoEstaEmAberto(!movParcelaDAO.parcelaEmAberto(movParcelaVO));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            movParcelaDAO = null;
        }
    }

    public void incluirAutorizacaoMovPagamento(String autorizacao, int movPagamento, String nsu) throws Exception {
        if (!UteisValidacao.emptyString(autorizacao) || !UteisValidacao.emptyString(nsu)) {
            MovPagamento tmpFacade = new MovPagamento(con);
            try {
                tmpFacade.incluirAutorizacao(movPagamento, autorizacao, nsu);
                tmpFacade.incluirAutorizacaoDependentes(movPagamento, autorizacao, nsu);
            } finally {
                tmpFacade = null;
            }
        }
    }

    public void incluirTransacaoMovParcelas(TransacaoVO transacaoVO) throws Exception {
        List<MovParcelaVO> lista = transacaoVO.getListaParcelas();
        if (lista != null) {
            TransacaoMovParcela tmpFacade = new TransacaoMovParcela(con);
            try {
                for (MovParcelaVO movParcelaVO : lista) {
                    TransacaoMovParcelaVO tmp = new TransacaoMovParcelaVO();
                    tmp.setMovParcela(movParcelaVO);
                    tmp.setTransacao(transacaoVO);
                    tmp.setNrTentativaParcela(movParcelaVO.getNumeroParcela());
                    tmp.setValorParcela(movParcelaVO.getValorParcela());
                    tmp.setValorJuros(movParcelaVO.getValorJuros());
                    tmp.setValorMulta(movParcelaVO.getValorMulta());
                    tmpFacade.incluir(tmp);
                }
            } finally {
                tmpFacade = null;
            }
        }
    }

    @Override
    public void alterar(final TransacaoVO obj) throws Exception {
        TransacaoVO.validarDados(obj);
        String sql = "UPDATE transacao "
                + "set codigoexterno=?, situacao=?, dataprocessamento=?, "
                + "recibopagamento=?, valor=?, paramsenvio=?, "
                + "paramsresposta=?, tipo=?, movpagamento=?, resultadoCaptura=?,"
                + "nomepessoa=?, usuarioResponsavel=?, resultadoCancelamento=?, "
                + "dataHoraCancelamento=?, logEstorno=?, empresa=?, permiterepescagem=?, pessoaPagador=?, aguardandoConfirmacao=?, "
                + "orderid = ?, codigoAutorizacao = ?, outrasinformacoes = ?, tokenAragorn = ?, conveniocobranca = ?, ambiente = ?, "
                + "proximaTentativa = ?, transacaoVerificarCartao = ?, tipoOrigem = ?, origem = ?, codigoretornodescricao = ?, nsu = ?, "
                + "desconto = ?, codigoExterno2 = ?, codigoNSU = ?, gateway_id = ? WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getCodigoExterno());
            sqlAlterar.setInt(2, obj.getSituacao().getId());
            sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));

            if (obj.getReciboPagamento() == 0) {
                sqlAlterar.setNull(4, Types.NULL);
            } else {
                sqlAlterar.setInt(4, obj.getReciboPagamento());
            }

            sqlAlterar.setDouble(5, obj.getValor());
            sqlAlterar.setString(6, obj.getParamsEnvio());
            sqlAlterar.setString(7, obj.getParamsResposta());
            sqlAlterar.setInt(8, obj.getTipo().getId());

            if (obj.getMovPagamento() == 0) {
                sqlAlterar.setNull(9, Types.NULL);
            } else {
                sqlAlterar.setInt(9, obj.getMovPagamento());
            }

            sqlAlterar.setString(10, obj.getResultadoCaptura());
            sqlAlterar.setString(11, obj.getNomePessoa());
            sqlAlterar.setInt(12, obj.getUsuarioResponsavel().getCodigo());
            sqlAlterar.setString(13, obj.getResultadoCancelamento());
            sqlAlterar.setTimestamp(14, Uteis.getDataJDBCTimestamp(obj.getDataHoraCancelamento()));
            sqlAlterar.setString(15, obj.getLogEstorno());

            if (obj.getEmpresa() == 0) {
                sqlAlterar.setNull(16, Types.NULL);
            } else {
                sqlAlterar.setInt(16, obj.getEmpresa());
            }
            sqlAlterar.setBoolean(17, obj.getPermiteRepescagem());
            if (obj.getPessoaPagador() == null || UteisValidacao.emptyNumber(obj.getPessoaPagador().getCodigo())) {
                sqlAlterar.setNull(18, Types.INTEGER);
            } else {
                sqlAlterar.setInt(18, obj.getPessoaPagador().getCodigo());
            }
            sqlAlterar.setBoolean(19, obj.getAguardandoConfirmacao());
            sqlAlterar.setString(20, obj.getOrderid());
            sqlAlterar.setString(21, obj.getCodigoAutorizacao());
            sqlAlterar.setString(22, obj.getOutrasInformacoes());
            sqlAlterar.setString(23, obj.getTokenAragorn());
            if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
                sqlAlterar.setNull(24, Types.NULL);
            } else {
                sqlAlterar.setInt(24, obj.getConvenioCobrancaVO().getCodigo());
            }
            sqlAlterar.setInt(25, obj.getAmbiente().getCodigo());

            try {
                //data proxima tentativa VINDI
                if (obj.getTipo().equals(TipoTransacaoEnum.VINDI)) {
                    String dataProximaString = obj.dataProximaTentativa();
                    if (!UteisValidacao.emptyString(dataProximaString)) {
                        Date dataProxima = Calendario.getDate("dd/MM/yyyy", dataProximaString);
                        if (dataProxima != null) {
                            obj.setProximaTentativa(Calendario.getDataComHoraZerada(dataProxima));
                        }
                    }
                }
            } catch (Exception ignored) {
            }
            sqlAlterar.setTimestamp(26, Uteis.getDataJDBCTimestamp(obj.getProximaTentativa()));
            sqlAlterar.setBoolean(27, obj.isTransacaoVerificarCartao());
            sqlAlterar.setInt(28, obj.getTipoOrigem().getId());//tipoOrigem
            sqlAlterar.setInt(29, obj.getOrigem().getCodigo());
            sqlAlterar.setString(30, obj.getCodigoRetornoDescricao());
            sqlAlterar.setString(31, obj.getNSU());
            sqlAlterar.setDouble(32, obj.getDesconto());
            sqlAlterar.setString(33, obj.getCodigoExterno2());
            sqlAlterar.setString(34, obj.getCodigoNSU());
            if (obj.getGateway_id() == null) {
                sqlAlterar.setNull(35, Types.NULL);
            } else {
                sqlAlterar.setLong(35, obj.getGateway_id());
            }

            sqlAlterar.setInt(36, obj.getCodigo());
            sqlAlterar.execute();
        }
        atualizarCodigoRetorno(obj);
        incluirAutorizacaoMovPagamento(obj.getAutorizacao(), obj.getMovPagamento(), obj.getNSU());
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(TransacaoVO obj) throws Exception {
        String sql = "DELETE FROM transacao WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    public List<TransacaoVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        return montarDadosConsulta(tabelaResultado, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public List<TransacaoVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con, int nivelMontarDados) throws Exception {
        CacheControl.toggleCache(Empresa.class, true);
        CacheControl.toggleCache(Usuario.class, true);
        try {
            List<TransacaoVO> vetResultado = new ArrayList<>();
            while (tabelaResultado.next()) {
                TransacaoVO obj = montarDados(tabelaResultado, con, nivelMontarDados);
                vetResultado.add(obj);
            }
            return vetResultado;
        } finally {
            CacheControl.clear();
        }
    }

    private static String montarUrlComprovanteCancelamento(TransacaoVO transacaoVO, Connection con) {
        try {
            String key = "";
            try {
                key = DAO.resolveKeyFromConnection(con);
            } catch (Exception ignored) {
            }
            if (UteisValidacao.emptyString(key)) {
                return "";
            }
            return gerarURLComprovanteCancelamentoTransacao(transacaoVO, key);
        } catch (Exception ex) {
            return "";
        }
    }

    private TransacaoVO montarDados(ResultSet ds, Connection con) throws Exception {
        return montarDados(ds, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    private TransacaoVO montarDados(ResultSet ds, Connection con, int nivelMontarDados) throws Exception {
        Pessoa pessoaDAO = new Pessoa(con);
        Empresa empresaDAO = new Empresa(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        Usuario usuarioDAO = new Usuario(con);

        TipoTransacaoEnum tipo = TipoTransacaoEnum.getTipoTransacaoEnum(ds.getInt("tipo"));
        TransacaoVO obj = obterObjetoTransacaoPorTipo(tipo);
        obj.setCodigo(ds.getInt("codigo"));
        obj.setCodigoExterno(ds.getString("codigoexterno"));
        obj.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(ds.getInt("situacao")));
        obj.setDataProcessamento(ds.getTimestamp("dataprocessamento"));
        obj.setReciboPagamento(ds.getInt("recibopagamento"));
        obj.setValor(ds.getDouble("valor"));
        obj.setParamsEnvio(ds.getString("paramsenvio"));
        obj.setParamsResposta(ds.getString("paramsresposta"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSMINIMOS_TRANSACAO) {
            return obj;
        }
        obj.setTipo(tipo);
        obj.setTipoOrigem(TipoTransacaoEnum.getTipoTransacaoEnum(ds.getInt("tipoOrigem")));
        obj.setResultadoCaptura(ds.getString("resultadoCaptura"));
        obj.setResultadoCancelamento(ds.getString("resultadoCancelamento"));
        obj.setDataHoraCancelamento(ds.getTimestamp("dataHoraCancelamento"));
        obj.setMovPagamento(ds.getInt("movpagamento"));
        obj.setNomePessoa(ds.getString("nomepessoa"));
        obj.getUsuarioResponsavel().setCodigo(ds.getInt("usuarioResponsavel"));
        obj.setLogEstorno(ds.getString("logEstorno"));
        obj.setEmpresa(ds.getInt("empresa"));
        obj.getEmpresaVO().setCodigo(ds.getInt("empresa"));
        obj.setAguardandoConfirmacao(ds.getBoolean("aguardandoConfirmacao"));
        obj.setOrderid(ds.getString("orderid"));
        obj.setCodigoAutorizacao(ds.getString("codigoAutorizacao"));
        obj.setOutrasInformacoes(ds.getString("outrasinformacoes"));
        obj.setTokenAragorn(ds.getString("tokenAragorn"));
        obj.getConvenioCobrancaVO().setCodigo(ds.getInt("conveniocobranca"));
        obj.setAmbiente(AmbienteEnum.consultarPorCodigo(ds.getInt("ambiente")));
        obj.setDataAtualizacao(ds.getTimestamp("dataAtualizacao"));
        obj.setProximaTentativa(ds.getTimestamp("proximaTentativa"));
        obj.setTransacaoVerificarCartao(ds.getBoolean("transacaoVerificarCartao"));
        obj.setOrigem(OrigemCobrancaEnum.obterPorCodigo(ds.getInt("origem")));
        obj.setErroProcessamento(ds.getString("erroProcessamento"));
        try {
            obj.setNsuTransacao(ds.getString("nsu"));
        } catch (Exception ignored) {
        }
        try {
            obj.setCodigoExterno2(ds.getString("codigoexterno2"));
        } catch (Exception ignored) {
        }
        try {
            obj.setGateway_id(ds.getLong("gateway_id"));
        } catch (Exception ignored) {
        }
        try {
            obj.setCodigoNSU(ds.getString("codigonsu"));
        } catch (Exception ignored) {
        }
        try{
            obj.setCodigoRetorno(ds.getString("codigoretorno"));
            obj.setCodigoRetornoDescricao(ds.getString("codigoretornodescricao"));
        }catch(Exception ignored){
        }
        try{
            obj.setPessoaPagador(new PessoaVO());
            obj.getPessoaPagador().setCodigo(ds.getInt("pessoaPagador"));
        }catch(Exception ignored){
        }
        try{
            obj.setUrlComprovanteCancelamento(montarUrlComprovanteCancelamento(obj, con));
        }catch(Exception ignored){
        }
        try{
            MovParcelaVO movparcela = new MovParcelaVO();
            movparcela.setCodigo(ds.getInt("movparcela"));
            obj.getListaParcelas().add(movparcela);
        }catch(Exception ignored){
        }
        try{
            obj.setQtdParcelasTransacao(ds.getInt("qtd_parcelas_transacao"));
        }catch(Exception ignored){
        }
        try{
            obj.setDesconto(ds.getDouble("desconto"));
        }catch(Exception ignored){
        }

        try {
            Integer cliente_codigo = ds.getInt("cliente_codigo");
            String cliente_matricula = ds.getString("cliente_matricula");

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigo(cliente_codigo);
            clienteVO.setMatricula(cliente_matricula);
            obj.setClienteVO(clienteVO);
        } catch (Exception ignored) {
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO) {
            return obj;
        }

        try {
            if (obj.getPessoaPagador() != null && !UteisValidacao.emptyNumber(obj.getPessoaPagador().getCodigo())) {
                obj.setPessoaPagador(pessoaDAO.consultarPorChavePrimaria(obj.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
        } catch (ConsistirException cex) {
            Uteis.logar(cex, Transacao.class);
        }

        if(obj.getTipo().equals(TipoTransacaoEnum.AprovaFacilCB)) {
            try {
                obj.setInformacaoErro(InformacaoErroTransacao.getTipoInformacaoErroTransacao(Integer.valueOf(obj.getCodErroExterno())));
            }catch (NumberFormatException ignored){}
        }

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA || nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_PACTO_PAY){
            obj.setListaParcelas(movParcelaDAO.consultarApenasDescricaoPorTransacao(obj.getCodigo()));
        }

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            obj.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS));
        }

        if (!UteisValidacao.emptyNumber(obj.getUsuarioResponsavel().getCodigo())) {
            obj.setUsuarioResponsavel(usuarioDAO.consultarPorChavePrimaria(obj.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
        }

        pessoaDAO = null;
        empresaDAO = null;
        movParcelaDAO = null;
        usuarioDAO = null;
        return obj;
    }

    public static TransacaoVO obterObjetoTransacaoPorTipo(TipoTransacaoEnum tipo) {
        TransacaoVO transacao = null;
        switch (tipo){
            case PACTO_PAY:
                transacao = new TransacaoPactoPayVO();
                break;
            case VINDI:
                transacao = new TransacaoVindiVO();
                break;
            case CIELO_ONLINE:
                transacao = new TransacaoCieloVO();
                break;
            case CIELO_DEBITO_ONLINE:
                transacao = new TransacaoCieloVO();
                break;
            case E_REDE:
                transacao = new TransacaoERedeVO();
                break;
            case MAXIPAGO:
                transacao = new TransacaoMaxiPagoVO();
                break;
            case FITNESS_CARD:
                transacao = new TransacaoFitnessCardVO();
                break;
            case GETNET_ONLINE:
                transacao = new TransacaoGetNetVO();
                break;
            case STONE_ONLINE:
                transacao = new TransacaoStoneOnlineVO();
                break;
            case DCC_STONE_ONLINE_V5:
                transacao = new TransacaoStoneOnlineV5VO();
                break;
            case MUNDIPAGG:
                transacao = new TransacaoMundiPaggVO();
                break;
            case PAGAR_ME:
                transacao = new TransacaoPagarMeVO();
                break;
            case PAGBANK:
                transacao = new TransacaoPagBankVO();
                break;
            case STRIPE:
                transacao = new TransacaoStripeVO();
                break;
            case PAGOLIVRE:
                transacao = new TransacaoPagoLivreVO();
                break;
            case FACILITEPAY:
                transacao = new TransacaoPagoLivreVO();
                break;
            case PINBANK:
                transacao = new TransacaoPinBankVO();
                break;
            case ONE_PAYMENT:
                transacao = new TransacaoOnePaymentVO();
                break;
            case CEOPAG:
                transacao = new TransacaoCeopagVO();
                break;
            case DCC_CAIXA_ONLINE:
                transacao = new TransacaoCaixaVO();
                break;
            default:
                transacao = new TransacaoVO();
                break;
        }
        return transacao;
    }

    private String montarSqlTransacoes(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao,
                                       Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                          String filtro, boolean count, Integer limit, Integer offset, boolean apresentarCobrancaVerificarCartao) throws Exception {

        StringBuilder sqlCondicao = new StringBuilder();

        if (situacao != null && situacao != SituacaoTransacaoEnum.NENHUMA) {
            sqlCondicao.append(" and t.situacao = " + situacao.getId());
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlCondicao.append(" and t.empresa = ").append(empresa).append(" ");
        }

        if (tipoTransacaoEnum != null && tipoTransacaoEnum.getId() != 0) {
            sqlCondicao.append(" and t.tipo = " + tipoTransacaoEnum.getId());
        }

//        if (codigoRetornoPactoEnum != null && codigoRetornoPactoEnum.getCodigo() != 0) {
//            if (tipoTransacaoEnum != null && tipoTransacaoEnum.equals(TipoTransacaoEnum.E_REDE)) {
//                sqlCondicao.append(" and (t.tipo = " + TipoTransacaoEnum.E_REDE.getId() + " and t.codigoretorno in (" + ERedeRetornoEnum.obterCodigosRetorno(codigoRetornoPactoEnum) + "))");
//            }
//        }

        if (!UteisValidacao.emptyString(filtro)) {
            sqlCondicao.append(" and (UPPER(t::text) like '%" + filtro.toUpperCase() + "%' ");
            sqlCondicao.append(" OR (" + TipoTransacaoEnum.getSwitchCaseSSQL(null) + ") like '%" + filtro.toUpperCase() + "%')");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        if (count) {
            sql.append(" count(t.codigo) as total \n");
        } else {
            sql.append(" t.* \n");
        }
        sql.append("FROM transacao t \n");
        if (apresentarCobrancaVerificarCartao) {
            sql.append("WHERE 1 = 1 \n");
        } else {
            sql.append("WHERE t.transacaoVerificarCartao = false \n");
        }
        sql.append("AND t.dataprocessamento::date between '").append(Uteis.getDataFormatoBD(dataInicio)).append("' and '").append(Uteis.getDataFormatoBD(dataFim)).append("' \n");
        sql.append(sqlCondicao);

        if (!count) {
            sql.append("\norder by t.dataprocessamento desc");
        }
        if (!count && !UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }

        return sql.toString();
    }

    @Override
    public TransacaoVO consultarPorChavePrimaria(final int codigo) throws Exception {
        return consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public TransacaoVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM transacao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( Transacao %s)",
                            new Object[]{
                                    codigo
                            }));
                }
                return montarDados(tabelaResultado, con, nivelMontarDados);
            }
        }
    }

    public String consultarParamsRespostaPorChavePrimaria(final int codigo) throws Exception {
        String sql = "SELECT * FROM transacao WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("paramsresposta");
                }
                return null;
            }
        }
    }

    public boolean possuiTransacaoAprovadaCodigoAutenticacao01NosUltimos2Dias(String codigoAutenticacao01) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" WITH recent_transacoes AS ( \n");
        sql.append(" SELECT 1 \n");
        sql.append(" FROM transacao \n");
        sql.append(" WHERE dataprocessamento::date >= (current_date - interval '2 days') \n");
        sql.append(" AND situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(" \n");
        sql.append(" AND outrasinformacoes LIKE '%").append(codigoAutenticacao01).append("%' \n");
        sql.append(")");
        sql.append("SELECT EXISTS (");
        sql.append(" SELECT 1");
        sql.append(" FROM recent_transacoes");
        sql.append(");");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getBoolean("exists");
                }
                return false;
            }
        }
    }

    public List<TransacaoVO> consultarGeradasAgoraPelaRecorrencia(final int codigoEmpresa, final int usuarioRecorrencia) throws Exception {
        if(usuarioRecorrencia == 0){
            Uteis.logar("Multiplos convenios - Usuario recorrencia não encontrado, os creditos não serão contabilizados.");
        }
        String sql = "SELECT * FROM transacao t " +
                "inner join transacaomovparcela tmp on tmp.transacao = t.codigo " +
                "AND t.usuarioresponsavel = ? " +
                "AND t.dataprocessamento >= ? " +
                "AND t.contabilizadaMultiplosConvenios = false " +
                "AND t.empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, usuarioRecorrencia);
        sqlConsultar.setDate(2, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())));
        sqlConsultar.setInt(3, codigoEmpresa);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado, con);
    }


    @Override
    public TransacaoVO consultarPorCodigoExterno(final String codigo) throws Exception {
        String sql = "SELECT * FROM transacao WHERE codigoExterno = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( Transacao de CodigoExterno %s)",
                            new Object[]{
                                    codigo
                            }));
                }
                return (montarDados(tabelaResultado, con));
            }
        }
    }

    @Override
    public List<TransacaoVO> consultarPorRemessa(final int codigoRemessa) throws Exception {
        String sql = "SELECT * FROM transacao WHERE codigo in (select transacao from remessatransacao where remessa = ?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoRemessa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, con);
            }
        }
    }

    @Override
    public List<TransacaoVO> consultar(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa,
                                       TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                       boolean apresentarCobrancaVerificarCartao) throws Exception {
        return consultar(dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, "", 0, 0, apresentarCobrancaVerificarCartao);
    }

    @Override
    public List<TransacaoVO> consultar(Date dataInicio, Date dataFim,
                                       SituacaoTransacaoEnum situacao, Integer empresa,
                                       TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                       String filtro, Integer limit, Integer offset, boolean apresentarCobrancaVerificarCartao) throws Exception {
        String sql = montarSqlTransacoes(dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro, false, limit, offset, apresentarCobrancaVerificarCartao);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con);
            }
        }
    }

    @Override
    public List<TransacaoVO> consultar(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa,
                                       TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                       String filtro, Integer limit, Integer offset, boolean apresentarCobrancaVerificarCartao,
                                       int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.*, \n");
        sql.append("(select count(*) from transacaomovparcela where transacao = t.codigo) as qtd_parcelas_transacao \n");
        sql.append("from transacao t \n");
        sql.append("left join pessoa pes on pes.codigo = t.pessoapagador\n");
        if (apresentarCobrancaVerificarCartao) {
            sql.append("where 1 = 1 \n");
        } else {
            sql.append("where t.transacaoVerificarCartao = false \n");
        }
        sql.append(getCondicao("t", "pes", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append(" order by t.dataprocessamento desc \n");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con, nivelMontarDados);
            }
        }
    }

    @Override
    public Integer obterCountTransacoes(Date dataInicio, Date dataFim,
                                        SituacaoTransacaoEnum situacao, Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                        String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception {
        String sql = montarSqlTransacoes(dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro, true, 0, 0, apresentarCobrancaVerificarCartao);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        }
        return 0;
    }

    private String getCondicao(String prefixoTransacao, String prefixoPessoa,
                               Date dataInicio, Date dataFim,
                               SituacaoTransacaoEnum situacao, Integer empresa,
                               TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum, String filtro) {

        StringBuilder sql = new StringBuilder();

        sql.append(" and ").append(prefixoTransacao).append(".dataprocessamento::date between '").append(Uteis.getDataFormatoBD(dataInicio)).append("' and '").append(Uteis.getDataFormatoBD(dataFim)).append("' \n");

        if (situacao != null && situacao != SituacaoTransacaoEnum.NENHUMA) {
            sql.append(" and ").append(prefixoTransacao).append(".situacao = ").append(situacao.getId()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and ").append(prefixoTransacao).append(".empresa = ").append(empresa).append(" \n");
        }

        if (tipoTransacaoEnum != null && tipoTransacaoEnum.getId() != 0) {
            sql.append(" and ").append(prefixoTransacao).append(".tipo = ").append(tipoTransacaoEnum.getId()).append(" \n");
        }

        if (codigoRetornoPactoEnum != null) {
            if (tipoTransacaoEnum != null && tipoTransacaoEnum.equals(TipoTransacaoEnum.E_REDE)) {
                sql.append(" and (").append(prefixoTransacao).append(".tipo = ").append(TipoTransacaoEnum.E_REDE.getId());
                sql.append(" and ").append(prefixoTransacao).append(".codigoretorno in (").append(ERedeRetornoEnum.obterCodigosRetorno(codigoRetornoPactoEnum)).append(")) \n");
            }
        }

        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" and (UPPER(").append(prefixoTransacao).append("::text) like '%").append(filtro.toUpperCase()).append("%' ");
            if (!UteisValidacao.emptyString(prefixoPessoa)) {
                sql.append("   OR UPPER(").append(prefixoPessoa).append("::text) like '%").append(filtro.toUpperCase()).append("%' ");
            }
            sql.append("   OR (").append(TipoTransacaoEnum.getSwitchCaseSSQL(prefixoTransacao)).append(") like '%").append(filtro.toUpperCase()).append("%') \n");
        }

        return sql.toString();
    }

    @Override
    public List<GrupoTransacoesTO> consultarQuantidadeAgrupandoPorSituacao(Date dataInicio, Date dataFim,
                                                                           SituacaoTransacaoEnum situacao, Integer empresa,
                                                                           TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                           String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.situacao, \n");
        sql.append("count(t.situacao) as qtd, \n");
        sql.append("sum(t.valor) as valor \n");
        sql.append("from transacao t \n");
        if (apresentarCobrancaVerificarCartao) {
            sql.append("where 1 = 1 \n");
        } else {
            sql.append("where t.transacaoVerificarCartao = false \n");
        }
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("group by t.situacao \n");
        sql.append("order by t.situacao \n");

        List<GrupoTransacoesTO> resultado = new ArrayList<GrupoTransacoesTO>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    SituacaoTransacaoEnum sit = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao"));
                    GrupoTransacoesTO g = new GrupoTransacoesTO();
                    g.setSituacao(sit);
                    g.setQuantidade(rs.getInt("qtd"));
                    g.setValor(rs.getDouble("valor"));
                    resultado.add(g);
                }
            }
        }
        return resultado;
    }

    @Override
    public Integer consultarQuantidadeTransacoesASeremCobradas(Date dataInicio, Date dataFim, Integer empresa) throws Exception {
        String condicaoEmpresa = "";
        if (empresa != 0) {
            condicaoEmpresa = " and empresa = " + empresa + " ";
        }
        String sql = "select count(codigo) as cont from transacao "
                + "where dataprocessamento::date between ? and ? and trim(codigoexterno) != ''"
                + condicaoEmpresa;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicio)));
            stm.setDate(2, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataFim)));
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    return rs.getInt("cont");
                }
            }
        }
        return 0;
    }

    @Override
    public Integer consultarQuantidadeTransacoesASeremCobradas(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao,
                                                               Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                               String filtro) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("count(t.codigo) as cont \n");
        sql.append("from transacao t \n");
        sql.append("where 1 = 1 \n");
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append(" and trim(t.codigoexterno) != '' \n");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("cont");
                }
            }
        }
        return 0;
    }

    @Override
    public List<GrupoTransacoesTO> consultarValoresAgrupandoPorSituacao(Date dataInicio, Date dataFim,
                                                                        SituacaoTransacaoEnum situacao, Integer empresa) throws Exception {

        String s = "";
        if (situacao != null && situacao != SituacaoTransacaoEnum.NENHUMA) {
            s = " and situacao = " + situacao.getId() + " ";
        }
        String condicaoEmpresa = "";
        if (empresa != 0) {
            condicaoEmpresa = " and empresa = " + empresa + " ";
        }
        String sql = "select situacao, sum(valor) as valor from transacao "
                + "where dataprocessamento::date between ? and ? " + s + condicaoEmpresa
                + "group by situacao order by situacao";
        List<GrupoTransacoesTO> resultado;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicio)));
            stm.setDate(2, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataFim)));
            try (ResultSet rs = stm.executeQuery()) {
                resultado = new ArrayList<GrupoTransacoesTO>();
                while (rs.next()) {
                    SituacaoTransacaoEnum sit = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao"));
                    double total = rs.getDouble("valor");
                    GrupoTransacoesTO g = new GrupoTransacoesTO();
                    g.setSituacao(sit);
                    g.setValor(total);
                    resultado.add(g);
                }
            }
        }
        return resultado;
    }

    @Override
    public List<GrupoTransacoesTO> consultarValoresAgrupandoPorSituacao(Date dataInicio, Date dataFim,
                                                                        SituacaoTransacaoEnum situacao, Integer empresa,
                                                                        TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                        String filtro) throws Exception {


        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.situacao,  \n");
        sql.append("count(t.codigo) as qtd,  \n");
        sql.append("sum(t.valor) as valor  \n");
        sql.append("from transacao t \n");
        sql.append("where 1 = 1 \n");
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("group by t.situacao\n");
        sql.append("order by t.situacao \n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        List<GrupoTransacoesTO> resultado = new ArrayList<GrupoTransacoesTO>();
        while (rs.next()) {
            SituacaoTransacaoEnum sit = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao"));
            GrupoTransacoesTO g = new GrupoTransacoesTO();
            g.setSituacao(sit);
            g.setQuantidade(rs.getInt("qtd"));
            g.setValor(rs.getDouble("valor"));
            resultado.add(g);
        }
        return resultado;
    }

    public GrupoParcelasTO consultarValoresParcelasAgrupandoPorSituacaoEmAberto(Date dataInicio, Date dataFim, Integer empresa) throws Exception {

        String condicaoEmpresa = "";
        if (empresa != 0) {
            condicaoEmpresa = " and empresa = " + empresa + " ";
        }

        String sql = "select situacao, count(codigo) as cont, sum(valorparcela) as soma from movparcela  "
                + "where codigo in (select movparcela from transacaomovparcela  "
                + "where transacao in (select codigo from transacao "
                + "where dataprocessamento::date between ? and ?)) and situacao = 'EA'"
                + condicaoEmpresa
                + "group by situacao";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicio)));
            stm.setDate(2, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataFim)));
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    GrupoParcelasTO g = new GrupoParcelasTO();
                    g.setSituacao(rs.getString("situacao"));
                    g.setQuantidade(rs.getInt("cont"));
                    g.setValor(rs.getDouble("soma"));
                    return g;
                } else {
                    return null;
                }
            }
        }
    }

    public List<ParcelaTransacaoVO> consultarParcelasTransacao(Date dataInicio,
                                                         Date dataFim,
                                                         SituacaoTransacaoEnum situacao,
                                                         Integer empresa,
                                                         TipoTransacaoEnum tipoTransacaoEnum,
                                                         CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                         String filtro) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select distinct \n");
        sql.append(" pessoa.nome as nomeCliente ");
        sql.append(",cliente.matricula \n");
        sql.append(",cliente.codigo as codigoCliente \n");
        sql.append(",mp.codigo as codigoParcela\n");
        sql.append(",mp.descricao as descricaoParcela\n");
        sql.append(",mp.situacao as situacaoParcela\n");
        sql.append(",mp.valorparcela \n");
        sql.append("from movparcela mp \n");
        sql.append("join pessoa on mp.pessoa = pessoa.codigo \n");
        sql.append("join cliente on cliente.pessoa = pessoa.codigo \n");
        sql.append("join transacaomovparcela tmp on tmp.movparcela = mp.codigo \n");
        sql.append("join transacao t on t.codigo = tmp.transacao \n");
        sql.append("where 1 = 1 \n");
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("order by mp.situacao \n");
        List<ParcelaTransacaoVO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = stm.executeQuery()) {
                while (dadosSQL.next()) {
                    ParcelaTransacaoVO parcelaTransacaoVO = new ParcelaTransacaoVO();
                    parcelaTransacaoVO.setCodigoParcela(dadosSQL.getInt("codigoParcela"));
                    parcelaTransacaoVO.setCodigoCliente(dadosSQL.getInt("codigoCliente"));
                    parcelaTransacaoVO.setDescricaoParcela(dadosSQL.getString("descricaoParcela"));
                    parcelaTransacaoVO.setSituacaoParcela(dadosSQL.getString("situacaoParcela"));
                    parcelaTransacaoVO.setNomeCliente(dadosSQL.getString("nomeCliente"));
                    parcelaTransacaoVO.setMatricula(dadosSQL.getString("matricula"));
                    parcelaTransacaoVO.setValorParcela(dadosSQL.getDouble("valorParcela"));

                    ClienteVO clienteVO = new ClienteVO();
                    clienteVO.setCodigo(parcelaTransacaoVO.getCodigoCliente());

                    parcelaTransacaoVO.setCliente(clienteVO);

                    lista.add(parcelaTransacaoVO);
                }
            }
        }
        return lista;
    }

    public List<GrupoParcelasTO> consultarValoresParcelasAgrupandoPorSituacaoParcela(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao, Integer empresa,
                                                                                     TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                                     String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select situacao\n");
        sql.append("      ,count(codigo) as cont\n");
        sql.append("      ,sum(valorparcela) as soma\n");
        sql.append("      from (\n");
        sql.append("            select distinct \n");
        sql.append("                  mp.situacao,  \n");
        sql.append("                  mp.codigo,  \n");
        sql.append("                  mp.valorparcela\n");
        sql.append("              from movparcela mp \n");
        sql.append("              inner join transacaomovparcela tmp on tmp.movparcela = mp.codigo \n");
        sql.append("              inner join transacao t on t.codigo = tmp.transacao \n");
        sql.append("             where t.transacaoVerificarCartao = false \n");
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("     ) sql \n");
        sql.append("group by situacao \n");
        List<GrupoParcelasTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    GrupoParcelasTO g = new GrupoParcelasTO();
                    g.setSituacao(rs.getString("situacao"));
                    g.setQuantidade(rs.getInt("cont"));
                    g.setValor(rs.getDouble("soma"));
                    lista.add(g);
                }
            }
        }
        return lista;
    }

    public GrupoParcelasTO consultarValoresParcelasAgrupandoPorSituacaoPago(Date dataInicio, Date dataFim, Integer empresa) throws Exception {

        String condicaoEmpresa = "";
        if (empresa != 0) {
            condicaoEmpresa = " and empresa = " + empresa + " ";
        }

        String sql = "select situacao, count(codigo) as cont, sum(valorparcela) as soma from movparcela  "
                + "where codigo in (select movparcela from pagamentomovparcela  "
                + "where movpagamento in (select movpagamento from transacao "
                + "where dataprocessamento::date between ? and ?)) "
                + " and situacao = 'PG' "
                + condicaoEmpresa
                + "group by situacao";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataInicio)));
            stm.setDate(2, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataFim)));
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    GrupoParcelasTO g = new GrupoParcelasTO();
                    g.setSituacao(rs.getString("situacao"));
                    g.setQuantidade(rs.getInt("cont"));
                    g.setValor(rs.getDouble("soma"));
                    return g;
                } else {
                    return null;
                }
            }
        }
    }

    public GrupoParcelasTO consultarValoresParcelasAgrupandoPorSituacaoPago(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao,
                                                                            Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                            String filtro) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.situacao,  \n");
        sql.append("count(mp.codigo) as cont,  \n");
        sql.append("sum(mp.valorparcela) as soma  \n");
        sql.append("from movparcela mp \n");
        sql.append("inner join transacaomovparcela tmp on tmp.movparcela = mp.codigo \n");
        sql.append("inner join transacao t on t.codigo = tmp.transacao \n");
        sql.append("where 1 = 1\n");
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("and mp.situacao = 'PG' \n");
        sql.append("group by mp.situacao \n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        if (rs.next()) {

            GrupoParcelasTO g = new GrupoParcelasTO();
            g.setSituacao(rs.getString("situacao"));
            g.setQuantidade(rs.getInt("cont"));
            g.setValor(rs.getDouble("soma"));
            return g;
        } else {
            return null;
        }
    }

    @Override
    public ContratoRecorrenciaVO obterContratoRecorrenciaPorTransacao(TransacaoVO transacao) throws Exception {
        TransacaoMovParcela transacaoMovParcelaDAO = null;
        ContratoRecorrencia contratoRecorrenciaDAO = null;
        try {
            transacaoMovParcelaDAO = new TransacaoMovParcela(con);
            contratoRecorrenciaDAO = new ContratoRecorrencia(con);

            if (transacao.getCodigo() != 0) {
                List<TransacaoMovParcelaVO> listaMovParcela = transacaoMovParcelaDAO.cosultarPorCodigoTransacao(transacao.getCodigo());
                if (!listaMovParcela.isEmpty()) {
                    TransacaoMovParcelaVO transMovParcela = listaMovParcela.get(0);
                    ContratoVO contrato = transMovParcela.getMovParcela().getContrato();
                    return contratoRecorrenciaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
            return null;
        } finally {
            transacaoMovParcelaDAO = null;
            contratoRecorrenciaDAO = null;
        }
    }

    @Override
    public ClienteVO obterClientePorTransacao(TransacaoVO transacao) throws Exception {
        Cliente clienteDAO = null;
        try {
            clienteDAO = new Cliente(con);
            if (transacao.getCodigo() != 0) {
                return clienteDAO.consultarPorCodigoPessoa(transacao.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }
            return null;
        } finally {
            clienteDAO = null;
        }
    }

    @Override
    public List<MovParcelaVO> obterParcelasDaTransacao(TransacaoVO transacao) throws Exception {
        List<MovParcelaVO> lista = new ArrayList();
        if (transacao.getCodigo() != 0) {
            TransacaoMovParcela transacaoMovParcelaDAO = new TransacaoMovParcela(con);
            List<TransacaoMovParcelaVO> listaMovParcela = transacaoMovParcelaDAO.cosultarPorCodigoTransacao(transacao.getCodigo());
            transacaoMovParcelaDAO = null;
            for (TransacaoMovParcelaVO tranMovParcela : listaMovParcela) {
                lista.add(tranMovParcela.getMovParcela());
            }
        }

        return lista;
    }

    private List<MovParcelaVO> obterParcelasDaTransacaoGerarRecibo(TransacaoVO transacao) throws Exception {
        TransacaoMovParcela transacaoMovParcelaDAO;
        MovParcela movParcelaDAO;
        try {
            transacaoMovParcelaDAO = new TransacaoMovParcela(this.con);
            movParcelaDAO = new MovParcela(this.con);

            List<MovParcelaVO> lista = new ArrayList();
            if (transacao == null || UteisValidacao.emptyNumber(transacao.getCodigo())) {
                return lista;
            }

            Double valorTotalParcelas = 0.0;
            Double valorMulta = 0.0;
            Double valorJuros = 0.0;

            List<TransacaoMovParcelaVO> listaMovParcela = transacaoMovParcelaDAO.cosultarPorCodigoTransacao(transacao.getCodigo());
            for (TransacaoMovParcelaVO tranMovParcela : listaMovParcela) {
                lista.add(tranMovParcela.getMovParcela());
                valorTotalParcelas += tranMovParcela.getValorParcela();
                valorMulta += tranMovParcela.getValorMulta();
                valorJuros += tranMovParcela.getValorJuros();
            }

            //criar parcela de multa e juros
            if (valorTotalParcelas < transacao.getValor() &&
                    (Uteis.arredondarForcando2CasasDecimais(valorTotalParcelas + valorJuros + valorMulta) == Uteis.arredondarForcando2CasasDecimais(transacao.getValor()))) {
                for (TransacaoMovParcelaVO tranMovParcela : listaMovParcela) {

                    // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
                    // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
                    // Por isso a inclusão desses logs
                    if (!UteisValidacao.emptyList(listaMovParcela)) {
                        String codigosMovParcelas = listaMovParcela.stream()
                                .map(p -> String.valueOf(p.getCodigo()))
                                .collect(Collectors.joining(","));
                        Uteis.logarDebug("Transacao - obterParcelasDaTransacaoGerarRecibo - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
                    }

                    if (!UteisValidacao.emptyNumber(tranMovParcela.getValorMulta()) || !UteisValidacao.emptyNumber(tranMovParcela.getValorJuros())) {
                        MovParcelaVO parcelaMultaJuros = movParcelaDAO.criarParcelaMultaJuros(tranMovParcela.getMovParcela(),
                                tranMovParcela.getValorMulta(), tranMovParcela.getValorJuros(), transacao.getUsuarioResponsavel());
                        lista.add(parcelaMultaJuros);
                        TransacaoMovParcelaVO novo = new TransacaoMovParcelaVO();
                        novo.setTransacao(transacao);
                        novo.setMovParcela(parcelaMultaJuros);
                        novo.setValorParcela(parcelaMultaJuros.getValorParcela());
                        novo.setNrTentativaParcela(parcelaMultaJuros.getNrTentativas());
                        transacaoMovParcelaDAO.incluir(novo);
                    }
                }
            }
            return lista;
        } finally {
            transacaoMovParcelaDAO = null;
            movParcelaDAO = null;
        }
    }

    public void estornarTransacoes(boolean estornarOperadora, LogVO log, List<TransacaoVO> listaTransacoes,
                                   UsuarioVO usuario, Boolean desvincularParcela) throws Exception {

        if (listaTransacoes != null) {
            for (TransacaoVO transacaoVO : listaTransacoes) {
                log.setValorCampoAlterado(log.getValorCampoAlterado() + "--------------------------------------\n\r");
                log.setValorCampoAlterado(log.getValorCampoAlterado() + " \n\rTransacao ID = " + transacaoVO.getCodigo()
                        + "\n\rCodigoExterno = " + transacaoVO.getCodigoExterno()
                        + "\n\r" + "NomeTitular = " + transacaoVO.getNomePessoa()
                        + "\n\r" + "Valor = R$ " + transacaoVO.getValor_Apresentar()
                        + "\n\r" + "DataProcessamento = " + transacaoVO.getDataProcessamento_Apresentar()
                        + "\n\r" + "Usuario = " + transacaoVO.getUsuarioResponsavel()
                        + "\n\r" + "Recibo = " + transacaoVO.getReciboPagamento() + "\n\r");
                transacaoVO.setLogEstorno(log.getValorCampoAlterado());
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                        transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) ||
                        transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.PENDENTE) ||
                        !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {

                    //tenta cancelar a transação no Gateway de pagamentos
                    if (estornarOperadora) {
                        try {
                            //usuario responsável cancelamento
                            if (UteisValidacao.emptyNumber(transacaoVO.getUsuarioResponsavelCancelamento().getCodigo())) {
                                transacaoVO.setUsuarioResponsavelCancelamento(usuario);
                            }

                            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobranca(transacaoVO);
                            if (convenioCobrancaVO == null ||
                                    UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                                throw new Exception("Convênio de Cobrança não encontrado.");
                            }
                            transacaoVO.setConvenioCobrancaVO(convenioCobrancaVO);

                            AprovacaoServiceInterface service = CobrancaOnlineService.getImplementacaoAprovacaoService(transacaoVO.getTipo(),
                                    transacaoVO.getEmpresa(), convenioCobrancaVO.getCodigo(), transacaoVO.getTipo().equals(TipoTransacaoEnum.PACTO_PAY), con);
                            if (service != null) {
                                service.cancelarTransacao(transacaoVO, false);
                                if (transacaoVO.getDataHoraCancelamento() == null &&
                                        (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA) ||
                                        transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA))) {
                                    transacaoVO.setDataHoraCancelamento(Calendario.hoje());
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            try {
                                incluirHistoricoRetorno(transacaoVO, ex.getMessage(), "estornarTransacoes", "Estorno");
                            } catch (Exception ex2){
                                ex2.printStackTrace();
                            }
                        }
                    }

                    //retirar vinculos de movpagamento e recibo
                    transacaoVO.setReciboPagamento(0);
                    transacaoVO.setMovPagamento(0);

                    //colocar informando que tem que estornar na operadora
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                            transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) ||
                            transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.PENDENTE)) {
                        transacaoVO.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                    }
                    transacaoVO.setValidarDados(false);
                    alterar(transacaoVO);
                }
                if (desvincularParcela) {
                    //retirar vinculos de parcelas para estornos
                    TransacaoMovParcela transacaoMovParcelaDAO = new TransacaoMovParcela(con);
                    transacaoMovParcelaDAO.excluirPorTransacao(transacaoVO.getCodigo());
                    transacaoMovParcelaDAO = null;
                }
            }
        }
    }

    public static Boolean transacaoJaInserida(TransacaoVO t, List<Integer> lista) {
        for (MovParcelaVO parcela : t.getListaParcelas()) {
            if (lista.contains(parcela.getCodigo())) {
                return true;
            }
        }
        return false;
    }

    public static void addtransacaoJaInserida(TransacaoVO t, List<Integer> lista) {
        for (MovParcelaVO parcela : t.getListaParcelas()) {
            lista.add(parcela.getCodigo());
        }
    }

    public static List<GrupoTransacoesTO> consultarGrupoRepescagem(List<TransacaoVO> listaTransacoes) {
        List<GrupoTransacoesTO> listaGrupo = new ArrayList();
        List<Integer> parcelasAdicionadas = new ArrayList<Integer>();

        GrupoTransacoesTO grupoRepescagemAprovado = new GrupoTransacoesTO();
        grupoRepescagemAprovado.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        //
        GrupoTransacoesTO grupoRepescagemNaoAprovado = new GrupoTransacoesTO();
        grupoRepescagemNaoAprovado.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        //
        for (TransacaoVO t : listaTransacoes) {
            if (!t.getRepescagem().isEmpty()
                    && t.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                grupoRepescagemAprovado.setQuantidade(grupoRepescagemAprovado.getQuantidade() + 1);
                grupoRepescagemAprovado.setValor(Uteis.arredondarForcando2CasasDecimais(
                        grupoRepescagemAprovado.getValor() + t.getValor()));
                addtransacaoJaInserida(t, parcelasAdicionadas);

            }
            if (!t.getRepescagem().isEmpty()
                    && t.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                grupoRepescagemNaoAprovado.setQuantidade(grupoRepescagemNaoAprovado.getQuantidade() + 1);
                if (!transacaoJaInserida(t, parcelasAdicionadas)) {
                    grupoRepescagemNaoAprovado.setValor(Uteis.arredondarForcando2CasasDecimais(
                            grupoRepescagemNaoAprovado.getValor() + t.getValor()));
                }
                addtransacaoJaInserida(t, parcelasAdicionadas);
            }
        }
        //
        if (grupoRepescagemAprovado.getQuantidade().intValue() != 0) {
            listaGrupo.add(grupoRepescagemAprovado);
        }
        if (grupoRepescagemNaoAprovado.getQuantidade().intValue() != 0) {
            listaGrupo.add(grupoRepescagemNaoAprovado);
        }
        return listaGrupo;
    }

    @Override
    public List<TransacaoVO> consultar(String sql, final int nivelMontarDados) throws Exception {
        try (ResultSet tabelaResultado = criarConsulta(sql, con)) {
            return montarDadosConsulta(tabelaResultado, con);
        }
    }

    public List<TransacaoVO> consultarComNivelMontarDados(String sql, final int nivelMontarDados) throws Exception {
        try (ResultSet tabelaResultado = criarConsulta(sql, con)) {
            return montarDadosConsulta(tabelaResultado, con, nivelMontarDados);
        }
    }

    @Override
    public void preencherAtributoEmpresaTodasTransacoes() throws Exception {
        String sql = "select * from transacao where empresa is null order by codigo";
        List<TransacaoVO> listaTransacoes = consultar(sql, Uteis.NIVELMONTARDADOS_TODOS);
        for (TransacaoVO transacaoVO : listaTransacoes) {

            String sqlUpdate = "update transacao set empresa = (select max(empresa) from movparcela where codigo in (select movparcela from transacaomovparcela where transacao = %s )) where codigo = %s";
            sqlUpdate = String.format(sqlUpdate, new Object[]{transacaoVO.getCodigo(), transacaoVO.getCodigo()});
            executarConsulta(sqlUpdate, con);
            Uteis.logar(null, "Atualizada empresa de Transação -> " + transacaoVO.getCodigo());

        }

    }

    private ResultSet montarSqlTransacaoCliente(Integer pessoa, boolean count, int limit, int offset) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append(" COUNT(distinct t.codigo) as total \n");
        } else {
            sql.append(" distinct t.*, \n");
            sql.append(" (select count(*) from transacaomovparcela where transacao = t.codigo) as qtd_parcelas_transacao \n");
        }
        sql.append("FROM Transacao t\n");
        sql.append("WHERE t.transacaoVerificarCartao = false \n");
        sql.append("AND t.codigo in (\n");
        sql.append("select  \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("where t.transacaoVerificarCartao = false \n");
        sql.append("and t.pessoapagador = ").append(pessoa).append(" \n");
        sql.append("union \n");
        sql.append("select \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
        sql.append("where t.transacaoVerificarCartao = false \n");
        sql.append("AND mp.pessoa = ").append(pessoa).append(" \n");
        sql.append(") \n");
        if (!count) {
            sql.append("ORDER BY t.codigo DESC \n");
        }
        if (!count && !UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        return criarConsulta(sql.toString(), con);
    }

    private ResultSet montarSqlUltimaAutorizacaoTransacaoAprovadaPorPessoaECartao(final int codigoPessoa, final String cartaoMascarado, final int empresa, final Integer[] situacoes) throws Exception {

        String condicaoSituacoes = "";
        if (situacoes != null) {
            for (Integer tipo : situacoes) {
                condicaoSituacoes += ",'" + tipo + "'";
            }
            condicaoSituacoes = condicaoSituacoes.replaceFirst(",", "");
        }

        StringBuilder sql = new StringBuilder("SELECT paramsResposta \n");
        sql.append(" FROM transacao t \n");
        sql.append(" WHERE t.situacao in (" + condicaoSituacoes + ") \n");
        sql.append(" AND outrasinformacoes ilike '%" + cartaoMascarado + "%' \n");
        sql.append(" AND pessoapagador = " + codigoPessoa + " \n");
        sql.append(" AND empresa = " + empresa + " \n");
        sql.append(" order by codigo desc \n");
        sql.append(" limit 1 \n");
        return criarConsulta(sql.toString(), con);
    }

    private ResultSet montarSqlTransacaoVerificacaoCliente(Integer pessoa, boolean count, int limit, int offset) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append(" COUNT(distinct t.codigo) as total \n");
        } else {
            sql.append(" distinct t.*, \n");
            sql.append(" (select count(*) from transacaomovparcela where transacao = t.codigo) as qtd_parcelas_transacao \n");
        }
        sql.append("FROM Transacao t\n");
        sql.append("WHERE t.transacaoVerificarCartao = true \n");
        sql.append("AND t.codigo in (\n");
        sql.append("select  \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("where t.transacaoVerificarCartao = true \n");
        sql.append("and t.pessoapagador = ").append(pessoa).append(" \n");
        sql.append("union \n");
        sql.append("select \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
        sql.append("where t.transacaoVerificarCartao = true \n");
        sql.append("AND mp.pessoa = ").append(pessoa).append(" \n");
        sql.append(") \n");
        if (!count) {
            sql.append("ORDER BY t.codigo DESC \n");
        }
        if (!count && !UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        return criarConsulta(sql.toString(), con);
    }

    public Integer obterCountTransacaoCliente(Integer pessoa) throws Exception {
        try (ResultSet rs = montarSqlTransacaoCliente(pessoa, true, 0, 0)) {
            while (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    @Override
    public Integer quantidadePorPessoa(Integer pessoa) throws SQLException {
        String sql = getTelaClienteSQL(true, pessoa, 0, 0);
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet rs = statement.executeQuery();
        if (rs.next()) {
            return rs.getInt("qtd");
        } else {
            return 0;
        }
    }

    @Override
    public List<CancelamentoGetCardTO> consultarPorPessoaTelaCliente(Integer pessoa, int limit, int offset) throws Exception {
        String sql = getTelaClienteSQL(false, pessoa, limit, offset);
        PreparedStatement statement = con.prepareStatement(sql);
        ResultSet resultSet = statement.executeQuery();
        List<CancelamentoGetCardTO> cancelGetCard = new ArrayList<>();
        while (resultSet.next()) {
            cancelGetCard.add(new CancelamentoGetCardTO(Calendario.getData(resultSet.getTimestamp("dataRegistro"), "dd/MM/yyyy HH:mm:ss"),
                    resultSet.getString("convenio"),
                    resultSet.getDouble("valor"),
                    resultSet.getString("dadospedido"),
                    pessoa,
                    resultSet.getString("paramsrespcancel"),
                    resultSet.getString("dadosCancelLog"),
                    resultSet.getString("paramsenvio")));
        }
        return cancelGetCard;
    }

    private String getTelaClienteSQL(boolean count, Integer pessoa, int limit, int offset) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append("COUNT(DISTINCT(p.codigo)) as qtd \n");
        } else {
            sql.append("p.dataregistro, c.descricao as convenio, p2.valor, p2.dadospedido, p2.paramsrespcancel, p.dados as dadosCancelLog, p2.paramsenvio \n");
        }
        sql.append("from pinpadhistorico p \n");
        sql.append("inner join pinpadpedido p2 on p2.pessoa = ").append(pessoa).append(" and p2.codigo = p.pinpadpedido \n");
        sql.append("inner join conveniocobranca c on c.codigo = p2.conveniocobranca \n");
        sql.append("where p.operacao = 'estornoGetcard - success' \n");
        if (!count) {
            sql.append("ORDER BY p.dataregistro desc \n");
            sql.append("LIMIT ").append(limit).append(" \n");
            sql.append("OFFSET ").append(offset).append(" \n");
        }
        return sql.toString();
    }

    public Integer obterCountTransacaoVerificacaoCliente(Integer pessoa) throws Exception {
        try (ResultSet rs = montarSqlTransacaoVerificacaoCliente(pessoa, true, 0, 0)) {
            while (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    @Override
    public List<TransacaoVO> consultarTelaCliente(final int codigoPessoa, int limit, int offset) throws Exception {
        try (ResultSet rs = montarSqlTransacaoCliente(codigoPessoa, false, limit, offset)) {
            return montarDadosConsulta(rs, con, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        }
    }

    public List<TransacaoVO> consultarTelaClienteTransacaoVerificacao(final int codigoPessoa, int limit, int offset) throws Exception {
        try (ResultSet rs = montarSqlTransacaoVerificacaoCliente(codigoPessoa, false, limit, offset)) {
            return montarDadosConsulta(rs, con, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        }
    }

    @Override
    public List<TransacaoVO> consultarPorPessoa(final int codigoPessoa) throws Exception {
        StringBuilder sql = new StringBuilder("select distinct t.* from transacao t "
                + "inner join transacaomovparcela tmp on tmp.transacao = t.codigo "
                + "inner join movparcela mp on mp.codigo = tmp.movparcela "
                + "where mp.pessoa = ").append(codigoPessoa).append(" order by codigo desc");
        return consultar(sql.toString(), Uteis.NIVELMONTARDADOS_TODOS);
    }

    public String consultarParamsRespostaPorPessoaCartaoEmpresaSituacao(final int codigoPessoa, final String cartaoMascarado, final int empresa, final Integer[] situacoes) throws Exception {
        try (ResultSet rs = montarSqlUltimaAutorizacaoTransacaoAprovadaPorPessoaECartao(codigoPessoa, cartaoMascarado, empresa, situacoes)) {
            while (rs.next()) {
                return rs.getString("paramsResposta");
            }
        }
        return "";
    }

    @Override
    public Boolean parcelaEstaEmTransacaoComSituacao(Integer movParcela, SituacaoTransacaoEnum situacao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT 1 ");
        sql.append("FROM transacao t ");
        sql.append("WHERE t.codigo IN ( ");
        sql.append("      SELECT max(tmov.transacao) ");
        sql.append("      FROM transacaomovparcela tmov ");
        sql.append("      WHERE tmov.movparcela = ?) ");
        sql.append("AND t.situacao = ? ");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, movParcela);
            ps.setInt(2, situacao.getId());
            return ps.executeQuery().next();
        }
    }

    @Override
    public List<TransacaoVO> consultarPorTipoESituacao(TipoTransacaoEnum tipo, SituacaoTransacaoEnum situacao,
                                                       Integer convenioCobranca, Integer empresa) throws Exception {
        return consultarPorTipoESituacao(tipo, situacao, null, convenioCobranca, empresa, false, false, null);
    }

    @Override
    public List<TransacaoVO> consultarPorTipoESituacao(TipoTransacaoEnum tipo, SituacaoTransacaoEnum situacao, Boolean aguardandoConfirmacao,
                                                       Integer convenioCobranca, Integer empresa, boolean semTransacaoAtualizadaHoje,
                                                       boolean validarProximaTentativa, Date dataInicialProcessamento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("t.* \n");
        sql.append("FROM transacao t \n");
        sql.append("WHERE t.situacao = ").append(situacao.getId()).append(" \n");
        sql.append("AND t.tipo = ").append(tipo.getId()).append(" \n");
        if (aguardandoConfirmacao != null) {
            sql.append("AND t.aguardandoConfirmacao = ").append(aguardandoConfirmacao).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND t.empresa = ").append(empresa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("AND t.convenioCobranca = ").append(convenioCobranca).append(" \n");
        }
        if (semTransacaoAtualizadaHoje) {
            sql.append(" AND (dataatualizacao is null or dataatualizacao::date <> '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("') \n");
        }
        if (validarProximaTentativa) {
            sql.append(" AND (proximaTentativa is null or '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("' >= proximaTentativa::date) \n");
        }
        if (dataInicialProcessamento != null) {
            sql.append(" AND t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(dataInicialProcessamento)).append("' \n");
        }
        sql.append(" ORDER BY t.codigo \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            return montarDadosConsulta(ps.executeQuery(), con);
        }
    }

    @Override
    public TransacaoVO consultarPorCodigoExternoETipo(String codigoExterno, TipoTransacaoEnum tipo) throws Exception {
        String sql = "SELECT * FROM transacao t WHERE t.tipo = ? AND t.codigoExterno = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, tipo.getId());
            ps.setString(2, codigoExterno);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? montarDados(rs, con) : null;
            }
        }
    }

    private String sqlTotalizadorPorConvenio(Integer codigoEmpresa, Date dataInicio, Date dataFim, TipoTransacaoEnum tipoTransacao, boolean porNrTentativa) {
        StringBuilder sql = new StringBuilder("");

        sql.append("SELECT \n");
        if (porNrTentativa) {
            sql.append("sum(m.valorparcela) as valor, \n");
            sql.append("count(distinct(m.codigo)) as qtd, \n");
            sql.append("(select nrtentativaparcela from movparcelatentativaconvenio where movparcela  = m.codigo order by nrtentativaparcela desc limit 1) as nrtentativas \n");
//            sql.append("m.nrtentativas \n");
        } else {
            sql.append("count(distinct(m.codigo)) as qtd, \n");
            sql.append("sum(m.valorparcela) as valor \n");
        }
        sql.append("FROM movparcela m \n");
        sql.append("INNER JOIN transacaomovparcela tm ON tm.movparcela = m.codigo \n");
        sql.append("INNER JOIN transacao t ON t.codigo = tm.transacao \n");
        sql.append("WHERE t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(" \n");
        sql.append("AND t.dataprocessamento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("AND t.empresa = ").append(codigoEmpresa).append(" \n");
        }

        if (tipoTransacao != null) {
            sql.append("AND t.tipo = ").append(tipoTransacao.getId()).append(" \n");
        }

        if (porNrTentativa) {
            sql.append("group by 3 \n");
            sql.append("order by 3 \n");
        }

        return sql.toString();
    }

    private void totalizadorPorConvenioPorNrTentativa(TotalizadorRemessaTO totalizadorPai, Integer codigoEmpresa, Date dataInicio, Date dataFim, TipoTransacaoEnum tipoTransacao, boolean porNrTentativa) throws Exception {
        String sql = sqlTotalizadorPorConvenio(codigoEmpresa, dataInicio, dataFim, tipoTransacao, true);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorRemessaTO totalizador = new TotalizadorRemessaTO();
                    Integer nrTentativa = rs.getInt("nrtentativas");
                    totalizador.setLabel((nrTentativa + 1) + "º Tentativa");
                    totalizador.setQuantidade(rs.getInt("qtd"));
                    totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    totalizador.calcularPorcentagem(totalizadorPai.getValor());
                    totalizadorPai.getListaFilhoTotalizadorRemessaTO().add(totalizador);
                }
            }
        }
    }

    public void totalizadorPorConvenio(TotalizadorRemessaTO totalizador, Integer codigoEmpresa, Date dataInicio, Date dataFim, TipoTransacaoEnum tipoTransacao) throws Exception {
        String sql = sqlTotalizadorPorConvenio(codigoEmpresa, dataInicio, dataFim, tipoTransacao, false);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    totalizador.setQuantidade(rs.getInt("qtd"));
                    totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                }
            }
        }
        totalizadorPorConvenioPorNrTentativa(totalizador, codigoEmpresa, dataInicio, dataFim, tipoTransacao, true);
    }

    public void totalizadorBIResultadoDCC(TotalizadorRemessaTO totalizador, Integer codigoEmpresa,
                                          Date dataInicio, Date dataFim,
                                          String nrTentativas, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                          boolean somenteMes, boolean somenteForaMes, List<Integer> tiposTransacao, String nrTentativasFiltrar) throws Exception {

        StringBuilder sql = new StringBuilder("");

        sql.append("SELECT \n");
        sql.append("codigo as movparcela, \n");
        sql.append("valorparcela:: NUMERIC as valor \n");
        sql.append("FROM (\n");
        sql.append("select \n");
        sql.append("distinct(mpa.codigo), \n");
        sql.append("mpa.valorparcela, \n");
        sql.append("(select count(*) from transacaomovparcela  where movparcela = mpa.codigo) as nrtentativaparcela  \n");
        sql.append("FROM transacaomovparcela trm\n");
        sql.append("INNER JOIN transacao tr ON trm.transacao = tr.codigo\n");
        sql.append("INNER JOIN movparcela mpa ON mpa.codigo = trm.movparcela  \n");
        sql.append("LEFT JOIN pagamentomovparcela pmp ON pmp.movparcela = trm.movparcela \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pmp.movpagamento \n");
        sql.append("LEFT JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append("WHERE tr.dataprocessamento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("AND tr.empresa = ").append(codigoEmpresa).append(" \n");
        }

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("AND mpa.situacao ").append(situacaoParcela).append(" \n");
        }

        if (!UteisValidacao.emptyString(somenteConvenio)) {
            sql.append(somenteConvenio).append("\n");
        }

//        if (!UteisValidacao.emptyString(nrTentativas)) {
//            sql.append("AND item.nrtentativaparcela ").append(nrTentativas).append(" \n");
//        }

        if (!UteisValidacao.emptyList(tiposTransacao)) {
            sql.append("AND tr.tipo IN ( ").append(Uteis.montarListaIN(tiposTransacao)).append(" ) \n");
        }

        if (!UteisValidacao.emptyString(situacaoRemessa)) {
            sql.append(situacaoRemessa).append(" \n");
        }

        if (!((somenteForaMes && somenteMes) || (!somenteMes && !somenteForaMes))) {
            if (somenteMes) {
                sql.append("AND mpa.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
            }

            if (somenteForaMes) {
                sql.append("AND mpa.datavencimento::date < '").append(Uteis.getData(dataInicio)).append("' \n");
            }
        }

        sql.append(") as foo \n");

        if (!UteisValidacao.emptyString(nrTentativasFiltrar)) {
            sql.append(" where nrtentativaparcela " + nrTentativasFiltrar);
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    MovParcelaVO movParcelaVO = new MovParcelaVO();
                    movParcelaVO.setCodigo(rs.getInt("movparcela"));
                    movParcelaVO.setValorParcela(rs.getDouble("valor"));

                    if (!totalizador.getListaParcelas().contains(movParcelaVO)) {
                        totalizador.getListaParcelas().add(movParcelaVO);
                    }

                    //            totalizador.setQuantidade(totalizador.getQuantidade() + rs.getInt("qtd"));
                    //            totalizador.setValor(totalizador.getValor() + Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                }
            }
        }
        totalizador.processarQtdValor();
    }

    public void atualizarCodigoRetorno(TransacaoVO transacaoVO) {
        atualizarCodigoRetorno(transacaoVO.getCodigoRetornoGestaoTransacao(), transacaoVO.getCodigoRetornoGestaoTransacaoMotivo(), transacaoVO.getCodigo());
    }

    public void atualizarCodigoRetorno(String codigoretorno, String codigoRetornoDescricao, Integer codigo) {
        try {
            String sql = "UPDATE transacao SET codigoretorno = ?, codigoRetornoDescricao = ? WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setString(1, codigoretorno);
                ps.setString(2, codigoRetornoDescricao);
                ps.setInt(3, codigo);
                ps.execute();
            }
        } catch (Exception ignored) {
        }
    }

    public void atualizarRetornoCancelamento(String retornoCancelamento, Integer codigo) {
        try {
            String sql = "UPDATE transacao SET retornoCancelamento = ? WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setString(1, retornoCancelamento);
                ps.setInt(2, codigo);
                ps.execute();
            }
        } catch (Exception ignored) {
        }
    }

    private void marcarTransacaoComoContabilizadaPacto(boolean contabilizada, TransacaoVO transacaoVO) throws Exception {
        if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
            String sql = "UPDATE transacao SET contabilizadaPacto = ? WHERE codigo = ?";
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setBoolean(1, contabilizada);
                stm.setInt(2, transacaoVO.getCodigo());
                stm.execute();
            }
        }
    }


    public void marcarTransacaoComoContabilizadaMultiplosConvenios(TransacaoVO transacaoVO) throws Exception {
        if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
            String sql = "UPDATE transacao SET contabilizadaMultiplosConvenios = true WHERE codigo = ?";
            PreparedStatement stm = con.prepareStatement(sql);
            stm.setInt(1, transacaoVO.getCodigo());
            stm.execute();
        }
    }

    private void verificaDataCreditosPacto(TransacaoVO transacaoVO, EmpresaVO empresa) throws Exception {
        if (transacaoVO.isTransacaoVerificarCartao()) {
            return;
        }
        //TODO: VINDI NÃO GASTA CREDITO DA PACTO DEPENDENDO DA CONFIGURAÇÃO DA EMPRESA CobrarCreditoVindi
        if (!empresa.isCobrarCreditoVindi() && transacaoVO.getTipo().equals(TipoTransacaoEnum.VINDI)) {
            return;
        }

        int creditoDCC = empresa.getCreditoDCC();
        int qtdUsada = 1;

        Date dataExpiracaoCreditoDCC = empresa.getDataExpiracaoCreditoDCC();
        if (dataExpiracaoCreditoDCC != null && Calendario.menorOuIgual(dataExpiracaoCreditoDCC, Calendario.hoje())) {
            throw new ConsistirException("Não é possível criar a transação. Seus créditos estão bloqueados.");
        }

        if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo()) ||
                empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo())) {
            if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                throw new ConsistirException("Não é possível criar a transação. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
            }
        }
    }

    private void validarDebitarCreditosDCC(TransacaoVO transacaoVO, EmpresaVO empresa, Empresa empresaDAO) throws Exception {
        if (transacaoVO.isTransacaoVerificarCartao()) {
            return;
        }
        if(!(empresa.isHabilitarReenvioAutomaticoRemessa() && isUsuarioRecorrencia(transacaoVO))) {
            debitarCreditosDCC(transacaoVO, empresa, empresaDAO);
        }
    }

    private boolean isUsuarioRecorrencia(TransacaoVO transacaoVO) throws Exception {
        Usuario usuarioDAO = new Usuario(con);
        boolean isRecorrencia = usuarioDAO.consultarPorChavePrimaria(transacaoVO.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL).getNome().equals("RECORRENCIA");
        usuarioDAO = null;
        return isRecorrencia;
    }

    public void debitarCreditosDCC(boolean marcarTransacaoComoContabilizadaMultiplosConvenios, List<TransacaoVO> transacoes, EmpresaVO empresa, Empresa empresaDAO, int qtdUsada) throws Exception {
        boolean cobrarCreditoVindi = empresa.isCobrarCreditoVindi();
        for (TransacaoVO transacaoVO : transacoes) {

            if (marcarTransacaoComoContabilizadaMultiplosConvenios) {
                marcarTransacaoComoContabilizadaMultiplosConvenios(transacaoVO);
            }

            if (!cobrarCreditoVindi && TipoTransacaoEnum.VINDI.equals(transacaoVO.getTipo())) {
                marcarTransacaoComoContabilizadaPacto(true, transacaoVO);
                continue;
            }

            if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                marcarTransacaoComoContabilizadaPacto(true, transacaoVO);
            } else {
                marcarTransacaoComoContabilizadaPacto(false, transacaoVO);
            }
        }

        if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
            int creditoDCC = empresa.getCreditoDCC();
            if (creditoDCC - qtdUsada <= 0) {
                RemessaService remessaService;
                try {
                    remessaService = new RemessaService(this.con);
                    remessaService.enviarEmailCreditosAcabando(empresa, null, creditoDCC);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    remessaService = null;
                }
            }
            empresaDAO.debitarCreditoDCC(qtdUsada, empresa.getCodigo(), "TRANSACAO");
            empresa.setCreditoDCC(creditoDCC - qtdUsada);
        }

        if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {
            String chave = "";
            try {
                chave = DAO.resolveKeyFromConnection(con);
            } catch (Exception ignored) {}

            if (UteisValidacao.emptyString(chave)) {
                throw new ConsistirException("Não é possível consultar o crédito disponível da rede de empresa!");
            }

            String urlOamd = PropsService.getPropertyValue(chave, PropsService.urlOamd);
            JSONObject info = empresaDAO.obterInfoRedeDCC(urlOamd, chave);
            int creditoDCC = info.getInt("creditos");
            if (creditoDCC - qtdUsada <= 0) {
                RemessaService remessaService;
                try {
                    remessaService = new RemessaService(this.con);
                    remessaService.enviarEmailCreditosAcabando(empresa, null, creditoDCC);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    remessaService = null;
                }
            }
            if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                throw new ConsistirException("Não é possível criar a transação. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
            }
            empresa.setCreditoDCC(creditoDCC - qtdUsada);
        }
    }


    public void debitarCreditosDCC(TransacaoVO transacaoVO, EmpresaVO empresa, Empresa empresaDAO) throws Exception {

        //TODO: VINDI NÃO GASTA CREDITO DA PACTO DEPENDENDO DA CONFIGURAÇÃO DA EMPRESA CobrarCreditoVindi
        if (!empresa.isCobrarCreditoVindi() && transacaoVO.getTipo().equals(TipoTransacaoEnum.VINDI)) {
            marcarTransacaoComoContabilizadaPacto(true, transacaoVO);
            return;
        }

        int creditoDCC = empresa.getCreditoDCC();
        Integer qtdUsada = 1;

        if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {

            if (creditoDCC - qtdUsada <= 0) {
                RemessaService remessaService;
                try {
                    remessaService = new RemessaService(this.con);
                    remessaService.enviarEmailCreditosAcabando(empresa, null, creditoDCC);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    remessaService = null;
                }
            }
            marcarTransacaoComoContabilizadaPacto(true, transacaoVO);
            empresaDAO.debitarCreditoDCC(qtdUsada, empresa.getCodigo(), "TRANSACAO");
            empresa.setCreditoDCC(creditoDCC - qtdUsada);

        } else {
            marcarTransacaoComoContabilizadaPacto(false, transacaoVO);
        }

        if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {

            String chave = "";
            try {
                chave = DAO.resolveKeyFromConnection(con);
            } catch (Exception e) {
                chave = "";
            }

            if (UteisValidacao.emptyString(chave)) {
                throw new ConsistirException("Não é possível consultar o crédito disponível da rede de empresa!");
            }

            String urlOamd = PropsService.getPropertyValue(chave, PropsService.urlOamd);

            JSONObject info = null;
            try {
                info = empresaDAO.obterInfoRedeDCC(urlOamd, chave);
            } catch (Exception ex){
                throw new ConsistirException("Não foi possível obter infoRedeDcc para debitar os créditos para Transação");
            }

            creditoDCC = info.getInt("creditos");
            if (creditoDCC - qtdUsada <= 0) {
                RemessaService remessaService;
                try {
                    remessaService = new RemessaService(this.con);
                    remessaService.enviarEmailCreditosAcabando(empresa, null, creditoDCC);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    remessaService = null;
                }
            }
            if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                throw new ConsistirException("Não é possível criar a transação. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
            }
            empresa.setCreditoDCC(creditoDCC - qtdUsada);
        }
    }

    public void excluirParcelaMultaJurosTransacao(TransacaoVO transacaoVO, MovParcelaVO parcelaMultaJuros) {
        MovProdutoParcela movProdutoParcelaDAO;
        MovParcela movParcelaDAO;
        MovProduto movProdutoDAO;
        TransacaoMovParcela transacaoMovParcelaDAO;
        PixMovParcela pixMovParcelaDAO;

        try {
            movProdutoParcelaDAO = new MovProdutoParcela(con);
            movParcelaDAO = new MovParcela(con);
            movProdutoDAO = new MovProduto(con);
            transacaoMovParcelaDAO = new TransacaoMovParcela(con);
            pixMovParcelaDAO = new PixMovParcela(con);

            if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) { //caso já tenha criado a transação

                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                        transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    return;
                }

                List<MovParcelaVO> listaParcelasTransacao = obterParcelasDaTransacao(transacaoVO);
                for (MovParcelaVO movParcelaVO : listaParcelasTransacao) {
                    List<MovProdutoParcelaVO> movProdutoParcelaVOS = movProdutoParcelaDAO.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (MovProdutoParcelaVO mppVO : movProdutoParcelaVOS) {
                        if (!UteisValidacao.emptyNumber(mppVO.getMovParcelaOriginalMultaJuros().getCodigo()) || movParcelaVO.getDescricao().contains("- MULTA E JUROS")) {
                            Uteis.logar(null, "Excluir parcela de MULTA E JUROS | Transação " + transacaoVO.getCodigo() + " | Parcela " + mppVO.getMovParcela());
                            movProdutoDAO.excluirSemCommit(mppVO.getMovProduto());
                            transacaoMovParcelaDAO.excluirPorMovParcela(mppVO.getMovParcela());
                            movParcelaDAO.excluirSemCommit(mppVO.getMovParcela());
                        }
                    }
                }


            } else if (parcelaMultaJuros != null) { //caso só tenha criado a parcela e a transacao nao foi criada

                List<MovProdutoParcelaVO> movProdutoParcelaVOS = movProdutoParcelaDAO.consultarPorCodigoMovParcela(parcelaMultaJuros.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (MovProdutoParcelaVO mppVO : movProdutoParcelaVOS) {
                    if (!UteisValidacao.emptyNumber(mppVO.getMovParcelaOriginalMultaJuros().getCodigo()) || parcelaMultaJuros.getDescricao().contains("- MULTA E JUROS")) {
                        Uteis.logar(null, "Excluir parcela de MULTA E JUROS | Parcela " + mppVO.getMovParcela());
                        pixMovParcelaDAO.excluirPorParcela(mppVO.getMovParcela());
                        movProdutoDAO.excluirSemCommit(mppVO.getMovProduto());
                        movParcelaDAO.excluirSemCommit(mppVO.getMovParcela());
                    }
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "ERRO excluirParcelaMultaJurosTransacao | ERRO: " + ex.getMessage());
        }finally {
            movProdutoParcelaDAO = null;
            movParcelaDAO = null;
            movProdutoDAO = null;
            transacaoMovParcelaDAO = null;
            pixMovParcelaDAO = null;
        }
    }

    public void incluirHistoricoRetorno(TransacaoVO obj, final String retorno,
                                        final String metodo, final String origemSincronizacao) throws Exception {
        try {
            String sql = "INSERT INTO historicoRetornoTransacao(data, transacao, paramsresposta, metodo, origemSincronizacao) VALUES (?,?,?,?,?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));


                if (obj == null || UteisValidacao.emptyNumber(obj.getCodigo())) {
                    sqlInserir.setNull(++i, Types.NULL);
                } else {
                    sqlInserir.setInt(++i, obj.getCodigo());
                }

                sqlInserir.setString(++i, retorno);

                if (metodo != null && metodo.length() > 150) {
                    sqlInserir.setString(++i, metodo.substring(0, 150));
                } else {
                    sqlInserir.setString(++i, metodo);
                }

                if (origemSincronizacao != null && origemSincronizacao.length() > 150) {
                    sqlInserir.setString(++i, origemSincronizacao.substring(0, 150));
                } else {
                    sqlInserir.setString(++i, origemSincronizacao);
                }

                sqlInserir.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void incluirHistoricoRetorno(TransacaoVO obj, final String retorno,
                                        final String metodo, final String origemSincronizacao, int statusServer, long tempoRequisicao) throws Exception {
        try {
            String sql = "INSERT INTO historicoRetornoTransacao(data, transacao, paramsresposta, metodo, origemSincronizacao, statusServer, tempoRequisicao) VALUES (?,?,?,?,?,?,?)";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 0;
                sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));

                if (obj == null || UteisValidacao.emptyNumber(obj.getCodigo())) {
                    sqlInserir.setNull(++i, Types.NULL);
                } else {
                    sqlInserir.setInt(++i, obj.getCodigo());
                }

                sqlInserir.setString(++i, retorno);

                if (metodo != null && metodo.length() > 150) {
                    sqlInserir.setString(++i, metodo.substring(0, 150));
                } else {
                    sqlInserir.setString(++i, metodo);
                }

                if (origemSincronizacao != null && origemSincronizacao.length() > 150) {
                    sqlInserir.setString(++i, origemSincronizacao.substring(0, 150));
                } else {
                    sqlInserir.setString(++i, origemSincronizacao);
                }

                if (!UteisValidacao.emptyNumber(statusServer)) {
                    sqlInserir.setInt(++i, statusServer);
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }

                if (!UteisValidacao.emptyNumber(tempoRequisicao)) {
                    sqlInserir.setLong(++i, tempoRequisicao);
                } else {
                    sqlInserir.setNull(++i, Types.NULL);
                }

                sqlInserir.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void alterarMessagemErro(TransacaoVO obj, final String erro) throws Exception {
        try {
            String sql = "update  transacao set erroProcessamento = ? where codigo = ?";

            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {

                sqlAlterar.setString(1, erro);
                sqlAlterar.setInt(2, obj.getCodigo());

                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public JSONObject gerarReciboTransacoesSemRecibo() {
        JSONObject json = new JSONObject();
        json.put("sucesso", true);
        Integer ajustado = 0;
        Integer erro = 0;
        Integer total = 0;
        JSONArray listaErros = new JSONArray();
        try {
            Uteis.logarDebug("GerarReciboTransacao | Início...");

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.codigo as transacao, \n");
            sql.append("t.conveniocobranca \n");
            sql.append("from transacao t \n");
            sql.append("where t.recibopagamento is null \n");
            sql.append("and t.transacaoverificarcartao = false \n");
            sql.append("and t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(" \n");
            sql.append("and exists( \n");
            sql.append("select \n");
            sql.append("mp.codigo \n");
            sql.append("from transacaomovparcela tm \n");
            sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
            sql.append("where tm.transacao = t.codigo \n");
            sql.append("and mp.situacao = 'EA') \n");
            sql.append("order by 1 ");

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

            Uteis.logarDebug("GerarReciboTransacao | Total " + total);
            Integer atual = 0;
            try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = stm.executeQuery()) {
                    Integer transacao = 0;
                    String msg = "";
                    while (rs.next()) {
                        try {
                            transacao = rs.getInt("transacao");
                            Integer conveniocobranca = rs.getInt("conveniocobranca");
                            if (UteisValidacao.emptyNumber(conveniocobranca)) {
                                ProcessoPreencharConvenioCobrancaTransacao.corrigir(con, transacao);
                            }
                            Integer recibo = GerarReciboTransacao.gerarReciboDaTransacao(con, transacao, true);
                            if (UteisValidacao.emptyNumber(recibo)) {
                                throw new Exception("Não foi gerado recibo");
                            }
                            msg = ("Recibo gerado " + recibo);
                            ajustado++;
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg = ("ERRO: " + ex.getMessage());
                            erro++;
                            JSONObject jsonErro = new JSONObject();
                            jsonErro.put("transacao", transacao);
                            jsonErro.put("erro", ex.getMessage());
                            listaErros.put(jsonErro);
                        } finally {
                            Uteis.logarDebug("GerarReciboTransacao | " + ++atual + "/" + total + " - Transacao " + transacao + " | Resultado: " + msg);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("GerarReciboTransacao | Erro: " + ex.getMessage());
            json.put("sucesso", false);
            json.put("msg", ex.getMessage());
        } finally {
            Uteis.logarDebug("GerarReciboTransacao | Fim...");
        }
        json.put("ajustado", ajustado);
        json.put("erro", erro);
        json.put("total", total);
        if (listaErros.length() > 0) {
            json.put("lista_erros", listaErros);
        }
        return json;
    }

    public void gerarLogErroTentativaCobranca(Integer pessoa, final String msgException) {
        try {

            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Pessoa não informada... " + pessoa);
            }

            LogVO log = new LogVO();
            log.setNomeEntidade("PESSOA");
            log.setNomeEntidadeDescricao("PESSOA");
            log.setDescricao("COBRANCA-GETNET-ONLINE");
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(null);
            log.setResponsavelAlteracao("");
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("COBRANCA-GETNET-ONLINE");
            log.setUserOAMD("");
            log.setPessoa(pessoa);
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(msgException);
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, "Erro ao gerarLogErroTentativaCobranca " + e.getMessage());
        }
    }

    public String cancelarTransacao(TransacaoVO transacaoVO, boolean estornarRecibo, UsuarioVO usuarioVO, String chave) throws Exception {
        try {

            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Transação inválida");
            }
            if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno()) &&
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) &&
                    transacaoVO.getTipoOrigem().equals(TipoTransacaoEnum.STONE_ONLINE)) {
                throw new Exception("Transação não capturada corretamente na Stone. Se você usa conciliação, aguarde o dia seguinte para o sistema validar automaticamente na Stone se ela foi cobrada de fato ou não. Se não usa, entre em contato com a Pacto.");
            }
            if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno()) &&
                    !transacaoVO.getTipoOrigem().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
                throw new Exception("Transação sem código externo");
            }
            if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno()) &&
                    transacaoVO.getTipoOrigem().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE) &&
                    !transacaoVO.getCodigoRetornoDescricao().contains("504 Gateway Time-out")) {
                throw new Exception("Transação sem código externo");
            }
            if (!transacaoVO.isPermiteCancelar()) {
                throw new Exception("Transação não pode ser cancelada");
            }
            if (usuarioVO == null  || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário logado não informado");
            }
            transacaoVO.setUsuarioResponsavelCancelamento(usuarioVO);

            //validarMovimentacoesFinanceiras
            validarMovimentacoesFinanceiras(transacaoVO);

            //obter o convênio de cobrança
            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobranca(transacaoVO);
            if (convenioCobrancaVO == null ||
                    UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                throw new Exception("Convênio de Cobrança não encontrado.");
            }
            transacaoVO.setConvenioCobrancaVO(convenioCobrancaVO);


            AprovacaoServiceInterface aprovacaoServiceInterface = CobrancaOnlineService.getImplementacaoAprovacaoService(transacaoVO.getTipo(),
                    transacaoVO.getEmpresa(), convenioCobrancaVO.getCodigo(), transacaoVO.getTipo().equals(TipoTransacaoEnum.PACTO_PAY), this.con);
            if (aprovacaoServiceInterface == null) {
                throw new Exception("Não foi posssível cancelar a transação. AprovacaoService null");
            }

            if (UteisValidacao.emptyList(transacaoVO.getListaParcelas())) {
                Uteis.logarDebug("Transacao - Lista de parcelas transação vazia | Transação " + transacaoVO.getCodigo());
                transacaoVO.setListaParcelas(obterParcelasDaTransacao(transacaoVO));
            }

            TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);
            SituacaoTransacaoEnum sitAnterior = transacaoAnterior.getSituacao();

            aprovacaoServiceInterface.cancelarTransacao(transacaoVO, estornarRecibo);
            SituacaoTransacaoEnum sitNova = transacaoVO.getSituacao();

            if ((sitAnterior != sitNova) && (sitNova == SituacaoTransacaoEnum.CANCELADA)) {

                gravarLogTransacao("TRANSACAO-CANCELADA", transacaoAnterior, transacaoVO, usuarioVO);

                transacaoVO.setUrlComprovanteCancelamento(gerarURLComprovanteCancelamentoTransacao(transacaoVO, chave));

                //enviar resultado da cobrança régua de cobrança PactoPay
                processarResultadoCobrancaReguaCobranca(transacaoVO);

                if (sitAnterior.equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    return "Transação cancelada na adquirente.";
                } else {
                    return "Transação cancelada com sucesso.";
                }
            } else {
                throw new Exception("Não foi possível cancelar a transação: " + transacaoVO.getValorAtributoCancelamento(APF.ResultSolicCancel));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public ConvenioCobrancaVO obterConvenioCobranca(TransacaoVO transacaoVO) throws Exception {
        if (UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {

            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(this.con);
            List<ConvenioCobrancaVO> listaConvenios = convenioCobrancaDAO.consultarTodosPorSituacaoEmpresa(transacaoVO.getEmpresa(), SituacaoConvenioCobranca.ATIVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            convenioCobrancaDAO = null;

            List<ConvenioCobrancaVO> listaConv = new ArrayList<>();
            for (ConvenioCobrancaVO obj : listaConvenios) {
                if (!obj.getTipo().getTipoTransacao().equals(TipoTransacaoEnum.NENHUMA) &&
                        obj.getTipo().getTipoTransacao().equals(transacaoVO.getTipoOrigem())) {
                    listaConv.add(obj);
                }
            }

            if (listaConv.size() == 1) {
                return listaConv.get(0);
            }
            return null;
        } else {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(this.con);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            convenioCobrancaDAO = null;
            return convenioCobrancaVO;
        }
    }

    public void validarMovimentacoesFinanceiras(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyNumber(transacaoVO.getMovPagamento())) {
            ConfiguracaoFinanceiro configuracaoFinanceiroDAO = new ConfiguracaoFinanceiro(this.con);
            ConfiguracaoFinanceiroVO confFinan = configuracaoFinanceiroDAO.consultar();
            configuracaoFinanceiroDAO = null;

            if (confFinan.getUsarMovimentacaoContas()) {
                MovPagamento movPagamentoDAO = new MovPagamento(this.con);
                MovPagamentoVO movPag = movPagamentoDAO.consultarPorChavePrimaria(transacaoVO.getMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                movPagamentoDAO = null;

                if (!movPag.getCredito()) {
                    CartaoCredito cartaoCreditoDAO = new CartaoCredito(this.con);
                    boolean verificarContemLote = cartaoCreditoDAO.verificarContemLote(movPag.getCodigo());
                    cartaoCreditoDAO = null;
                    if (verificarContemLote) {
                        throw new Exception("Esta transação não pode ser estornada porque possui cartões que se encontram em lotes do Financeiro Web. Faça as movimentações necessárias e tente novamente.");
                    }
                }
            }
        }
    }

    public static String gerarURLComprovanteCancelamentoTransacao(TransacaoVO transacaoVO, String chave) {
        try {
            if (transacaoVO != null &&
                    !UteisValidacao.emptyString(chave) &&
                    !UteisValidacao.emptyNumber(transacaoVO.getCodigo()) &&
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {

                JSONObject json = new JSONObject();
                json.put("chave", chave);
                json.put("transacao", transacaoVO.getCodigo());

                return (Uteis.getUrlAPI() + "/prest/util/" + chave+ "/impressao/" + Uteis.encriptar(json.toString(), Uteis.getChaveCriptoImpressaoServlet()));
            } else {
                return "";
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public void gravarLogTransacao(String operacao, TransacaoVO transacaoAnteriorVO, TransacaoVO transacaoAtualVO, UsuarioVO usuarioVO) {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("TRANSACAO");
            log.setNomeEntidadeDescricao("TRANSACAO");
            log.setChavePrimaria(transacaoAtualVO.getCodigo().toString());
            log.setNomeCampo("TRANSACAO");
            log.setValorCampoAnterior("");
            JSONObject json = new JSONObject();
            json.put("transacao", transacaoAtualVO.getCodigo());
            json.put("operacao", operacao.toUpperCase());
            json.put("situacaoAtual", transacaoAtualVO.getSituacao().getDescricao());
            if (transacaoAnteriorVO != null) {
                json.put("situacaoAnterior", transacaoAnteriorVO.getSituacao().getDescricao());
            }
            log.setValorCampoAlterado(json.toString());
            log.setDataAlteracao(Calendario.hoje());

            try {
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("");
            }

            log.setOperacao(operacao.toUpperCase());
            log.setPessoa(transacaoAtualVO.getPessoaPagador().getCodigo());

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro ao gravarLogTransacao " + ex.getMessage());
        }
    }

    public List<GrupoTransacoesErrosTO> consultarErrosAgrupandoPorTipoTransacao(Date dataInicio, Date dataFim,
                                                                                SituacaoTransacaoEnum situacao, Integer empresa,
                                                                                TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                                String filtro) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.tipo, \n");
        sql.append("t.codigoretorno, \n");
        sql.append("count(t.codigo) as qtd, \n");
        sql.append("sum(t.valor) as valor \n");
        sql.append("from transacao t \n");
        sql.append("where 1 = 1 \n");
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("group by 1,2 \n");
        sql.append("order by 1,3 desc \n");

        Map<TipoTransacaoEnum, GrupoTransacoesErrosTO> map = new HashMap<>();

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TipoTransacaoEnum tipo = TipoTransacaoEnum.getTipoTransacaoEnum(rs.getInt("tipo"));

                    GrupoTransacoesErrosTO grupoTipo = map.get(tipo);
                    if (grupoTipo == null) {
                        grupoTipo = new GrupoTransacoesErrosTO();
                        grupoTipo.setTipo(tipo);
                    }

                    String codErro = rs.getString("codigoretorno");
                    Integer qtd = rs.getInt("qtd");
                    Double valor = rs.getDouble("valor");

                    GrupoTransacoesTO grupoErro = new GrupoTransacoesTO();
                    grupoErro.setCodErro(codErro);
                    grupoErro.setMsgErro(Uteis.obterMensagemRetornoTransacaoOnline(tipoTransacaoEnum, codErro));
                    grupoErro.setQuantidade(qtd);
                    grupoErro.setValor(valor);
                    grupoTipo.getErros().add(grupoErro);
                    map.put(tipo, grupoTipo);
                }
            }
        }

        List<GrupoTransacoesErrosTO> retorno = new ArrayList<>();
        retorno.addAll(map.values());
        return retorno;
    }

    public List<GrupoTransacoesErrosTO> consultarTotalizadorTransacaoPortipo(Date dataInicio, Date dataFim, SituacaoTransacaoEnum situacao,
                                                                             Integer empresa, TipoTransacaoEnum tipoTransacaoEnum, CodigoRetornoPactoEnum codigoRetornoPactoEnum,
                                                                             String filtro, boolean apresentarCobrancaVerificarCartao) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.tipo, \n");
        sql.append("t.situacao, \n");
        sql.append("count(t.codigo) as qtd, \n");
        sql.append("sum(t.valor) as valor \n");
        sql.append("from transacao t \n");
        if (apresentarCobrancaVerificarCartao) {
            sql.append("where 1 = 1 \n");
        } else {
            sql.append("where t.transacaoVerificarCartao = false \n");
        }
        sql.append(getCondicao("t", "", dataInicio, dataFim, situacao, empresa, tipoTransacaoEnum, codigoRetornoPactoEnum, filtro));
        sql.append("group by 1,2 \n");
        sql.append("order by 1,2 \n");

        Map<TipoTransacaoEnum, GrupoTransacoesErrosTO> map = new HashMap<>();

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {

                    TipoTransacaoEnum tipo = TipoTransacaoEnum.getTipoTransacaoEnum(rs.getInt("tipo"));

                    GrupoTransacoesErrosTO grupoTipo = map.get(tipo);
                    if (grupoTipo == null) {
                        grupoTipo = new GrupoTransacoesErrosTO();
                        grupoTipo.setTipo(tipo);
                    }


                    SituacaoTransacaoEnum situacaoEnum = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao"));
                    Integer qtd = rs.getInt("qtd");
                    Double valor = rs.getDouble("valor");

                    GrupoTransacoesTO grupoErro = new GrupoTransacoesTO();
                    grupoErro.setSituacao(situacaoEnum);
                    grupoErro.setQuantidade(qtd);
                    grupoErro.setValor(valor);
                    grupoTipo.getErros().add(grupoErro);
                    map.put(tipo, grupoTipo);
                }
            }
        }

        List<GrupoTransacoesErrosTO> retorno = new ArrayList<>();
        retorno.addAll(map.values());
        return retorno;
    }

    public void excluirCartaoVindi(String codigoExterno, ConvenioCobrancaVO obj) {
        VindiService vindiService = null;
        try {
            //excluir na vindi após alteração na autorização de ocbrança
            if (obj.getSituacao() != null &&
                    obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                    obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                    !UteisValidacao.emptyString(codigoExterno)) {
                vindiService = new VindiService(this.con, obj.getEmpresa().getCodigo(), obj.getCodigo());
                vindiService.excluirPerfilPagamento(Integer.parseInt(codigoExterno));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            vindiService = null;
        }
    }

    public void incluirTransacaoWebhook(Integer tipotransacao, String webhook) throws Exception {
        try {
            String sql = "INSERT INTO transacaowebhook(data, webhook, tipotransacao) VALUES (?,?,?)";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                int i = 0;
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                pst.setString(++i, webhook);
                if (UteisValidacao.emptyNumber(tipotransacao)) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, tipotransacao);
                }
                pst.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String sincronizarTransacao(TransacaoVO transacaoVO, UsuarioVO usuarioVO, boolean validarSincronizar) throws Exception {
        VerificadorTransacaoService service = null;
        try {
            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Transação inválida");
            }

            if (transacaoVO.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE) &&
                    UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
            }

            if (!transacaoVO.getTipo().equals(TipoTransacaoEnum.PAGAR_ME) &&
                    !transacaoVO.getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE) &&
                    UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                throw new Exception("Transação sem código externo");
            }
            if (validarSincronizar && !transacaoVO.isPermiteSincronizar()) {
                throw new Exception("Transação não pode ser sincronizada");
            }
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não informado");
            }

            TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);

            service = new VerificadorTransacaoService(this.con);
            service.verificarAlteracoesTransacao(transacaoVO, true);

            gravarLogTransacao("TRANSACAO-SINCRONIZAR", transacaoAnterior, transacaoVO, usuarioVO);

            if (transacaoAnterior.getSituacao().equals(transacaoVO.getSituacao())) {
                return "Transação não sofreu alterações.";
            }

            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                return "Transação está paga. Foi realizado o pagamento no sistema, gerando o recibo do pagamento.";
            }

            return "Transação foi atualizada para a situação \"" + transacaoVO.getSituacao().getDescricao().replace("Transação ", "") + "\".";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            service = null;
        }
    }

    public List<TransacaoVO> consultarPorParcela(Integer codigoParcela) throws Exception {
        return consultarPorParcela(codigoParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public List<TransacaoVO> consultarPorParcela(Integer codigoParcela, int nivelMontarDados) throws Exception {
        String sql = "SELECT distinct t.* FROM transacao t  inner join transacaomovparcela tm on tm.transacao = t.codigo WHERE tm.movparcela = ? order by codigo";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoParcela);
            return montarDadosConsulta(ps.executeQuery(), con, nivelMontarDados);
        }
    }

    public List<ObjetoGenerico> obterTransacoesDuplicadas() throws Exception {
        List<ObjetoGenerico> clientesDuplicados = new ArrayList<ObjetoGenerico>();
        StringBuilder sqlDuplicados = new StringBuilder();
        sqlDuplicados.append("select m.codigo as parcela,coalesce(m.contrato,0) as contrato, coalesce(c.codigomatricula,0) as matricula, p.nome, count(t.codigo)as nrtransacoes,\n");
        sqlDuplicados.append("   min(dataprocessamento) as primeiratentativa, max(dataprocessamento) as ultimatentativa from transacao t \n");
        sqlDuplicados.append("  inner join transacaomovparcela tm on tm.transacao = t.codigo inner  join movparcela m on m.codigo = tm.movparcela \n");
        sqlDuplicados.append("  inner join pessoa p on p.codigo = m.pessoa\n");
        sqlDuplicados.append("left join cliente c on c.pessoa = p.codigo \n");
        sqlDuplicados.append("where t.situacao in (2,4)  \n");
        sqlDuplicados.append(" group by 1,2,3,4\n");
        sqlDuplicados.append("having count(t.codigo) > 1 and min(dataprocessamento) >  '").append(Uteis.getDataJDBC(Uteis.somarDias(Calendario.hoje(), -25))).append("'");
        Statement stm = con.createStatement();
        ResultSet resultSet = stm.executeQuery(sqlDuplicados.toString());
        while (resultSet.next()) {
            String nome = "Parcela "+resultSet.getInt("parcela")+" do contrato "+resultSet.getInt("contrato")+", "+resultSet.getString("nome")+", mat: "+resultSet.getInt("matricula");
            String duplicacoes = resultSet.getString("nrtransacoes");
            ObjetoGenerico objetoGenerico = new ObjetoGenerico(nome, duplicacoes);
            clientesDuplicados.add(objetoGenerico);
        }
        return clientesDuplicados;
    }

    public void alterarDataAtualizacao(final TransacaoVO obj, boolean atualizarParamsResposta) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE transacao set dataAtualizacao = ? ");
        if (atualizarParamsResposta) {
            sql.append(", paramsresposta = ? ");
        }
        sql.append(" WHERE codigo = ? ");
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
            int i = 0;
            sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataAtualizacao()));
            if (atualizarParamsResposta) {
                sqlAlterar.setString(++i, obj.getParamsResposta());
            }
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterarSituacao(final TransacaoVO obj) throws Exception {
        String sql = ("UPDATE transacao set situacao = ? WHERE codigo = ? ");
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setInt(++i, obj.getSituacao().getId());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public String retentativaTransacao(TransacaoVO transacaoVO, UsuarioVO usuarioVO,
                                       Integer convenioCobranca, String ipCliente,
                                       OrigemCobrancaEnum origemCobrancaEnum) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        MovParcela movParcelaDAO = null;
        TransacaoMovParcela transacaoMovParcelaDAO = null;
        PagamentoService pagamentoService = null;
        try {
            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Transação inválida");
            }
            if (!transacaoVO.isPermiteRetentativa()) {
                throw new Exception("Transação não pode ser realizada retentativa");
            }
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyNumber(convenioCobranca)) {
                throw new Exception("Convênio de Cobrança não informado.");
            }
            if (UteisValidacao.emptyNumber(transacaoVO.getEmpresaVO().getCodigo())) {
                throw new Exception("Empresa não encontrada.");
            }
            if (origemCobrancaEnum == null) {
                origemCobrancaEnum = OrigemCobrancaEnum.ZW_MANUAL_RETENTATIVA;
            }

            convenioCobrancaDAO = new ConvenioCobranca(this.con);
            transacaoMovParcelaDAO = new TransacaoMovParcela(this.con);
            movParcelaDAO = new MovParcela(this.con);

            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, transacaoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if (UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                throw new Exception("Convênio de cobrança não encontrado.");
            }

            List<TransacaoMovParcelaVO> listaTransacaoParcela = transacaoMovParcelaDAO.cosultarPorCodigoTransacao(transacaoVO.getCodigo());

            List<MovParcelaVO> listaParcelas = new ArrayList<>();
            for (TransacaoMovParcelaVO transacaoMovParcelaVO : listaTransacaoParcela) {
                if (transacaoMovParcelaVO.getMovParcela() != null &&
                        !UteisValidacao.emptyNumber(transacaoMovParcelaVO.getMovParcela().getCodigo())) {
                    MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(transacaoMovParcelaVO.getMovParcela().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (movParcelaVO.getSituacao().equalsIgnoreCase("EA") && !movParcelaVO.getDescricao().toUpperCase().startsWith("MULTA E JUROS - PARCELA")) {
                        listaParcelas.add(movParcelaVO);
                    }
                }
            }
            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Nenhuma parcela encontrada.");
            }

            List<String> msgErro = new ArrayList<>();

            if (transacaoVO.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)) {
                if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                    transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
                    if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                        alterarCodigoExterno(transacaoVO);
                    }
                }
                if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno()) &&
                        transacaoVO.getConvenioCobrancaVO() != null &&
                        !UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo()) &&
                        (convenioCobrancaVO.getCodigo().equals(transacaoVO.getConvenioCobrancaVO().getCodigo()))) {
                    sincronizarTransacao(transacaoVO, usuarioVO, false);
                    if (!transacaoVO.isPermiteRetentativa()) {
                        return "Não foi necessário fazer a retentativa da cobrança. A transação foi sincronizada, verifique a situação atual dela novamente.";
                    }
                }
            }

            if (!UteisValidacao.emptyList(listaParcelas)) {
                List<MovParcelaVO> parcelasDaTransacaoSeraoCobradasHoje = movParcelaDAO.consultarParcelasEmAbertoParaPagamento(
                        convenioCobrancaVO, Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS, listaParcelas);
                if (!UteisValidacao.emptyList(parcelasDaTransacaoSeraoCobradasHoje)) {
                    StringBuilder parcelasString = new StringBuilder();
                    parcelasString.append("(");
                    for (MovParcelaVO movParcelaVO : parcelasDaTransacaoSeraoCobradasHoje) {
                        parcelasString.append(movParcelaVO.getCodigo() + " , ");
                    }
                    String textoParc = Uteis.removerUltimosCaracteres(parcelasString.toString(), 3);
                    textoParc += ")";
                    if (parcelasDaTransacaoSeraoCobradasHoje.size() == 1) {
                        throw new Exception("A parcela " + textoParc + " já será cobrada automaticamente hoje. Não é possível retentar a transação a fim de evitar duplicidade de pagamento.");
                    } else {
                        throw new Exception("As parcelas " + textoParc + " já serão cobradas automaticamente hoje. Não é possível retentar a transação a fim de evitar duplicidade de pagamento.");
                    }
                }
            }

            pagamentoService = new PagamentoService(getCon(), convenioCobrancaVO);
            Set<Integer> transacaoCriadas = pagamentoService.processarCobrancaNaoPresencial(listaParcelas, usuarioVO, msgErro, true, origemCobrancaEnum, false, ipCliente);
            if (transacaoCriadas.isEmpty()) {
                if (UteisValidacao.emptyList(msgErro)) {
                    throw new Exception("Não foi possível reenviar a transação.");
                } else {
                    throw new Exception("Não foi possível reenviar a transação: " + msgErro.get(0));
                }
            } else {
                return "Transação reenviada, código da transação gerada: " + transacaoCriadas.iterator().next() + ".";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            movParcelaDAO = null;
            transacaoMovParcelaDAO = null;
            pagamentoService = null;
        }
    }

    public JSONObject realizaCobrancaVerificarCartao(ClienteVO clienteVO, ColaboradorVO colaboradorVO,
                                                     AutorizacaoCobrancaVO autoVO, UsuarioVO usuarioVO, String ipOrigem) {
        ConvenioCobranca convenioCobrancaDAO = null;
        Pessoa pessoaDAO = null;
        Empresa empresaDAO = null;
        ConfiguracaoSistema configDAO = null;
        TransacaoVO transacaoVO = null;
        try {
            JSONObject retorno = new JSONObject();
            retorno.put("erro", false);
            retorno.put("message", "");
            retorno.put("modal", "");
            retorno.put("transacao", 0);
            retorno.put("autorizacaoCobrancaCliente", autoVO != null ? autoVO.getCodigo() : 0);
            convenioCobrancaDAO = new ConvenioCobranca(this.con);
            pessoaDAO = new Pessoa(this.con);
            empresaDAO = new Empresa(this.con);
            configDAO = new ConfiguracaoSistema(this.con);

            if (configDAO.isAtivarVerificarCartao()
                    && autoVO != null
                    && autoVO.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

                Integer codEmpresa = 0;
                if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    codEmpresa = empresaDAO.obterEmpresaClientePessoa(clienteVO.getCodigo(), null);
                } else if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                    codEmpresa = empresaDAO.obterEmpresaColaborador(colaboradorVO.getCodigo());
                }

                ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.obterConvenioPadraoVerificacaoCartao(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (convenioCobrancaVO == null ||
                        convenioCobrancaVO.getSituacao().equals(SituacaoConvenioCobranca.INATIVO)) {
                    if (!UteisValidacao.emptyNumber(autoVO.getConvenio().getCodigo()) && autoVO.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.NENHUM)) {
                        convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(autoVO.getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    } else {
                        convenioCobrancaVO = autoVO.getConvenio();
                    }
                }

                if (convenioCobrancaVO != null &&
                        (!convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                                !convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) &&
                                !convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) &&
                        convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {

                    Integer codPessoa = 0;
                    if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        codPessoa = pessoaDAO.obterPessoaCliente(clienteVO.getCodigo());
                    } else if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                        codPessoa = pessoaDAO.obterPessoaColaborador(colaboradorVO.getCodigo());
                    }

                    String numeroCartao = autoVO.getNumeroCartao();
                    if (numeroCartao.contains("***")) {
                        numeroCartao = autoVO.getNazgDTO().getCard();
                    }

                    if (UteisValidacao.emptyString(numeroCartao)) {
                        retorno.put("message", "Número do cartão não identificado");
                        return retorno;
                    }
                    retorno.put("modal", "show");
                    transacaoVO = realizaCobrancaVerificarCartao(autoVO.getTokenAragorn(), codPessoa, numeroCartao,
                            autoVO.getNomeTitularCartao(), autoVO.getCpfTitular(), autoVO.getValidadeCartao(), autoVO.getCvv(),
                            autoVO.getOperadoraCartao(), codEmpresa, convenioCobrancaVO.getCodigo(), usuarioVO, ipOrigem, autoVO);
                    retorno.put("transacao", transacaoVO != null ? transacaoVO.getCodigo() : 0);
                }
            }
            return retorno;
        } catch (Exception ex) {
            ex.printStackTrace();
            JSONObject retorno = new JSONObject();
            retorno.put("erro", true);
            retorno.put("message", ex.getMessage());
            retorno.put("modal", "show");
            retorno.put("transacao", transacaoVO != null ? transacaoVO.getCodigo() : 0);
            retorno.put("autorizacaoCobrancaCliente", autoVO != null ? autoVO.getCodigo() : 0);
            return retorno;
        } finally {
            convenioCobrancaDAO = null;
            pessoaDAO = null;
            empresaDAO = null;
            configDAO = null;
        }
    }

    private TransacaoVO realizaCobrancaVerificarCartao(String tokenAragorn, Integer pessoa, String numeroCartao, String nomeTitular, String cpfTitular,
                                                String validade, String cvv, OperadorasExternasAprovaFacilEnum operadoraEnum,
                                                Integer empresa, Integer convenioCobranca, UsuarioVO usuarioVO, String ipOrigem,
                                                AutorizacaoCobrancaVO autoVO) throws Exception {

        ConvenioCobranca convenioCobrancaDAO = null;
        AutorizacaoCobrancaCliente autoClienteDAO = null;
        try {
            autoClienteDAO = new AutorizacaoCobrancaCliente(this.con);
            convenioCobrancaDAO = new ConvenioCobranca(this.con);

            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                    convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {

                CartaoCreditoTO cartaoCreditoTO = new CartaoCreditoTO();
                cartaoCreditoTO.setTokenAragorn(tokenAragorn);
                cartaoCreditoTO.setIpClientePacto(ipOrigem);
                cartaoCreditoTO.setNumero(numeroCartao.replace(" ", ""));
                cartaoCreditoTO.setNomeTitular(nomeTitular);
                cartaoCreditoTO.setCpfCnpjPortador(cpfTitular);
                if (validade.length() == 5) {
                    cartaoCreditoTO.setValidade(validade.replace("/", "/20"));
                }else {
                    cartaoCreditoTO.setValidade(validade);
                }
                cartaoCreditoTO.setCodigoSeguranca(cvv);
                cartaoCreditoTO.setBand(operadoraEnum);
                cartaoCreditoTO.setIdPessoaCartao(pessoa);
                cartaoCreditoTO.setEmpresa(empresa);
                cartaoCreditoTO.setUsuarioResponsavel(usuarioVO);
                cartaoCreditoTO.setParcelas(1);
                cartaoCreditoTO.setTransacaoVerificarCartao(true);
                cartaoCreditoTO.setTransacaoPresencial(true);
                cartaoCreditoTO.setVerificacaoZeroDollar(convenioCobrancaVO.isVerificacaoZeroDollar());
                cartaoCreditoTO.setOrigemCobranca(OrigemCobrancaEnum.VERIFICADOR_CARTAO);
                cartaoCreditoTO.setEditandoAutorizacao(autoVO != null && !autoVO.isNovoObj());
                if (autoVO != null && !autoVO.getAdquirenteMaxiPago().getId().equals(AdquirenteMaxiPagoEnum.NENHUM.getId())){
                    cartaoCreditoTO.setAdquirenteMaxiPago(autoVO.getAdquirenteMaxiPago());
                }

                //Essa opção é para as Adquirente que não tem o checkbox para habilitar o recurso no Cadastro do Convênio de Cobrança
                boolean gerarValorZerado = cartaoCreditoTO.isTransacaoVerificarCartao() &&
                        ((convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                        convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE) ||
                        convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) ||
                        cartaoCreditoTO.isVerificacaoZeroDollar());

                if (gerarValorZerado) {
                    cartaoCreditoTO.setValor(0.0);
                    cartaoCreditoTO.setParcelas(0);
                } else {
                    //gerar valor entre R$1,00 e R$2,50
                    Integer valorCobrar = UteisValidacao.gerarNumeroRandomico(100, 250);
                    String valorString = valorCobrar.toString();
                    cartaoCreditoTO.setValor(new Double(valorString.substring(0, 1) + "." + valorString.substring(1, 3)));
                }

                AprovacaoServiceInterface service = CobrancaOnlineService.getImplementacaoAprovacaoService(convenioCobrancaVO.getTipo().getTipoTransacao(),
                        empresa, convenioCobrancaVO.getCodigo(), convenioCobrancaVO.isPactoPay(), this.con);
                if (service == null) {
                    throw new Exception("Não foi possível obter o aprovação service.");
                } else {
                    TransacaoVO transacaoVO = service.tentarAprovacao(cartaoCreditoTO);
                    if (transacaoVO != null) {
                        if (autoVO != null) {
                            autoVO.setIdPinBank(transacaoVO.getIdCartaoPinBank());
                            if (!UteisValidacao.emptyNumber(autoVO.getCodigo()) && !UteisValidacao.emptyString(autoVO.getIdPinBank())){
                                try {
                                    autoClienteDAO.alterarIdPinBank((AutorizacaoCobrancaClienteVO) autoVO);
                                } catch (Exception ignore) {
                                }
                            }
                        }
                        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                            try {

                                boolean isAdquirenteQuePodeTerTransacaoPendenteAzul = transacaoVO.getTipo().equals(TipoTransacaoEnum.VINDI)
                                        || transacaoVO.getTipo().equals(TipoTransacaoEnum.PAGOLIVRE)
                                        || transacaoVO.getTipo().equals(TipoTransacaoEnum.FACILITEPAY);

                                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                                        (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) && !isAdquirenteQuePodeTerTransacaoPendenteAzul)) {
                                    autoVO.setCartaoVerificado(true);
                                }

                                boolean transacaoVerificaoStoneV5 = transacaoVO.isTransacaoVerificarCartao() && transacaoVO.getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5);
                                if (!transacaoVerificaoStoneV5) {
                                    service.cancelarTransacao(transacaoVO, false);
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        } else {
                            if (UteisValidacao.emptyString(transacaoVO.getCodigoRetornoGestaoTransacaoMotivo().trim())) {
                                throw new Exception("Não foi possível verificar o cartão.");
                            } else {
                                throw new Exception(transacaoVO.getCodigoRetornoGestaoTransacaoMotivo().replace("Verifique o retorno da adquirente.", ""));
                            }
                        }
                    } else {
                        throw new Exception("Não foi possível verificar o cartão.");
                    }
                    return transacaoVO;
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            autoClienteDAO = null;
        }
    }

    public List<TransacaoVO> consultarTransacaoVerificacaoParaCancelar(TipoTransacaoEnum tipo, Integer convenioCobranca, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("t.* \n");
        sql.append("FROM transacao t \n");
        sql.append("WHERE t.transacaoVerificarCartao = true \n");
        sql.append("AND t.situacao in (").append(SituacaoTransacaoEnum.APROVADA.getId()).append(",").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sql.append("AND t.tipo = ").append(tipo.getId()).append(" \n");
        sql.append("AND t.valor > 0.0 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND t.empresa = ").append(empresa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("AND t.convenioCobranca = ").append(convenioCobranca).append(" \n");
        }
        sql.append(" ORDER BY t.codigo \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            return montarDadosConsulta(ps.executeQuery(), con);
        }
    }

    public void preencherProximaTentativaVindi() {
        try {
            Uteis.logarDebug("preencherProximaTentativaVindi | Início...");
            List<TransacaoVO> listaTransacao = consultarPorTipoESituacao(TipoTransacaoEnum.VINDI, SituacaoTransacaoEnum.APROVADA, null, 0, 0, false, false, null);

            Uteis.logarDebug("preencherProximaTentativaVindi | Total " + listaTransacao.size());
            int i = 0;
            for (TransacaoVO obj : listaTransacao) {
                try {
                    Uteis.logarDebug("preencherProximaTentativaVindi | " + ++i + "/" + listaTransacao.size());
                    String dataProximaString = obj.dataProximaTentativa();
                    if (!UteisValidacao.emptyString(dataProximaString)) {
                        Date dataProxima = Calendario.getDate("dd/MM/yyyy", dataProximaString);
                        if (dataProxima != null) {
                            obj.setProximaTentativa(Calendario.getDataComHoraZerada(dataProxima));
                            alterarProximaTentativa(obj);
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logarDebug("preencherProximaTentativaVindi | Erro transacao " + obj.getCodigo() + " | " + ex.getMessage());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logarDebug("preencherProximaTentativaVindi | Fim...");
        }
    }

    public void alterarProximaTentativa(final TransacaoVO obj) throws Exception {
        String sql = "UPDATE transacao set proximaTentativa = ? WHERE codigo = ? ";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getProximaTentativa()));
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterarProximaTentativaNull(final TransacaoVO obj) throws Exception {
        String sql = "UPDATE transacao set proximaTentativa = NULL WHERE codigo = ? ";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterarCodigoExterno(final TransacaoVO obj) throws Exception {
        String sql = "UPDATE transacao set codigoexterno = ? WHERE codigo = ? ";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getCodigoExterno());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public void alterarOutrasInformacoes(final TransacaoVO obj) throws Exception {
        String sql = "UPDATE transacao set outrasinformacoes = ? WHERE codigo = ? ";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getOutrasInformacoes());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public List<TransacaoVO> obterListaTransacoesEstorno(List<MovParcelaVO> listParc) throws Exception {
        ConfiguracaoSistema configuracaoSistemaDAO = null;
        Empresa empresaDAO = null;
        try {
            configuracaoSistemaDAO = new ConfiguracaoSistema(this.con);
            empresaDAO = new Empresa(this.con);

            Set<Integer> codigoTransacoes = new HashSet<>();
            List<TransacaoVO> listaTransacoes = new ArrayList<>();
            for (MovParcelaVO parcela : listParc) {
                List<TransacaoVO> transacoesParcela = consultarPorParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
                for (TransacaoVO transacaoVOParcela : transacoesParcela) {
                    // #1 Esta funçao serve para verificar se ja possui alguma transação na lista com o mesmo codigo.
                    if (!UteisValidacao.emptyNumber(transacaoVOParcela.getCodigo()) &&
                            !codigoTransacoes.contains(transacaoVOParcela.getCodigo())) {
                        codigoTransacoes.add(transacaoVOParcela.getCodigo());
                        listaTransacoes.add(transacaoVOParcela);
                    }
                }
            }

            if (!UteisValidacao.emptyList(listaTransacoes)) {
                ConfiguracaoSistemaVO config = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                EmpresaVO emp = null;
                if (!listParc.isEmpty()) {
                    emp = empresaDAO.obterEmpresaDeUmaListaParcelas(listParc);
                }
                String url = ConfiguracaoSistemaVO.obterURLRecorrencia(emp, config);

                for (TransacaoVO transacaoVO : listaTransacoes) {
                    transacaoVO.setUrlTransiente(url);
                    transacaoVO.setListaParcelas(obterParcelasDaTransacao(transacaoVO));
                }
            }
            return listaTransacoes;
        } finally {
            configuracaoSistemaDAO = null;
            empresaDAO = null;
        }
    }

    public Integer consultarQtdParcelasTransacao(Integer transacao) throws Exception {
        if (UteisValidacao.emptyNumber(transacao)) {
            return 0;
        }
        try (PreparedStatement ps = con.prepareStatement("select count(movparcela) as qtd from transacaomovparcela where transacao = " + transacao)) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("qtd");
                } else {
                    return 0;
                }
            }
        }
    }

    public void gerarLogEstornarTransacoes(boolean estornarTransacoes, Integer pessoa,
                                           UsuarioVO usuarioVO, String origem) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeEntidade("ESTORNO-ESTORNAR-TRANSACOES");
            log.setNomeEntidadeDescricao("ESTORNO-ESTORNAR-TRANSACOES");
            log.setDescricao("ESTORNO-ESTORNAR-TRANSACOES");
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());

            try {
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("");
            }
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("ESTORNO-ESTORNAR-TRANSACOES-" + origem.toUpperCase());
            log.setPessoa(pessoa);
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(estornarTransacoes ? "SIM" : "NÃO");
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, "Erro ao gerarLogEstornarTransacoes " + e.getMessage());
        } finally {
            logDAO = null;
        }
    }

    private MovPagamentoVO montarMovPagamentoTransacao(TransacaoVO transacaoVO, UsuarioVO usuarioVO,
                                                      boolean utilizarDataProcessamento) throws Exception {
        FormaPagamento formaPagamentoDAO;
        Adquirente adquirenteDAO;
        try {
            formaPagamentoDAO = new FormaPagamento(this.con);
            adquirenteDAO = new Adquirente(this.con);

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();

            if (utilizarDataProcessamento) {
                movPagamentoVO.setDataLancamento(transacaoVO.getDataProcessamento());
                movPagamentoVO.setDataPagamento(transacaoVO.getDataProcessamento());
                movPagamentoVO.setDataQuitacao(transacaoVO.getDataProcessamento());
                movPagamentoVO.setDataPagamentoOriginal(transacaoVO.getDataProcessamento());
                movPagamentoVO.setDataCobrancaTransacao(transacaoVO.getDataProcessamento());
            }

            boolean temFormaPagamento = false;
            List<FormaPagamentoVO> formasPgtConvenio = formaPagamentoDAO.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (FormaPagamentoVO form : formasPgtConvenio) {
                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                    movPagamentoVO.setFormaPagamento(form);
                    temFormaPagamento = true;
                    break;
                }
            }
            if (!temFormaPagamento) {
                movPagamentoVO.setFormaPagamento(formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente());
            }

            try {
                movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacaoVO));
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(transacaoVO.getValor());
            movPagamentoVO.setValorTotal(transacaoVO.getValor());
            movPagamentoVO.setPessoa(transacaoVO.getPessoaPagador());
            movPagamentoVO.setNomePagador(transacaoVO.getPessoaPagador().getNome());
            movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
            if (!UteisValidacao.emptyNumber(transacaoVO.getNrVezesCobranca())) {
                movPagamentoVO.setNrParcelaCartaoCredito(transacaoVO.getNrVezesCobranca());
            } else {
                movPagamentoVO.setNrParcelaCartaoCredito(1);
            }
            movPagamentoVO.setOperadoraCartaoVO(obterOperadoraCartao(transacaoVO));
            if (usuarioVO != null) {
                movPagamentoVO.setResponsavelPagamento(usuarioVO);
            } else {
                movPagamentoVO.setResponsavelPagamento(transacaoVO.getUsuarioResponsavel());
            }
            movPagamentoVO.setNsu(transacaoVO.getNSU());
            movPagamentoVO.setEmpresa(transacaoVO.getEmpresaVO());
            movPagamentoVO.setConvenio(transacaoVO.getConvenioCobrancaVO());
            prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, transacaoVO.getListaParcelas());
            return movPagamentoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            formaPagamentoDAO = null;
            adquirenteDAO = null;
        }
    }

    public ReciboPagamentoVO gerarReciboTransacao(TransacaoVO transacaoVO, UsuarioVO usuarioVO,
                                                  boolean utilizarDataProcessamento) throws Exception {

        //para gerar o recibo:
        //os objetos (EmpresaVO, ConvenioCobrancaVO) da transação devem estar preenchidos com os dados básicos
        //caso não seja informado usuário será utilizado o usuarioresponsavel da transacaoVO

        ZillyonWebFacade zwFacade;
        Cliente clienteDAO;
        NotaFiscal notaFiscalDAO;
        MovPagamento movPagamentoDAO;
        try {
            zwFacade = new ZillyonWebFacade(this.con);
            clienteDAO = new Cliente(this.con);
            notaFiscalDAO = new NotaFiscal(this.con);
            movPagamentoDAO = new MovPagamento(this.con);

            List<MovParcelaVO> listaParcelas = obterParcelasDaTransacaoGerarRecibo(transacaoVO);
            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Nenhuma parcela da transação encontrada");
            }

            List<MovPagamentoVO> listaPagamento = new ArrayList<>();
            MovPagamentoVO movPagamentoVO = montarMovPagamentoTransacao(transacaoVO, usuarioVO, utilizarDataProcessamento);
            listaPagamento.add(movPagamentoVO);
            ReciboPagamentoVO reciboObj = movPagamentoDAO.incluirListaPagamento(
                    listaPagamento,
                    listaParcelas,
                    null,
                    obterContratoParcelas(listaParcelas),
                    false, 0.0);

            try {
                //o próprio método de enviar o nfse já valida se a empresa usa ou não o recurso
                reciboObj.getPagamentosDesteRecibo().get(0).setProdutosPagos(movPagamentoDAO.obterProdutosPagosMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo()));
                if (transacaoVO.getEmpresaVO().isEnviarNFSeAutomatico() &&
                        transacaoVO.getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()) {
                    notaFiscalDAO.gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboObj, usuarioVO != null ? usuarioVO : transacaoVO.getUsuarioResponsavel(), DAO.resolveKeyFromConnection(this.getCon()));
                }
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logarDebug("Nota Fiscal Eletronica ignorada para o Recibo: " + reciboObj.getCodigo() + ", devido ao erro: " + e.getMessage());
            }

            transacaoVO.setReciboPagamento(reciboObj.getCodigo());
            transacaoVO.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());

            if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
            }

            alterar(transacaoVO);

            try {
                if (transacaoVO.getEmpresaVO().isNotificarWebhook()) {
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboObj.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    zwFacade.notificarPagamento(clienteVO, reciboObj);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return reciboObj;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            zwFacade = null;
            clienteDAO = null;
            notaFiscalDAO = null;
            movPagamentoDAO = null;
        }
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> listaMovParcelas) {
        ParceiroFidelidadeZW parceiroFidelidadeZW = null;
        try {
            parceiroFidelidadeZW = new ParceiroFidelidadeZW(getCon());
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        } finally {
            parceiroFidelidadeZW = null;
        }
    }

    private ContratoVO obterContratoParcelas(List<MovParcelaVO> listaParcelas) {
        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = listaParcelas.get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : listaParcelas) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                return null;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        return contratoVO;
    }

    private OperadoraCartaoVO obterOperadoraCartao(TransacaoVO transacaoVO) {
        OperadoraCartao operadoraDAO;
        try {
            operadoraDAO = new OperadoraCartao(getCon());

            OperadorasExternasAprovaFacilEnum operadoraEnum = transacaoVO.getOperadoraEnum();
            if (operadoraEnum == null) {
                throw new Exception("Bandeira não identificada.");
            }
            return operadoraDAO.consultarOuCriaPorCodigoIntegracao(transacaoVO.getConvenioCobrancaVO().getTipo(), transacaoVO.getNrVezesCobranca(), operadoraEnum);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            operadoraDAO = null;
        }
        return new OperadoraCartaoVO();
    }

    public List<TransacaoVO> consultarPactoPay(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTransacaoPactoPay(filtroDTO, TipoConsultaPactoPayEnum.LISTA);
        processarPaginador(sql, "t.dataprocessamento desc", paginadorDTO);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return montarDadosConsulta(rs, con, Uteis.NIVELMONTARDADOS_CONSULTA_PACTO_PAY);
            }
        }
    }

    public void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws Exception {
        if (paginadorDTO != null) {

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            paginadorDTO.setQuantidadeTotalElementos(SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as a", this.con).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            //adicionar ordenação
            if (!UteisValidacao.emptyString(orderByDefault)
                    && paginadorDTO.getSQLOrderByUse() != null
                    && UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse().trim())) {
                sql.append(" ORDER BY ").append(orderByDefault).append(" \n");
            } else {
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            //adicionar limit
            sql.append(paginadorDTO.getSQLLimitByUse());

            //adicionar offset
            sql.append(paginadorDTO.getSQLOffsetByUse());
        }
    }

    private StringBuilder obterSQLTransacaoPactoPay(FiltroPactoPayDTO filtroDTO, TipoConsultaPactoPayEnum tipoConsultaPactoPayEnum) throws Exception {
        return obterSQLTransacaoPactoPay(filtroDTO, tipoConsultaPactoPayEnum, null);
    }

    private StringBuilder obterSQLTransacaoPactoPay(FiltroPactoPayDTO filtroDTO,
                                                    TipoConsultaPactoPayEnum tipoConsultaPactoPayEnum,
                                                    String situacaoParcela) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");

        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.LISTA)) {
            sql.append("t.*, \n");
            sql.append("cl.codigo as cliente_codigo, \n");
            sql.append("cl.matricula as cliente_matricula, \n");
            sql.append("(select count(*) from transacaomovparcela where transacao = t.codigo) as qtd_parcelas_transacao \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR)) {
            sql.append("t.situacao, \n");
            sql.append("count(t.codigo) as qtd, \n");
            sql.append("sum(t.valor) as total \n");
        } if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_TIPO)) {
            sql.append("t.tipo, \n");
            sql.append("t.situacao, \n");
            sql.append("count(t.codigo) as qtd, \n");
            sql.append("sum(t.valor) as total \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_BANDEIRA)) {
            sql.append("upper(split_part(split_part(t.outrasinformacoes, 'cartaoBandeira\":\"',2), '\"', 1)) as bandeira, \n");
            sql.append("count(t.codigo) as qtd, \n");
            sql.append("sum(t.valor) as total \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA)) {
            sql.append("mp.situacao, \n");
            sql.append("count(distinct(mp.codigo)) as qtd, \n");
            sql.append("sum(tm.valormovparcela) as total \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("distinct \n");
            sql.append("clim.matricula, \n");
            sql.append("pesm.nome, \n");
            sql.append("mp.pessoa, \n");
            sql.append("mp.codigo as parcela, \n");
            sql.append("mp.situacao, \n");
            sql.append("mp.descricao, \n");
            sql.append("mp.valorparcela, \n");
            sql.append("mp.nrtentativas, \n");
            sql.append("mp.datavencimento \n");
        }

        sql.append("from transacao t \n");

        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA) ||
                tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
            sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");

            if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
                sql.append("inner join pessoa pesm on pesm.codigo = mp.pessoa \n");
                sql.append("left join cliente clim on clim.pessoa = mp.pessoa \n");
            }
        }

        sql.append("left join pessoa pes on pes.codigo = t.pessoapagador\n");
        sql.append("left join cliente cl on cl.pessoa = t.pessoapagador\n");

        if (filtroDTO.getInicioDate() == null) {
            throw new Exception("Data início pesquisa não informado");
        }
        if (filtroDTO.getFimDate() == null) {
            throw new Exception("Data fim pesquisa não informado");
        }

        sql.append("where t.dataprocessamento >= '").append(Uteis.getDataFormatoBD(filtroDTO.getInicioDate())).append(" 00:00:00.000' and t.dataprocessamento <= '")
                .append(Uteis.getDataFormatoBD(filtroDTO.getFimDate())).append(" 23:59:59.999' \n");

        if (!filtroDTO.isApresentarVerificacao()) {
            sql.append("and t.transacaoVerificarCartao = false \n");
        }

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("and mp.situacao = '").append(situacaoParcela).append("' \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getSituacoes())) {
            sql.append("and t.situacao in (").append(filtroDTO.getSituacoesString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getEmpresas())) {
            sql.append("and t.empresa in (").append(filtroDTO.getEmpresasString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getUsuarios()) && filtroDTO.getUsuarios().size() < 2) {
            for (Integer usuario : filtroDTO.getUsuarios()) {
                if (usuario == 3) { // 3 = id usuario Recorrência, entende que tudo feito por ele é cobranças Automáticas
                    sql.append("and t.usuarioresponsavel = ").append(usuario).append(" \n");
                } else if (usuario == 0) { // 0 = entende tudo que não foi feito por ele usuário Recorrência é cobranças Manuais
                    sql.append("and t.usuarioresponsavel != ").append(3).append(" \n");
                }
            }
        }

        if (!UteisValidacao.emptyList(filtroDTO.getOrigens())) {
            sql.append("and t.origem in (").append(filtroDTO.getOrigensString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getConvenios())) {
            sql.append("and t.conveniocobranca in (").append(filtroDTO.getConveniosString()).append(") \n");
        }

        if (!UteisValidacao.emptyString(filtroDTO.getParametro())) {
            sql.append("and (UPPER(t::text) like '%").append(filtroDTO.getParametro().toUpperCase()).append("%' ");
            sql.append(" OR UPPER(pes::text) like '%").append(filtroDTO.getParametro().toUpperCase()).append("%') ");
        }
        return sql;
    }

    public List<TotalizadorDTO> consultarPactoPayTotalizador(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTransacaoPactoPay(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR);
        sql.append("group by 1 \n");
        processarPaginador(sql, "1", paginadorDTO);
        List<TotalizadorDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));
                    SituacaoTransacaoEnum situacaoTransacaoEnum = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao"));
                    dto.setDescricao(situacaoTransacaoEnum.getStatusPactoPayEnum().getDescricao());
                    dto.setCodigo(String.valueOf(situacaoTransacaoEnum.getStatusPactoPayEnum().getCodigo()));
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public List<TotalizadorTipoDTO> consultarPactoPayTotalizadorPorTipo(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTransacaoPactoPay(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_TIPO);
        sql.append("group by 1,2 \n");
        processarPaginador(sql, "1,2", paginadorDTO);
        Map<TipoTransacaoEnum, List<TotalizadorDTO>> mapa = new HashMap<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(rs.getInt("tipo"));
                    List<TotalizadorDTO> lista = mapa.get(tipoTransacaoEnum);
                    if (lista == null) {
                        lista = new ArrayList<>();
                    }
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));
                    SituacaoTransacaoEnum situacaoTransacaoEnum = SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao"));
                    dto.setDescricao(situacaoTransacaoEnum.getStatusPactoPayEnum().getDescricao());
                    dto.setCodigo(String.valueOf(situacaoTransacaoEnum.getStatusPactoPayEnum().getCodigo()));
                    lista.add(dto);

                    mapa.put(tipoTransacaoEnum, lista);
                }
            }
        }

        List<TotalizadorTipoDTO> lista = new ArrayList<>();
        for (TipoTransacaoEnum tipoTransacaoEnum : mapa.keySet()) {
            TotalizadorTipoDTO dto = new TotalizadorTipoDTO();
            dto.setTipo_codigo(tipoTransacaoEnum.getId());
            dto.setTipo_descricao(tipoTransacaoEnum.getDescricao().toUpperCase());
            dto.setTotalizadores(mapa.get(tipoTransacaoEnum));
            lista.add(dto);
        }
        return lista;
    }

    public List<TotalizadorDTO> consultarPactoPayTotalizadorPorParcela(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTotalizadorParcela(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA, null);
        sql.append("group by situacao \n");
        processarPaginador(sql, "3 desc", paginadorDTO);
        List<TotalizadorDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));

                    String situacao = rs.getString("situacao");
                    String descricao = "";
                    if (situacao.equals("EA")) {
                        descricao = "Em aberto";
                    }
                    if (situacao.equals("PG")) {
                        descricao = "Pagas";
                    }
                    if (situacao.equals("CA")) {
                        descricao = "Canceladas";
                    }
                    if (situacao.equals("RG")) {
                        descricao = "Renegociadas";
                    }

                    dto.setDescricao(descricao.toUpperCase());
                    dto.setCodigo(situacao);
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    private StringBuilder obterSQLTotalizadorParcela(FiltroPactoPayDTO filtroDTO, TipoConsultaPactoPayEnum tipoConsultaPactoPayEnum, String situacaoParcela) {
        StringBuilder sql = new StringBuilder();
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA)) {
            sql.append("select \n");
            sql.append("situacao, \n");
            sql.append("count(codigo) as qtd, \n");
            sql.append("sum(valorparcela) as total \n");
            sql.append("from ( \n");
            sql.append("select distinct \n");
            sql.append("mp.* \n");
        } else if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("select \n");
            sql.append("distinct \n");
            sql.append("clim.matricula, \n");
            sql.append("pesm.nome, \n");
            sql.append("mp.pessoa, \n");
            sql.append("mp.codigo as parcela, \n");
            sql.append("mp.situacao, \n");
            sql.append("mp.descricao, \n");
            sql.append("mp.valorparcela, \n");
            sql.append("mp.nrtentativas, \n");
            sql.append("mp.datavencimento \n");
        }
        sql.append("from movparcela mp \n");
        sql.append("inner join transacaomovparcela tmp on tmp.movparcela = mp.codigo \n");
        sql.append("inner join transacao t on t.codigo = tmp.transacao \n");
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA)) {
            sql.append("inner join pessoa pesm on pesm.codigo = mp.pessoa \n");
            sql.append("left join cliente clim on clim.pessoa = mp.pessoa \n");
            sql.append("left join pessoa pes on pes.codigo = t.pessoapagador\n");
            sql.append("left join cliente cl on cl.pessoa = t.pessoapagador\n");
        }
        sql.append("where t.transacaoVerificarCartao = false \n");
        sql.append("and t.dataprocessamento::date between '").append(Uteis.getDataFormatoBD(filtroDTO.getInicioDate())).append("' and '").append(Uteis.getDataFormatoBD(filtroDTO.getFimDate())).append("' \n");

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("and mp.situacao = '").append(situacaoParcela).append("' \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getSituacoes())) {
            sql.append("and t.situacao in (").append(filtroDTO.getSituacoesString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getEmpresas())) {
            sql.append("and t.empresa in (").append(filtroDTO.getEmpresasString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getConvenios())) {
            sql.append("and t.conveniocobranca in (").append(filtroDTO.getConveniosString()).append(") \n");
        }

        if (!UteisValidacao.emptyList(filtroDTO.getUsuarios()) && filtroDTO.getUsuarios().size() < 2) {
            for (Integer usuario : filtroDTO.getUsuarios()) {
                if (usuario == 3) { // 3 = id usuario Recorrência, entende que tudo feito por ele é cobranças Automáticas
                    sql.append("and t.usuarioresponsavel = ").append(usuario).append(" \n");
                } else if (usuario == 0) { // 0 = entende tudo que não foi feito por ele usuário Recorrência é cobranças Manuais
                    sql.append("and t.usuarioresponsavel != ").append(3).append(" \n");
                }
            }
        }

        if (!UteisValidacao.emptyString(filtroDTO.getParametro())) {
            sql.append("and (UPPER(t::text) like '%").append(filtroDTO.getParametro().toUpperCase()).append("%') \n");
        }
        if (tipoConsultaPactoPayEnum.equals(TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA)) {
            sql.append(") sql \n");
        }
        return sql;
    }

    public List<ParcelaDTO> consultarPactoPayTotalizadorPorParcelaLista(FiltroPactoPayDTO filtroDTO, String situacaoParcela, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTotalizadorParcela(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_PARCELA_LISTA, situacaoParcela);
        processarPaginador(sql, "pesm.nome", paginadorDTO);
        List<ParcelaDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ParcelaDTO dto = new ParcelaDTO();
                    dto.setCodigo(rs.getInt("parcela"));
                    dto.setMatricula(rs.getString("matricula"));
                    dto.setNome(rs.getString("nome"));
                    dto.setPessoa(rs.getInt("pessoa"));
                    dto.setDescricao(rs.getString("descricao"));

                    MovParcelaVO movParcelaVO = new MovParcelaVO();
                    movParcelaVO.setSituacao(rs.getString("situacao"));
                    dto.setSituacao(movParcelaVO.getSituacao());
                    dto.setSituacaoApresentar(movParcelaVO.getSituacao_Apresentar().toUpperCase());

                    dto.setValorParcela(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorparcela")));
                    dto.setNrTentativas(rs.getInt("nrtentativas"));
                    dto.setVencimento(Calendario.getDataAplicandoFormatacao(rs.getDate("datavencimento"), "dd/MM/yyyy"));
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public List<TotalizadorDTO> consultarPactoPayTotalizadorPorBandeira(FiltroPactoPayDTO filtroDTO, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = obterSQLTransacaoPactoPay(filtroDTO, TipoConsultaPactoPayEnum.TOTALIZADOR_BANDEIRA);
        sql.append("group by 1 \n");
        processarPaginador(sql, "3 desc", paginadorDTO);
        List<TotalizadorDTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorDTO dto = new TotalizadorDTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total")));
                    dto.setDescricao(rs.getString("bandeira").toUpperCase());
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public String obterCodigoRetornoDescricao(String codigoRetorno, TipoTransacaoEnum tipoTransacaoEnum) throws SQLException {
        String sql2 = "select codigoretornodescricao from transacao where tipo = " + tipoTransacaoEnum.getId() +
                " and length(coalesce(codigoretornodescricao,'')) > 0 and codigoretorno = '" + codigoRetorno + "' order by codigo desc limit 1";
        try (Statement stm1 = con.createStatement()) {
            try (ResultSet rs1 = stm1.executeQuery(sql2)) {
                if (rs1.next()) {
                    return rs1.getString(1);
                }
            }
        }
        return "";
    }

    public void enviarEmailComprovanteCancelamento(String emailEnviar, TransacaoVO transacaoVO) throws Exception {
        if (UteisValidacao.emptyString(emailEnviar)) {
            throw new Exception("Informe o email.");
        }
        if (!UteisValidacao.validaEmail(emailEnviar)) {
            throw new Exception("O email não é válido.");
        }

        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();

        UteisEmail uteisEmail = new UteisEmail();
        String assuntoEmail = "Comprovante Cancelamento de Transação";
        uteisEmail.novo(assuntoEmail, configuracaoSistemaCRMVO);

        StringBuilder texto = new StringBuilder();
        texto.append("<p><center><b>Comprovante Cancelamento de Transação</b></center></p>");
        texto.append("<br/><b>Comprovante Cancelamento de Transação: </b><br/>");
        texto.append("Comprovante: ").append(transacaoVO.getUrlComprovanteCancelamento());
        texto.append("<br/><br/>Att.<br/><br/>");
        texto.append(transacaoVO.getEmpresaVO().getNome());
        String[] emails = {emailEnviar};
        uteisEmail.enviarEmailN(emails, texto.toString(), assuntoEmail, transacaoVO.getEmpresaVO().getNome());
    }

    public void enviarEmailComprovantePagamento(String emailEnviar, TransacaoVO transacaoVO) throws Exception {
        VendasConfig vendasConfigDAO;
        ConfiguracaoSistemaCRM crmDAO;
        try {
            vendasConfigDAO = new VendasConfig(this.con);
            crmDAO = new ConfiguracaoSistemaCRM(this.con);

            if (UteisValidacao.emptyString(emailEnviar)) {
                throw new Exception("Informe o email.");
            }
            if (!UteisValidacao.validaEmail(emailEnviar)) {
                throw new Exception("O email não é válido.");
            }

            //enviar email
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = crmDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!configuracaoSistemaCRMVO.isConfiguracaoEmailValida()) {
                throw new Exception("ConfiguracaoSistemaCRMVO não é válida");
            }

            EmailPagamentoTO emailPagamentoTO = vendasConfigDAO.obterDados(transacaoVO.getCodigo(), null);
            if (emailPagamentoTO == null) {
                throw new Exception("Não foi possível obter dados para envio do email");
            }

            if (!UteisValidacao.emptyString(emailEnviar)) {
                emailPagamentoTO.setEmail(emailEnviar);
            }

            if (UteisValidacao.emptyString(emailPagamentoTO.getEmail())) {
                throw new Exception("Pessoa sem email para correspondência");
            }

            UteisEmail uteis = new UteisEmail();
            uteis.novo("Pagamento aprovado! \\o/", configuracaoSistemaCRMVO);
            uteis.enviarEmail(emailPagamentoTO.getEmail(), "", vendasConfigDAO.gerarCorpoEmailPagamento(emailPagamentoTO).toString(), "", configuracaoSistemaCRMVO.getIntegracaoPacto(), configuracaoSistemaCRMVO.preparaEnvioSendy());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            vendasConfigDAO = null;
            crmDAO = null;
        }
    }

    public PessoaCPFTO obterDadosPessoaPagador(PessoaVO pessoaVO,
                                               boolean validarNome, boolean validarCPF) throws Exception {
        Cliente clienteDAO;
        Pessoa pessoaDAO;
        try {
            clienteDAO = new Cliente(con);
            pessoaDAO = new Pessoa(con);

            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                return null;
            }

            PessoaVO pessoaVOCompleta = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaCPFTO pessoaCPFTO = new PessoaCPFTO();
            pessoaCPFTO.setCodigo(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setPessoa(pessoaVOCompleta.getCodigo());
            pessoaCPFTO.setNome(pessoaVOCompleta.getNome());

            String nomeResponsavel = "";
            String cpfResponsavel = "";
            Integer codPessoaResponsavel = 0;

            PessoaVO pessoaResponsavel = clienteDAO.obterPessoaResponsavelCliente(null, pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (pessoaResponsavel != null && !UteisValidacao.emptyString(pessoaResponsavel.getCfp().trim())) {
                nomeResponsavel = pessoaResponsavel.getNome();
                cpfResponsavel = pessoaResponsavel.getCfp();
                codPessoaResponsavel = pessoaResponsavel.getCodigo();
            } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomeMae().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfMae().trim())) {
                nomeResponsavel = pessoaVOCompleta.getNomeMae();
                cpfResponsavel = pessoaVOCompleta.getCpfMae();
            } else if (!UteisValidacao.emptyString(pessoaVOCompleta.getNomePai().trim()) && !UteisValidacao.emptyString(pessoaVOCompleta.getCpfPai().trim())) {
                nomeResponsavel = pessoaVOCompleta.getNomePai();
                cpfResponsavel = pessoaVOCompleta.getCpfPai();
            } else {
                throw new ConsistirException(String.format("Cliente %s não possui responsável cadastrado.", pessoaVOCompleta.getNome()));
            }

            pessoaCPFTO.setPessoaResponsavel(codPessoaResponsavel);
            pessoaCPFTO.setNomeResponsavel(nomeResponsavel);
            pessoaCPFTO.setCpfResponsavel(Formatador.removerMascara(cpfResponsavel));

            if (validarNome && UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel())) {
                throw new Exception("Nome do responsável não informado");
            }

            if (validarCPF && UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", ""))) {
                throw new Exception("CPF do responsável não informado");
            }
            return pessoaCPFTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            pessoaDAO = null;
        }
    }

    private void processarResultadoCobrancaReguaCobranca(TransacaoVO transacaoVO) {
        PactoPayConfig pactoPayConfigDAO;
        try {
            pactoPayConfigDAO = new PactoPayConfig(this.getCon());
            pactoPayConfigDAO.processarResultadoCobranca(transacaoVO, false, null, false);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    public void preencherNSUeAutorizacao(Integer convenioCobranca, Date dataInicio) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.*, \n");
            sql.append("t.nsu as nsu_transacao, \n");
            sql.append("t.codigoautorizacao as autorizacao_transacao \n");
            sql.append("from transacao t \n");
            sql.append("where t.situacao in (").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(",").append(SituacaoTransacaoEnum.CANCELADA.getId()).append(") \n");
            sql.append("and (coalesce(t.nsu,'') = '' or coalesce(t.codigoautorizacao,'') = '') \n");
            if (dataInicio != null) {
                sql.append("and t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            }
            if (!UteisValidacao.emptyNumber(convenioCobranca)) {
                sql.append("and t.convenioCobranca = ").append(convenioCobranca).append(" \n");
            }
            try (ResultSet rs = criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    try {
                        TransacaoVO obj = montarDados(rs, con, Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
                        String nsu_transacao = rs.getString("nsu_transacao");
                        String autorizacao_transacao = rs.getString("autorizacao_transacao");

                        if (UteisValidacao.emptyString(nsu_transacao) && !UteisValidacao.emptyString(obj.getNSU())) {
                            executarConsulta("update transacao set nsu = '" + obj.getNSU() + "' where codigo = " + obj.getCodigo(), con);
                        }
                        if (UteisValidacao.emptyString(autorizacao_transacao) && !UteisValidacao.emptyString(obj.getAutorizacao())) {
                            executarConsulta("update transacao set codigoautorizacao = '" + obj.getAutorizacao() + "' where codigo = " + obj.getCodigo(), con);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public TransacaoVO consultarPorGatewayId(final Long gateway_id) throws Exception {
        String sql = "SELECT codigo, codigonsu, codigoautorizacao, gateway_id, outrasinformacoes FROM transacao WHERE gateway_id = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setLong(1, gateway_id);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                while (rs.next()) {
                    TransacaoVO transacaoVO = new TransacaoVO();
                    transacaoVO.setCodigo(rs.getInt("codigo"));
                    transacaoVO.setCodigoNSU(rs.getString("codigonsu"));
                    transacaoVO.setCodigoAutorizacao(rs.getString("codigoAutorizacao"));
                    transacaoVO.setGateway_id(rs.getLong("gateway_id"));
                    transacaoVO.setOutrasInformacoes(rs.getString("outrasinformacoes"));
                    return transacaoVO;
                }
            }
        }
        return null;
    }

    public boolean existeTransacaoVerificacaoRecenteParaCartao(String cartaoMascarado) throws SQLException {
        try {
            String sql =
                    "SELECT EXISTS ( \n" +
                            "  SELECT 1 \n" +
                            "    FROM transacao \n" +
                            "   WHERE outrasinformacoes ILIKE '%" + cartaoMascarado + "%' \n" +
                            "     AND dataprocessamento >= now() - INTERVAL '10 second' \n" +
                            "     AND origem = 8 \n" +
                            ");";

            try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    if (tabelaResultado.next()) {
                        return tabelaResultado.getBoolean("exists");
                    }
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
