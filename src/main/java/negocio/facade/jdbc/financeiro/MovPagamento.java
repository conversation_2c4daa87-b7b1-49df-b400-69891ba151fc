package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.StatusMovimentacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoClienteRestricaoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.integracao.conciliadora.StatusPagamentoConciliadora;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import controle.arquitetura.exceptions.ServiceException;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.ConfiguracaoEmpresaTotemVO;
import negocio.comuns.TotemTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.GerarCreditosPersonalServico;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;
import negocio.oamd.RedeEmpresaVO;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import relatorio.negocio.jdbc.financeiro.ParcelaSPCTO;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.admCoreMs.AdmCoreMsService;
import servicos.impl.devolucaocheque.DevolucaoChequeServiceImpl;
import servicos.impl.microsservice.integracoes.AcaoSPCCallable;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
import servicos.integracao.rdstationmarketing.AtualizarCamposEnum;
import servicos.oamd.RedeEmpresaService;
import servicos.pix.PixStatusEnum;
import servlet.caixaemaberto.FormaPagamentoDTO;
import servlet.caixaemaberto.PagamentoDTO;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>MovPagamentoVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>MovPagamentoVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see MovPagamentoVO
 * @see SuperEntidade
 */
public class MovPagamento extends SuperEntidade implements MovPagamentoInterfaceFacade {

    private Hashtable pagamentoMovParcelas;
    private Hashtable pagamentoCheques;
    private MovimentoContaCorrenteClienteVO depositoContaCorrente;
    private MovimentoContaCorrenteClienteVO creditoContaCorrente;
    private MovimentoContaCorrenteClienteVO pagarDebitoCC;
    private List<PagamentoMovParcelaVO> parcelasCredito;
    private List<PagamentoMovParcelaVO> parcelasDebito;
    private Double credito;
    private Double debito;

    public MovPagamento() throws Exception {
        super();
        setPagamentoMovParcelas(new Hashtable());
        setPagamentoCheques(new Hashtable());

    }

    public MovPagamento(Connection conexao) throws Exception {
        super(conexao);
        setPagamentoMovParcelas(new Hashtable());
        setPagamentoCheques(new Hashtable());

    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>MovPagamentoVO</code>.
     */
    @Override
    public MovPagamentoVO novo() throws Exception {
        incluir(getIdEntidade());
        MovPagamentoVO obj = new MovPagamentoVO();
        setDepositoContaCorrente(new MovimentoContaCorrenteClienteVO());
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovPagamentoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovPagamentoVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void incluir(MovPagamentoVO obj) throws Exception {
        incluir(obj, true);
    }
    public void incluir(MovPagamentoVO obj, boolean controlarAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, controlarAcesso);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(MovPagamentoVO obj, boolean controlarAcesso) throws Exception {
        MovPagamentoVO.validarDados(obj);
        if(controlarAcesso){
            incluir(getIdEntidade());
        }
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO MovPagamento( pessoa, dataPagamento, dataLancamento, "
                + "valor, formaPagamento, nomePagador, operadoraCartao, "
                + "nrParcelaCartaoCredito, movPagamentoEscolhida, responsavelPagamento, "
                + "reciboPagamento, dataquitacao, convenioCobranca, dataalteracaomanual, "
                + "empresa, observacao, valortotal, nsu, credito,dataPagamentooriginal, adquirente, "
                + "usarParceiroFidelidade, tabelaParceiroFidelidade, multiplicadorParceiroFidelidade, tipoPontoParceiroFidelidade, "
                + "pontosParceiroFidelidade, cpfParceiroFidelidade, codigoExternoProdutoParceiroFidelidade, senhaParceiroFidelidade, numerounicotransacao, respostaRequisicaoPinpad) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getPessoa().getCodigo() != 0) {
                sqlInserir.setInt(1, obj.getPessoa().getCodigo());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
            sqlInserir.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            sqlInserir.setDouble(4, obj.getValor());
            if (obj.getFormaPagamento().getCodigo() != 0) {
                sqlInserir.setInt(5, obj.getFormaPagamento().getCodigo());
            } else {
                sqlInserir.setNull(5, 0);
            }
            sqlInserir.setString(6, obj.getNomePagador());
            if (obj.getOperadoraCartaoVO().getCodigo() != 0) {
                sqlInserir.setInt(7, obj.getOperadoraCartaoVO().getCodigo());
            } else {
                sqlInserir.setNull(7, 0);
            }
            sqlInserir.setInt(8, obj.getNrParcelaCartaoCredito());
            sqlInserir.setBoolean(9, obj.getMovPagamentoEscolhida());
            if (obj.getResponsavelPagamento().getCodigo() != 0) {
                sqlInserir.setInt(10, obj.getResponsavelPagamento().getCodigo());
            } else {
                sqlInserir.setNull(10, 0);
            }
            if (obj.getReciboPagamento().getCodigo() != 0) {
                sqlInserir.setInt(11, obj.getReciboPagamento().getCodigo());
            } else {
                sqlInserir.setNull(11, 0);
            }
            sqlInserir.setDate(12, Uteis.getDataJDBC(obj.getDataQuitacao()));
            if (obj.getConvenio().getCodigo() != 0) {
                sqlInserir.setInt(13, obj.getConvenio().getCodigo());
            } else {
                sqlInserir.setNull(13, 0);
            }
            if (obj.getDataAlteracaoManual() == null) {
                sqlInserir.setNull(14, 0);
            } else {
                sqlInserir.setDate(14, Uteis.getDataJDBC(obj.getDataAlteracaoManual()));
            }
            if (obj.getEmpresa().getCodigo() != 0) {
                sqlInserir.setInt(15, obj.getEmpresa().getCodigo());
            } else {
                sqlInserir.setNull(15, 0);
            }
            sqlInserir.setString(16, obj.getObservacao());
            sqlInserir.setDouble(17, obj.getValorTotal());
            sqlInserir.setString(18, obj.getNsu().trim());
            sqlInserir.setBoolean(19, obj.getCredito());
            if (obj.getDataPagamentoOriginal() == null) {
                sqlInserir.setTimestamp(20, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
            } else {
                sqlInserir.setTimestamp(20, Uteis.getDataJDBCTimestamp(obj.getDataPagamentoOriginal()));
            }
            if (UteisValidacao.emptyNumber(obj.getAdquirenteVO().getCodigo())) {
                sqlInserir.setNull(21, 0);
            } else {
                sqlInserir.setInt(21, obj.getAdquirenteVO().getCodigo());
            }

            sqlInserir.setBoolean(22, obj.isUsarParceiroFidelidade());
            sqlInserir.setInt(23, obj.getTabelaParceiroFidelidadeVO().getCodigo());
            sqlInserir.setDouble(24, obj.getMultiplicadorParceiroFidelidade());
            sqlInserir.setInt(25, obj.getTipoPontoParceiroFidelidade().getCodigo());
            sqlInserir.setInt(26, obj.getPontosParceiroFidelidade());
            sqlInserir.setString(27, obj.getCpfParceiroFidelidade());
            sqlInserir.setString(28, obj.getCodigoExternoProdutoParceiroFidelidade());
            sqlInserir.setString(29, obj.getSenhaParceiroFidelidade());
            sqlInserir.setString(30, obj.getNumeroUnicoTransacao());
            sqlInserir.setString(31, obj.getRespostaRequisicaoPinpad());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        if (!UteisValidacao.emptyList(obj.getPagamentoMovParcelaVOs())) {
            PagamentoMovParcela pagamentoMovParcela = new PagamentoMovParcela(this.con);
            pagamentoMovParcela.incluirPagamentoMovParcelas(obj.getCodigo(), obj.getPagamentoMovParcelaVOs(), obj.getReciboPagamento().getCodigo());
            pagamentoMovParcela = null;
        }
        if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
            Cheque cheque = new Cheque(this.con);
            cheque.incluirPagamentoCheques(obj.getCodigo(), obj.getChequeVOs());
            cheque = null;
        }
    }

    @Override
    public ReciboPagamentoVO incluirListaPagamento(List listaFormaPagamento, List listaParcela, MovimentoContaCorrenteClienteVO movimentoContaCorrenteCliente, ContratoVO contrato, Boolean receberTroco, Double valorResiduoParaDeposito) throws Exception{
        return incluirListaPagamento(listaFormaPagamento, listaParcela, movimentoContaCorrenteCliente, contrato, receberTroco, valorResiduoParaDeposito, true, null, null);
    }

    @Override
    public ReciboPagamentoVO incluirListaPagamento(List listaFormaPagamento, List listaParcela, MovimentoContaCorrenteClienteVO movimentoContaCorrenteCliente, ContratoVO contrato, Boolean receberTroco, Double valorResiduoParaDeposito, Boolean controlarTransacao, ReciboPagamentoVO recibo) throws Exception {
        return incluirListaPagamento(listaFormaPagamento, listaParcela, movimentoContaCorrenteCliente, contrato, receberTroco, valorResiduoParaDeposito, controlarTransacao, null, null);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovPagamentoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    @SuppressWarnings("unchecked")
    public ReciboPagamentoVO incluirListaPagamento(List lista,
                                                   List listaParcela,
                                                   MovimentoContaCorrenteClienteVO movimentoContaCorrenteCliente,
                                                   ContratoVO contrato,
                                                   Boolean receberTroco,
                                                   Double valorResiduoParaDeposito,
                                                   Boolean controlarTransacao,
                                                   ReciboPagamentoVO recibo,
                                                   TipoOperacaoContaCorrenteEnum tipoOperacaoContaCorrenteEnum) throws Exception {
        ReciboPagamentoVO reciboPagamento = new ReciboPagamentoVO();
        Ordenacao.ordenarLista(lista, "valor");
        try {
            if (controlarTransacao != null && controlarTransacao) {
                con.setAutoCommit(false);
            }
            setCreditoContaCorrente(null);
            setPagarDebitoCC(null);
            excluirPagamentoBoletoEmAberto(listaParcela);
            alterarValorMultaJurosNaoRecebidos(listaParcela);
            if (recibo == null) {
                inicializarDadosReciboPagamento(lista, reciboPagamento, contrato);
                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                reciboPagamentoDAO.incluir(reciboPagamento);

                Iterator e = lista.iterator();
                while (e.hasNext()) {
                    MovPagamentoVO movPag = (MovPagamentoVO) e.next();
                    if (!UteisValidacao.emptyString(movPag.getObservacao()) && movPag.getObservacao().contains("Pagamento Linx Totem - ")) {
                        Uteis.logarDebug("LINX TOTEM - incluirListaPagamento Getou Recibo: " + reciboPagamento.getCodigo());
                    }
                }

                reciboPagamentoDAO = null;
            } else {
                reciboPagamento = recibo;
            }
            if (context() != null) {
                context().getExternalContext().getSessionMap().put("reciboPagamento",
                        reciboPagamento);
            }

            String formasPagamentoEmissaoAutomaticaNfse = (reciboPagamento.getEmpresa().getFormasPagamentoEmissaoNFSe() != null ?
                    reciboPagamento.getEmpresa().getFormasPagamentoEmissaoNFSe() : "");

            Iterator i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("AV")) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, receberTroco);
                    if(formasPagamentoEmissaoAutomaticaNfse.contains("AV")){
                        reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                    }
                }
            }
            i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("BB")) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, receberTroco);
                    if(formasPagamentoEmissaoAutomaticaNfse.contains("BB")){
                        reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                    }
                }
            }

            i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, receberTroco);
                }
            }

            i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla())) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, receberTroco);
                    if(formasPagamentoEmissaoAutomaticaNfse.contains("PX")){
                        reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                    }
                }else if(movPagamento.getMovPagamentoEscolhida()){
                    Pix pixDAO = new Pix(con);
                    try {
                        //tentar cancelar pix ativos por parcelas
                        pixDAO.cancelarAtivosPorParcelasVOs(listaParcela);
                    } catch (Exception ex) {
                        if (ex.getMessage().contains("A cobrança não está ATIVA")) {
                            //Se caiu aqui é porque tentou cancelar uma cobrança que já está cancelada, então atualizar o status do nosso lado também
                            expirarCancelarPix(listaParcela, pixDAO);
                        }
                    } finally {
                        pixDAO = null;
                    }
                }
            }

            i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida() && (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CD"))) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    movPagamento.preencherDataDeAcordoComTipo();
                    if (movPagamento.getFormaPagamento().isCompensacaoDiasUteis()) {
                        movPagamento.setDataPagamento(obterProximoDiaUtil(movPagamento.getDataPagamento(), movPagamento.getEmpresa()));
                    }
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, null);
                    reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                }
                if (!UteisValidacao.emptyString(movPagamento.getObservacao()) && movPagamento.getObservacao().contains("Pagamento Linx Totem - ")) {
                    Uteis.logarDebug("LINX TOTEM - incluirListaPagamento 5 while: ");
                }
            }

            Iterator j = lista.iterator();
            while (j.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) j.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcelaCheque(movPagamento, listaParcela, reciboPagamento);
                    if(formasPagamentoEmissaoAutomaticaNfse.contains("CH")){
                        reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                    }
                }
            }
            Iterator e = lista.iterator();
            while (e.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) e.next();
                if (movPagamento.getMovPagamentoEscolhida() && (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA"))) {
                    try {
                        registrarLogCapptaServidorIncluirListaPagamento(listaParcela, movPagamento);
                        preencherProdutoPagoParcela(listaParcela, movPagamento);
                    } catch (Exception exc) {}

                    movPagamento.setReciboPagamento(reciboPagamento);
                    movPagamento.preencherDataDeAcordoComTipo();
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, null);
                    reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                }
                if (!UteisValidacao.emptyString(movPagamento.getObservacao()) && movPagamento.getObservacao().contains("Pagamento Linx Totem - ")) {
                    Uteis.logarDebug("LINX TOTEM - incluirListaPagamento 7 while: ");
                }
            }

            Iterator k = lista.iterator();
            while (k.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) k.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, null);
                    if (movPagamento.getValor() > 0 && movimentoContaCorrenteCliente != null) {
                        movimentoContaCorrenteCliente.setMovPagamentosVOs(new ArrayList<MovPagamentoVO>());
                        movimentoContaCorrenteCliente.setReciboPagamentoVO(reciboPagamento);
                        movimentoContaCorrenteCliente.getMovPagamentosVOs().add(movPagamento);
                        movimentoContaCorrenteCliente.setValor(movPagamento.getValor());
                        movimentoContaCorrenteCliente.setResponsavelAutorizacao(movPagamento.getResponsavelPagamento());

                        MovimentoContaCorrenteCliente movDAO = new MovimentoContaCorrenteCliente(con);
                        movDAO.adicionarmovimento(movimentoContaCorrenteCliente);
                        movDAO = null;

                    } else { // Isso é necessario para fazer bater o caixa com o gestão de recebéveis
                        movPagamento.setValor(movPagamento.getValorTotal());
                        alterarSemCommit(movPagamento);
                    }
                }
            }

            i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.TRANSFERENCIA_BANCARIA.getSigla())) {
                    movPagamento.setReciboPagamento(reciboPagamento);
                    inicializarMovParcela(lista, movPagamento, listaParcela, reciboPagamento, receberTroco);
                    if(formasPagamentoEmissaoAutomaticaNfse.contains("TB")){
                        reciboPagamento.setEmitirNFSeAutomatico(movPagamento.getEmpresa().isEnviarNFSeAutomatico());
                    }
                }
            }

            if (getCreditoContaCorrente() != null) {
                MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(con);
                movimentoContaCorrenteClienteDAO.adicionarmovimento(getCreditoContaCorrente());
                movimentoContaCorrenteClienteDAO = null;
            }

            if (getPagarDebitoCC() != null) {
                MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(con);
                movimentoContaCorrenteClienteDAO.pagarProdutosDebito(getPagarDebitoCC(), false);
                movimentoContaCorrenteClienteDAO = null;
            }
            setarProdutosPagos(reciboPagamento.getCodigo());

            ReciboClienteConsultor reciboClienteConsultorDAO = new ReciboClienteConsultor(con);
            reciboClienteConsultorDAO.incluirComRecibo(reciboPagamento);
            reciboClienteConsultorDAO = null;

            gerarNFSeEmitidaParaChequesDevolvidos(reciboPagamento, listaParcela, con);

            DevolucaoChequeServiceImpl devolucaoChequeServiceDAO = new DevolucaoChequeServiceImpl(con);
            devolucaoChequeServiceDAO.removerChequeContaDevolucao(new ArrayList<MovParcelaVO>(listaParcela));
            devolucaoChequeServiceDAO = null;

            gerarCreditosPersonal(reciboPagamento.getCodigo());

            if(tipoOperacaoContaCorrenteEnum==null || tipoOperacaoContaCorrenteEnum != TipoOperacaoContaCorrenteEnum.TOCC_Devolver){
                gerarPontosParceiroFidelidade(reciboPagamento.getCodigo());
            }

            if(controlarTransacao != null && controlarTransacao){
                con.commit();
            }
        } catch (Exception e) {
            e.printStackTrace();
            if(controlarTransacao != null && controlarTransacao){
                con.rollback();
                con.setAutoCommit(true);
            }
            Iterator o = lista.iterator();
            while (o.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) o.next();
                if (movPagamento.getMovPagamentoEscolhida()) {
                    movPagamento.setNovoObj(true);
                    movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                }
            }
            Iterator i = listaParcela.iterator();
            while (i.hasNext()) {
                MovParcelaVO movParcela = (MovParcelaVO) i.next();
                movParcela.setSituacao("EA");
            }
            throw e;

        } finally {
            if(controlarTransacao != null && controlarTransacao){
                con.setAutoCommit(true);
            }
        }

        removerNegativacoesSPC(lista, listaParcela);
        retirarClienteRestricoesInadimplenciaRedeEmpresa(reciboPagamento.getPessoaPagador());
        atualizarInformacoesRdStationMarketing(reciboPagamento.getPessoaPagador(), reciboPagamento.getEmpresa().getCodigo());

        return reciboPagamento;
    }

    private void atualizarInformacoesRdStationMarketing(PessoaVO pessoa, Integer codigoEmpresa) {
        Empresa empresaDAO;
        Cliente clienteDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            empresaDAO = new Empresa(con);
            clienteDAO = new Cliente(con);
            zillyonWebFacade = new ZillyonWebFacade(con);
            boolean configsHabilitadas = empresaDAO.configsRdStationMarketingEstaoHabilitadas(codigoEmpresa);
            if(configsHabilitadas) {
                ClienteVO clienteCompleto = clienteDAO.consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                zillyonWebFacade.startThreadRDStationMarketing(clienteCompleto, null, null,
                        AtualizarCamposEnum.APENAS_SITUACAO_INADIMPLENCIA);
            }
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        } finally {
            empresaDAO = null;
            clienteDAO = null;
            zillyonWebFacade = null;
        }
    }

    private void expirarCancelarPix(List<MovParcelaVO> listaParcela, Pix pixDAO) throws Exception {
        try {
            List<Integer> codigosParcelas = new ArrayList<>();
            for (MovParcelaVO movParcelaVO : listaParcela) {
                codigosParcelas.add(movParcelaVO.getCodigo());
            }
            List<PixVO> listaPix = pixDAO.consultarAtivosPorParcelas(codigosParcelas);
            if (!UteisValidacao.emptyList(listaPix)) {
                for (PixVO pixVO : listaPix) {
                    pixDAO.alterarStatusAjusteManual(pixVO, PixStatusEnum.CANCELADA);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void registrarLogCapptaServidorIncluirListaPagamento(List listaParcela, MovPagamentoVO movPagamento) {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela, por isso estamos adicionando Logs para tentar encontrar o problema.
        //Pelo tipo de erro no Log do Servidor, imagino que o MovPagamentoVO.contratoVO, está perndendo o código ou ficando null.
        //Quando encontrar o problema, pode retirar esse if
        if (!movPagamento.getCredito() && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad()) && movPagamento.getRespostaRequisicaoPinpad().contains("administrativeCode")) {
            Uteis.logarDebug("CAPPTA - MovPagamento.incluirListaPagamento - Tamanho da Lista de Parcelas = " + listaParcela.size());
            for (Object obj : listaParcela) {
                MovParcelaVO mov = (MovParcelaVO) obj;
                Uteis.logarDebug("Descricao, Codigo, Valor e Situação da MovParcela na Lista = " + mov.getDescricao() + " | " + mov.getCodigo() + " | " + mov.getValorParcela() + " | " + mov.getSituacao());
            }

            MovParcelaVO movParcelaVO = (MovParcelaVO) listaParcela.get(0);
            Uteis.logarDebug("CAPPTA - MovPagamento.incluirListaPagamento - Número do Contrato: " + movParcelaVO.getContrato().getCodigo());
        }
    }

    private void preencherProdutoPagoParcela(List listaParcela, MovPagamentoVO movPagamento) throws Exception {
        if (!movPagamento.getCredito() && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad()) && movPagamento.getRespostaRequisicaoPinpad().contains("administrativeCode")) {
            Uteis.logarDebug("CAPPTA - MovPagamento.preencherProdutoPagoParcela");
            for (Object obj : listaParcela) {
                MovParcelaVO mov = (MovParcelaVO) obj;
                if ((mov.getContrato() == null || mov.getContrato().getCodigo() == 0) &&
                        (mov.getVendaAvulsaVO() == null || mov.getVendaAvulsaVO().getCodigo() == 0) &&
                        (mov.getAulaAvulsaDiariaVO() == null || mov.getAulaAvulsaDiariaVO().getCodigo() == 0) &&
                        (mov.getPersonal() == null || mov.getPersonal().getCodigo() == 0)) {

                    try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon())) {
                        try (ResultSet consulta = criarConsulta("SELECT contrato, vendaavulsa, aulaavulsadiaria, personal FROM movparcela WHERE codigo = " + mov.getCodigo() + ";", connection)) {
                            if (consulta.next()) {
                                if (!UteisValidacao.emptyNumber(consulta.getInt("contrato"))) {
                                    mov.getContrato().setCodigo(consulta.getInt("contrato"));
                                } else if (!UteisValidacao.emptyNumber(consulta.getInt("vendaavulsa"))) {
                                    mov.getVendaAvulsaVO().setCodigo(consulta.getInt("vendaavulsa"));
                                } else if (!UteisValidacao.emptyNumber(consulta.getInt("aulaavulsadiaria"))) {
                                    mov.getAulaAvulsaDiariaVO().setCodigo(consulta.getInt("aulaavulsadiaria"));
                                } else if (!UteisValidacao.emptyNumber(consulta.getInt("personal"))) {
                                    mov.getPersonal().setCodigo(consulta.getInt("personal"));
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                Uteis.logarDebug("CAPPTA - MovPagamento.preencherProdutoPagoParcela - Contrato: " + mov.getContrato().getCodigo());
                Uteis.logarDebug("CAPPTA - MovPagamento.inicializarMovParcela.while - Parcela: " + mov.getCodigo() + ", Situacao: " + mov.getSituacao());
            }
        }
    }

    private void removerNegativacoesSPC(List<MovPagamentoVO> pagamentos, List<MovParcelaVO> listaParcela) {
        List<AcaoSPCCallable> callableTasks = new ArrayList<>();

        UsuarioVO usuarioResponsavel = null;
        EmpresaVO empresaPagamento = null;
        for (MovPagamentoVO pagamento : pagamentos) {
            if (pagamento.getMovPagamentoEscolhida()) {
                empresaPagamento = pagamento.getEmpresa();
                usuarioResponsavel = pagamento.getResponsavelPagamento();
                break;
            }
        }

        if (empresaPagamento != null
                && (UteisValidacao.emptyString(empresaPagamento.getOperadorSpc())
                || UteisValidacao.emptyString(empresaPagamento.getSenhaSpc()))) {
            return;
        }

        try {
            String chave;
            if (JSFUtilities.isJSFContext()) {
                chave = (String) JSFUtilities.getFromSession(JSFUtilities.KEY);
            } else {
                chave = DAO.resolveKeyFromConnection(con);
            }
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);

            for (MovParcelaVO parcelaPaga : listaParcela) {
                if (parcelaPaga.isPago() && parcelaPaga.isIncluidaSPC()) {
                    ParcelaSPCTO parcelaSPC = parcelaPaga.toParcelaSPCTO();

                    callableTasks.add(new AcaoSPCCallable()
                            .setIntegracoesMsUrl(clientDiscoveryDataDTO.getServiceUrls().getIntegracoesMsUrl())
                            .setParcela(parcelaSPC)
                            .setEmpresaVO(empresaPagamento)
                            .setResponsavel(usuarioResponsavel)
                            .setMovparcelaDAO(new MovParcela(con))
                            .setLogDAO(new Log(con))
                            .setAcaoSPC(AcaoSPCCallable.EXCLUIR)
                            .setChave(chave));
                }
            }

            final ExecutorService executorService = Executors.newFixedThreadPool(5);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void alterarValorMultaJurosNaoRecebidos(List<MovParcelaVO> parcelas) throws Exception {
        for (MovParcelaVO movParcela : parcelas) {
            for (MovProdutoParcelaVO movProdutoParcela : movParcela.getMovProdutoParcelaVOs()) {
                if (movProdutoParcela.getMovProdutoVO() != null &&
                        (movProdutoParcela.getMovProdutoVO().getMultaNaoRecebida() > 0.0 || movProdutoParcela.getMovProdutoVO().getJurosNaoRecebidos() > 0.0)) {
                    MovProduto movProdutoDAO = new MovProduto(con);
                    movProdutoDAO.alterarMultaJurosNaoRecebidos(movProdutoParcela.getMovProdutoVO());
                    movProdutoDAO = null;
                }
            }
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovPagamentoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    @SuppressWarnings("unchecked")
    public ReciboPagamentoVO incluirPagamentoParcela(MovParcelaVO parcela, ContratoVO contrato, Date dataCancelamento, UsuarioVO responsavel) throws Exception {
        MovPagamentoVO movPagamento = new MovPagamentoVO();
        FormaPagamento formPag = new FormaPagamento(con);
        List<FormaPagamentoVO> formasPagamento = formPag.consultarPorTipoFormaPagamento("AV", true, false, true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        FormaPagamentoVO fpagamento;
        if (formasPagamento.isEmpty()) {
            fpagamento = new FormaPagamentoVO();
            fpagamento.setDescricao("DINHEIRO");
            fpagamento.setTipoFormaPagamento("AV");
            formPag.incluir(fpagamento);
        } else {
            fpagamento = formasPagamento.get(0);
        }
        movPagamento.setMovPagamentoEscolhida(true);
        movPagamento.setFormaPagamento(fpagamento);
        movPagamento.setDataLancamento(dataCancelamento);
        movPagamento.setDataPagamento(dataCancelamento);
        movPagamento.setDataQuitacao(dataCancelamento);
        movPagamento.setResponsavelPagamento(responsavel);
        movPagamento.setNomePagador(contrato.getPessoa().getNome());
        movPagamento.setPessoa(contrato.getPessoa());
        movPagamento.setEmpresa(contrato.getEmpresa());
        movPagamento.setValor(parcela.getValorParcela());

        PagamentoMovParcelaVO pagMovParcela = new PagamentoMovParcelaVO();
        pagMovParcela.setMovParcela(parcela);
        pagMovParcela.setValorPago(parcela.getValorParcela());
        movPagamento.getPagamentoMovParcelaVOs().add(pagMovParcela);

        List<MovPagamentoVO> listaMPG = new ArrayList<MovPagamentoVO>();
        listaMPG.add(movPagamento);
        List<MovParcelaVO> listaMP = new ArrayList<MovParcelaVO>();
        listaMP.add(parcela);

        return incluirListaPagamento(listaMPG, listaMP, new MovimentoContaCorrenteClienteVO(), contrato, false, 0.0);
    }

    private void gerarNFSeEmitidaParaChequesDevolvidos(ReciboPagamentoVO reciboPagamento, List listaParcela, Connection con) throws Exception {
        List<NFSeEmitidaVO> nfseEmitidas = new ArrayList<NFSeEmitidaVO>();
        for (Object object : listaParcela) {
            MovParcelaVO parcela = (MovParcelaVO) object;
            MovParcela movParcelaDAO = new MovParcela(con);
            if (con.getAutoCommit()) { //pode consultar no banco
                parcela = movParcelaDAO.consultarPorChavePrimaria(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_RECIBOPAGAMENTO);
            } else { //consultar em memória, parcela de multa e juros pode não ter sido commitada ainda
                parcela = (MovParcelaVO) object;
            }

            movParcelaDAO = null;
            boolean contem = false;
            if (parcela.getMovParcelaDetalhamentoVO() != null) {
                for (NFSeEmitidaVO nfseEmitidaDetalheParcela : parcela.getMovParcelaDetalhamentoVO().getNotasEmitidas()) {
                    for (NFSeEmitidaVO nfSeEmitidaVO : nfseEmitidas) {
                        if (nfSeEmitidaVO.getCodigo().equals(nfseEmitidaDetalheParcela.getCodigo())) {
                            contem = true;
                            break;
                        }
                    }
                    if (!contem) {
                        nfseEmitidas.add(nfseEmitidaDetalheParcela);
                    }
                }
            }
        }

        boolean faturamentoRecebido = reciboPagamento.getEmpresa().getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo();
        boolean receita = reciboPagamento.getEmpresa().getTipoGestaoNFSe() == TipoRelatorioDF.RECEITA.getCodigo();
        List<MovPagamentoVO> listaPagamentosAtualizada = consultarPorCodigoRecibo(reciboPagamento.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        for (MovPagamentoVO movPagamentoVO : listaPagamentosAtualizada) {
            for (NFSeEmitidaVO nfSeEmitidaVO : nfseEmitidas) {
                if (receita){
                    if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        for (ChequeVO chequeVO : movPagamentoVO.getChequeVOs()) {
                            NFSeEmitidaVO novaNota = new NFSeEmitidaVO();
                            novaNota.setCheque(chequeVO);
                            novaNota.setContrato(reciboPagamento.getContrato());
                            novaNota.setIdRps(nfSeEmitidaVO.getIdRps());
                            novaNota.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.PROCESSADA);
                            NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                            nfSeEmitidaDAO.incluir(novaNota);
                            nfSeEmitidaDAO = null;
                        }
                    } else if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        for (CartaoCreditoVO cartao : movPagamentoVO.getCartaoCreditoVOs()) {
                            NFSeEmitidaVO novaNota = new NFSeEmitidaVO();
                            novaNota.setCartaoCredito(cartao);
                            novaNota.setContrato(reciboPagamento.getContrato());
                            novaNota.setIdRps(nfSeEmitidaVO.getIdRps());
                            novaNota.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.PROCESSADA);
                            NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                            nfSeEmitidaDAO.incluir(novaNota);
                            nfSeEmitidaDAO = null;
                        }
                    } else {
                        NFSeEmitidaVO novaNota = new NFSeEmitidaVO();
                        novaNota.setMovPagamento(movPagamentoVO);
                        novaNota.setContrato(reciboPagamento.getContrato());
                        novaNota.setIdRps(nfSeEmitidaVO.getIdRps());
                        novaNota.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.PROCESSADA);
                        NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                        nfSeEmitidaDAO.incluir(novaNota);
                        nfSeEmitidaDAO = null;
                    }
                }else if (faturamentoRecebido){
                    NFSeEmitidaVO novaNota = new NFSeEmitidaVO();
                    novaNota.setRecibo(reciboPagamento);
                    novaNota.setContrato(reciboPagamento.getContrato());
                    novaNota.setIdRps(nfSeEmitidaVO.getIdRps());
                    novaNota.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.PROCESSADA);
                    NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                    nfSeEmitidaDAO.incluir(novaNota);
                    nfSeEmitidaDAO = null;
                }
            }
        }
    }

    public void gerarCreditosPersonal(int codigoRecibo) throws Exception {
        if (JSFUtilities.getFromSession("key") != null) {
            GerarCreditosPersonalServico servico = new GerarCreditosPersonalServico(con);
            String returnServ = servico.gerarCreditosColaborador(JSFUtilities.getFromSession("key").toString(), codigoRecibo);
            if (returnServ == null || returnServ.isEmpty() || returnServ.toLowerCase().contains("erro")) {
                throw new Exception("Não foi possível conectar ao TreinoWeb para registrar os créditos do personal. Tente novamente.");
            }
        }
    }

    /**
     * <AUTHOR> Alcides 28/05/2013
     */
    private void excluirPagamentoBoletoEmAberto(List listaParcela) throws SQLException, Exception {
        for (Object obj : listaParcela) {
            MovParcelaVO movParcela = (MovParcelaVO) obj;
            try (ResultSet consulta = criarConsulta(" SELECT mp.codigo FROM movpagamento mp "
                    + " INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo AND pmp.movparcela = " + movParcela.getCodigo()
                    + " WHERE pmp.recibopagamento is null ", con)) {
                if (movParcela.getSituacao().equals("EA")) {
                    while (consulta.next()) {
                        MovPagamentoVO pag = new MovPagamentoVO();
                        pag.setCodigo(consulta.getInt("codigo"));
                        excluirSemCommit(pag);
                    }
                }
            }
        }
    }

    /**
     * Operação responsável adicionar MovPagamentos a um movimento de conta
     * corrente. Primeiramente atualiza a lista de cheques ou cartões de
     * credito. Logo após adiciona o movimento já pronto para ser adicionado
     * junto ao movimento
     *
     * @param movPagamento Objeto da classe <code>MovPagamentoVO</code> que será
     * gravado no banco de dados.
     * @throws InstantiationException
     * @throws IllegalAccessException
     * @throws Exception
     */
    public void adicionaMovpagamentoMovimentoCC(MovPagamentoVO movPagamento, Integer movPagOrigem) throws Exception {

        Double valorMovimento = 0.0;
        movPagamento.setNovoObj(true);
        if (!movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) { // pagamentos do tipo conta corrente não são considerados crédito.
            movPagamento.setCredito(true);
        }
        movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
        movPagamento.setReciboPagamento(new ReciboPagamentoVO());
        if (debito > 0) {
            movPagamento.setValor(debito);
            movPagamento.setValorTotal(debito);
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                movPagamento = atualizarChequeMovimentoCC((MovPagamentoVO) movPagamento.getClone(true));
            }
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                movPagamento = atualizarCartaoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true));
            }
            movPagamento.setMovPagamentoOrigemCredito(movPagOrigem);
            incluirSemCommit(movPagamento);
        }
        if (credito > 0) {
            MovPagamentoVO movCredito = (MovPagamentoVO) movPagamento.getClone(true);
            movCredito.setValor(credito);
            movCredito.setValorTotal(credito);
            if (movCredito.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                movCredito = atualizarChequeMovimentoCC((MovPagamentoVO) movCredito.getClone(true));
            }
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                movCredito = atualizarCartaoMovimentoCC((MovPagamentoVO) movCredito.getClone(true));
            }
            if (debito > 0) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    movPagamento = retiraChequesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                }
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    movPagamento = retiraCartoesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                }
                alterarSemCommit(movPagamento);
            }
            movCredito.setMovPagamentoOrigemCredito(movPagOrigem);
            incluirSemCommit(movCredito);
            getCreditoContaCorrente().getMovPagamentosVOs().add(movCredito);
        }
        if (debito > 0) {
            if (credito > 0 && (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH") || movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA"))) {
                getPagarDebitoCC().getMovPagamentosVOs().add(consultarPorChavePrimaria(movPagamento.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            } else {
                getPagarDebitoCC().getMovPagamentosVOs().add(movPagamento);
            }

        }

    }

    /**
     * Operação responsável adicionar MovPagamentos a um movimento de conta
     * corrente. Primeiramente atualiza a lista de cheques ou cartões de
     * credito. Logo após adiciona o movimento já pronto para ser adicionado
     * junto ao movimento
     *
     * @param movPagamento Objeto da classe <code>MovPagamentoVO</code> que será
     * gravado no banco de dados.
     * @throws InstantiationException
     * @throws IllegalAccessException
     * @throws Exception
     */
    public MovPagamentoVO gerarMovpagamentoMovimentoCC(MovPagamentoVO movPagamento) throws Exception {
        Double valorMovimento = Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorTotal().doubleValue() - movPagamento.getValor().doubleValue());
        movPagamento.setValor(valorMovimento);
        movPagamento.setValorTotal(valorMovimento);
        movPagamento.setNovoObj(true);
        movPagamento.setCredito(true);
        movPagamento.setPagamentoMovParcelaVOs(new ArrayList());
        movPagamento.setReciboPagamento(new ReciboPagamentoVO());
        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
            movPagamento = atualizarChequeMovimentoCC((MovPagamentoVO) movPagamento.getClone(true));
        }
        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
            movPagamento = atualizarCartaoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true));
        }
        if (getCreditoContaCorrente() != null) {
            getCreditoContaCorrente().getMovPagamentosVOs().add(movPagamento);
        }
        return movPagamento;

    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovPagamentoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovPagamentoVO</code> que será gravado
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     * @deprecated Necessário depreciar este para o outro, que tem 2 parametros,
     * ou seja, padronizar ambos.
     */
    @Override
    @Deprecated
    public void incluirSemCommit(MovPagamentoVO obj) throws Exception {
        try {
            MovPagamentoVO.validarDados(obj);
            //incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO MovPagamento( pessoa, dataPagamento, dataLancamento, "
                    + "valor, formaPagamento, nomePagador, operadoraCartao, nrParcelaCartaoCredito, "
                    + "movPagamentoEscolhida, responsavelPagamento, reciboPagamento, "
                    + "dataquitacao, convenioCobranca, dataalteracaomanual, empresa, observacao, autorizacaocartao, "
                    + "valortotal, credito, movpagamentoorigemcredito,movconta, nsu,dataPagamentoOriginal, adquirente, id_recebe, "
                    + "usarParceiroFidelidade, tabelaParceiroFidelidade, multiplicadorParceiroFidelidade, tipoPontoParceiroFidelidade, "
                    + "pontosParceiroFidelidade, cpfParceiroFidelidade, codigoExternoProdutoParceiroFidelidade, senhaParceiroFidelidade, numerounicotransacao, respostaRequisicaoPinpad, enviadoConciliadora) "
                    + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";

            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                int i = 1;
                if (obj.getPessoa().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getPessoa().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }

                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
                sqlInserir.setDouble(i++, obj.getValor());
                if (obj.getFormaPagamento().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getFormaPagamento().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }

                sqlInserir.setString(i++, obj.getNomePagador());
                if (obj.getOperadoraCartaoVO().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getOperadoraCartaoVO().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                sqlInserir.setInt(i++, obj.getNrParcelaCartaoCredito());
                sqlInserir.setBoolean(i++, obj.getMovPagamentoEscolhida());
                if (obj.getResponsavelPagamento().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getResponsavelPagamento().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }

                if (obj.getReciboPagamento().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getReciboPagamento().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataQuitacao()));

                if (obj.getConvenio().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getConvenio().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                if (obj.getDataAlteracaoManual() == null) {
                    sqlInserir.setNull(i++, 0);
                } else {
                    sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataAlteracaoManual()));
                }
                if (obj.getEmpresa().getCodigo() != 0) {
                    sqlInserir.setInt(i++, obj.getEmpresa().getCodigo());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                sqlInserir.setString(i++, obj.getObservacao());
                sqlInserir.setString(i++, obj.getAutorizacaoCartao().toUpperCase().trim());
                sqlInserir.setDouble(i++, obj.getValorTotal());
                sqlInserir.setBoolean(i++, obj.getCredito());
                if (!UteisValidacao.emptyNumber(obj.getMovPagamentoOrigemCredito())) {
                    sqlInserir.setInt(i++, obj.getMovPagamentoOrigemCredito());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                if (!UteisValidacao.emptyNumber(obj.getMovconta())) {
                    sqlInserir.setInt(i++, obj.getMovconta());
                } else {
                    sqlInserir.setNull(i++, 0);
                }
                sqlInserir.setString(i++, obj.getNsu());
                if (obj.getDataPagamentoOriginal() == null) {
                    sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
                } else {
                    sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataPagamentoOriginal()));
                }

                if (UteisValidacao.emptyNumber(obj.getAdquirenteVO().getCodigo())) {
                    sqlInserir.setNull(i++, 0);
                } else {
                    sqlInserir.setInt(i++, obj.getAdquirenteVO().getCodigo());
                }
                if (UteisValidacao.emptyNumber(obj.getId_recebe())) {
                    sqlInserir.setNull(i++, 0);
                } else {
                    sqlInserir.setInt(i++, obj.getId_recebe());
                }

                sqlInserir.setBoolean(i++, obj.isUsarParceiroFidelidade());
                sqlInserir.setInt(i++, obj.getTabelaParceiroFidelidadeVO().getCodigo());
                sqlInserir.setDouble(i++, obj.getMultiplicadorParceiroFidelidade());
                sqlInserir.setInt(i++, obj.getTipoPontoParceiroFidelidade().getCodigo());
                sqlInserir.setInt(i++, obj.getPontosParceiroFidelidade());
                sqlInserir.setString(i++, obj.getCpfParceiroFidelidade());
                sqlInserir.setString(i++, obj.getCodigoExternoProdutoParceiroFidelidade());
                sqlInserir.setString(i++, obj.getSenhaParceiroFidelidade());
                sqlInserir.setString(i++, obj.getNumeroUnicoTransacao());
                sqlInserir.setString(i++, obj.getRespostaRequisicaoPinpad());
                sqlInserir.setBoolean(i++, obj.isEnviadoConciliadora());

                try (ResultSet rs = sqlInserir.executeQuery()) {
                    if (rs.next()) {
                        obj.setCodigo(rs.getInt("codigo"));
                        obj.setNovoObj(false);
                    }
                }
            }

            if (obj.getPagamentoMovParcelaVOs() != null) {

                try {
                    registrarLogCapptaServidorIncluirSemCommit(obj);
                } catch (Exception e){}

                PagamentoMovParcela pagamentoMovParcela = new PagamentoMovParcela(con);
                pagamentoMovParcela.incluirPagamentoMovParcelas(obj.getCodigo(), obj.getPagamentoMovParcelaVOs(), obj.getReciboPagamento().getCodigo());
                pagamentoMovParcela = null;
            }
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Cheque chequeDAO = new Cheque(con);
                chequeDAO.incluirPagamentoCheques(obj.getCodigo(), obj.getChequeVOs());
                chequeDAO =null;
            }
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                if (obj.getCartaoCreditoVOs() == null || obj.getCartaoCreditoVOs().isEmpty()) {
                    montarListaCartaoCredito(obj);
                } else {
                    atualizarMovPagamentoListaCartaoCredito(obj);
                }
                CartaoCredito cartaoDAO = new CartaoCredito(con);
                cartaoDAO.incluirCartaoCreditos(obj.getCodigo(), obj.getCartaoCreditoVOs());
                cartaoDAO = null;
            }
            if ((obj.getEmpresa().getUsarNFSe()) && ((obj.getMovPagamentoOrigemCredito() != null) && (obj.getMovPagamentoOrigemCredito() > 0))) {
                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                nfSeEmitidaDAO.incluirNFSeParaPagamentoDinheiro(obj.getCodigo(), obj.getMovPagamentoOrigemCredito());
                nfSeEmitidaDAO = null;
            }

//            //INICIO - REGISTRANDO LOG DE MODIFICACOES
//            obj.setNovoObj(new Boolean(true));
//            SuperEntidade.registrarLogObjetoVO(obj, obj.getCodigo().intValue(), "MOVPAGAMENTO", obj.getPessoa().getCodigo());
//            obj.setNovoObj(new Boolean(false));
//            //FIM - REGISTRANDO LOG DE MODIFICACOES

        } catch (Exception e) {
            obj.setNovoObj(true);
            throw e;
        }
    }

    private static void registrarLogCapptaServidorIncluirSemCommit(MovPagamentoVO obj) {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela, por isso estamos adicionando Logs para tentar encontrar o problema.
        //Pelo tipo de erro no Log do Servidor, imagino que o MovPagamentoVO.contratoVO, está perndendo o código ou ficando null.
        //Quando encontrar o problema, pode retirar esse if
        if (!UteisValidacao.emptyString(obj.getRespostaRequisicaoPinpad()) && obj.getRespostaRequisicaoPinpad().contains("administrativeCode")) {
            PagamentoMovParcelaVO pagamentoMovParcelaVO = obj.getPagamentoMovParcelaVOs().get(0);
            Uteis.logarDebug(" CAPPTA - MovPagamento.incluirSemCommit - Número do Contrato: " + pagamentoMovParcelaVO.getMovParcela().getContrato().getCodigo());
        }
        //Daqui para frente, não encontrei algo que possa validar o que Cappta ou Nexxera, pois não tem esse dados no PagamentoMovParcela
        //O problema acontece quando vai gravar o PagamentoMovParcelas, mas com os logs atuais, já vejo se ele apaga o contrato da Parcela antes de chegar aqui
        //Se não encontrar, talvez vou ter de alterar o método incluirPagamentoMovParcelas para passar o parâmetro da cappta, para não ficar gerando Log de tudo que é pagamento
    }

    private void atualizarMovPagamentoListaCartaoCredito(MovPagamentoVO obj) {
        Double acumulado = 0.0;
        List<CartaoCreditoVO> atualizadas = new ArrayList<CartaoCreditoVO>();

        for (int i = 0; i < obj.getCartaoCreditoVOs().size(); i++) {
            CartaoCreditoVO cartao = obj.getCartaoCreditoVOs().get(i);
            cartao.setMovpagamento(obj);
        }

    }

    public void montarListaCartaoCredito(MovPagamentoVO obj) throws Exception {
        obj.setCartaoCreditoVOs(new ArrayList<CartaoCreditoVO>());
        Double valorParcela = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getValorTotal() / obj.getNrParcelaCartaoCredito().doubleValue());
        double diferencaEmCentavos = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getValorTotal()
                - (valorParcela
                * obj.getNrParcelaCartaoCredito().doubleValue()));
        boolean primeira = true;
        Date data = obj.getDataPagamento();
        Date dataCompensacaoPorTaxa = null;
        Date dataCompensacao = null;
        int nrParcelaMaxAplicarCompAuto = UteisValidacao.emptyNumber(obj.getNrParcelaCartaoCredito()) ? 0 : obj.getNrParcelaCartaoCredito();
        Double diferenca = Uteis.arredondarForcando2CasasDecimais(obj.getValorTotal() - obj.getValor());
        for (int i = 1; i <= obj.getNrParcelaCartaoCredito(); i++) {

            //Nova Regra, primeiramente validar se existe dias compensacao por taxa. E gerar a data de compensação de acordo com o configurado na taxa do cartão.
            boolean compensacaoPorTaxa = false;
            if(obj.getAdquirenteVO() != null && obj.getAdquirenteVO().getCodigo() != 0 && obj.getOperadoraCartaoVO() != null &&
                    obj.getOperadoraCartaoVO().getCodigo() != 0 && obj.getFormaPagamento().getTaxasCartao() != null && obj.getFormaPagamento().getTaxasCartao().size() > 0){
                for(TaxaCartaoVO taxa : obj.getFormaPagamento().getTaxasCartao()){
                    if(taxa.getNrmeses() == nrParcelaMaxAplicarCompAuto) {
                        if ((taxa.getVigenciaFinal() == null && Calendario.menorOuIgual(taxa.getVigenciaInicial(), obj.getDataPagamento())) ||
                                Calendario.entre(obj.getDataPagamento(), taxa.getVigenciaInicial(), taxa.getVigenciaFinal())) { // validar vigencia da taxa.
                            if (i <= nrParcelaMaxAplicarCompAuto && obj.getAdquirenteVO().getCodigo().equals(taxa.getAdquirenteVO().getCodigo()) &&
                                    obj.getOperadoraCartaoVO().getCodigo().equals(taxa.getBandeira().getCodigo()) && taxa.isCompensacaoPorTaxa()) { // validar condições da taxa, se pode aplicar a dataCompensação.
                                dataCompensacaoPorTaxa = Uteis.somarDias(obj.getDataPagamento(), taxa.getNrDiasCompensacaoPorTaxa());
                                compensacaoPorTaxa = true;
                                break;
                            }
                        }
                    }
                }
            }
            // Regra de negocio quando o pagamento e feito no cartao de credito joga o primeiro pagamento para daqui 30 dias

            //João Alcides: de acordo com a atividade 77733/1/1, foi alterado para que a soma seja sempre de 30 dias, e não de mês
            //em mês, pois é assim que as operadoras de cartão fazem.
            data = Uteis.somarDias(data, obj.getFormaPagamento().getDiasCompensacaoCartaoCredito());
            CartaoCreditoVO cartao = new CartaoCreditoVO();
            cartao.setOperadora(obj.getOperadoraCartaoVO());
            //processamento de Arquivo de Retorno de uma Remessa possui data específica para o depósito
            if (obj.getDataPrevistaDeposito() != null && primeira) {
                dataCompensacao = obj.getDataPrevistaDeposito();
                data = obj.getDataPrevistaDeposito();
            } else if (obj.getFormaPagamento().isCompensacaoDiasUteis()) {
                dataCompensacao = obterProximoDiaUtil(compensacaoPorTaxa ? dataCompensacaoPorTaxa : data, obj.getEmpresa());
            } else {
                dataCompensacao = compensacaoPorTaxa ? dataCompensacaoPorTaxa : data;
            }
            cartao.setDataCompensacao(dataCompensacao);
            cartao.setMovpagamento(obj);
            cartao.setValor(Uteis.arredondarForcando2CasasDecimais(valorParcela));
            cartao.setValorTotal(Uteis.arredondarForcando2CasasDecimais(valorParcela));
            if (primeira && diferencaEmCentavos != 0.0) {
                cartao.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(cartao.getValor() + diferencaEmCentavos));
                cartao.setValorTotal(cartao.getValor());
            }
            cartao.setSituacao("EA");
            cartao.setNrParcela(i);
            if (diferenca > 0) {
                Double valor = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getValor() - ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * i) + diferencaEmCentavos));
                if (valor < 0) {
                    if (Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorParcela + (primeira ? diferencaEmCentavos : 0.0)) > (valor * -1)) {
                        cartao.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorParcela + (primeira ? diferencaEmCentavos : 0.0) + valor));
                        CartaoCreditoVO cartaoresiduo = new CartaoCreditoVO();
                        cartaoresiduo.setOperadora(obj.getOperadoraCartaoVO());
                        cartaoresiduo.setDataCompensacao(dataCompensacao);
                        cartaoresiduo.setNrParcela(i);
                        cartaoresiduo.setMovpagamento(obj);
                        cartaoresiduo.setValor(valor * -1);
                        cartaoresiduo.setSituacao("CA");
                        cartaoresiduo.setValorTotal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorParcela + (primeira ? diferencaEmCentavos : 0.0)));
                        obj.getCartaoCreditoVOs().add(cartao);
                        obj.getCartaoCreditoVOs().add(cartaoresiduo);
                    } else {
                        cartao.setSituacao("CA");
                        obj.getCartaoCreditoVOs().add(cartao);
                    }
                } else {
                    obj.getCartaoCreditoVOs().add(cartao);
                }
            } else {
                obj.getCartaoCreditoVOs().add(cartao);
            }
            primeira = false;
        }

    }

    @Override
    public Date obterProximoDiaUtil(final Date dataBase, EmpresaVO empresa) throws Exception {
        Date dataObtida = Calendario.proximoDiaUtil(dataBase, 0);
        Feriado feriadoDAO = new Feriado(this.con);
        while (!Calendario.isDiaUtil(dataObtida) || feriadoDAO.validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresa, dataObtida)) {
            dataObtida = Calendario.proximoDiaUtil(dataObtida, 1);
        }
        feriadoDAO = null;
        return dataObtida;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovPagamentoVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovPagamentoVO</code> que será alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void alterar(MovPagamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            MovPagamentoVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE MovPagamento set pessoa=?, dataPagamento=?, dataLancamento=?, "
                    + "valor=?, formaPagamento=?, nomePagador=?, operadoraCartao=?, "
                    + "nrParcelaCartaoCredito=?, movPagamentoEscolhida=?, responsavelPagamento=?, "
                    + "reciboPagamento=?, dataquitacao=?, convenioCobranca=?, dataalteracaomanual=?, "
                    + "empresa=? , observacao=?, autorizacaocartao=?, valortotal=?, nsu=?, adquirente = ?, usarParceiroFidelidade = ?, "
                    + "tabelaParceiroFidelidade = ?, multiplicadorParceiroFidelidade = ?, tipoPontoParceiroFidelidade = ?, pontosParceiroFidelidade = ?, "
                    + "cpfParceiroFidelidade = ?, codigoExternoProdutoParceiroFidelidade = ?, senhaParceiroFidelidade = ?, numerounicotransacao = ?, respostaRequisicaoPinpad = ? "
                    + " WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getPessoa().getCodigo() != 0) {
                    sqlAlterar.setInt(1, obj.getPessoa().getCodigo());
                } else {
                    sqlAlterar.setNull(1, 0);
                }

                sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
                sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
                sqlAlterar.setDouble(4, obj.getValor());
                if (obj.getFormaPagamento().getCodigo() != 0) {
                    sqlAlterar.setInt(5, obj.getFormaPagamento().getCodigo());
                } else {
                    sqlAlterar.setNull(5, 0);
                }

                sqlAlterar.setString(6, obj.getNomePagador());
                if (obj.getOperadoraCartaoVO().getCodigo() != 0) {
                    sqlAlterar.setInt(7, obj.getOperadoraCartaoVO().getCodigo());
                } else {
                    sqlAlterar.setNull(7, 0);
                }
                sqlAlterar.setInt(8, obj.getNrParcelaCartaoCredito());
                sqlAlterar.setBoolean(9, obj.getMovPagamentoEscolhida());
                if (obj.getResponsavelPagamento().getCodigo() != 0) {
                    sqlAlterar.setInt(10, obj.getResponsavelPagamento().getCodigo());
                } else {
                    sqlAlterar.setNull(10, 0);
                }

                if (obj.getReciboPagamento().getCodigo() != 0) {
                    sqlAlterar.setInt(11, obj.getReciboPagamento().getCodigo());
                } else {
                    sqlAlterar.setNull(11, 0);
                }
                sqlAlterar.setDate(12, Uteis.getDataJDBC(obj.getDataQuitacao()));
                if (obj.getConvenio().getCodigo() != 0) {
                    sqlAlterar.setInt(13, obj.getConvenio().getCodigo());
                } else {
                    sqlAlterar.setNull(13, 0);
                }
                if (obj.getDataAlteracaoManual() == null) {
                    sqlAlterar.setNull(14, 0);
                } else {
                    sqlAlterar.setDate(14, Uteis.getDataJDBC(obj.getDataAlteracaoManual()));
                }
                if (obj.getEmpresa().getCodigo() != 0) {
                    sqlAlterar.setInt(15, obj.getEmpresa().getCodigo());
                } else {
                    sqlAlterar.setNull(15, 0);
                }
                sqlAlterar.setString(16, obj.getObservacao());
                sqlAlterar.setString(17, obj.getAutorizacaoCartao().toUpperCase().trim());
                sqlAlterar.setDouble(18, obj.getValorTotal());
                sqlAlterar.setString(19, obj.getNsu());
                if (UteisValidacao.emptyNumber(obj.getAdquirenteVO().getCodigo())) {
                    sqlAlterar.setNull(20, 0);
                } else {
                    sqlAlterar.setInt(20, obj.getAdquirenteVO().getCodigo());
                }

                sqlAlterar.setBoolean(21, obj.isUsarParceiroFidelidade());
                sqlAlterar.setInt(22, obj.getTabelaParceiroFidelidadeVO().getCodigo());
                sqlAlterar.setDouble(23, obj.getMultiplicadorParceiroFidelidade());
                sqlAlterar.setInt(24, obj.getTipoPontoParceiroFidelidade().getCodigo());
                sqlAlterar.setInt(25, obj.getPontosParceiroFidelidade());
                sqlAlterar.setString(26, obj.getCpfParceiroFidelidade());
                sqlAlterar.setString(27, obj.getCodigoExternoProdutoParceiroFidelidade());
                sqlAlterar.setString(28, obj.getSenhaParceiroFidelidade());
                sqlAlterar.setString(29, obj.getNumeroUnicoTransacao());
                sqlAlterar.setString(30, obj.getRespostaRequisicaoPinpad());

                sqlAlterar.setInt(31, obj.getCodigo());

                sqlAlterar.execute();
            }
            PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
            pagamentoMovParcelaDAO.alterarPagamentoMovParcelas(obj.getCodigo(), obj.getPagamentoMovParcelaVOs(), obj.getReciboPagamento().getCodigo());
            pagamentoMovParcelaDAO = null;
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Cheque chequeDAO = new Cheque(con);
                chequeDAO.alterarPagamentoCheques(obj, obj.getChequeVOs());
                chequeDAO = null;
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovPagamentoVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovPagamentoVO</code> que será alterada
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterarSemCommit(MovPagamentoVO obj) throws Exception {
        try {
            alterarSemCommit(obj, true);
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarSemCommit(MovPagamentoVO obj, boolean controlarAcesso) throws Exception{
        try {
            MovPagamentoVO.validarDados(obj);
            if(controlarAcesso) {
                alterar(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "UPDATE MovPagamento set pessoa=?, dataPagamento=?, "
                    + "dataLancamento=?, valor=?, formaPagamento=?, nomePagador=?, "
                    + "operadoraCartao=?, nrParcelaCartaoCredito=?, movPagamentoEscolhida=?, "
                    + "responsavelPagamento=?, reciboPagamento=?, "
                    + "dataquitacao=?, convenioCobranca=?, dataalteracaomanual=?, empresa=?, observacao=?, "
                    + " autorizacaocartao=?, valortotal=?, credito=?, movpagamentoorigemcredito=?, produtospagos=?, nsu=?, depositoCC = ?, adquirente = ? "
                    + " WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                int i = 1;
                if (obj.getPessoa().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getPessoa().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }

                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataPagamento()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
                sqlAlterar.setDouble(i++, obj.getValor());
                if (obj.getFormaPagamento().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getFormaPagamento().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }

                sqlAlterar.setString(i++, obj.getNomePagador());
                if (obj.getOperadoraCartaoVO().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getOperadoraCartaoVO().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                sqlAlterar.setInt(i++, obj.getNrParcelaCartaoCredito());
                sqlAlterar.setBoolean(i++, obj.getMovPagamentoEscolhida());
                if (obj.getResponsavelPagamento().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getResponsavelPagamento().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }

                if (obj.getReciboPagamento().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getReciboPagamento().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataQuitacao()));
                if (obj.getConvenio().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getConvenio().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                if (obj.getDataAlteracaoManual() == null) {
                    sqlAlterar.setNull(i++, 0);
                } else {
                    sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataAlteracaoManual()));
                }
                if (obj.getEmpresa().getCodigo() != 0) {
                    sqlAlterar.setInt(i++, obj.getEmpresa().getCodigo());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                sqlAlterar.setString(i++, obj.getObservacao());
                sqlAlterar.setString(i++, obj.getAutorizacaoCartao().trim());
                sqlAlterar.setDouble(i++, obj.getValorTotal());
                sqlAlterar.setBoolean(i++, obj.getCredito());
                if (!UteisValidacao.emptyNumber(obj.getMovPagamentoOrigemCredito())) {
                    sqlAlterar.setInt(i++, obj.getMovPagamentoOrigemCredito());
                } else {
                    sqlAlterar.setNull(i++, 0);
                }
                sqlAlterar.setString(i++, obj.getProdutosPagos());
                sqlAlterar.setString(i++, obj.getNsu());

                boolean origemPagamentoDepositoCC = origemPagamentoEhDepositoContaCorrente(obj);
                sqlAlterar.setBoolean(i++, origemPagamentoDepositoCC);

                if (UteisValidacao.emptyNumber(obj.getAdquirenteVO().getCodigo())) {
                    sqlAlterar.setNull(i++, 0);
                } else {
                    sqlAlterar.setInt(i++, obj.getAdquirenteVO().getCodigo());
                }

                sqlAlterar.setInt(i++, obj.getCodigo());
                sqlAlterar.execute();
            }

            NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
            nfSeEmitidaDAO.excluirPorCartaoCredito(obj.getListaCartaoExcluirVinculoComNFSe());
            nfSeEmitidaDAO.excluirPorCheque(obj.getListaChequeExcluirVinculoComNFSe());
            nfSeEmitidaDAO = null;

            PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
            pagamentoMovParcelaDAO.alterarPagamentoMovParcelas(obj.getCodigo(), obj.getPagamentoMovParcelaVOs(), obj.getReciboPagamento().getCodigo());
            pagamentoMovParcelaDAO = null;
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Cheque chequeDAO = new Cheque(con);
                chequeDAO.alterarPagamentoCheques(obj, obj.getChequeVOs());
                if (obj.getValor() == 0.0) {
                    chequeDAO.removerChequesPagamentosLote(obj.getCodigo());
                }
                chequeDAO =null;
            }
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                if (obj.getCartaoCreditoVOs() == null) {
                    montarListaCartaoCredito(obj);
                }
                CartaoCredito  cartaoCreditoDAO = new CartaoCredito(con);
                cartaoCreditoDAO.alterarCartaoCreditos(obj.getCodigo(), obj.getCartaoCreditoVOs());
                if (obj.getValor() == 0.0) {
                    cartaoCreditoDAO.removerCartoesPagamentosLote(obj.getCodigo());
                }
                cartaoCreditoDAO = null;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    private boolean origemPagamentoEhDepositoContaCorrente(MovPagamentoVO movPagamentoVO)throws Exception{
        if ((movPagamentoVO.getMovPagamentoOrigemCredito() == null) || (movPagamentoVO.getMovPagamentoOrigemCredito() <= 0)){
            return false;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select cc.* \n");
        sql.append("from movPagamento mp \n");
        sql.append("inner join movimentocontacorrentecliente cc on cc.reciboPagamento = mp.reciboPagamento \n");
        sql.append(" \n");
        sql.append("where mp.codigo = ").append(movPagamentoVO.getMovPagamentoOrigemCredito()).append(" and cc.tipoMovimentacao = 'CR' and mp.movPagamentoOrigemCredito is null ");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                return rs.next();
            }
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>MovPagamentoVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovPagamentoVO</code> que será removido
     * no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public void excluir(MovPagamentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM MovPagamento WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }

            PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
            pagamentoMovParcelaDAO.excluirPagamentoMovParcelas(obj.getCodigo());
            pagamentoMovParcelaDAO = null;

            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Cheque chequeDAO = new Cheque(con);
                chequeDAO.excluirPagamentoCheques(obj.getCodigo());
                chequeDAO = null;
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(MovPagamentoVO obj) throws Exception {
        try {
            executarConsultaUpdate("DELETE FROM negociacaoeventocontratopagamento WHERE movpagamento = " + obj.getCodigo().intValue(), con);
            excluir(getIdEntidade());
            String sql = "DELETE FROM MovPagamento WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluirPagtoChequeIncluirOutro(MovPagamentoVO antigo, MovPagamentoVO novo) throws Exception {
        try {
            con.setAutoCommit(false);
            // exclui a lista de cheques antiga
            excluirSemCommit(antigo);
            // inclui o novo movpagamento
            incluirSemCommit(novo);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarPagtoChequeIncluiOutro(MovPagamentoVO antigo, MovPagamentoVO novo) throws Exception {
        try {
            con.setAutoCommit(false);
            // altera a lista de cheques antiga
            alterarSemCommit(antigo);
            // inclui o novo movpagamento
            incluirSemCommit(novo);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void inicializarDadosReciboPagamento(List lista, ReciboPagamentoVO reciboPagamento, ContratoVO contratro) throws Exception {
        try {
            double soma = 0.0;
            Iterator e = lista.iterator();
            while (e.hasNext()) {
                MovPagamentoVO movPag = (MovPagamentoVO) e.next();
                if (movPag.getMovPagamentoEscolhida()) {
                    soma = Uteis.arredondarForcando2CasasDecimais(soma + movPag.getValor());
                }
                if (!UteisValidacao.emptyString(movPag.getObservacao()) && movPag.getObservacao().contains("Pagamento Linx Totem - ")) {
                    Uteis.logarDebug("LINX TOTEM - inicializarDadosReciboPagamento Soma: " + soma);
                }
            }
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPag = (MovPagamentoVO) i.next();
                if (movPag.getMovPagamentoEscolhida()) {
                    reciboPagamento.setResponsavelLancamento(movPag.getResponsavelPagamento());
                    if (movPag.getPessoa().getCodigo().intValue() != 0) {
                        reciboPagamento.setPessoaPagador(movPag.getPessoa());
                    }
                    reciboPagamento.setNomePessoaPagador(movPag.getNomePagador());
                    if (contratro != null && contratro.getCodigo().intValue() != 0) {
                        reciboPagamento.setContrato(contratro);
                    }
                    if (movPag.getDataCobrancaTransacao() != null) {
                        reciboPagamento.setData(movPag.getDataCobrancaTransacao());
                    } else {
                        reciboPagamento.setData(negocio.comuns.utilitarias.Calendario.hoje());
                    }
                    reciboPagamento.setValorTotal(soma);
                    reciboPagamento.setEmpresa(movPag.getEmpresa());
                    break;
                }
                if (!UteisValidacao.emptyString(movPag.getObservacao()) && movPag.getObservacao().contains("Pagamento Linx Totem - ")) {
                    Uteis.logarDebug("LINX TOTEM - inicializarDadosReciboPagamento While");
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarMovParcelaCheque(MovPagamentoVO movPagamento, List listaParcela, ReciboPagamentoVO reciboPagamento) throws Exception {
        try {
            setDepositoContaCorrente(new MovimentoContaCorrenteClienteVO());
            setParcelasCredito(null);
            debito = 0.0;
            credito = 0.0;
            setParcelasCredito(new ArrayList<PagamentoMovParcelaVO>());
            setParcelasDebito(new ArrayList<PagamentoMovParcelaVO>());
            int parcialmentePago = 0;
            Double valorMovPagamentoParcialmentePago = 0.0;
            Double valorMovPagamento = Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor());
            MovParcelaVO parcelaVerificacao = (MovParcelaVO) listaParcela.get(listaParcela.size() - 1);
            ChequeVO chequeVerificacao = new ChequeVO();
            if(!UteisValidacao.emptyList(movPagamento.getChequeVOs())) {
                chequeVerificacao = (ChequeVO) movPagamento.getChequeVOs().get(movPagamento.getChequeVOs().size() - 1);
            }
            verificarPermissaoUsuarioPagamentoChequeDataAposVencimento(chequeVerificacao, parcelaVerificacao);
            Iterator i = listaParcela.iterator();
            while (i.hasNext()) {
                MovParcelaVO movParcela = (MovParcelaVO) i.next();
                Iterator j = movPagamento.getChequeVOs().iterator();
                while (j.hasNext()) {
                    ChequeVO cheque = (ChequeVO) j.next();
//                    verificarPermissaoUsuarioPagamentoChequeDataAposVencimento(cheque, movParcela);
                    validarChequeAvistaPrazo(cheque, movParcela, movPagamento.getEmpresa().getNrDiasChequeAVista());
                    if (movParcela.getSituacao().equals("EA")) {
                        PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
                        List objs = pagamentoMovParcelaDAO.consultarPorCodigoMovParcela(movParcela.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        pagamentoMovParcelaDAO = null;

                        Iterator l = objs.iterator();
                        while (l.hasNext()) {
                            PagamentoMovParcelaVO movParcelaParciamentePago = (PagamentoMovParcelaVO) l.next();
                            valorMovPagamentoParcialmentePago = Uteis.arredondarForcando2CasasDecimais(movParcelaParciamentePago.getValorPago() + valorMovPagamentoParcialmentePago);
                            parcialmentePago = 1;
                        }
                        if (parcialmentePago == 1) {
                            valorMovPagamentoParcialmentePago = Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela() - valorMovPagamentoParcialmentePago);
                            if (valorMovPagamento >= valorMovPagamentoParcialmentePago) {
                                valorMovPagamento = movPagamentoParciamentePago(movPagamento, movParcela, valorMovPagamento, valorMovPagamentoParcialmentePago, reciboPagamento);
                            } else {
                                movPagamentoNaoPago(movPagamento, movParcela, valorMovPagamento);
                                valorMovPagamento = 0.0;
                            }
                            parcialmentePago = 0;
                            valorMovPagamentoParcialmentePago = 0.0;
                        } else if (Uteis.arredondarForcando2CasasDecimais(valorMovPagamento) >= Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela())) {
                            valorMovPagamento = movPagamentoPago(movPagamento, movParcela, valorMovPagamento, reciboPagamento);
                        } else if (Uteis.arredondarForcando2CasasDecimais(valorMovPagamento) > 0) {
                            movPagamentoNaoPago(movPagamento, movParcela, valorMovPagamento);
                            valorMovPagamento = 0.0;
                        }
                    }
                }
            }
            if (movPagamento.getValor() < movPagamento.getValorTotal()) {
                movPagamento = atualizarListaCheques((MovPagamentoVO) movPagamento.getClone(true));
                if (movPagamento.getValor().doubleValue() == 0) {
                    movPagamento.setPagamentoMovParcelaVOs(getParcelasCredito());
                    movPagamento.getPagamentoMovParcelaVOs().addAll(getParcelasDebito());

                }
            }
            validarValorDasParcelas(movPagamento);
            if (movPagamento.isNovoObj().booleanValue()) {
                incluirSemCommit(movPagamento);
            } else {
                alterarSemCommit(movPagamento);
            }
            if (movPagamento.getValor() < movPagamento.getValorTotal()) {

                movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor()));

                if (movPagamento.getValor().doubleValue() > 0) {
                    MovPagamentoVO novo = (MovPagamentoVO) movPagamento.getClone(true);
                    novo.setValor(0.0);
                    novo.setValorTotal(movPagamento.getValorTotal() - movPagamento.getValor());
                    novo.setPagamentoMovParcelaVOs(getParcelasCredito());
                    novo.getPagamentoMovParcelaVOs().addAll(getParcelasDebito());
                    novo.setChequeVOs(new ArrayList<ChequeVO>());
                    Iterator m = movPagamento.getChequeVOs().iterator();
                    while (m.hasNext()) {
                        ChequeVO cheque = (ChequeVO) m.next();
                        if (cheque.getSituacao().equals("CA")) {
                            novo.getChequeVOs().add((ChequeVO) cheque.getClone(true));
                        }
                    }

                    novo.setCredito(false);
                    novo.setReciboPagamento(movPagamento.getReciboPagamento());
                    novo.setNovoObj(true);
                    incluirSemCommit(novo);
                    adicionaMovpagamentoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true), novo.getCodigo());
                    movPagamento = retiraChequesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                    Double valorTotal = new Double(movPagamento.getValor());
                    movPagamento.setValorTotal(valorTotal);
                    alterarSemCommit(movPagamento);
                    reciboPagamento.getPagamentosDesteRecibo().add(novo);

                } else {
                    adicionaMovpagamentoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true), movPagamento.getCodigo());
                }
            }
            reciboPagamento.getPagamentosDesteRecibo().add(movPagamento);

        } catch (Exception e) {
            movPagamento.setNovoObj(true);
            throw e;
        }
    }

    public void verificarPermissaoUsuarioPagamentoChequeDataAposVencimento(ChequeVO cheque, MovParcelaVO movParcela) throws Exception {
        try {
            if (cheque.getDataCompensacao() != null && movParcela.getDataVencimento().compareTo(cheque.getDataCompensacao()) == -1) {
                verificarPermissaoUsuarioFuncionalidade("MovPagamento_AutorizaPagamentoPosteriorDataVencimento", "4.19 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarChequeAvistaPrazo(ChequeVO cheque, MovParcelaVO movParcela, int diasAVista) {
        cheque.setVistaOuPrazo(cheque.getDataCompensacao().compareTo(Uteis.somarDias(negocio.comuns.utilitarias.Calendario.hoje(), diasAVista)) == 1 ? "PR" : "AV");
    }

    public void inicializarMovParcela(List lista, MovPagamentoVO movPagamento, List listaParcela, ReciboPagamentoVO reciboPagamento, Boolean receberTroco) throws Exception {
        setDepositoContaCorrente(new MovimentoContaCorrenteClienteVO());
        try {
            int parcialmentePago = 0;
            credito = 0.0;
            debito = 0.0;
            movPagamento.setPagamentoMovParcelaVOs(new ArrayList());
            setParcelasCredito(new ArrayList<PagamentoMovParcelaVO>());
            setParcelasDebito(new ArrayList<PagamentoMovParcelaVO>());
            Double valorMovPagamentoParcialmentePago = 0.0;
            Double valorMovPagamento = Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorTotal());
            Iterator i = listaParcela.iterator();

            try {
                registrarLogCapptaServidorInicializarMovParcelaInicioInteracao(movPagamento, listaParcela);
            } catch (Exception e){}

            while (i.hasNext()) {
                MovParcelaVO movParcela = (MovParcelaVO) i.next();
                try {
                    if (movPagamento != null && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
                        Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.while Antes Ajuste - Parcela: " + movParcela.getCodigo() + ", Situacao: " + movParcela.getSituacao());
                        preencherSituacaoParcelaCappta(movParcela);
                        Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.while Depois Ajuste - Parcela: " + movParcela.getCodigo() + ", Situacao: " + movParcela.getSituacao());
                    }
                } catch (Exception e){}
                if (movParcela.getSituacao().equals("EA")) {
                    PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
                    List objs = pagamentoMovParcelaDAO.consultarPorCodigoMovParcela(movParcela.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
                    pagamentoMovParcelaDAO = null;
                    Iterator l = objs.iterator();
                    //itera nas partes já pagas desta parcela
                    while (l.hasNext()) {
                        PagamentoMovParcelaVO movParcelaParciamentePago = (PagamentoMovParcelaVO) l.next();
                        valorMovPagamentoParcialmentePago = Uteis.arredondarForcando2CasasDecimais(movParcelaParciamentePago.getValorPago() + valorMovPagamentoParcialmentePago);
                        parcialmentePago = 1;
                    }
                    //caso já tenha uma parte da parcela paga
                    try {
                        if (movPagamento != null && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
                            Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.while - Já tem Parte Parcela Paga = " + parcialmentePago + " || OBS: 1 sim e 0 não.");
                        }
                    } catch (Exception e){}
                    if (parcialmentePago == 1) {
                        //verificamos quanto da parcela ainda falta ser paga
                        valorMovPagamentoParcialmentePago = Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela() - valorMovPagamentoParcialmentePago);
                        //se o valor do pagamento ainda paga o que resta da parcela
                        if (Uteis.arredondarForcando2CasasDecimais(valorMovPagamento) >= valorMovPagamentoParcialmentePago) {
                            //terminar de quitar a parcela
                            valorMovPagamento = movPagamentoParciamentePago(movPagamento, movParcela, valorMovPagamento, valorMovPagamentoParcialmentePago, reciboPagamento);
                        } else {
                            //se o valor da parcela ainda é maior do que o pagamento, pagar uma parte da parcela
                            movPagamentoNaoPago(movPagamento, movParcela, valorMovPagamento);
                            valorMovPagamento = 0.0;
                        }
                        parcialmentePago = 0;
                        valorMovPagamentoParcialmentePago = 0.0;
                    } //se a parcela está toda em aberto, verificar se o pagamento paga a parcela
                    else if (Uteis.arredondarForcando2CasasDecimais(valorMovPagamento) >= Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela())) {
                        try {
                            if (movPagamento != null && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
                                Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.while - Entrou em Parcela Toda em Aberto e verificar se o pagamento paga a parcela.");
                            }
                        } catch (Exception e){}
                        //caso pague, quitar a parcela e guardar o "troco" para pagar outras parcelas
                        valorMovPagamento = movPagamentoPago(movPagamento, movParcela, valorMovPagamento, reciboPagamento);
                    } //se o valor do pagamento não paga toda a parcela, mas é maior do que 0
                    else if (valorMovPagamento > 0) {
                        //pagar uma parte da parcela
                        try {
                            if (movPagamento != null && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
                                Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.while - Entrou em Se o valor do pagamento não paga toda a parcela, mas é maior do que 0.");
                            }
                        } catch (Exception e){}
                        movPagamentoNaoPago(movPagamento, movParcela, valorMovPagamento);
                        valorMovPagamento = 0.0;
                    }

                }
            }
            try {
                if (movPagamento != null && !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
                    Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.getValor - movPagamento.getValor = " + movPagamento.getValor().doubleValue());
                    Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.getValor - movPagamento.getPagamentoMovParcelaVOs Tamanho = " + movPagamento.getPagamentoMovParcelaVOs().size());
                }
            } catch (Exception e){}
            if (movPagamento.getValor().doubleValue() == 0) {
                movPagamento.setPagamentoMovParcelaVOs(getParcelasCredito());
                movPagamento.getPagamentoMovParcelaVOs().addAll(getParcelasDebito());
            }
            //Joao Alcides: aqui valido se as parcelas condizem com o pagamento
            validarValorDasParcelas(movPagamento);
            if (movPagamento.isNovoObj().booleanValue()) {
                try {
                    registrarLogCapptaServidorInicializarMovParcela(movPagamento, listaParcela);
                } catch (Exception e) {}

                incluirSemCommit(movPagamento);
            } else {
                alterarSemCommit(movPagamento, false);
            }

            if (movPagamento.getValor() < movPagamento.getValorTotal()) {
                movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor()));
                if (movPagamento.getValor().doubleValue() > 0) {
                    MovPagamentoVO novo = (MovPagamentoVO) movPagamento.getClone(true);
                    novo.setValor(0.0);
                    novo.setValorTotal(movPagamento.getValorTotal() - movPagamento.getValor());
                    novo.setPagamentoMovParcelaVOs(getParcelasCredito());
                    novo.getPagamentoMovParcelaVOs().addAll(getParcelasDebito());
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        novo.setCartaoCreditoVOs(new ArrayList<CartaoCreditoVO>());
                        Iterator m = movPagamento.getCartaoCreditoVOs().iterator();
                        while (m.hasNext()) {
                            CartaoCreditoVO cartao = (CartaoCreditoVO) m.next();
                            if (cartao.getSituacao().equals("CA")) {
                                novo.getCartaoCreditoVOs().add((CartaoCreditoVO) cartao.getClone(true));
                            }
                        }
                    }
                    novo.setCredito(false);
                    novo.setReciboPagamento(movPagamento.getReciboPagamento());
                    novo.setNovoObj(true);
                    incluirSemCommit(novo);
                    adicionaMovpagamentoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true), novo.getCodigo());
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        movPagamento = retiraCartoesCancelados((MovPagamentoVO) movPagamento.getClone(true));
                    }
                    Double valorTotal = new Double(movPagamento.getValor());
                    movPagamento.setValorTotal(valorTotal);
                    alterarSemCommit(movPagamento, false);
                    reciboPagamento.getPagamentosDesteRecibo().add(novo);

                } else {
                    adicionaMovpagamentoMovimentoCC((MovPagamentoVO) movPagamento.getClone(true), movPagamento.getCodigo());
                }
            }
            reciboPagamento.getPagamentosDesteRecibo().add(movPagamento);
            //     validarResiduo(valorMovPagamento, lista, movPagamento, listaParcela, reciboPagamento, usuarioLogado, receberTroco);
        } catch (Exception e) {
            e.printStackTrace();
            movPagamento.setNovoObj(true);

            StringBuilder msg = new StringBuilder(e.toString());

            if (reciboPagamento != null && reciboPagamento.getPessoaPagador() != null) {
                msg.append("\nCodigo Pessoa: ").append(reciboPagamento.getPessoaPagador().getCodigo())
                        .append("\nNome Pessoa: ").append(reciboPagamento.getPessoaPagador().getNome())
                        .append("\nCPF Pessoa: ").append(reciboPagamento.getPessoaPagador().getCfp());
            }
            if (reciboPagamento != null && reciboPagamento.getContrato() != null) {
                msg.append("\nCodigo Contrato: ").append(reciboPagamento.getContrato().getCodigo())
                        .append("\nE possivel que a situacao da parcela esteja (CA) cancelada.");
            }

            throw new Exception(msg.toString());
        }
    }

    private void preencherSituacaoParcelaCappta(MovParcelaVO movParcela) {
        if (movParcela.getSituacao().equals("PG")) {
            try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon())) {
                try (ResultSet consulta = criarConsulta("SELECT situacao FROM movparcela WHERE codigo = " + movParcela.getCodigo() + ";", connection)) {
                    if (consulta.next()) {
                        if (!UteisValidacao.emptyString(consulta.getString("situacao"))) {
                            movParcela.setSituacao(consulta.getString("situacao"));
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    private static void registrarLogCapptaServidorInicializarMovParcelaInicioInteracao(MovPagamentoVO movPagamento, List listaParcela) {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela, por isso estamos adicionando Logs para tentar encontrar o problema.
        //Pelo tipo de erro no Log do Servidor, imagino que o MovPagamentoVO.contratoVO, está perndendo o código ou ficando null.
        //Quando encontrar o problema, pode retirar esse if
        if (!UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
            Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela.registrarLogCapptaServidorInicializarMovParcelaInicioInteracao - Tamanho Lista: " + listaParcela.size());
        }
    }

    private static void registrarLogCapptaServidorInicializarMovParcela(MovPagamentoVO movPagamento, List listaParcela) {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela, por isso estamos adicionando Logs para tentar encontrar o problema.
        //Pelo tipo de erro no Log do Servidor, imagino que o MovPagamentoVO.contratoVO, está perndendo o código ou ficando null.
        //Quando encontrar o problema, pode retirar esse if
        if (!UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad())) {
            MovParcelaVO movParcelaVO = (MovParcelaVO) listaParcela.get(0);
            Uteis.logarDebug(" CAPPTA - MovPagamento.inicializarMovParcela - Número do Contrato: " + movParcelaVO.getContrato().getCodigo());
        }
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 10/05/2013
     */
    private void validarValorDasParcelas(MovPagamentoVO movPagamento) throws ConsistirException {
        if (movPagamento.getValor() > 0.0 && !movPagamento.getCredito()) {
            Double parcelas = 0.0;
            for (Object obj : movPagamento.getPagamentoMovParcelaVOs()) {
                PagamentoMovParcelaVO pmp = (PagamentoMovParcelaVO) obj;
                parcelas += pmp.getValorPago();
            }
            if (Uteis.arredondarForcando2CasasDecimais(parcelas) < Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor())) {
                registrarLogCapptaServidorValidarValorDasParcelas(movPagamento, parcelas);
                throw new ConsistirException("O valor das parcelas não condiz com o pagamento. Volte ao 'Caixa em Aberto' e selecione as parcelas novamente para efetuar o pagamento. Certifique-se de não estar utilizando o Sistema em mais de uma aba do seu navegador.");
            }
        }
    }

    private static void registrarLogCapptaServidorValidarValorDasParcelas(MovPagamentoVO movPagamento, Double parcelas) {
        try {
            Uteis.logarDebug("=== registrarLogCapptaServidorValidarValorDasParcelas ===");
            Uteis.logarDebug("Tamanho da Lista de Parcelas = " + movPagamento.getPagamentoMovParcelaVOs().size());
            for (Object obj : movPagamento.getPagamentoMovParcelaVOs()) {
                PagamentoMovParcelaVO pmp = (PagamentoMovParcelaVO) obj;
                Uteis.logarDebug("Descricao, Codigo e Valor da MovParcela na Lista = " + pmp.getMovParcela().getDescricao() + " | " + pmp.getMovParcela().getCodigo() + " | " + pmp.getValorPago());
            }
            Uteis.logarDebug("Valor Total das Parcelas: " + parcelas + ", Valor Pago: " + movPagamento.getValor());
        } catch (Exception e) {

        }
    }

    public Double movPagamentoParciamentePago(MovPagamentoVO movPagamento, MovParcelaVO movParcela, Double valorMovPagamento, Double valorMovPagamentoParcialmentePago, ReciboPagamentoVO reciboPagamento) throws Exception {
        PagamentoMovParcelaVO pagamentoMovParcela = new PagamentoMovParcelaVO();
        valorMovPagamento = valorMovPagamento - valorMovPagamentoParcialmentePago;
        movParcela.setSituacao("PG");
        movParcela.setReciboPagamento(reciboPagamento);
        pagamentoMovParcela.setMovParcela(movParcela);
        pagamentoMovParcela.setValorPago(Uteis.arredondarForcando2CasasDecimais(valorMovPagamentoParcialmentePago));
        if (movParcela.isMovimentoCC()) {
            inicializarMovimentoCC(movPagamento, pagamentoMovParcela.getValorPago(), movParcela.getDescricao().substring(5));
            getParcelasCredito().add(pagamentoMovParcela);
        } else if (movParcela.getMovPagamentoCC() != null && movParcela.getMovPagamentoCC() != "") {
            inicializarMovimentoCCDebito(movPagamento, pagamentoMovParcela.getValorPago(), movParcela.getDescricao().substring(5), movParcela.getMovPagamentoCC());
            getParcelasDebito().add(pagamentoMovParcela);
        } else {
            if (movPagamento.getPagamentoMovParcelaVOs() == null){
                movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
            }
            movPagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcela);
        }
        return valorMovPagamento;
    }

    public Double movPagamentoPago(MovPagamentoVO movPagamento, MovParcelaVO movParcela, Double valorMovPagamento, ReciboPagamentoVO reciboPagamento) throws Exception {
        PagamentoMovParcelaVO pagamentoMovParcela = new PagamentoMovParcelaVO();
        valorMovPagamento = Uteis.arredondarForcando2CasasDecimais(valorMovPagamento - (movParcela.getValorParcela()));
        movParcela.setSituacao("PG");
        movParcela.setReciboPagamento(reciboPagamento);
        pagamentoMovParcela.setValorPago(Uteis.arredondarForcando2CasasDecimais(movParcela.getValorParcela()));
        pagamentoMovParcela.setMovParcela(movParcela);
        if (movParcela.isMovimentoCC()) {
            inicializarMovimentoCC(movPagamento, pagamentoMovParcela.getValorPago(), movParcela.getDescricao().substring(5));
            getParcelasCredito().add(pagamentoMovParcela);
        } else if (!UteisValidacao.emptyString(movParcela.getMovPagamentoCC())) {
            inicializarMovimentoCCDebito(movPagamento, pagamentoMovParcela.getValorPago(), movParcela.getDescricao().substring(5), movParcela.getMovPagamentoCC());
            getParcelasDebito().add(pagamentoMovParcela);
        } else {
            if (movPagamento.getPagamentoMovParcelaVOs() == null){
                movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
            }
            movPagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcela);
        }
        return valorMovPagamento;
    }

    public void movPagamentoNaoPago(MovPagamentoVO movPagamento, MovParcelaVO movParcela, Double valorMovPagamento) throws Exception {
        PagamentoMovParcelaVO pagamentoMovParcela = new PagamentoMovParcelaVO();
        pagamentoMovParcela.setValorPago(Uteis.arredondarForcando2CasasDecimais(valorMovPagamento));
        movParcela.setSituacao("EA");
        pagamentoMovParcela.setMovParcela(movParcela);
        if (movParcela.isMovimentoCC()) {
            inicializarMovimentoCC(movPagamento, pagamentoMovParcela.getValorPago(), movParcela.getDescricao().substring(5));
            getParcelasCredito().add(pagamentoMovParcela);
        } else if (movParcela.getMovPagamentoCC() != null && movParcela.getMovPagamentoCC() != "") {
            inicializarMovimentoCCDebito(movPagamento, pagamentoMovParcela.getValorPago(), movParcela.getDescricao().substring(5), movParcela.getMovPagamentoCC());
            getParcelasDebito().add(pagamentoMovParcela);
        } else {
            if (movPagamento.getPagamentoMovParcelaVOs() == null){
                movPagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
            }
            movPagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcela);
        }
    }

    private void inicializarMovimentoCCDebito(MovPagamentoVO movPagamento,
                                              Double valorMovPagamento, String descricao, String movPagamentosDebito) {
        if (getPagarDebitoCC() == null) {
            setPagarDebitoCC(new MovimentoContaCorrenteClienteVO());
            getPagarDebitoCC().setDescricao(descricao);
            getPagarDebitoCC().setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            getPagarDebitoCC().setPessoa(movPagamento.getPessoa());
            getPagarDebitoCC().setResponsavelAutorizacao(movPagamento.getResponsavelPagamento());
            getPagarDebitoCC().setTipoMovimentacao("CR");
            getPagarDebitoCC().setValor(0.0);
            getPagarDebitoCC().setReciboPagamentoVO(movPagamento.getReciboPagamento());
        }
        if (getPagarDebitoCC().getPagamentosDebito().equals("") && getPagarDebitoCC().getPagamentosDebito() != null) {
            getPagarDebitoCC().setPagamentosDebito(movPagamentosDebito);
        } else {
            String[] movDebitos = movPagamentosDebito.split(",");
            String[] atuais = getPagarDebitoCC().getPagamentosDebito().split(",");
            for (int i = 0; i < movDebitos.length; i++) {
                boolean presente = false;
                for (int j = 0; j < atuais.length; j++) {
                    if (movDebitos[i].equals(atuais[j])) {
                        presente = true;
                        break;
                    }
                }
                if (!presente) {
                    getPagarDebitoCC().setPagamentosDebito(getPagarDebitoCC().getPagamentosDebito() + "," + movDebitos[i]);
                }
            }
        }
        movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor() - valorMovPagamento));
        debito = Uteis.arredondarForcando2CasasDecimais(valorMovPagamento + debito);
        getPagarDebitoCC().setValor(Uteis.arredondarForcando2CasasDecimais(getPagarDebitoCC().getValor() + valorMovPagamento));

    }

    private void inicializarMovimentoCC(MovPagamentoVO movPagamento,
                                        Double valorMovPagamento, String descricao) throws Exception {
        if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
            throw new ConsistirException("Produtos que geram crédito na conta corrente do aluno não podem  ser pagos com conta corrente");
        }
        if (getCreditoContaCorrente() == null) {
            setCreditoContaCorrente(new MovimentoContaCorrenteClienteVO());
            getCreditoContaCorrente().setDescricao(descricao);
            getCreditoContaCorrente().setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            getCreditoContaCorrente().setPessoa(movPagamento.getPessoa());
            getCreditoContaCorrente().setResponsavelAutorizacao(movPagamento.getResponsavelPagamento());
            getCreditoContaCorrente().setTipoMovimentacao("CR");
            getCreditoContaCorrente().setValor(0.0);
            getCreditoContaCorrente().setReciboPagamentoVO(movPagamento.getReciboPagamento());
        }
        movPagamento.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamento.getValor() - valorMovPagamento));
        credito = Uteis.arredondarForcando2CasasDecimais(valorMovPagamento + credito);
        getCreditoContaCorrente().setValor(Uteis.arredondarForcando2CasasDecimais(getCreditoContaCorrente().getValor() + valorMovPagamento));

    }

    @Override
    public MovimentoContaCorrenteClienteVO gerarMovimentoContaCorrenteCliente(MovPagamentoVO movPagamento,
                                                                              Double valorMovPagamento, String descricao, String tipoMovimento) throws Exception {
        try {
            setDepositoContaCorrente(new MovimentoContaCorrenteClienteVO());
            getDepositoContaCorrente().setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            getDepositoContaCorrente().setDescricao(descricao);
            getDepositoContaCorrente().setPessoa(movPagamento.getPessoa());
            getDepositoContaCorrente().setResponsavelAutorizacao(movPagamento.getResponsavelPagamento());
            getDepositoContaCorrente().setTipoMovimentacao(tipoMovimento);
            getDepositoContaCorrente().setValor(valorMovPagamento);

            MovimentoContaCorrenteCliente movDAO = new MovimentoContaCorrenteCliente(con);
            MovimentoContaCorrenteClienteVO mov = movDAO.consultarPorCodigoPessoa(movPagamento.getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movDAO = null;

            if (mov == null) {
                getDepositoContaCorrente().setSaldoAtual(valorMovPagamento);
            } else {
                getDepositoContaCorrente().setSaldoAtual(valorMovPagamento + mov.getSaldoAtual());
            }
            return getDepositoContaCorrente();
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluirMovPagamentoEstornoContrato(Integer codigoReciboPagamento) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM MovPagamento WHERE ((ReciboPagamento = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codigoReciboPagamento.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String codigoOperacaoCartao</code>. Retorna os objetos, com início
     * do valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPagamentoDeUmContrato(Integer codigoContrato, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM movpagamento ");
        sqlStr.append("WHERE recibopagamento IN (SELECT rp.codigo FROM recibopagamento rp ");
        sqlStr.append("INNER JOIN movprodutoparcela mpp ON mpp.recibopagamento = rp.codigo ");
        sqlStr.append("INNER JOIN movparcela mp ON mpp.movparcela = mp.codigo WHERE mp.contrato = ?)");
        try (PreparedStatement sql = con.prepareStatement(sqlStr.toString())) {
            sql.setInt(1, codigoContrato);
            try (ResultSet tabelaResultado = sql.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    @Override
    public List consultarPagamentoDeUmaParcela(Integer codigoParcela, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM movpagamento ");
        sqlStr.append("WHERE codigo IN (SELECT pp.codigo FROM pagamentomovparcela pp ");
        sqlStr.append(" WHERE pp.movparcela = ?)");
        try (PreparedStatement sql = con.prepareStatement(sqlStr.toString())) {
            sql.setInt(1, codigoParcela);
            try (ResultSet tabelaResultado = sql.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String codigoOperacaoCartao</code>. Retorna os objetos, com início
     * do valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCodigoOperacaoCartao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( codigoOperacaoCartao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY codigoOperacaoCartao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String numeroCartao</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorNumeroCartao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( numeroCartao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY numeroCartao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String bancoCheque</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorBancoCheque(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( bancoCheque ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY bancoCheque";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String agenciaCheque</code>. Retorna os objetos, com início do
     * valor do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorAgenciaCheque(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( agenciaCheque ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY agenciaCheque";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String numeroCheque</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorNumeroCheque(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( numeroCheque ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY numeroCheque";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String cpfPagador</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCpfPagador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( cpfPagador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY cpfPagador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>String nomePagador</code>. Retorna os objetos, com início do valor
     * do atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorNomePagador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE upper( nomePagador ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nomePagador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>FormaPagamento</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorDescricaoFormaPagamento(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovPagamento.* FROM MovPagamento, FormaPagamento WHERE MovPagamento.formaPagamento = FormaPagamento.codigo and upper( FormaPagamento.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY FormaPagamento.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>descricao</code> da classe
     * <code>FormaPagamento</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoFormaPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovPagamento.* FROM MovPagamento, FormaPagamento WHERE MovPagamento.formaPagamento = FormaPagamento.codigo and FormaPagamento.codigo = " + valorConsulta + "  ORDER BY FormaPagamento.descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>Date dataLancamento</code>. Retorna os objetos com valores
     * pertecentes ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorDataLancamento(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE ((dataLancamento >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataLancamento <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataLancamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }


    public List consultarPorDataPagamentoProdutoPlano(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE ((dataLancamento >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataLancamento <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataLancamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }






    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>Date dataPagamento</code>. Retorna os objetos com valores
     * pertecentes ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorDataPagamento(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE ((dataPagamento >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataPagamento <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataPagamento";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    public List<MovPagamentoVO> consultarPorDataPagamentoPessoa(Date hoje, int empresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM MovPagamento WHERE (dataPagamento::date) = '" + Uteis.getDataJDBC(Uteis.obterDataAnterior(hoje, 1))+"' and empresa = "+empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    @Override
    public List consultarComFiltros(int empresa, String nomep, String nomeTerceiro, Date lanci, Date lancf, String horalanci, String horalancf,
                                    Date compi, Date compf, String horacompi, String horacompf, String formasPagamento, String cpf, String matricula,
                                    String nomeClienteContrato, ChequeVO chequeVO, Boolean pesquisarComLote, UsuarioVO operadorCaixa,
                                    String codigoAutorizacao, Integer operadoraCartao, String nsu, boolean chequeAvista, boolean chequeAprazo,
                                    boolean considerarDataOriginal, List<Integer> codigoLote, boolean mostrarCancelados,
                                    int nivelMontarDados, Integer codigoCentroCusto, boolean antecipados,
                                    StatusMovimentacaoEnum statusMovimentacao, Integer adquirente, String numeroDocumento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct movpagamento.*, empresa.nome as empresaNome, cli.matricula,cli.cpf as cpfPagador, conta.descricao as descricaoconta, movc.dataquitacao as datamovconta, ");
        sql.append(" array_to_string(array(select  ");
        sql.append(" mp.descricao ");
        sql.append(" from pagamentomovparcela p ");
        sql.append(" inner join movparcela mp on mp.codigo = p.movparcela ");
        sql.append(" where p.movpagamento  = movpagamento.codigo), ', ', '') as parcelasPagas, ");
        sql.append("p.nomepai, p.nomemae, p.datanasc ");
        sql.append(" from movpagamento ");
        sql.append(" left join empresa ON empresa.codigo = movpagamento.empresa");
        sql.append(" left outer join situacaoclientesinteticodw as cli on cli.codigopessoa = movpagamento.pessoa ");
        sql.append(" left outer join movconta as movc on movc.codigo = movpagamento.movconta ");
        sql.append(" left outer join conta on conta.codigo = movc.conta ");
        sql.append(" left join pessoa p on p.codigo = movpagamento.pessoa ");

        // se periodo de compensacao foi informado
        if (compi != null || chequeVO != null) {
            sql.append("left outer join cheque on movpagamento.codigo = cheque.movpagamento ");
            if (!mostrarCancelados){
                sql.append(" and cheque.situacao not like 'CA' ");
                sql.append(" and cheque.situacao not like 'DV' ");
            }
            sql.append("left outer join cartaocredito on movpagamento.codigo = cartaocredito.movpagamento ");
            if (!mostrarCancelados){
                sql.append(" AND cartaocredito.situacao not like 'CA' ");
            }
        }
        if (!nomep.trim().isEmpty()) {
            sql.append(" left outer join pessoa as pesPagador on movpagamento.pessoa = pesPagador.codigo ");
        }
        if (!UteisValidacao.emptyString(nomeClienteContrato) || !UteisValidacao.emptyString(cpf) || !UteisValidacao.emptyString(matricula)) {
            // foi feito inner join com a mesma tabela(pagamentomovparcela) para que todos os pagamentos de um recibos que pagam uma parcela sejam apresentados, senão apenas o pagamento que paga uma parcela é apresentado e os demais não
            // isso pode acontecer com frequência em pagamentos conjuntos
            sql.append(" left outer join (select distinct pmp.movpagamento as movpPessoa from pagamentomovparcela pmp inner join pagamentomovparcela pmprecibo on pmprecibo.recibopagamento = pmp.recibopagamento inner join movparcela mpar on pmprecibo.movparcela = mpar.codigo inner join pessoa pesParcela on pesParcela.codigo = mpar.pessoa ");
            if (!UteisValidacao.emptyString(nomeClienteContrato)){
                sql.append(" and pesParcela.nome ilike '").append(nomeClienteContrato).append("%'");
            }
            if (!UteisValidacao.emptyString(cpf)){
                sql.append(" and pesParcela.cfp = '").append(cpf).append("'");
            }
            if (!UteisValidacao.emptyString(matricula)){
                try {
                    sql.append(" inner join cliente as cliParcela on cliParcela.pessoa = pesParcela.codigo and cliParcela.codigomatricula = ").append(Integer.valueOf(matricula));
                }   catch(Exception e){
                    throw new Exception("Matricula inválida");
                }
            }
            sql.append(" ) as fooPessoa on fooPessoa.movpPessoa = movpagamento.codigo ");

            if (UteisValidacao.emptyString(nomep)){
                sql.append(" left outer join pessoa as pesPagador on movpagamento.pessoa = pesPagador.codigo");
                if (!UteisValidacao.emptyString(matricula)){
                    sql.append(" inner join cliente as cliParcela on cliParcela.pessoa = pesPagador.codigo");
                }
            }
        }
        sql.append(" inner join formapagamento fp on movpagamento.formapagamento = fp.codigo ");

        if (!formasPagamento.trim().isEmpty()) {
            sql.append(" and fp.tipoformapagamento IN (").append(formasPagamento).append(") ");
        }

        sql.append("inner join usuario u on u.codigo = movpagamento.responsavelpagamento ");
        if (operadorCaixa != null && !UteisValidacao.emptyNumber(operadorCaixa.getCodigo())) {
            sql.append(" and u.codigo = ").append(operadorCaixa.getCodigo()).append(" ");
        }

        //amostra de antecipações que vem do extratodiário
        if (considerarDataOriginal) {
            sql.append(" left join extratodiarioitem ei on ei.codigo = (\n");
            sql.append(" select max(ei2.codigo) from extratodiarioitem ei2\n");
            sql.append(" where ei2.codigocartaocredito IS NOT NULL and ei2.codigocartaocredito = cartaocredito.codigo\n");
            sql.append(" and ei2.datapgtooriginalantesdaantecipacao is not null\n");
            sql.append(" and ei2.antecipacao is true\n");
            sql.append(" and ei2.tipoconciliacao =  ").append(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()).append("\n");
            sql.append(")\n");
        }

        sql.append(" where 1 = 1 \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and movpagamento.empresa = ").append(empresa).append(" \n");
        }
//        sql.append(" and movpagamento.codigo not in (select movpagamento from negociacaoeventocontratopagamento) ");
        // se filtro pelo pagador foi informado
        sql.append(" and (movpagamento.recibopagamento is not null or movpagamento.credito = 't') and (movpagamento.valor > 0 or fp.tipoformapagamento = 'CC') "); // elimina pagamento sem recibo, no caso  de boleto. Apenas pagamento de crédito que não tem recibo podem ser consultados.



        if (!nomep.trim().isEmpty()) {
            sql.append(" and (pesPagador.nome ILIKE '%").append(nomep.toUpperCase()).append("%' "); // pegar o nome atual do aluno
            sql.append(" or movpagamento.nomepagador ILIKE '%").append(nomep.toUpperCase()).append("%' ) ");  // pagamento de venda de consumidor
        }else if(!UteisValidacao.emptyString(nomeClienteContrato) || !UteisValidacao.emptyString(cpf) || !UteisValidacao.emptyString(matricula)){
            if (!UteisValidacao.emptyString(nomeClienteContrato)){
                sql.append(" and (pesPagador.nome ILIKE '%").append(nomeClienteContrato.toUpperCase()).append("%' ");
                sql.append(" or movpagamento.nomepagador ILIKE '%").append(nomeClienteContrato.toUpperCase()).append("%' ) ");
            }
            if(!UteisValidacao.emptyString(cpf)){
                sql.append(" and pesPagador.cfp = '").append(cpf).append("'");
            }
            if (!UteisValidacao.emptyString(matricula)){
                sql.append(" and cliParcela.codigomatricula = '").append(Integer.valueOf(matricula)).append("'");
            }
        }

        // se os 2 periodos foram informados
        if (lanci != null & compi != null) {
            sql.append("and ((movpagamento.datalancamento >= '").append(Uteis.getDataJDBC(lanci)).append(" " + horalanci + "' ");
            sql.append(" and movpagamento.datalancamento <= '").append(Uteis.getDataJDBC(lancf)).append(" " + horalancf + "' ) ) ");
            sql.append(" and ((movpagamento.datapagamento >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
            sql.append(" and movpagamento.datapagamento <= '").append(Uteis.getDataJDBC(compf));
            sql.append(" " + horacompf + "' and fp.tipoformapagamento IN ('AV','CD','CC','BB','CO','TB','PX')) ");
            sql.append(" or (cheque.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
            sql.append(" and cheque.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" " + horacompf + "') ");
            sql.append(" or (cartaocredito.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
            sql.append(" and cartaocredito.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" " + horacompf + "')) ");
            // se somente o periodo de lancamento foi informado
        } else if (lanci != null) {
            sql.append(" and (movpagamento.datalancamento >= '").append(Uteis.getDataJDBC(lanci)).append(" " + horalanci + "' ")
                    .append(" and movpagamento.datalancamento <= '").append(Uteis.getDataJDBC(lancf)).append(" " + horalancf + "') ");
            // se somente o periodo de compensacao foi informado
        } else if (compi != null) {
            if (considerarDataOriginal) {
                sql.append(" and ((movpagamento.datapagamentooriginal >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
                sql.append(" and movpagamento.datapagamentooriginal <= '");
            } else {
                sql.append(" and ((movpagamento.datapagamento >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
                sql.append(" and movpagamento.datapagamento <= '");
            }
            sql.append(Uteis.getDataJDBC(compf)).append(" " + horacompf + "' and fp.tipoformapagamento IN ('AV','CD','CC','BB','CO','TB','PX')) ");

            if (considerarDataOriginal) {
                sql.append(" or ((cheque.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" ").append(horacompi).append("' ");
                sql.append(" and cheque.datacompesancao <= '").append(Uteis.getDataJDBC(compf));
                sql.append(" ").append(horacompf).append("' and cheque.dataoriginal is null) OR ");

                sql.append(" (cheque.dataoriginal >= '").append(Uteis.getDataJDBC(compi)).append(" ").append(horacompi).append("' ");
                sql.append(" and cheque.dataoriginal <= '").append(Uteis.getDataJDBC(compf)).append(" ").append(horacompf).append("'  and fp.tipoformapagamento = 'CH' ))");
            } else {
                sql.append(" or (cheque.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
                sql.append(" and cheque.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" " + horacompf + "'  and fp.tipoformapagamento = 'CH' ) ");
            }

            if (considerarDataOriginal) {
                sql.append(" or ((cartaocredito.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" ").append(horacompi).append("' ");
                sql.append(" and cartaocredito.datacompesancao <= '").append(Uteis.getDataJDBC(compf));
                sql.append(" ").append(horacompf).append("' and cartaocredito.dataoriginal is null) OR ");

                sql.append(" (cartaocredito.dataoriginal >= '").append(Uteis.getDataJDBC(compi)).append(" ").append(horacompi).append("' ");
                sql.append(" and cartaocredito.dataoriginal <= '").append(Uteis.getDataJDBC(compf)).append(" ").append(horacompf).append("'  and fp.tipoformapagamento = 'CA' ) ");

                sql.append("OR (ei.datapgtooriginalantesdaantecipacao BETWEEN '").append(Uteis.getDataJDBC(compi)).append(" ").append(horacompi).append("'").append(" and '").append(Uteis.getDataJDBC(compf)).append(" ").append(horacompf).append("' ");
                sql.append(" and fp.tipoformapagamento = 'CA')");

                sql.append("))");
            } else {
                sql.append(" or (cartaocredito.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" " + horacompi + "' ");
                sql.append(" and cartaocredito.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" " + horacompf + "' and fp.tipoformapagamento = 'CA' )) ");
            }

        }

        if(antecipados){
            sql.append(" and ((cartaocredito.dataoriginal > cartaocredito.datacompesancao) or (cheque.dataoriginal > cheque.datacompesancao)) ");
        }

        //se cheque for informado
        if (chequeVO != null) {
            if (!UteisValidacao.emptyString(chequeVO.getAgencia())) {
                sql.append(" and cheque.agencia ilike '").append(chequeVO.getAgencia()).append("%'");
            }
            if (!UteisValidacao.emptyString(chequeVO.getConta())) {
                sql.append(" and cheque.conta ilike '").append(chequeVO.getConta()).append("%'");
            }
            if (!UteisValidacao.emptyString(chequeVO.getNumero())) {
                sql.append(" and cheque.numero = '").append(chequeVO.getNumero()).append("'");
            }
            if (chequeVO.getBanco() != null && !UteisValidacao.emptyNumber(chequeVO.getBanco().getCodigo().intValue())) {
                sql.append(" and cheque.banco = ").append(chequeVO.getBanco().getCodigo().intValue());
            }
        }
        if (!UteisValidacao.emptyString(nomeTerceiro)) {
            sql.append(" and remove_acento_upper(cheque.nomenocheque) ilike remove_acento_upper('").append(nomeTerceiro).append("%')");
        }

        if (chequeAprazo && !chequeAvista) {
            sql.append(" and cheque.vistaouprazo = 'PR' ");
        }

        if (chequeAvista && !chequeAprazo) {
            sql.append(" and cheque.vistaouprazo = 'AV' ");
        }
        if (!UteisValidacao.emptyString(codigoAutorizacao)) {
            //remover espaços em branco no banco antes de comparar
            sql.append(" AND regexp_replace(movpagamento.autorizacaocartao, '\\s+', '', 'g') LIKE  '%").append(codigoAutorizacao.trim()).append("'");
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            sql.append(" and movpagamento.operadoracartao = ").append(operadoraCartao);
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            //adquirente 9999 é o filtro 'SEM ADQUIRENTE'
            if (adquirente == 9999) {
                sql.append(" and movpagamento.adquirente is null ");
            } else {
                sql.append(" and movpagamento.adquirente= ").append(adquirente);
            }
        }
        if (!UteisValidacao.emptyString(nsu)) {
            sql.append(" and movpagamento.nsu = '").append(nsu).append("' ");
        }

        if (!UteisValidacao.emptyString(numeroDocumento)) {
            sql.append(" and exists(select codigo from boleto where boleto.movpagamento = movpagamento.codigo and boleto.idexterno = '").append(numeroDocumento).append("') \n");
        }


        //se pesquisar considerando somente com lotes ou sem lotes
        if (pesquisarComLote != null) {
            if (pesquisarComLote) {
                sql.append("and (EXISTS (SELECT * FROM chequecartaolote WHERE cheque = cheque.codigo) or EXISTS (SELECT * FROM chequecartaolote where cartao = cartaocredito.codigo))");
            } else {
                sql.append("and (NOT EXISTS (SELECT * FROM chequecartaolote WHERE cheque = cheque.codigo) AND NOT EXISTS(SELECT * FROM chequecartaolote where cartao = cartaocredito.codigo))");
            }
        }

        if (!UteisValidacao.emptyNumber(codigoCentroCusto)) {
            StringBuilder subselectCentroCusto = new StringBuilder();
            subselectCentroCusto.append("(SELECT 1 FROM movpagamento mp ");
            subselectCentroCusto.append("INNER JOIN recibopagamento rp ON rp.codigo = mp.recibopagamento ");
            subselectCentroCusto.append("INNER JOIN movprodutoparcela mpp ON mpp.recibopagamento = rp.codigo ");
            subselectCentroCusto.append("INNER JOIN movproduto mprod ON mprod.codigo = mpp.movproduto ");
            subselectCentroCusto.append("LEFT JOIN produto prod ON mprod.produto = prod.codigo ");
            subselectCentroCusto.append("LEFT JOIN movprodutomodalidade mpm ON mpm.movproduto = mprod.codigo ");
            subselectCentroCusto.append("LEFT JOIN modalidade mod ON mod.codigo = mpm.modalidade ");
            subselectCentroCusto.append("LEFT JOIN rateiointegracao riprod ON riprod.produto = prod.codigo OR prod.categoriaproduto = riprod.categoriaproduto ");
            subselectCentroCusto.append("LEFT JOIN rateiointegracao rimod ON rimod.modalidade = mod.codigo ");
            subselectCentroCusto.append("WHERE mp.codigo = movpagamento.codigo ");
            subselectCentroCusto.append("AND (riprod.centrocusto = " + codigoCentroCusto + " OR rimod.centrocusto = " + codigoCentroCusto + ")) ");

            sql.append(" AND EXISTS " + subselectCentroCusto.toString());
        }

        if (codigoLote != null && !codigoLote.isEmpty()) {
            String lotes = "(";
            for (int i = 0; i < codigoLote.size(); i++) {
                lotes += (i + 1) == codigoLote.size() ? codigoLote.get(i) + ")" : codigoLote.get(i) + ",";
            }
            sql.append(" and ((cheque.codigo in (select cheque from historicocheque where datafim is null and lote IN ").append(lotes).append("))");
            sql.append(" or (not exists(select cheque from historicocheque where datafim is null)) and ")
                    .append("(SELECT CASE WHEN COUNT(lote) > 0 THEN TRUE ELSE FALSE END FROM lote lte inner join chequecartaolote ")
                    .append("cclote on cclote.lote = lte.codigo WHERE cclote.cheque = cheque.codigo AND lote IN ").append(lotes).append(")");
            sql.append(" or cartaocredito.codigo in (select cartao from chequecartaolote where lote IN ")
                    .append(lotes).append("))");
        }

        List<MovPagamentoVO> resultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                resultado = montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
        if (!UteisValidacao.emptyNumber(codigoCentroCusto) && resultado != null && !resultado.isEmpty()) {
            carregarCentroCusto(resultado);
        }
        return resultado;
    }

    @Override
    public List consultarComFiltros(List<String> idEmpresas, String nomep, String nomeTerceiro, Date lanci, Date lancf, String horalanci, String horalancf,
                                    Date compi, Date compf, String horacompi, String horacompf, String formasPagamento, String cpf, String matricula,
                                    String nomeClienteContrato, ChequeVO chequeVO, Boolean pesquisarComLote, UsuarioVO operadorCaixa,
                                    String codigoAutorizacao, Integer operadoraCartao, String nsu, boolean chequeAvista, boolean chequeAprazo,
                                    boolean considerarDataOriginal, List<Integer> codigoLote, boolean mostrarCancelados,
                                    int nivelMontarDados, Integer codigoCentroCusto, boolean antecipados,
                                    StatusMovimentacaoEnum statusMovimentacao, Integer adquirente, String numeroDocumento) throws Exception {

        List<MovPagamentoVO> resultado = new ArrayList<>();

        if (!UteisValidacao.emptyList(idEmpresas)) {
            for(String empresa: idEmpresas){
                resultado.addAll(
                        consultarComFiltros( 
                                Integer.parseInt(empresa), nomep, nomeTerceiro, lanci, lancf, horalanci, horalancf, compi, compf, horacompi, horacompf, formasPagamento,
                                cpf, matricula, nomeClienteContrato, chequeVO, pesquisarComLote, operadorCaixa, codigoAutorizacao, operadoraCartao, nsu, chequeAvista,
                                chequeAprazo, considerarDataOriginal, codigoLote, mostrarCancelados, nivelMontarDados, codigoCentroCusto, antecipados, statusMovimentacao, 
                                adquirente, numeroDocumento
                ));
            }
        }

        return resultado;
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Pessoa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovPagamento.* FROM MovPagamento, Pessoa WHERE MovPagamento.pessoa = Pessoa.codigo and upper( Pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Pessoa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Método usado para consultar historico de pagamentos de clientes
     *
     * @param valorConsulta
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public List<MovPagamentoVO> consultarPorCodigoClienteParaHistoricoPagamento(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        /*String sqlStr = "SELECT MovPagamento.* FROM MovPagamento, Pessoa WHERE MovPagamento.pessoa = Pessoa.codigo and  Pessoa.codigo =" + valorConsulta.intValue() + " ORDER BY dataLancamento desc";*/
        String sqlStr = String.format("select distinct mp.* from movpagamento mp "
                + "inner join movprodutoparcela mpp on mpp.recibopagamento = mp.recibopagamento "
                + "inner join movproduto mprod on mprod.codigo = mpp.movproduto and mprod.pessoa = %s"
                + "where (mp.pessoa = %s AND mp.recibopagamento is not null) "
                + "or (mprod.pessoa = %s) "
                + "ORDER BY dataLancamento desc", valorConsulta, valorConsulta, valorConsulta);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Método usado para consultar historico de pagamentos de consumidores
     *
     * @param nomeComprador
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public List consultarPorConsumidorParaHistoricoPagamento(String nomeComprador, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "select * from movpagamento "
                + "where movpagamento.nomepagador ilike '" + nomeComprador + "' and (recibopagamento in (select recibopagamento from movprodutoparcela)) "
                + "and pessoa is null "
                + "ORDER BY dataLancamento desc;";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Método usado para consultar movimentações de pagamento com
     * dataAlteracaoManual
     *
     * @param empresa
     * @param fim
     * @return
     * @throws Exception 07/11/2011
     */
    @Override
    public int contarPagamentoAlteracaoManual(Date inicio, Date fim, Integer empresa, List<ColaboradorVO> lista) throws Exception {
        String sql = new String();
        int qtde = 0;
        sql = " SELECT count(movpagamento.codigo)as qtd FROM movpagamento X  WHERE movpagamento.dataalteracaomanual ";
        sql += "BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND  '" + Uteis.getDataJDBC(fim) + " 23:59:59'";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += " AND movpagamento.empresa = " + empresa;
        }

        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sql = sql.replaceAll("X", "INNER JOIN recibopagamento on recibopagamento.codigo = movpagamento.recibopagamento LEFT JOIN usuario usu ON recibopagamento.responsavellancamento = usu.codigo ");
                    qtde++;
                    sql += " AND (";
                } else {
                    sql += " OR ";
                }
                sql += "usu.colaborador = " + co.getCodigo().intValue();
            }
        }
        sql = sql.replaceAll("X", "");
        sql += (qtde > 0 ? ")" : "");

        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (!rs.next()) {
                    return 0;
                }
                return rs.getInt("qtd");
            }
        }
    }

    /**
     * Responsável por consultar pagamentos com alteracao manual por intervalo
     * de datas e empresa como parametro
     *
     * <AUTHOR> 07/11/2011
     * @throws Exception
     */
    @Override
    public List<MovPagamentoVO> consultarPorIntervaloDatasEmpresaDataAlteracaoManual(Date inicio, Date fim, int empresa, List<ColaboradorVO> lista, final int nivelMontarDados) throws Exception {
        String sql = new String();
        int qtde = 0;
        List<MovPagamentoVO> result = new ArrayList<MovPagamentoVO>();
        sql = " SELECT movpagamento.* FROM movpagamento X WHERE movpagamento.dataalteracaomanual ";
        sql += "BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND  '" + Uteis.getDataJDBC(fim) + " 23:59:59'";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += " AND movpagamento.empresa = " + empresa;
        }

        // filtra a consulta pelos colaboradores
        for (ColaboradorVO co : lista) {
            if (co.getColaboradorEscolhidoOperacoes()) {
                if (qtde == 0) {
                    sql = sql.replaceAll("X", "INNER JOIN recibopagamento on recibopagamento.codigo = movpagamento.recibopagamento LEFT JOIN usuario usu ON recibopagamento.responsavellancamento = usu.codigo ");
                    qtde++;
                    sql += " AND (";
                } else {
                    sql += " OR ";
                }
                sql += "usu.colaborador = " + co.getCodigo().intValue();
            }
        }
        sql = sql.replaceAll("X", "");
        sql += (qtde > 0 ? ")" : "");

        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                result = montarDadosConsulta(rs, nivelMontarDados, con, true);
            }
        }
        return result;
    }

    /**
     * Método usado para consultar historico de pagamentos de contratos de
     * clientes
     *
     * @param valorConsulta
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    @Override
    public List consultarPorClienteContratoParaHistoricoPagamento(Integer valorConsulta, int contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT movpagamento.* FROM movpagamento "
                + "INNER JOIN recibopagamento ON movpagamento.recibopagamento = recibopagamento.codigo "
                + "WHERE movpagamento.pessoa = " + valorConsulta.intValue() + " and "
                + "recibopagamento.contrato = " + contrato + " ORDER BY dataLancamento desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>Pessoa</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM MovPagamento WHERE pessoa = " + valorConsulta.intValue() + " ORDER BY Pessoa";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovPagamento WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovPagamento</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public List<MovPagamentoVO> consultarPorCodigoRecibo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = " SELECT * from movPagamento where recibopagamento = " + valorConsulta.intValue();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da
     * <code>HistoricoContratoVO</code> no BD. Garantindo o relacionamento com a
     * entidade principal
     * <code>contrato.Contrato</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception Erro de conexão com o BD ou restrição de acesso a
     * esta operação.
     */
    @Override
    public void incluirMovPagamentos(List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            MovPagamentoVO obj = (MovPagamentoVO) e.next();
            incluirSemCommit(obj);
        }

    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovPagamentoVO</code> resultantes da consulta.
     */
    public static List<MovPagamentoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con, boolean turnOffCache) throws Exception {
        CacheControl.toggleCache(Empresa.class, true);
        CacheControl.toggleCache(Banco.class, true);
        CacheControl.toggleCache(Usuario.class, true);
        CacheControl.toggleCache(ConvenioCobranca.class, true);
        CacheControl.toggleCache(OperadoraCartao.class, true);
        CacheControl.toggleCache(FormaPagamento.class, true);
        CacheControl.toggleCache(Cidade.class, true);
        CacheControl.toggleCache(Estado.class, true);
        CacheControl.toggleCache(Pais.class, true);
        CacheControl.toggleCache(Adquirente.class, true);
        CacheControl.toggleCache(Pessoa.class, true);

        try {
            List<MovPagamentoVO> vetResultado = new ArrayList<>();
            while (tabelaResultado.next()) {
                MovPagamentoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
                vetResultado.add(obj);
            }
            return vetResultado;
        } finally {
            if (turnOffCache) {
                CacheControl.clear();
            }
        }
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>MovPagamentoVO</code>.
     *
     * @return O objeto da classe <code>MovPagamentoVO</code> com os dados
     * devidamente montados.
     */
    public static MovPagamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovPagamentoVO obj = new MovPagamentoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        try{
            obj.getPessoa().setNomePai(dadosSQL.getString("nomepai"));
            obj.getPessoa().setDataNasc(dadosSQL.getTimestamp("datanasc"));
            obj.getPessoa().setNomeMae(dadosSQL.getString("nomemae"));
        } catch (Exception ignored) {
        }
        obj.setDataPagamento(dadosSQL.getTimestamp("dataPagamento"));
        obj.setDataPagamentoOriginal(dadosSQL.getTimestamp("dataPagamentoOriginal"));
        obj.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setCredito(dadosSQL.getBoolean("credito"));
        obj.setDepositoCC(dadosSQL.getBoolean("depositoCC"));
        obj.setValorTotal(dadosSQL.getDouble("valortotal"));
        obj.setNomePagador(dadosSQL.getString("nomePagador"));
        try {
            obj.setCpfPagador(dadosSQL.getString("cpfPagador"));
        }catch (Exception ignored){}
        obj.getOperadoraCartaoVO().setCodigo(dadosSQL.getInt("operadoraCartao"));
        obj.setNrParcelaCartaoCredito(dadosSQL.getInt("nrParcelaCartaoCredito"));
        obj.getResponsavelPagamento().setCodigo(dadosSQL.getInt("responsavelPagamento"));
        obj.getReciboPagamento().setCodigo(dadosSQL.getInt("reciboPagamento"));
        obj.setMovPagamentoEscolhida(dadosSQL.getBoolean("movPagamentoEscolhida"));
        obj.setDataQuitacao(dadosSQL.getDate("dataquitacao"));
        obj.setDataAlteracaoManual(dadosSQL.getDate("dataalteracaomanual"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setAutorizacaoCartao(dadosSQL.getString("autorizacaocartao"));
        obj.setNumeroUnicoTransacao(dadosSQL.getString("numerounicotransacao"));
        obj.setNsu(dadosSQL.getString("nsu"));
        obj.setRespostaRequisicaoPinpad(dadosSQL.getString("respostaRequisicaoPinpad"));

        ConvenioCobrancaVO convenio = null;
        FormaPagamentoVO formaPagamento = null;
        if (nivelMontarDados != Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) {
            convenio = new ConvenioCobrancaVO();
            convenio.setCodigo(dadosSQL.getInt("convenioCobranca"));
            formaPagamento = new FormaPagamentoVO();
            formaPagamento.setCodigo(dadosSQL.getInt("formaPagamento"));
        }
        obj.setConvenio(convenio);
        obj.setFormaPagamento(formaPagamento);
        obj.setNovoObj(false);

        obj.setEmpresa(getCachedEmpresa(dadosSQL.getInt("empresa"), con));
        try {
            obj.getEmpresa().setNome(dadosSQL.getString("empresaNome"));
        }catch (Exception ignored){}

        try {
            obj.setUsarParceiroFidelidade(dadosSQL.getBoolean("usarParceiroFidelidade"));
            obj.setTabelaParceiroFidelidadeVO(new TabelaParceiroFidelidadeVO());
            obj.getTabelaParceiroFidelidadeVO().setCodigo(dadosSQL.getInt("tabelaParceiroFidelidade"));
            obj.setMultiplicadorParceiroFidelidade(dadosSQL.getDouble("multiplicadorParceiroFidelidade"));
            obj.setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.valueOf(dadosSQL.getInt("tipoPontoParceiroFidelidade")));
            obj.setPontosParceiroFidelidade(dadosSQL.getInt("pontosParceiroFidelidade"));
            obj.setCpfParceiroFidelidade(dadosSQL.getString("cpfParceiroFidelidade"));
            obj.setParceiroFidelidadeProcessado(dadosSQL.getBoolean("parceiroFidelidadeProcessado"));
            obj.setCodigoExternoProdutoParceiroFidelidade(dadosSQL.getString("codigoExternoProdutoParceiroFidelidade"));
            obj.setSenhaParceiroFidelidade(dadosSQL.getString("senhaParceiroFidelidade"));
        } catch (Exception ignored) {
        }

        try {
            obj.setMovPagamentoOrigemCredito(dadosSQL.getInt("movpagamentoorigemcredito"));
            obj.setProdutosPagos(dadosSQL.getString("produtospagos"));
            obj.setProdutosPagosCancelados(dadosSQL.getString("produtospagoscancelados"));
            obj.setMatriculaPagador(dadosSQL.getString("matricula"));
        } catch (Exception ignored) {
        }

        try {
            obj.setNrCheques(dadosSQL.getInt("nrcheque"));
        } catch (Exception ignored) {
        }

        try {
            obj.setMovconta(dadosSQL.getInt("movconta"));
        } catch (Exception ignored) {
        }

        try {
            obj.setDataMovimento(dadosSQL.getDate("datamovconta"));
            obj.setContaFinanceiro(dadosSQL.getString("descricaoconta"));
        } catch (Exception ignored) {
        }
        try {
            obj.getAdquirenteVO().setCodigo(dadosSQL.getInt("adquirente"));
        }catch (Exception ignored){
        }
        try {
            obj.setEnviadoConciliadora(dadosSQL.getBoolean("enviadoConciliadora"));
        }catch (Exception ignored){
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            montarDadosFormaPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_LANCAMENTOS_FINANCEIROS) {
            return obj;
        }

        Integer codigoConvenio = dadosSQL.getInt("convenioCobranca");
        Integer formaPagamentoCodigo = dadosSQL.getInt("formaPagamento");
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) {
            if (!UteisValidacao.emptyNumber(codigoConvenio)) {
                ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
                obj.setConvenio(convenioCobrancaDAO.consultarPorChavePrimaria(codigoConvenio, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                convenioCobrancaDAO = null;
            }
            if (!UteisValidacao.emptyNumber(formaPagamentoCodigo)) {
                FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
                obj.setFormaPagamento(formaPagamentoDAO.consultarPorChavePrimaria(formaPagamentoCodigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                formaPagamentoDAO = null;
            }
            return obj;
        }

        montarDadosFormaPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosAdquirente(obj, con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            montarDadosOperadoraCartao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
//            PagamentoMovParcela pagamentoMovParcela = new PagamentoMovParcela(con);
//            obj.setPagamentoMovParcelaVOs(pagamentoMovParcela.consultarPagamentoMovParcelas(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
//            pagamentoMovParcela = null;
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PAGAMENTOS_TELA_CLIENTE) {
            montarDadosOperadoraCartao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            if (!obj.getReciboPagamento().getCodigo().equals(0)) {
                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                obj.setReciboPagamento(reciboPagamentoDAO.consultarPorChavePrimaria(obj.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                reciboPagamentoDAO = null;
            }
            return obj;
        }
        if (obj.getEmpresa().getCodigo() > 0) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            empresa = null;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS) {
            if (!UteisValidacao.emptyNumber(obj.getReciboPagamento().getCodigo())) {
                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                obj.setReciboPagamento(reciboPagamentoDAO.consultarPorChavePrimaria(obj.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS));
                reciboPagamentoDAO = null;
                try {
                    //validar se é recebimento de um aluno pagando para outro para montar hint lá na tela do gestão de recebíveis
                    if (!UteisValidacao.emptyNumber(obj.getPessoa().getCodigo()) &&
                            obj.getReciboPagamento() != null &&
                            obj.getReciboPagamento().getContrato() != null &&
                            obj.getReciboPagamento().getContrato().getPessoa() != null &&
                            !UteisValidacao.emptyNumber(obj.getReciboPagamento().getContrato().getPessoa().getCodigo()) &&
                            !obj.getPessoa().getCodigo().equals(obj.getReciboPagamento().getContrato().getPessoa().getCodigo())) {
                        obj.setPessoaVODoContratoDoMovPagamento(obj.getReciboPagamento().getContrato().getPessoa());
                        obj.setCodMatriculaDoContratoDoMovPagamento(obj.getReciboPagamento().getContrato().getCliente().getCodigoMatricula());
                    }
                } catch (Exception ignored) {
                }
            }
            return obj;
        }

        int forcarNivel = nivelMontarDados == Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO ? nivelMontarDados : Uteis.NIVELMONTARDADOS_TODOS;

        PagamentoMovParcela pagamentoMovParcela;
        try {
            pagamentoMovParcela = new PagamentoMovParcela(con);
            obj.setPagamentoMovParcelaVOs(pagamentoMovParcela.consultarPagamentoMovParcelas(obj.getCodigo(), forcarNivel));
        } catch (Exception ex) {
        } finally {
            pagamentoMovParcela = null;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PARCELA) {
            montarDadosOperadoraCartao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            if (!obj.getReciboPagamento().getCodigo().equals(0)) {
                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                obj.setReciboPagamento(reciboPagamentoDAO.consultarPorChavePrimaria(obj.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                reciboPagamentoDAO = null;
            }
            return obj;
        }

        Cheque cheque = new Cheque(con);
        obj.setChequeVOs(cheque.consultarPagamentoCheques(obj.getCodigo(), null, false, false, false, Uteis.NIVELMONTARDADOS_TODOS));
        cheque = null;

        CartaoCredito cartaoCredito = new CartaoCredito(con);
        obj.setCartaoCreditoVOs(cartaoCredito.consultarCartaoCreditos(obj.getCodigo(), forcarNivel));
        cartaoCredito = null;

        if (convenio.getCodigo() != 0) {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            obj.setConvenio(convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio.getCodigo(), obj.getEmpresa().getCodigo(), forcarNivel));
            convenioCobrancaDAO = null;
        } else {
            obj.setConvenio(new ConvenioCobrancaVO());
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        montarDadosOperadoraCartao(obj, nivelMontarDados, con);
        montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        montarDadosResponsavelPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>FormaPagamentoVO</code> relacionado ao objeto
     * <code>MovPagamentoVO</code>. Faz uso da chave primária da classe
     * <code>FormaPagamentoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param con
     */
    private static void montarDadosFormaPagamento(MovPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        int codigoFormaPagamento = obj.getFormaPagamento().getCodigo();

        if (codigoFormaPagamento == 0) {
            obj.setFormaPagamento(new FormaPagamentoVO());
            return;
        }

        FormaPagamento formaPagamento = new FormaPagamento(con);
        FormaPagamentoVO formaPagamentoVO = formaPagamento.consultarPorChavePrimaria(codigoFormaPagamento, nivelMontarDados);
        obj.setFormaPagamento(formaPagamentoVO);
        formaPagamento = null;
    }

    public static void montarDadosResponsavelPagamento(MovPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelPagamento().getCodigo() == 0) {
            obj.setResponsavelPagamento(new UsuarioVO());
            return;

        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavelPagamento(usuario.consultarPorChavePrimaria(obj.getResponsavelPagamento().getCodigo(), nivelMontarDados));
        usuario = null;
    }

    private static void montarDadosAdquirente(MovPagamentoVO obj, Connection con) throws Exception {
        int codigoAdquirente = obj.getAdquirenteVO().getCodigo();
        if (UteisValidacao.emptyNumber(codigoAdquirente)) {
            return;
        }

        Adquirente adDAO = new Adquirente(con);
        AdquirenteVO adquirenteVO = adDAO.consultarPorCodigo(codigoAdquirente);
        obj.setAdquirenteVO(adquirenteVO);
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto
     * <code>MovPagamentoVO</code>. Faz uso da chave primária da classe
     * <code>PessoaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param con
     */
    public static void montarDadosPessoa(MovPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoa().getCodigo() == 0) {
            obj.setPessoa(new PessoaVO());
            return;

        }
        Pessoa pessoa = new Pessoa(con);
        obj.setPessoa(pessoa.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), nivelMontarDados));
        pessoa = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto
     * <code>MovPagamentoVO</code>. Faz uso da chave primária da classe
     * <code>PessoaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param con conexão
     */
    private static void montarDadosOperadoraCartao(MovPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        try {
            int codigoOperadora = obj.getOperadoraCartaoVO().getCodigo();
            if (codigoOperadora == 0) {
                obj.setOperadoraCartaoVO(new OperadoraCartaoVO());
                return;
            }
            OperadoraCartao operadoraCartao = new OperadoraCartao(con);
            OperadoraCartaoVO operadoraCartaoVO = operadoraCartao.consultarPorChavePrimaria(codigoOperadora, nivelMontarDados);
            obj.setOperadoraCartaoVO(operadoraCartaoVO);
            obj.setNomeOperadoraCartao(operadoraCartaoVO.getDescricao());
            operadoraCartao = null;
        }catch (Exception e){}
    }

    /**
     * Operação responsável por adicionar um objeto da
     * <code>PagamentoMovParcelaVO</code> no Hashtable
     * <code>PagamentoMovParcelas</code>. Neste Hashtable são mantidos todos os
     * objetos de PagamentoMovParcela de uma determinada MovPagamento.
     *
     * @param obj Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjPagamentoMovParcelas(PagamentoMovParcelaVO obj) throws Exception {
        getPagamentoMovParcelas().put(obj.getMovParcela().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe
     * <code>PagamentoMovParcelaVO</code> do Hashtable
     * <code>PagamentoMovParcelas</code>. Neste Hashtable são mantidos todos os
     * objetos de PagamentoMovParcela de uma determinada MovPagamento.
     *
     * @param MovParcela Atributo da classe <code>PagamentoMovParcelaVO</code>
     * utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjPagamentoMovParcelas(Integer MovParcela) throws Exception {
        getPagamentoMovParcelas().remove(MovParcela + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>MovPagamentoVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    @Override
    public MovPagamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        MovPagamentoVO eCache = (MovPagamentoVO) obterFromCache(codigoPrm);
        if (eCache != null){
            return eCache;
        }
        String sql = "SELECT * FROM MovPagamento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( MovPagamento ).");
                }
                eCache = (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
        putToCache(eCache);
        return eCache;
    }

    /**
     * Método responsável por inserir em banco a relação entre contrato de
     * evento e movpagamento.
     *
     * @param codigoContratoEvento
     * <AUTHOR>
     * @throws Exception
     */
    @Override
    public void salvarPagamentoContratoEvento(Integer codigoContratoEvento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO negociacaoeventocontratopagamento(contrato, movpagamento)");
        sql.append(" VALUES (?, ?)");
        Integer codigoMovPagamento = obterValorChavePrimariaCodigo();
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, codigoContratoEvento);
        dc.setInt(++i, codigoMovPagamento);
        dc.execute();

    }

    /**
     * Método responsável por obter o registro de MovPagamento referente ao
     * evento indicado pelo código.
     *
     * @param codigoEvento
     * @return pagamento
     * @throws Exception
     */
    @Override
    public MovPagamentoVO obterPorCodigoEvento(Integer codigoEvento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT MP.* FROM movpagamento MP ");
        sql.append("INNER JOIN negociacaoeventocontratopagamento NECP ON NECP.movpagamento = MP.codigo ");
        sql.append("INNER JOIN negociacaoeventocontrato NEC ON NECP.contrato = NEC.codigo ");
        sql.append("WHERE NEC.eventointeresse = ?");
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setInt(1, codigoEvento);
        try (ResultSet tabelaResultado = dc.executeQuery()) {
            if (tabelaResultado.next()) {
                return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, con);
            } else {
                return new MovPagamentoVO();
            }
        }
    }

    public Hashtable getPagamentoMovParcelas() {
        return (pagamentoMovParcelas);
    }

    public void setPagamentoMovParcelas(Hashtable pagamentoMovParcelas) {
        this.pagamentoMovParcelas = pagamentoMovParcelas;
    }

    public Hashtable getPagamentoCheques() {
        return pagamentoCheques;
    }

    public void setPagamentoCheques(Hashtable pagamentoCheques) {
        this.pagamentoCheques = pagamentoCheques;
    }

    public MovimentoContaCorrenteClienteVO getDepositoContaCorrente() {
        return depositoContaCorrente;
    }

    public void setDepositoContaCorrente(MovimentoContaCorrenteClienteVO depositoContaCorrente) {
        this.depositoContaCorrente = depositoContaCorrente;
    }

    public LogVO alterarDataPagamentoCartaoDebito(Integer nrDiasCompensacao, UsuarioVO user, EmpresaVO empresa) throws Exception {
        String sqlCartaoDebitoCount = "SELECT COUNT(1) AS nrRegistros FROM (SELECT * FROM movpagamento WHERE formapagamento IN "
                + "(SELECT codigo FROM formapagamento WHERE tipoformapagamento LIKE 'CD')  AND empresa = " + empresa.getCodigo() + ") AS cont";
        int nrRegistros;
        try (ResultSet rsCount = criarConsulta(sqlCartaoDebitoCount, con)) {
            rsCount.next();
            nrRegistros = rsCount.getInt("nrRegistros");
        }
        String sqlCartaoDebito = "SELECT mov.codigo,mov.datalancamento, fp.compensacaodiasuteis  FROM movpagamento mov inner join formapagamento fp on \n" +
                "fp.codigo = mov.formapagamento  WHERE fp.tipoformapagamento LIKE 'CD'  AND empresa = " + empresa.getCodigo() ;
        try (ResultSet rs = criarConsulta(sqlCartaoDebito, con)) {
            String sqlUpdate = "";
            Date datacompensacao = null;
            while (rs.next()) {
                datacompensacao = Uteis.obterDataFutura2(rs.getDate("datalancamento"), nrDiasCompensacao);
                if (rs.getBoolean("compensacaodiasuteis")) {
                    datacompensacao = obterProximoDiaUtil(datacompensacao, empresa);
                }
                sqlUpdate = "UPDATE movpagamento set datapagamento = '" + Uteis.getDataJDBC(datacompensacao) + "'"
                        + " WHERE codigo = " + rs.getInt("codigo");
                executarConsulta(sqlUpdate, con);

            }
        }


        LogVO log = new LogVO();
        log.setNomeEntidade("MOVPAGAMENTO");
        log.setNomeEntidadeDescricao("MovPagamento");
        log.setResponsavelAlteracao(user.getNome());
        log.setUserOAMD(user.getUserOamd());
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo("Data pagamento");
        log.setValorCampoAlterado("Realizada operação de atualização de data de pagamento, para pagamentos feitos com cartão de débito.<br/>"
                + nrRegistros + " registros atualizados com sucesso em " + Uteis.getDataComHora(Calendario.hoje()));
        return log;


    }

    /**
     * Joao Alcides 10/07/2012
     *
     * @param movPagamento
     * @return
     * @throws SQLException
     * @throws Exception
     */
    public static boolean calcularProdutosDoCheque(int movPagamento, Connection con) throws SQLException, Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(DISTINCT(mp.contrato)) as contratos FROM movproduto mp, movprodutoparcela mpp, pagamentomovparcela pmp \n");
        sql.append("WHERE mp.codigo = mpp.movproduto  \n");
        sql.append("AND mpp.movparcela = pmp.movparcela \n");
        sql.append("AND pmp.movpagamento = " + movPagamento);
        try (ResultSet resultSet = criarConsulta(sql.toString(), con)) {
            if (resultSet.next() && resultSet.getInt("contratos") > 1) {
                return false;
            }
        }
        sql = new StringBuilder();
        sql.append("SELECT con.dividirprodutosnasparcelas FROM movproduto mp, movprodutoparcela mpp, pagamentomovparcela pmp, contrato con \n");
        sql.append("WHERE mp.codigo = mpp.movproduto  \n");
        sql.append("AND mpp.movparcela = pmp.movparcela \n");
        sql.append("AND mp.contrato = con.codigo \n");
        sql.append("AND pmp.movpagamento =  " + movPagamento);
        sql.append(" GROUP BY mp.contrato, con.dividirprodutosnasparcelas  ");
        try (ResultSet resultSet2 = criarConsulta(sql.toString(), con)) {
            if (resultSet2.next() && resultSet2.getBoolean("dividirprodutosnasparcelas")) {
                return false;
            }
        }

        return true;
    }

    public void incluirAutorizacao(int movPagamento, String autorizacao, String nsu) throws Exception {
        if(StringUtils.isNotBlank(nsu)){
            con.prepareStatement(" UPDATE movpagamento SET nsu = '" + nsu.trim() + "' WHERE codigo = " + movPagamento).execute();
        }

        if(StringUtils.isNotBlank(autorizacao)) {
            con.prepareStatement(" UPDATE movpagamento SET autorizacaocartao = '" + autorizacao.trim() + "' WHERE codigo = " + movPagamento).execute();
        }
    }

    public void alterarAdquirente(Integer adquirente, Integer movPagamento) throws Exception {
        if (!UteisValidacao.emptyNumber(movPagamento)) {
            String sql = "UPDATE movpagamento SET adquirente = ? WHERE codigo = ?";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                if (UteisValidacao.emptyNumber(adquirente)) {
                    pst.setNull(1, 0);
                } else {
                    pst.setInt(1, adquirente);
                }
                pst.setInt(2, movPagamento);
                pst.execute();
            }
        }
    }

    public void incluirAutorizacaoDependentes(int movPagamento, String autorizacao, String nsu) throws Exception {
        if(StringUtils.isNotBlank(nsu)){
            con.prepareStatement(" UPDATE movpagamento SET nsu = '" + nsu.trim() + "' WHERE movpagamentoorigemcredito = " + movPagamento).execute();
        }

        if(StringUtils.isNotBlank(autorizacao)) {
            con.prepareStatement(" UPDATE movpagamento SET autorizacaocartao = '" + autorizacao.trim() + "' WHERE movpagamentoorigemcredito = " + movPagamento).execute();
        }
    }

    public void alterarDataCompensacao(int codigo, Date data) throws Exception {
        String sql = "UPDATE movpagamento SET datapagamento = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(data));
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.execute();
        }
    }

    public void alterarDataCompensacaoOrigemCredito(int codigo, Date data) throws Exception {
        String sql = "UPDATE movpagamento SET datapagamento = ? WHERE codigo = ? OR movpagamentoorigemcredito = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(data));
            sqlAlterar.setInt(2, codigo);
            sqlAlterar.setInt(3, codigo);
            sqlAlterar.execute();
        }
    }

    public void setCreditoContaCorrente(MovimentoContaCorrenteClienteVO creditoContaCorrente) {
        this.creditoContaCorrente = creditoContaCorrente;
    }

    public MovimentoContaCorrenteClienteVO getCreditoContaCorrente() {
        return creditoContaCorrente;
    }

    public MovimentoContaCorrenteClienteVO getPagarDebitoCC() {
        return pagarDebitoCC;
    }

    public void setPagarDebitoCC(MovimentoContaCorrenteClienteVO pagarDebitoCC) {
        this.pagarDebitoCC = pagarDebitoCC;
    }

    public void setarProdutosPagos(int codigoRecibo) throws SQLException {
        ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();
        produtosPagosServico.setarProdutosPagos(getCon(), codigoRecibo);

    }

    public MovPagamentoVO atualizarChequeMovimentoCC(MovPagamentoVO mov) throws Exception {
        List novoscheques = new ArrayList<ChequeVO>();
        Iterator i = mov.getChequeVOs().iterator();
        String composicao = "";
        Double restante = mov.getValor();
        ChequeVO aux = null;

        while (i.hasNext()) {
            ChequeVO cheque = (ChequeVO) i.next();
            if (UteisValidacao.emptyNumber(mov.getReciboPagamento().getCodigo())) {
                cheque.setProdutosPagos("");
            }
            if (cheque.getValor() < cheque.getValorTotal()) {
                if (cheque.getSituacao().equals("EA")) {
                    if (aux != null && validaChequeComposicao(cheque, aux)) {
                        composicao = composicao + "," + cheque.getCodigo().toString();
                    } else {
                        aux = (ChequeVO) cheque.getClone(true);
                        composicao = cheque.getCodigo().toString();
                    }
                    if (!UteisValidacao.emptyString(cheque.getComposicao())) {
                        composicao += "," + cheque.getComposicao();
                    }
                }
            } else {
                if (cheque.getSituacao().equals("EA")) {
                    aux = null;
                    composicao = "";
                }
            }
            if (cheque.getSituacao().equals("CA")) {
                restante = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(restante - cheque.getValor());
                if (restante >= 0) {
                    cheque.setSituacao("EA");
                    if (cheque.getValor() < cheque.getValorTotal() && !composicao.equals("")) {
                        cheque.setComposicaoNova(composicao);
                        composicao = "";
                    }
                    novoscheques.add(cheque);
                } else if (cheque.getValor() > (restante * -1)) {
                    ChequeVO novo = (ChequeVO) cheque.getClone(true);
                    cheque.setSituacao("EA");
                    if (cheque.getValor() < cheque.getValorTotal() && !composicao.equals("")) {
                        cheque.setComposicaoNova(composicao);
                        composicao = "";
                    }
                    cheque.setValor(cheque.getValor() + restante);
                    novo.setValor(restante * -1);
                    novoscheques.add(cheque);
                    novoscheques.add(novo);
                } else {
                    novoscheques.add(cheque);
                }
            }
        }

        mov.setChequeVOs(novoscheques);

        return mov;

    }

    public boolean validaChequeComposicao(ChequeVO novo, ChequeVO composicao) {
        if (composicao == null) {
            return false;
        }
        return (novo.getNumero().equals(composicao.getNumero())
                && novo.getConta().equals(composicao.getConta())
                && novo.getAgencia().equals(composicao.getAgencia())
                && novo.getBanco().getCodigo().equals(composicao.getBanco().getCodigo())
                && novo.getDataCompensacao().equals(composicao.getDataCompensacao()));
    }

    public MovPagamentoVO atualizarCartaoMovimentoCC(MovPagamentoVO mov) throws Exception {
        List novoscartaoes = new ArrayList<CartaoCreditoVO>();
        Iterator i = mov.getCartaoCreditoVOs().iterator();
        String composicao = "";
        Double restante = mov.getValor();
        CartaoCreditoVO aux = null;

        while (i.hasNext()) {
            CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
            if (UteisValidacao.emptyNumber(mov.getReciboPagamento().getCodigo())) {
                cartao.setProdutosPagos("");
            }
            if (cartao.getValor() < cartao.getValorTotal()) {
                if (cartao.getSituacao().equals("EA")) {
                    if (aux != null && validaCartaoComposicao(cartao, aux)) {
                        composicao = composicao + "," + cartao.getCodigo().toString();
                    } else {
                        aux = (CartaoCreditoVO) cartao.getClone(true);
                        composicao = cartao.getCodigo().toString();
                    }
                    if (!UteisValidacao.emptyString(cartao.getComposicao())) {
                        composicao += "," + cartao.getComposicao();
                    }
                }
            } else {
                if (cartao.getSituacao().equals("EA")) {
                    aux = null;
                    composicao = "";
                }
            }

            if (cartao.getSituacao().equals("CA")) {
                restante = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(restante - cartao.getValor());
                cartao.setMovpagamento(new MovPagamentoVO());
                if (restante >= 0) {
                    cartao.setSituacao("EA");
                    if (cartao.getValor() < cartao.getValorTotal() && !composicao.equals("")) {
                        cartao.setComposicaoNova(composicao);
                        composicao = "";
                    }
                    novoscartaoes.add(cartao);
                } else if (cartao.getValor() > (restante * -1)) {
                    CartaoCreditoVO novo = (CartaoCreditoVO) cartao.getClone(true);
                    cartao.setSituacao("EA");
                    if (cartao.getValor() < cartao.getValorTotal() && !composicao.equals("")) {
                        cartao.setComposicaoNova(composicao);
                        composicao = "";
                    }
                    cartao.setValor(cartao.getValor() + restante);
                    novo.setValor(restante * -1);
                    novoscartaoes.add(cartao);
                    novoscartaoes.add(novo);
                } else {
                    novoscartaoes.add(cartao);
                }
            }

        }
        mov.setCartaoCreditoVOs(novoscartaoes);
        return mov;

    }

    public boolean validaCartaoComposicao(CartaoCreditoVO novo, CartaoCreditoVO composicao) {
        if (composicao == null) {
            return false;
        }
        return (Uteis.arredondarForcando2CasasDecimais(novo.getValorTotal()) == Uteis.arredondarForcando2CasasDecimais(composicao.getValorTotal())
                && novo.getOperadora().getCodigo().equals(composicao.getOperadora().getCodigo())
                && novo.getDataCompensacao().equals(composicao.getDataCompensacao()));
    }

    public MovPagamentoVO atualizarListaCartaoCredito(MovPagamentoVO mov) throws Exception {
        Double acumulado = 0.0;
        List<CartaoCreditoVO> atualizadas = new ArrayList<CartaoCreditoVO>();
        if(!UteisValidacao.emptyList(mov.getCartoesCanceladoVOs())){
            for (int i = 0; i < mov.getCartoesCanceladoVOs().size(); i++) {
                CartaoCreditoVO cartao = mov.getCartoesCanceladoVOs().get(i);
                acumulado += Uteis.arredondarForcando2CasasDecimais(cartao.getValor());
            }
        }
        for (int i = 0; i < mov.getCartaoCreditoVOs().size(); i++) {
            CartaoCreditoVO cartao = mov.getCartaoCreditoVOs().get(i);
            acumulado += Uteis.arredondarForcando2CasasDecimais(cartao.getValor());
            Double valor =Uteis.arredondarForcando2CasasDecimaisMantendoSinal(mov.getValor() - acumulado);
            if (valor < 0) {

                if (Uteis.arredondarForcando2CasasDecimais(cartao.getValor()) > (valor * -1)) {
                    CartaoCreditoVO cartaoCan = (CartaoCreditoVO) cartao.getClone(true);
                    Double novoValor = Uteis.arredondarForcando2CasasDecimais(cartao.getValor().doubleValue() + valor.doubleValue());
                    cartao.setValor(new Double(novoValor));
                    cartaoCan.setSituacao("CA");
                    cartaoCan.setValor(Uteis.arredondarForcando2CasasDecimais(new Double(cartaoCan.getValor() - novoValor)));
                    atualizadas.add(cartao);
                    atualizadas.add(cartaoCan);

                } else {
                    cartao.setSituacao("CA");
                    atualizadas.add(cartao);
                }
            } else {
                atualizadas.add(cartao);
            }
        }
        mov.setCartaoCreditoVOs(atualizadas);
        return mov;
    }

    public MovPagamentoVO atualizarListaCheques(MovPagamentoVO mov) throws Exception {
        Double acumulado = 0.0;
        List<ChequeVO> atualizados = new ArrayList<ChequeVO>();
        if(!UteisValidacao.emptyList(mov.getChequesCanceladoVOs())){
            for (int i = 0; i < mov.getChequesCanceladoVOs().size(); i++) {
                ChequeVO cheque = mov.getChequesCanceladoVOs().get(i);
                acumulado = Uteis.arredondarForcando2CasasDecimais(acumulado + cheque.getValor());
            }
        }

        for (int i = 0; i < mov.getChequeVOs().size(); i++) {
            ChequeVO cheque = mov.getChequeVOs().get(i);
            acumulado = Uteis.arredondarForcando2CasasDecimais(acumulado + cheque.getValor());
            Double valor = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(mov.getValor() - acumulado);
            if (valor < 0) {
                if (Uteis.arredondarForcando2CasasDecimais(cheque.getValor()) > (valor * -1)) {
                    ChequeVO chequecan = (ChequeVO) cheque.getClone(true);
//	            		ChequeVO atual = (ChequeVO) cheque.getClone(true);
                    Double novoValor = Uteis.arredondarForcando2CasasDecimais(cheque.getValor().doubleValue() + valor.doubleValue());
                    cheque.setValor(new Double(novoValor));
                    chequecan.setSituacao("CA");
                    Double novoValorCan = valor.doubleValue() * -1;
                    chequecan.setValor(Uteis.arredondarForcando2CasasDecimais(new Double(chequecan.getValor() - novoValor)));
                    atualizados.add(cheque);
                    atualizados.add(chequecan);
                } else {
                    cheque.setSituacao("CA");
                    atualizados.add(cheque);
                }
            } else {
                atualizados.add(cheque);
            }
        }
        mov.setChequeVOs(atualizados);
        return mov;
    }

    public MovPagamentoVO retiraChequesCancelados(MovPagamentoVO mov) {
        Iterator i = mov.getChequeVOs().iterator();
        List atualizados = new ArrayList<ChequeVO>();

        while (i.hasNext()) {
            ChequeVO cheque = (ChequeVO) i.next();
            if (!cheque.getSituacao().equals("CA")) {
                atualizados.add(cheque);
            }
        }
        mov.setChequeVOs(atualizados);
        return mov;
    }

    public MovPagamentoVO retiraCartoesCancelados(MovPagamentoVO mov) {
        Iterator i = mov.getCartaoCreditoVOs().iterator();
        List atualizados = new ArrayList<CartaoCreditoVO>();
        while (i.hasNext()) {
            CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
            if (!cartao.getSituacao().equals("CA")) {
                atualizados.add(cartao);
            }

        }
        mov.setCartaoCreditoVOs(atualizados);
        return mov;
    }

    public List<ResumoFormaPagamentoRelatorio> consultarLancamentosFechamentoCaixa(Date inicio, Date fim, int empresa) throws Exception {
        List<MovPagamentoVO> lista = consultarComFiltros(empresa, "", "",
                inicio, fim, "00:00", "23:59",
                null, null, null, null, "",
                null, null, null, null,
                null, null, null,
                null, null, false, false, false,
                null, false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR, null, false,
                null, null, null);
        List<Integer> chequeComposicao = new ArrayList<Integer>();
        List<Integer> cartaoComposicao = new ArrayList<Integer>();
        Map<String, ResumoFormaPagamentoRelatorio> mapa = new HashMap<String, ResumoFormaPagamentoRelatorio>();
        for (MovPagamentoVO mov : lista) {
            if (mov.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
                continue;
            }
            ResumoFormaPagamentoRelatorio get = mapa.get(mov.getFormaPagamento().getDescricao());
            if (get == null) {
                get = new ResumoFormaPagamentoRelatorio();
                get.setValor(0.0);
                get.setFormaPagamento(mov.getFormaPagamento().getDescricao());
                mapa.put(mov.getFormaPagamento().getDescricao(), get);
            }
            if (mov.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
                Cheque chequeDAO = new Cheque(con);
                List<ChequeVO> cheques = chequeDAO.consultarPagamentoCheques(mov.getCodigo(), null,
                        false, false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                chequeDAO = null;

                for (ChequeVO cheque : cheques) {
                    if (cheque.getSituacao().equals("CA")) {
                        continue;
                    }
                    if (cheque.getComposicao() != null && !cheque.getComposicao().equals("")) {
                        Boolean presente = false;	// condição para verificar se algum cheque da composicao já está na lista
                        for (Integer codigo : chequeComposicao) {
                            if (cheque.getCodigo().intValue() == codigo) {
                                presente = true; //cheque já está na lista
                                break;
                            }
                        }
                        if (!presente) { // se cjque não está presente, adicionar na lista os codigos dos cheques que não podem entrar na lista
                            String[] codigos = cheque.getComposicao().split(",");
                            for (String codComposicao : codigos) {
                                chequeComposicao.add(Integer.parseInt(codComposicao));
                            }
                        } else {
                            continue; // cheque já presente na lista, não adicionar
                        }
                    }
                    get.setValor(Uteis.arredondarForcando2CasasDecimais(get.getValor() + cheque.getValorTotal()));
                }
            } else if (mov.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                CartaoCredito cartaoCreditoDAO = new CartaoCredito(con);
                List<CartaoCreditoVO> cartoes = cartaoCreditoDAO.consultarPorMovPagamento(mov.getCodigo(), null, false,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                cartaoCreditoDAO = null;

                for (CartaoCreditoVO cartao : cartoes) {
                    if (cartao.getSituacao().equals("CA")) {
                        continue;
                    }
                    if (cartao.getComposicao() != null && !cartao.getComposicao().equals("")) {
                        Boolean presente = false;
                        for (Integer codigo : cartaoComposicao) {
                            if (cartao.getCodigo().intValue() == codigo) {
                                presente = true;
                                break;
                            }
                        }
                        if (!presente) {
                            String[] codigos = cartao.getComposicao().split(",");
                            for (String codComposicao : codigos) {
                                cartaoComposicao.add(Integer.parseInt(codComposicao));
                            }
                        } else {
                            continue;
                        }
                    }
                    get.setValor(Uteis.arredondarForcando2CasasDecimais(get.getValor() + cartao.getValorTotal()));
                }
            } else {
                get.setValor(Uteis.arredondarForcando2CasasDecimais(get.getValor() + mov.getValor()));
            }
        }
        List<ResumoFormaPagamentoRelatorio> formas = new ArrayList<ResumoFormaPagamentoRelatorio>(mapa.values());
        Ordenacao.ordenarLista(formas, "formaPagamento");
        return formas;
    }

    public void alterarSomenteEntrouNoCaixa(MovPagamentoVO pagamento, Integer movconta) throws Exception {
        String sql = "";
        // Se não tem MovPagamentoOrigemCredito, não é pagamento de Conta Corrente, então segue o fluxo antigo
        // Se tem MovPagamentoOrigemCredito, é pagamento de Conta Corrente, então altera todos os MovPagamentos vinculados a ele
        if (UteisValidacao.emptyNumber(pagamento.getMovPagamentoOrigemCredito())) {
            sql = "UPDATE movpagamento SET movconta = " + movconta + " where codigo = " + pagamento.getCodigo();
        } else {
            sql = "UPDATE movpagamento SET movconta = " + movconta + " WHERE movpagamentoorigemcredito = " + pagamento.getMovPagamentoOrigemCredito();
        }

        executarConsulta(sql, con);
        MovConta movContaDAO = new MovConta(con);
        movContaDAO.gravarMovimentacaoFinanceiraPagamento(pagamento.getCodigo(), movconta);
        movContaDAO = null;
    }

    public void alterarPagamentoRetirandoDoCaixa(Integer movconta, Integer qtdDiasCartaoDtPagamento) throws Exception {
        final StringBuilder  sql =
                new StringBuilder(" UPDATE movpagamento mov ")
                        .append(" SET datapagamento = ")
                        .append(" CASE ")
                        .append(" WHEN f.tipoformapagamento = 'CD' AND mov.datapagamentooriginal IS NOT NULL ")
                        .append(" THEN datapagamentooriginal")
                        .append(" WHEN f.tipoformapagamento = 'CD' ")
                        .append(" THEN ")
                        .append(" dataquitacao + INTERVAL '").append(qtdDiasCartaoDtPagamento).append(" DAYS' ")
                        .append(" WHEN f.tipoformapagamento = 'BB' ")
                        .append(" THEN ")
                        .append(" datapagamento ")
                        .append(" WHEN f.tipoformapagamento = 'PX' AND mov.datapagamentooriginal IS NOT NULL ")
                        .append(" THEN datapagamentooriginal")
                        .append(" ELSE ")
                        .append(" dataquitacao ")
                        .append(" END ")
                        .append(" FROM formapagamento f  ")
                        .append(" WHERE f.codigo = mov.formapagamento ")
                        .append(" AND movconta = ").append(movconta);

        executarConsulta(sql.toString(), con);
    }

    /**
     * @param parcelasDebito the parcelasDebito to set
     */
    public void setParcelasDebito(List<PagamentoMovParcelaVO> parcelasDebito) {
        this.parcelasDebito = parcelasDebito;
    }

    /**
     * @return the parcelasDebito
     */
    public List<PagamentoMovParcelaVO> getParcelasDebito() {
        return parcelasDebito;
    }

    /**
     * @param parcelasCredito the parcelasCredito to set
     */
    public void setParcelasCredito(List<PagamentoMovParcelaVO> parcelasCredito) {
        this.parcelasCredito = parcelasCredito;
    }

    /**
     * @return the parcelasCredito
     */
    public List<PagamentoMovParcelaVO> getParcelasCredito() {
        return parcelasCredito;
    }

    /**
     *
     */
    @Override
    public List<MovPagamentoVO> consultarCreditosDependentes(Integer valorConsulta,
                                                             int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM MovPagamento WHERE movpagamentoorigemcredito = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    public List<MovPagamentoVO> consultarPagamentosEvento(Integer eventoInteresse, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder(" select distinct mp.*, \n");
        sql.append(" (SELECT COUNT(*) FROM cheque WHERE movpagamento = mp.codigo) as nrcheque from movpagamento mp \n");
        sql.append(" inner join pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo \n");
        sql.append(" inner join negociacaoeventocontratoparcelas ne ON pmp.movparcela = ne.parcela \n");
        sql.append(" inner join negociacaoeventocontrato nec ON nec.codigo = ne.contrato \n");
        sql.append(" where nec.eventointeresse = " + eventoInteresse);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true));
            }
        }
    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(rs.getString("cliente").trim().replaceAll("\"", "\'")).append("\",");
                json.append("\"").append(rs.getDate("datapagamento")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
                json.append("\"").append(rs.getString("formapagamento")).append("\",");
                json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valortotal"))).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT mp.codigo, pe.nome AS cliente,\n" + "  mp.datapagamento, fp.descricao AS formapagamento, emp.nome AS empresa, mp.valortotal\n" + "FROM movpagamento mp\n" + "  INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n" + "  INNER JOIN empresa emp ON mp.empresa = emp.codigo\n" + "  INNER JOIN formapagamento fp ON mp.formapagamento = fp.codigo");
        if (empresa != 0) {
            sql.append("  WHERE empresa = ?\n");
        }
        sql.append("  ORDER BY mp.codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                MovPagamentoVO movPagamento = new MovPagamentoVO();
                String geral = rs.getString("codigo") + rs.getString("cliente") + rs.getString("datapagamento") + rs.getString("formapagamento") + rs.getString("empresa") + rs.getString("valortotal");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    movPagamento.setCodigo(rs.getInt("codigo"));
                    movPagamento.setNomePagador(rs.getString("cliente"));
                    movPagamento.setDataPagamento(rs.getTimestamp("datapagamento"));
                    movPagamento.getFormaPagamento().setDescricao(rs.getString("formapagamento"));
                    movPagamento.getEmpresa().setNome(rs.getString("empresa"));
                    movPagamento.setValorTotal(rs.getDouble("valortotal"));
                    lista.add(movPagamento);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Cliente")) {
            Ordenacao.ordenarLista(lista, "nomePagador");
        } else if (campoOrdenacao.equals("Pagamento")) {
            Ordenacao.ordenarLista(lista, "dataPagamento_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Forma de Pagamento")) {
            Ordenacao.ordenarLista(lista, "pagamento_Apresentar");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valorTotal_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public String obterProdutosPagosMovPagamento(Integer movPagamento) throws Exception {
        String sql = "SELECT produtospagos FROM movpagamento WHERE codigo = " + movPagamento;
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getString("produtosPagos");
            }
        }
        return "";
    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit,
                                String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception {



        StringBuilder sqlCount = new StringBuilder("SELECT count(codigo) FROM movpagamento rp");
        if (empresa != 0) {
            sqlCount.append("  WHERE rp.empresa = ").append(empresa).append("\n");
        }

        StringBuilder sql = new StringBuilder("SELECT mp.codigo, pe.nome AS cliente,\n");
        sql.append("  mp.datapagamento, fp.descricao AS formapagamento, emp.nome AS empresa, mp.valortotal\n");
        sql.append(" FROM movpagamento mp  INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n");
        sql.append("  INNER JOIN empresa emp ON mp.empresa = emp.codigo\n");
        sql.append("  INNER JOIN formapagamento fp ON mp.formapagamento = fp.codigo");
        sql.append(" WHERE 1 = 1\n");

        if (empresa != 0) {
            sql.append("  AND mp.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(mp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(pe.nome) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mp.datapagamento::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(emp.nome) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(fp.descricao) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(mp.valortotal::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sql.append(")");
        }
        sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" limit ").append(limit).append("\n");
        }
        sql.append(" offset ").append(offset).append("\n");

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(mp.codigo) \n");
        sqlContarFiltrados.append(" FROM movpagamento mp  INNER JOIN pessoa pe ON mp.pessoa = pe.codigo\n");
        sqlContarFiltrados.append("  INNER JOIN empresa emp ON mp.empresa = emp.codigo\n");
        sqlContarFiltrados.append("  INNER JOIN formapagamento fp ON mp.formapagamento = fp.codigo");
        sqlContarFiltrados.append(" WHERE 1 = 1\n");

        if (empresa != 0) {
            sqlContarFiltrados.append("  AND mp.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(mp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(pe.nome) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mp.datapagamento::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(emp.nome) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(fp.descricao) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(mp.valortotal::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sqlContarFiltrados.append(")");
        }

        StringBuilder json;
        boolean dados;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                json = new StringBuilder();
                json.append("{");
                json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
                json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
                json.append("\"sEcho\":\"").append(sEcho).append("\",");
                json.append("\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(rs.getString("cliente").trim().replaceAll("\"", "\'")).append("\",");
                    json.append("\"").append(rs.getDate("datapagamento")).append("\",");
                    json.append("\"").append(rs.getString("empresa")).append("\",");
                    json.append("\"").append(rs.getString("formapagamento")).append("\",");
                    json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valortotal"))).append("\"],");

                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public void validarPermissaoDataCheque(UsuarioVO usuario, List<MovParcelaVO> listaParcelas,
                                           List<MovPagamentoVO> listaPagamentos) throws Exception {

        Date dataLimite = Calendario.hoje();
        Ordenacao.ordenarLista(listaParcelas, "dataVencimento");
        for (MovParcelaVO parcela : listaParcelas) {
            if (Calendario.maior(dataLimite, parcela.getDataVencimento())) {
                dataLimite = parcela.getDataVencimento();
            }
        }
        Iterator<MovPagamentoVO> i = listaPagamentos.iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = i.next();
            if (movPagamento.getMovPagamentoEscolhida()) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
                    for (ChequeVO cheque : movPagamento.getChequeVOs()) {
                        if (Calendario.maior(dataLimite, cheque.getDataCompensacao())) {
                            try {
                                verificarPermissaoUsuarioFuncionalidade("MovPagamento_AutorizaPagamentoDataRetroativa",
                                        "4.30 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Retroativa");
                            } catch (Exception e) {
                                throw new Exception("Você não possui permissão para inserir um cheque com data retroativa. Permissão : 4.30 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Retroativa");
                            }
                        }
                    }
                }
            }
        }
    }

    public String consultarDescricaoProdutosPagosPlano(String produtosPagos) throws Exception {
        String descricaoProdutos = "";
        String descricaoPlanos = "";
        String descricaoModalidades = "";
        List<Integer> contratos = new ArrayList<Integer>(0);

        if (!UteisValidacao.emptyString(produtosPagos)) {
            String sql = "select pro.codigo,pro.descricao,pla.codigo as  codigoplano, pla.descricao as descricaoplano from produto  pro inner join movproduto mp on mp.produto = pro.codigo left join contrato c on c.codigo = mp.contrato left join plano pla on pla.codigo = c.plano  where mp.codigo = ?";
            List<Integer> produtosAdicionados = new ArrayList<Integer>();
            List<Integer> planosAdicionados = new ArrayList<Integer>();
            List<Integer> modalidadesAdicionados = new ArrayList<Integer>();
            String[] split = produtosPagos.split("\\|");
            for (String str : split) {
                if (!str.isEmpty() && !str.equals("null")) {
                    String[] prodvalor = str.split(",");
                    if (prodvalor.length < 4) {
                        continue;
                    }
                    try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
                        sqlConsultar.setInt(1, Integer.parseInt(prodvalor[0]));
                        try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                            if (tabelaResultado.next()) {
                                if (!produtosAdicionados.contains(tabelaResultado.getInt("codigo"))) {
                                    descricaoProdutos += tabelaResultado.getString("descricao") + ",";
                                    produtosAdicionados.add(tabelaResultado.getInt("codigo"));
                                }
                                if (tabelaResultado.getInt("codigoplano") > 0 && !planosAdicionados.contains(tabelaResultado.getInt("codigoplano"))) {
                                    descricaoPlanos += tabelaResultado.getString("descricaoplano") + ",";
                                    planosAdicionados.add(tabelaResultado.getInt("codigoplano"));
                                }
                                if (tabelaResultado.getInt("codigoplano") > 0 && !contratos.contains(Integer.parseInt(prodvalor[2]))) {
                                    contratos.add(Integer.parseInt(prodvalor[2]));
                                }
                            }
                        }
                    }
                }
            }
            if (!contratos.isEmpty()){
                StringBuilder sqlStr = new StringBuilder("select modalidade.codigo codigomodalidade, modalidade.nome modalidade from contratomodalidade ");
                sqlStr.append("inner join modalidade on modalidade.codigo = contratomodalidade.modalidade ");
                sqlStr.append("where contratomodalidade.contrato in (");
                for (int i=0; i<contratos.size(); i++){
                    Integer codMovPro = (Integer) contratos.get(i);
                    if (i < (contratos.size()-1)){
                        sqlStr.append(codMovPro).append(",");
                    }else {
                        sqlStr.append(codMovPro).append(")");
                    }
                }
                sqlStr.append("group by modalidade.codigo, modalidade.nome");
                try (PreparedStatement sqlQuery = con.prepareStatement(sqlStr.toString())) {
                    try (ResultSet tabelaResultado = sqlQuery.executeQuery()) {
                        while (tabelaResultado.next()) {
                            if (tabelaResultado.getInt("codigomodalidade") > 0 && !modalidadesAdicionados.contains(tabelaResultado.getInt("codigomodalidade"))) {
                                descricaoModalidades += tabelaResultado.getInt("codigomodalidade") + "-" + tabelaResultado.getString("modalidade") + ",";
                                modalidadesAdicionados.add(tabelaResultado.getInt("codigomodalidade"));
                            }
                        }
                    }
                }
                contratos = null;
                sqlStr = null;
            }
        }
        if (descricaoProdutos.length() > 0) {
            descricaoProdutos = descricaoProdutos.substring(0, descricaoProdutos.length() - 1);
        }
        if (descricaoPlanos.length() > 0) {
            descricaoPlanos = descricaoPlanos.substring(0, descricaoPlanos.length() - 1);
        }
        if (descricaoModalidades.length() > 0) {
            descricaoModalidades = descricaoModalidades.substring(0, descricaoModalidades.length() - 1);
        }
        return descricaoProdutos + "?" + descricaoPlanos + "?" + descricaoModalidades;

    }

    public void gerarMovPagamento(String nomeComprador, PessoaVO pessoaVO, MovParcelaVO parcela) throws Exception {
        String nomePagador = UteisValidacao.emptyNumber(pessoaVO.getCodigo()) ?  nomeComprador : pessoaVO.getNome();

        ReciboPagamentoVO recibo = new ReciboPagamentoVO();
        recibo.setValorTotal(parcela.getValorParcela());
        recibo.setPessoaPagador(pessoaVO);
        recibo.setNomePessoaPagador(nomePagador);
        recibo.setResponsavelLancamento(parcela.getResponsavel());
        recibo.setData(parcela.getDataRegistro());
        recibo.setEmpresa(parcela.getEmpresa());
        recibo.setContrato(parcela.getContrato());
        ReciboPagamento reciboPagamento = new ReciboPagamento(this.con);
        reciboPagamento.incluir(recibo);

        MovPagamentoVO pagamento = new MovPagamentoVO();
        pagamento.setResponsavelPagamento(parcela.getResponsavel());
        pagamento.setMovPagamentoEscolhida(true);
        pagamento.setNomePagador(nomePagador);
        pagamento.setEmpresa(parcela.getEmpresa());

        FormaPagamento formaPagamento = new FormaPagamento(this.con);
        pagamento.setFormaPagamento(formaPagamento.consultarAVista());
        pagamento.setValor(parcela.getValorParcela());
        pagamento.setValorTotal(parcela.getValorParcela());
        pagamento.setCredito(false);
        pagamento.setDataPagamento(parcela.getDataRegistro());
        pagamento.setDataLancamento(parcela.getDataRegistro());
        pagamento.setDataQuitacao(parcela.getDataRegistro());
        pagamento.setPessoa(pessoaVO);
        pagamento.setReciboPagamento(recibo);

        PagamentoMovParcelaVO pagamentoMovParcela = new PagamentoMovParcelaVO();
        pagamentoMovParcela.setMovPagamento(pagamento.getCodigo());
        pagamentoMovParcela.setMovParcela(parcela);
        pagamentoMovParcela.setReciboPagamento(recibo);
        pagamentoMovParcela.setValorPago(parcela.getValorParcela());
        pagamentoMovParcela.setUsuarioVO(parcela.getResponsavel());

        pagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcela);

        MovProdutoParcela movProdutoParcela = new MovProdutoParcela(this.con);


        incluirSemCommit(pagamento);
        Iterator i = parcela.getMovProdutoParcelaVOs().iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO movProdutoParcelaVO = (MovProdutoParcelaVO) i.next();
            movProdutoParcelaVO.setReciboPagamento(recibo);
            movProdutoParcela.alterarSomenteReciboPagamentoSemCommit(movProdutoParcelaVO);
        }
        movProdutoParcela = null;
    }

    public void separarChequesCancelados(MovPagamentoVO mov) {
        Iterator i = mov.getChequeVOs().iterator();
        List atualizados = new ArrayList<ChequeVO>();
        List cancelados = new ArrayList<ChequeVO>();

        while (i.hasNext()) {
            ChequeVO cheque = (ChequeVO) i.next();
            if (cheque.getSituacao().equals("EA")) {
                atualizados.add(cheque);
            } else {
                cancelados.add(cheque);
            }
        }
        mov.setChequeVOs(atualizados);
        mov.setChequesCanceladoVOs(cancelados);
    }

    public void separarCartoesCancelados(MovPagamentoVO mov) {
        Iterator i = mov.getCartaoCreditoVOs().iterator();
        List atualizados = new ArrayList<CartaoCreditoVO>();
        List cancelados = new ArrayList<CartaoCreditoVO>();
        while (i.hasNext()) {
            CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
            if (!cartao.getSituacao().equals("CA")) {
                atualizados.add(cartao);
            }else {
                cancelados.add(cartao);
            }
        }
        mov.setCartaoCreditoVOs(atualizados);
        mov.setCartoesCanceladoVOs(cancelados);
    }
    private ResultSet montarSqlPagamentoCliente(Integer pessoa,Integer contrato,boolean count,int limit,int offset) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        if(count){
            sql.append(" COUNT(distinct mp.codigo) as total ");
        } else {
            sql.append(" distinct mp.codigo as movpagamento,mp.credito as creditoCC, \n");
            sql.append(" fp.tipoformapagamento, fp.descricao as forma, \n");
            sql.append(" mp.recibopagamento, mp.nomepagador, mp.empresa, emp.nome, \n");
            sql.append(" mp.datalancamento, mp.datapagamento, mp.valor, mp.valortotal,   p.datanasc, p.nomemae, p.nomepai,\n");
            sql.append(" mp.pessoa, \n");
            sql.append(" CASE \n");
            sql.append(" WHEN fp.tipoformapagamento not in ('CA','CD') THEN 4\n");
            sql.append(" WHEN mp.enviadoConciliadora THEN 2\n");
            sql.append(" WHEN not exists(select codigo from logconciliadora where movpagamento = mp.codigo) THEN 1\n");
            sql.append(" WHEN exists(select codigo from logconciliadora where sucesso = false and movpagamento = mp.codigo) THEN 3\n");
            sql.append(" ELSE 0 END as statusConciliadora \n");
        }
        sql.append(" FROM movpagamento mp \n");
        sql.append(" INNER JOIN recibopagamento rp on rp.codigo = mp.recibopagamento \n");
        sql.append(" INNER JOIN pagamentomovparcela pmp on rp.codigo = pmp.recibopagamento \n");
        sql.append(" INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento \n");
        sql.append(" INNER JOIN movparcela mparc ON pmp.movparcela = mparc.codigo AND mparc.pessoa =  ").append(pessoa).append(" \n");
        sql.append(" LEFT JOIN negociacaoeventocontratopagamento necp ON necp.movpagamento = mp.codigo \n");
        sql.append(" INNER JOIN empresa emp ON mp.empresa = emp.codigo \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mp.pessoa");
        sql.append(" WHERE (");
        sql.append(" (mp.pessoa = ").append(pessoa).append(" AND mp.recibopagamento is not null) ");
        sql.append(" or (mparc.pessoa = ").append(pessoa).append(")) \n");
        sql.append(" AND necp.contrato is null \n");
        if(!UteisValidacao.emptyNumber(contrato)){
            sql.append("and mparc.contrato = ").append(contrato);
        }
        if(!count) {
            sql.append(" ORDER BY mp.codigo DESC");
        }
        if(!count && !UteisValidacao.emptyNumber(limit)){
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs;
    }

    public Integer obterCountPagamentosCliente(Integer pessoa,Integer contrato) throws Exception {
        try (ResultSet rs = montarSqlPagamentoCliente(pessoa, contrato, true, 0, 0)) {
            while (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public List<MovPagamentoVO> consultarTelaCliente(Integer pessoa, Integer contrato, Integer limit,Integer offset) throws Exception{
        List<MovPagamentoVO> lista;
        try (ResultSet rs = montarSqlPagamentoCliente(pessoa, contrato, false, limit, offset)) {
            lista = new ArrayList<MovPagamentoVO>();
            while (rs.next()) {
                MovPagamentoVO movpagamento = new MovPagamentoVO();
                movpagamento.setEmpresa(new EmpresaVO());
                movpagamento.getEmpresa().setCodigo(rs.getInt("empresa"));
                movpagamento.getEmpresa().setNome(rs.getString("nome"));
                movpagamento.setCodigo(rs.getInt("movpagamento"));
                movpagamento.setReciboPagamento(new ReciboPagamentoVO());
                movpagamento.getReciboPagamento().setCodigo(rs.getInt("recibopagamento"));
                movpagamento.setDataLancamento(rs.getTimestamp("datalancamento"));
                movpagamento.setPessoa(new PessoaVO());
                movpagamento.getPessoa().setCodigo(rs.getInt("pessoa"));
                movpagamento.getPessoa().setNome("nome");
                movpagamento.getPessoa().setDataNasc(rs.getDate("datanasc"));
                movpagamento.getPessoa().setNomeMae(rs.getString("nomemae"));
                movpagamento.getPessoa().setNomePai(rs.getString("nomepai"));
                movpagamento.setNomePagador(rs.getString("nomepagador"));
                movpagamento.setValor(rs.getDouble("valor"));
                movpagamento.setValorTotal(rs.getDouble("valortotal"));
                movpagamento.setFormaPagamento(new FormaPagamentoVO());
                movpagamento.setCredito(rs.getBoolean("creditoCC"));
                movpagamento.getFormaPagamento().setDescricao(rs.getString("forma"));
                movpagamento.getFormaPagamento().setTipoFormaPagamento(rs.getString("tipoformapagamento"));
                movpagamento.setStatusConciliadora(StatusPagamentoConciliadora.obterPorCodigo(rs.getInt("statusConciliadora")));
                lista.add(movpagamento);
            }
        }
        return lista;
    }

    @Override
    public Date consultarDataPagamentoByCodigo(int codMovPagamento) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select datapagamento from movpagamento where codigo = " + codMovPagamento);
            try (PreparedStatement sqlQuery = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = sqlQuery.executeQuery()) {
                    if (rs.next()) {
                        return rs.getTimestamp("datapagamento");
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<ItemFaturamentoTO> consultarItensFaturamento(Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select sum(valor) as total, descricao, nrparcelacartaocredito, taxa, tipoformapagamento from\n");
        sql.append("(select distinct movpagamento.*,\n");
        sql.append("fp.descricao as descricao,\n");
        sql.append("fp.taxacartao as taxa,\n");
        sql.append("fp.tipoformapagamento as tipoformapagamento\n");
        sql.append("from movpagamento  \n");
        sql.append("left outer join cartaocredito on movpagamento.codigo = cartaocredito.movpagamento  AND cartaocredito.situacao not like 'CA' \n");
        sql.append("inner join formapagamento fp on movpagamento.formapagamento = fp.codigo and not fp.somentefinanceiro  and fp.descricao like 'CART%'\n");
        sql.append("where movpagamento.empresa = 1 \n");
        sql.append("and movpagamento.codigo not in (select movpagamento from negociacaoeventocontratopagamento)  \n");
        sql.append("and (movpagamento.recibopagamento is not null or movpagamento.credito = 't') \n");
        sql.append("and movpagamento.valor > 0  \n");
        sql.append("and fp.tipoformapagamento in ('CA', 'CD')\n");
        sql.append("and (movpagamento.datalancamento >= '" + Uteis.getDataJDBCTimestamp(dataInicio) + "'  \n");
        sql.append("and movpagamento.datalancamento <= '" + Uteis.getDataJDBCTimestamp(dataFim) + "')) as query\n");

        sql.append("group by descricao, nrparcelacartaocredito, taxa, tipoformapagamento\n");
        sql.append("order by total desc\n");
        try (PreparedStatement sqlQuery = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlQuery.executeQuery()) {
                return montarItensFaturamento(tabelaResultado);
            }
        }
    }

    private List<ItemFaturamentoTO> montarItensFaturamento(ResultSet tabelaResultado) throws Exception {
        List<ItemFaturamentoTO> itens = new ArrayList<ItemFaturamentoTO>();
        ItemFaturamentoTO item;
        while(tabelaResultado.next()) {
            item = ItemFaturamentoFactory.novo(
                    tabelaResultado.getString("tipoformapagamento"),
                    tabelaResultado.getInt("nrparcelacartaocredito"));

            item.setValor(tabelaResultado.getBigDecimal(("total")));
            item.setTaxaPorcentagem(tabelaResultado.getBigDecimal("taxa"));

            if (!tenteAtualizarItem(item, itens)) {
                itens.add(item);
            }

        }

        return itens;
    }

    private boolean tenteAtualizarItem(ItemFaturamentoTO item, List<ItemFaturamentoTO> itens) {
        for (ItemFaturamentoTO itemCorrente : itens) {
            if (itemCorrente.atualizar(item))
                return true;
        }

        return false;
    }

    public boolean existeNotaEmitidaMovPagamentoOrigem(Integer codigoMovPagamentoOrigem) throws Exception {
        String sql = "select exists (select * from nfseemitida  where movpagamento  in (select codigo from movpagamento  where movpagamentoorigemcredito  = ? )) as existe";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigoMovPagamentoOrigem);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    public Integer obterMovContaPagamento(Integer codigoPagamento) throws Exception {
        String sql = "select movconta from movpagamento where codigo = ? ";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigoPagamento);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getInt("movconta");
                }
            }
        }
        return 0;
    }

    @Override
    public Integer incluirPagamentoTotem(List<Integer> parcelas, String tipo, String autorizacao, String totem, String comprovante, Integer codigoEmpresa) throws Exception {
        ConfiguracaoEmpresaTotem configEmpresaDao = new ConfiguracaoEmpresaTotem(con);
        List<TotemTO> totemTOS = configEmpresaDao.obterConfigs(codigoEmpresa, "");

        Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> mapa = new HashMap<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>();
        for(TotemTO t : totemTOS){
            if(t.getTotem().equals(totem) ||  t.getTotem().equals(totem.replace("_", " "))){
                mapa = t.getConfigs();
            }
        }

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();

        TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla(tipo);
        FormaPagamentoVO forma = null;

        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        if(tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)){
            ConfiguracaoEmpresaTotemVO configuracaoEmpresaTotemVO = mapa.get(ConfigTotemEnum.FORMA_PAGAMENTO_CREDITO);
            if(configuracaoEmpresaTotemVO != null){
                forma = formaPagamentoDAO.consultarPorChavePrimaria(configuracaoEmpresaTotemVO.getValorAsInt(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
        }else if(tipoFormaPagto.equals(TipoFormaPagto.CARTAODEBITO)){
            ConfiguracaoEmpresaTotemVO configuracaoEmpresaTotemVO = mapa.get(ConfigTotemEnum.FORMA_PAGAMENTO_DEBITO);
            if(configuracaoEmpresaTotemVO != null){
                forma = formaPagamentoDAO.consultarPorChavePrimaria(configuracaoEmpresaTotemVO.getValorAsInt(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
        }
        if(forma == null){
            forma = formaPagamentoDAO.obterFormaPagamentoRapido(tipoFormaPagto);
        }
        formaPagamentoDAO = null;


        Double valor = 0.0;
        PessoaVO pessoa = null;
        MovParcelaVO parcelaVO = null;
        Integer empresa = 0;
        MovParcela parcelaDao = new MovParcela(con);
        Empresa empresaDao = new Empresa(con);
        MovProdutoParcela produtoParcelaDao = new MovProdutoParcela(con);
        for (Integer parcela : parcelas) {
            MovParcelaVO parc = parcelaDao.consultarPorCodigo(parcela, Uteis.NIVELMONTARDADOS_PARCELA);
            parc.setMovProdutoParcelaVOs(produtoParcelaDao.consultarPorCodigoMovParcela(parc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            pessoa = pessoa == null ? parc.getPessoa() : pessoa;
            parcelaVO = parcelaVO == null ? parc : parcelaVO;
            valor += parc.getValorParcela();

            empresa = parc.getEmpresa().getCodigo();
            listaParcelas.add(parc);
        }

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
        usuarioDAO = null;
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Double multa = parcelaDao.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, Calendario.hoje(),
                false, 1.0, null);
        List<MovParcelaVO> multas = new ArrayList<MovParcelaVO>();
        if (!UteisValidacao.emptyNumber(multa)) {

            // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
            // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
            // Por isso a inclusão desses logs
            if (!UteisValidacao.emptyList(listaParcelas)) {
                String codigosMovParcelas = listaParcelas.stream()
                        .map(p -> String.valueOf(p.getCodigo()))
                        .collect(Collectors.joining(","));
                Uteis.logarDebug("MovPagamento - incluirPagamentoTotem - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
            }

            multas = parcelaDao.criarParcelaMultaJuros(listaParcelas,
                    empresaVO, usuarioVO, Calendario.hoje(), 1.0, null, true);
        }

        listaParcelas.addAll(multas);


        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setFormaPagamento(forma);
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(valor + multa);
        movPagamentoVO.setValorTotal(valor + multa);
        movPagamentoVO.setPessoa(pessoa);
        movPagamentoVO.setObservacao("Pagamento Linx Totem - " + totem);
        if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)) {
            movPagamentoVO.setNrParcelaCartaoCredito(1);
        }

        OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);
        List<OperadoraCartaoVO> operadoras = operadoraCartaoDAO.consultarPorDescricao("MASTERCARD", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        operadoraCartaoDAO = null;

        if (operadoras != null && !operadoras.isEmpty()) {
            movPagamentoVO.setOperadoraCartaoVO(operadoras.get(0));
        }
        movPagamentoVO.setNomePagador(pessoa.getNome());
        movPagamentoVO.setResponsavelPagamento(usuarioVO);
        movPagamentoVO.setPessoa(parcelaVO.getPessoa());
        movPagamentoVO.setEmpresa(parcelaVO.getEmpresa());
        try {
            movPagamentoVO.setRespostaRequisicaoPinpad(comprovante);
            JSONObject jsonObject = new JSONObject(comprovante);
            String codigoAutorizacao = jsonObject.optString("acquirerAuthorizationCode");
            movPagamentoVO.setAutorizacaoCartao(codigoAutorizacao);
            String codigoNSU = jsonObject.optString("administrativeCode");
            movPagamentoVO.setNsu(codigoNSU);
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao obter autorização do cartão Linx: " + e.getMessage());
        }

        listaPagamento.add(movPagamentoVO);

        Uteis.logarDebug("incluirPagamentoTotem - Tipo Forma Pagamento: " + movPagamentoVO.getFormaPagamento().getTipoFormaPagamento());
        Uteis.logarDebug("incluirPagamentoTotem - Nr parcelas cartao credito: " + movPagamentoVO.getNrParcelaCartaoCredito());

        MovPagamentoVO.validarDados(movPagamentoVO);

        Uteis.logarDebug("incluirPagamentoTotem - Vai acessar incluirListaPagamento - Tamanho listaParcelas: " + listaParcelas.size());
        Uteis.logarDebug("incluirPagamentoTotem - Vai acessar incluirListaPagamento - Comprovante: " + comprovante);
        ReciboPagamentoVO reciboObj = incluirListaPagamento(
                listaPagamento,
                listaParcelas,
                null,
                parcelaVO.getContrato(),
                false, 0.0);
        return reciboObj.getCodigo();

    }


    public List<MovPagamentoVO> consultarPagamentosItemExtrato(ExtratoDiarioItemVO item) throws Exception {
        String sql = "SELECT * "
                + " FROM movpagamento mp "
                + " WHERE mp.valor > 0 AND formapagamento = "+item.getFormaPagamentoVO().getCodigo();

        //venda consumidor não tem pessoa
        if (!UteisValidacao.emptyNumber(item.getMovPagamento().getPessoa().getCodigo())) {
            sql += " AND pessoa = " + item.getMovPagamento().getPessoa().getCodigo() + " \n";
        }

        if (item.getCodigoMovPagamentoExtratoDiario() > 0) {
            sql += " AND codigo = " + item.getCodigoMovPagamentoExtratoDiario();
        } else if (item.getCodigoMovPagamento() > 0) {
            sql += " AND codigo = " + item.getCodigoMovPagamento();
        }
        if(item.getTipoRegistro().equals("05") && !UteisValidacao.emptyString(item.getNsu())){
            sql += " and  trim(leading '0' from nsu) = trim(leading '0' from '" + item.getNsu() + "')" ;
        } else {
            sql += " and  (autorizacaocartao ilike '" + item.getAutorizacao().trim() + "' ";
            if (!UteisValidacao.emptyString(item.getNsu())) {
                sql += " or nsu = '" + item.getNsu() + "' ";
            }
            sql += ") ";
        }
        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        try (ResultSet rs = criarConsulta(sql, con)) {
            while (rs.next()) {
                MovPagamentoVO pagamento = new MovPagamentoVO();
                pagamento.setCodigo(rs.getInt("codigo"));
                item.getMovPagamento().getPessoa().setCodigo(rs.getInt("pessoa"));
                try {
                    pagamento.setMovconta(rs.getInt("movconta"));
                } catch (Exception e) {
                }
                pagamento.setFormaPagamento(item.getFormaPagamentoVO());
                if (!UteisValidacao.emptyNumber(rs.getInt("movpagamentoorigemcredito"))) {
                    String sqlValorTotalCC = "SELECT valortotal, codigo FROM movpagamento mp WHERE codigo = " + rs.getInt("movpagamentoorigemcredito") + ";";
                    try (ResultSet rsValorTotalCC = criarConsulta(sqlValorTotalCC, con)) {
                        while (rsValorTotalCC.next()) {
                            pagamento.setValor(rsValorTotalCC.getDouble("valortotal"));
                            pagamento.setMovPagamentoOrigemCredito(rsValorTotalCC.getInt("codigo"));
                        }
                    } catch (Exception e) {
                    }
                } else {
                    pagamento.setValor(rs.getDouble("valor"));
                }
                pagamento.setDataPagamento(rs.getTimestamp("datapagamento"));
                pagamento.setDataPagamentoOriginal(rs.getTimestamp("datapagamentoOriginal"));
                pagamento.setDataLancamento(rs.getTimestamp("datalancamento"));
                pagamento.setAutorizacaoCartao(rs.getString("autorizacaocartao"));

                // Montar Adquirente
                if(!UteisValidacao.emptyNumber(rs.getInt("adquirente"))){
                    pagamento.setAdquirenteVO(new AdquirenteVO());
                    Adquirente adquirenteDAO = new Adquirente(con);
                    pagamento.setAdquirenteVO(adquirenteDAO.consultarPorCodigo(rs.getInt("adquirente")));
                    adquirenteDAO = null;
                }

                // Montar Operadora Cartão
                if(!UteisValidacao.emptyNumber(rs.getInt("operadoracartao"))){
                    pagamento.setOperadoraCartaoVO(new OperadoraCartaoVO());
                    OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);
                    pagamento.setOperadoraCartaoVO(operadoraCartaoDAO.consultarPorChavePrimaria(rs.getInt("operadoracartao"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    operadoraCartaoDAO = null;
                }

                listaPagamento.add(pagamento);
            }
        }

        return listaPagamento;
    }

    public void preencherDataPagamentoOriginalCartaoDebito(Integer nrDiasCompensacao, EmpresaVO empresa) throws Exception {
        String sqlCartaoDebito = "SELECT mov.codigo,mov.datalancamento, fp.compensacaodiasuteis  FROM movpagamento mov inner join formapagamento fp on \n" +
                "fp.codigo = mov.formapagamento  WHERE fp.tipoformapagamento LIKE 'CD'  AND empresa = " + empresa.getCodigo() ;
        try (ResultSet rs = criarConsulta(sqlCartaoDebito, con)) {
            String sqlUpdate = "";
            Date datacompensacao = null;
            while (rs.next()) {
                datacompensacao = Uteis.obterDataFutura2(rs.getDate("datalancamento"), nrDiasCompensacao);
                if (rs.getBoolean("compensacaodiasuteis")) {
                    datacompensacao = obterProximoDiaUtil(datacompensacao, empresa);
                }
                sqlUpdate = "UPDATE movpagamento set datapagamentoOriginal = '" + Uteis.getDataJDBC(datacompensacao) + "'"
                        + " WHERE codigo = " + rs.getInt("codigo");
                executarConsulta(sqlUpdate, con);

            }
        }

    }

    public List<MovPagamentoVO> consultarSimplificadoCliente(Integer cliente) throws Exception{
        List<MovPagamentoVO> lista = new ArrayList<MovPagamentoVO>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select recibopagamento, datapagamento, datalancamento, fp.tipoformapagamento, mp.valor  from movpagamento mp \n");
        sql.append(" inner join formapagamento fp on fp.codigo = mp.formapagamento ");
        sql.append(" inner join cliente cli on cli.pessoa = mp.pessoa");
        sql.append(" where cli.codigo = ").append(cliente).append(" ORDER BY datapagamento DESC ");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                MovPagamentoVO movPgto = new MovPagamentoVO();
                movPgto.setDataPagamento(rs.getDate("datapagamento"));
                movPgto.setDataLancamento(rs.getDate("datalancamento"));
                movPgto.setValor(rs.getDouble("valor"));
                movPgto.setReciboPagamento(new ReciboPagamentoVO());
                movPgto.getReciboPagamento().setCodigo(rs.getInt("recibopagamento"));

                movPgto.setFormaPagamento(new FormaPagamentoVO());
                movPgto.getFormaPagamento().setTipoFormaPagamento(rs.getString("tipoformapagamento"));
                lista.add(movPgto);
            }
        }
        return lista;
    }

    @Override
    public List<MovPagamentoVO> consultarPorMovConta(Integer codigoMovConta) throws Exception {
        final StringBuilder sql =
                new StringBuilder(" SELECT mp.*                                 ")
                        .append("   FROM movpagamento mp                        ")
                        .append("   WHERE movconta = ").append(codigoMovConta);

        final List<MovPagamentoVO> movPagamentos;
        try (ResultSet dadosTabela = criarConsulta(sql.toString(), con)) {
            movPagamentos = new ArrayList<MovPagamentoVO>();
            while (dadosTabela.next()) {
                movPagamentos.add(montarDados(dadosTabela, Uteis.NIVELMONTARDADOS_TELACONSULTA, con));
            }
        }
        return movPagamentos;
    }

    @Override
    public List<MovPagamentoVO> consultarPorMovConta(Integer codigoMovConta, int limit, int offset) throws Exception {
        final String sql = getMovPagamentoByMovContaSQL(false, codigoMovConta, limit, offset);

        final List<MovPagamentoVO> movPagamentos;
        try (ResultSet dadosTabela = criarConsulta(sql.toString(), con)) {
            movPagamentos = new ArrayList<MovPagamentoVO>();
            while (dadosTabela.next()) {
                movPagamentos.add(montarDados(dadosTabela, Uteis.NIVELMONTARDADOS_TODOS, con));
            }
        }
        return movPagamentos;
    }

    @Override
    public Integer consultarPorMovContaCount(Integer codigoMovConta, int limit, int offset) throws Exception {
        final String sql = getMovPagamentoByMovContaSQL(true, codigoMovConta, limit, offset);
        final List<MovPagamentoVO> movPagamentos;
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getInt("qtd");
            } else {
                return 0;
            }
        }
    }

    private String getMovPagamentoByMovContaSQL(boolean count, Integer movConta, int limit, int offset) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT \n");
        if (count) {
            sql.append(" COUNT(DISTINCT(mp.codigo)) as qtd \n");
        } else {
            sql.append("  DISTINCT mp.*, cli.matricula, p.cfp as cpfPagador, con.descricao as descricaoconta, movc.dataquitacao as datamovconta\n");
        }
        sql.append(" FROM movpagamento mp \n");
        sql.append(" left join pessoa p on p.codigo = mp.pessoa \n");
        sql.append(" left join cliente cli on cli.pessoa = p.codigo \n");
        sql.append(" left join movconta movc on movc.codigo = mp.movconta \n");
        sql.append(" left join conta con on con.codigo = movc.conta \n");
        sql.append(" WHERE movconta = ").append(movConta).append(" \n");
        if (!count) {
            sql.append("ORDER BY mp.codigo \n");
            sql.append("LIMIT ").append(limit).append(" \n");
            sql.append("OFFSET ").append(offset).append(" \n");
        }
        return sql.toString();
    }

    public void carregarCentroCusto(List<MovPagamentoVO> listaMovPagamentoVO) throws Exception{
        if (listaMovPagamentoVO == null || listaMovPagamentoVO.isEmpty()) {
            return;
        }
        StringBuilder codigoMovimentacoes = new StringBuilder();
        for (MovPagamentoVO movPagamentoVO : listaMovPagamentoVO) {
            if (movPagamentoVO.getCodigo() > 0) {
                codigoMovimentacoes.append("," + movPagamentoVO.getCodigo());
            }
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.codigo, rimod.centrocusto as centro_custo_modalidade, riprod.centrocusto as centro_custo_prod ");
        sql.append("FROM movpagamento mp ");
        sql.append("INNER JOIN recibopagamento rp ON rp.codigo = mp.recibopagamento ");
        sql.append("INNER JOIN movprodutoparcela mpp ON mpp.recibopagamento = rp.codigo ");
        sql.append("INNER JOIN movproduto mprod ON mprod.codigo = mpp.movproduto ");
        sql.append("LEFT JOIN produto prod ON mprod.produto = prod.codigo ");
        sql.append("LEFT JOIN movprodutomodalidade mpm ON mpm.movproduto = mprod.codigo ");
        sql.append("LEFT JOIN modalidade mod ON mod.codigo = mpm.modalidade ");
        sql.append("LEFT JOIN rateiointegracao riprod ON riprod.produto = prod.codigo OR prod.categoriaproduto = riprod.categoriaproduto ");
        sql.append("LEFT JOIN rateiointegracao rimod ON rimod.modalidade = mod.codigo ");
        sql.append("WHERE (riprod.centrocusto IS NOT NULL OR rimod.centrocusto IS NOT NULL) ");
        sql.append("AND mp.codigo IN (" + codigoMovimentacoes.toString().replaceFirst(",", "") + ") ");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            Map<Integer, Set<Integer>> mapaMovPagamentoCentroCusto = new HashMap<Integer, Set<Integer>>();
            while (rs.next()) {
                Integer codigoMovPagamento = rs.getInt("codigo");
                Integer codigoCentroCustoModalidade = rs.getInt("centro_custo_modalidade");
                Integer codigoCentroCustoProd = rs.getInt("centro_custo_prod");
                Set<Integer> centrosCusto = mapaMovPagamentoCentroCusto.get(codigoMovPagamento);
                if (centrosCusto == null) {
                    centrosCusto = new HashSet<Integer>();
                    mapaMovPagamentoCentroCusto.put(codigoMovPagamento, centrosCusto);
                }
                if (codigoCentroCustoModalidade > 0) {
                    centrosCusto.add(codigoCentroCustoModalidade);
                } else if (codigoCentroCustoProd > 0) {
                    centrosCusto.add(codigoCentroCustoProd);
                }
            }
            for (MovPagamentoVO movPagamentoVO : listaMovPagamentoVO) {
                StringBuilder centrosCusto = new StringBuilder("");
                for (Integer codigoCentroCusto : mapaMovPagamentoCentroCusto.get(movPagamentoVO.getCodigo())) {
                    centrosCusto.append("," + codigoCentroCusto);
                }
                if (centrosCusto.toString().isEmpty()) {
                    continue;
                }
                StringBuilder sqlCentroCusto = new StringBuilder();
                sqlCentroCusto.append("SELECT cc.nome, cc.codigo FROM centrocusto cc ");
                sqlCentroCusto.append("WHERE cc.codigo IN (" + centrosCusto.toString().replaceFirst(",", "") + " )");
                try (ResultSet rs2 = criarConsulta(sqlCentroCusto.toString(), con)) {
                    while (rs2.next()) {
                        CentroCustoTO centroCustoTO = new CentroCustoTO();
                        centroCustoTO.setCodigo(rs2.getInt("codigo"));
                        centroCustoTO.setDescricao(rs2.getString("nome"));
                        movPagamentoVO.getListaCentroCusto().add(centroCustoTO);
                    }
                }
            }
        }
    }

    public void ajustarMovContaPagamentoCC(MovPagamentoVO pagamento) throws Exception {
        if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())){
            String sql = "UPDATE movpagamento SET movconta = null where codigo = " + pagamento.getCodigo();
            executarConsulta(sql,con);
        }
    }

    @Override
    public void gerarPontosParceiroFidelidade(final int codigoRecibo) throws Exception {
        ParceiroFidelidadeZW parceiroFidelidadeZW = new ParceiroFidelidadeZW(con);
        parceiroFidelidadeZW.processarPontos(codigoRecibo, false);
        parceiroFidelidadeZW = null;
    }

    public void marcarParceiroFidelidadeProcessado(boolean processado, Integer movPagamento) throws Exception {
        String sql = "UPDATE movPagamento set parceiroFidelidadeProcessado = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, processado);
            ps.setInt(2, movPagamento);
            ps.executeUpdate();
        }
    }

    public void alterarNSU(String nsu, Integer movPagamento) throws Exception {
        if (!UteisValidacao.emptyNumber(movPagamento)) {
            String sql = "UPDATE movPagamento set nsu = ? WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setString(1, nsu);
                ps.setInt(2, movPagamento);
                ps.executeUpdate();
            }
        }
    }

    public void alterarNSUeCodAutorizacao(String codigoNSU, String autorizacaoCartao, int codMovpagamento) throws Exception {
        if (!UteisValidacao.emptyNumber(codMovpagamento)) {
            String sql = "UPDATE movPagamento set nsu = ?, autorizacaoCartao = ? WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setString(1, codigoNSU);
                ps.setString(2, autorizacaoCartao);
                ps.setInt(3, codMovpagamento);
                ps.executeUpdate();
            }
        }
    }

    public void liberarCobrancaBaixaProtheus(String recno) throws Exception {
        try (ResultSet rs = criarConsulta("select codigo, descricao from movparcela where recno = '" + recno + "'", con)) {
            while (rs.next()) {
                String descricao = rs.getString("descricao");
                Integer codigo = rs.getInt("codigo");
                if (descricao.contains("BORDERÔ")) {
                    descricao = descricao.replace(" - BORDERÔ", "");
                    executarConsulta("update movparcela set descricao = '"
                            + descricao + "' where codigo = " + codigo, con);
                }

                executarConsulta("delete from remessaitem where movparcela = " + rs.getInt("codigo"), con);
            }
        }
    }

    public Integer incluirBaixaProtheus(String recno, String numerotitulo) throws Exception {

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();

        TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla("CA");
        FormaPagamentoVO forma = new FormaPagamentoVO();
        forma.setDescricao("DEBITO EM CONTA CORRENTE");
        forma.setDefaultRecorrencia(false);
        forma.setDefaultDCO(true);

        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        forma = formaPagamentoDAO.criarOuConsultarSeExistePorFlag(forma);
        formaPagamentoDAO = null;

        Double valor = 0.0;
        PessoaVO pessoa = null;
        MovParcelaVO parcelaVO = null;
        Integer empresa = 0;
        MovParcela parcelaDao = new MovParcela(con);
        Empresa empresaDao = new Empresa(con);
        MovProdutoParcela produtoParcelaDao = new MovProdutoParcela(con);

        try (ResultSet rs = criarConsulta("select codigo from movparcela where recno = '" + recno + "'", con)) {
            while (rs.next()) {
                MovParcelaVO parc = parcelaDao.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_PARCELA);
                parc.setMovProdutoParcelaVOs(produtoParcelaDao.consultarPorCodigoMovParcela(parc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                pessoa = pessoa == null ? parc.getPessoa() : pessoa;
                parcelaVO = parcelaVO == null ? parc : parcelaVO;
                valor += parc.getValorParcela();

                empresa = parc.getEmpresa().getCodigo();
                listaParcelas.add(parc);
            }
        }

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
        usuarioDAO = null;

        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setFormaPagamento(forma);
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(valor);
        movPagamentoVO.setValorTotal(valor);
        movPagamentoVO.setPessoa(pessoa);
        movPagamentoVO.setObservacao(numerotitulo);
        movPagamentoVO.setNomePagador(pessoa.getNome());
        movPagamentoVO.setResponsavelPagamento(usuarioVO);
        movPagamentoVO.setPessoa(parcelaVO.getPessoa());
        movPagamentoVO.setEmpresa(parcelaVO.getEmpresa());

        listaPagamento.add(movPagamentoVO);

        MovPagamentoVO.validarDados(movPagamentoVO);

        ReciboPagamentoVO reciboObj = incluirListaPagamento(
                listaPagamento,
                listaParcelas,
                null,
                parcelaVO.getContrato(),
                false, 0.0);

        for(MovParcelaVO mp : listaParcelas){
            for(MovProdutoParcelaVO mpp : mp.getMovProdutoParcelaVOs()){
                executarConsulta("update movproduto set statusprotheus = 'BAIXADO' WHERE codigo = " + mpp.getMovProdutoVO().getCodigo(), con);
            }
        }
        for(MovParcelaVO mp : listaParcelas){
            executarConsulta("delete from remessaitem where movparcela = "+mp.getCodigo(), con);
        }
        return reciboObj.getCodigo();

    }

    public List<MovPagamentoVO> consultarSQL(String sql, int nivelMontarDados) throws Exception {
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con, true);
            }
        }
    }

    public void marcarEnviadoConciliadora(boolean enviado, Integer movPagamento) throws Exception {
        String sql = "UPDATE movPagamento set enviadoConciliadora = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, enviado);
            ps.setInt(2, movPagamento);
            ps.executeUpdate();
        }
    }

    public boolean existeMovPagamentoFormaPagamento(Integer codigoFormaPagamento) throws Exception {
        String sql = "select exists (select codigo from movpagamento  where formapagamento = ? limit 1) as existe";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codigoFormaPagamento);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                tabelaResultado.next();
                return tabelaResultado.getBoolean("existe");
            }
        }
    }

    //Método responsável por realizar o update na compensação de cada boleto
    public Integer acaoAtualizarDiasCompensacaoBoleto(Integer diasCompensacaoBoleto, Integer codigoConvenio, boolean atualizar) throws Exception {
        HashMap<Integer, Date> dataPagamento = consultarRecebiveisBoleto(codigoConvenio);
        if (atualizar) {
            String sql = "UPDATE movPagamento set datapagamento = ? WHERE codigo = ?;";
            con.setAutoCommit(false);
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                for (Map.Entry<Integer, Date> entry : dataPagamento.entrySet()) {
                    Integer key = entry.getKey();
                    Date data = entry.getValue();
                    ps.setDate(1, Uteis.getDataJDBC(Uteis.somarDias(data, diasCompensacaoBoleto)));
                    ps.setInt(2, key);
                    ps.executeUpdate();
                }
                con.commit();
            } catch (Exception e) {
                con.rollback();
                Uteis.logar(e, MovPagamento.class);
                return null;
            } finally {
                con.setAutoCommit(true);
            }
        }
        return dataPagamento.values().size();
    }

    //Método responsável por entregar a consulta dos boletos do convênio
    private HashMap<Integer, Date> consultarRecebiveisBoleto(Integer codigoConvenio) throws Exception {
        HashMap<Integer, Date> boletos = new HashMap<>();
        StringBuilder query = new StringBuilder();
        query.append("select m.codigo,m.datalancamento from movpagamento m \n");
        query.append("inner join formapagamento f on m.formapagamento = f.codigo \n");
        query.append("where f.tipoformapagamento = 'BB'\n");
        query.append("and m.conveniocobranca = " + codigoConvenio);
        PreparedStatement stm = con.prepareStatement(query.toString());
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            boletos.put(rs.getInt("codigo"), rs.getDate("datalancamento"));
        }
        return boletos;
    }

    public void alterarConvenioCobranca(Integer convenioCobranca, Integer movPagamento) throws Exception {
        if (!UteisValidacao.emptyNumber(movPagamento)) {
            String sql = "UPDATE movPagamento set conveniocobranca = ? WHERE codigo = ?";
            PreparedStatement ps = con.prepareStatement(sql);
            resolveIntegerNull(ps, 1, convenioCobranca);
            ps.setInt(2, movPagamento);
            ps.executeUpdate();
        }
    }

    /**
     * Responsável por consultar pagamentos com alteracao manual por intervalo
     * de datas e empresa como parametro
     *
     * <AUTHOR> 07/11/2011
     * @throws Exception
     */
    @Override
    public ResultSet consultarMovimentacoesRecebiveisIntegracao(Date lancamentoInicio, Date lancamentoFim, int empresa, String matricula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql
                .append("SELECT \n")
                .append("movPag.codigo, movPag.nomepagador,usu.codigo as codresponsavelpagamento, \n")
                .append("usu.nome as responsavelpagamento, movPag.recibopagamento, movPag.datalancamento, movPag.formapagamento, \n")
                .append("(CASE WHEN cc.codigo is not null THEN cc.produtospagos WHEN ch.produtospagos is not null THEN ch.produtospagos ELSE movPag.produtospagos END) produtosPagos, \n")
                .append("(CASE WHEN cc.valortotal > 0.0 THEN cc.valortotal WHEN ch.valortotal > 0.0 THEN ch.valortotal ELSE movPag.valortotal END) valor, \n")
                .append("movPag.autorizacaocartao, formaPag.tipoformapagamento,formaPag.descricao descricaoformapagamento, \n")
                .append("pes.codigo as codpessoa, pes.nome as nomepessoa, pes.cfp as cpfpessoa, \n")
                .append("cc.codigo codcartao, cc.dataCompesancao dataCompensacaoCartao, cc.composicao composicaocartao, \n")
                .append("ch.codigo codcheque, ch.dataCompesancao dataCompensacaoCheque, ch.composicao composicaocheque, \n")
                .append("emp.codigo as codempresa, emp.nome as nomeempresa, emp.cnpj as cnpjempresa \n")
                .append("FROM MovPagamento movPag \n")
                .append("INNER JOIN formaPagamento formaPag on formaPag.codigo = movPag.formapagamento \n")
                .append("LEFT JOIN movProdutoParcela movProdParc on movProdParc.recibopagamento = movPag.recibopagamento \n")
                .append("LEFT JOIN MovProduto movProd on movProd.codigo = movProdParc.movproduto \n")
                .append("LEFT JOIN Pessoa pes on pes.codigo = movPag.pessoa \n")
                .append("LEFT JOIN cliente cli on cli.pessoa = pes.codigo \n")
                .append("LEFT JOIN cartaocredito cc ON cc.movpagamento = movPag.codigo \n")
                .append("LEFT JOIN cheque ch ON ch.movpagamento = movPag.codigo \n")
                .append("INNER JOIN usuario usu ON usu.codigo = movPag.responsavelpagamento \n")
                .append("INNER JOIN empresa emp ON emp.codigo = movPag.empresa \n")
                .append("WHERE formaPag.tipoFormaPagamento <> 'CC'  and emp.codigo = "+empresa+" and movPag.valor > 0  and movPag.recibopagamento is not null  ")
                .append("and (movPag.datalancamento BETWEEN '"+ Uteis.getDataJDBC(lancamentoInicio) + " 00:00:00' AND  '" + Uteis.getDataJDBC(lancamentoFim) + " 23:59:59') \n");

        if (!UteisValidacao.emptyString(matricula) && Uteis.isNumeroValido(matricula)) {
            sql.append(" and cli.matricula like '" + matricula + "' \n");
        }

        sql
                .append("GROUP BY movPag.codigo, formaPag.codigo, pes.codigo, cc.codigo, ch.codigo, usu.codigo, emp.codigo \n")
                .append("ORDER BY movPag.codigo, cc.codigo, ch.codigo \n");

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return rs;
    }

    public ReciboPagamentoVO gravarPagamento(PagamentoDTO pagamentoDTO, Boolean pactoApp, Boolean controlarTransacao, boolean pagarVencida) throws Exception {

        if(pagamentoDTO.getFormasPagamento() == null || pagamentoDTO.getFormasPagamento().isEmpty()){
            throw new ServiceException("Nenhuma forma de pagamento foi informada");
        }

        Contrato contrato = new Contrato(getCon());
        MovParcela movParcela = new MovParcela(getCon());
        FormaPagamento formaPagamento = new FormaPagamento(getCon());
        Usuario usuario = new Usuario(getCon());
        Cliente cliente = new Cliente(getCon());
        Adquirente adquirente = new Adquirente(getCon());
        VendaAvulsa vendaAvulsa = new VendaAvulsa(getCon());
        AulaAvulsaDiaria aulaAvulsaDiaria = new AulaAvulsaDiaria(getCon());
        boolean isVendaAvulsa = (pagamentoDTO.getContrato() == 0 && pagamentoDTO.getVendaAvulsa() > 0 && pagamentoDTO.getAulaAvulsa() == 0);
        boolean isAulaAvulsa = (pagamentoDTO.getContrato() == 0 && pagamentoDTO.getVendaAvulsa() == 0 && pagamentoDTO.getAulaAvulsa() > 0);

        ContratoVO contratoVO = null;
        ClienteVO clienteVO = null;
        VendaAvulsaVO vendaAvulsaVO = null;
        AulaAvulsaDiariaVO aulaAvulsaDiariaVO = null;
        List<MovParcelaVO> movparcelas = new ArrayList<>();

        if(!isVendaAvulsa && !isAulaAvulsa){
            contratoVO = contrato.consultarPorCodigo(pagamentoDTO.getContrato(), Uteis.NIVELMONTARDADOS_TODOS);
            if(contratoVO == null) {
                throw new ServiceException("Contrato não encontrado");
            }
            clienteVO = cliente.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(pactoApp) {
                if(!(pagamentoDTO.getParcelasContrato().size() > 0)) {
                    throw new ServiceException("Para o pagamento parcial, é necessário informar quais parcelas serão pagas.");
                }
                for(Integer parcela : pagamentoDTO.getParcelasContrato()) {
                    MovParcelaVO movParcelaVO = movParcela.consultarPorCodigo(parcela, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if(movParcelaVO != null && (pagarVencida || Calendario.maiorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje())))  {
                        movparcelas.add(movParcelaVO);
                    } else {
                        throw new ServiceException("Não foi possível realizar o pagamento devido a parcela " + parcela + "! Verifique se o código está correto e a data limite de pagamento." );
                    }
                }
            } else if(pagamentoDTO.getPagamentoIntegral()) {
                movparcelas = movParcela.consultarEmAbertoPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                movparcelas = movParcela.consultarParcelasPagarContrato(contratoVO);
            }
        } else if(isVendaAvulsa){
            vendaAvulsaVO = vendaAvulsa.consultarPorChavePrimaria(pagamentoDTO.getVendaAvulsa(), Uteis.NIVELMONTARDADOS_TODOS);
        } else if(isAulaAvulsa) {
            aulaAvulsaDiariaVO = aulaAvulsaDiaria.consultarPorChavePrimaria(pagamentoDTO.getAulaAvulsa(), Uteis.NIVELMONTARDADOS_TODOS);
        }

        UsuarioVO usuarioVO = new UsuarioVO();

        try {
            usuarioVO = usuario.consultarPorChavePrimaria(pagamentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        if(isVendaAvulsa){
            if(pactoApp && !pagamentoDTO.getParcelasVendaAvulsa().isEmpty()) {
                for(Integer cod : pagamentoDTO.getParcelasVendaAvulsa()) {
                    movparcelas.add(movParcela.consultarPorCodigo(cod, Uteis.NIVELMONTARDADOS_MINIMOS));
                }
            } else {
                movparcelas.addAll(movParcela.consultarPorCodigoVendaAvulsaLista(pagamentoDTO.getVendaAvulsa(),null, null,"EA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } else if (isAulaAvulsa) {
            movparcelas.addAll(movParcela.consultarPorCodigoAulaAvulsaDiariaLista(pagamentoDTO.getAulaAvulsa(),"EA", false, null,null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        Double valorTotalParcelas = Math.round(movparcelas.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum() * 100.0) / 100.0;
        Double valorTotalPgamentos = Math.round(pagamentoDTO.getFormasPagamento().stream().mapToDouble(pagamento -> pagamento.getValor()).sum() * 100.0) / 100.0;

        if (movparcelas.equals(0.0) && !isVendaAvulsa) {
            throw new ServiceException("Não existe parcelas pendentes de pagamento para o contrato " + contratoVO.getCodigo() + ".");
        } else if (movparcelas.equals(0.0) && isVendaAvulsa) {
            throw new ServiceException("Não existe parcelas pendentes de pagamento para a venda avulsa " + vendaAvulsaVO.getCodigo() + ".");
        }

        if(!valorTotalParcelas.equals(valorTotalPgamentos)){
            throw new ServiceException("O valor total dos pagamentos não é igual ao valor total das parcelas a pagar. " +
                    "O valor total das parcelas é de R$ "+ Uteis.formatarValorEmReal(valorTotalParcelas)+" e o valor total dos pagamentos é de R$ "+Uteis.formatarValorEmReal(valorTotalPgamentos)+".");
        }

        Date hoje = Calendario.hoje();

        List<MovPagamentoVO> movPagamentoVOS = new ArrayList<>();
        for (FormaPagamentoDTO formaPagamentoDTO: pagamentoDTO.getFormasPagamento()) {
            Boolean credito = formaPagamentoDTO.getAutorizacaoCartao() == null || formaPagamentoDTO.getAutorizacaoCartao().trim().isEmpty() ? false : true;
            FormaPagamentoVO formaPagamentoVO = formaPagamento.obterPorCodigo(formaPagamentoDTO.getCodigoFormaPagamento());
            AdquirenteVO adquirenteVO = null;
            if(formaPagamentoDTO.getCodigoAdquirente() != null){
                adquirenteVO = adquirente.consultarPorCodigo(formaPagamentoDTO.getCodigoAdquirente());
                if(adquirenteVO == null){
                    new ServiceException("Adquirente de código "+formaPagamentoDTO.getCodigoAdquirente()+" não existe");
                }
            }

            if(formaPagamentoVO == null){
                new ServiceException("Forma de pagamento de código "+formaPagamentoDTO.getCodigoFormaPagamento()+" não existe");
            }

            List<String> list = Arrays.asList(new String[]{TipoFormaPagto.AVISTA.getSigla(),
                    TipoFormaPagto.CARTAOCREDITO.getSigla(),
                    TipoFormaPagto.CARTAODEBITO.getSigla(),
                    TipoFormaPagto.TRANSFERENCIA_BANCARIA.getSigla()});
            if(!list.contains(formaPagamentoVO.getTipoFormaPagamentoEnum().getSigla())){
                throw new ServiceException("O tipo da forma de pagamento "+formaPagamentoVO.getSigla()+" não pode ser registrada por esta aplicação.");
            }

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setDataPagamento(hoje);
            movPagamentoVO.setDataLancamento(hoje);
            movPagamentoVO.setResponsavelPagamento(usuarioVO);
            movPagamentoVO.setFormaPagamento(formaPagamentoVO);
            movPagamentoVO.setValor(formaPagamentoDTO.getValor());
            movPagamentoVO.setValorTotal(formaPagamentoDTO.getValor());
            movPagamentoVO.setDataPagamento(hoje);
            movPagamentoVO.setDataLancamento(hoje);
            movPagamentoVO.setDataQuitacao(hoje);
            movPagamentoVO.setAutorizacaoCartao(formaPagamentoDTO.getAutorizacaoCartao().trim());
            movPagamentoVO.setNrParcelaCartaoCredito(formaPagamentoDTO.getNumeroParcelasCartao());
            movPagamentoVO.setCredito(credito);
            movPagamentoVO.setDepositoCC(false);
            movPagamentoVO.setDataPagamentoOriginal(hoje);
            movPagamentoVO.setDataPagamentoOriginal(hoje);
            movPagamentoVO.setAdquirenteVO(adquirenteVO);
            movPagamentoVO.setUsarParceiroFidelidade(false);
            movPagamentoVO.setMovPagamentoEscolhida(true);

            if(isVendaAvulsa) {
                movPagamentoVO.setNomePagador(vendaAvulsaVO.getCliente().getPessoa().getNome());
                movPagamentoVO.setPessoa(vendaAvulsaVO.getCliente().getPessoa());
                movPagamentoVO.setEmpresa(vendaAvulsaVO.getEmpresa());
                movPagamentoVO.setEmpresa(vendaAvulsaVO.getEmpresa());
            } else if(isAulaAvulsa) {
                movPagamentoVO.setNomePagador(aulaAvulsaDiariaVO.getCliente().getPessoa().getNome());
                movPagamentoVO.setPessoa(aulaAvulsaDiariaVO.getCliente().getPessoa() );
                movPagamentoVO.setEmpresa(aulaAvulsaDiariaVO.getEmpresa());
                movPagamentoVO.setEmpresa(aulaAvulsaDiariaVO.getEmpresa());
            } else {
                movPagamentoVO.setNomePagador(clienteVO.getPessoa().getNome());
                movPagamentoVO.setPessoa(clienteVO.getPessoa());
                movPagamentoVO.setEmpresa(contratoVO.getEmpresa());
                movPagamentoVO.setEmpresa(contratoVO.getEmpresa());
            }

            movPagamentoVOS.add(movPagamentoVO);
        }

        ReciboPagamentoVO reciboPagamentoVoIncluido = incluirListaPagamento(
                movPagamentoVOS,
                movparcelas,
                null,
                contratoVO,
                false,
                0.0,
                controlarTransacao,
                null,
                TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);

        contrato = null;
        movParcela = null;
        formaPagamento = null;
        usuario = null;
        cliente = null;
        adquirente = null;

        return reciboPagamentoVoIncluido;
    }

    public ReciboPagamentoVO gravarPagamentoContrato(PagamentoDTO pagamentoDTO, Date dataPagamento) throws Exception {
        Contrato contrato = new Contrato(getCon());
        Usuario usuario = new Usuario(getCon());
        MovParcela movParcela = new MovParcela(getCon());
        FormaPagamento formaPagamento = new FormaPagamento(getCon());
        Cliente cliente = new Cliente(getCon());
        Adquirente adquirente = new Adquirente(getCon());

        if (UteisValidacao.emptyNumber(pagamentoDTO.getContrato())) {
            throw new ServiceException("Contrato não informado!");
        }
        ContratoVO contratoVO = contrato.consultarPorCodigo(pagamentoDTO.getContrato(), Uteis.NIVELMONTARDADOS_MINIMOS);

        ClienteVO clienteVO = null;
        List<MovParcelaVO> movparcelas = new ArrayList<>();

        clienteVO = cliente.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!(pagamentoDTO.getParcelasContrato().size() > 0)) {
            throw new ServiceException("Para o pagamento parcial, é necessário informar quais parcelas serão pagas.");
        }
        for (Integer parcela : pagamentoDTO.getParcelasContrato()) {
            MovParcelaVO movParcelaVO = movParcela.consultarPorCodigo(parcela, Uteis.NIVELMONTARDADOS_MINIMOS);
            movparcelas.add(movParcelaVO);
        }

        if (UteisValidacao.emptyNumber(pagamentoDTO.getUsuario())) {
            throw new ServiceException("Usuario não informado!");
        }
        UsuarioVO usuarioVO = usuario.consultarPorChavePrimaria(pagamentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Double valorTotalParcelas = Uteis.arredondarForcando2CasasDecimais(movparcelas.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum());
        Double valorTotalPgamentos = Uteis.arredondarForcando2CasasDecimais(pagamentoDTO.getFormasPagamento().stream().mapToDouble(pagamento -> pagamento.getValor()).sum());

        if(!valorTotalParcelas.equals(valorTotalPgamentos)){
            throw new ServiceException("O valor total dos pagamentos não é igual ao valor total das parcelas a pagar. " +
                    "O valor total das parcelas é de R$ "+ Uteis.formatarValorEmReal(valorTotalParcelas)+" e o valor total dos pagamentos é de R$ "+Uteis.formatarValorEmReal(valorTotalPgamentos)+".");
        }

        List<MovPagamentoVO> movPagamentoVOS = new ArrayList<>();
        for (FormaPagamentoDTO formaPagamentoDTO: pagamentoDTO.getFormasPagamento()) {
            FormaPagamentoVO formaPagamentoVO = formaPagamento.obterPorCodigo(formaPagamentoDTO.getCodigoFormaPagamento());
            AdquirenteVO adquirenteVO = null;
            if(!UteisValidacao.emptyNumber(formaPagamentoDTO.getCodigoAdquirente())){
                adquirenteVO = adquirente.consultarPorCodigo(formaPagamentoDTO.getCodigoAdquirente());
                if(adquirenteVO == null || UteisValidacao.emptyNumber(adquirenteVO.getCodigo())){
                    throw new ServiceException("Adquirente de código "+formaPagamentoDTO.getCodigoAdquirente()+" não existe");
                }
            }

            if(formaPagamentoVO == null){
                throw new ServiceException("Forma de pagamento de código "+formaPagamentoDTO.getCodigoFormaPagamento()+" não existe");
            }

            List<String> list = Arrays.asList(new String[]{TipoFormaPagto.AVISTA.getSigla(),
                    TipoFormaPagto.CARTAOCREDITO.getSigla(),
                    TipoFormaPagto.CARTAODEBITO.getSigla(),
                    TipoFormaPagto.BOLETOBANCARIO.getSigla(),
                    TipoFormaPagto.PIX.getSigla(),
                    TipoFormaPagto.TRANSFERENCIA_BANCARIA.getSigla()});
            if(!list.contains(formaPagamentoVO.getTipoFormaPagamentoEnum().getSigla())){
                throw new ServiceException(String.format("O tipo da forma de pagamento %s não pode ser utilizada por esta aplicação.", formaPagamentoVO.getSigla()));
            }

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setDataPagamento(dataPagamento);
            movPagamentoVO.setDataLancamento(dataPagamento);
            movPagamentoVO.setResponsavelPagamento(usuarioVO);
            movPagamentoVO.setFormaPagamento(formaPagamentoVO);
            movPagamentoVO.setValor(formaPagamentoDTO.getValor());
            movPagamentoVO.setValorTotal(formaPagamentoDTO.getValor());
            movPagamentoVO.setDataPagamento(dataPagamento);
            movPagamentoVO.setDataLancamento(dataPagamento);
            movPagamentoVO.setDataCobrancaTransacao(dataPagamento);
            movPagamentoVO.setDataQuitacao(dataPagamento);
            movPagamentoVO.setAutorizacaoCartao(formaPagamentoDTO.getAutorizacaoCartao().trim());
            movPagamentoVO.setNrParcelaCartaoCredito(formaPagamentoDTO.getNumeroParcelasCartao());
            movPagamentoVO.setCredito(false);
            movPagamentoVO.setDepositoCC(false);
            movPagamentoVO.setDataPagamentoOriginal(dataPagamento);
            movPagamentoVO.setDataPagamentoOriginal(dataPagamento);
            movPagamentoVO.setAdquirenteVO(adquirenteVO);
            movPagamentoVO.setUsarParceiroFidelidade(false);
            movPagamentoVO.setMovPagamentoEscolhida(true);

            movPagamentoVO.setNomePagador(clienteVO.getPessoa().getNome());
            movPagamentoVO.setPessoa(clienteVO.getPessoa());
            movPagamentoVO.setEmpresa(contratoVO.getEmpresa());
            movPagamentoVO.setEmpresa(contratoVO.getEmpresa());

            movPagamentoVOS.add(movPagamentoVO);
        }

        ReciboPagamentoVO reciboPagamentoVO = incluirListaPagamento(
                movPagamentoVOS,
                movparcelas,
                null,
                contratoVO,
                false,
                0.0,
                false,
                null,
                TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);

        movParcela = null;
        formaPagamento = null;
        usuario = null;
        cliente = null;
        adquirente = null;

        return reciboPagamentoVO;
    }

    public void validarParametros(PagamentoDTO pagamentoDTO, Boolean pactoApp) throws ParametroObrigatorioException {
        if (pagamentoDTO.getUsuario() == null || pagamentoDTO.getUsuario() <= 0) {
            throw new ParametroObrigatorioException("O usuario é obrigatório");
        }

        if(pagamentoDTO.getFormasPagamento() == null || pagamentoDTO.getFormasPagamento().isEmpty()){
            if(pagamentoDTO.getUsuario() == null || pagamentoDTO.getUsuario() <= 0){
                throw new ParametroObrigatorioException("As formasPagamento são obrigatórias");
            }

            for(FormaPagamentoDTO formaPagamentoDTO: pagamentoDTO.getFormasPagamento()){
                if(formaPagamentoDTO.getCodigoFormaPagamento() == null || formaPagamentoDTO.getCodigoFormaPagamento() <= 0){
                    throw new ParametroObrigatorioException("O codigoFormaPagamento é obrigatório");
                }
                if(formaPagamentoDTO.getValor() == null || formaPagamentoDTO.getValor() <= 0){
                    throw new ParametroObrigatorioException("O valor é obrigatório");
                }
            }
        }

        if((pagamentoDTO.getContrato() == null || pagamentoDTO.getContrato() == 0)){
            throw new ParametroObrigatorioException("É necessário informar um contrato");
        } else if ((pactoApp && (pagamentoDTO.getParcelasContrato() != null && pagamentoDTO.getParcelasContrato().isEmpty()))) {
            throw new ParametroObrigatorioException("É necessário informar pelo menos uma parcela de contrato");
        }
    }

    public void geraReciboContratoImportacao(PagamentoDTO pagamentoDTO, Date dataPagamento) throws Exception {
        Boolean contratoPreenchido = (pagamentoDTO.getParcelasContrato() != null && !pagamentoDTO.getParcelasContrato().isEmpty());

        List<PagamentoDTO> pagamentoDTOList = new ArrayList<>();

        List<MovParcelaVO> movParcelasContrato = new ArrayList<>();
        MovParcela movParcela = new MovParcela(getCon());

        if (contratoPreenchido) {
            for (Integer cod : pagamentoDTO.getParcelasContrato()) {
                addValorParcelasContrato(pagamentoDTO, movParcela, pagamentoDTOList, movParcelasContrato, cod);
            }
        }

        validaValorInformado(pagamentoDTO, movParcelasContrato);

        for (PagamentoDTO pagamentoDTO1 : pagamentoDTOList) {
            gravarPagamentoContrato(pagamentoDTO1, dataPagamento);
        }
    }

    private void validaValorInformado(PagamentoDTO pagamentoDTO, List<MovParcelaVO> movparcelas) throws ServiceException {
        Double valorTotalParcelas = Math.round(movparcelas.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum() * 100.0) / 100.0;
        Double valorTotalPgamentos = Math.round(pagamentoDTO.getFormasPagamento().stream().mapToDouble(pagamento -> pagamento.getValor()).sum() * 100.0) / 100.0;

        if (!valorTotalParcelas.equals(valorTotalPgamentos)) {
            throw new ServiceException("O valor do pagamento " + valorTotalParcelas + " é diferente do valor informado: " + valorTotalPgamentos);
        }
    }

    private PagamentoDTO encontrarPorContrato(List<PagamentoDTO> pagamentoDTOList, MovParcelaVO movParcelaVO) {
        Optional<PagamentoDTO> pagamentoDTO = pagamentoDTOList.stream()
                .filter(p -> p.getContrato().equals(movParcelaVO.getCodigoContrato()))
                .findFirst();

        return pagamentoDTO.orElse(null);
    }

    private void addValorParcelasContrato(PagamentoDTO pagamentoDTO, MovParcela movParcela, List<PagamentoDTO> pagamentoDTOList, List<MovParcelaVO> movparcelasContrato, Integer cod) throws Exception {
        MovParcelaVO movParcelaVO = movParcela.consultarPorCodigo(cod, Uteis.NIVELMONTARDADOS_MINIMOS);
        movparcelasContrato.add(movParcelaVO);

        PagamentoDTO pagamentoDTO1 = encontrarPorContrato(pagamentoDTOList, movParcelaVO);
        if (pagamentoDTO1 != null) {
            pagamentoDTO1.getParcelasContrato().add(movParcelaVO.getCodigo());
            pagamentoDTO1.getFormasPagamento().get(0).setValor(pagamentoDTO1.getFormasPagamento().get(0).getValor() + movParcelaVO.getValorParcela());
        } else {
            PagamentoDTO pagamentoDTOCopy = new PagamentoDTO(pagamentoDTO);
            pagamentoDTOCopy.setAulaAvulsa(0);
            pagamentoDTOCopy.setVendaAvulsa(0);

            List<Integer> parcelas = new ArrayList<>();
            parcelas.add(movParcelaVO.getCodigo());
            pagamentoDTOCopy.setParcelasContrato(parcelas);
            pagamentoDTOCopy.setContrato(movParcelaVO.getCodigoContrato());
            pagamentoDTOCopy.getFormasPagamento().get(0).setValor(movParcelaVO.getValorParcela());
            pagamentoDTOList.add(pagamentoDTOCopy);
        }

    }

    public ReciboPagamentoVO gerarPagamentoCartao(PessoaVO pessoaVO, Double valorTotal, List<MovParcelaVO> listaParcelas,
                                                  Integer nrParcelas, String nsu, String autorizacao, UsuarioVO usuarioVO,
                                                  FormaPagamentoVO formaPagamentoVO, ConvenioCobrancaVO convenioCobrancaVO,
                                                  AdquirenteVO adquirenteVO, OperadoraCartaoVO operadoraCartaoVO,
                                                  EmpresaVO empresaVO) throws Exception {

        Cliente clienteDAO;
        NotaFiscal notaFiscalDAO;
        ZillyonWebFacade zwFacade;
        Empresa empresaDAO;
        try {
            clienteDAO = new Cliente(this.getCon());
            notaFiscalDAO = new NotaFiscal(this.getCon());
            empresaDAO = new Empresa(this.getCon());
            zwFacade = new ZillyonWebFacade(this.getCon());

            List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setFormaPagamento(formaPagamentoVO);

            try {
//                movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacaoVO));
                movPagamentoVO.setAdquirenteVO(adquirenteVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(valorTotal);
            movPagamentoVO.setValorTotal(valorTotal);
            movPagamentoVO.setPessoa(pessoaVO);
            movPagamentoVO.setNomePagador(pessoaVO.getNome());

            if (formaPagamentoVO.getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
                if (!UteisValidacao.emptyNumber(nrParcelas)) {
                    movPagamentoVO.setNrParcelaCartaoCredito(nrParcelas);
                } else {
                    movPagamentoVO.setNrParcelaCartaoCredito(1);
                }
            }

//            movPagamentoVO.setOperadoraCartaoVO(obterOperadoraCartao(transacaoVO, cartaoTO));
            movPagamentoVO.setOperadoraCartaoVO(operadoraCartaoVO);

            movPagamentoVO.setResponsavelPagamento(usuarioVO);
            movPagamentoVO.setNsu(nsu);
            movPagamentoVO.setAutorizacaoCartao(autorizacao.trim());
            movPagamentoVO.setEmpresa(empresaVO);
            movPagamentoVO.setConvenio(convenioCobrancaVO);
            prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, listaParcelas);
            listaPagamento.add(movPagamentoVO);
            ReciboPagamentoVO reciboObj = this.incluirListaPagamento(
                    listaPagamento, listaParcelas,
                    null,
                    obterContratoParcelas(listaParcelas),
                    false, 0.0);

            try {
                //o próprio método de enviar o nfse já valida se a empresa usa ou não o recurso
                reciboObj.getPagamentosDesteRecibo().get(0).setProdutosPagos(this.obterProdutosPagosMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo()));
                EmpresaVO empresaVO1 = empresaDAO.consultarPorChavePrimaria(reciboObj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (empresaVO1.isEnviarNFSeAutomatico() && empresaVO1.getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()) {
                    String chave = DAO.resolveKeyFromConnection(this.getCon());
                    notaFiscalDAO.gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboObj, movPagamentoVO.getResponsavelPagamento(), chave);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logarDebug("Nota Fiscal Eletronica ignorada para o Recibo: " + reciboObj.getCodigo() + ", devido ao erro: " + e.getMessage());
            }

            try {
                if (empresaVO.isNotificarWebhook()) {
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboObj.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    zwFacade.notificarPagamento(clienteVO, reciboObj);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return reciboObj;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            zwFacade = null;
        }
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> listaMovParcelas) {
        ParceiroFidelidadeZW parceiroFidelidadeZW = null;
        try {
            parceiroFidelidadeZW = new ParceiroFidelidadeZW(getCon());
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        } finally {
            parceiroFidelidadeZW = null;
        }
    }

    private ContratoVO obterContratoParcelas(List<MovParcelaVO> listaParcelas) {
        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = listaParcelas.get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : listaParcelas) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                return null;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        return contratoVO;
    }

    public void removerMovPagamentoNfeEmitida(MovPagamentoVO obj) throws Exception {
        try {
            String sql = "UPDATE nfseemitida set movpagamento = null WHERE movpagamento = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void atualizarProdutosPagosMovPagamento(String produtosPagos, Integer codigoMovPagamento) throws Exception {
        String sqlStr = "UPDATE movpagamento SET produtospagos = '" + produtosPagos + "' WHERE codigo = " + codigoMovPagamento;
        try (Statement stm = con.createStatement()) {
            stm.execute(sqlStr);
        }
    }

    public List<MovPagamentoVO> consultarPagamentosPixPJBankDataCompensacaoIncorreta(Date inicio, Date fim) throws Exception {
        List<MovPagamentoVO> lista = new ArrayList<>();
        String sql = "select m.codigo, m.nomepagador from movpagamento m \n" +
                " inner join formapagamento f on m.formapagamento = f.codigo \n" +
                " and m.datalancamento::date between '" + Uteis.getDataJDBC(inicio) + "' and '" + Uteis.getDataJDBC(fim) + "' \n" +
                " and f.tipoformapagamento = 'PX'\n" +
                " AND m.datapagamentooriginal IS NOT null\n" +
                " AND m.datapagamento IS NOT null\n" +
                " and m.datapagamentooriginal::date <> datapagamento::date";
        try (ResultSet rs = criarConsulta(sql, con)) {
            while (rs.next()) {
                MovPagamentoVO movp = new MovPagamentoVO();
                movp.setCodigo(rs.getInt("codigo"));
                movp.setNomePagador(rs.getString("nomepagador"));
                lista.add(movp);
            }
            return lista;
        }
    }

    public void corrigirPagamentosPixPJBankDataCompensacaoIncorreta(List<MovPagamentoVO> lista) throws Exception {
        for (MovPagamentoVO movp : lista) {
            String sql = "UPDATE movpagamento SET datapagamento = datapagamentooriginal WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setInt(1, movp.getCodigo());
                ps.executeUpdate();
            }
        }
    }


    private void retirarClienteRestricoesInadimplenciaRedeEmpresa(PessoaVO pessoaVO) {
        try {
            Empresa empresaDAO = new Empresa(con);
            Cliente clienteDAO = new Cliente(con);
            ClienteRestricao clienteRestricaoDAO = new ClienteRestricao(con);

            final ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO == null || clienteVO.getDataInclusaoClienteRestricaoRedeEmpresa() == null) {
                return;
            }

            final EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!empresaVO.isUtilizaGestaoClientesComRestricoes()) {
                return;
            }

            if (clienteDAO.clienteEstaInadimplente(clienteVO.getCodigo(), clienteVO.getEmpresa().getCodigo())) {
                return;
            }

            if (UteisValidacao.emptyString(clienteVO.getPessoa().getCfp())) {
                return;
            }

            final String cpf = Uteis.tirarCaracteres(clienteVO.getPessoa().getCfp(), true);
            if (cpf.length() != 11) {
                return;
            }

            String chave;
            if (JSFUtilities.isJSFContext()) {
                chave = (String) JSFUtilities.getFromSession(JSFUtilities.KEY);
            } else {
                chave = DAO.resolveKeyFromConnection(con);
            }

            final RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
            final boolean temRedeEmpresa = redeEmpresa != null && redeEmpresa.getGestaoRedes();

            if (temRedeEmpresa) {
                AdmCoreMsService.retirarClienteRestricoes(redeEmpresa, chave, cpf, TipoClienteRestricaoEnum.INADIMPLENCIA);
            } else {
                clienteRestricaoDAO.excluirSemCommit(cpf, TipoClienteRestricaoEnum.INADIMPLENCIA);
            }

            clienteDAO.atualizarDataInclusaoClienteRestricao(clienteVO.getCodigo(), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
