/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.atualizadb.processo.GerarReciboItemRemessa;
import br.com.pactosolucoes.enumeradores.TotalizadorBIDCCEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.interfaces.financeiro.RemessaItemInterfaceFacade;
import org.jboleto.JBoleto;
import org.jboleto.JBoletoBean;
import org.jboleto.bancos.BancoSicredi;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.boleto.BancoEnum;
import servicos.impl.dcc.base.RemessaService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RemessaItem extends SuperEntidade implements RemessaItemInterfaceFacade {

    public RemessaItem() throws Exception {

    }

    public RemessaItem(Connection conex) throws Exception {
        super(conex);
    }

    @Override
    public void incluir(RemessaItemVO obj) throws Exception {

        String sql = "INSERT INTO remessaitem(remessa, movparcela, props, movpagamento, pessoa, nrtentativaparcela,"
                + "vencimentoboleto, tipo, codigosretorno, tipobaixa, valorcredito, identificador, "
                + "diaDoMesDescontoBoletoPagAntecipado, porcentagemDescontoBoletoPagAntecipado, valorItemRemessa, valorMovParcela, valorMulta, valorJuros, contabilizadapacto,porcentagemDescontoBoleto, "
                + "valordescontoboletopagantecipado ) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        if (obj.getRemessa().getTipo().equals(TipoRemessaEnum.EDI_CIELO)) {
            obj.put(APF.CartaoMascarado, obj.getNazgDTO().getCardMask());
        }
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 1;
            ps.setInt(i++, obj.getRemessa().getCodigo());
            resolveFKNull(ps, i++, obj.getMovParcela().getCodigo());
            ps.setString(i++, obj.getProps().toString());
            RemessaItem.resolveFKNull(ps, i++, obj.getMovPagamento().getCodigo());
            RemessaItem.resolveFKNull(ps, i++, obj.getPessoa().getCodigo());
            ps.setInt(i++, (obj.getMovParcela().getNrTentativas() + 1));
            resolveDateNull(ps, i++, obj.getDataVencimentoBoleto());
            ps.setInt(i++, obj.getRemessa().getTipo().getId());
            ps.setString(i++, obj.getCodigosRetorno());
            ps.setInt(i++, obj.getTipoBaixa().getId());
            resolveDoubleNull(ps, i, obj.getValorCredito());
            i++;
            if (obj.getIdentificador() == null) {
                ps.setNull(i++, Types.INTEGER);
            } else {
                ps.setInt(i++, obj.getIdentificador());
            }
            ps.setInt(i++, obj.getDiaDoMesDescontoBoletoPagAntecipado());
            ps.setDouble(i++, obj.getPorcentagemDescontoBoletoPagAntecipado());
            ps.setDouble(i++, obj.getValorItemRemessa());
            ps.setDouble(i++, obj.getValorParcela());
            ps.setDouble(i++, obj.getValorMulta());
            ps.setDouble(i++, obj.getValorJuros());
            ps.setBoolean(i++, obj.isContabilizadaPacto());
            ps.setDouble(i++, obj.getPorcentagemDescontoBoleto());
            ps.setDouble(i++, obj.getValorDescontoBoletoPagAntecipado());

            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        incluirRemessaItemMovParcelaVO(obj);
    }

    private void incluirRemessaItemMovParcelaVO(RemessaItemVO remessaItemVO) throws Exception {
        RemessaItemMovParcela tmpFacade = new RemessaItemMovParcela(con);
        try {
            for (RemessaItemMovParcelaVO item : remessaItemVO.getMovParcelas()) {
                item.getRemessaItemVO().setCodigo(remessaItemVO.getCodigo());
                tmpFacade.incluirSemCommit(item);
            }
        } finally {
            tmpFacade = null;
        }
    }

    @Override
    public void alterar(RemessaItemVO obj) throws Exception {

        String sql = "UPDATE remessaitem "
                + "set remessa = ?, movparcela = ?, props = ?, movpagamento = ?, pessoa = ?, nrtentativaparcela=?, codigosretorno = ?, diaDoMesDescontoBoletoPagAntecipado = ?, "
                + "porcentagemDescontoBoletoPagAntecipado = ?, contabilizadapacto = ?, porcentagemDescontoBoleto = ?, valordescontoboletopagantecipado = ? ";
        if(obj.getIdentificador() != null){
            sql += " , identificador = ? ";
        }
        sql += " WHERE ((codigo = ?))";
        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(i++, obj.getRemessa().getCodigo());
            RemessaItem.resolveFKNull(ps, i++, obj.getMovParcela().getCodigo());
            ps.setString(i++, obj.getProps().toString());
            RemessaItem.resolveFKNull(ps, i++, obj.getMovPagamento().getCodigo());
            RemessaItem.resolveFKNull(ps, i++, obj.getPessoa().getCodigo());
            ps.setInt(i++, (obj.getMovParcela().getNrTentativas()));
            ps.setString(i++, obj.getCodigosRetorno());
            ps.setInt(i++, obj.getDiaDoMesDescontoBoletoPagAntecipado());
            ps.setDouble(i++, obj.getPorcentagemDescontoBoletoPagAntecipado());
            ps.setBoolean(i++, obj.isContabilizadaPacto());
            ps.setDouble(i++, obj.getPorcentagemDescontoBoleto());
            ps.setDouble(i++, obj.getValorDescontoBoletoPagAntecipado());
            if (obj.getIdentificador() != null) {
                ps.setInt(i++, (obj.getIdentificador()));
            }
            ps.setInt(i, obj.getCodigo());

            ps.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoComposicaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacço na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoComposicaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(RemessaItemVO obj) throws Exception {
        String sql = "DELETE FROM remessaitem WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
        }
    }

    private List<RemessaItemVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con, final int nivel) throws Exception {
        List<RemessaItemVO> vetResultado = new ArrayList<RemessaItemVO>();
        while (tabelaResultado.next()) {
            RemessaItemVO obj = montarDados(tabelaResultado, con, nivel);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public void estornarItensRemessa(LogVO log, List<RemessaItemVO> listaItens, boolean removerVinculoParcela, UsuarioVO usuarioEstornoVO) throws Exception {
        if (!UteisValidacao.emptyList(listaItens)) {

            for (RemessaItemVO item : listaItens) {

                if (item.getMovPagamento() != null && !UteisValidacao.emptyNumber(item.getMovPagamento().getCodigo())) {
                    log.setValorCampoAlterado(log.getValorCampoAlterado() + "RemessaItem ID = " + item.getCodigo()
                            + ";" + item.getMovParcela()
                            + ";" + "Status = " + item.getDescricaoStatus()
                            + ";" + "Autorização = " + item.getAutorizacao()
                            + ";" + "Descrição = " + item.getMovParcela().getDescricao()
                            + ";" + "Situação = " + item.getMovParcela().getSituacao()
                            + ";" + "Usuario Retorno = " + item.getRemessa().getUsuarioRetorno()
                            + ";" + "Recibo = " + item.getMovPagamento().getReciboPagamento().getCodigo());
                    item.getProps().put("nomePessoa", item.getPessoa().getNome());
                    item.getProps().put("logEstorno", log.getValorCampoAlterado());

                } else {

                    log.setValorCampoAlterado(log.getValorCampoAlterado() + "RemessaItem ID = " + item.getCodigo()
                            + ";" + item.getMovParcela());
                    item.getProps().put("nomePessoa", item.getPessoa().getNome());
                    item.getProps().put("logEstorno", log.getValorCampoAlterado());
                }

                //estornar remessaitemmovparcela
                JSONObject jsonEstorno = estornarRemessaItemMovParcela(usuarioEstornoVO, item, removerVinculoParcela);

                //retirar vinculos de movpagamento e parcela
                item.setMovPagamento(new MovPagamentoVO());
                if (removerVinculoParcela) {
                    item.setMovParcela(new MovParcelaVO());
                }

                alterar(item);

                if (jsonEstorno.length() > 0) {
                    item.setJsonEstorno(jsonEstorno.toString());
                    alterarJsonEstorno(item);
                }
            }
        }
    }

    private JSONObject estornarRemessaItemMovParcela(UsuarioVO usuarioEstornoVO, RemessaItemVO item, boolean removerVinculoParcela) throws Exception {
        RemessaItemMovParcela remessaItemMovParcelaDAO = null;
        try {
            remessaItemMovParcelaDAO = new RemessaItemMovParcela(con);

            JSONObject jsonEstorno = new JSONObject();
            jsonEstorno.put("Pessoa", item.getPessoa().getCodigo());
            jsonEstorno.put("Status", item.getDescricaoStatus());
            jsonEstorno.put("DataEstorno", Uteis.getDataComHora(Calendario.hoje()));
            jsonEstorno.put("Autorizacao", item.getAutorizacao());
            jsonEstorno.put("UsuarioRetorno", item.getRemessa().getUsuarioRetorno());
            if (usuarioEstornoVO != null) {
                jsonEstorno.put("UsuarioEstornoCodigo", usuarioEstornoVO.getCodigo());
                jsonEstorno.put("UsuarioEstornoNome", usuarioEstornoVO.getNome());
            }

            if (item.getMovPagamento() != null) {
                if (!UteisValidacao.emptyNumber(item.getMovPagamento().getCodigo())) {
                    jsonEstorno.put("MovPagamento", item.getMovPagamento().getCodigo());
                }
                if (item.getMovPagamento().getReciboPagamento() != null &&
                        !UteisValidacao.emptyNumber(item.getMovPagamento().getReciboPagamento().getCodigo())) {
                    jsonEstorno.put("Recibo", item.getMovPagamento().getReciboPagamento().getCodigo());
                }
            }

            if (!UteisValidacao.emptyList(item.getMovParcelas())) {
                JSONArray array = new JSONArray();
                for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getMovParcelas()) {
                    JSONObject jsonParcela = new JSONObject();
                    jsonParcela.put("Codigo", itemMovParcelaVO.getMovParcelaVO().getCodigo());
                    jsonParcela.put("Descricao", itemMovParcelaVO.getMovParcelaVO().getDescricao());
                    jsonParcela.put("Contrato", itemMovParcelaVO.getMovParcelaVO().getContrato().getCodigo());
                    jsonParcela.put("VendaAvulsa", itemMovParcelaVO.getMovParcelaVO().getVendaAvulsaVO().getCodigo());
                    jsonParcela.put("Pessoa", itemMovParcelaVO.getMovParcelaVO().getPessoa().getCodigo());
                    jsonParcela.put("PessoaNome", itemMovParcelaVO.getMovParcelaVO().getPessoa().getNome());

                    itemMovParcelaVO.setJsonEstorno(jsonParcela.toString());

                    if (removerVinculoParcela) {
                        itemMovParcelaVO.setMovParcelaVO(new MovParcelaVO());
                        remessaItemMovParcelaDAO.alterarMovParcela(itemMovParcelaVO);
                    }

                    remessaItemMovParcelaDAO.alterarJsonEstorno(itemMovParcelaVO);
                    array.put(jsonParcela);
                }
                jsonEstorno.put("Parcelas", array);
            }
            return jsonEstorno;
        } finally {
            remessaItemMovParcelaDAO = null;
        }
    }

    private static RemessaItemVO montarDadosParcelaVencida(ResultSet ds, Connection con, final int nivelMontarDados) throws Exception {
        RemessaItemVO obj = new RemessaItemVO();
        obj.setCodigo(ds.getInt("codigo"));
        obj.setTipo(TipoRemessaEnum.getTipoRemessaEnum(ds.getInt("tipo")));
        obj.setProps(Uteis.obterMapFromString(ds.getString("props")));
        obj.setNrTentativaParcela(ds.getInt("nrtentativaparcela"));
        obj.setDataVencimentoBoleto(ds.getDate("vencimentoboleto"));
        obj.setCodigosRetorno(ds.getString("codigosretorno"));
        obj.setTipoBaixa(TipoBaixaEnum.getTipoRemessaEnum(ds.getInt("tipobaixa")));
        obj.setValorCredito(ds.getDouble("valorcredito"));
        obj.getRemessa().setCodigo(ds.getInt("remessa"));
        obj.setIdentificador(ds.getInt("identificador"));
        obj.setDiaDoMesDescontoBoletoPagAntecipado(ds.getInt("diaDoMesDescontoBoletoPagAntecipado"));
        obj.setPorcentagemDescontoBoletoPagAntecipado(ds.getDouble("porcentagemDescontoBoletoPagAntecipado"));
        obj.setValorItemRemessa(ds.getDouble("valorItemRemessa"));
        obj.setValorParcela(ds.getDouble("valorMovParcela"));
        obj.setValorMulta(ds.getDouble("valorMulta"));
        obj.setValorJuros(ds.getDouble("valorJuros"));
        obj.setContabilizadaPacto(ds.getBoolean("contabilizadapacto"));
        obj.setPorcentagemDescontoBoleto(ds.getDouble("porcentagemDescontoBoleto"));
        obj.setValorDescontoBoletoPagAntecipado(ds.getDouble("valordescontoboletopagantecipado"));
        if (ds.getInt("movparcela") != 0) {
            obj.getPessoa().setCodigo(ds.getInt("pessoa"));
        }

//        try {
//            obj.setMovParcela(new MovParcelaVO());
//            if (ds.getInt("movparcela") != 0) {
//                MovParcelaVO movParcelaVO = movParcelaFacade.consultarPorChavePrimaria(ds.getInt("movparcela"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
//                obj.setMovParcela(movParcelaVO);
//            }

//            RemessaVO remessaVO = remessaFacade.consultarPorChavePrimaria(ds.getInt("remessa"));
//            obj.setRemessa(remessaVO);
//            remessaVO =null;

//            if (ds.getInt("pessoa") != 0) {
//                Cliente clienteFacade = new Cliente(con);
//                ClienteVO clienteVO = clienteFacade.consultarPorCodigoPessoa(ds.getInt("pessoa"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
//                clienteFacade = null;
//                if(!UteisValidacao.emptyNumber(clienteVO.getCodigo())){
//                    obj.setClienteVO(clienteVO);
//                    obj.setPessoa(clienteVO.getPessoa());
//                } else {
//                    PessoaVO pessoa = pessoaFacade.consultarPorChavePrimaria(ds.getInt("pessoa"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
//                    obj.setPessoa(pessoa);
//                }
//            }

//            if (ds.getInt("movpagamento") != 0) {
//                obj.setMovPagamento(movPagamentoFacade.consultarPorChavePrimaria(ds.getInt("movpagamento"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
//            }

//            montarDadosParcelasBoleto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
//        } finally {
//            movParcelaFacade = null;
//            pessoaFacade = null;
//            remessaFacade = null;
//            movPagamentoFacade = null;
//        }

        return obj;
    }

    private static RemessaItemVO montarDados(ResultSet ds, Connection con, final int nivelMontarDados) throws Exception {
        RemessaItemVO obj = new RemessaItemVO();
        obj.setCodigo(ds.getInt("codigo"));
        obj.setTipo(TipoRemessaEnum.getTipoRemessaEnum(ds.getInt("tipo")));
        obj.setProps(Uteis.obterMapFromString(ds.getString("props")));
        obj.setNrTentativaParcela(ds.getInt("nrtentativaparcela"));
        obj.setDataVencimentoBoleto(ds.getDate("vencimentoboleto"));
        obj.setCodigosRetorno(ds.getString("codigosretorno"));
        obj.setTipoBaixa(TipoBaixaEnum.getTipoRemessaEnum(ds.getInt("tipobaixa")));
        obj.setValorCredito(ds.getDouble("valorcredito"));
        obj.getRemessa().setCodigo(ds.getInt("remessa"));
        obj.setIdentificador(ds.getInt("identificador"));
        obj.setDiaDoMesDescontoBoletoPagAntecipado(ds.getInt("diaDoMesDescontoBoletoPagAntecipado"));
        obj.setPorcentagemDescontoBoletoPagAntecipado(ds.getDouble("porcentagemDescontoBoletoPagAntecipado"));
        obj.setValorItemRemessa(ds.getDouble("valorItemRemessa"));
        obj.setValorParcela(ds.getDouble("valorMovParcela"));
        obj.setValorMulta(ds.getDouble("valorMulta"));
        obj.setValorJuros(ds.getDouble("valorJuros"));
        obj.setContabilizadaPacto(ds.getBoolean("contabilizadapacto"));
        obj.setPorcentagemDescontoBoleto(ds.getDouble("porcentagemDescontoBoleto"));
        obj.setValorDescontoBoletoPagAntecipado(ds.getDouble("valordescontoboletopagantecipado"));

        try {
            obj.setJsonEstorno(ds.getString("jsonEstorno"));
        } catch (Exception ignored) {
        }

        try {
            if (ds.getInt("movparcela") != 0 || ds.getInt("pessoa") != 0) {
                obj.getPessoa().setCodigo(ds.getInt("pessoa"));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_GESTAOREMESSABOLETO) {
            return obj;
        }
        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            try {
                MovParcela movParcelaDAO = new MovParcela(con);
                obj.setMovParcela(movParcelaDAO.consultarPorChavePrimaria(ds.getInt("movparcela"), Uteis.NIVELMONTARDADOS_MINIMOS));
                movParcelaDAO = null;
            } catch (Exception erro) {
                obj.setMovParcela(new MovParcelaVO());
            }

            Remessa remessaFacade = new Remessa(con);
            obj.setRemessa(remessaFacade.consultarPorChavePrimaria(ds.getInt("remessa")));
            remessaFacade = null;

            if (ds.getInt("movpagamento") != 0) {
                obj.setMovPagamento(new MovPagamentoVO());
                obj.getMovPagamento().setCodigo(ds.getInt("movpagamento"));
            }
            return obj;
        }

        MovParcela movParcelaFacade = new MovParcela(con);
        Pessoa pessoaFacade = new Pessoa(con);
        Remessa remessaFacade = new Remessa(con);
        MovPagamento movPagamentoFacade = new MovPagamento(con);
        try {
            obj.setMovParcela(new MovParcelaVO());
            if (ds.getInt("movparcela") != 0) {
                MovParcelaVO movParcelaVO = movParcelaFacade.consultarPorChavePrimaria(ds.getInt("movparcela"), Uteis.NIVELMONTARDADOS_MINIMOS);
                obj.setMovParcela(movParcelaVO);
            }

            RemessaVO remessaVO = remessaFacade.consultarPorChavePrimaria(ds.getInt("remessa"));
            obj.setRemessa(remessaVO);

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO){
                Cliente clienteFacade = new Cliente(con);
                ClienteVO clienteVO = clienteFacade.consultarPorCodigoPessoa(ds.getInt("pessoa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                clienteFacade = null;
                if(!UteisValidacao.emptyNumber(clienteVO.getCodigo())){
                    obj.setClienteVO(clienteVO);
                    obj.setPessoa(clienteVO.getPessoa());
                } else {
                    PessoaVO pessoa = pessoaFacade.consultarPorChavePrimaria(ds.getInt("pessoa"), Uteis.NIVELMONTARDADOS_MINIMOS);
                    obj.setPessoa(pessoa);
                }
            }

            if (ds.getInt("pessoa") != 0) {
                Cliente clienteFacade = new Cliente(con);
                ClienteVO clienteVO = new ClienteVO();
                //try criado pois o cliente pode não ter cadastrado sua cidade.
                try {
                    clienteVO = clienteFacade.consultarPorCodigoPessoa(ds.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                clienteFacade = null;
                if(!UteisValidacao.emptyNumber(clienteVO.getCodigo())){
                    obj.setClienteVO(clienteVO);
                    obj.setPessoa(clienteVO.getPessoa());
                } else {
                    PessoaVO pessoa = pessoaFacade.consultarPorChavePrimaria(ds.getInt("pessoa"), Uteis.NIVELMONTARDADOS_MINIMOS);
                    obj.setPessoa(pessoa);
                }
            }

            if (ds.getInt("movpagamento") != 0) {
                obj.setMovPagamento(movPagamentoFacade.consultarPorChavePrimaria(ds.getInt("movpagamento"), Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }

            montarDadosParcelasBoleto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        } finally {
            movParcelaFacade = null;
            pessoaFacade = null;
            remessaFacade = null;
            movPagamentoFacade = null;
        }

        return obj;
    }

    private static void montarDadosParcelasBoleto(RemessaItemVO obj, int nivelMontarDados, Connection con) throws Exception {
        RemessaItemMovParcela remessaItemMovParcela = new RemessaItemMovParcela(con);
        obj.setMovParcelas(remessaItemMovParcela.consultarPorRemessaItem(obj.getCodigo(), nivelMontarDados));
        remessaItemMovParcela = null;
    }

    @Override
    public RemessaItemVO consultarPorChavePrimaria(final int codigo, final int nivel) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( RemessaItem %s)", codigo));
                }
                return (montarDados(tabelaResultado, con, nivel));
            }
        }
    }

    public List<RemessaItemVO> consultarPorCodigoRemessa(final int codigoRemessa, final int nivel) throws Exception {
        return consultarPorCodigoRemessa(codigoRemessa, null, nivel);
    }

    public List<RemessaItemVO> consultarPorCodigoRemessa(final int codigoRemessa, PaginadorDTO paginadorDTO, final int nivel) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM remessaitem WHERE remessa = ").append(codigoRemessa).append(" order by codigo \n");

        if (paginadorDTO != null) {
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            sql.append("LIMIT ").append(maxResults).append(" \n");
            sql.append("OFFSET ").append(indiceInicial).append(" \n");
        }

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, con, nivel);
            }
        }
    }

    public Integer consultarQtdPorCodigoRemessa(final int codigoRemessa) throws Exception {
        String sql = "SELECT count(*) as qtd FROM remessaitem WHERE remessa = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoRemessa);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("qtd");
                }
            }
        }
        return 0;
    }

    @Override
    public Double consultarPorCodigoValorRemessaAceito(final RemessaVO remessaVO) throws Exception {
        String statusVendaAceito;
        if (remessaVO.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.ITAU)) {
            statusVendaAceito = "'%StatusVenda=06%'";
        } else {
            statusVendaAceito = "'%StatusVenda=00%'";
        }
        String sql = "SELECT sum(valoritemremessa) as valorremessa FROM remessaitem WHERE remessa = ? and props ilike" + statusVendaAceito;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, remessaVO.getCodigo());
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                rs.next();
                return rs.getDouble("valorremessa");
            }
        }
    }

    @Override
    public Double consultarPorCodigoValorRemessa(final int codigoRemessa) throws Exception {
        String sql = "SELECT sum(valoritemremessa) as valor FROM remessaitem WHERE remessa = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoRemessa);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    return rs.getDouble("valor");
                } else {
                    return 0.0;
                }
            }
        }
    }

    public boolean existeItensASerProcessados(final int codigoRemessa, boolean isDCO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT NOT EXISTS \n");
        sql.append("(SELECT ri.codigo \n");
        sql.append(" FROM remessaitem ri \n");
        sql.append(" LEFT JOIN movparcela mp on mp.codigo = ri.movparcela \n");
        sql.append(" WHERE (ri.props NOT LIKE '%StatusVenda%' \n");
        sql.append("        OR ri.props LIKE '%StatusVenda=BD%' \n");
        if (!isDCO) {
            //Status 02 para DCO é retorno passível de considerar a remessa concluída
            sql.append("        OR ri.props LIKE '%StatusVenda=02%' \n");
        } else {
            sql.append("        OR ri.props LIKE '%StatusVenda=PE%' \n");
            sql.append("        OR ri.props LIKE '%StatusVenda=AT%' \n");
        }
        sql.append(")\n");
        if (isDCO) {
            sql.append("AND mp.situacao <> 'PG' \n");
        }
        sql.append(" AND ri.remessa = ?) AS remessaprocessada");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, codigoRemessa);
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                rs.next();
                return rs.getBoolean(1);
            }
        }
    }

    @Override
    public List<RemessaItemVO> consultarPorCodigoParcela(final int codigoMovParcela, final int nivel) throws Exception {
        String sql = "select \n" +
                "distinct ri.* \n" +
                "from remessaitem ri \n" +
                "left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo\n" +
                "where coalesce(ri.movparcela,0) = ? \n" +
                "or coalesce(rim.movparcela,0) = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoMovParcela);
            sqlConsultar.setInt(2, codigoMovParcela);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, con, nivel);
            }
        }
    }

    private ResultSet montarRemessaCliente(Integer pessoa, boolean count,int limit,int offset, TipoCobrancaEnum[] arrayTiposCobrancaEnum, int movParcela) throws Exception{
        StringBuilder filtroTipoConvenio = new StringBuilder();
        if (arrayTiposCobrancaEnum != null && arrayTiposCobrancaEnum.length > 0) {
            String tiposC = "";
            for (TipoCobrancaEnum tipoCobranca : arrayTiposCobrancaEnum) {
                List<TipoConvenioCobrancaEnum> listaConvenio = TipoConvenioCobrancaEnum.obterListaTipoCobranca(tipoCobranca);
                for (TipoConvenioCobrancaEnum tipo : listaConvenio) {
                    tiposC += ("," + tipo.getCodigo());
                }
            }
            filtroTipoConvenio.append("AND cc.tipoconvenio IN (").append(tiposC.replaceFirst(",", "")).append(") \n");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        if (count) {
            sql.append("COUNT(distinct(ri.codigo)) as total \n");
        } else {
            sql.append("distinct ri.* \n");
        }
        sql.append("from remessaitem ri where ri.codigo in ( \n");
        sql.append("SELECT  \n");
        sql.append("ri.codigo \n");
        sql.append("FROM remessaitem ri \n");
        sql.append("inner join remessa re on re.codigo = ri.remessa \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = re.conveniocobranca \n");
        if(!count && !UteisValidacao.emptyNumber(movParcela)) {
            sql.append("inner join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
            sql.append("inner join movparcela mp on mp.codigo = rim.movparcela \n");
        }
        sql.append("where ri.pessoa = ").append(pessoa).append(" \n");
        if(!count && !UteisValidacao.emptyNumber(movParcela)) {
            sql.append("and mp.codigo = ").append(movParcela).append(" \n");
        }
        sql.append(filtroTipoConvenio).append(" \n");
        sql.append("union \n");
        sql.append("SELECT  \n");
        sql.append("ri.codigo \n");
        sql.append("FROM remessaitem ri \n");
        sql.append("inner join remessa re on re.codigo = ri.remessa \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = re.conveniocobranca \n");
        sql.append("inner join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sql.append("inner join movparcela mp on mp.codigo = rim.movparcela \n");
        sql.append("where mp.pessoa = ").append(pessoa).append(" \n");
        if(!count && !UteisValidacao.emptyNumber(movParcela)) {
            sql.append("and mp.codigo = ").append(movParcela).append(" \n");
        }
        sql.append(filtroTipoConvenio).append(" \n");
        sql.append("union \n");
        sql.append("SELECT  \n");
        sql.append("ri.codigo \n");
        sql.append("FROM remessaitem ri \n");
        sql.append("inner join remessa re on re.codigo = ri.remessa \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = re.conveniocobranca \n");
        sql.append("inner join movparcela mp on mp.codigo = ri.movparcela \n");
        sql.append("where mp.pessoa = ").append(pessoa).append(" \n");
        if(!count && !UteisValidacao.emptyNumber(movParcela)) {
            sql.append("and mp.codigo = ").append(movParcela).append(" \n");
        }
        sql.append(filtroTipoConvenio).append(" \n");
        sql.append(") \n");
        if (!count) {
            sql.append("ORDER BY ri.codigo DESC \n");
        }

        if (!count && !UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit).append(" offset ").append(offset);
        }
        return criarConsulta(sql.toString(), con);
    }

    public Integer obterCountRemessaCliente(Integer pessoa, TipoCobrancaEnum[] arrayTiposCobrancaEnum) throws Exception {
        try (ResultSet rs = montarRemessaCliente(pessoa, true, 0, 0, arrayTiposCobrancaEnum, 0)) {
            while (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    @Override
    public List<RemessaItemVO> consultarTelaCliente(final int codigoPessoa, int limit, int offset, TipoCobrancaEnum[] arrayTiposCobrancaEnum, int nivel, int movParcela) throws Exception {
        try (ResultSet tabelaResultado = montarRemessaCliente(codigoPessoa, false, limit, offset, arrayTiposCobrancaEnum, movParcela)) {
            return montarDadosConsulta(tabelaResultado, con, nivel);
        }
    }

    @Override
    public List<RemessaItemVO> consultarPorCodigoPessoa(final int codigoPessoa, final int nivel) throws Exception {
        String sql = "SELECT ri.* FROM remessaitem ri inner join remessa r on r.codigo = ri.remessa WHERE ri.pessoa = ? order by ri.codigo desc";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPessoa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, con, nivel);
            }
        }
    }

    public List<MovParcelaVO> consultarParcelasEmAbertoRemessa(final int remessa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select mp.* from movparcela mp \n");
        sql.append(" inner join remessaitem ri on ri.movparcela = mp.codigo \n");
        sql.append(" left join transacaomovparcela tmp on tmp.movparcela = mp.codigo \n");
        sql.append(" left join transacao t on tmp.transacao = t.codigo \n");
        sql.append(" where ri.remessa = ").append(remessa).append(" and mp.situacao = 'EA' \n");
        sql.append(" and (t.codigo is null or t.situacao not in (");
        sql.append(SituacaoTransacaoEnum.APROVADA.getId()).append(",");
        sql.append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(",");
        sql.append(SituacaoTransacaoEnum.NENHUMA.getId()).append("))");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            return MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        }
    }

    @Override
    public List<RemessaItemVO> consultarPorRecibo(final int codRecibo, final int nivel) throws Exception {
        String sql = "select ri.* from remessaitem ri "
                + "inner join movpagamento mp on mp.codigo = ri.movpagamento "
                + "where mp.recibopagamento = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codRecibo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, con, nivel);
            }
        }
    }

    @Override
    public RemessaItemVO obterUltimoItemRemessaPorCodigoParcela(final int codigoMovParcela, int nivelMontarDados, boolean parcelaVencida) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE codigo in (select codigo from remessaitem where movparcela = ? order by codigo desc limit 1)" +
                "or remessaitem.codigo in (select rimp.remessaitem from remessaitemmovparcela rimp where movparcela = ? ORDER BY codigo DESC LIMIT 1)";
        RemessaItemVO remessaItemVO;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoMovParcela);
            sqlConsultar.setInt(2, codigoMovParcela);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {

                if (!tabelaResultado.next()) {
                    return null;
                }

                remessaItemVO = null;

                if (parcelaVencida) {
                    remessaItemVO = montarDadosParcelaVencida(tabelaResultado, con, nivelMontarDados);
                } else {
                    remessaItemVO = montarDados(tabelaResultado, con, nivelMontarDados);
                }
            }
        }

        return remessaItemVO;
    }

    @Override
    public RemessaItemVO obterUltimoItemRemessaPorCodigoParcela(final int codigoMovParcela, int nivelMontarDados) throws Exception {
        return obterUltimoItemRemessaPorCodigoParcela(codigoMovParcela, nivelMontarDados, false);
    }

    @Override
    public RemessaItemVO consultarPorParcelaPorCodigoRemessa(Integer codigoParcela, Integer codigoRemessa, int nivel) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE remessa = ? AND movparcela = ?;";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoRemessa);
            sqlConsultar.setInt(2, codigoParcela);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new RemessaItemVO();
                }
                return (montarDados(tabelaResultado, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }
    }

    public void preencherTotalizador(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
                                     String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
                                     String defaultRec, TotalizadorRemessaTO totalizador,
                                     String situacaoRemessa, boolean somenteMes, boolean somenteForaMes, String somenteConvenio) throws Exception {
        try (ResultSet rs = consultarTotalizador(codigoEmpresa, colunas, count, colunasCount, nrTentativas, situacaoParcela,
                dataInicio, dataFim, convenios, defaultRec, situacaoRemessa, somenteMes, somenteForaMes, somenteConvenio)) {

            while (rs.next()) {
                totalizador.setQuantidade(rs.getInt("qtd"));
                totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
            }
        }

        totalizador.setCodConvenio(convenios != null && !convenios.isEmpty() ? convenios.get(0) : null);
    }

    public List<RemessaItemVO> consultarItens(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
                                              String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
                                              String descricaoPagamento, String situacaoRemessa, boolean somenteMes, boolean somenteForaMes, String somenteConvenio) throws Exception {
        try (ResultSet rs = consultarTotalizador(codigoEmpresa, colunas, count, colunasCount, nrTentativas, situacaoParcela,
                dataInicio, dataFim, convenios, descricaoPagamento, situacaoRemessa, somenteMes, somenteForaMes, somenteConvenio)) {
            return montarDadosConsulta(rs, con, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
    }

    public ResultSet consultarTotalizador(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
                                          String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
                                          String defaultRec, String situacaoRemessa, boolean somenteMes, boolean somenteForaMes, String somenteConvenio) throws Exception {

        StringBuilder sql = new StringBuilder("");

        if (count) {
            sql.append("SELECT\n");
            sql.append("  count(").append(colunasCount[0]).append(") as qtd,\n");
            sql.append("  sum(").append(colunasCount[1]).append(":: NUMERIC) as valor\n");
            sql.append("FROM (");
        } else {
            sql.append("SELECT\n");
            sql.append("  rei.*,\n");
            sql.append("  cli.matricula           AS matricula,\n");
            sql.append("  cli.codigo              AS codcliente,\n");
            sql.append("  pes.nome                AS nome,\n");
            sql.append("  con.codigo              AS codcontrato,\n");
            sql.append("  con.datalancamento      AS datalancamento,\n");
            sql.append("  con.dataprevistarenovar AS datavencimento,\n");
            sql.append("  con.vigenciade          AS vigenciade,\n");
            sql.append("  con.vigenciaateajustada AS vigenciaateajustada,\n");
            sql.append("  cd.numeromeses          AS numeromeses,\n");
            sql.append("  mp.codigo               AS codigoparcela,\n");
            sql.append("  mp.descricao            AS descricaoparcela\n");
            sql.append("FROM remessa re\n");
            sql.append("  LEFT JOIN remessaitem rei ON re.codigo = rei.remessa\n");
            sql.append("  LEFT JOIN movparcela mp ON rei.movparcela = mp.codigo\n");
            sql.append("  LEFT JOIN contrato con ON con.codigo = mp.contrato\n");
            sql.append("  LEFT JOIN contratoduracao cd ON cd.contrato = con.codigo\n");
            sql.append("  LEFT JOIN cliente cli ON rei.pessoa = cli.pessoa\n");
            sql.append("  LEFT JOIN pessoa pes ON rei.pessoa = pes.codigo\n");
            sql.append("WHERE rei.movparcela IN (");
        }

        sql.append("SELECT\n");
        sql.append(colunas).append("\n");
        sql.append("FROM remessa re\n");
        sql.append("  LEFT OUTER JOIN remessaitem rei ON re.codigo = rei.remessa\n");
        sql.append("  LEFT OUTER JOIN movparcela mpa ON rei.movparcela = mpa.codigo\n");
        sql.append("  LEFT OUTER JOIN pagamentomovparcela pmp ON rei.movparcela = pmp.movparcela\n");
        sql.append("  LEFT OUTER JOIN movpagamento mpg ON pmp.movpagamento = mpg.codigo\n");
        sql.append("  LEFT OUTER JOIN formapagamento fp ON mpg.formapagamento = fp.codigo\n");
//        sql.append(monteFiltroColaboradores(colaboradores));
        sql.append("WHERE 1 = 1\n");
        sql.append("    AND mpa.codigo IS NOT NULL\n");
        if (codigoEmpresa != null && codigoEmpresa > 0 ) {
            sql.append("      AND re.empresa = ").append(codigoEmpresa).append("\n");
        }
        if (situacaoParcela != null && !situacaoParcela.isEmpty()) {
            sql.append("      AND mpa.situacao ").append(situacaoParcela).append("\n");
        }

        if (defaultRec != null && !defaultRec.isEmpty()) {
            sql.append("      AND fp.defaultrecorrencia  ").append(defaultRec).append("\n");
        }

        if (!UteisValidacao.emptyString(somenteConvenio)) {
            sql.append("      AND ").append(somenteConvenio).append("\n");
        }

        if (!UteisValidacao.emptyString(nrTentativas)) {
            sql.append("      AND rei.nrtentativaparcela ").append(nrTentativas).append(" \n");
        }
        sql.append("      AND re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("'\n");

        if (convenios != null && !convenios.isEmpty()) {
            sql.append("      AND re.conveniocobranca IN( ").append(Uteis.montarListaIN(convenios)).append(" )\n");
        }

        if (situacaoRemessa != null && !situacaoRemessa.isEmpty()) {
            sql.append("      ").append(situacaoRemessa).append("\n");
        }

        if (!((somenteForaMes && somenteMes) || (!somenteMes && !somenteForaMes))) {
            if (somenteMes) {
                sql.append("      AND mpa.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("'\n");
            }

            if (somenteForaMes) {
                sql.append("      AND mpa.datavencimento::date < '").append(Uteis.getData(dataInicio)).append("'");
            }
        }

        if (count) {
            sql.append(") as foo");
        } else {
            sql.append("\nGROUP BY ").append(colunas.toLowerCase().replace("distinct", "")).append(")");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        return stm.executeQuery();
    }

    public ResultSet consultarOperacoesSuspeitas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount) throws Exception {
        StringBuilder sql = new StringBuilder("");

        if (count) {
            sql.append("SELECT\n");
            sql.append("  count(").append(colunasCount[0]).append(") as qtd,\n");
            sql.append("  sum(").append(colunasCount[1]).append(":: NUMERIC) as valor\n");
            sql.append("FROM (");
        } else {
            sql.append("SELECT\n");
            sql.append("  rei.*,\n");
            sql.append("  cli.matricula           AS matricula,\n");
            sql.append("  cli.codigo              AS codcliente,\n");
            sql.append("  pes.nome                AS nome,\n");
            sql.append("  pes.cfp                 AS cpf,\n");
            sql.append("  con.codigo              AS codcontrato,\n");
            sql.append("  con.datalancamento      AS datalancamento,\n");
            sql.append("  con.dataprevistarenovar AS datavencimento,\n");
            sql.append("  con.vigenciade          AS vigenciade,\n");
            sql.append("  con.vigenciaateajustada AS vigenciaateajustada,\n");
            sql.append("  cd.numeromeses          AS numeromeses\n");
            sql.append("FROM remessa re\n");
            sql.append("  LEFT JOIN remessaitem rei ON re.codigo = rei.remessa\n");
            sql.append("  LEFT JOIN movparcela mp ON rei.movparcela = mp.codigo\n");
            sql.append("  LEFT JOIN contrato con ON con.codigo = mp.contrato\n");
            sql.append("  LEFT JOIN contratoduracao cd ON cd.contrato = con.codigo\n");
            sql.append("  LEFT JOIN cliente cli ON rei.pessoa = cli.pessoa\n");
            sql.append("  LEFT JOIN pessoa pes ON rei.pessoa = pes.codigo\n");
            sql.append("WHERE rei.codigo IN (");
        }

        sql.append("SELECT\n");
        sql.append(colunas).append("\n");
        sql.append("FROM remessa re\n");
        sql.append("  LEFT OUTER JOIN remessaitem rei ON re.codigo = rei.remessa\n");
        sql.append("  LEFT OUTER JOIN movparcela mpa ON rei.movparcela = mpa.codigo\n");
        sql.append("  LEFT OUTER JOIN pagamentomovparcela pmp ON rei.movparcela = pmp.movparcela\n");
        sql.append("  LEFT OUTER JOIN movpagamento mpg ON pmp.movpagamento = mpg.codigo\n");
        sql.append("  LEFT OUTER JOIN formapagamento fp ON mpg.formapagamento = fp.codigo\n");
        sql.append("WHERE 1 = 1\n");
        sql.append("    AND re.empresa = ").append(codigoEmpresa).append("\n");
        sql.append("    AND (mpa.codigo IS NULL OR situacao = 'CA')\n");
        sql.append("    AND (split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '00')\n");

        if (count) {
            sql.append(") as foo");
        } else {
            sql.append("\nGROUP BY ").append(colunas.toLowerCase().replace("distinct", "")).append(")");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        return stm.executeQuery();
    }
    @Override
    public List<RemessaItemVO> consultarPorParcelas(final String codigosParcelas, final int nivel) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE movparcela IN ("+codigosParcelas+") order by codigo";
        return montarDadosConsulta(criarConsulta(sql, con), con, nivel);
    }

    @Override
    public List<RemessaItemVO> consultarPorParcelasConvenioCobranca(final String codigosParcelas, final int codigoConvenioCobranca, final int nivel) throws Exception{
        String sql = "SELECT ri.* FROM remessaitem ri\n" +
                "INNER JOIN remessa r ON ri.remessa = r.codigo\n" +
                "WHERE movparcela IN (" + codigosParcelas + ") and r.conveniocobranca = " + codigoConvenioCobranca + " order by codigo";
        try(ResultSet rs = criarConsulta(sql, con)){
            return montarDadosConsulta(rs, con, nivel);
        }
    }

     @Override
    public boolean existeParcelaEmRemessaGeradaouAguardando(Integer codigoMovparcela) throws Exception{
        String sql = "select exists(select ri.*  from  remessaitem ri inner join remessa r on r.codigo = ri.remessa where r.situacaoremessa in (1,4) and ri.movparcela = ?) as  resultado";
         try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
             sqlConsultar.setInt(1, codigoMovparcela);
             try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                 tabelaResultado.next();
                 return tabelaResultado.getBoolean("resultado");
             }
         }
     }

    @Override
    public List<RemessaItemVO> consultarCodigos(String codRemessaItem, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE codigo IN ("+codRemessaItem+") order by codigo";
        return montarDadosConsulta(criarConsulta(sql, con), con, nivelMontarDados);
    }

    @Override
    public List<RemessaItemVO> consultarCodigosPorConvenio(String codRemessaItem, int convenioCobranca, int nivelMontarDados) throws Exception {
        String sql = "SELECT ri.* FROM remessaitem ri\n" +
                "INNER JOIN remessa r ON ri.remessa = r.codigo\n" +
                "WHERE ri.codigo IN ("+codRemessaItem+")\n" +
                "and r.conveniocobranca = " + convenioCobranca +
                "order by codigo";
        return montarDadosConsulta(criarConsulta(sql, con), con, nivelMontarDados);
    }

    public void gerarReciboItemRemessa(Integer codigoRemessaItem, String codAutorizacao, Date dataCompensacao, UsuarioVO usuarioVO) throws Exception {
        GerarReciboItemRemessa gerar = new GerarReciboItemRemessa();
        gerar.gerarItemRemessa(codigoRemessaItem, codAutorizacao, dataCompensacao, con, usuarioVO);
    }

    public String consultarMesesEmAberto(RemessaItemVO itemReimpressao) throws Exception {
        StringBuilder pessoas = new StringBuilder();
        for (RemessaItemMovParcelaVO reimVO : itemReimpressao.getMovParcelas()) {
            pessoas.append(",").append(reimVO.getMovParcelaVO().getPessoa().getCodigo());
        }
        if (pessoas.length() > 0) {
            pessoas.deleteCharAt(0);
        }
        if (pessoas.length() == 0) {
            return "";
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT array_to_string(\n");
        sql.append("           array(\n");
        sql.append("               SELECT DISTINCT mprod.mesreferencia\n");
        sql.append("               FROM movparcela mpar\n");
        sql.append("                 INNER JOIN movprodutoparcela mpp ON mpar.codigo = mpp.movparcela\n");
        sql.append("                 INNER JOIN movproduto mprod ON mprod.codigo = mpp.movproduto\n");
        sql.append("               WHERE 1 = 1\n");
        sql.append("                     AND mpar.pessoa IN (").append(pessoas.toString()).append(")\n");
        sql.append("                     AND mprod.situacao = 'EA'\n");
        sql.append("                     AND mpar.datavencimento < ('01/' || to_char(now(), 'MM/YYYY')) :: DATE\n");
        sql.append("                     AND mpar.datavencimento >= ((('01/' || to_char(now(), 'MM/YYYY')) :: DATE) - INTERVAL '6 months')\n");
        sql.append("           ), ',') AS mesesAberto");

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    String meses = rs.getString("mesesaberto");
                    meses = meses.replace("01/", "JANEIRO/");
                    meses = meses.replace("02/", "FEVEREIRO/");
                    meses = meses.replace("03/", "MARÇO/");
                    meses = meses.replace("04/", "ABRIL/");
                    meses = meses.replace("05/", "MAIO/");
                    meses = meses.replace("06/", "JUNHO/");
                    meses = meses.replace("07/", "JULHO/");
                    meses = meses.replace("08/", "AGOSTO/");
                    meses = meses.replace("09/", "SETEMBRO/");
                    meses = meses.replace("10/", "OUTUBRO/");
                    meses = meses.replace("11/", "NOVEMBRO/");
                    meses = meses.replace("12/", "DEZEMBRO/");
                    return meses;
                }
            }
        }
        return "";
    }

    public RemessaItemVO consultarUltimoBoletoParcela(MovParcelaVO movParcelaVO) throws Exception {

        String tipoRemessaBoleto = "";
        for (TipoConvenioCobrancaEnum tipoConvEnum : TipoConvenioCobrancaEnum.values()) {
            if (tipoConvEnum.getTipoCobranca().equals(TipoCobrancaEnum.BOLETO) &&
                    tipoConvEnum.getTipoRemessa() != null) {
                tipoRemessaBoleto += ("," + tipoConvEnum.getTipoRemessa().getId());
            }
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT \n");
        sb.append("DISTINCT ri.* \n");
        sb.append("FROM remessaitem ri \n");
        sb.append("INNER JOIN remessa rem on rem.codigo = ri.remessa \n");
        sb.append("INNER JOIN remessaitemmovparcela rimp ON ri.codigo = rimp.remessaitem \n");
        sb.append("where rem.tipo in (").append(tipoRemessaBoleto.replaceFirst(",", "")).append(") \n");
        sb.append("and rimp.movparcela = ? \n");
        sb.append("ORDER BY ri.codigo DESC \n");
        sb.append("LIMIT 1;");
        try (PreparedStatement stm = con.prepareStatement(sb.toString())) {
            int i = 0;
            stm.setInt(++i, movParcelaVO.getCodigo());
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, con, Uteis.NIVELMONTARDADOS_TODOS);
                }
            }
        }
        return null;
    }

    public boolean remessaItemVinculadaVariasParcelas(RemessaItemVO remessaItemVO)throws Exception{
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery("select count(*) as total from remessaItemMovParcela where remessaItem = " + remessaItemVO.getCodigo())) {
                if (rs.next()) {
                    return rs.getInt("total") > 1;
                }
            }
        }
        return false;
    }

    @Override
    public String alterarProps(Integer codigoRemessaItem, String propriedadeAdicionar) throws  Exception{
        String props;
        try (ResultSet rs = con.prepareStatement("SELECT props FROM remessaitem WHERE codigo = " + codigoRemessaItem).executeQuery()) {
            rs.next();
            props = rs.getString("props");
        }
        props = props.substring(0, props.length() - 1) + ", " + propriedadeAdicionar + "}";
        try (PreparedStatement ps = con.prepareStatement("UPDATE remessaitem set props = ? WHERE codigo = ?")) {
            ps.setString(1, props);
            ps.setInt(2, codigoRemessaItem);
            ps.executeUpdate();
        }
        return props;
    }

    @Override
    public List<RemessaItemVO> consultarIdentificadores(String codIdentificadores, Integer codigoConvenio, int nivelmontardadosDados) throws Exception {
        String sql = "SELECT ri.* FROM remessaitem ri INNER JOIN remessa r ON r.codigo = ri.remessa WHERE ri.identificador IN ("+codIdentificadores+")  AND r.conveniocobranca = "+codigoConvenio+" order by ri.identificador";
        return montarDadosConsulta(criarConsulta(sql, con), con, nivelmontardadosDados);
    }


    public void totalizadorBIResultadoDCC(TotalizadorRemessaTO totalizador, Integer codigoEmpresa,
                                                   Date dataInicio, Date dataFim,
                                                   String nrTentativas, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                                   boolean somenteMes, boolean somenteForaMes, List<Integer> convenios, String nrTentativasFiltrar) throws Exception {

        StringBuilder sql = new StringBuilder("");

        sql.append("SELECT \n");
        sql.append("codigo as movparcela, \n");
        sql.append("valorparcela::NUMERIC as valor, datavencimento \n");
        sql.append("FROM ( \n");
        sql.append("select \n");
        sql.append("distinct(mpa.codigo), \n");
        sql.append("mpa.valorparcela, mpa.datavencimento, \n");
        sql.append("(select max(nrtentativa) from (\n");
        sql.append("select  nrtentativa from (select coalesce(nrtentativaparcela,0) as nrtentativa from remessaitem where movparcela =mpa.codigo  order by nrtentativaparcela desc limit 1) as foo \n");
        sql.append(" union all \n");
        sql.append("select  nrtentativa from (select coalesce(nrtentativaparcela,0) as nrtentativa from remessaitemmovparcela where movparcela =  mpa.codigo order by nrtentativaparcela desc limit 1 ) as foo \n");
        sql.append(" ) as foo) as nrtentativaparcela \n");
        sql.append("from remessaitem  item \n");
        sql.append("left join remessaitemmovparcela itempar on itempar.remessaitem = item.codigo \n");
        sql.append("inner join remessa re on re.codigo =item.remessa \n");
        sql.append("inner join empresa emp on emp.codigo = re.empresa \n");
        sql.append("inner join movparcela mpa on mpa.codigo = item.movparcela or mpa.codigo =itempar.movparcela \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = re.conveniocobranca \n");
        sql.append("left join pagamentomovparcela pmp on pmp.movparcela = mpa.codigo \n");
        sql.append("left join movpagamento mp on mp.codigo = pmp.movpagamento \n");
        sql.append("left join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("WHERE 1 = 1 \n");
        sql.append("AND re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("AND re.empresa = ").append(codigoEmpresa).append(" \n");
        }

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("AND mpa.situacao ").append(situacaoParcela).append(" \n");
        }

        if (!UteisValidacao.emptyString(somenteConvenio)) {
            sql.append(somenteConvenio).append("\n");
        }

        if (!UteisValidacao.emptyString(nrTentativas)) {
            sql.append("AND (item.nrtentativaparcela ").append(nrTentativas).append(" or itempar.nrtentativaparcela  ").append(nrTentativas).append(")\n");
        }

        if (!UteisValidacao.emptyList(convenios)) {
            sql.append("AND re.conveniocobranca IN ( ").append(Uteis.montarListaIN(convenios)).append(" ) \n");
        }

        if (!UteisValidacao.emptyString(situacaoRemessa)) {
            sql.append(situacaoRemessa).append(" \n");
        }

        if (!((somenteForaMes && somenteMes) || (!somenteMes && !somenteForaMes))) {
            if (somenteMes) {
                sql.append("AND mpa.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
            }

            if (somenteForaMes) {
                sql.append("AND mpa.datavencimento::date < '").append(Uteis.getData(dataInicio)).append("' \n");
            }
        }

        sql.append(") as foo \n");

        if (!UteisValidacao.emptyString(nrTentativasFiltrar)) {
            sql.append(" where nrtentativaparcela "  + nrTentativasFiltrar);
        }

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    MovParcelaVO movParcelaVO = new MovParcelaVO();
                    movParcelaVO.setCodigo(rs.getInt("movparcela"));
                    movParcelaVO.setValorParcela(rs.getDouble("valor"));
                    movParcelaVO.setDataVencimento(rs.getDate("datavencimento"));
                    if (!totalizador.getListaParcelas().contains(movParcelaVO)) {
                        totalizador.getListaParcelas().add(movParcelaVO);
                    }
                    //            totalizador.setQuantidade(totalizador.getQuantidade() + rs.getInt("qtd"));
                    //            totalizador.setValor(totalizador.getValor() + Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                }
            }
        }
        totalizador.processarQtdValor();
    }


    public void preencherTotalizadorBIResultadoDCC(TotalizadorBIDCCEnum totalizadorBIDCCEnum, TotalizadorRemessaTO totalizadorRemessaTO, Integer codigoEmpresa, Date dataInicio, Date dataFim,  boolean incluirCancelados, boolean somenteMes, boolean somenteForaMes, List<Integer> convenios) throws Exception {

        StringBuilder sql = new StringBuilder("");
        sql.append("select count(movparcela) as qtd, sum(valorparcela) as soma from ( \n");
        sql.append("select \n");
        sql.append("mpa.codigo as movparcela, \n");
        sql.append("mpa.valorparcela, \n");
        sql.append("mpa.situacao, \n");
        sql.append("exists (select codigo from pagamentomovparcela  where movparcela  = item.movparcela) as estaPago, \n");
        sql.append("exists (select codigo from formapagamento where codigo = mp.formapagamento and conveniocobranca is not null) pagoConvenio, \n");
        sql.append("(select nrtentativaparcela from remessaitem where movparcela = item.movparcela order by codigo desc limit 1) as nrtentativas, \n");
        sql.append("(select situacaoRemessa from remessa where codigo in (select remessa from remessaitem where movparcela = item.movparcela order by codigo desc limit 1)) as situacaoRemessa \n");
        sql.append("from remessaitem item \n");
        sql.append("inner join remessa re on re.codigo =item.remessa \n");
        sql.append("inner join empresa emp on emp.codigo = re.empresa \n");
        sql.append("inner join movparcela mpa on mpa.codigo = item.movparcela \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = re.conveniocobranca \n");
        sql.append("left join pagamentomovparcela pmp on pmp.movparcela = item.movparcela \n");
        sql.append("left join movpagamento mp on mp.codigo = pmp.movpagamento \n");
        sql.append("where 1 = 1 \n");
        sql.append("and item.movparcela IS NOT NULL \n");
        sql.append("and mpa.situacao not in ('RG') \n");
        sql.append("and re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("AND re.empresa = ").append(codigoEmpresa).append(" \n");
        }

        if (!UteisValidacao.emptyList(convenios)) {
            sql.append("AND re.conveniocobranca IN ( ").append(Uteis.montarListaIN(convenios)).append(" ) \n");
        }

        if (!((somenteForaMes && somenteMes) || (!somenteMes && !somenteForaMes))) {
            if (somenteMes) {
                sql.append("AND mpa.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
            }

            if (somenteForaMes) {
                sql.append("AND mpa.datavencimento::date < '").append(Uteis.getData(dataInicio)).append("' \n");
            }
        }

        sql.append("group by 1,2,3,4,5,6,7 \n");
        sql.append("order by 1) as foo \n");
        sql.append("where 1 = 1 \n");

        if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.ENVIADAS)) {
            sql.append("and nrtentativas = 1 \n");
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.PAGAS_PELO_CONVENIO)) {
            sql.append("and pagoConvenio = true \n");

            if (incluirCancelados) {
                sql.append("and situacao in ('PG','CA') \n");
            } else {
                sql.append("and situacao = 'PG' \n");
            }
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.PAGAS_FORA_CONVENIO)) {
            sql.append("and situacao = 'PG' \n");
            sql.append("and pagoConvenio = false \n");
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.NAO_PAGAS)) {
            sql.append("and situacaoRemessa not in (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + ") \n");
            if (incluirCancelados) {
                sql.append("and situacao in ('EA','CA') \n");
            } else {
                sql.append("and situacao = 'EA' \n");
            }
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.AGUARDANDO_RETORNO)) {
            sql.append("and situacao = 'EA' \n");
            sql.append("and situacaoRemessa in (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + ") \n");
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.PAGAS_PRIMEIRA_TENTATIVA)) {
            sql.append("and situacao = 'PG' \n");
            sql.append("and pagoConvenio = true \n");
            sql.append("and nrtentativas = 1 \n");
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.PAGAS_SEGUNDA_TENTATIVA)) {
            sql.append("and situacao = 'PG' \n");
            sql.append("and pagoConvenio = true \n");
            sql.append("and nrtentativas = 2 \n");
        } else if (totalizadorBIDCCEnum.equals(TotalizadorBIDCCEnum.PAGAS_DEMAIS_TENTATIVAS)) {
            sql.append("and situacao = 'PG' \n");
            sql.append("and pagoConvenio = true \n");
            sql.append("and nrtentativas > 2 \n");
        }


        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    totalizadorRemessaTO.setQuantidade(rs.getInt("qtd"));
                    totalizadorRemessaTO.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("soma")));
                    ;
                }
            }
        }
    }

    @Override
    public RemessaItemVO consultarPorIdentificador(final int identificador, final int nivel) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE identificador = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, identificador);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados (RemessaItem identificador: %s)", identificador));
                }
                return (montarDados(tabelaResultado, con, nivel));
            }
        }
    }

    @Override
    public void limparDadosRetorno(RemessaItemVO item) throws Exception {
        String sql = "UPDATE remessaitem SET props = '" + item.getProps().toString() + "', movpagamento = null, codigosretorno = '' WHERE codigo = " + item.getCodigo();
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.execute();
        }
    }

    @Override
    public void estornarPagamentoBoleto(RemessaItemVO item, String key, UsuarioVO usuarioVO) throws Exception {
        ReciboPagamento reciboPagamentoDAO = null;
        MovPagamento movPagamentoDAO = null;
        MovParcela movParcelaDAO = null;
        MovProdutoParcela movProdutoParcelaDAO = null;
        try {
            con.setAutoCommit(false);
            reciboPagamentoDAO = new ReciboPagamento(con);
            movPagamentoDAO = new MovPagamento(con);
            movParcelaDAO = new MovParcela(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);

            limparDadosRetorno(item);

            Integer codigoRecibo = item.getMovPagamento().getReciboPagamento().getCodigo();

            ReciboPagamentoVO recibo = reciboPagamentoDAO.consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TODOS);
            EstornoReciboVO estornoReciboVO = new EstornoReciboVO();
            estornoReciboVO.setReciboPagamentoVO(recibo);
            estornoReciboVO.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            estornoReciboVO.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            estornoReciboVO.setResponsavelEstornoRecivo(usuarioVO);

            //consulta as transações de cartão de crédito relacionadas aos contratos do recibo
            estornoReciboVO.montarListaTransacoes(estornoReciboVO.getListaMovParcela(), con);

            reciboPagamentoDAO.estornarReciboPagamentoSemCommit(estornoReciboVO, movPagamentoDAO, movProdutoParcelaDAO, new CaixaVO(), key, null);
            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            throw e;
        } finally {
            reciboPagamentoDAO = null;
            movPagamentoDAO = null;
            movParcelaDAO = null;
            movProdutoParcelaDAO = null;
            con.setAutoCommit(true);
        }
    }

    public void totalizadorPorConvenio(TotalizadorRemessaTO totalizador, Integer codigoEmpresa, Date dataInicio, Date dataFim, Integer convenio) throws Exception {
        String sql = sqlTotalizadorPorConvenio(codigoEmpresa, dataInicio, dataFim, convenio, false);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    totalizador.setQuantidade(rs.getInt("qtd"));
                    totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                }
            }
        }
        totalizadorPorConvenioPorNrTentativa(totalizador, codigoEmpresa, dataInicio, dataFim, convenio);
    }

    private void totalizadorPorConvenioPorNrTentativa(TotalizadorRemessaTO totalizadorPai, Integer codigoEmpresa, Date dataInicio, Date dataFim, Integer convenio) throws Exception {
        String sql = sqlTotalizadorPorConvenio(codigoEmpresa, dataInicio, dataFim, convenio, true);
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorRemessaTO totalizador = new TotalizadorRemessaTO();
                    Integer nrTentativa = rs.getInt("nrtentativas");
                    totalizador.setLabel(nrTentativa + "ª Tentativa");
                    totalizador.setQuantidade(rs.getInt("qtd"));
                    totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    totalizador.calcularPorcentagem(totalizadorPai.getValor());
                    totalizadorPai.getListaFilhoTotalizadorRemessaTO().add(totalizador);
                }
            }
        }
    }

    private String sqlTotalizadorPorConvenio(Integer codigoEmpresa,
                                       Date dataInicio, Date dataFim, Integer convenio, boolean porNrTentativa) throws Exception {

        StringBuilder sql = new StringBuilder("");

        if (porNrTentativa) {
            sql.append("select \n");
            sql.append("count(distinct(mpa.codigo)) as qtd, \n");
            sql.append("sum(mpa.valorparcela) as valor , \n");
            sql.append("(select nrtentativaparcela from movparcelatentativaconvenio where movparcela =  item.movparcela order by nrtentativaparcela desc limit 1) as nrtentativas \n");
        } else {
            sql.append("SELECT \n");
            sql.append("count(codigo) as qtd, \n");
            sql.append("sum(valorparcela:: NUMERIC) as valor \n");
            sql.append("FROM ( \n");
            sql.append("select \n");
            sql.append("distinct(mpa.codigo), \n");
            sql.append("mpa.valorparcela, \n");
            sql.append("(select nrtentativaparcela from remessaitem where movparcela =  item.movparcela order by nrtentativaparcela desc limit 1) as nrtentativaparcela \n");
        }
        sql.append("from remessaitem  item \n");
        sql.append("inner join remessa re on re.codigo =item.remessa \n");
        sql.append("inner join empresa emp on emp.codigo = re.empresa \n");
        sql.append("inner join movparcela mpa on mpa.codigo = item.movparcela \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = re.conveniocobranca \n");
        sql.append("left join pagamentomovparcela pmp on pmp.movparcela = item.movparcela \n");
        sql.append("left join movpagamento mp on mp.codigo = pmp.movpagamento \n");
        sql.append("left join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("WHERE 1 = 1 \n");
        sql.append("AND item.movparcela IS NOT NULL \n");
        sql.append("AND re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
        sql.append("AND mpa.situacao = 'PG' \n");
        sql.append("AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("AND re.empresa = ").append(codigoEmpresa).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(convenio)) {
            sql.append("AND re.conveniocobranca IN ( ").append(convenio).append(" ) \n");
        }

        if (porNrTentativa) {
            sql.append("group by 3 \n");
            sql.append("order by 3 \n");
        } else {
            sql.append(") as foo \n");
        }

        return sql.toString();
    }

    public List<RemessaItemVO> consultarPorMovPagamento(final int codigoMovPagamento, final int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM remessaitem WHERE movpagamento = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoMovPagamento);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, con, nivelMontarDados);
            }
        }
    }

    @Override
    public void excluirPorCodigoRemessa(int codigoRemessa) throws SQLException {
        String sql = "DELETE FROM remessaitem WHERE remessa = "+codigoRemessa;
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.execute();
        }
    }

    public void marcarRemessaComoContabilizadaPacto(boolean contabilizada, RemessaVO remessa) throws Exception {
        if (remessa != null && !UteisValidacao.emptyNumber(remessa.getCodigo())) {
            String sql = "UPDATE remessaitem SET contabilizadapacto = ? WHERE remessa = ?";
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setBoolean(1, contabilizada);
                stm.setInt(2, remessa.getCodigo());
                stm.execute();
            }
        } else if (remessa != null && !UteisValidacao.emptyList(remessa.getListaItens())) {
            for (RemessaItemVO item : remessa.getListaItens()) {
                item.setContabilizadaPacto(contabilizada);
            }
        }
    }

    public void alterarJsonEstorno(RemessaItemVO obj) throws Exception {
        String sql = "update remessaitem set jsonEstorno = ? where codigo = ?;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, obj.getJsonEstorno());
            ps.setInt(2, obj.getCodigo());
            ps.execute();
        }
    }

    public Integer consultarQtdParcelasItem(Integer remessaItem) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select max(qtd) as qtd from ( \n");
        sql.append("select  \n");
        sql.append("'novo' as tipo, \n");
        sql.append("count(rim.codigo) as qtd \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa \n");
        sql.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sql.append("where r.novoformato  \n");
        sql.append("and ri.codigo = ").append(remessaItem).append(" \n");
        sql.append("union \n");
        sql.append("select  \n");
        sql.append("'ant' as tipo, \n");
        sql.append("1 as qtd \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa \n");
        sql.append("where r.novoformato = false \n");
        sql.append("and ri.codigo = ").append(remessaItem).append(") as sql \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("qtd");
                } else {
                    return 0;
                }
            }
        }
    }

    public JBoleto obterJBoleto(RemessaItemVO remessaItem, Connection con) throws Exception {
        RemessaService service = new RemessaService(con);
        boolean layout240 = (remessaItem.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                remessaItem.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
        JBoletoBean boletoBean = service.gerarBoletoCobranca(remessaItem);
        processarNossoNumero(remessaItem, boletoBean);
        JBoleto boleto = new JBoleto(null, boletoBean, remessaItem.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);
        return boleto;
    }

    private void processarNossoNumero(RemessaItemVO remessaItemVO, JBoletoBean boletoBean) {
        if (remessaItemVO.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO) &&
                remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
            return;
        }
        if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICREDI) ||
                remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_SICREDI)){
            boletoBean.setNossoNumero(BancoSicredi.gerarNossoNumero(remessaItemVO.getIdentificador(), 2, Integer.parseInt(boletoBean.getAgencia()),
                    Integer.parseInt(boletoBean.getDvAgencia()), Integer.parseInt(boletoBean.getContaCorrente())));
        }else if(remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL)){
            String formataNossoNumero = String.format("%010d", remessaItemVO.getIdentificador());
            boletoBean.setNossoNumero(formataNossoNumero);
        }else if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)){
            boletoBean.setNossoNumero(remessaItemVO.getIdentificador().toString());
        } else if (!remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_CAIXA)
                && !remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            boletoBean.setNossoNumero(remessaItemVO.getCodigo().toString());
        }
    }
}
