package negocio.comuns.utilitarias;

import br.com.pactosolucoes.ce.comuns.to.BinarioTO;

import java.util.ArrayList;
import java.util.Hashtable;

/**
 * @deprecated
 * Classe estática responsável por manter os domínios de valores utilizados pela
 * aplicação. Um domínio define um conjunto de valores que um determinado
 * atributo pode assumir. Exemplo de domínios são: Estados Federativos; Tipos de
 * Sexo; Estado Cívil. Ao alterar um domínio neste classe, o valor modificado
 * estará automaticamente disponível em toda a aplicação.
 */
@Deprecated
public abstract class Dominios {

    protected static Hashtable<String, String> tipoPessoa = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoContrato = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoPlanoTextoPadrao = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoPlanoTextoPadrao = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoPagamento = new Hashtable<String, String>();
    protected static Hashtable<String, String> formaRecebimentoParceiro = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoProduto = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoVigencia = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoPergunta = new Hashtable<String, String>();
    //    protected static Hashtable<String, String> tipoTelefone = new Hashtable<String, String>();
//    protected static Hashtable<String, String> tipoEndereco = new Hashtable<String, String>();
//    protected static Hashtable<String, String> tipoCategoria = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoCliente = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoColaborador = new Hashtable<String, String>();
    protected static Hashtable<String, String> nivelAcesso = new Hashtable<String, String>();
    protected static Hashtable<String, String> estadoCivil = new Hashtable<String, String>();
    protected static Hashtable<String, String> escolaridade = new Hashtable<String, String>();
    protected static Hashtable<String, String> simNao = new Hashtable<String, String>();
    protected static Hashtable<String, String> estado = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoHorarioTurma = new Hashtable<String, String>();
    protected static Hashtable<String, String> sexo = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoMovParcela = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoRelativaHistorioco = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoMovimentacao = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoValor = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoOperacao = new Hashtable<String, String>();
    protected static Hashtable<String, String> grauDeInstrucao = new Hashtable<String, String>();
    protected static Hashtable<String, String> diaSemana = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorCliente = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorVenda = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorItensVenda = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorPacoteVenda = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorEmpresa = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorTurma = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorFormaPagto = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorContrato = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorUsuario = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorPlano = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorComposicao = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorModalidade = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorMovParcela = new Hashtable<String, String>();
    protected static Hashtable<String, String> valorDesejado = new Hashtable<String, String>();
    protected static Hashtable<String, String> cpfCnpj = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoOperacaoContrato = new Hashtable<String, String>();
    protected static Hashtable<String, String> vinculos = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoComprador = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoRenovacaoContrado = new Hashtable<String, String>();
    protected static Hashtable<String, String> situacaoContradoOperacoesBasicas = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoAcessoCliente = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoJustificativaOperacao = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoCompradorAulaAvulsa = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorMovPagamento = new Hashtable<String, String>();
    protected static Hashtable<String, String> marcadorReciboPagamento = new Hashtable<String, String>();
    protected static Hashtable<String, String> tipoMensagem = new Hashtable<String, String>();
    protected static ArrayList modeloMensagemEmail = new ArrayList();
    protected static ArrayList modeloMensagemSMS = new ArrayList();
    private static Hashtable<String, String> tipoVisao = new Hashtable<String, String>();
    private static Hashtable<String, String> tipoAgendamento = new Hashtable<String, String>();
    private static Hashtable<String, String> tipoGrupo = new Hashtable<String, String>();
    private static Hashtable<String, String> tipoStatus = new Hashtable<String, String>();
    private static Hashtable<String, String> tipoContatoHistoricoContato = new Hashtable<String, String>();
    private static Hashtable<String, String> tipoOperacaoHistoricoContato = new Hashtable<String, String>();
    private static Hashtable<String, String> horas = new Hashtable<String, String>();
    private static Hashtable<String, String> identificadorMeta = new Hashtable<String, String>();
    private static Hashtable<String, String> minutos = new Hashtable<String, String>();
    private static Hashtable<String, String> faseAtual = new Hashtable<String, String>();
    private static Hashtable<String, String> marcadoEmail = new Hashtable<String, String>();
    private static Hashtable<String, String> tipoGrupoObjecao = new Hashtable<String, String>();
    private static Hashtable<String, String> genero = new Hashtable<String, String>();

    protected static Hashtable<String, String> necessidadesEspeciaisSesiCe = new Hashtable<String, String>();

    protected static Hashtable<String, String> statusMatriculaSesiCe = new Hashtable<String, String>();

    static {
        inicializarDominioTipoMensagem();
        inicializarDominioModeloMensagemEmail();
        inicializarDominioModeloMensagemSMS();
        inicializarDominioTipoPessoa();
        inicializarDominioSituacaoContrato();
        inicializarDominioTipoPagamento();
        inicializarDominioFormaRecebimentoParceiro();
        inicializarDominioTipoProduto();
        inicializarDominioTipoVigencia();
        inicializarDominioTipoPergunta();
        inicializarDominioSituacaoCliente();
        inicializarDominioSituacaoColaborador();
        inicializarDominioNivelAcesso();
        inicializarDominioEstadoCivil();
        inicializarDominioEscolaridade();
        inicializarDominioSimNao();
        inicializarDominioEstado();
        inicializarDominioSituacaoHorarioTurma();
        inicializarDominioSexo();
        inicializarDominioGenero();
        inicializarDominioSituacaoMovParcela();
        inicializarDominioSituacaoRelativaHistorioco();
        inicializarDominioGrauDeInstrucao();
        inicializarDominioTipoMovimentacao();
        inicializarDominioTipoValor();
        inicializarDominioTipoOperacao();
        inicializarDominioDiaSemana();
        inicializarDominioMarcadorCliente();
        inicializarDominioMarcadorVenda();
        inicializarDominioMarcadorItensVenda();
        inicializarDominioMarcadorPacoteVenda();
        inicializarDominioMarcadorTurma();
        inicializarDominioMarcadorFormaPagto();
        inicializarDominioMarcadorComposicao();
        inicializarDominioMarcadorModalidade();
        inicializarDominioMarcadorMovParcela();
        inicializarDominioMarcadorEmpresa();
        inicializarDominioMarcadorContrato();
        inicializarDominioMarcadorUsuario();
        inicializarDominioMarcadorPlano();
        inicializarDominioSituacaoPlanoTextoPadrao();
        inicializarDominioTipoPlanoTextoPadrao();
        inicializarDominioValorDesejado();
        inicializarDominioCpfCnpj();
        inicializarDominioTipoOperacaoContrato();
        inicializarDominioVinculos();
        inicializarDominioTipoComprador();
        inicializarDominioSituacaoRenovacaoContrato();
        inicializarDominioSituacaoContratoOperacoesBasicas();
        inicializarDominioTipoAcessoCliente();
        inicializarDominioTipoJustificativaOperacao();
        inicializarDominioTipoCompradorAulaAvulsa();
        inicializarDominioMarcadorMovPagamento();
        inicializarDominioMarcadorReciboPagamento();
        inicializarDominioTipoVisao();
        inicializarDominioTipoAgendamento();
        inicializarDominioTipoGrupo();
        inicializarDominioTipoStatus();
        inicializarDominioTipoContato();
        inicializarDominioHoras();
        inicializarDominioIdentificadorMeta();
        inicializarDominioMinutos();
        inicializarDominioTipoFaseAtual();
        inicializarDominioTipoOperacaoHistoricoContato();
        inicializarDominioMarcadorEmail();
        inicializarDominioTipoGrupoObjecao();
        inicializarDominioNecessidadesEspeciaisSesiCe();
        inicializarDominioStatusMatriculaSesiCe();
    }

    private Dominios() {

    }

    private static void inicializarDominioMinutos() {
        getMinutos().put("00", "00");
        getMinutos().put("01", "01");
        getMinutos().put("02", "02");
        getMinutos().put("03", "03");
        getMinutos().put("04", "04");
        getMinutos().put("05", "05");
        getMinutos().put("06", "06");
        getMinutos().put("07", "07");
        getMinutos().put("08", "08");
        getMinutos().put("09", "09");
        getMinutos().put("10", "10");
        getMinutos().put("11", "11");
        getMinutos().put("12", "12");
        getMinutos().put("13", "13");
        getMinutos().put("14", "14");
        getMinutos().put("15", "15");
        getMinutos().put("16", "16");
        getMinutos().put("17", "17");
        getMinutos().put("18", "18");
        getMinutos().put("19", "19");
        getMinutos().put("20", "20");
        getMinutos().put("21", "21");
        getMinutos().put("22", "22");
        getMinutos().put("23", "23");
        getMinutos().put("24", "24");
        getMinutos().put("25", "25");
        getMinutos().put("26", "26");
        getMinutos().put("27", "27");
        getMinutos().put("28", "28");
        getMinutos().put("29", "29");
        getMinutos().put("30", "30");
        getMinutos().put("31", "31");
        getMinutos().put("32", "32");
        getMinutos().put("33", "33");
        getMinutos().put("34", "34");
        getMinutos().put("35", "35");
        getMinutos().put("36", "36");
        getMinutos().put("37", "37");
        getMinutos().put("38", "38");
        getMinutos().put("39", "39");
        getMinutos().put("40", "40");
        getMinutos().put("41", "41");
        getMinutos().put("42", "42");
        getMinutos().put("43", "43");
        getMinutos().put("44", "44");
        getMinutos().put("45", "45");
        getMinutos().put("46", "46");
        getMinutos().put("47", "47");
        getMinutos().put("48", "48");
        getMinutos().put("49", "49");
        getMinutos().put("50", "50");
        getMinutos().put("51", "51");
        getMinutos().put("52", "52");
        getMinutos().put("53", "53");
        getMinutos().put("54", "54");
        getMinutos().put("55", "55");
        getMinutos().put("56", "56");
        getMinutos().put("57", "57");
        getMinutos().put("58", "58");
        getMinutos().put("59", "59");

    }

    private static void inicializarDominioIdentificadorMeta() {
        getIdentificadorMeta().put("PV", "Pós Venda");
        getIdentificadorMeta().put("AG", "Agendamentos");
        getIdentificadorMeta().put("RE", "Renovação");
        getIdentificadorMeta().put("HO", "24 Horas");
        getIdentificadorMeta().put("AN", "Aniversariantes");
        getIdentificadorMeta().put("FA", "Faltosos");
        getIdentificadorMeta().put("PE", "Desistentes");
        getIdentificadorMeta().put("RI", "Grupo de Risco");
        getIdentificadorMeta().put("IN", "Indicações");
        getIdentificadorMeta().put("CP", "Passivo");
        getIdentificadorMeta().put("MF", "Faturamento");
        getIdentificadorMeta().put("MQ", "Qtde de Vendas");
    }

    private static void inicializarDominioHoras() {
        getHoras().put("06", "06");
        getHoras().put("07", "07");
        getHoras().put("08", "08");
        getHoras().put("09", "09");
        getHoras().put("10", "10");
        getHoras().put("11", "11");
        getHoras().put("12", "12");
        getHoras().put("13", "13");
        getHoras().put("14", "14");
        getHoras().put("15", "15");
        getHoras().put("16", "16");
        getHoras().put("17", "17");
        getHoras().put("18", "18");
        getHoras().put("19", "19");
        getHoras().put("20", "20");
        getHoras().put("21", "21");
        getHoras().put("22", "22");
        getHoras().put("23", "23");
    }

    private static void inicializarDominioTipoContato() {
        getTipoContatoHistoricoContato().put("TE", "Contato Telefônico");
        getTipoContatoHistoricoContato().put("WA", "Contato WhatsApp");
        getTipoContatoHistoricoContato().put("EM", "Contato E-mail");
        getTipoContatoHistoricoContato().put("PE", "Contato Pessoal");
        getTipoContatoHistoricoContato().put("LC", "Ligação sem contato");
        getTipoContatoHistoricoContato().put("CS", "Contato SMS");
        getTipoContatoHistoricoContato().put("AP", "Contato APP");
    }

    private static void inicializarDominioTipoOperacaoHistoricoContato() {
        getTipoOperacaoHistoricoContato().put("RE", "Reagendamento");
        getTipoOperacaoHistoricoContato().put("CO", "Confirmação");
    }

    private static void inicializarDominioTipoStatus() {
        getTipoStatus().put("IN", "Inativo");
        getTipoStatus().put("AT", "Ativo");
    }

    private static void inicializarDominioTipoGrupoObjecao() {
        getTipoGrupoObjecao().put("OB", "Objeção");
        getTipoGrupoObjecao().put("MD", "Objeção de Desistência"); //ESSA OBJEÇÃO FOI REMOVIDA NO TICKET #4927 - NÃO FOI REMOVIDA DO DOMINÍOS PARA NÃO AFETAR OS CLIENTES QUE JA UTILIZAM
        getTipoGrupoObjecao().put("OD", "Objeção Definitiva");
    }

    private static void inicializarDominioTipoAgendamento() {
        getTipoAgendamento().put("LI", "Ligação");
        getTipoAgendamento().put("VI", "Visita");
        getTipoAgendamento().put("AE", "Aula Experimental");
    }

    private static void inicializarDominioTipoFaseAtual() {
        getFaseAtual().put("AG", "Agendamento");
        getFaseAtual().put("CP", "Passivo");
        getFaseAtual().put("IN", "Indicado");
        getFaseAtual().put("HO", "24h");
        getFaseAtual().put("RE", "Renovação");
        getFaseAtual().put("PV", "Pós Venda");
        getFaseAtual().put("AN", "Aniversariante");
        getFaseAtual().put("PE", "Desistentes");
        getFaseAtual().put("RI", "Grupos de Risco");
        getFaseAtual().put("FA", "Faltosos");
    }

    private static void inicializarDominioTipoVisao() {
        getTipoVisao().put("AC", "Acesso a somente seus Clientes");
        getTipoVisao().put("IG", "Acesso a clientes de todos Integrantes do grupo");
        getTipoVisao().put("VI", "Visualizar todas as carteiras");
    }

    private static void inicializarDominioTipoJustificativaOperacao() {
        getTipoJustificativaOperacao().put("CA", "Cancelamento");
        getTipoJustificativaOperacao().put("TR", "Trancamento");
        getTipoJustificativaOperacao().put("CR", "Férias");
        getTipoJustificativaOperacao().put("AT", "Atestado");
        getTipoJustificativaOperacao().put("BO", "Bônus");

    }

    private static void inicializarDominioValorDesejado() {
        getValorDesejado().put("VM", "Valor Mensal");
        getValorDesejado().put("PA", "Parcelas");
    }

    private static void inicializarDominioSituacaoPlanoTextoPadrao() {
        getSituacaoPlanoTextoPadrao().put("IN", "Inativa");
        getSituacaoPlanoTextoPadrao().put("AT", "Ativa");
    }

    private static void inicializarDominioTipoPlanoTextoPadrao() {
        getTipoPlanoTextoPadrao().put("PL", "Plano");
        getTipoPlanoTextoPadrao().put("SE", "Serviço");
        getTipoPlanoTextoPadrao().put("AM", "Armário");
        getTipoPlanoTextoPadrao().put("VO", "Termo de Aceite");
        getTipoPlanoTextoPadrao().put("LP", "Termo de Aceite Link Pagamento");
        getTipoPlanoTextoPadrao().put("CC", "Comprovante de Compra");
    }

    private static void inicializarDominioMarcadorCliente() {
        getMarcadorCliente().put("[(60){}AssinaturaDigital_Cliente]", "AssinaturaDigital");
        getMarcadorCliente().put("[(10){}Matricula_Cliente]", "Matricula_CLiente");
        getMarcadorCliente().put("[(61){}AssinaturaBiometriaDigital_Cliente]", "AssinaturaBiometriaDigital");
        getMarcadorCliente().put("[(30){}Banco_Cliente]", "Banco_CLiente");
        getMarcadorCliente().put("[(20){}Agencia_Cliente]", "Agencia_CLiente");
        getMarcadorCliente().put("[(20){}Conta_Cliente]", "Conta_Cliente");
        getMarcadorCliente().put("[(75){}NowLocationIpVOnline_Cliente]", "NowLocationIpVOnline_Cliente");
        getMarcadorCliente().put("[(50){}Webpage_Cliente]", "Webpage_Cliente");
        getMarcadorCliente().put("[(2){}Sexo_Cliente]", "Sexo_Cliente");
        getMarcadorCliente().put("[(20){}Naturalidade_Cliente]", "Naturalidade_Cliente");
        getMarcadorCliente().put("[(10){}EstadoCivil_Cliente]", "EstadoCivil_Cliente");
        getMarcadorCliente().put("[(20){}Rg_Cliente]", "Rg_Cliente");
        getMarcadorCliente().put("[(14){}Cpf_Cliente]", "Cpf_Cliente");
        getMarcadorCliente().put("[(14){}Cpf_ResponsavelLegal_Cliente]", "Cpf_ResponsavelLegal_Cliente");
        getMarcadorCliente().put("[(20){}DataNasc_Cliente]", "DataNasc_Cliente");
        getMarcadorCliente().put("[(80){}Nome_Cliente]", "Nome_Cliente");
        getMarcadorCliente().put("[(80){}Nome_ResponsavelLegal_Cliente]", "Nome_ResponsavelLegal_Cliente");
        getMarcadorCliente().put("[(10){}Codigo_Cliente]", "Codigo_Cliente");
        getMarcadorCliente().put("[(20){}Telefone_Cliente]", "Telefone_Cliente");
        getMarcadorCliente().put("[(20){}Telefone_Celular_Cliente]", "Telefone_Celular_Cliente");
        getMarcadorCliente().put("[(20){}Telefone_Comercial_Cliente]", "Telefone_Comercial_Cliente");
        getMarcadorCliente().put("[(40){}Endereco_Cliente]", "Endereco_Cliente");
        getMarcadorCliente().put("[(5){}Endereco_Numero_Cliente]", "Endereco_Numero_Cliente");
        getMarcadorCliente().put("[(20){}Endereco_Cidade_Cliente]", "Endereco_Cidade_Cliente");
        getMarcadorCliente().put("[(20){}Endereco_Estado_Cliente]", "Endereco_Estado_Cliente");
        getMarcadorCliente().put("[(10){}CEP_Cliente]", "CEP_Cliente");
        getMarcadorCliente().put("[(40){}Profissao_Cliente]", "Profissao_Cliente");
        getMarcadorCliente().put("[(40){}Mae_Cliente]", "Mae_Cliente");
        getMarcadorCliente().put("[(40){}Pai_Cliente]", "Pai_Cliente");
        getMarcadorCliente().put("[(40){}Responsavel_Cliente]", "Responsavel_Cliente");
        getMarcadorCliente().put("[(14){}Responsavel_Cliente_Cpf]", "Responsavel_Cliente_Cpf");
        getMarcadorCliente().put("[(20){}Responsavel_Cliente_Rg]", "Responsavel_Cliente_Rg");
        getMarcadorCliente().put("[(40){}Responsavel_Cliente_Pai]", "Responsavel_Cliente_Pai");
        getMarcadorCliente().put("[(40){}Responsavel_Cliente_Pai_Cpf]", "Responsavel_Cliente_Pai_Cpf");
        getMarcadorCliente().put("[(20){}Responsavel_Cliente_Pai_Rg]", "Responsavel_Cliente_Pai_Rg");
        getMarcadorCliente().put("[(40){}Responsavel_Cliente_Mae]", "Responsavel_Cliente_Mae");
        getMarcadorCliente().put("[(40){}Responsavel_Cliente_Mae_Cpf]", "Responsavel_Cliente_Mae_Cpf");
        getMarcadorCliente().put("[(40){}Responsavel_Financeiro_Nome_Cliente]", "Responsavel_Financeiro_Nome_Cliente");
        getMarcadorCliente().put("[(40){}Responsavel_Financeiro_Cpf_Cliente]", "Responsavel_Financeiro_Cpf_Cliente");
        getMarcadorCliente().put("[(20){}Responsavel_Financeiro_Rg_Cliente]", "Responsavel_Financeiro_Rg_Cliente");
        getMarcadorCliente().put("[(40){}Responsavel_Financeiro_Email_Cliente]", "Responsavel_Financeiro_Email_Cliente");
        getMarcadorCliente().put("[(40){}ComplementoEnd_Cliente]", "ComplementoEnd_Cliente");
        getMarcadorCliente().put("[(40){}Email_Cliente]", "Email_Cliente");
        getMarcadorCliente().put("[(50){}Grupo_Cliente]", "Grupo_Cliente");
        getMarcadorCliente().put("[(250){}AutorizacaoCobranca_Cliente]", "AutorizacaoCobranca_Cliente");
        getMarcadorCliente().put("[(250){}Observacao_Cliente]", "Observacao_Cliente");
        getMarcadorCliente().put("[(50){}Categoria_Cliente]", "Categoria_Cliente");
        getMarcadorCliente().put("[(50){}BairroEnd_Cliente]", "BairroEnd_Cliente");
        getMarcadorCliente().put("[(50){}ConsultorAtual_Cliente]", "ConsultorAtual_Cliente");
        getMarcadorCliente().put("[(80){}Responsavel_Cliente_Cpf_Nome]", "Responsavel_Cliente_Cpf_Nome");
        getMarcadorCliente().put("[(20){}Habilitacao_Sesc_Cliente]", "Habilitacao_Sesc_Cliente");
        getMarcadorCliente().put("[(30){}Contato_Emergencia_Cliente]", "Contato_Emergencia_Cliente");
        getMarcadorCliente().put("[(20){}Telefone_Emergencia_Cliente]", "Telefone_Emergencia_Cliente");

        getMarcadorCliente().put("[(50){}Bandeira_Cartao_Cliente]", "Bandeira_Cartao_Cliente");
        getMarcadorCliente().put("[(50){}Nome_Titular_Cartao_Cliente]", "Nome_Titular_Cartao_Cliente");
        getMarcadorCliente().put("[(100){}Numero_Cartao_Cliente]", "Numero_Cartao_Cliente");
        getMarcadorCliente().put("[(7){}aValidade_Cartao_Cliente]", "aValidade_Cartao_Cliente");
        getMarcadorCliente().put("[(14){}CPF_Titular_Cartao_Cliente]", "CPF_Titular_Cartao_Cliente");
    }

    private static void inicializarDominioMarcadorVenda() {
        getMarcadorVenda().put("[(20){}Data_Venda]", "Data_Venda");
        getMarcadorVenda().put("[(15){}ValorTotal_Venda]", "ValorTotal_Venda");
        getMarcadorVenda().put("[(15){}ValorFinal_Venda]", "ValorFinal_Venda");
        getMarcadorVenda().put("[(15){}ValorDesconto_Venda]", "ValorDesconto_Venda");
        getMarcadorVenda().put("[(5){}Codigo_Venda]", "Codigo_Venda");
    }


    private static void inicializarDominioMarcadorItensVenda() {
        getMarcadorItensVenda().put("[(5){}Codigo_Itens]", "Codigo_Itens");
        getMarcadorItensVenda().put("[(150){}Descricao_Itens]", "Descricao_Itens");
        getMarcadorItensVenda().put("[(20){}ValorUnitario_Itens]", "ValorUnitario_Item_Venda");
        getMarcadorItensVenda().put("[(20){}ValorDesconto_Itens]", "ValorDesconto_Item_Venda");
        getMarcadorItensVenda().put("[(20){}ValorFinal_Itens]", "ValorFinal_Item_Venda");
        getMarcadorItensVenda().put("[(20){}Quantidade_Itens]", "Quantidade_Itens");
    }

    private static void inicializarDominioMarcadorPacoteVenda() {
        getMarcadorPacoteVenda().put("[(5){}Codigo_PacoteItem]", "Codigo_PacoteItem");
        getMarcadorPacoteVenda().put("[(50){}Descricao_PacoteItem]", "Descricao_PacoteItem");
        getMarcadorPacoteVenda().put("[(15){}Valor_PacoteItem]", "Valor_PacoteItem");
    }

    private static void inicializarDominioMarcadorEmpresa() {
        getMarcadoEmpresa().put("[(10){}Codigo_Empresa]", "Codigo_Empresa");
        getMarcadoEmpresa().put("[(40){}Assinatura_Empresa]", "Assinatura_Empresa");
        getMarcadoEmpresa().put("[(14){}Fax_Empresa]", "Fax_Empresa");
        getMarcadoEmpresa().put("[(50){}Site_Empresa]", "Site_Empresa");
        getMarcadoEmpresa().put("[(50){}Email_Empresa]", "Email_Empresa");
        getMarcadoEmpresa().put("[(14){}TelComercial1_Empresa]", "TelComercial1_Empresa");
        getMarcadoEmpresa().put("[(14){}TelComercial2_Empresa]", "TelComercial2_Empresa");
        getMarcadoEmpresa().put("[(14){}TelComercial3_Empresa]", "TelComercial3_Empresa");
        getMarcadoEmpresa().put("[(20){}InscEstadual_Empresa]", "InscEstadual_Empresa");
        getMarcadoEmpresa().put("[(18){}Cnpj_Empresa]", "Cnpj_Empresa");
        getMarcadoEmpresa().put("[(10){}Cep_Empresa]", "Cep_Empresa");
        getMarcadoEmpresa().put("[(50){}Complemento_Empresa]", "Complemento_Empresa");
        getMarcadoEmpresa().put("[(5){}Numero_Empresa]", "Numero_Empresa");
        getMarcadoEmpresa().put("[(20){}Setor_Empresa]", "Setor_Empresa");
        getMarcadoEmpresa().put("[(50){}Endereco_Empresa]", "Endereco_Empresa");
        getMarcadoEmpresa().put("[(50){}RazaoSocial_Empresa]", "RazaoSocial_Empresa");
        getMarcadoEmpresa().put("[(50){}Nome_Empresa]", "Nome_Empresa");
        getMarcadoEmpresa().put("[(50){}Cidade_Empresa]", "Cidade_Empresa");
        getMarcadoEmpresa().put("[(50){}Estado_Empresa]", "Estado_Empresa");
    }

    private static void inicializarDominioMarcadorContrato() {
        getMarcadorContrato().put("[(10){}Codigo_Contrato]", "Codigo_Contrato");
        getMarcadorContrato().put("[(50){}Responsavel_Contrato]", "Responsavel_Contrato");
        getMarcadorContrato().put("[(50){}ConsultorResponsavel_Contrato]", "ConsultorResponsavel_Contrato");
        getMarcadorContrato().put("[(20){}VigenciaDe_Contrato]", "VigenciaDe_Contrato");
        getMarcadorContrato().put("[(20){}VigenciaAte_Contrato]", "VigenciaAte_Contrato");
        getMarcadorContrato().put("[(20){}VigenciaAteAjustada_Contrato]", "VigenciaAteAjustada_Contrato");
        getMarcadorContrato().put("[(10){}Duracao_Contrato]", "Duracao_Contrato");
        getMarcadorContrato().put("[(20){}Horario_Contrato]", "Horario_Contrato");
        getMarcadorContrato().put("[(15){}ValorBaseCalculo_Contrato]", "ValorBaseCalculo_Contrato");
        getMarcadorContrato().put("[(15){}ValorFinal_Contrato]", "ValorFinal_Contrato");
        getMarcadorContrato().put("[(250){}Observacao_Contrato]", "Observacao_Contrato");
        getMarcadorContrato().put("[(10){}NomeModalidades_Contrato]", "NomeModalidades_Contrato");
        getMarcadorContrato().put("[(250){}NomeCompletoModalidades_Contrato]", "NomeCompletoModalidades_Contrato");
        getMarcadorContrato().put("[(250){}NrVezesNomeCompletoModalidades_Contrato]", "NrVezesNomeCompletoModalidades_Contrato");
        getMarcadorContrato().put("[(10){}valorMensal_Contrato]", "valorMensal_Contrato");
        getMarcadorContrato().put("[(10){}valorMensalDesconto_Contrato]", "valorMensalDesconto_Contrato");
        getMarcadorContrato().put("[(10){}valorMensalAdequado_Contrato]", "valorMensalAdequado_Contrato");
        getMarcadorContrato().put("[(10){}ValorMensalBase_Contrato]", "ValorMensalBase_Contrato");
        getMarcadorContrato().put("[(10){}valorDescontoExtra_Contrato]", "valorDescontoExtra_Contrato");
        getMarcadorContrato().put("[(10){}DtLancamento_Contrato]", "DtLancamento_Contrato");
        getMarcadorContrato().put("[(80){}ValorPorExtenso_Contrato]", "ValorPorExtenso_Contrato");
        getMarcadorContrato().put("[(50){}Convenio_Contrato]", "Convenio_Contrato");
        getMarcadorContrato().put("[(50){}ValorMatricula_Contrato]", "ValorMatricula_Contrato");
        getMarcadorContrato().put("[(50){}ValorRematricula_Contrato]", "ValorRematricula_Contrato");
        getMarcadorContrato().put("[(50){}CondicaoPagamento_Contrato]", "CondicaoPagamento_Contrato");
        getMarcadorContrato().put("[(50){}NomeProduto_Contrato]", "NomeProduto_Contrato");
        getMarcadorContrato().put("[(50){}QtdProduto_Contrato]", "QtdProduto_Contrato");
        getMarcadorContrato().put("[(50){}ValorProduto_Contrato]", "ValorProduto_Contrato");
        getMarcadorContrato().put("[(50){}ValorDescontoProduto_Contrato]", "ValorDescontoProduto_Contrato");
        getMarcadorContrato().put("[(50){}ValorUnitarioProduto_Contrato]", "ValorUnitarioProduto_Contrato");
        getMarcadorContrato().put("[(50){}TabelaProdutos_Contrato]", "TabelaProdutos_Contrato");
        getMarcadorContrato().put("[(250){}ConvenioDescontoResumo_Contrato]", "ConvenioDescontoResumo_Contrato");
        getMarcadorContrato().put("[(2){}diaVencimentoCartao_ContratoRecorrencia]", "diaVencimentoCartao_ContratoRecorrencia");
        getMarcadorContrato().put("[(10){}valorAnuidade_ContratoRecorrencia]", "valorAnuidade_ContratoRecorrencia");
        getMarcadorContrato().put("[(50){}valorTotalSemDesconto_Contrato]", "valorTotalSemDesconto_Contrato");
        getMarcadorContrato().put("[(50){}valorTotalDescontoContrato_Contrato]", "valorTotalDescontoContrato_Contrato");
        getMarcadorContrato().put("[(50){}valorTotalContratoSemDescontoExtra_Contrato]", "valorTotalContratoSemDescontoExtra_Contrato");
        getMarcadorContrato().put("[(50){}valorParcelaMensal_Contrato]", "valorParcelaMensal_Contrato");
        getMarcadorContrato().put("[(10){}quantidadeCreditoTreino_Contrato]", "quantidadeCreditoTreino_Contrato");
        getMarcadorContrato().put("[(10){}totalDias_Contrato]", "totalDias_Contrato");
        getMarcadorContrato().put("[(3){}DiasCarencia_Contrato]", "DiasCarencia_Contrato");
        getMarcadorContrato().put("[(250){}NomeModalidadesNrVezes_Contrato]", "NomeModalidadesNrVezes_Contrato");
        getMarcadorContrato().put("[(15){}ValorAdesao_Contrato]", "ValorAdesao_Contrato");
        getMarcadorContrato().put("[(15){}Saldo_Credito_Contrato]", "Saldo_Credito_Contrato");
        getMarcadorContrato().put("[(50){}ValorCheioMatricula_Contrato]", "ValorCheioMatricula_Contrato");
        getMarcadorContrato().put("[(50){}ValorDescontoMatricula_Contrato]", "ValorDescontoMatricula_Contrato");
        getMarcadorContrato().put("[(50){}ValorCheioRematricula_Contrato]", "ValorCheioRematricula_Contrato");
        getMarcadorContrato().put("[(50){}ValorDescontoRematricula_Contrato]", "ValorDescontoRematricula_Contrato");
        getMarcadorContrato().put("[(10){}valorDescontoAnuidade_ContratoRecorrencia]", "valorDescontoAnuidade_ContratoRecorrencia");
        getMarcadorContrato().put("[(10){}valorFinalAnuidade_ContratoRecorrencia]", "valorFinalAnuidade_ContratoRecorrencia");
    }

    private static void inicializarDominioMarcadorUsuario() {
        getMarcadoUsuario().put("[(10){}Codigo_Usuario]", "Codigo_Usuario");
        getMarcadoUsuario().put("[(100){}Nome_Usuario]", "Nome_Usuario");
        getMarcadoUsuario().put("[(10){}Username_Usuario]", "Username_Usuario");
        getMarcadoUsuario().put("[(60){}Usuario_Assinatura]", "Usuario_Assinatura");

    }

    private static void inicializarDominioMarcadorEmail() {
        getMarcadoEmail().put("<strong> &lt;NOME&gt; </strong>", "Nome");
        getMarcadoEmail().put("ENDERECO_TAG", "Endereço");
        getMarcadoEmail().put("CIDADE_ESTADO_TAG", "Cidade/Estado");
        getMarcadoEmail().put("TELEFONE_TAG", "Telefone");
        getMarcadoEmail().put("WEB_SITE_TAG", "Site");

    }

    private static void inicializarDominioMarcadorPlano() {
        getMarcadorPlano().put("[(10){}Codigo_Plano]", "Codigo_Plano");
        getMarcadorPlano().put("[(50){}Descricao_Plano]", "Descricao_Plano");
        getMarcadorPlano().put("[(200){}Descricao_Plano]", "Descricao_Plano");
        getMarcadorPlano().put("[(20){}VigenciaDe_Plano]", "VigenciaDe_Plano");
        getMarcadorPlano().put("[(20){}VigenciaAte_Plano]", "VigenciaAte_Plano");
        getMarcadorPlano().put("[(20){}IngressoAte_Plano]", "IngressoAte_Plano");

    }

    private static void inicializarDominioMarcadorTurma() {
        getMarcadorTurma().put("[(10){}Codigo_Turma]", "Codigo da Turma");
        getMarcadorTurma().put("[(200){}Descricao_Turma]", "Horário da turma (Completo)");
        getMarcadorTurma().put("[(20){}Identificador_Turma]", "Identificador da Turma");
        getMarcadorTurma().put("[(20){}DataInicioVigencia_Turma]", "Data Inicial da Vigência da Turma");
        getMarcadorTurma().put("[(20){}DataFinalVigencia_Turma]", "Data Final da Vigência da Turma");
        getMarcadorTurma().put("[(5){}IdadeMinima_Turma]", "Idade Mínima da Turma");
        getMarcadorTurma().put("[(5){}IdadeMaxima_Turma]", "Idade Máxima da Turma");
        getMarcadorTurma().put("[(200){}DescricaoCurta_Turma]", "Horário da turma (Resumida)");
    }

    private static void inicializarDominioMarcadorFormaPagto() {
        getMarcadoFormaPagto().put("[(10){}Codigo_FormaPagto]", "Codigo_FormaPagto");
        getMarcadoFormaPagto().put("[(45){}Descricao_FormaPagto]", "Descricao_FormaPagto");
        getMarcadoFormaPagto().put("[(15){}TaxaCartao_FormaPagto]", "TaxaCartao_FormaPagto");
    }

    private static void inicializarDominioMarcadorComposicao() {
        getMarcadorComposicao().put("[(10){}Codigo_Composicao]", "Codigo_Composicao");
        getMarcadorComposicao().put("[(45){}Descricao_Composicao]", "Descricao_Composicao");
        getMarcadorComposicao().put("[(15){}PrecoComposicao_Composicao]", "PrecoComposicao_Composicao");
    }

    private static void inicializarDominioMarcadorModalidade() {
        getMarcadorModalidade().put("[(10){}Codigo_Modalidade]", "Codigo_Modalidade");
        getMarcadorModalidade().put("[(15){}ValorMensal_Modalidade]", "ValorMensal_Modalidade");
        getMarcadorModalidade().put("[(10){}NrVezes_Modalidade]", "NrVezes_Modalidade");
        getMarcadorModalidade().put("[(50){}Nome_Modalidade]", "Nome_Modalidade");
        getMarcadorModalidade().put("[(80){}NomeVezes_Modalidade]", "NomeVezes_Modalidade");
    }

    private static void inicializarDominioMarcadorMovParcela() {
        getMarcadorMovParcela().put("[(10){}Codigo_MovParcela]", "Codigo_MovParcela");
        getMarcadorMovParcela().put("[(10){}Codigo_MovParcela_Sem_Renegociadas]", "Codigo_MovParcela_Sem_Renegociadas");
        getMarcadorMovParcela().put("[(20){}DataVencimento_MovParcela]", "DataVencimento_MovParcela");
        getMarcadorMovParcela().put("[(20){}DataVencimento_MovParcela_Sem_Renegociadas]", "DataVencimento_MovParcela_Sem_Renegociadas");
        getMarcadorMovParcela().put("[(15){}ValorParcela_MovParcela]", "ValorParcela_MovParcela");
        getMarcadorMovParcela().put("[(15){}ValorParcela_MovParcela_Renegociadas]", "ValorParcela_MovParcela_Sem_Renegociadas");
        getMarcadorMovParcela().put("[(15){}PercentualMulta_MovParcela]", "PercentualMulta_MovParcela");
        getMarcadorMovParcela().put("[(15){}PercentualJuro_MovParcela]", "PercentualJuro_MovParcela");
        getMarcadorMovParcela().put("[(60){}Descricao_MovParcela]", "Descricao_MovParcela");
        getMarcadorMovParcela().put("[(60){}Descricao_MovParcela_Sem_Renegociadas]", "Descricao_MovParcela_Sem_Renegociadas");
    }

    private static void inicializarDominioTipoOperacao() {
        getTipoOperacao().put("AC", "Acrescimo");
        getTipoOperacao().put("RE", "Redução");
    }

    private static void inicializarDominioTipoValor() {
        getTipoValor().put("VE", "Valor");
        getTipoValor().put("PD", "Percentual");
    }

    private static void inicializarDominioSituacaoRelativaHistorioco() {
        getSituacaoRelativaHistorioco().put("TR", "Trancado");
        getSituacaoRelativaHistorioco().put("CO", "Congelado");
    }

    private static void inicializarDominioSituacaoMovParcela() {
        getSituacaoMovParcela().put("EA", "Em Aberto");
        getSituacaoMovParcela().put("PG", "Pago");
    }

    private static void inicializarDominioSexo() {
        getSexo().put("F", "Feminino");
        getSexo().put("M", "Masculino");
    }

    private static void inicializarDominioGenero() {
        getGenero().put("AG", "Agênero");
        getGenero().put("FE", "Feminino");
        getGenero().put("MA", "Masculino");
        getGenero().put("NB", "Não-binário");
    }

    private static void inicializarDominioSituacaoHorarioTurma() {
        getSituacaoHorarioTurma().put("IN", "Inativa");
        getSituacaoHorarioTurma().put("AT", "Ativa");
    }

    private static void inicializarDominioEstado() {
        getEstado().put("BA", "BA");
        getEstado().put("RS", "RS");
        getEstado().put("RR", "RR");
        getEstado().put("RO", "RO");
        getEstado().put("RN", "RN");
        getEstado().put("RJ", "RJ");
        getEstado().put("CE", "CE");
        getEstado().put("AP", "AP");
        getEstado().put("MT", "MT");
        getEstado().put("MS", "MS");
        getEstado().put("PR", "PR");
        getEstado().put("GO", "GO");
        getEstado().put("AM", "AM");
        getEstado().put("AL", "AL");
        getEstado().put("SP", "SP");
        getEstado().put("DF", "DF");
        getEstado().put("PI", "PI");
        getEstado().put("AC", "AC");
        getEstado().put("MG", "MG");
        getEstado().put("ES", "ES");
        getEstado().put("PE", "PE");
        getEstado().put("SE", "SE");
        getEstado().put("SC", "SC");
        getEstado().put("MA", "MA");
        getEstado().put("PB", "PB");
        getEstado().put("PA", "PA");
        getEstado().put("TO", "TO");
    }

    private static void inicializarDominioSimNao() {
        getSimNao().put("S", "Sim");
        getSimNao().put("N", "Não");
    }

    private static void inicializarDominioEscolaridade() {
        getEscolaridade().put("PR", "Primário");
        getEscolaridade().put("BA", "Básico");
        getEscolaridade().put("DO", "Doutorado");
        getEscolaridade().put("PD", "Pós-Doutorado");
        getEscolaridade().put("SU", "Superior");
        getEscolaridade().put("PL", "Pós-Graduação Lato-Senso (MBA)");
        getEscolaridade().put("ME", "Mestrado");
    }

    private static void inicializarDominioGrauDeInstrucao() {
        getGrauDeInstrucao().put("GR", "Graduação");
        getGrauDeInstrucao().put("PR", "Pós - Graduação");
        getGrauDeInstrucao().put("MB", "MBA");
        getGrauDeInstrucao().put("DO", "Doutorado");
        getGrauDeInstrucao().put("PD", "Pós - Doutorado");
        getGrauDeInstrucao().put("ME", "Mestrado");
        getGrauDeInstrucao().put("EM", "Ensino Médio");
        getGrauDeInstrucao().put("LD", "Livre Docência");
        getGrauDeInstrucao().put("TE", "Técnico");
    }

    private static void inicializarDominioEstadoCivil() {
        getEstadoCivil().put("S", "Solteiro(a)");
        getEstadoCivil().put("C", "Casado(a)");
        getEstadoCivil().put("A", "Amasiado(a)");
        getEstadoCivil().put("V", "Viúvo(a)");
        getEstadoCivil().put("D", "Divorciado(a)");
        getEstadoCivil().put("P", "Separado(a)");
        getEstadoCivil().put("U", "União estável");
    }

    private static void inicializarDominioNivelAcesso() {
        getNivelAcesso().put("(0)(9)(1)", "Incluir e Consultar");
        getNivelAcesso().put("(0)", "Consultar");
        getNivelAcesso().put("(12)", "Relatorio");
        getNivelAcesso().put("(2)", "Alterar");
        getNivelAcesso().put("(0)(1)(2)(9)(12)", "Total (Sem Excluir)");
        getNivelAcesso().put("(0)(1)(2)(3)(9)(12)", "Total");
        getNivelAcesso().put("(3)", "Excluir");
        getNivelAcesso().put("(1)(9)", "Incluir");
    }

    private static void inicializarDominioSituacaoCliente() {
        getSituacaoCliente().put("AT", "Ativo");
        getSituacaoCliente().put("IN", "Inativo");
        getSituacaoCliente().put("VI", "Visitante");
        getSituacaoCliente().put("TR", "Trancado");
        getSituacaoCliente().put("DE", "Desistentes");
        getSituacaoCliente().put("VE", "Vencidos");
        getSituacaoCliente().put("AV", "A Vencer");
        getSituacaoCliente().put("CA", "Cancelados");

    }

    private static void inicializarDominioSituacaoColaborador() {
        getSituacaoColaborador().put("AT", "Ativo");
        getSituacaoColaborador().put("NA", "Inativo");

    }

    private static void inicializarDominioTipoPergunta() {
        getTipoPergunta().put("ME", "Multipla Escolha");
        getTipoPergunta().put("SE", "Simples  Escolha");
        getTipoPergunta().put("TE", "Textual");
        getTipoPergunta().put("SN", "Sim/Não");
        getTipoPergunta().put("NS", "NPS");
    }

    private static void inicializarDominioTipoVigencia() {
        getTipoVigencia().put("PF", "Período Fixo");
        getTipoVigencia().put("ID", "Intervalo de Dias");
        getTipoVigencia().put("VV", "Vigência Variável");
    }

    private static void inicializarDominioTipoProduto() {
        getTipoProduto().put("MA", "Matrícula");
        getTipoProduto().put("PE", "Produto Estoque");
        getTipoProduto().put("PM", "Plano Mensal");
        getTipoProduto().put("RE", "Rematrícula");
        getTipoProduto().put("RN", "Renovação");
        getTipoProduto().put("SE", "Serviço");
        getTipoProduto().put("CD", "Convênio de Desconto");
        getTipoProduto().put("CP", "Crédito Personal");
        getTipoProduto().put("DE", "Desconto");
        getTipoProduto().put("FR", "Free Pass");
        getTipoProduto().put("AA", "Aula Avulsa");
        getTipoProduto().put("DI", "Diária");
        getTipoProduto().put("TR", "Trancamento");
        getTipoProduto().put("DR", "Desconto Renovação Antecipada");
        getTipoProduto().put("TP", "Taxa de Personal Trainer");
        getTipoProduto().put("TD", "Taxa de Adesão Plano Recorrência");
        getTipoProduto().put("TA", "Taxa de Anuidade Plano Recorrência");
        getTipoProduto().put("SS", "Sessão");
        getTipoProduto().put("AT", "Atestado");
        getTipoProduto().put("AR", "Armário");
        getTipoProduto().put("DS", "Desafio");
        getTipoProduto().put("HM", "App Home Fit");
        getTipoProduto().put("BT", "Bio Totem");
        getTipoProduto().put("CN", "Consulta Nutricional");
        getTipoProduto().put("LC", "Locação");
        getTipoProduto().put("OC", "Ordem de Compra");
        getTipoProduto().put("CT", "Carteirinha");
    }

    private static void inicializarDominioFormaRecebimentoParceiro() {
        getFormaRecebimentoParceiro().put("DC", "Débito em Conta");
        getFormaRecebimentoParceiro().put("VB", "Via Boleto");
    }

    private static void inicializarDominioTipoPagamento() {
        getTipoPagamento().put("CA", "Cartão de Crédito");
        getTipoPagamento().put("CD", "Cartão de Débito");
        getTipoPagamento().put("CH", "Cheque");
        getTipoPagamento().put("AV", "A vista");
        getTipoPagamento().put("CO", "Convênio");
        getTipoPagamento().put("CC", "Crédito em Conta Corrente");
        getTipoPagamento().put("BB", "Boleto Bancário");
        getTipoPagamento().put("PD", "Pagamento Digital");
    }

    private static void inicializarDominioSituacaoContrato() {
        getSituacaoContrato().put("AT", "Ativo");
        getSituacaoContrato().put("AE", "Atestado");
        getSituacaoContrato().put("CA", "Carência");
        getSituacaoContrato().put("FE", "Férias");
        getSituacaoContrato().put("TR", "Trancado");
        getSituacaoContrato().put("CO", "Congelado");
    }

    private static void inicializarDominioTipoMensagem() {
        getTipoMensagem().put("AG", "Agendamento");
        getTipoMensagem().put("HO", "24 Horas");
        getTipoMensagem().put("AN", "Aniversariante");
        getTipoMensagem().put("RE", "Renovação");
        getTipoMensagem().put("PV", "Pos-Venda");
        getTipoMensagem().put("FA", "Falta");
        getTipoMensagem().put("PE", "Desistente");
        getTipoMensagem().put("RI", "Risco");
        getTipoMensagem().put("IN", "Indicação");
        getTipoMensagem().put("PO", "Potencial");
        getTipoMensagem().put("CV", "Cartão de Crédito Vencido");
        getTipoMensagem().put("EC", "Confirmação de Compra por e-mail");

    }

    /**
     * Metodo statico de inicializacao dos tipos de modelos de mensagens
     * <p/>
     * Autor: Pedro Y. Saito
     * Criado em 03/01/2011
     */
    @SuppressWarnings("unchecked")
    private static void inicializarDominioModeloMensagemEmail() {
        int i = 0;

        BinarioTO option = new BinarioTO();
        option.setCodigo("selecione");
        option.setDescricao("Selecione um modelo");
        getModeloMensagemEmail().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("PA");
        option.setDescricao("Padrão");
        getModeloMensagemEmail().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("A1");
        option.setDescricao("Aniversário 1");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A2");
        option.setDescricao("Aniversário 2");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A3");
        option.setDescricao("Aniversário 3");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A4");
        option.setDescricao("Aniversário 4");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A5");
        option.setDescricao("Aniversário 5");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A6");
        option.setDescricao("Aniversário 6");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A7");
        option.setDescricao("Aniversário 7");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A8");
        option.setDescricao("Aniversário 8");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("A9");
        option.setDescricao("Aniversário 9");
        getModeloMensagemEmail().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("FA");
        option.setDescricao("Falta");
        getModeloMensagemEmail().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("G1");
        option.setDescricao("Genérico 1");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("G2");
        option.setDescricao("Genérico 2");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("G3");
        option.setDescricao("Genérico 3");
        getModeloMensagemEmail().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("I1");
        option.setDescricao("Informativo 1");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("I2");
        option.setDescricao("Informativo 2");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("I3");
        option.setDescricao("Informativo 3");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("I4");
        option.setDescricao("Informativo 4");
        getModeloMensagemEmail().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("R1");
        option.setDescricao("Revisão A. Física 1");
        getModeloMensagemEmail().add(i++, option);
        option = new BinarioTO();
        option.setCodigo("R2");
        option.setDescricao("Revisão A. Física 2");
        getModeloMensagemEmail().add(i++, option);
    }

    /**
     * Metodo statico de inicializacao dos tipos de modelos de mensagens
     * de SMS
     * Autor: Carla Pereira de Moraes
     * Criado em 06/07/2012
     */
    @SuppressWarnings("unchecked")
    private static void inicializarDominioModeloMensagemSMS() {
        int i = 0;

        BinarioTO option = new BinarioTO();
        option.setCodigo("selecione");
        option.setDescricao("Selecione um modelo");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("A1");
        option.setDescricao("Aniversário 1");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("A2");
        option.setDescricao("Aniversário 2");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("A3");
        option.setDescricao("Aniversário 3");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("A4");
        option.setDescricao("Aniversário 4");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("A5");
        option.setDescricao("Aniversário 5");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("F1");
        option.setDescricao("Faltantes 1");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("F2");
        option.setDescricao("Faltantes 2");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("F3");
        option.setDescricao("Faltantes 3");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("F4");
        option.setDescricao("Faltantes 4");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("V1");
        option.setDescricao("Visitante 1");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("V2");
        option.setDescricao("Visitante 2");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("V3");
        option.setDescricao("Visitante 3");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("V4");
        option.setDescricao("Visitante 4");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("PV1");
        option.setDescricao("Pós-Venda 1");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("PV2");
        option.setDescricao("Pós-Venda 2");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("PV3");
        option.setDescricao("Pós-Venda 3");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("GR1");
        option.setDescricao("Grupo de Risco 1");
        getModeloMensagemSMS().add(i++, option);

        option = new BinarioTO();
        option.setCodigo("GR2");
        option.setDescricao("Grupo de Risco 2");
        getModeloMensagemSMS().add(i++, option);
    }

    private static void inicializarDominioTipoPessoa() {
        getTipoPessoa().put("CE", "Colaborador Existente");
        getTipoPessoa().put("NC", "Novo Colaborador");
    }

    private static void inicializarDominioTipoGrupo() {
        getTipoGrupo().put("PR", "Professor");
        getTipoGrupo().put("TW", "Professor (TreinoWeb)");
        getTipoGrupo().put("PT", "Personal Trainer");
        getTipoGrupo().put("OR", "Orientador");
        getTipoGrupo().put("CO", "Consultor");
        getTipoGrupo().put("PI", "Personal Interno");
        getTipoGrupo().put("PE", "Personal Externo");
        getTipoGrupo().put("TE", "Terceirizado");
        getTipoGrupo().put("FO", "Fornecedor");
    }

    private static void inicializarDominioVinculos() {
        getVinculos().put("PR", "Professor");
        getVinculos().put("TW", "Professor (TreinoWeb)");
        getVinculos().put("PT", "Personal Trainer");
        getVinculos().put("OR", "Orientador");
        getVinculos().put("CO", "Consultor");
        getVinculos().put("PI", "Personal Interno");
        getVinculos().put("PE", "Personal Externo");
        getVinculos().put("TE", "Terceirizado");
    }

    private static void inicializarDominioTipoMovimentacao() {
        getTipoMovimentacao().put("CR", "Crédito");
        getTipoMovimentacao().put("DE", "Débito");
    }

    private static void inicializarDominioDiaSemana() {
        getDiaSemana().put("DM", "Domingo");
        getDiaSemana().put("SG", "Segundo");
        getDiaSemana().put("TR", "Terça");
        getDiaSemana().put("QA", "Quarta");
        getDiaSemana().put("QI", "Quinta");
        getDiaSemana().put("SX", "Sexta");
        getDiaSemana().put("SB", "Sábado");
    }

    private static void inicializarDominioCpfCnpj() {
        getCpfCnpj().put("cpf", "CPF");
        getCpfCnpj().put("cnpj", "CNPJ");
    }

    @Deprecated
    private static void inicializarDominioTipoOperacaoContrato() {
        getTipoOperacaoContrato().put("RM", "Rematrícula");
        getTipoOperacaoContrato().put("TS", "Transferência Saída");
        getTipoOperacaoContrato().put("MA", "Matrícula");
        getTipoOperacaoContrato().put("AD", "Alteração Duração");
        getTipoOperacaoContrato().put("AH", "Alteração Horário");
        getTipoOperacaoContrato().put("RE", "Renovação");
        getTipoOperacaoContrato().put("BO", "Bônus");
        getTipoOperacaoContrato().put("CF", "Carência/Férias");
        getTipoOperacaoContrato().put("TE", "Transferência Entrada");
        getTipoOperacaoContrato().put("CA", "Cancelamento");
        getTipoOperacaoContrato().put("TR", "Trancamento");
        getTipoOperacaoContrato().put("IM", "Incluir Modalidade");
        getTipoOperacaoContrato().put("RM", "Remover Modalidade");
    }

    private static void inicializarDominioTipoComprador() {
        getTipoComprador().put("CN", "Consumidor");
        getTipoComprador().put("CO", "Colaborador");
        getTipoComprador().put("CI", "Cliente");
    }

    private static void inicializarDominioTipoCompradorAulaAvulsa() {
        getTipoCompradorAulaAvulsa().put("CI", "Cliente");
        getTipoCompradorAulaAvulsa().put("CO", "Colaborador");
    }

    private static void inicializarDominioMarcadorMovPagamento() {
        getMarcadorMovPagamento().put("[(40){}tipoFormaPagamento_MovPagamento]", "tipoFormaPagamento_MovPagamento");
        getMarcadorMovPagamento().put("[(20){ Operadora do Cartão: } operadoraCC_MovPagamento]", "operadoraCC_MovPagamento");
        getMarcadorMovPagamento().put("[(40){}valor_MovPagamento]", "valor_MovPagamento");
        //numero de vezes no cartao de credito
        getMarcadorMovPagamento().put("[(20) { Vezes: } parcelasCC_MovPagamento]", "parcelasCC_MovPagamento");
        getMarcadorMovPagamento().put("[(20){}valor_Cheque]", "valor_Cheque");
        getMarcadorMovPagamento().put("[(20){}banco_Cheque]", "banco_Cheque");
        getMarcadorMovPagamento().put("[(20){}agencia_Cheque]", "agencia_Cheque");
        getMarcadorMovPagamento().put("[(20){}contaCorrente_Cheque]", "contaCorrente_Cheque");
        getMarcadorMovPagamento().put("[(20){}numero_Cheque]", "numero_Cheque");
        getMarcadorMovPagamento().put("[(20){}dataCompensacao_Cheque]", "dataCompensacao_Cheque");

    }

    private static void inicializarDominioMarcadorReciboPagamento() {
        getMarcadorReciboPagamento().put("[(10){}codigo_Recibo]", "codigo_Recibo");
        getMarcadorReciboPagamento().put("[(10){}valorTotal_Recibo]", "valorTotal_Recibo");
        getMarcadorReciboPagamento().put("[(200){}nomePessoaPagador_Recibo]", "nomePessoaPagador_Recibo");
        getMarcadorReciboPagamento().put("[(200){}responsavelLancamento_Recibo]", "responsavelLancamento_Recibo");
        getMarcadorReciboPagamento().put("[(10){}contrato_Recibo]", "contrato_Recibo");
        getMarcadorReciboPagamento().put("[(10){}data_Recibo]", "data_Recibo");
        getMarcadorReciboPagamento().put("[(200){}valorPorExtenso_Recibo]", "valorPorExtenso_Recibo");
        getMarcadorReciboPagamento().put("[(20){}dataImpressao_Recibo]", "dataImpressao_Recibo");

    }

    private static void inicializarDominioSituacaoRenovacaoContrato() {
        getSituacaoRenovacaoContrado().put("AN", "Antecipada");
        getSituacaoRenovacaoContrado().put("ND", "No Dia");
        getSituacaoRenovacaoContrado().put("AV", "Atrasada Vencida");
        getSituacaoRenovacaoContrado().put("AD", "Atrasada Desistente");
    }

    private static void inicializarDominioSituacaoContratoOperacoesBasicas() {
        getSituacaoContradoOperacoesBasicas().put("MA", "Matrícula");
        getSituacaoContradoOperacoesBasicas().put("RE", "Renovação");
        getSituacaoContradoOperacoesBasicas().put("RM", "Rematrícula");
    }

    private static void inicializarDominioTipoAcessoCliente() {
        getTipoAcessoCliente().put("CA", "Contrato Ativo");
        getTipoAcessoCliente().put("BO", "Bônus");
        getTipoAcessoCliente().put("TO", "Tolerância");
        getTipoAcessoCliente().put("TD", "Transfêrencia de dias");
        getTipoAcessoCliente().put("PL", "Passe Livre - Free Pass");
    }

    public static Hashtable<String, String> getNecessidadesEspeciaisSesiCe() {
        return necessidadesEspeciaisSesiCe;
    }

    public static void setNecessidadesEspeciaisSesiCe(Hashtable<String, String> necessidadesEspeciaisSesiCe) {
        Dominios.necessidadesEspeciaisSesiCe = necessidadesEspeciaisSesiCe;
    }

    public static Hashtable<String, String> getStatusMatriculaSesiCe() {
        return statusMatriculaSesiCe;
    }

    public static void setStatusMatriculaSesiCe(Hashtable<String, String> statusMatriculaSesiCe) {
        Dominios.statusMatriculaSesiCe = statusMatriculaSesiCe;
    }

    private static void inicializarDominioNecessidadesEspeciaisSesiCe() {
        getNecessidadesEspeciaisSesiCe().put("AU", "Auditiva");
        getNecessidadesEspeciaisSesiCe().put("FI", "Física");
        getNecessidadesEspeciaisSesiCe().put("IN", "Intelectual (Mental)");
        getNecessidadesEspeciaisSesiCe().put("ND", "Não Declarada");
        getNecessidadesEspeciaisSesiCe().put("VI", "Visual");
        getNecessidadesEspeciaisSesiCe().put("AH", "Altas Habilidades");
        getNecessidadesEspeciaisSesiCe().put("MU", "Múltiplas");
    }
    private static void inicializarDominioStatusMatriculaSesiCe() {
        getStatusMatriculaSesiCe().put("CA", "Cancelado");
        getStatusMatriculaSesiCe().put("CO", "Concluído");
        getStatusMatriculaSesiCe().put("EV", "Evadido");
        getStatusMatriculaSesiCe().put("MA", "Matriculado");
    }

    public static Hashtable<String, String> getTipoComprador() {
        return tipoComprador;
    }

    public static void setTipoComprador(Hashtable<String, String> tipoComprador) {
        Dominios.tipoComprador = tipoComprador;
    }

    public static Hashtable<String, String> getSituacaoRelativaHistorioco() {
        return situacaoRelativaHistorioco;
    }

    public static void setSituacaoRelativaHistorioco(Hashtable<String, String> situacaoRelativaHistoriocoPrm) {
        situacaoRelativaHistorioco = situacaoRelativaHistoriocoPrm;
    }

    public static Hashtable<String, String> getSituacaoMovParcela() {
        return situacaoMovParcela;
    }

    public static void setSituacaoMovParcela(Hashtable<String, String> situacaoMovParcelaPrm) {
        situacaoMovParcela = situacaoMovParcelaPrm;
    }

    public static Hashtable<String, String> getSexo() {
        return sexo;
    }

    public static void setSexo(Hashtable<String, String> sexoPrm) {
        sexo = sexoPrm;
    }

    public static Hashtable<String, String> getSituacaoHorarioTurma() {
        return situacaoHorarioTurma;
    }

    public static void setSituacaoHorarioTurma(Hashtable<String, String> situacaoHorarioTurmaPrm) {
        situacaoHorarioTurma = situacaoHorarioTurmaPrm;
    }

    public static Hashtable<String, String> getEstado() {
        return estado;
    }

    public static void setEstado(Hashtable<String, String> estadoPrm) {
        estado = estadoPrm;
    }

    public static Hashtable<String, String> getSimNao() {
        return simNao;
    }

    public static void setSimNao(Hashtable<String, String> simNaoPrm) {
        simNao = simNaoPrm;
    }

    public static Hashtable<String, String> getEscolaridade() {
        return escolaridade;
    }

    public static void setEscolaridade(Hashtable<String, String> escolaridadePrm) {
        escolaridade = escolaridadePrm;
    }

    public static Hashtable<String, String> getEstadoCivil() {
        return estadoCivil;
    }

    public static void setEstadoCivil(Hashtable<String, String> estadoCivilPrm) {
        estadoCivil = estadoCivilPrm;
    }

    public static Hashtable<String, String> getNivelAcesso() {
        return nivelAcesso;
    }

    public static void setNivelAcesso(Hashtable<String, String> nivelAcessoPrm) {
        nivelAcesso = nivelAcessoPrm;
    }

    public static Hashtable<String, String> getSituacaoCliente() {
        return situacaoCliente;
    }

    public static void setSituacaoCliente(Hashtable<String, String> situacaoClientePrm) {
        situacaoCliente = situacaoClientePrm;
    }

    public static Hashtable<String, String> getTipoPergunta() {
        return tipoPergunta;
    }

    //    public static void setTipoCategoria(Hashtable<String, String> tipoCategoriaPrm) {
//        tipoCategoria = tipoCategoriaPrm;
//    }
//
//    public static Hashtable<String, String> getTipoCategoria() {
//        return tipoCategoria;
//    }
//    public static void setTipoEndereco(Hashtable<String, String> tipoEnderecoPrm) {
//        tipoEndereco = tipoEnderecoPrm;
//    }
//
//    public static Hashtable<String, String> getTipoEndereco() {
//        return tipoEndereco;
//    }
//    public static void setTipoTelefone(Hashtable<String, String> tipoTelefonePrm) {
//        tipoTelefone = tipoTelefonePrm;
//    }
//
//    public static Hashtable<String, String> getTipoTelefone() {
//        return tipoTelefone;
//    }
    public static void setTipoPergunta(Hashtable<String, String> tipoPerguntaPrm) {
        tipoPergunta = tipoPerguntaPrm;
    }

    public static Hashtable<String, String> getTipoVigencia() {
        return tipoVigencia;
    }

    public static void setTipoVigencia(Hashtable<String, String> tipoVigenciaPrm) {
        tipoVigencia = tipoVigenciaPrm;
    }

    public static Hashtable<String, String> getTipoProduto() {
        return tipoProduto;
    }

    public static void setTipoProduto(Hashtable<String, String> tipoProdutoPrm) {
        tipoProduto = tipoProdutoPrm;
    }

    public static Hashtable<String, String> getFormaRecebimentoParceiro() {
        return formaRecebimentoParceiro;
    }

    public static void setFormaRecebimentoParceiro(Hashtable<String, String> formaRecebimentoParceiroPrm) {
        formaRecebimentoParceiro = formaRecebimentoParceiroPrm;
    }

    public static Hashtable<String, String> getTipoPagamento() {
        return tipoPagamento;
    }

    public static void setTipoPagamento(Hashtable<String, String> tipoPagamentoPrm) {
        tipoPagamento = tipoPagamentoPrm;
    }

    public static Hashtable<String, String> getSituacaoContrato() {
        return situacaoContrato;
    }

    public static void setSituacaoContrato(Hashtable<String, String> situacaoContratoPrm) {
        situacaoContrato = situacaoContratoPrm;
    }

    public static Hashtable<String, String> getTipoPessoa() {
        return tipoPessoa;
    }

    public static void setTipoPessoa(Hashtable<String, String> tipoPessoaPrm) {
        tipoPessoa = tipoPessoaPrm;
    }

    public static Hashtable<String, String> getTipoMensagem() {
        return tipoMensagem;
    }

    public static Hashtable<String, String> getGrauDeInstrucao() {
        return grauDeInstrucao;
    }

    public static void setGrauDeInstrucao(Hashtable<String, String> grauDeInstrucao) {
        Dominios.grauDeInstrucao = grauDeInstrucao;
    }

    public static Hashtable<String, String> getTipoMovimentacao() {
        return tipoMovimentacao;
    }

    public static void setTipoMovimentacao(Hashtable<String, String> tipoMovimentacao) {
        Dominios.tipoMovimentacao = tipoMovimentacao;
    }

    public static Hashtable<String, String> getSituacaoColaborador() {
        return situacaoColaborador;
    }

    public static void setSituacaoColaborador(Hashtable<String, String> situacaoColaborador) {
        Dominios.situacaoColaborador = situacaoColaborador;
    }

    public static Hashtable<String, String> getTipoValor() {
        return tipoValor;
    }

    public static void setTipoValor(Hashtable<String, String> tipoValor) {
        Dominios.tipoValor = tipoValor;
    }

    public static Hashtable<String, String> getDiaSemana() {
        return diaSemana;
    }

    public static void setDiaSemana(Hashtable<String, String> diaSemana) {
        Dominios.diaSemana = diaSemana;
    }

    public static Hashtable<String, String> getTipoOperacao() {
        return tipoOperacao;
    }

    public static void setTipoOperacao(Hashtable<String, String> tipoOperacao) {
        Dominios.tipoOperacao = tipoOperacao;
    }

    public static Hashtable<String, String> getMarcadorCliente() {
        return marcadorCliente;
    }

    public static void setMarcadorCliente(Hashtable<String, String> marcador) {
        Dominios.marcadorCliente = marcador;
    }

    public static Hashtable<String, String> getMarcadorVenda() {
        return marcadorVenda;
    }

    public static Hashtable<String, String> getMarcadorItensVenda() {
        return marcadorItensVenda;
    }

    public static Hashtable<String, String> getMarcadorPacoteVenda() {
        return marcadorPacoteVenda;
    }

    public static Hashtable<String, String> getMarcadorContrato() {
        return marcadorContrato;
    }

    public static void setMarcadorContrato(Hashtable<String, String> marcador) {
        Dominios.marcadorContrato = marcador;
    }

    public static Hashtable<String, String> getMarcadorPlano() {
        return marcadorPlano;
    }

    public static void setMarcadorPlano(Hashtable<String, String> marcador) {
        Dominios.marcadorPlano = marcador;
    }

    public static Hashtable<String, String> getSituacaoPlanoTextoPadrao() {
        return situacaoPlanoTextoPadrao;
    }

    public static void setSituacaoPlanoTextoPadrao(Hashtable<String, String> situacaoPlanoTextoPadrao) {
        Dominios.situacaoPlanoTextoPadrao = situacaoPlanoTextoPadrao;
    }

    public static Hashtable<String, String> getTipoPlanoTextoPadrao() {
        return tipoPlanoTextoPadrao;
    }

    public static void setTipoPlanoTextoPadrao(Hashtable<String, String> tipoPlanoTextoPadrao) {
        Dominios.tipoPlanoTextoPadrao = tipoPlanoTextoPadrao;
    }

    public static Hashtable<String, String> getValorDesejado() {
        return valorDesejado;
    }

    public static void setValorDesejado(Hashtable<String, String> valorDesejado) {
        Dominios.valorDesejado = valorDesejado;
    }

    public static Hashtable<String, String> getMarcadoUsuario() {
        return marcadorUsuario;
    }

    public static void setMarcadoUsuario(Hashtable<String, String> aMarcadoUsuario) {
        marcadorUsuario = aMarcadoUsuario;
    }

    public static Hashtable<String, String> getCpfCnpj() {
        return cpfCnpj;
    }

    public static void setCpfCnpj(Hashtable<String, String> cpfCnpj) {
        Dominios.cpfCnpj = cpfCnpj;
    }

    public static Hashtable<String, String> getMarcadoEmpresa() {
        return marcadorEmpresa;
    }

    public static void setMarcadorEmpresa(Hashtable<String, String> marcador) {
        marcadorEmpresa = marcador;
    }

    public static Hashtable<String, String> getMarcadorTurma() {
        return marcadorTurma;
    }

    public static void setMarcadorTurma(Hashtable<String, String> marcador) {
        marcadorTurma = marcador;
    }

    public static Hashtable<String, String> getMarcadoFormaPagto() {
        return marcadorFormaPagto;
    }

    public static void setMarcadorFormaPagto(Hashtable<String, String> marcador) {
        marcadorFormaPagto = marcador;
    }

    public static Hashtable<String, String> getMarcadorComposicao() {
        return marcadorComposicao;
    }

    public static void setMarcadorComposicao(Hashtable<String, String> marcador) {
        marcadorComposicao = marcador;
    }

    public static Hashtable<String, String> getMarcadorModalidade() {
        return marcadorModalidade;
    }

    public static void setMarcadorModalidade(Hashtable<String, String> marcador) {
        marcadorModalidade = marcador;
    }

    public static Hashtable<String, String> getMarcadorMovParcela() {
        return marcadorMovParcela;
    }

    public static void setMarcadoMovParcela(Hashtable<String, String> marcador) {
        marcadorMovParcela = marcador;
    }

    /**
     * @return the tipoOperacaoContrato
     */
    public static Hashtable<String, String> getTipoOperacaoContrato() {
        return tipoOperacaoContrato;
    }

    /**
     * @param aTipoOperacaoContrato the tipoOperacaoContrato to set
     */
    public static void setTipoOperacaoContrato(Hashtable<String, String> aTipoOperacaoContrato) {
        tipoOperacaoContrato = aTipoOperacaoContrato;
    }

    public static Hashtable<String, String> getVinculos() {
        return vinculos;
    }

    public static void setVinculos(Hashtable<String, String> vinculos) {
        Dominios.vinculos = vinculos;
    }

    /**
     * @return the situacaoRenovacaoContrado
     */
    public static Hashtable<String, String> getSituacaoRenovacaoContrado() {
        return situacaoRenovacaoContrado;
    }

    /**
     * @param aSituacaoRenovacaoContrado the situacaoRenovacaoContrado to set
     */
    public static void setSituacaoRenovacaoContrado(Hashtable<String, String> aSituacaoRenovacaoContrado) {
        situacaoRenovacaoContrado = aSituacaoRenovacaoContrado;
    }

    /**
     * @return the situacaoContrado
     */
    public static Hashtable<String, String> getSituacaoContradoOperacoesBasicas() {
        return situacaoContradoOperacoesBasicas;
    }

    /**
     * @param aSituacaoContradoOperacoesBasicas the situacaoContrado to set
     */
    public static void setSituacaoContradoOperacoesBasicas(Hashtable<String, String> aSituacaoContradoOperacoesBasicas) {
        situacaoContradoOperacoesBasicas = aSituacaoContradoOperacoesBasicas;
    }

    /**
     * @return the tipoAcessoCliente
     */
    public static Hashtable<String, String> getTipoAcessoCliente() {
        return tipoAcessoCliente;
    }

    /**
     * @param aTipoAcessoCliente the tipoAcessoCliente to set
     */
    public static void setTipoAcessoCliente(Hashtable<String, String> aTipoAcessoCliente) {
        tipoAcessoCliente = aTipoAcessoCliente;
    }

    public static Hashtable<String, String> getTipoJustificativaOperacao() {
        return tipoJustificativaOperacao;
    }

    public static void setTipoJustificativaOperacao(Hashtable<String, String> tipoJustificativaOperacao) {
        Dominios.tipoJustificativaOperacao = tipoJustificativaOperacao;
    }

    public static Hashtable<String, String> getTipoCompradorAulaAvulsa() {
        return tipoCompradorAulaAvulsa;
    }

    public static void setTipoComparadorAulaAvulsa(Hashtable<String, String> tipoCompradorAulaAvulsa) {
        Dominios.tipoCompradorAulaAvulsa = tipoCompradorAulaAvulsa;
    }

    public static Hashtable<String, String> getMarcadorMovPagamento() {
        return marcadorMovPagamento;
    }

    public static void setMarcadorMovPagamento(Hashtable<String, String> marcadorMovPagamento) {
        Dominios.marcadorMovPagamento = marcadorMovPagamento;
    }

    public static Hashtable<String, String> getMarcadorReciboPagamento() {
        return marcadorReciboPagamento;
    }

    /**
     * @return the tipoVisao
     */
    public static Hashtable<String, String> getTipoVisao() {
        return tipoVisao;
    }

    /**
     * @param aTipoVisao the tipoVisao to set
     */
    public static void setTipoVisao(Hashtable<String, String> aTipoVisao) {
        tipoVisao = aTipoVisao;
    }

    /**
     * @return the tipoAgendamento
     */
    public static Hashtable<String, String> getTipoAgendamento() {
        return tipoAgendamento;
    }

    /**
     * @param aTipoAgendamento the tipoAgendamento to set
     */
    public static void setTipoAgendamento(Hashtable<String, String> aTipoAgendamento) {
        tipoAgendamento = aTipoAgendamento;
    }

    /**
     * @return the tipoGrupo
     */
    public static Hashtable<String, String> getTipoGrupo() {
        return tipoGrupo;
    }

    /**
     * @param aTipoGrupo the tipoGrupo to set
     */
    public static void setTipoGrupo(Hashtable<String, String> aTipoGrupo) {
        tipoGrupo = aTipoGrupo;
    }

    /**
     * @return the tipoStatus
     */
    public static Hashtable<String, String> getTipoStatus() {
        return tipoStatus;
    }

    /**
     * @param aTipoStatus the tipoStatus to set
     */
    public static void setTipoStatus(Hashtable<String, String> aTipoStatus) {
        tipoStatus = aTipoStatus;
    }

    public static Hashtable<String, String> getHoras() {
        return horas;
    }

    public static void setHoras(Hashtable<String, String> horas) {
        Dominios.horas = horas;
    }

    public static Hashtable<String, String> getIdentificadorMeta() {
        return identificadorMeta;
    }

    public static void setIdentificadorMeta(Hashtable<String, String> identificadorMeta) {
        Dominios.identificadorMeta = identificadorMeta;
    }

    public static Hashtable<String, String> getMinutos() {
        return minutos;
    }

    public static void setMinutos(Hashtable<String, String> minutos) {
        Dominios.minutos = minutos;
    }

    public static Hashtable<String, String> getTipoContatoHistoricoContato() {
        return tipoContatoHistoricoContato;
    }

    public static void setTipoContatoHistoricoContato(Hashtable<String, String> tipoContatoHistoricoContato) {
        Dominios.tipoContatoHistoricoContato = tipoContatoHistoricoContato;
    }

    public static Hashtable<String, String> getFaseAtual() {
        return faseAtual;
    }

    public static void setFaseAtual(Hashtable<String, String> faseAtual) {
        Dominios.faseAtual = faseAtual;
    }

    public static Hashtable<String, String> getTipoOperacaoHistoricoContato() {
        return tipoOperacaoHistoricoContato;
    }

    public static void setTipoOperacaoHistoricoContato(Hashtable<String, String> tipoOperacaoHistoricoContato) {
        Dominios.tipoOperacaoHistoricoContato = tipoOperacaoHistoricoContato;
    }

    public static Hashtable<String, String> getMarcadoEmail() {
        return marcadoEmail;
    }

    public static void setMarcadoEmail(Hashtable<String, String> marcadoEmail) {
        Dominios.marcadoEmail = marcadoEmail;
    }

    public static Hashtable<String, String> getTipoGrupoObjecao() {
        return tipoGrupoObjecao;
    }

    public static void setTipoGrupoObjecao(Hashtable<String, String> tipoGrupoObjecao) {
        Dominios.tipoGrupoObjecao = tipoGrupoObjecao;
    }

    @SuppressWarnings("unchecked")
    public static ArrayList getModeloMensagemEmail() {
        return modeloMensagemEmail;
    }

    @SuppressWarnings("unchecked")
    public static ArrayList getModeloMensagemSMS() {
        return modeloMensagemSMS;
    }

    public static Hashtable<String, String> getGenero() {
        return genero;
    }

    public static void setGenero(Hashtable<String, String> genero) {
        Dominios.genero = genero;
    }
}
