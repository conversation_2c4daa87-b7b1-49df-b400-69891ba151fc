/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package negocio.comuns.utilitarias.validator;

import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

/**
 * <p>Perform credit card validations.</p>
 * <p>
 * By default, all supported card types are allowed.  You can specify which
 * cards should pass validation by configuring the validation options.  For
 * example,<br/><code>CreditCardValidator ccv = new CreditCardValidator(CreditCardValidator.AMEX + CreditCardValidator.VISA);</code>
 * configures the validator to only pass American Express and Visa cards.
 * If a card type is not directly supported by this class, you can implement
 * the CreditCardType interface and pass an instance into the
 * <code>addAllowedCardType</code> method.
 * </p>
 * For a similar implementation in Perl, reference Sean M. Burke's
 * <a href="http://www.speech.cs.cmu.edu/~sburke/pub/luhn_lib.html">script</a>.
 * More information is also available
 * <a href="http://www.merriampark.com/anatomycc.htm">here</a>.
 *
 * @version $Revision: 1227719 $ $Date: 2012-01-05 18:45:51 +0100 (Thu, 05 Jan 2012) $
 * @since Validator 1.4
 */
public class CreditCardValidator implements Serializable {

    private static final long serialVersionUID = 5955978921148959496L;

    /**
     * Option specifying that no cards are allowed.  This is useful if
     * you want only custom card types to validate so you turn off the
     * default cards with this option.
     * <br/>
     * <pre>
     * CreditCardValidator v = new CreditCardValidator(CreditCardValidator.NONE);
     * v.addAllowedCardType(customType);
     * v.isValid(aCardNumber);
     * </pre>
     */
    public static final long NONE = 0;

    /**
     * Option specifying that American Express cards are allowed.
     */
    public static final long AMEX = 1 << 0;

    /**
     * Option specifying that Visa cards are allowed.
     */
    public static final long VISA = 1 << 1;

    /**
     * Option specifying that Mastercard cards are allowed.
     */
    public static final long MASTERCARD = 1 << 2;

    /**
     * Option specifying that Discover cards are allowed.
     */
    public static final long DISCOVER = 1 << 3;

    /**
     * Option specifying that Diners cards are allowed.
     */
    public static final long DINERS = 1 << 4;
    
    /**
     * Option specifying that Diners cards are allowed.
     */
    public static final long HIPERCARD = 1 << 5;

    /**
     * Option specifying that Cabal cards are allowed.
     */
    public static final long CABAL = 1 << 6;

    /**
     * The CreditCardTypes that are allowed to pass validation.
     */
    private final List cardTypes = new ArrayList();

    /**
     * Luhn checkdigit validator for the card numbers.
     */
    private static final CheckDigit LUHN_VALIDATOR = LuhnCheckDigit.LUHN_CHECK_DIGIT;

    /** American Express (Amex) Card Validator */
    public static final CodeValidator AMEX_VALIDATOR = new CodeValidator("^(3[47]\\d{13})$", LUHN_VALIDATOR);

    /** Diners Card Validator */
    public static final CodeValidator DINERS_VALIDATOR = new CodeValidator("^(30[0-5]\\d{11}|3095\\d{10}|36\\d{12}|3[8-9]\\d{12})$", LUHN_VALIDATOR);

    /** Discover Card regular expressions */
    private static final RegexValidator DISCOVER_REGEX = new RegexValidator(new String[] {"^(6011\\d{12})$", "^(64[4-9]\\d{13})$", "^(65\\d{14})$"});

    /** Discover Card Validator */
    public static final CodeValidator DISCOVER_VALIDATOR = new CodeValidator(DISCOVER_REGEX, LUHN_VALIDATOR);

    /** Mastercard Card Validator */
    public static final CodeValidator MASTERCARD_VALIDATOR = new CodeValidator("^(5[1-5]\\d{14}|2[2-7]\\d{14})$", LUHN_VALIDATOR);

    /** Visa Card Validator */
    public static final CodeValidator VISA_VALIDATOR = new CodeValidator("^(4)(\\d{12}|\\d{15})$", LUHN_VALIDATOR);
    
    /** Visa Card Validator */
    public static final CodeValidator HIPERCARD_VALIDATOR = new CodeValidator("^(606282\\d{10}(\\d{3})?)|(3841\\d{15})$", LUHN_VALIDATOR);

    /** Visa Card Validator */
    public static final CodeValidator CABAL_VALIDATOR = new CodeValidator("(60\\d{14}(\\d{3})?)$", LUHN_VALIDATOR);

    /**
     * Create a new CreditCardValidator with default options.
     */
    public CreditCardValidator() {
        this(AMEX + VISA + MASTERCARD + DISCOVER + DINERS + HIPERCARD + CABAL);
    }

    /**
     * Create a new CreditCardValidator with the specified options.
     * @param options Pass in
     * CreditCardValidator.VISA + CreditCardValidator.AMEX to specify that
     * those are the only valid card types.
     */
    public CreditCardValidator(long options) {
        super();

        if (isOn(options, VISA)) {
            this.cardTypes.add(VISA_VALIDATOR);
        }

        if (isOn(options, AMEX)) {
            this.cardTypes.add(AMEX_VALIDATOR);
        }

        if (isOn(options, MASTERCARD)) {
            this.cardTypes.add(MASTERCARD_VALIDATOR);
        }

        if (isOn(options, DISCOVER)) {
            this.cardTypes.add(DISCOVER_VALIDATOR);
        }

        if (isOn(options, DINERS)) {
            this.cardTypes.add(DINERS_VALIDATOR);
        }
        if (isOn(options, HIPERCARD)) {
            this.cardTypes.add(HIPERCARD_VALIDATOR);
        }

        if (isOn(options, CABAL)) {
            this.cardTypes.add(CABAL_VALIDATOR);
        }
        
    }

    /**
     * Create a new CreditCardValidator with the specified {@link CodeValidator}s.
     * @param creditCardValidators Set of valid code validators
     */
    public CreditCardValidator(CodeValidator[] creditCardValidators) {
        if (creditCardValidators == null) {
            throw new IllegalArgumentException("Card validators are missing");
        }
        for (int i = 0; i < creditCardValidators.length; i++) {
            cardTypes.add(creditCardValidators[i]);
        }
    }

    /**
     * Checks if the field is a valid credit card number.
     * @param card The card number to validate.
     * @return Whether the card number is valid.
     */
    public boolean isValid(String card) {
        if (card == null || card.length() == 0) {
            return false;
        }
        for (int i = 0; i < cardTypes.size(); i++) {
            CodeValidator type = (CodeValidator)cardTypes.get(i);
            if (type.isValid(card)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the field is a valid credit card number.
     * @param card The card number to validate.
     * @return The card number if valid or <code>null</code>
     * if invalid.
     */
    public Object validate(String card) {
        if (card == null || card.length() == 0) {
            return null;
        }
        Object result = null;
        for (int i = 0; i < cardTypes.size(); i++) {
            CodeValidator type = (CodeValidator)cardTypes.get(i);
            result = type.validate(card);
            if (result != null) {
                return result ;
            }
        }
        return null;

    }
    /**
     * Tests whether the given flag is on.  If the flag is not a power of 2
     * (ie. 3) this tests whether the combination of flags is on.
     *
     * @param options The options specified.
     * @param flag Flag value to check.
     *
     * @return whether the specified flag value is on.
     */
    private boolean isOn(long options, long flag) {
        return (options & flag) > 0;
    }
    
    
    public String operadora(String card) {
        if (card == null || card.length() == 0) {
            return null;
        }
//        VISA(1, "Visa", "visa", new Integer[]{4}),
        if(VISA_VALIDATOR.isValid(card)){
            return "VISA";
        }
//    MASTERCARD(2, "Mastercard", "mastercard", new Integer[]{5}),
        if(MASTERCARD_VALIDATOR.isValid(card)){
            return "MASTERCARD";
        }
//    DINERS(3, "Diners", "diners", new Integer[]{30, 36}),
        if(DINERS_VALIDATOR.isValid(card)){
            return "DINERS";
        }
//    AMEX(4, "Amex", "amex", new Integer[]{34, 37}),
        if(AMEX_VALIDATOR.isValid(card)){
            return "AMEX";
        }
//    HIPERCARD(5, "Hipercard", "hipercard", new Integer[]{0}),
        if(HIPERCARD_VALIDATOR.isValid(card)){
            return "HIPERCARD";
        }

        //Hibrael 29/01/2025 - Validação utilizada para verificar se o cartão é de Bandeira ELO
        //Não remover deste local para não interferir no fluxo de validações já existente
        try {
            ValidaBandeira.Bandeira bandeira = null;
            bandeira = ValidaBandeira.buscarBandeira(card);
            if(bandeira != null && bandeira.equals(ValidaBandeira.Bandeira.ELO)){
                return "ELO";
            }
        }catch (Exception e) {

        }


//    JCB(6, "JCB", "jcb", new Integer[]{35}),
//    SOROCRED(7, "Sorocred", "sorocred", new Integer[]{0}),
//    AURA(8, "Aura", "aura", new Integer[]{0}),
//    ELO(9, "Elo", "elo", new Integer[]{0});
        if(DISCOVER_VALIDATOR.isValid(card)){
            return "DISCOVER";
        }
        
        return null;
    }

}
