package negocio.comuns.utilitarias;

import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.validator.CreditCardValidator;

import java.lang.reflect.InvocationTargetException;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.net.InetAddress;

import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.queue.MsgTO;
import org.apache.commons.beanutils.BeanUtils;

/**
 * Esta classe visa centralizar as lógicas de validação do sistema.
 *
 * <AUTHOR>
 */
public class UteisValidacao {

    /**
     * Valida E-mail
     *
     * @param email
     * @return verdadeiro ou falso
     * <AUTHOR>
     */
    public static boolean validaEmail(String email) {
        Pattern p = Pattern.compile("^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$");
        Matcher m = p.matcher(email.trim());
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Criado para validar se um horario é maior ou igual a outro
     *
     * @param hrInicial
     * @param hrFinal
     * @return<i>true</i> caso os dados sejam válidos, <i>false</i> caso não
     * sejam válidos
     * <AUTHOR>
     */
    public static boolean comparaHorario(Date hrInicial, Date hrFinal) {
        if (hrInicial.before(hrFinal)) {
            return true;
        }
        if (hrInicial.equals(hrFinal)) {
            return true;
        }
        return false;
    }

    /**
     * Criado para validar um telefone no formato (XX)XXXX-XXXX, onde os
     * parênteses e traços podem ser substituidos por espaços
     *
     * @param telefone entrada a ser validada
     * @return <i>true</i> caso os dados sejam válidos, <i>false</i> caso não
     * sejam válidos
     * <AUTHOR>
     */
    public static boolean validaTelefone(String telefone) {
        String expression = "^\\(?(\\d{2})\\)?[- ]?(\\d{4,5})[- ]?(\\d{4})$";
        CharSequence inputStr = telefone;
        Pattern pattern = Pattern.compile(expression);
        Matcher matcher = pattern.matcher(inputStr);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    /**
     * Valida o CPF de acordo com as regras da RF
     *
     * @param cpf - String
     * @return <i>true</i> se o dado for válido, <i>false</i> se não
     */
    public static boolean isValidCPF(String cpf) {
        int[] pesoCPF = {11, 10, 9, 8, 7, 6, 5, 4, 3, 2};
        if ((cpf == null) || (cpf.length() != 11)) {
            if (cpf != null && cpf.length() == 14) {
                cpf = Uteis.removerMascara(cpf);
                if (cpf.length() != 11) {
                    return false;
                }
            } else {
                return false;
            }
        }
        Integer digito1 = calcularDigito(cpf.substring(0, 9), pesoCPF);
        Integer digito2 = calcularDigito(cpf.substring(0, 9) + digito1, pesoCPF);
        return cpf.equals(cpf.substring(0, 9) + digito1.toString()
                + digito2.toString());
    }


    /**
     * Método inteno utilizado para verificação de CPF e CNPJ
     *
     * @param str  - String
     * @param peso - int[]
     * @return digito verificador
     */
    private static int calcularDigito(String str, int[] peso) {
        int soma = 0;
        for (int indice = str.length() - 1, digito; indice >= 0; indice--) {
            digito = Integer.parseInt(str.substring(indice, indice + 1));
            soma += digito * peso[peso.length - str.length() + indice];
        }
        soma = 11 - soma % 11;
        return soma > 9 ? 0 : soma;
    }

    /**
     * Valida um CNPJ de acordo com as regras da RF
     *
     * @param cnpj - String
     * @return <i>true</i> se o dado for válido, <i>false</i> se não
     */
    public static boolean validaCNPJ(String cnpj) {
        int[] pesoCNPJ = {6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2};
        // remove pontos e barras
        cnpj = cnpj.replaceAll("\\.", "");
        cnpj = cnpj.replaceAll("-", "");
        cnpj = cnpj.replaceAll("/", "");
        // prossegue normalmente a validação
        if ((cnpj == null) || (cnpj.length() != 14)) {
            return false;
        }
        Integer digito1 = calcularDigito(cnpj.substring(0, 12), pesoCNPJ);
        Integer digito2 = calcularDigito(cnpj.substring(0, 12) + digito1,
                pesoCNPJ);
        return cnpj.equals(cnpj.substring(0, 12) + digito1.toString()
                + digito2.toString());
    }

    /**
     * Testa se um número é nulo ou igual a zero
     *
     * @param number número que será comparado
     * @return <i>true</i>se for nulo ou igual a zero, <i>false</i> se não
     */
    public static boolean emptyNumber(Number number) {
        return number == null || number.equals(0) || number.equals(0.0d) || number.equals(0L) || number.equals(0.f);
    }

    /**
     * Testa se um número não é nulo nem igual a zero
     *
     * @param number número que será comparado
     * @return <i>true</i>se for nulo ou igual a zero, <i>false</i> se não
     */
    public static boolean notEmptyNumber(Number number) {
        return !emptyNumber(number);
    }

    /**
     * Testa se uma string é nula ou vazia
     *
     * @param s
     * @return <i>true</i>se for nulo ou vazio, <i>false</i> se não
     */
    public static boolean emptyString(String s) {
        if (s == null) {
            return true;
        }
        if (s.equals("")) {
            return true;
        }
        return false;
    }

    /**
     * Verifica se dentre um conjunto de objetos todos estão válidos (neste caso
     * não nulos)
     *
     * @param objs
     * @return <i>true</i> se todos os objetos forem válidos, <i>false</i> se
     * não
     * <AUTHOR>
     */
    public static boolean nenhumNulo(Object... objs) {
        boolean validade = true;
        for (Object obj : objs) {
            if (obj == null) {
                validade = false;
            }
        }
        return validade;
    }

    /**
     * Compara datas
     *
     * @param data Date
     * @return <i>true</i> se a data for menor que a atual, <i>false</i> se não
     */
    public static boolean dataMenorDataAtual(Date data) {
        if (data == null) {
            return false;
        }

        if (data.before(negocio.comuns.utilitarias.Calendario.hoje())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Compara datas ignorando a hora
     *
     * @param data Date
     * @return <i>true</i> se a data for menor que a atual, <i>false</i> se não
     */
    public static boolean dataMenorDataAtualSemHora(Date data) {
        try {
            if (data == null) {
                return false;
            }

            SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
            Date dataAtualFormatada = format.parse(format.format(negocio.comuns.utilitarias.Calendario.hoje()));

            if (data.before(dataAtualFormatada)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verifica se algum dos objetos passados como parâmetro é igual ao primeiro
     * Impede o ocorrimento de NPE
     *
     * @param valorInicial - objeto a ser comparado com os demais
     * @param objects      - os demais
     * @return <i>true</i> caso o primeiro objeto seja igual a um dos outros,
     * <i>false</i> se não
     */
    public static boolean valorIn(Object valorInicial, Object... objects) {
        if (valorInicial == null || objects == null) {
            return false;
        }
        for (Object object : objects) {
            if (valorInicial.equals(object)) {
                return true;
            }
        }
        return false;
    }

    /*
     * author: Ulisses Data: 01/06/11 Retorno: True: Se o conteúdo da string
     * tiver somente números. False: Se o conteúdo da string tiver algum
     * caracter diferente de número.
     */
    public static boolean somenteNumeros(String str) {
        String numeros = str.replaceAll("[^0-9]", "");
        return (str.length() == numeros.length());
    }

    public static boolean somenteNumerosEPontos(String str) {
        str = str.replaceAll("\\.", "");
        String numeros = str.replaceAll("[^0-9]", "");
        return (str.length() == numeros.length());
    }

    /**
     * Verifica se a propriedade possui valor nulo
     *
     * @param objeto      - Object
     * @param propriedade - EL do caminho da propriedade
     * @return <i>true</i> caso náo consiga obter a propriedade, ela esteja
     * vazia ou nula, <i>false</i> se a propriedade possuir valor
     */
    public static boolean valorNulo(Object objeto, String propriedade) {
        try {
            String property = BeanUtils.getProperty(objeto, propriedade);
            return property == null || property.equals("");
        } catch (IllegalAccessException e) {
            return true;
        } catch (IllegalArgumentException e) {
            return true;
        } catch (InvocationTargetException e) {
            return true;
        } catch (NoSuchMethodException e) {
            return true;
        }
    }

    /**
     * Método criado para validar dia e mês informados, sendo que o dia e mês
     * informados são sempre separadamente válidos vindos de um comboBox com
     * números sempre válidos.
     *
     * @param dia
     * @param mes
     * @return
     * @throws Exception
     */
    public static boolean validaDiaMesCalendar(int dia, int mes)
            throws Exception {
        Calendar cal = Calendario.getInstance();
        cal.setTime(negocio.comuns.utilitarias.Calendario.hoje());
        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes);

        Date data = cal.getTime();
        cal.setTime(data);
        // verifica se o Calendar não acrescentou ou retirou dias
        // se o usuário informar valores errados o dia será diferente
        if (cal.get(Calendar.DAY_OF_MONTH) != dia) {
            return false;
        }
        return true;
    }

    public static String removerZeros(String string) {
        String r = string.replaceAll("0", "");
        int i = r.indexOf(".");
        if (i > 0) {
            String s1 = r.substring(0, i);
            String s2 = r.substring(i + 1, r.length());
            String[] s = s2.split("\\.");
            String s3 = ".";
            for (String cdg : s) {
                Integer integer = new Integer(cdg);
                int d2 = integer;
                String d3 = String.valueOf(d2);
                s3 += d3 + ".";
            }
            String string2 = s1 + s3;
            String substring = string2.substring(0, string2.length() - 1);
            return substring;
        } else {
            return r;
        }
    }

    public static String rearanjar(int[] codigos, String codigoPlano) {
        int niveis = codigoPlano.split("\\.").length;
        String retorno = null;
        if (niveis == 1) {
            Integer codigo = codigos[0];
            retorno = String.valueOf(codigo++);
            codigos[0] = codigo;
        } else {
            int codigo = codigos[niveis - 1];
            String codigoNovo = "";
            for (int k = 0; k < niveis - 1; k++) {
                codigoNovo += codigos[k] - 1 + ".";
            }
            retorno = codigoNovo + codigo;
            codigos[niveis - 1] = ++codigo;
        }
        codigos[niveis] = 0;
        return retorno;
    }

    public static void enfileirarEmail(MsgTO mensagem) {
        ThreadEnviarEmail.enfileirar(mensagem);
    }

    public static void enviarEmail(StringBuffer mensagem, String emailDestinatario,
                                   String nomeAmigavelDestinatario, String assunto) {
        UteisEmail uteis;
        try {
            System.out.println("Tentando ENVIAR EMAIL...");
            uteis = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPRobo();
            uteis.novo(assunto, config);
            mensagem.append("<b>From: ").append(InetAddress.getLocalHost().getHostAddress()).append("</b>");
            uteis.enviarEmail(emailDestinatario,
                    nomeAmigavelDestinatario, mensagem.toString(), "");

            System.out.println("Email enviado com SUCESSO!!");
        } catch (Exception ex) {
            System.out.println("EXC.: Não foi possível enviar email de erro ocorrido. MENSAGEM: '"
                    + mensagem.toString()
                    + "' , por causa do seguinte erro de email: " + ex.getMessage());
        }

    }

    public static int gerarNumeroRandomico(int nrInicial, int nrFinal) {
        Random random = new Random();
        int sorteio = random.nextInt(nrFinal - nrInicial); //Tamanho do intervalo
        return sorteio + nrInicial;
    }

    public static void validarHoraMinutos(String hora, String mensagem) throws ConsistirException {

        if(hora.isEmpty()){
            throw new ConsistirException(mensagem);
        }
        int minutos = Integer.parseInt(hora.substring(3, 5));
        int horas = Integer.parseInt(hora.substring(0, 2));
        if (minutos > 60) {
            throw new ConsistirException(mensagem);
        }
        if (horas > 23) {
            throw new ConsistirException(mensagem);
        }
    }

    /**
     * Responsável por validar o formato dos códigos agrupadores de centro de custos e plano de custos
     *
     * <AUTHOR>
     * 25/11/2011
     */
    public static void validaCodigoFinanceiro(String codigo) throws ConsistirException {
        if (!somenteNumerosEPontos(codigo)) {
            throw new ConsistirException("" + codigo + " está incorreto. Informe apenas números e pontos.");
        }
        if (codigo.startsWith(".") || codigo.endsWith(".")) {
            throw new ConsistirException("" + codigo + " está incorreto. Informe pontos apenas entre os números.");
        }
        if (codigo.contains(".")) {
            String[] codigos = codigo.split("[.]");
            for (String cod : codigos) {
                if (cod.length() != 3) {
                    throw new ConsistirException("" + codigo + " está incorreto. Cada agrupador do código deve ter 3 dígitos.");
                }
            }
        } else {
            if (codigo.length() != 3) {
                throw new ConsistirException("" + codigo + " está incorreto. Cada agrupador do código deve ter 3 dígitos.");
            }
        }
    }

    /**
     * Testa se uma collection é nula ou vazia
     *
     * @param l java.lang.Number
     * @return <i>true</i>se for nulo ou igual a zero, <i>false</i> se não
     */
    public static boolean emptyList(Collection l) {
        if (l == null) {
            return true;
        }
        if (l.isEmpty()) {
            return true;
        }
        return false;
    }

    public static boolean emptyArray(Object[] arr) {
        if (arr == null) {
            return true;
        }
        if (arr.length == 0) {
            return true;
        }
        return false;
    }

    public static void validarNumeroCartaoCredito(final String numero) throws ConsistirException {
        if (numero.contains("******") || numero.lastIndexOf("=") != -1)/* criptografado*/ {
            return;
        }
        CreditCardValidator creditCardValidator = new CreditCardValidator();
        if (!creditCardValidator.isValid(numero)) {
            throw new ConsistirException("Número de Cartão inválido: " + numero);
        }
    }

    public static void validarNumeroCartaoCreditoElo(final String numero) throws ConsistirException {
        if (numero.contains("******") || numero.lastIndexOf("=") != -1)/* criptografado*/ {
            return;
        }
        int l = numero != null ? numero.trim().length() : 0;
        if (l < 13) {
            throw new ConsistirException("Número de Cartão inválido 1.");
        } else if (l == 13 && !(numero.startsWith("000") && (numero.charAt(3) != 0))) {
            throw new ConsistirException("Número de Cartão inválido 2.");
        } else if (l == 14 && !(numero.startsWith("00") && (numero.charAt(2) != 0))) {
            throw new ConsistirException("Número de Cartão inválido 3.");
        } else if (numero.charAt(0) == 0) {
            throw new ConsistirException("Número de Cartão inválido 4.");
        }
        int soma = 0;
        for (int i = 0; i < l - 1; i++) {
            int valor = Integer.valueOf(String.valueOf(numero.charAt(i)));
            int resultado = valor * (i % 2 == 0 ? 2 : 1);
            String aux = String.valueOf(resultado);
            for (int j = 0; j < aux.length(); j++) {
                int k = Integer.valueOf(String.valueOf(aux.charAt(j)));
                soma += k;
            }
        }

        int resto = soma % 10;
        int dv = 10 - resto;
        dv = (dv >= 10) ? 0 : dv;
        if (dv != Integer.valueOf(String.valueOf(numero.charAt(numero.length() - 1))).intValue()) {
            throw new ConsistirException("Número de Cartão inválido 5.");
        }
    }

    public static void validarVencimentoCartao(final String validadeMMYYYY) throws ConsistirException {
        int mes = 0;
        int ano = 0;
        if (validadeMMYYYY.length() == 7) {
            mes = Integer.valueOf(validadeMMYYYY.substring(0, 2));
            ano = Integer.valueOf(validadeMMYYYY.substring(3));
        } else if (validadeMMYYYY.length() == 5) {
            mes = Integer.valueOf(validadeMMYYYY.substring(0, 2));
            ano = Integer.parseInt("20" + validadeMMYYYY.substring(3, 5));
        } else {
            throw new ConsistirException("A VALIDADE informada está fora do padrão.");
        }

        int anoAtual = Calendario.getInstance().get(Calendar.YEAR);
        int mesAtual = Calendario.getInstance().get(Calendar.MONTH) + 1;
        if (ano < anoAtual || (ano == anoAtual && mes < mesAtual)) {
            throw new ConsistirException("Cartão informado está vencido segundo a VALIDADE informada: " + validadeMMYYYY);
        }
    }

    /**
     * @param password
     * @return boolean
     * <b>A senha deve conter pelo menos 8 caractéres, 1 caractere especial, 1 número, 1 letra maiúscula e 1 letra minúscula.<b/>
     */
    public static boolean validarSenhaSegura(String password) {
        if (password != null)
            return password.matches("^(?:(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*['!@#$%^¨&*(){}~,.<>;?+=]).*)[^\\s]{8,}$");
        return false;
    }

    /**
     * @param pin
     * @return boolean
     * <b>O pin deve ter exatos 4 dígitos numéricos e não pode ser sequência crescente ou decrescente.<b/>
     */
    public static boolean validarPinSeguro(String pin) {
        try {
            return Integer.parseInt(pin) > 0 && pin.trim().length() == 4 && !"0123456789876543210 0000111122223333444455556666777788889999".contains(pin);
        } catch (Exception e) {
            return false;
        }
    }

    public static Integer converterInteiro(String valor){
        Integer numeroConvertido = null;
        try {
            numeroConvertido = Integer.parseInt(valor);
        }catch (Exception e){
            // Ignored
        }

        return  numeroConvertido;
    }

    public static Boolean converterBooleano(String valor){
        Boolean valorConvertido = Boolean.FALSE;
        try {
            valorConvertido = Boolean.parseBoolean(valor);
        }catch (Exception e){
            // Ignored
        }

        return  valorConvertido;
    }

    public static Date converterData(String data, String formato){
        Date dataConvertida = null;
        try {
            dataConvertida = Uteis.getDate(data, formato);
        }catch (Exception e){
            // Ignored
        }

        return  dataConvertida;
    }

//    public static void main(String... args) {
//        try {
//            //Amex
//            validarNumeroCartaoCredito("***************");
//            //Mastercard
//            validarNumeroCartaoCredito("****************");
//            //VISA
//            validarNumeroCartaoCredito("****************");
//        } catch (ConsistirException ex) {
//            Logger.getLogger(UteisValidacao.class.getName()).log(Level.SEVERE, null, ex);
//        }
//    }

    public static String adicionarUmMinutoNaHora(String horaInicial){
        String novaHoraString = null;
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            LocalTime hora = LocalTime.parse(horaInicial, formatter);
            LocalTime novaHora = hora.plusMinutes(1);
            novaHoraString = novaHora.format(formatter);
        }catch (Exception e){
            // Ignored
        }

        return  novaHoraString;
    }

    public static boolean emptyObject(Object obj) {
        return obj == null;
    }

    public static String[] validarHorarioTurmaPlanilha(String horarioTurma) throws Exception {
        if (horarioTurma == null || horarioTurma.trim().isEmpty()) {
            throw new Exception("A coluna HORARIO_TURMA precisa ser preenchida na planilha.");
        }

        // Normalizar o formato do horário
        horarioTurma = horarioTurma.toLowerCase()
                .replace("até", "-")
                .replace("a", "-")
                .replace("à", "-")
                .replaceAll("[ \t\r\n]+", "")
                .trim();

        String regex = "(?i)(\\d{1,2})(?::?(\\d{2})?)?-{1}(\\d{1,2})(?::?(\\d{2})?)?";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(horarioTurma);

        if (matcher.matches()) {
            String horaInicio = matcher.group(1);
            String minutoInicio = matcher.group(2) != null ? matcher.group(2) : "00";
            String horaFim = matcher.group(3);
            String minutoFim = matcher.group(4) != null ? matcher.group(4) : "00";

            // Ajustar o formato para HH:MM
            horaInicio = String.format("%02d", Integer.parseInt(horaInicio));
            horaFim = String.format("%02d", Integer.parseInt(horaFim));

            String horarioFormatado = String.format("%s:%s - %s:%s", horaInicio, minutoInicio, horaFim, minutoFim);
            return horarioFormatado.split(" - ");
        } else {
            throw new Exception("O valor na coluna HORARIO_TURMA está em um formato inválido. O formato esperado é: 00:00 - 00:00.");
        }
    }






    public static boolean isMesmoDia(Date data1, Date data2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(data1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(data2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    public static String converterParaValorMonetario(Double valorContrato) {
        if (valorContrato == null) {
            return "R$ 0,00";
        }
        NumberFormat formatoMoeda = NumberFormat.getCurrencyInstance(new Locale("pt", "BR"));
        return formatoMoeda.format(valorContrato);
    }
}
