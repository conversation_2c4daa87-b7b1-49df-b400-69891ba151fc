package negocio.comuns.utilitarias;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.BIEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.ConfiguracaoUsuarioEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 27/02/2016.
 */
public class ConfiguracaoSistemaUsuarioVO extends SuperVO {


    private int codigo;
    private String valor;
    private UsuarioVO usuario;
    private Date dataRegistro;
    private ConfiguracaoUsuarioEnum tipo;

    @NaoControlarLogAlteracao
    List<String> listaOrdemCol1;
    @NaoControlarLogAlteracao
    List<String> listaOrdemCol2;

    private BICarregarTO pendencia = new BICarregarTO();
    private BICarregarTO gRisco = new BICarregarTO();
    private BICarregarTO indiceRenovacao = new BICarregarTO();
    private BICarregarTO conversaoVenda = new BICarregarTO();
    private BICarregarTO conversaoVendaSS = new BICarregarTO();
    private BICarregarTO controleOp = new BICarregarTO();
    private BICarregarTO probEvas = new BICarregarTO();
    private BICarregarTO metasFinan = new BICarregarTO();
    private BICarregarTO contratoR = new BICarregarTO();
    private BICarregarTO movContrato = new BICarregarTO();
    private BICarregarTO ticketMedio = new BICarregarTO();
    private BICarregarTO conviteBI = new BICarregarTO();
    private BICarregarTO clientesVerificados = new BICarregarTO();
    private BICarregarTO aulaExperimental = new BICarregarTO();
    private BICarregarTO gestaoAcesso = new BICarregarTO();
    private BICarregarTO inadimplencia = new BICarregarTO();
    private BICarregarTO ltv = new BICarregarTO();
    private BICarregarTO gymPass = new BICarregarTO();

    public ConfiguracaoSistemaUsuarioVO(){
        this.codigo = 0;
        this.valor = "";
        this.usuario = new UsuarioVO();
        this.dataRegistro = new Date();
        this.getPendencia().setNaLixeira(false);
        this.getgRisco().setNaLixeira(false);
        this.getIndiceRenovacao().setNaLixeira(false);
        this.getConversaoVenda().setNaLixeira(false);
        this.getConversaoVendaSS().setNaLixeira(false);
        this.getControleOp().setNaLixeira(false);
        this.getProbEvas().setNaLixeira(false);
        this.getMetasFinan().setNaLixeira(false);
        this.getContratoR().setNaLixeira(false);
        this.getMovContrato().setNaLixeira(false);
        this.getTicketMedio().setNaLixeira(false);
        this.getConviteBI().setNaLixeira(false);
        this.getClientesVerificados().setNaLixeira(false);
        this.getAulaExperimental().setNaLixeira(false);
        this.getGestaoAcesso().setNaLixeira(false);
        this.getInadimplencia().setNaLixeira(false);
        this.getLtv().setNaLixeira(false);
        this.getGymPass().setNaLixeira(false);
    }

    public void montarLista() {
        List<Integer> todosBI = BIEnum.obterTodosIndices();
        if (UteisValidacao.emptyString(getValor())) {
            setValor("11-0,0-0,1-0,2-0,3-0,4-0,10-0,15-1,5-1,6-1,7-1,8-1,9-1,12-0,13-1,14-0,17-0,16-0");
        }

        listaOrdemCol1 = new ArrayList<>();
        listaOrdemCol2 = new ArrayList<>();
        for (String i : getValor().split(",")) {

            String bi = i.split("-")[0];
            String col = i.split("-")[1];
            todosBI.remove(Integer.valueOf(bi));
            switch (col) {
                case "0":
                    listaOrdemCol1.add(bi);
                    removerBIsLixeira(bi);
                    break;
                case "1":
                    listaOrdemCol2.add(bi);
                    removerBIsLixeira(bi);
                    break;
                case "2":
                    adicionarLixeira(bi);
                    break;
            }
        }
        for(Integer bi: todosBI){ // bi's que não estão selecionados para impressão ou marcados como lixeira, são adionados a lixeira. Isso evita que novos bi's sejam selecionáveis, para clientes que tem configuração gravada
            adicionarLixeira(bi.toString());
            setValor(getValor()+","+bi+"-2");
        }
    }

    public  BICarregarTO obterBICarregarPorID(String id){
        switch (id) {
            case "0":
                return this.getPendencia();
            case "1":
                return this.getConversaoVenda();
            case "2":
                return this.getConversaoVendaSS();
            case "3":
                return this.getMetasFinan();
            case "4":
                return this.getTicketMedio();
            case "5":
                return this.getgRisco();
            case "6":
                return this.getIndiceRenovacao();
            case "7":
                return this.getMovContrato();
            case "8":
                return this.getContratoR();
            case "9":
                return this.getControleOp();
            case "10":
                return this.getConviteBI();
            case "11":
                return this.getClientesVerificados();
            case "12":
                return this.getAulaExperimental();
            case "13":
                return this.getGestaoAcesso();
            case "14":
                return this.getInadimplencia();
            case "15":
                return this.getProbEvas();
            case "16":
                return this.getLtv();
            case "17":
                return this.getGymPass();
        }
        return  null;
    }
    public void removerBIsLixeira(String i){
        obterBICarregarPorID(i).setNaLixeira(false);
    }
    public void adicionarLixeira(String i){
        obterBICarregarPorID(i).setNaLixeira(true);
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public UsuarioVO getUsuario() {
        if(usuario == null){
            usuario = new UsuarioVO();
        }
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public List<String> getListaOrdemCol1() {
        return listaOrdemCol1;
    }

    public List<String> getListaOrdemCol2() {
        return listaOrdemCol2;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getValor() {
        return valor;
    }

    public String getOrdenacao() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
    public void setOrdenacao(String valor) {
        this.valor = valor;
    }
    public ConfiguracaoUsuarioEnum getTipo() {
        return tipo;
    }

    public void setTipo(ConfiguracaoUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public BICarregarTO getPendencia() {
        return pendencia;
    }

    public void setPendencia(BICarregarTO pendencia) {
        this.pendencia = pendencia;
    }

    public BICarregarTO getgRisco() {
        return gRisco;
    }

    public void setgRisco(BICarregarTO gRisco) {
        this.gRisco = gRisco;
    }

    public BICarregarTO getIndiceRenovacao() {
        return indiceRenovacao;
    }

    public void setIndiceRenovacao(BICarregarTO indiceRenovacao) {
        this.indiceRenovacao = indiceRenovacao;
    }

    public BICarregarTO getConversaoVenda() {
        return conversaoVenda;
    }

    public void setConversaoVenda(BICarregarTO conversaoVenda) {
        this.conversaoVenda = conversaoVenda;
    }

    public BICarregarTO getConversaoVendaSS() {
        return conversaoVendaSS;
    }

    public void setConversaoVendaSS(BICarregarTO conversaoVendaSS) {
        this.conversaoVendaSS = conversaoVendaSS;
    }

    public BICarregarTO getControleOp() {
        return controleOp;
    }

    public void setControleOp(BICarregarTO controleOp) {
        this.controleOp = controleOp;
    }

    public BICarregarTO getProbEvas() { return probEvas; }

    public void setProbEvas(BICarregarTO probEvas) {
        this.probEvas = probEvas;
    }

    public BICarregarTO getMetasFinan() {
        return metasFinan;
    }

    public void setMetasFinan(BICarregarTO metasFinan) {
        this.metasFinan = metasFinan;
    }

    public BICarregarTO getContratoR() {
        return contratoR;
    }

    public void setContratoR(BICarregarTO contratoR) {
        this.contratoR = contratoR;
    }

    public BICarregarTO getMovContrato() {
        return movContrato;
    }

    public void setMovContrato(BICarregarTO movContrato) {
        this.movContrato = movContrato;
    }

    public BICarregarTO getTicketMedio() {
        return ticketMedio;
    }

    public void setTicketMedio(BICarregarTO ticketMedio) {
        this.ticketMedio = ticketMedio;
    }

    public BICarregarTO getConviteBI() {
        return conviteBI;
    }

    public void setConviteBI(BICarregarTO conviteBI) {
        this.conviteBI = conviteBI;
    }

    public BICarregarTO  getClientesVerificados() {
        return clientesVerificados;
    }

    public void setClientesVerificados(BICarregarTO clientesVerificados) {
        this.clientesVerificados = clientesVerificados;
    }

    public BICarregarTO getAulaExperimental() {
        return aulaExperimental;
    }

    public void setAulaExperimental(BICarregarTO aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }

    public BICarregarTO getGestaoAcesso() {
        return gestaoAcesso;
    }

    public void setGestaoAcesso(BICarregarTO gestaoAcesso) {
        this.gestaoAcesso = gestaoAcesso;
    }

    public BICarregarTO getInadimplencia() {
        return inadimplencia;
    }

    public void setInadimplencia(BICarregarTO inadimplencia) {
        this.inadimplencia = inadimplencia;
    }

    public void inicializarCarregandoParaAtualizacao(){
        this.getPendencia().inicializarDadosSobreCarregamento();
        this.getgRisco().inicializarDadosSobreCarregamento();
        this.getIndiceRenovacao().inicializarDadosSobreCarregamento();
        this.getConversaoVenda().inicializarDadosSobreCarregamento();
        this.getConversaoVendaSS().inicializarDadosSobreCarregamento();
        this.getControleOp().inicializarDadosSobreCarregamento();
        this.getMetasFinan().inicializarDadosSobreCarregamento();
        this.getContratoR().inicializarDadosSobreCarregamento();
        this.getMovContrato().inicializarDadosSobreCarregamento();
        this.getTicketMedio().inicializarDadosSobreCarregamento();
        this.getConviteBI().inicializarDadosSobreCarregamento();
        this.getClientesVerificados().inicializarDadosSobreCarregamento();
        this.getAulaExperimental().inicializarDadosSobreCarregamento();
        this.getGestaoAcesso().inicializarDadosSobreCarregamento();
        this.getInadimplencia().inicializarDadosSobreCarregamento();
        this.getProbEvas().inicializarDadosSobreCarregamento();
        this.getLtv().inicializarDadosSobreCarregamento();
        this.getGymPass().inicializarDadosSobreCarregamento();
    }

    public BICarregarTO getLtv() {
        return ltv;
    }

    public void setLtv(BICarregarTO ltv) {
        this.ltv = ltv;
    }

    public BICarregarTO getGymPass() {
        return gymPass;
    }

    public void setGymPass(BICarregarTO gymPass) {
        this.gymPass = gymPass;
    }
}
