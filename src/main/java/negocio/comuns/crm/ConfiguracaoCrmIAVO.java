package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;

public class ConfiguracaoCrmIAVO extends SuperVO {
    private Integer codigo;
    private Boolean habilitarConfigIA;
    private String personalidade;
    private String informacoesAdicionaisAcademia;
    private String tokenZApi;
    private String idInstancia;
    private String pactoConversasLogin;
    private String pactoConversasSenha;
    private Boolean whatsappBusiness;
    private java.sql.Time horarioPadrao;
    private Boolean matriz;
    private String chaveMatriz;
    private Boolean habilitarConfigParaRedeAcademias;
    private Integer codigoEmpresa;
    private String nomeMatriz;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHabilitarConfigIA() {
        return habilitarConfigIA;
    }

    public void setHabilitarConfigIA(Boolean habilitarConfigIA) {
        this.habilitarConfigIA = habilitarConfigIA;
    }

    public String getPersonalidade() {
        return personalidade;
    }

    public void setPersonalidade(String personalidade) {
        this.personalidade = personalidade;
    }

    public String getInformacoesAdicionaisAcademia() {
        return informacoesAdicionaisAcademia;
    }

    public void setInformacoesAdicionaisAcademia(String informacoesAdicionaisAcademia) {
        this.informacoesAdicionaisAcademia = informacoesAdicionaisAcademia;
    }

    public String getTokenZApi() {
        return tokenZApi;
    }

    public void setTokenZApi(String tokenZApi) {
        this.tokenZApi = tokenZApi;
    }

    public String getIdInstancia() {
        return idInstancia;
    }

    public void setIdInstancia(String idInstancia) {
        this.idInstancia = idInstancia;
    }

    public String getPactoConversasLogin() {
        return pactoConversasLogin;
    }

    public void setPactoConversasLogin(String pactoConversasLogin) {
        this.pactoConversasLogin = pactoConversasLogin;
    }

    public String getPactoConversasSenha() {
        return pactoConversasSenha;
    }

    public void setPactoConversasSenha(String pactoConversasSenha) {
        this.pactoConversasSenha = pactoConversasSenha;
    }

    public Boolean getWhatsappBusiness() {
        return whatsappBusiness;
    }

    public void setWhatsappBusiness(Boolean whatsappBusiness) {
        this.whatsappBusiness = whatsappBusiness;
    }

    public java.sql.Time getHorarioPadrao() {
        return horarioPadrao;
    }

    public void setHorarioPadrao(java.sql.Time horarioPadrao) {
        this.horarioPadrao = horarioPadrao;
    }

    public Boolean getMatriz() {
        return matriz;
    }

    public void setMatriz(Boolean matriz) {
        this.matriz = matriz;
    }

    public String getChaveMatriz() {
        return chaveMatriz;
    }

    public void setChaveMatriz(String chaveMatriz) {
        this.chaveMatriz = chaveMatriz;
    }

    public Boolean getHabilitarConfigParaRedeAcademias() {
        return habilitarConfigParaRedeAcademias;
    }

    public void setHabilitarConfigParaRedeAcademias(Boolean habilitarConfigParaRedeAcademias) {
        this.habilitarConfigParaRedeAcademias = habilitarConfigParaRedeAcademias;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeMatriz() {
        return nomeMatriz;
    }

    public void setNomeMatriz(String nomeMatriz) {
        this.nomeMatriz = nomeMatriz;
    }
}
