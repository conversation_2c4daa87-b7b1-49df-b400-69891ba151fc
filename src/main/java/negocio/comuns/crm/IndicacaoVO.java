package negocio.comuns.crm;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

/**
 * Reponsável por manter os dados da entidade Indicacao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class IndicacaoVO extends SuperVO {

    protected Integer codigo;
    private ClienteVO clienteQueIndicou;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected UsuarioVO responsavelCadastro;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    /**ColaboradorResponsavel e responsavelCadastro serão os responsáveis por operar o sistema. */
    private UsuarioVO colaboradorResponsavel;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected ColaboradorVO colaboradorQueIndicou;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Cliente </code>.*/
    private String observacao;
    private Date dia;
    private EventoVO evento;
    private List<IndicadoVO> indicadoVOs;
    //campo somente usado para saber qual dia sera atualizada abertura
    private Date diaAbertura;
    private EmpresaVO empresa;
    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;

    /**
     * Wanderson
     * Atributo usando para relatorios em indicacaoCons.jsp
     */
    private IndicadoVO indicadoMobile; //atributo transient.
    private PessoaVO pessoaIndicouVO;

    public IndicacaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>IndicacaoVO</code>.
     */
    public static void validarUnicidade(List<IndicacaoVO> lista, IndicacaoVO obj) throws ConsistirException {
        for (IndicacaoVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>IndicacaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(IndicacaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (((obj.getClienteQueIndicou() == null) || (obj.getClienteQueIndicou().getCodigo().intValue() == 0))
                && ((obj.getColaboradorQueIndicou() == null) || (obj.getColaboradorQueIndicou().getCodigo().intValue() == 0))
                || (obj.getClienteQueIndicou().getPessoa().getNome().isEmpty() && obj.getColaboradorQueIndicou().getPessoa().getNome().isEmpty())) {
            throw new ConsistirException("O campo CLIENTE QUE INDICOU e o campo COLABORADOR QUE INDICOU estão vazios, informe um dos campos !");
        }
        if((obj.getResponsavelCadastro().getCodigo().intValue() == 0) || (obj.getResponsavelCadastro() == null)){
        	throw new ConsistirException("Nenhum Responsável Cadastro foi informado, talvez não tenha sido feita a abertura de meta do dia !");
        }
        if((obj.getColaboradorResponsavel().getCodigo().intValue() == 0) || (obj.getColaboradorResponsavel() == null)){
        	throw new ConsistirException("Nenhum Colaborador Responsável foi informado, talvez não tenha sido feita a abertura de meta do dia !");
        }
        if (obj.getIndicadoVOs().isEmpty()) {
            throw new ConsistirException("Nenhum Indicado foi informado !");
        }

    }

    public Boolean getApresentarClienteQueIndicou() {
        if (getClienteQueIndicou() == null || getClienteQueIndicou().getCodigo().intValue() == 0) {
            return false;
        }
        return true;
    }

    public Boolean getApresentarColaboradorQueIndicou() {
        if (getColaboradorQueIndicou() == null || getColaboradorQueIndicou().getCodigo().intValue() == 0) {
            return false;
        }
        return true;
    }

    public void realizarUpperCaseDados() {
        setObservacao(getObservacao().toUpperCase());
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        
    }

    public void adicionarObjIndicadoVOs(IndicadoVO obj) throws Exception {
        IndicadoVO.validarDados(obj);
        obj.setIndicacaoVO(this);
        int index = 0;
        Iterator i = getIndicadoVOs().iterator();
        while (i.hasNext()) {
            IndicadoVO objExistente = (IndicadoVO) i.next();
            if (objExistente.getTelefoneIndicado().equals(obj.getTelefoneIndicado())
                    && objExistente.getNomeIndicado().equals(obj.getNomeIndicado())) {
                getIndicadoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getIndicadoVOs().add(obj);
    }

    public void excluirObjIndicadoVOs(IndicadoVO obj) throws Exception {
        int index = 0;
        Iterator i = getIndicadoVOs().iterator();
        while (i.hasNext()) {
            IndicadoVO objExistente = (IndicadoVO) i.next();
            if (objExistente.getTelefoneIndicado().equals(obj.getTelefoneIndicado())
                    && objExistente.getNomeIndicado().equals(obj.getNomeIndicado())) {
                getIndicadoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>Indicacao</code>).
     */
    public ColaboradorVO getColaboradorQueIndicou() {
        if (colaboradorQueIndicou == null) {
            colaboradorQueIndicou = new ColaboradorVO();
        }
        return (colaboradorQueIndicou);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>Indicacao</code>).
     */
    public void setColaboradorQueIndicou(ColaboradorVO obj) {
        this.colaboradorQueIndicou = obj;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>Indicacao</code>).
     */
    public UsuarioVO getResponsavelCadastro() {
        if (responsavelCadastro == null) {
            responsavelCadastro = new UsuarioVO();
        }
        return (responsavelCadastro);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>Indicacao</code>).
     */
    public void setResponsavelCadastro(UsuarioVO obj) {
        this.responsavelCadastro = obj;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the observacao
     */
    public String getObservacao() {
    	if(observacao == null){
    		observacao = "";
    	}
        return observacao;
    }

    /**
     * @param observacao the observacao to set
     */
    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    /**
     * @return the colaboradorResponsavel
     */
    public UsuarioVO getColaboradorResponsavel() {
    	if(colaboradorResponsavel == null){
    		colaboradorResponsavel = new UsuarioVO();
    	}
        return colaboradorResponsavel;
    }

    /**
     * @param colaboradorResponsavel the colaboradorResponsavel to set
     */
    public void setColaboradorResponsavel(UsuarioVO colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    /**
     * @return the clienteQueIndicou
     */
    public ClienteVO getClienteQueIndicou() {
    	if(clienteQueIndicou == null){
    		clienteQueIndicou = new ClienteVO();
    	}
        return clienteQueIndicou;
    }

    /**
     * @param clienteQueIndicou the clienteQueIndicou to set
     */
    public void setClienteQueIndicou(ClienteVO clienteQueIndicou) {
        this.clienteQueIndicou = clienteQueIndicou;
    }

    /**
     * @return the indicadoVOs
     */
    public List<IndicadoVO> getIndicadoVOs() {
        if (indicadoVOs == null) {
            indicadoVOs = new ArrayList<IndicadoVO>();
        }
        return indicadoVOs;
    }

    /**
     * @param indicadoVOs the indicadoVOs to set
     */
    public void setIndicadoVOs(List<IndicadoVO> indicadoVOs) {
        this.indicadoVOs = indicadoVOs;
    }

    public String getIndicados() {
        boolean primeiro = true;
        StringBuilder ret = new StringBuilder();
        for(IndicadoVO aux : indicadoVOs) {
            if(primeiro)
                primeiro = false;
            else
                ret.append(", ");
            ret.append(aux.getNomeIndicado());
        }
        return ret.toString();
    }

    public Date getDia() {
        if (dia == null) {
            dia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dia);
    }
    
    public String getDia_ApresentarRel() {
    	if(dia == null){
    		return "";
    	}
    	return (Uteis.getData(dia));
    }

    /**
     * @param dia the dia to set
     */
    public void setDia(Date dia) {
        this.dia = dia;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDia_Apresentar() {
        return (Uteis.getDataComHora(getDia()));
    }

    /**
     * @return the evento
     */
    public EventoVO getEvento() {
    	if(evento == null){
    		evento = new EventoVO();
    	}
        return evento;
    }

    /**
     * @param evento the evento to set
     */
    public void setEvento(EventoVO evento) {
        this.evento = evento;
    }

	/**
	 * @return the diaAbertura
	 */
	public Date getDiaAbertura() {
		return diaAbertura;
	}

	/**
	 * @param diaAbertura the diaAbertura to set
	 */
	public void setDiaAbertura(Date diaAbertura) {
		this.diaAbertura = diaAbertura;
	}

	/**
	 * @param empresa the empresa to set
	 */
	public void setEmpresa(EmpresaVO empresa) {
		this.empresa = empresa;
	}

	/**
	 * @return the empresa
	 */
	public EmpresaVO getEmpresa() {
		if(empresa == null){
			empresa = new EmpresaVO();
		}
		return empresa;
	}

    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }

    public IndicadoVO getIndicadoMobile() {
        return indicadoMobile;
    }

    public void setIndicadoMobile(IndicadoVO indicadoMobile) {
        this.indicadoMobile = indicadoMobile;
    }

    public PessoaVO getPessoaIndicouVO() {
        if (pessoaIndicouVO == null) {
            pessoaIndicouVO = new PessoaVO();
        }
        return pessoaIndicouVO;
    }

    public void setPessoaIndicouVO(PessoaVO pessoaIndicouVO) {
        this.pessoaIndicouVO = pessoaIndicouVO;
    }

}
