/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class CamposGenericosTO extends SuperTO{
    
    private String campo1 = "SIM";
    private String campo2 = "NÃO";
    private String campo3;
    private String campo4;
    private String campo5;

    public String getCampo1() {
        return campo1;
    }

    public void setCampo1(String campo1) {
        this.campo1 = campo1;
    }

    public String getCampo2() {
        return campo2;
    }

    public void setCampo2(String campo2) {
        this.campo2 = campo2;
    }

    public String getCampo3() {
        if(campo3 == null){
            campo3 = "";
        }
        return campo3;
    }

    public void setCampo3(String campo3) {
        this.campo3 = campo3;
    }

    public String getCampo4() {
        return campo4;
    }

    public void setCampo4(String campo4) {
        this.campo4 = campo4;
    }

    public String getCampo5() {
        return campo5;
    }

    public void setCampo5(String campo5) {
        this.campo5 = campo5;
    }

    public CamposGenericosTO() {
    }
    
    @Override
    public String toString(){
        String str = (UteisValidacao.emptyString(campo1) ? "" : (";"+campo1.toUpperCase()))+
                (UteisValidacao.emptyString(campo2) ? "" : (";"+campo2.toUpperCase()))+
                (UteisValidacao.emptyString(campo3) ? "" : (";"+campo3.toUpperCase()))+
                (UteisValidacao.emptyString(campo4) ? "" : (";"+campo4.toUpperCase()))+
                (UteisValidacao.emptyString(campo5) ? "" : (";"+campo5.toUpperCase()));
       return str.replaceFirst(";","");
    }
    
}
