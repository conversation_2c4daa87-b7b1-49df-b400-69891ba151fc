package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.TipoExigibilidadeISSEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ConfiguracaoNotaFiscalVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private TipoNotaFiscalEnum tipoNotaFiscal;
    private String descricao;
    private Double iss;
    private Double pis;
    private Double cofins;
    private Double irrf;
    private Double csll;
    private Double valorMinimoIRRF;
    private String codListaServico;
    private String codTributacaoMunicipal;
    private String cnae;
    private TipoExigibilidadeISSEnum exigibilidadeISS = TipoExigibilidadeISSEnum.EXIGIVEL;
    private String observacao;
    private String naturezaOperacao;
    private String cnpj;
    private boolean enviarNotaCidadeEmpresa = false;
    @ChaveEstrangeira
    private PaisVO paisVO;
    @ChaveEstrangeira
    private EstadoVO estadoVO;
    @ChaveEstrangeira
    private CidadeVO cidadeVO;
    private boolean cpfObrigatorio = true;
    private boolean emailObrigatorio = false;
    private boolean enderecoObrigatorio = true;
    private String serie;
    private boolean issRetido = false;
    private boolean usarDescricaoFormaPagamento = false;
    private boolean enviarObservacaoNaDescricao = false;
    private boolean apresentarDuracaoPlano = false;
    private boolean apresentarDescricaoParcela = false;
    @ChaveEstrangeira
    private EmpresaVO empresaVO;
    private boolean ativo = true;

    //config enotas
    private boolean enotas = false;
    private AmbienteEmissaoNotaFiscalEnum ambienteEmissao;
    private String idEnotas;
    private Date dataCadastro;
    private String inscricaoMunicipal;
    private String inscricaoEstadual;
    private String razaoSocial;
    private String nomeFantasia;
    private boolean optanteSimplesNacional = false;
    private String email;
    private String telefoneComercial;
    private boolean incentivadorCultural = false;
    private String regimeEspecialTributacao;
    private String descricaoServico;
    private boolean enviarEmailCliente = false;
    private boolean apresentarCompetencia = false;
    private boolean apresentarTipoIntegracaoPagamento = false;

    //Endereco notaFiscal
    private String logradouro;
    private String numero;
    private String complemento;
    private String bairro;
    private String cep;

    private String chaveCertificado;
    private String senhaCertificado;
    private String formatoCertificado;

    private String chaveLogotipo;
    private String formatoLogotipo;
    private Date dataCertificado;
    private Integer limiteComplementoEndereco;
    private Integer limiteDescricao;
    private Integer limiteObservacao;
    private String linkPrefeitura;

    private Double percentualAproximadoTributos;
    private Double percentualFederal;
    private Double percentualEstadual;
    private Double percentualMunicipal;

    @NaoControlarLogAlteracao
    private String urlDownloadLogotipo;
    @NaoControlarLogAlteracao
    private String urlDownloadCertificado;

    @NaoControlarLogAlteracao
    private transient byte logotipoByte[];

    //Configuração de Emissao HOMOLOGAÇÃO
    @NaoControlarLogAlteracao
    private ConfiguracaoNotaFiscalAmbienteVO configHomologacaoVO;
    //Configuração de Emissao PRODUÇÃO
    @NaoControlarLogAlteracao
    private ConfiguracaoNotaFiscalAmbienteVO configProducaoVO;
    @NaoControlarLogAlteracao
    private int idTipoExigibilidadeISSEnum;

    private String caminhoCertificadoTemp;


    public static void validarDados(ConfiguracaoNotaFiscalVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("O campo DESCRIÇÃO (Configuração Emissão) deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            throw new ConsistirException("O campo EMPRESA (Configuração Emissão) deve ser informado.");
        }
      /*  if(obj.isNovoObj()){
            validarExisteConfigNotaFiscalAtiva(obj);
        }*/
    }

    public static void validarExisteConfigNotaFiscalAtiva(ConfiguracaoNotaFiscalVO obj) throws ConsistirException {
        List<ConfiguracaoNotaFiscalVO> listaConfigNotaFiscalVO = new ArrayList<>();
        try {
            listaConfigNotaFiscalVO = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(obj.getEmpresaVO().getCodigo(),null, true, true, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (obj.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE) && !UteisValidacao.emptyList(listaConfigNotaFiscalVO)){
            throw new ConsistirException("Existe um NFS-e ou NFC-e ou NF-e já configurado. Inative antes de configurar um novo NF-e");
        }

        for (ConfiguracaoNotaFiscalVO item: listaConfigNotaFiscalVO){
            if(item.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE) || item.getTipoNotaFiscal().equals(obj.getTipoNotaFiscal())){
                throw new ConsistirException("Existe uma configuração NFe ou do mesmo tipo já configurado. Inative antes de configurar um novo");
            }
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setRazaoSocial(getRazaoSocial().toUpperCase());
        setNomeFantasia(getNomeFantasia().toUpperCase());
        setEmail(getEmail().toUpperCase());
        setBairro(getBairro().toUpperCase());
        setComplemento(getComplemento().toUpperCase());
        setLogradouro(getLogradouro().toUpperCase());
    }

    public boolean isApresentarSelecaoMostrarCompetencia(){
        TipoRelatorioDF tipoGestaoNFSe = TipoRelatorioDF.getTipoRelatorioDF(getEmpresaVO().getTipoGestaoNFSe());
        return  tipoGestaoNFSe != null && tipoGestaoNFSe.equals(TipoRelatorioDF.COMPETENCIA);
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getIss() {
        if (iss == null) {
            iss = 0.0;
        }
        return iss;
    }

    public void setIss(Double iss) {
        this.iss = Math.round(iss * 1000000000) / 1000000000.0;
    }

    public Double getPis() {
        if (pis == null) {
            pis = 0.0;
        }
        return pis;
    }

    public void setPis(Double pis) {
        this.pis = pis;
    }

    public Double getCofins() {
        if (cofins == null) {
            cofins = 0.0;
        }
        return cofins;
    }

    public void setCofins(Double cofins) {
        this.cofins = cofins;
    }

    public Double getIrrf() {
        if (irrf == null) {
            irrf = 0.0;
        }
        return irrf;
    }

    public void setIrrf(Double irrf) {
        this.irrf = irrf;
    }

    public Double getCsll() {
        if(csll == null) {
            csll = 0.0;
        }
        return csll;
    }

    public void setCsll(Double csll) {
        this.csll = csll;
    }

    public Double getValorMinimoIRRF() {
        if (valorMinimoIRRF == null) {
            valorMinimoIRRF = 0.0;
        }
        return valorMinimoIRRF;
    }

    public void setValorMinimoIRRF(Double valorMinimoIRRF) {
        this.valorMinimoIRRF = valorMinimoIRRF;
    }

    public String getCodListaServico() {
        if (codListaServico == null) {
            codListaServico = "";
        }
        return codListaServico;
    }

    public void setCodListaServico(String codListaServico) {
        this.codListaServico = codListaServico;
    }

    public String getCodTributacaoMunicipal() {
        if (codTributacaoMunicipal == null) {
            codTributacaoMunicipal = "";
        }
        return codTributacaoMunicipal;
    }

    public void setCodTributacaoMunicipal(String codTributacaoMunicipal) {
        this.codTributacaoMunicipal = codTributacaoMunicipal;
    }

    public String getCnae() {
        if (cnae == null) {
            cnae = "";
        }
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public TipoExigibilidadeISSEnum getExigibilidadeISS() {
        if (exigibilidadeISS == null) {
            exigibilidadeISS = TipoExigibilidadeISSEnum.EXIGIVEL;
        }
        return exigibilidadeISS;
    }

    public void setExigibilidadeISS(TipoExigibilidadeISSEnum exigibilidadeISS) {
        this.exigibilidadeISS = exigibilidadeISS;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getNaturezaOperacao() {
        if (naturezaOperacao == null) {
            naturezaOperacao = "";
        }
        return naturezaOperacao;
    }

    public void setNaturezaOperacao(String naturezaOperacao) {
        this.naturezaOperacao = naturezaOperacao;
    }

    public boolean isNFSe() {
        return getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE);
    }

    public boolean isNFe() {
        return getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE);
    }

    public boolean isNFCe() {
        return getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE);
    }

    public String getTitleObservacao() {
        if (isNFSe() || isNFe()) {
            return "TAG's disponíveis:<br/>[VALOR_NFSE] = valor total da nota.<br/>" +
                    "[VALOR_ISS] = valor em reais da porcentagem do ISS, sobre o total da nota.<br/>" +
                    "[VALOR_PIS] = valor em reais da porcentagem do PIS, sobre o total da nota.<br/>" +
                    "[VALOR_COFINS] = valor em reais da porcentagem do COFINS, sobre o total da nota.<br/>" +
                    "[VALOR_TOTAL_IMPOSTO] = soma dos valores em reais das porcentagens de ISS, PIS e COFINS sobre o valor da nota.<br/>" +
                    "[TRIBUTOS_VALOR] = valor em reais da porcentagem cadastrada no campo \"Valor Aproximado de Tributos(%)\", sobre o total da nota.<br/>" +
                    "[TRIBUTOS_PERCENTUAL] = percentual bruto cadastrado no campo \"Valor Aproximado de Tributos(%)\". (Ex: \"17%\").<br/>" +
                    "[VALOR_FEDERAL] = valor em reais do Percentual Federal informada no cadastro, sobre o total da nota.<br/>" +
                    "[VALOR_ESTADUAL] = valor em reais do Percentual Estadual informada no cadastro, sobre o total da nota.<br/>" +
                    "[VALOR_MUNICIPAL] = valor em reais do Percentual Municipal informada no cadastro, sobre o total da nota.";
        } else {
            return "";
        }
    }

    public String getTitleEnviarObservacao() {
        return "Caso esta esteja marcada o sistema ira substituir a nomenclatura<br/> do produto/serviço, pelo que estiver no campo de observação.";
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isEnviarObservacaoNaDescricao() {
        return enviarObservacaoNaDescricao;
    }

    public void setEnviarObservacaoNaDescricao(boolean enviarObservacaoNaDescricao) {
        this.enviarObservacaoNaDescricao = enviarObservacaoNaDescricao;
    }

    public String getCnpj() {
        if (cnpj == null) {
            cnpj = "";
        }
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public boolean isEnviarNotaCidadeEmpresa() {
        return enviarNotaCidadeEmpresa;
    }

    public void setEnviarNotaCidadeEmpresa(boolean enviarNotaCidadeEmpresa) {
        this.enviarNotaCidadeEmpresa = enviarNotaCidadeEmpresa;
    }

    public PaisVO getPaisVO() {
        if (paisVO == null) {
            paisVO = new PaisVO();
        }
        return paisVO;
    }

    public void setPaisVO(PaisVO paisVO) {
        this.paisVO = paisVO;
    }

    public EstadoVO getEstadoVO() {
        if (estadoVO == null) {
            estadoVO = new EstadoVO();
        }
        return estadoVO;
    }

    public void setEstadoVO(EstadoVO estadoVO) {
        this.estadoVO = estadoVO;
    }

    public CidadeVO getCidadeVO() {
        if (cidadeVO == null) {
            cidadeVO = new CidadeVO();
        }
        return cidadeVO;
    }

    public void setCidadeVO(CidadeVO cidadeVO) {
        this.cidadeVO = cidadeVO;
    }


    public boolean isCpfObrigatorio() {
        return cpfObrigatorio;
    }

    public void setCpfObrigatorio(boolean cpfObrigatorio) {
        this.cpfObrigatorio = cpfObrigatorio;
    }

    public boolean isEmailObrigatorio() {
        return emailObrigatorio;
    }

    public void setEmailObrigatorio(boolean emailObrigatorio) {
        this.emailObrigatorio = emailObrigatorio;
    }

    public boolean isEnderecoObrigatorio() {
        return enderecoObrigatorio;
    }

    public void setEnderecoObrigatorio(boolean enderecoObrigatorio) {
        this.enderecoObrigatorio = enderecoObrigatorio;
    }

    public String getSerie() {
        if (serie == null) {
            serie = "";
        }
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public boolean isIssRetido() {
        return issRetido;
    }

    public void setIssRetido(boolean issRetido) {
        this.issRetido = issRetido;
    }

    public boolean isUsarDescricaoFormaPagamento() {
        return usarDescricaoFormaPagamento;
    }

    public void setUsarDescricaoFormaPagamento(boolean usarDescricaoFormaPagamento) {
        this.usarDescricaoFormaPagamento = usarDescricaoFormaPagamento;
    }

    public boolean isApresentarDuracaoPlano() {
        return apresentarDuracaoPlano;
    }

    public void setApresentarDuracaoPlano(boolean apresentarDuracaoPlano) {
        this.apresentarDuracaoPlano = apresentarDuracaoPlano;
    }

    public boolean isApresentarDescricaoParcela() {
        return apresentarDescricaoParcela;
    }

    public void setApresentarDescricaoParcela(boolean apresentarDescricaoParcela) {
        this.apresentarDescricaoParcela = apresentarDescricaoParcela;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isEnotas() {
        return enotas;
    }

    public void setEnotas(boolean enotas) {
        this.enotas = enotas;
    }

    public AmbienteEmissaoNotaFiscalEnum getAmbienteEmissao() {
        if (ambienteEmissao == null) {
            ambienteEmissao = AmbienteEmissaoNotaFiscalEnum.HOMOLOGACAO;
        }
        return ambienteEmissao;
    }

    public void setAmbienteEmissao(AmbienteEmissaoNotaFiscalEnum ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    public String getIdEnotas() {
        if (idEnotas == null) {
            idEnotas = "";
        }
        return idEnotas;
    }

    public void setIdEnotas(String idEnotas) {
        this.idEnotas = idEnotas;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getInscricaoMunicipal() {
        if (inscricaoMunicipal == null) {
            inscricaoMunicipal = "";
        }
        return inscricaoMunicipal;
    }

    public void setInscricaoMunicipal(String inscricaoMunicipal) {
        this.inscricaoMunicipal = inscricaoMunicipal;
    }

    public String getInscricaoEstadual() {
        if (inscricaoEstadual == null) {
            inscricaoEstadual = "";
        }
        return inscricaoEstadual;
    }

    public void setInscricaoEstadual(String inscricaoEstadual) {
        this.inscricaoEstadual = inscricaoEstadual;
    }

    public String getNomeFantasia() {
        if (nomeFantasia == null) {
            nomeFantasia = "";
        }
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        if (razaoSocial == null) {
            razaoSocial = "";
        }
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public boolean isOptanteSimplesNacional() {
        return optanteSimplesNacional;
    }

    public void setOptanteSimplesNacional(boolean optanteSimplesNacional) {
        this.optanteSimplesNacional = optanteSimplesNacional;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefoneComercial() {
        if (telefoneComercial == null) {
            telefoneComercial = "";
        }
        return telefoneComercial;
    }

    public void setTelefoneComercial(String telefoneComercial) {
        this.telefoneComercial = telefoneComercial;
    }

    public boolean isIncentivadorCultural() {
        return incentivadorCultural;
    }

    public void setIncentivadorCultural(boolean incentivadorCultural) {
        this.incentivadorCultural = incentivadorCultural;
    }

    public String getRegimeEspecialTributacao() {
        if (regimeEspecialTributacao == null) {
            regimeEspecialTributacao = "";
        }
        return regimeEspecialTributacao;
    }

    public void setRegimeEspecialTributacao(String regimeEspecialTributacao) {
        this.regimeEspecialTributacao = regimeEspecialTributacao;
    }

    public String getDescricaoServico() {
        if (descricaoServico == null) {
            descricaoServico = "";
        }
        return descricaoServico;
    }

    public void setDescricaoServico(String descricaoServico) {
        this.descricaoServico = descricaoServico;
    }

    public boolean isEnviarEmailCliente() {
        return enviarEmailCliente;
    }

    public void setEnviarEmailCliente(boolean enviarEmailCliente) {
        this.enviarEmailCliente = enviarEmailCliente;
    }

    public String getLogradouro() {
        if (logradouro == null) {
            logradouro = "";
        }
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        if (complemento == null) {
            complemento = "";
        }
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getBairro() {
        if (bairro == null) {
            bairro = "";
        }
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        if (cep == null) {
            cep = "";
        }
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public ConfiguracaoNotaFiscalAmbienteVO getConfigHomologacaoVO() {
        if (configHomologacaoVO == null) {
            configHomologacaoVO = new ConfiguracaoNotaFiscalAmbienteVO();
        }
        return configHomologacaoVO;
    }

    public void setConfigHomologacaoVO(ConfiguracaoNotaFiscalAmbienteVO configHomologacaoVO) {
        this.configHomologacaoVO = configHomologacaoVO;
    }

    public ConfiguracaoNotaFiscalAmbienteVO getConfigProducaoVO() {
        if (configProducaoVO == null) {
            configProducaoVO = new ConfiguracaoNotaFiscalAmbienteVO();
        }
        return configProducaoVO;
    }

    public void setConfigProducaoVO(ConfiguracaoNotaFiscalAmbienteVO configProducaoVO) {
        this.configProducaoVO = configProducaoVO;
    }

    public boolean isAmbienteEmissaoProducao() {
        return getAmbienteEmissao().equals(AmbienteEmissaoNotaFiscalEnum.PRODUCAO);
    }

    public TipoNotaFiscalEnum getTipoNotaFiscal() {
        if (tipoNotaFiscal == null) {
            tipoNotaFiscal = TipoNotaFiscalEnum.NFSE;
        }
        return tipoNotaFiscal;
    }

    public void setTipoNotaFiscal(TipoNotaFiscalEnum tipoNotaFiscal) {
        this.tipoNotaFiscal = tipoNotaFiscal;
    }

    public String getChaveCertificado() {
        if (chaveCertificado == null) {
            chaveCertificado = "";
        }
        return chaveCertificado;
    }

    public void setChaveCertificado(String chaveCertificado) {
        this.chaveCertificado = chaveCertificado;
    }

    public String getSenhaCertificado() {
        if (senhaCertificado == null) {
            senhaCertificado = "";
        }
        return senhaCertificado;
    }

    public void setSenhaCertificado(String senhaCertificado) {
        this.senhaCertificado = senhaCertificado;
    }

    public byte[] getLogotipoByte() {
        return logotipoByte;
    }

    public void setLogotipoByte(byte[] logotipoByte) {
        this.logotipoByte = logotipoByte;
    }

    public String getFormatoCertificado() {
        if (formatoCertificado == null) {
            formatoCertificado = "";
        }
        return formatoCertificado;
    }

    public void setFormatoCertificado(String formatoCertificado) {
        this.formatoCertificado = formatoCertificado;
    }

    public String getFormatoLogotipo() {
        if (formatoLogotipo == null) {
            formatoLogotipo = "";
        }
        return formatoLogotipo;
    }

    public void setFormatoLogotipo(String formatoLogotipo) {
        this.formatoLogotipo = formatoLogotipo;
    }

    public String getChaveLogotipo() {
        if (chaveLogotipo == null) {
            chaveLogotipo = "";
        }
        return chaveLogotipo;
    }

    public void setChaveLogotipo(String chaveLogotipo) {
        this.chaveLogotipo = chaveLogotipo;
    }

    public String getUrlDownloadLogotipo() {
        if (urlDownloadLogotipo == null) {
            urlDownloadLogotipo = "";
        }
        return urlDownloadLogotipo;
    }

    public void setUrlDownloadLogotipo(String urlDownloadLogotipo) {
        this.urlDownloadLogotipo = urlDownloadLogotipo;
    }

    public String getUrlDownloadCertificado() {
        if (urlDownloadCertificado == null) {
            urlDownloadCertificado = "";
        }
        return urlDownloadCertificado;
    }

    public void setUrlDownloadCertificado(String urlDownloadCertificado) {
        this.urlDownloadCertificado = urlDownloadCertificado;
    }

    public boolean isApresentarCompetencia() {
        return apresentarCompetencia;
    }

    public void setApresentarCompetencia(boolean apresentarCompetencia) {
        this.apresentarCompetencia = apresentarCompetencia;
    }

    public boolean isApresentarTipoIntegracaoPagamento() {
        return apresentarTipoIntegracaoPagamento;
    }

    public void setApresentarTipoIntegracaoPagamento(boolean apresentarTipoIntegracaoPagamento) {
        this.apresentarTipoIntegracaoPagamento = apresentarTipoIntegracaoPagamento;
    }

    public Date getDataCertificado() {
        return dataCertificado;
    }

    public void setDataCertificado(Date dataCertificado) {
        this.dataCertificado = dataCertificado;
    }

    public String getDataCertificado_Apresentar() {
        if (getDataCertificado() != null) {
            return Uteis.getDataComHora(getDataCertificado());
        } else {
            return "";
        }
    }

    public Integer getLimiteComplementoEndereco() {
        if (limiteComplementoEndereco == null) {
            limiteComplementoEndereco = 0;
        }
        return limiteComplementoEndereco;
    }

    public void setLimiteComplementoEndereco(Integer limiteComplementoEndereco) {
        this.limiteComplementoEndereco = limiteComplementoEndereco;
    }

    public Double getPercentualAproximadoTributos() {
        if (percentualAproximadoTributos == null) {
            percentualAproximadoTributos = 0.0;
        }
        return percentualAproximadoTributos;
    }

    public void setPercentualAproximadoTributos(Double percentualAproximadoTributos) {
        this.percentualAproximadoTributos = percentualAproximadoTributos;
    }

    public Double getPercentualFederal() {
        if (percentualFederal == null) {
            percentualFederal = 0.0;
        }
        return percentualFederal;
    }

    public void setPercentualFederal(Double percentualFederal) {
        this.percentualFederal = percentualFederal;
    }

    public Double getPercentualEstadual() {
        if (percentualEstadual == null) {
            percentualEstadual = 0.0;
        }
        return percentualEstadual;
    }

    public void setPercentualEstadual(Double percentualEstadual) {
        this.percentualEstadual = percentualEstadual;
    }

    public Double getPercentualMunicipal() {
        if (percentualMunicipal == null) {
            percentualMunicipal = 0.0;
        }
        return percentualMunicipal;
    }

    public void setPercentualMunicipal(Double percentualMunicipal) {
        this.percentualMunicipal = percentualMunicipal;
    }

    public Integer getLimiteDescricao() {
        if (limiteDescricao == null) {
            limiteDescricao = 0;
        }
        return limiteDescricao;
    }

    public void setLimiteDescricao(Integer limiteDescricao) {
        this.limiteDescricao = limiteDescricao;
    }

    public Integer getLimiteObservacao() {
        if (limiteObservacao == null) {
            limiteObservacao = 0;
        }
        return limiteObservacao;
    }

    public void setLimiteObservacao(Integer limiteObservacao) {
        this.limiteObservacao = limiteObservacao;
    }

    public String getLinkPrefeitura() {
        if (linkPrefeitura == null) {
            linkPrefeitura = "";
        }
        return linkPrefeitura;
    }

    public void setLinkPrefeitura(String linkPrefeitura) {
        this.linkPrefeitura = linkPrefeitura;
    }

    public int getIdTipoExigibilidadeISSEnum() {
        idTipoExigibilidadeISSEnum = exigibilidadeISS.getId();
        return idTipoExigibilidadeISSEnum;
    }

    public void setIdTipoExigibilidadeISSEnum(int idTipoExigibilidadeISSEnum) {
        for (int i = 0; i < TipoExigibilidadeISSEnum.values().length; i++) {
            if (idTipoExigibilidadeISSEnum == TipoExigibilidadeISSEnum.values()[i].getId()) {
                exigibilidadeISS = TipoExigibilidadeISSEnum.values()[i];
            }
        }
        this.idTipoExigibilidadeISSEnum = idTipoExigibilidadeISSEnum;
    }

    public String getCaminhoCertificadoTemp() {
        return caminhoCertificadoTemp;
    }

    public void setCaminhoCertificadoTemp(String caminhoCertificadoTemp) {
        this.caminhoCertificadoTemp = caminhoCertificadoTemp;
    }
}
