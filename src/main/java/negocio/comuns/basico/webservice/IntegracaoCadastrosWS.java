/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.webservice;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.comuns.json.DadosBITreinoJSON;
import br.com.pactosolucoes.comuns.json.DadosGameJSON;
import br.com.pactosolucoes.comuns.json.EstatisticaSolicitacaoJSON;
import br.com.pactosolucoes.comuns.json.EstatisticaTicketJSON;
import br.com.pactosolucoes.comuns.json.SimplesJSON;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.controle.json.cliente.FrequenciaAlunoFeraJSON;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.gympass.CheckinGympassService;
import br.com.pactosolucoes.integracao.log.LogJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import cfin.wrapper.ParcelaConsultada;
import cfin.wrapper.TResultadoBoleto;
import cfin.wrapper.TResultadoParcelaConsultada;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.UsuarioControle;
import controle.contrato.RetornoTrancamentoContratoControle;
import negocio.comuns.ConfiguracaoEmpresaTotemVO;
import negocio.comuns.TotemTO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.RecursoEmpresaTO;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ArquivoVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteMensagemWS;
import negocio.comuns.basico.ClienteTokenVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ClienteWS;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ConsultaClienteWS;
import negocio.comuns.basico.ConsultaIntegracaoFera;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.SolicitacaoWS;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.basico.enumerador.OpcoesNomenclaturaVendaCreditoEnum;
import negocio.comuns.basico.xmlgen.ClienteXML;
import negocio.comuns.basico.xmlgen.ColaboradorXML;
import negocio.comuns.basico.xmlgen.RiscoXML;
import negocio.comuns.contrato.AtestadoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ContratoWS;
import negocio.comuns.contrato.DadosContratoOperacaoWS;
import negocio.comuns.contrato.DadosRetornoOperacaoWS;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.feed.FeedGestaoVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.DFSinteticoDetalheVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ParcelaWS;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisJSON;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeFactory;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.webservice.IntegracaoColaborador;
import negocio.facade.jdbc.basico.webservice.UsuarioJSON;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.DadosGerencialPmg;
import negocio.facade.jdbc.financeiro.FinanceiroPacto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.DFSinteticoDetalheInterfaceFacade;
import negocio.interfaces.basico.DadosGameInterfaceFacade;
import negocio.interfaces.basico.VinculoInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.financeiro.DadosGerencialPmgInterfaceFacade;
import negocio.interfaces.financeiro.TicketMedioInterfaceFacade;
import negocio.intranet.SolicitacaoJSON;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.LoginSiteRedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import relatorio.negocio.comuns.basico.ConsultaTO;
import relatorio.negocio.comuns.basico.ConsultasCadastradasTO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.crm.impl.RDStationServiceImpl;
import servicos.crm.intf.RDStationServiceInterface;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.adm.beans.EmpresaWS;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.integracao.treino.TreinoWS;
import servicos.notificador.NotificadorRecursoEmpresaServiceControle;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.telegram.TelegramService;
import servicos.util.ExecuteRequestHttpService;
import webservice.controle.WebserviceControle;
import ws.nfe.TResultadoTransacaoWS;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static controle.arquitetura.SuperControle.notificarRecursoEmpresa;
import static controle.basico.CanalPactoControle.LIMIT_CONSULTA_SOLICITACOES_CONCLUIDAS;
import static controle.basico.CanalPactoControle.LIMIT_SOLICITACOES_ESTATISTICA;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * <AUTHOR> Data:10/11/10 Objetivo do WS: Integração dos dados cadastrais
 *         do ZillyonWeb com outros Sistemas Legados da Pacto criados em Delphi7.
 *         Documentação: Arquivo:"Form 2.1.1 - Especificação de Requisitos de
 *         Software.doc" anexado ao projeto:7072 da intranet Service BMP.
 */
@WebService(name = "IntegracaoCadastrosWS", serviceName = "IntegracaoCadastrosWS")
public class IntegracaoCadastrosWS {

    public static final String CHAVE_URL_MOBILE = "Tr3in0";
    private static final String CHAVE_APP_LOGIN = "appLoginFoto";
    private static final String RETURN = "RETURN";
    private static final String ERRO = "ERRO";


    private final String TOKEN_PADRAO = "1234321567890";

    private TreinoWS treinoWS;

    /*
     * Objetivo do Mãtodo: Verificar se o requisitante da consulta tem permissão
     * para realizar a consulta.
     * Tipo de Retorno   : True: Se tem permissão ou False: Se não tem permissão.
     * Requisitantes que tem permissão para realizar consultas: Musculacao e Vida
     */
    private boolean requisitanteTemPermissaoParaConsultar(String textoPesquisar, int empresa, String chave, String recurso) {
        /* O nome do requisistante tem 10 caracteres e vem juntamente com o nome a ser pesquisado*/
        String requisitante = (textoPesquisar.substring(0, 10));
        requisitante = requisitante.toUpperCase().trim();
        notificarRecurso(empresa, recurso, chave, requisitante);
        return (requisitante.equals("CHVMUSC"))
                || (requisitante.equals("CHVVIDA"))
                || (requisitante.equals("ZILLYONWEB"));
    }

    @WebMethod(operationName = "urlFotoEmpresa")
    public String urlFotoEmpresa(@WebParam(name = "key") final String key,
                                 @WebParam(name = "unidade") final Integer unidade) {
        try {
            final String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, unidade.toString());
            final String urlFotoEmpresa = Uteis.getPaintFotoDaNuvem(genKey);
            return urlFotoEmpresa;
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private boolean requisitanteTemPermissaoParaConsultarEspecifico(String textoPesquisar, MetodosPermissoes metodo) {
        if(metodo == null){
            return false;
        }
        /* O nome do requisistante tem 10 caracteres e vem juntamente com o nome a ser pesquisado*/
        String requisitante = (textoPesquisar.substring(0, 10));
        for(String chave : metodo.chavesPermitidas){
            if(chave.trim().equals(requisitante.trim())){
                return true;
            }
        }
        return false;
    }

    private String retornarTextoSemChave(String textoPesquisar) {
        return (textoPesquisar.substring(10, textoPesquisar.length()));
    }

    private String retornarRequisitante(String textoPesquisar) {
        if (textoPesquisar != null && !textoPesquisar.isEmpty()) {
            return textoPesquisar.trim();
        }
        return null;
    }

    @WebMethod
    public String gravarResposta(@WebParam(name = "key") final String key,
                                 @WebParam(name = "codigoNotificacao") final String codigoNotificacao,
                                 @WebParam(name = "resposta") final String resposta) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).getHistoricoContatoDao().gravarResposta(Integer.valueOf(codigoNotificacao), resposta);
            return "OK";
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    /**
     * Objetivo do mãtodo: Pesquisar clientes pelo nome. Tipo de Retorno :
     * Retorna uma "String", cujo conteãdo ã um arquivo XML no formato
     * DataPacket, com a relação dos clientes consultados.
     */
    @WebMethod(operationName = "consultarClientesPeloNome")
    public String consultarClientesPeloNome(
            @WebParam(name = "idEmpresa") int idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomePesquisar") String nomePesquisar) throws Exception {
        List<ClienteVO> listaClientes;
        String req = retornarRequisitante(nomePesquisar);
        if (requisitanteTemPermissaoParaConsultar(nomePesquisar, idEmpresa, key, "consultarClientesPeloNome")) {
            nomePesquisar = retornarTextoSemChave(nomePesquisar);
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            listaClientes = acessoControle.consultarClientesPeloNome(nomePesquisar, idEmpresa, 50);
            logar(key, req, idEmpresa, String.format("consultarClientesPeloNome, tamanho: %s", listaClientes.size()));

            return (new ClienteXML()).criarArquivoXMLDataPacket(listaClientes);
        } else {
            return retornarMsgNaoAutorizado(key, req, idEmpresa);
        }
    }

    private void notificarRecurso(int empresa, String recurso, String chave, String requisitante){
        try {
            RecursoEmpresaTO recursoEmpresaTO = new RecursoEmpresaTO();
            recursoEmpresaTO.setDate(Calendario.hoje());
            recursoEmpresaTO.setRecurso("INTEGRACAO_CADASTROS_WS" + recurso);
            recursoEmpresaTO.setEmpresa(empresa);
            recursoEmpresaTO.setChave(chave);
            recursoEmpresaTO.setModulos("");
            recursoEmpresaTO.setUsuario(requisitante);
            recursoEmpresaTO.setNomeEmpresa("");
            recursoEmpresaTO.setCidade("");
            recursoEmpresaTO.setUf("");
            recursoEmpresaTO.setPais("");
            NotificadorRecursoEmpresaServiceControle.adicionarNotificacao(recursoEmpresaTO);
        }catch (Exception e){
            //igoner
        }

    }

    @WebMethod
    public String marcarStatusBG(@WebParam(name = "key") final String key,
                                         @WebParam(name = "matricula") final Integer matricula){
        try {
            DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().marcarStatusBG(matricula);
            return "OK";
        }catch (Exception e){
            return e.getMessage();
        }

    }

    /**
     * Objetivo do mãtodo: Pesquisar clientes pela matrãcula. Tipo de Retorno :
     * Retorna uma "String", cujo conteãdo ã um arquivo XML no formato
     * DataPacket, com a relação dos clientes consultados.
     */
    @WebMethod(operationName = "consultarClientesPelaMatricula")
    public String consultarClientesPelaMatricula(
            @WebParam(name = "idEmpresa") int idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "matriculaPesquisar") String matriculaPesquisar) throws Exception {
        List<ClienteVO> listaClientes = null;
        String req = retornarRequisitante(matriculaPesquisar);
        if (requisitanteTemPermissaoParaConsultar(matriculaPesquisar, idEmpresa, key, "consultarClientesPelaMatricula")
                || requisitanteTemPermissaoParaConsultarEspecifico(matriculaPesquisar, MetodosPermissoes.CONSULTARCLIENTEPELAMATRICULA)) {
            matriculaPesquisar = retornarTextoSemChave(matriculaPesquisar);
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            listaClientes = acessoControle.consultarClientesPelaMatricula(matriculaPesquisar, idEmpresa);
            logar(key, req, idEmpresa, String.format("consultarClientesPelaMatricula, tamanho: %s",
                    new Object[]{
                            listaClientes.size()
                    }));
            return (new ClienteXML()).criarArquivoXMLDataPacket(listaClientes);
        } else {
            return retornarMsgNaoAutorizado(key, req, idEmpresa);
        }
    }

    @WebMethod
    public String persistirColaborador(
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            IntegracaoColaborador integracaoColaborador = new IntegracaoColaborador(DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            UsuarioJSON usuarioJSON = integracaoColaborador.gravarColaboradorIntegracao(json);
            retorno.put("success", true);
            retorno.put("usuario", usuarioJSON.toString());
        } catch (Exception ex) {
            try {
                org.json.JSONObject retErro = new org.json.JSONObject();
                retErro.put("success", false);
                retErro.put("mensagem", ex.getMessage());
                return retErro.toString();
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod(operationName = "consultarClientesPeloNomeNoLimits")
    public String consultarClientesPeloNomeNoLimits(
            @WebParam(name = "idEmpresa") int idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomePesquisar") String nomePesquisar) throws Exception {
        List<ClienteVO> listaClientes = null;
        String req = retornarRequisitante(nomePesquisar);
        if (requisitanteTemPermissaoParaConsultar(nomePesquisar, idEmpresa, key, "consultarClientesPeloNomeNoLimits")
                || requisitanteTemPermissaoParaConsultarEspecifico(nomePesquisar, MetodosPermissoes.CONSULTARCLIENTESPELONOME)) {
            nomePesquisar = retornarTextoSemChave(nomePesquisar);
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            listaClientes = acessoControle.consultarClientesPeloNome(nomePesquisar, idEmpresa, -1);
            logar(key, req, idEmpresa, String.format("consultarClientesPeloNomeNoLimits, tamanho: %s",
                    new Object[]{
                            listaClientes.size()
                    }));
            return (new ClienteXML()).criarArquivoXMLDataPacket(listaClientes);
        } else {
            return retornarMsgNaoAutorizado(key, req, idEmpresa);
        }
    }

    /**
     * Objetivo do mãtodo: Pesquisar professores pelo nome. Tipo de Retorno :
     * Retorna uma "String", cujo conteãdo ã um arquivo XML no formato
     * DataPacket, com a relação dos professores consultados.
     */
    @WebMethod(operationName = "consultarProfessoresPeloNome")
    public String consultarProfessoresPeloNome(
            @WebParam(name = "idEmpresa") int idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "nomePesquisar") String nomePesquisar) throws Exception {
        List listaColaborador = null;
        String req = retornarRequisitante(nomePesquisar);
        if (requisitanteTemPermissaoParaConsultar(nomePesquisar, idEmpresa, key, "consultarProfessoresPeloNome")) {
            nomePesquisar = retornarTextoSemChave(nomePesquisar);
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            listaColaborador = acessoControle.consultarProfessorPeloNome(nomePesquisar, idEmpresa);
            logar(key, req, idEmpresa, String.format("consultarProfessoresPeloNome, tamanho: %s",
                    new Object[]{
                            listaColaborador.size()
                    }));
            return (new ColaboradorXML()).criarArquivoXMLDataPacket(listaColaborador);
        } else {
            return retornarMsgNaoAutorizado(key, req, idEmpresa);
        }
    }

    @WebMethod(operationName = "consultarClientePorMatriculaExternaImportacao")
    public String consultarClientePorMatriculaExternaImportacao(
            @WebParam(name = "key") String key,
            @WebParam(name = "matriculaPesquisar") String matriculaPesquisar) throws Exception {
        List<ClienteVO> listaCliente = null;
        String req = retornarRequisitante(matriculaPesquisar);
        if (requisitanteTemPermissaoParaConsultar(matriculaPesquisar, 0, key, "consultarClientePorMatriculaExternaImportacao")) {
            matriculaPesquisar = retornarTextoSemChave(matriculaPesquisar);
            if (matriculaPesquisar.trim().equals("")) {
                final String msg = "É obrigatório informar uma matrícula para a pesquisa.";
                logar(key, req, 0, msg);
                return msg;
            }
            listaCliente = DaoAuxiliar.retornarAcessoControle(key).
                    getClienteDao().consultar("matriculaExterna = "
                            + matriculaPesquisar + " and coalesce(matriculaExterna,0) <> 0",
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            logar(key, req, 0, String.format("consultarClientePorMatriculaExternaImportacao, tamanho: %s",
                    new Object[]{
                            listaCliente.size()
                    }));
            return listaCliente != null && !listaCliente.isEmpty() ? listaCliente.get(0).getCodAcesso() : "";
        } else {
            return retornarMsgNaoAutorizado(key, req, 0);
        }
    }

    /**
     * Objetivo do mãtodo: Pesquisar grupo de risco do aluno pela matrãcula.
     * Tipo de Retorno : Retorna uma "String", cujo conteãdo ã um arquivo XML no
     * formato DataPacket, com a relação dos alunos consultados.
     */
    @WebMethod(operationName = "consultarGrupoDeRiscoPelaMatricula")
    public String consultarGrupoDeRiscoPelaMatricula(
            @WebParam(name = "idEmpresa") int idEmpresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "matriculaPesquisar") String matriculaPesquisar) throws Exception {
        List<RiscoVO> listaRisco = null;
        String req = retornarRequisitante(matriculaPesquisar);
        if (requisitanteTemPermissaoParaConsultar(matriculaPesquisar, idEmpresa, key, "consultarGrupoDeRiscoPelaMatricula")) {
            matriculaPesquisar = retornarTextoSemChave(matriculaPesquisar);
            if (matriculaPesquisar.trim().equals("")) {
                final String msg = "É obrigatório informar uma matrícula para a pesquisa.";
                logar(key, req, 0, msg);
                return msg;
            }
            listaRisco = DaoAuxiliar.retornarAcessoControle(key).getRiscoDao().
                    consultarPorMatriculaCliente(matriculaPesquisar, String.valueOf(idEmpresa), false,
                            Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            logar(key, req, 0, String.format("consultarGrupoDeRiscoPelaMatricula, tamanho: %s",
                    new Object[]{
                            listaRisco.size()
                    }));
            return (new RiscoXML()).criarArquivoXMLDataPacket(listaRisco);
        } else {
            return retornarMsgNaoAutorizado(key, req, idEmpresa);
        }
    }

    @WebMethod
    public String consultarClienteSinteticoPorEmailPessoa(
            @WebParam(name = "email") String email,
            @WebParam(name = "key") String key) throws Exception {

        SituacaoClienteSinteticoDWVO sit = DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().
                consultarClientePorEmailPessoa(email, Uteis.NIVELMONTARDADOS_TODOS);

        if (sit != null) {
            return sit.toJSON(false).toString();
        }

        return "";

    }

    private String retornarMsgNaoAutorizado(final String key,
                                            final String requisitante,
                                            final int idEmpresa) {
        final String msg = "Aplicação não autorizada para realizar pesquisa no WebService.";
        Logger.getLogger(IntegracaoCadastrosWS.class.getSimpleName()).log(Level.SEVERE,
                "Chave: {0} - Requisitante: {1} - Empresa: {2} - {3}",
                new Object[]{
                        key,
                        requisitante,
                        idEmpresa,
                        msg
                });

        return msg;
    }

    private void logar(final String key, final String requisitante,
                       final int idEmpresa, final String msg) {
        Logger.getLogger(IntegracaoCadastrosWS.class.getSimpleName()).log(Level.SEVERE,
                "Chave: {0} - Requisitante: {1} - Empresa: {2} - {3}",
                new Object[]{
                        key,
                        requisitante,
                        idEmpresa,
                        msg
                });

    }

    @WebMethod
    public String consultarEmpresas(@WebParam(name = "key") String key) throws Exception {
        List<EmpresaVO> empresas = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarEmpresas();
        if (empresas.isEmpty()) {
            return "";
        }

        JSONArray json = new JSONArray();
        for (EmpresaVO empresa : empresas) {
            json.put(empresa.toJSON());
        }
        return json.toString();
    }

    @WebMethod
    public String consultarConfiguracaoENotas(@WebParam(name = "key") String key) throws Exception {
        List<EmpresaVO> empresas = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarEmpresas();
        if (empresas.isEmpty()) {
            return "";
        }

        JSONArray json = new JSONArray();
        for (EmpresaVO empresa : empresas) {
            json.put(empresa.toJSON());
        }
        return json.toString();
    }

    @WebMethod
    public String consultarLocaisAcesso(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key) throws Exception {
        List<LocalAcessoVO> locais = DaoAuxiliar.retornarAcessoControle(key).
                getLocalAcessoDao().consultarPorEmpresa(empresa,true,
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (locais.isEmpty()) {
            return "";
        }
        JSONArray json = new JSONArray();
        for (LocalAcessoVO local : locais) {
            json.put(local.toJSON());
        }
        return json.toString();
    }

    @WebMethod
    public String consultarColetores(
            @WebParam(name = "key") String key,
            @WebParam(name = "localacesso") Integer localacesso) throws Exception {
        List<ColetorVO> coletores=null;
        try{
            coletores = DaoAuxiliar.retornarAcessoControle(key).getColetorDao().consultarColetores(localacesso, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }catch (Exception e){
            throw new Exception("Verifique se URL corresponde a Unidade desejada. "+e.getMessage());
        }
        if (coletores ==null || coletores.isEmpty()) {
            return "";
        }
        JSONArray json = new JSONArray();
        for (ColetorVO coletor : coletores) {
            json.put(coletor.toJSON());
        }
        return json.toString();
    }

    @WebMethod
    public String obterConfiguracaoEmailPadrao() throws Exception {
        ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
        // Setando configuraçães de email padrão
        JSONObject configs = new JSONObject();
        configs.put("loginServidorSmtp", config.getLogin());
        configs.put("senhaServidorSmtp", config.getSenha());
        configs.put("smtpPadrao", config.getMailServer());
        configs.put("emailRemet", config.getEmailPadrao());
        configs.put("portaPadrao", config.getPortaServer());
        configs.put("nomeRemet", config.getRemetentePadrao());
        configs.put("conexaoSegura", config.isConexaoSegura());
        configs.put("iniciarTLS", config.isIniciarTLS());
        return configs.toString();
    }


    @WebMethod
    public String consultarClientes(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "parametro") String parametro) throws Exception {
        List<ClienteVO> clientes;
        if (Uteis.getValidarStringSePossuiLetra(parametro)) {
            clientes = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorNomePessoa(parametro, empresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS, 50);
        } else {
            clientes = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorMatriculaOuCPF(parametro, empresa, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS);
        }
        if (clientes.isEmpty()) {
            return "";
        }
        JSONArray json = new JSONArray();
        for (ClienteVO cliente : clientes) {
            json.put(cliente.toJSON());
        }
        return json.toString();
    }

    @WebMethod
    public String consultarColaboradores(
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "key") String key,
            @WebParam(name = "parametro") String parametro) throws Exception {
        List<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
        if (UteisValidacao.emptyString(parametro)) {
            return "";
        }
        if (Uteis.getValidarStringSePossuiLetra(parametro)) {
            colaboradores = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarPorNomePessoa(parametro, empresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS);
        } else {
            ColaboradorVO colaborador = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarColaboradorPorCodigo(Integer.valueOf(parametro), empresa, true, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            if (colaborador == null || UteisValidacao.emptyNumber(colaborador.getCodigo())) {
                return "";
            }
            colaboradores.add(colaborador);
        }
        if (colaboradores.isEmpty()) {
            return "";
        }
        JSONArray json = new JSONArray();
        for (ColaboradorVO colaborador : colaboradores) {
            json.put(colaborador.toJSON());
        }
        return json.toString();
    }

    @WebMethod
    public String consultarClientesTreino(
            @WebParam(name = "cpf") String cpf,
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "matricula") Integer matricula,
            @WebParam(name = "nome") String nome) throws Exception {
        List<ClienteVO> clientes;
        StringBuilder sb = new StringBuilder(" 1 = 1 ");

        ClienteInterfaceFacade clienteDAO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
        VinculoInterfaceFacade vinculoDAO = DaoAuxiliar.retornarAcessoControle(key).getVinculoDao();
        if (UteisValidacao.emptyString(nome)
                && UteisValidacao.emptyString(cpf)
                && UteisValidacao.emptyNumber(matricula)) {
            throw new Exception("Informe o nome, cpf ou matrícula para a consulta");
        } else {
            sb.append(!UteisValidacao.emptyNumber(empresa) ? String.format(" and empresa = %s ", new Object[]{empresa}) : "");
            sb.append(!UteisValidacao.emptyString(nome) ? String.format(" and pessoa in (select codigo from pessoa where UPPER(nome) like '%%%s%%') ", new Object[]{nome.toUpperCase()}) : "");
            sb.append(!UteisValidacao.emptyString(cpf) ? String.format(" and pessoa in (select codigo from pessoa where cfp = '%s') ", new Object[]{cpf}) : "");
            sb.append(!UteisValidacao.emptyNumber(matricula) ? String.format(" and codigomatricula = %s", new Object[]{matricula}) : "");
            sb.append(" limit 20");
            clientes = clienteDAO.consultar(sb.toString(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            if (clientes.isEmpty()) {
                return "";
            }
            JSONArray json = new JSONArray();
            for (ClienteVO cliente : clientes) {
                List<VinculoVO> lista = vinculoDAO.consultarPorCodigoCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                JSONArray vinculosJSON = new JSONArray();
                for (VinculoVO v : lista) {
                    JSONObject o = new JSONObject();
                    o.put("codColaborador", v.getColaborador().getCodigo());
                    o.put("codPessoaColaborador", v.getColaborador().getPessoa().getCodigo());
                    o.put("nomeColaborador", v.getColaborador().getPessoa().getNome());
                    o.put("tipoVinculo", v.getTipoVinculo());
                    o.put("codigoCliente", v.getCliente().getCodigo());
                    vinculosJSON.put(o);
                }
                JSONObject obj = new JSONObject();
                obj.put("matricula", cliente.getCodigoMatricula());
                obj.put("codigoCliente", cliente.getCodigo());
                obj.put("nome", cliente.getPessoa().getNome());
                obj.put("codigoPessoa", cliente.getPessoa().getCodigo());
                obj.put("email", cliente.getPessoa().getEmailVOs().isEmpty() ? "" : ((EmailVO) cliente.getPessoa().getEmailVOs().get(0)).getEmail());
                obj.put("vinculos", vinculosJSON);
                obj.put("key",key);
                json.put(obj);
            }
            return json.toString();
        }
    }

    @WebMethod
    public String obterCliente(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "key") String key) {
        try {
            ClienteVO cliente = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return cliente.toJSON().toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    @WebMethod
    public String consultarClienteSintetico(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "key") String key) {
        try {
            SituacaoClienteSinteticoDWVO c = DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().consultarClienteWS(codigoCliente, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            ClienteVO cli = new ClienteVO();
            cli.setCodigo(codigoCliente);
            UsuarioMovelVO userMovel = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().consultarPorCliente(cli, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (userMovel != null && !UteisValidacao.emptyNumber(userMovel.getCodigo())) {
                c.setEmail(userMovel.getNome());
            } else {
                Email emailDao = DaoAuxiliar.retornarAcessoControle(key).getEmailDao();
                List emails = emailDao.consultarEmails(c.getCodigoPessoa(), Boolean.FALSE, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                c.setEmail(emails.isEmpty() ? "" : ((EmailVO) emails.get(0)).getEmail());
            }
            c.setKey(key);
            return c.toJSON(true).toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    private StringBuilder gerarCorpoEmailSenhaUsuarioMovel(
            final String key,
            final ClienteVO cliente, ColaboradorVO colaborador,
            final UsuarioMovelVO usuarioMovel,
            final String senha,
            final boolean novoAPP,
            final String urlAppEmail,
            final String nomeEmpresa) throws Exception {

        final String expiracao = Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), 7), "yyyyMMdd");
        final String hash = Uteis.encriptar(String.format("%s|%s|%s|%s", key, usuarioMovel.getNome(),
                senha, expiracao), CHAVE_URL_MOBILE);


        Uteis.desencriptar(String.format("%s|%s|%s|%s", key, usuarioMovel.getNome(),
                senha, expiracao), CHAVE_URL_MOBILE);
        String urlAtivacaoIOS;
        String urlApp = "";
        if (novoAPP) {
            urlAtivacaoIOS = "appdoaluno://?token=" + hash;
        } else {
            urlAtivacaoIOS = "pactotreino://?token=" + hash;
        }
        final String urlAtivacaoAndroid = "http://www.pactotreino.com.br/?token=" + hash;
        if (key.equals("ad979b3ace1b577b2a87ebe8781b4992")
                || key.equals("78cfbd0442cf8d6bc06a8ba70e838355")
                || key.equals("c9ee582d4d58d2537ea914133746e883")) {

            String prefixoQRCode = "parkshopping";

            if (key.equals("78cfbd0442cf8d6bc06a8ba70e838355")) {
                prefixoQRCode = "asasul";
            } else if (key.equals("c9ee582d4d58d2537ea914133746e883")) {
                prefixoQRCode = "sudoeste";
            }
            EmpresaVO empresa = null;
            if (cliente != null && cliente.getCodigo() > 0) {
                empresa = DaoAuxiliar.retornarAcessoControle(key).
                        getEmpresaDao().consultarPorChavePrimaria(
                                cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else if (colaborador != null && colaborador.getCodigo() > 0) {
                empresa = DaoAuxiliar.retornarAcessoControle(key).
                        getEmpresaDao().consultarPorChavePrimaria(
                                colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {
                empresa = DaoAuxiliar.retornarAcessoControle(key).
                        getEmpresaDao().consultarPorChavePrimaria(
                                usuarioMovel.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoTreinoUnique.txt").toURI());
            StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
            final String aux = texto.toString().
                    replaceAll("#URL_SITE", empresa.getSite()).
                    //                    replaceAll("#URL_LOGO", "http://app.pactosolucoes.com.br/oamd/imagens/" + key + ".jpg").
                            replaceAll("#CHAVE_EMPRESA", key).replaceAll("#EMAIL_USUARIO", usuarioMovel.getNome().toLowerCase()).
                            replaceAll("#SENHA", senha).replaceAll("#PREFIXO_EMPRESA", prefixoQRCode).
                            replaceAll("#HASH_IOS", urlAtivacaoIOS).
                            replaceAll("#HASH_ANDROID", urlAtivacaoAndroid);
            texto = null;
            return new StringBuilder(aux);
        } else {
            String[] imagens = new String[]{"baixar_androidP4CT0.png", "baixar_appleP4CT0.png", "entrar_androidP4CT0.png", "entrar_appleP4CT0.png", "topo_emailP4CT0.png", "setaP4CT0.png"};
            Map<String, File> mapaImagem = new HashMap<>();
            for (String imagem : imagens) {
                File arqImg = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + imagem).toURI());
                mapaImagem.put(imagem, arqImg);
            }
            UteisEmail.criarImagensEmailTreino(mapaImagem);

            boolean app_personalizado = false;
            String app_personalizado_nome = "", app_personalizado_url = "", app_url_email = "";

            RequestHttpService httpService = new RequestHttpService();
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
            RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(urlTreino + "/prest/config/" + key + "/manutencao", null, null, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
            if (json.has("return")) {
                String returnValue = json.getString("return");
                returnValue = returnValue.replaceAll("\\\\", "");
                JSONObject nestedObj = new JSONObject(returnValue);
                app_personalizado = nestedObj.getBoolean("aplicativo_personalizado");
                app_personalizado_nome = nestedObj.getString("aplicativo_personalizado_nome");
                app_personalizado_url = nestedObj.getString("aplicativo_personalizado_url");
                app_url_email = nestedObj.getString("app_url_email");
            }

            String urlAppPlay = "";
            String urlAppApple = "";
            String modeloArquivo = "";
            String nomeApp = "";
            if (app_personalizado) {
                nomeApp = app_personalizado_nome;
                urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto.zwacademia&hl=pt_BR&gl=US";
                urlAppApple = "https://apps.apple.com/br/app/minha-academia/id1388309741";
                modeloArquivo = "emailPactoTreinoAppPersonalizado.txt";
            } else if (!UteisValidacao.emptyString(app_url_email)) {
                switch (app_url_email) {

                    case "MINHA_ACADEMIA":
                        urlApp = "https://sistemspacto.app.link/minha-academia";
                        urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto.zwacademia&hl=pt_BR&gl=US";
                        urlAppApple = "https://apps.apple.com/br/app/minha-academia/id1388309741";
                        modeloArquivo = "emailPactoTreinoModeloMinhaAcademia.txt";
                        nomeApp = "Minha Academia";
                        break;

                    case "MEU_BOX":
                        urlApp = "https://sistempacto.app.link/meu-box";
                        urlAppPlay = "https://play.google.com/store/apps/details?id=com.pactosolucoes.meubox&hl=pt_BR&gl=US";
                        urlAppApple = "https://apps.apple.com/pt/app/meu-box/id1342274240";
                        modeloArquivo = "emailPactoTreinoDefaultNovoModeloMyBox.txt";
                        nomeApp = "Meu Box";
                        break;
                    default:
                        urlApp = "https://sistemapacto.app.link/apptreino";
                        urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto&hl=pt_BR&gl=US";
                        urlAppApple = "https://apps.apple.com/br/app/treino/id862662527";
                        modeloArquivo = "emailPactoTreinoDefaultNovoModelo.txt";
                        nomeApp = "App Treino";
                        break;
                }
            }


            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + modeloArquivo).toURI());
            StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
            String nome = (cliente != null && cliente.getCodigo() > 0) ? Uteis.getPrimeiroNome(cliente.getPessoa().getNome()) : Uteis.getPrimeiroNome(colaborador.getPessoa().getPrimeiroNomeConcatenado());

            EmpresaVO empresa = null;
            if (cliente != null && cliente.getCodigo() > 0) {
                Integer codigoEmpresa = cliente.getEmpresa().getCodigo() != null && cliente.getEmpresa().getCodigo() > 0 ? cliente.getEmpresa().getCodigo() : usuarioMovel.getEmpresa();
                empresa = DaoAuxiliar.retornarAcessoControle(key).
                        getEmpresaDao().consultarPorChavePrimaria(
                                codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else if (colaborador != null && colaborador.getCodigo() > 0) {
                empresa = DaoAuxiliar.retornarAcessoControle(key).
                        getEmpresaDao().consultarPorChavePrimaria(
                                colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {
                empresa = DaoAuxiliar.retornarAcessoControle(key).
                        getEmpresaDao().consultarPorChavePrimaria(
                                usuarioMovel.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            final String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, cliente.getEmpresa().getCodigo().toString());
            final String urlFotoEmpresa = Uteis.getPaintFotoDaNuvem(genKey);
            if (urlAppEmail.equals("TREINO") && usuarioMovel.getCliente() != null && UteisValidacao.emptyNumber(usuarioMovel.getCliente().getCodigo()) &&
                    usuarioMovel.getColaborador() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getColaborador().getCodigo())) {
                texto = new StringBuilder(texto.toString().replace("&#233; um aluno da #NOME_ACADEMIA e poder&#225; acessar seus treinos, agendar aulas e muito mais",
                        "est&#225; apto a prescrever treinos, agendar aulas e muito mais na #NOME_ACADEMIA"));
            }
            final String aux = texto.toString()
                    .replaceAll("#NOME_ACADEMIA", empresa.getNome())
                    .replaceAll("#ENDERECO_EMPRESA", empresa.getEndereco())
                    .replaceAll("#CIDADE_EMPRESA", (!UteisValidacao.emptyString(empresa.getCidade().getNome())) ? " - " + empresa.getCidade().getNome() : "")
                    .replaceAll("#ESTADO_EMPRESA", (!UteisValidacao.emptyString(empresa.getEstadoSigla())) ? " - " + empresa.getEstadoSigla() : "")
                    .replaceAll("#TELEFONE_EMPRESA", empresa.getPrimeiroTelefoneNaoNulo())
                    .replaceAll("#EMAIL_EMPRESA", empresa.getEmail())
                    .replaceAll("#NEW_USER", nome)
                    .replaceAll("#USER_NAME", usuarioMovel.getNome().toLowerCase())
                    .replaceAll("#SENHA", UteisValidacao.emptyString(senha) ? "A senha não foi alterada" : senha)
                    .replaceAll("#URL_APP", app_personalizado ? app_personalizado_url : urlApp)
                    .replaceAll("#URL_PLAY", urlAppPlay)
                    .replaceAll("#URL_MACA", urlAppApple)
                    .replaceAll("#APP_NOME", nomeApp)
                    .replaceAll("#URL_FOTO_EMPRESA", (urlFotoEmpresa == null) ?
                            "https://app.pactosolucoes.com.br/midias/email_app/novo_logo_pacto.png":urlFotoEmpresa);
            return new StringBuilder(aux);
        }

    }


    @WebMethod
    public String gerarUsuarioMovelAluno(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String username,
            @WebParam(name = "urlAppEmail") String urlAppEmail,
            @WebParam(name = "nomeEmpresa") String nomeEmpresa) {

        try {
            UsuarioMovelInterfaceFacade usuarioMovelDao = DaoAuxiliar.retornarAcessoControle(key).
                    getUsuarioMovelDao();
            ClienteVO cliente = DaoAuxiliar.retornarAcessoControle(key).
                    getClienteDao().consultarPorCodigo(codigoCliente, false,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            /**
             * Como determinar o Email (username): 1. Se o usãrio informar um
             * novo e-mail, o sistema deve verificar se este já existe
             * cadastrado e incluir se não existe; 2. Se o usuário não informar
             * um e-mail, o sistema deve procurar o primeiro cadastrado e
             * utilizã-lo como 'username'; 3. Se o usuário informar que não
             * usarã o app mobile o sistema deve usar a matrãcula como
             * 'username'
             */
            boolean matriculaComoUserName = username != null && username.equals(cliente.getCodigoMatricula().toString());
            Email emailDao = DaoAuxiliar.retornarAcessoControle(key).getEmailDao();
            List<EmailVO> listaEmails = emailDao.consultarEmails(cliente.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boolean jaExiste = false;
            for (EmailVO emailVO : listaEmails) {
                if (username != null && emailVO.getEmail().equalsIgnoreCase(username)) {
                    jaExiste = true;
                    break;
                }
            }
            if (username == null) {
                username = listaEmails.isEmpty() ? null : listaEmails.get(0).getEmail();
                jaExiste = username != null && !username.isEmpty();
            }
            if (username == null || username.isEmpty()) {
                throw new ConsistirException(String.format("Nenhum Email cadastrado/informado", new Object[]{username}));
            }
            username = username.toLowerCase();
            if (!matriculaComoUserName) {
                if (!UteisEmail.getValidEmail(username)) {
                    throw new ConsistirException(String.format("Email %s inválido", new Object[]{username}));
                }
            }

            UsuarioMovelVO usuarioMovel = usuarioMovelDao.consultarPorCliente(
                    cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //se o usuário movél já existir, o sistema não altera senha atravãs deste mãtodo
            //ã preciso usar os mãtodos de Alteração ("AlterarUsuarioMovel...")
            String senhaRandomica = senha;
            if (senha == null || senha.isEmpty()) {
                String uuid = UUID.randomUUID().toString();
                senhaRandomica = uuid.substring(0, uuid.indexOf("-"));
                if (senhaRandomica == null || senhaRandomica.isEmpty()) {
                    senhaRandomica = cliente.getCodigoMatricula().toString();
                }
            }
            boolean senhaAlterada = true;

            if (usuarioMovel.getCodigo() > 0) {
                usuarioMovel.setNome(username);
                usuarioMovel.setSenha(senha);
                usuarioMovel.setAtivo(true);
                if(UteisValidacao.emptyString(senha)) {
                    usuarioMovelDao.alterarSemSenha(usuarioMovel);
                    senhaAlterada = false;
                }else{
                    usuarioMovelDao.alterar(usuarioMovel);
                }
            } else {
                UsuarioMovelVO novoUsuario = new UsuarioMovelVO();
                novoUsuario.setAtivo(true);
                novoUsuario.setCliente(cliente);
                novoUsuario.setNome(username);
                novoUsuario.setEmpresa(cliente.getEmpresa().getCodigo());
                novoUsuario.setOrigem("TW");
                novoUsuario.setSenha(senhaRandomica);
                //Para caso gerado senha randomica a mesma seja atribuida ao corpo do e-mail enviado ao aluno.
                senha = senhaRandomica;
                usuarioMovelDao.incluir(novoUsuario);
                usuarioMovel = novoUsuario;
            }

            cliente.setAtualizarTreino(false);

            if (!matriculaComoUserName) {
                try {
                    AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                    if (nomeEmpresa.equals("false") || UteisValidacao.emptyString(nomeEmpresa)){
                        EmpresaVO emp = acessoControle.getEmpresaDao().consultarPorChavePrimaria(
                                cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        nomeEmpresa = emp.getNome();
                    }
                    DaoAuxiliar.retornarAcessoControle(key).enviarEmail(new String[]{username}, "Informações de acesso ao Pacto Treino",
                            gerarCorpoEmailSenhaUsuarioMovel(key, cliente, null, usuarioMovel, senha, true, urlAppEmail, nomeEmpresa));
                } catch (Exception ex) {
                    Uteis.logar("Encontrado problema ao enviar e-mail para " + username + " verifique as configurações de envio : " + ex.getMessage());
                }
            }
            //
            SituacaoClienteSinteticoDWVO swCliente = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().atualizarSintetico(
                    cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, true);
            usuarioMovel.getCliente().setSituacaoClienteSinteticoVO(swCliente);
            TreinoWSConsumer.sincronizarUsuario(key, usuarioMovel.toUsuarioTreino());

            return (!matriculaComoUserName && senhaAlterada)
                    ? "Email enviado com sucesso para " + username
                    : "Cadastro realizado com sucesso para matrícula " + username;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            if (ex.getMessage() != null
                    && ex.getMessage().contains("username_ukey")) {
                String detail = "";
                try {
                    detail = DaoAuxiliar.retornarAcessoControle(key).
                            getUsuarioMovelDao().obterUsuarioMesmoNome(username);
                } catch (Exception e) {
                }
                return "ERRO: Já existe um usuário com este email! " + detail;
            } else {
                return "ERRO: " + ex.getMessage();
            }
        }
    }

    @WebMethod
    public String alterarUsuarioMovelAlunoNovo(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String username,
            @WebParam(name = "nomeApp") String nomeApp,
            @WebParam(name = "urlAppEmail") String urlAppEmail) {
        return alterarUsuarioMovelAlunoGenerico(codigoCliente, key, senha, username, true, nomeApp, urlAppEmail);
    }

    @WebMethod
    public String alterarUsuarioMovelAluno(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String username,
            @WebParam(name = "nomeApp") String nomeApp,
            @WebParam(name = "urlAppEmail") String urlAppEmail) {
        return alterarUsuarioMovelAlunoGenerico(codigoCliente, key, senha, username, false, nomeApp, urlAppEmail);
    }

    private String alterarUsuarioMovelAlunoGenerico(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String username,
            @WebParam(name = "novoAPP") boolean novoAPP,
            @WebParam(name = "nomeApp") String nomeApp,
            @WebParam(name = "urlAppEmail") String urlAppEmail) {
        try {
            UsuarioMovelInterfaceFacade usuarioMovelDao = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao();
            UsuarioMovelVO usuarioMovel;
            boolean appTreino = nomeApp.equalsIgnoreCase("APP Treino - Colaborador") || nomeApp.equalsIgnoreCase("APP Treino - Cliente");

            if(nomeApp.equalsIgnoreCase("APP Treino - Colaborador")) {
                ColaboradorInterfaceFacade colaboradorDao = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao();
                usuarioMovel = usuarioMovelDao.consultarPorColaborador(colaboradorDao.consultarPorCodigo(codigoCliente,0, Uteis.NIVELMONTARDADOS_DADOSBASICOS), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else if (nomeApp.equalsIgnoreCase("APP Treino - Cliente")) {
                usuarioMovel = usuarioMovelDao.consultarPorCodigoCliente(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
                ClienteInterfaceFacade clienteDao = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
                usuarioMovel.setCliente(clienteDao.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS));
            } else {
                usuarioMovel = usuarioMovelDao.consultarPorUserName(username);
            }

            if (!UteisValidacao.emptyNumber(codigoCliente) && !appTreino &&
                    (usuarioMovel == null || UteisValidacao.emptyNumber(usuarioMovel.getCodigo()))) {
                ClienteInterfaceFacade clienteDAO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
                usuarioMovel = clienteDAO.gerarUsuarioMovelAluno(key, codigoCliente, "", username);
                clienteDAO = null;
            }

            if (isBlank(senha)) {
                String uuid = UUID.randomUUID().toString();
                senha = uuid.substring(0, uuid.indexOf("-"));
                if (isBlank(senha)) {
                    senha = codigoCliente.toString();
                }
            }

            if (usuarioMovel != null && usuarioMovel.getCodigo() > 0) {
                if(!appTreino) {
                    usuarioMovel.setNome(username);
                } else if(usuarioMovel.getColaborador() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getColaborador().getCodigo())) {
                    UsuarioInterfaceFacade usuarioDao = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao();
                    usuarioDao.alterarSenhaUsuario(usuarioDao.consultarPorColaborador(usuarioMovel.getColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS),true, Uteis.encriptar(senha));
                }

                usuarioMovel.setSenha(senha);
                usuarioMovelDao.alterar(usuarioMovel);
            } else {
                throw new ConsistirException("Usuário móvel não existe!");
            }
            SituacaoClienteSinteticoDWVO swCliente = null;
            List<EmailVO> emailVOs = new ArrayList<>();
            if (usuarioMovel.getCliente() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getCliente().getCodigo())) {
                swCliente = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().atualizarSintetico(usuarioMovel.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, true);
                emailVOs = DaoAuxiliar.retornarAcessoControle(key).getEmailDao().consultarEmails(usuarioMovel.getCliente().getPessoa().getCodigo(), Boolean.FALSE,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (usuarioMovel.getNome() != null && UteisValidacao.validaEmail(usuarioMovel.getNome())) {
                    EmailVO email = new EmailVO();
                    email.setEmail(usuarioMovel.getNome());
                    emailVOs.add(email);
                }
            }

            if (usuarioMovel.getColaborador() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getColaborador().getCodigo())) {
                if(usuarioMovel.getUsuarioEmailVO() != null && !Util.isEmptyString(usuarioMovel.getUsuarioEmailVO().getEmail())) {
                    EmailVO email = new EmailVO();
                    email.setEmail(usuarioMovel.getUsuarioEmailVO().getEmail());
                    emailVOs.add(email);
                } else {
                    emailVOs = DaoAuxiliar.retornarAcessoControle(key).getEmailDao().consultarEmails(usuarioMovel.getColaborador().getPessoa().getCodigo(), Boolean.FALSE,
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }

            if (emailVOs.isEmpty()) {
                throw new ConsistirException("Nenhum email foi encontrado para envio da senha.");
            }


            Set<String> emailsSet = new HashSet<>();
            if (UteisValidacao.validaEmail(username)) {
                emailsSet.add(username);
            }
            for (EmailVO email : emailVOs) {
                emailsSet.add(email.getEmail());
            }
            List<String> emails = new ArrayList<>(emailsSet);
            String[] emailArr = new String[emails.size()];
            DaoAuxiliar.retornarAcessoControle(key).enviarEmail(
                    emails.toArray(emailArr), "Informações de acesso ao Pacto Treino",
                    gerarCorpoEmailSenhaUsuarioMovel(key, usuarioMovel.getCliente(), usuarioMovel.getColaborador(), usuarioMovel, senha, novoAPP, urlAppEmail, nomeApp));
            if (usuarioMovel.getCliente() != null && UteisValidacao.notEmptyNumber(usuarioMovel.getCliente().getCodigo())) {
                usuarioMovel.getCliente().setSituacaoClienteSinteticoVO(swCliente);
            }

            TreinoWSConsumer.sincronizarUsuario(key, usuarioMovel.toUsuarioTreino());
            return "Email enviado!";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + (ex.getMessage() != null
                    && ex.getMessage().contains("username_ukey")
                    ? "Já existe um usuário com este email!"
                    : ex.getMessage());
        }
    }

    @WebMethod
    public String enviarEmailColaborador(
            @WebParam(name = "codigoColaborador") Integer codigoColaborador,
            @WebParam(name = "key") String key,
            @WebParam(name = "texto") String texto) {
        try {
            ColaboradorVO colaborador = DaoAuxiliar.retornarAcessoControle(key).
                    getColaboradorDao().consultarPorChavePrimaria(codigoColaborador,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Email emailDao = DaoAuxiliar.retornarAcessoControle(key).getEmailDao();
            List<EmailVO> listaEmails = emailDao.consultarEmails(colaborador.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (listaEmails != null && !listaEmails.isEmpty()) {
                StringBuilder stringB = new StringBuilder(texto);
                String[] emails = new String[listaEmails.size()];
                for (int i = 0; i < listaEmails.size(); i++) {
                    emails[i] = listaEmails.get(i).getEmail();
                }
                DaoAuxiliar.retornarAcessoControle(key).enviarEmail(
                        emails, "Extrato de créditos de personal", stringB);
            }
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String alterarUsuarioMovelProfessor(
            @WebParam(name = "codigoColaborador") Integer codigoColaborador,
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String username,
            @WebParam(name = "nomeApp") String nomeApp,
            @WebParam(name = "urlAppEmail") String urlAppEmail) {
        try {
            UsuarioMovelInterfaceFacade usuarioMovelDao = DaoAuxiliar.retornarAcessoControle(key).
                    getUsuarioMovelDao();
            ColaboradorVO colaborador = DaoAuxiliar.retornarAcessoControle(key).
                    getColaboradorDao().consultarPorChavePrimaria(codigoColaborador,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            colaborador.setPessoa(DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().
                    consultarPorChavePrimaria(colaborador.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            colaborador.setListaTipoColaboradorVOs(DaoAuxiliar.retornarAcessoControle(key).getTipoColaboradorDao().consultarTipoColaborador(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            UsuarioMovelVO usuarioMovel = usuarioMovelDao.consultarPorColaborador(
                    colaborador, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);


            if (usuarioMovel.getCodigo() > 0) {
                List<EmailVO> listaEmails = DaoAuxiliar.retornarAcessoControle(key).
                        getEmailDao().consultarEmails(colaborador.getPessoa().getCodigo(),
                        false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (senha == null || senha.isEmpty()) {
                    if (listaEmails != null && !listaEmails.isEmpty()) {
                        String uuid = UUID.randomUUID().toString();
                        senha = uuid.substring(0, uuid.indexOf("-"));
                        if (senha == null || senha.isEmpty()) {
                            senha = colaborador.getCodigo().toString();
                        }
                    } else {
                        throw new ConsistirException("Usuário não possui nenhum e-mail cadastrado!");
                    }
                }
                if(UteisValidacao.emptyNumber(usuarioMovel.getUsuarioEmailVO().getCodigo())){
                    usuarioMovel.setNome(username);
                }
                usuarioMovel.setSenha(senha);
                usuarioMovelDao.alterar(usuarioMovel);
                usuarioMovel.setColaborador(colaborador);
                TreinoWSConsumer.sincronizarUsuario(key, usuarioMovel.toUsuarioTreino());
                atualizaUsernameSenhaUsuario(key, senha,UteisValidacao.emptyNumber(usuarioMovel.getUsuarioEmailVO().getCodigo()) ?  username : null, colaborador.getCodigo());

                if (listaEmails != null && !listaEmails.isEmpty()) {
                    String[] emails = new String[listaEmails.size()];
                    for (int i = 0; i < listaEmails.size(); i++) {
                        emails[i] = listaEmails.get(i).getEmail();
                    }
                    DaoAuxiliar.retornarAcessoControle(key).enviarEmail(
                            emails,
                            "Informações de acesso ao Pacto Treino",
                            gerarCorpoEmailSenhaUsuarioMovel(key, null, colaborador, usuarioMovel, senha, false, urlAppEmail, nomeApp));
                    return "Dados alterados com sucesso! Email enviado para: " + listaEmails;
                }
            } else {
                throw new ConsistirException("Usuário movél não existe!");
            }
            return "Dados alterados com sucesso!";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + (ex.getMessage() != null
                    && ex.getMessage().contains("username_ukey")
                    ? "Já existe um á com este email!"
                    : ex.getMessage());
        }
    }

    private void atualizaUsernameSenhaUsuario(String key, String senha, String username, int codigoColaborador) throws Exception {
        UsuarioInterfaceFacade usuario = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao();
        UsuarioVO usuarioVO = usuario.consultarPorCodigoColaborador(codigoColaborador, Uteis.NIVELMONTARDADOS_MINIMOS);
        usuarioVO.setSenha(senha);
        if(!UteisValidacao.emptyString(username)) {
            usuarioVO.setUsername(username);
        }
        usuario.alterarUsernameSenha(usuarioVO);
    }

    @WebMethod(operationName = "gerarVinculoProfessorAluno")
    public String gerarVinculoProfessorAluno(
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "codigoProfessor") Integer codigoProfessor,
            @WebParam(name = "usuarioResponsavel") Integer usuarioResponsavel,
            @WebParam(name = "key") String key) {
        try {
            VinculoVO vinculo = new VinculoVO();
            vinculo.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            vinculo.setCliente(new ClienteVO());
            vinculo.getCliente().setCodigo(codigoCliente);
            vinculo.setColaborador(new ColaboradorVO());
            vinculo.getColaborador().setCodigo(codigoProfessor);
            List<VinculoVO> vinculos = DaoAuxiliar.retornarAcessoControle(key).
                    getVinculoDao().consultarPorCodigoCliente(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS, true);
            boolean alterado = false;
            if (vinculos != null && !vinculos.isEmpty()) {
                for (VinculoVO vinc : vinculos) {
                    if (vinc.getTipoVinculo().equals("TW")) {
                        if (vinc.getColaborador().getCodigo().equals(codigoProfessor)) {
                            return "Operação realizada com sucesso! Aluno já possui vinculo com esse(a) professor(a)!";
                        }
                        DaoAuxiliar.retornarAcessoControle(key).
                                getVinculoDao().excluir(vinc, "TREINO", false);
                        alterado = true;
                    }
                }
            }
            if (usuarioResponsavel == null || usuarioResponsavel == 0) {
                DaoAuxiliar.retornarAcessoControle(key).
                        getVinculoDao().incluir(vinculo, "TREINO", null);
            } else if (codigoProfessor == null) {
                return "Operação realizada com sucesso! Vínculo do aluno removido!";
            } else {
                UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                DaoAuxiliar.retornarAcessoControle(key).
                        getVinculoDao().incluir(vinculo, Calendario.hoje(), "TREINO", true, usuarioVO, null);
            }
            if (alterado) {
                return "Aluno já possuía um(a) professor(a). Vínculo foi alterado com sucesso!";
            }
            return "Novo Vinculo incluído com sucesso!";
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "validarUsuario")
    public UsuarioTO validarUsuario(
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String userName) {
        try {
            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getControleAcessoDao().verificarLoginUsuario(userName, senha, true);
            if (usuarioVO == null
                    || usuarioVO.getCodigo() == null
                    || usuarioVO.getCodigo() == 0) {
                return null;
            } else {
                UsuarioTO usuarioto = new UsuarioTO();
                usuarioto.setNome(usuarioVO.getNome());
                usuarioto.setUserName(usuarioVO.getUsername());
                usuarioto.setCodigo(usuarioVO.getCodigo());
                usuarioto.setUsuarioTreino(DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().usuarioTreino(usuarioVO.getCodigo()));
                if (usuarioto.isUsuarioTreino()) {
                    UsuarioMovelVO usuarioMovelVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (usuarioMovelVO != null) {
                        usuarioto.setUsuarioMovelTreino(usuarioMovelVO.getNome());
                        usuarioto.setCredecialTreino(usuarioMovelVO.getSenha());
                    }
                }
                usuarioto.setUrlTreino(PropsService.getPropertyValue(key, PropsService.urlTreinoWeb));
                usuarioto.setAdministrador(usuarioVO.getAdministrador());
                usuarioto.setPermissaoAlterarRPS(usuarioVO.isPermissaoAlterarRPS());

                if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                    usuarioto.setUrlFotosNuvem(PropsService.getPropertyValue(PropsService.urlFotosNuvem));
                    usuarioto.setUrlFoto(usuarioVO.getColaboradorVO().getPessoa().getUrlFoto());
                    usuarioto.setTypeMidiasService(PropsService.getPropertyValue(PropsService.typeMidiasService));

                }
                List<UsuarioPerfilAcessoVO> listaPerfisUsuario = DaoAuxiliar.retornarAcessoControle(key).
                        getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(),
                        Uteis.NIVELMONTARDADOS_MINIMOS);
                if(listaPerfisUsuario.size() == 1){
                    if (listaPerfisUsuario.get(0).getEmpresa().getEstado().getCodigo() == 0) {
                        listaPerfisUsuario.get(0).getEmpresa().setEstado(null);
                    }
                    if (listaPerfisUsuario.get(0).getEmpresa().getPais().getCodigo() == 0) {
                        listaPerfisUsuario.get(0).getEmpresa().setPais(null);
                    }
                    usuarioto.setEmpresaDefault(new EmpresaWS(listaPerfisUsuario.get(0).getEmpresa()));
                    try {
                        usuarioto.setTipoPerfilAcesso(listaPerfisUsuario.get(0).getPerfilAcesso().getTipo().getNome());
                    } catch (Exception e) {
                        Uteis.logar(this.getClass().getName(), e.getMessage());
                    }
                } else {
                    usuarioto.setEmpresaDefault(new EmpresaWS(usuarioVO.getColaboradorVO().getEmpresa()));
                    for (UsuarioPerfilAcessoVO upf: listaPerfisUsuario) {
                        if (upf.getEmpresa().getCodigo().equals(usuarioto.getEmpresaDefault().getCodigo())) {
                            try {
                                usuarioto.setTipoPerfilAcesso(upf.getPerfilAcesso().getTipo().getNome());
                            } catch (Exception e) {
                                Uteis.logar(this.getClass().getName(), e.getMessage());
                            }
                        }
                    }
                }
                for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                    AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                    EmpresaVO emp = acessoControle.getEmpresaDao().consultarPorChavePrimaria(
                            perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    try {
                        if(!userName.toLowerCase().equals("pactobr") && !userName.toLowerCase().equals("admin") && !emp.isAtiva()){
                            continue;
                        }
                        if (emp.getEstado().getCodigo() > 0) {
                            emp.setEstado(acessoControle.getEstadoDao().consultarPorChavePrimaria(emp.getEstado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            emp.setPais(acessoControle.getPaisDao().consultarPorChavePrimaria(emp.getPais().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            perfil.setEmpresa(emp);
                        }
                    } catch (Exception e) {
                        Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE,
                                String.format("não foi possível consultar dados de PAÍS/ESTADO da empresa %s devido ao erro...", emp.getNome()), e);
                    }
                    usuarioto.getEmpresas().add(new EmpresaWS(emp.getCodigo(), emp.getNome(), emp.getTokenSMS(),
                            emp.getEstado().getDescricao(), emp.getEstado().getSigla(), emp.getPais().getNome(), emp.getUsarNFSe(), emp.isUsarNFCe(), emp.getChaveNFSe(), emp.isUsaEnotas(), emp.getTotalDiasExtras()));

                    EmpresaWS empresaAcessoModuloNota = retornarPerfilSePossuirPermissao("AcessoModuloNotasUsuario", perfil);

                    if (empresaAcessoModuloNota != null) {
                        usuarioto.getEmpresasPermissaoModuloNotas().add(empresaAcessoModuloNota);
                    }
                }

                if (usuarioVO.getColaboradorVO() != null
                        && !UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getCodigo())) {
                    usuarioto.setCodigoPessoa(
                            DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().obterCodigoPessoaUsuario(usuarioVO.getColaboradorVO().getCodigo()));
                }

                usuarioto.setValidarUserOamd(PropsService.isTrue(PropsService.validarUsuarioOAMD));
                return usuarioto;
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            UsuarioTO usu = new UsuarioTO();
            usu.setMensagem(ex.getMessage());
            return usu;
        }
    }

    private EmpresaWS retornarPerfilSePossuirPermissao(String entidadePermissao, UsuarioPerfilAcessoVO usuarioPerfilAcessoVO) {
        for (PermissaoVO permissaoVO : usuarioPerfilAcessoVO.getPerfilAcesso().getPermissaoVOs()) {
            if (permissaoVO.getNomeEntidade().equals(entidadePermissao)) {
                return new EmpresaWS(
                        usuarioPerfilAcessoVO.getEmpresa().getCodigo(),
                        usuarioPerfilAcessoVO.getEmpresa().getNome()
                );
            }
        }

        return null;
    }

    @WebMethod(operationName = "validarUsuarioEmail")
    public String validarUsuarioEmail(
            @WebParam(name = "key") String key,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String userName) {
        JSONObject obj = new JSONObject();
        try {
            UsuarioVO usuarioVO;
            if (senha != null && senha.equalsIgnoreCase("")){
                UsuarioEmailVO usuarioEmailVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioEmailDao().consultarPorEmail(userName);
                if (usuarioEmailVO == null ){
                    throw new Exception("Usuário não encontrado");
                }
                usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(usuarioEmailVO.getUsuario(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getControleAcessoDao().verificarLoginUsuarioApp(userName, senha.toUpperCase(), true);
            }

            if (usuarioVO == null
                    || usuarioVO.getCodigo() == null
                    || usuarioVO.getCodigo() == 0) {
                return null;
            } else {
                UsuarioTO usuarioto = new UsuarioTO();
                usuarioto.setNome(usuarioVO.getNome());
                usuarioto.setUserName(usuarioVO.getUsername());
                usuarioto.setCodigo(usuarioVO.getCodigo());
                usuarioto.setUsuarioTreino(DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().usuarioTreino(usuarioVO.getCodigo()));
                usuarioto.setUrlTreino(PropsService.getPropertyValue(key, PropsService.urlTreinoWeb));
                if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                    usuarioto.setUrlFotosNuvem(PropsService.getPropertyValue(PropsService.urlFotosNuvem));
                    usuarioto.setUrlFoto(usuarioVO.getColaboradorVO().getPessoa().getUrlFoto());
                    usuarioto.setTypeMidiasService(PropsService.getPropertyValue(PropsService.typeMidiasService));

                }
                List<UsuarioPerfilAcessoVO> listaPerfisUsuario = DaoAuxiliar.retornarAcessoControle(key).
                        getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(),
                        Uteis.NIVELMONTARDADOS_LOGIN);
                if(listaPerfisUsuario.size() == 1){
                    usuarioto.setEmpresaDefault(new EmpresaWS(listaPerfisUsuario.get(0).getEmpresa()));
                } else {
                    usuarioVO.getColaboradorVO().setEmpresa(DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(usuarioVO.getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    usuarioto.setEmpresaDefault(new EmpresaWS(usuarioVO.getColaboradorVO().getEmpresa()));
                }

                for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                    EmpresaVO emp = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(
                            perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if(emp.isAtiva()) {
                        EmpresaWS empresaWS = new EmpresaWS(emp.getCodigo(), emp.getNome(), emp.getTokenSMS(), null, null, emp.getCNPJ(), emp.getCodEmpresaFinanceiro(), emp.getTokenSMSShortCode());
                        empresaWS.setDescricaoPerfil(perfil.getPerfilAcesso().getNome());
                        empresaWS.setEmail(perfil.getEmpresa().getEmail());
                        empresaWS.setCodigoFinanceiro(emp.getCodEmpresaFinanceiro());
                        usuarioto.getEmpresas().add(empresaWS);
                    }
                }

                if (usuarioVO.getColaboradorVO() != null && !UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getCodigo())) {
                    usuarioto.setCodigoPessoa(DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().obterCodigoPessoaUsuario(usuarioVO.getColaboradorVO().getCodigo()));
                }

                usuarioto.setValidarUserOamd(PropsService.isTrue(PropsService.validarUsuarioOAMD));

                obj.put(RETURN,new JSONObject(usuarioto));
                return obj.toString();
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
    }

    @WebMethod(operationName = "validarUsuarioTelefone")
    public String validarUsuarioTelefone(
            @WebParam(name = "key") String key,
            @WebParam(name = "codUsuario") Integer codUsuario) {
        JSONObject obj = new JSONObject();
        try {
            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (usuarioVO == null
                    || usuarioVO.getCodigo() == null
                    || usuarioVO.getCodigo() == 0) {
                return null;
            } else {
                UsuarioTO usuarioto = new UsuarioTO();
                usuarioto.setNome(usuarioVO.getNome());
                usuarioto.setUserName(usuarioVO.getUsername());
                usuarioto.setCodigo(usuarioVO.getCodigo());
                usuarioto.setUsuarioTreino(DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().usuarioTreino(usuarioVO.getCodigo()));
                usuarioto.setUrlTreino(PropsService.getPropertyValue(key, PropsService.urlTreinoWeb));
                if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                    usuarioto.setUrlFotosNuvem(PropsService.getPropertyValue(PropsService.urlFotosNuvem));
                    usuarioto.setUrlFoto(usuarioVO.getColaboradorVO().getPessoa().getUrlFoto());
                    usuarioto.setTypeMidiasService(PropsService.getPropertyValue(PropsService.typeMidiasService));

                }
                List<UsuarioPerfilAcessoVO> listaPerfisUsuario = DaoAuxiliar.retornarAcessoControle(key).
                        getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(),
                        Uteis.NIVELMONTARDADOS_LOGIN);
                if(listaPerfisUsuario.size() == 1){
                    usuarioto.setEmpresaDefault(new EmpresaWS(listaPerfisUsuario.get(0).getEmpresa()));
                } else {
                    usuarioto.setEmpresaDefault(new EmpresaWS(usuarioVO.getColaboradorVO().getEmpresa()));
                }

                for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                    EmpresaVO emp = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(
                            perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if(emp.isAtiva()) {
                        EmpresaWS empresaWS = new EmpresaWS(emp.getCodigo(), emp.getNome(), emp.getTokenSMS(), null, null, emp.getCNPJ(), emp.getCodEmpresaFinanceiro(), emp.getTokenSMSShortCode());
                        empresaWS.setDescricaoPerfil(perfil.getPerfilAcesso().getNome());
                        empresaWS.setEmail(perfil.getEmpresa().getEmail());
                        usuarioto.getEmpresas().add(empresaWS);
                    }
                }

                if (usuarioVO.getColaboradorVO() != null
                        && !UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getCodigo())) {
                    usuarioto.setCodigoPessoa(
                            DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().obterCodigoPessoaUsuario(usuarioVO.getColaboradorVO().getCodigo()));
                }

                usuarioto.setValidarUserOamd(PropsService.isTrue(PropsService.validarUsuarioOAMD));

                obj.put(RETURN,new JSONObject(usuarioto));
                return obj.toString();
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
    }

    @WebMethod
    public Integer consultarNrDiasParcelaEmAberto(
            @WebParam(name = "codigoPessoa") Integer codigoPessoa,
            @WebParam(name = "key") String key) {
        try {
            MovParcelaVO parcela = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao().consultarParcelaEmAbertoMaisAntiga(codigoPessoa);
            if (parcela == null) {
                return 0;
            }
            return Long.valueOf(Uteis.nrDiasEntreDatas(parcela.getDataVencimento(), Calendario.hoje())).intValue();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return 0;
        }
    }

    @WebMethod
    public String atualizarCadastroPersonal(
            @WebParam(name = "codigoColaborador") Integer codigoColaborador,
            @WebParam(name = "atualizarSaldo") Boolean atualizarSaldo,
            @WebParam(name = "saldo") Integer saldo,
            @WebParam(name = "emAtendimento") Boolean emAtendimento,
            @WebParam(name = "key") String key) {
        try {
            if (atualizarSaldo) {
                DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().atualizarSaldoPersonal(codigoColaborador, saldo);
            } else {
                DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().atualizarEmAtendimento(codigoColaborador, emAtendimento);
            }
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String gerarVendaCreditos(
            @WebParam(name = "codigoColaborador") Integer codigoColaborador,
            @WebParam(name = "codigoUsuarioZW") Integer codigoUsuarioZW,
            @WebParam(name = "numeroCreditos") Integer numeroCreditos,
            @WebParam(name = "key") String key) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getVendaAvulsaDao().gerarVendaCreditos(codigoColaborador, numeroCreditos, codigoUsuarioZW);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public Integer obterDuracaoCreditosEmpresa(
            @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
            @WebParam(name = "key") String key) {
        try {
            EmpresaVO empresa = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            return empresa.getConfigsPersonal().getDuracaoCredito();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return 0;
        }
    }

    @WebMethod
    public String persistirClienteSite(
            @WebParam(name = "nome") final String nome,
            @WebParam(name = "cpf") final String cpf,
            @WebParam(name = "email") final String email,
            @WebParam(name = "sexo") final String sexo,
            @WebParam(name = "dataNascimento") final String dataNascimento,
            @WebParam(name = "endereco") final String endereco,
            @WebParam(name = "complemento") final String complemento,
            @WebParam(name = "numero") final String numero,
            @WebParam(name = "bairro") final String bairro,
            @WebParam(name = "cep") final String cep,
            @WebParam(name = "telCelular") final String telCelular,
            @WebParam(name = "telResidencial") final String telResidencial,
            @WebParam(name = "senha") final String senha,
            @WebParam(name = "empresa") int empresa,
            @WebParam(name = "codigoCidade") Integer codigoCidade,
            @WebParam(name = "codigoEstado") Integer codigoEstado,
            @WebParam(name = "idEmpresaFinanceiroRede") Integer idEmpresaFinanceiroRede,
            @WebParam(name = "consultor") Integer consultor,
            @WebParam(name = "key") final String key,
            @WebParam(name = "observacao") final String observacao) {

        try {
            String mensagem = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().persistirClienteSite(
                    nome, cpf, email, sexo, dataNascimento, endereco, complemento, numero, bairro, cep, telCelular,
                    telResidencial, senha, empresa,codigoCidade, codigoEstado, idEmpresaFinanceiroRede,null, consultor, observacao);
            if (mensagem.startsWith("ERRO")) {
                throw new Exception(mensagem);
            }
            return mensagem;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @WebMethod
    public String alterarDadosPessoaisClienteSite(
            @WebParam(name = "codigoCliente") int codigoCliente,
            @WebParam(name = "endereco") final String endereco,
            @WebParam(name = "complemento") final String complemento,
            @WebParam(name = "numero") final String numero,
            @WebParam(name = "bairro") final String bairro,
            @WebParam(name = "cep") final String cep,
            @WebParam(name = "telCelular") final String telCelular,
            @WebParam(name = "telResidencial") final String telResidencial,
            @WebParam(name = "codigoCidade") Integer codigoCidade,
            @WebParam(name = "codigoEstado") Integer codigoEstado,
            @WebParam(name = "key") final String key) {

        try {
            String mensagem = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().alterarDadosPessoaisClienteSite(codigoCliente,
                     endereco, complemento, numero, bairro, cep, codigoCidade,codigoEstado, telCelular, telResidencial);
            if (mensagem.startsWith("ERRO")) {
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, mensagem);
            }
            return mensagem;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }


    @WebMethod
    public String alterarSenhaUsuarioMovel(@WebParam(name = "key") final String key,
                                           @WebParam(name = "email") final String email,
                                           @WebParam(name = "senha") final String senha,
                                           @WebParam(name = "lembreteSenha") final String lembreteSenha) {
        try {
            String mensagem = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().alterarSenhaUsuarioMovel(email, senha, lembreteSenha);
            if (mensagem.startsWith("ERRO")) {
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, mensagem);
            }
            return mensagem;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarEstado(@WebParam(name = "key") final String key) throws Exception {
        try {
            String msgRetorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().consultarEstado();
            if (msgRetorno.startsWith("ERRO")) {
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, msgRetorno);
            }
            return msgRetorno;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarCidade(@WebParam(name = "key") final String key,
                                  @WebParam(name = "codigoEstado") final int codigoEstado) throws Exception {
        try {
            String msgRetorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().consultarCidade(codigoEstado);
            if (msgRetorno.startsWith("ERRO")) {
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, msgRetorno);
            }
            return msgRetorno;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarCupomDesconto")
    public String consultarCupomDesconto(@WebParam(name = "key") final String key,
                                        @WebParam(name = "listaCupom") String listaCupom) {
        try{
            //informar os valores para o parãmetro "listaCupom" separados por vãrgula.
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().consultarCupomDesconto(key, listaCupom);

        }catch (Exception e){
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, e);
        }
        return  "[{}]";
    }

    @WebMethod(operationName = "consultarClienteNaRedeEmpresa")
    public String consultarClienteNaRedeEmpresa(@WebParam(name = "key") final String key,
                                                @WebParam(name = "cpf") String cpf) {
        try{
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().consultarClienteNaRedeEmpresa(key,cpf);
        }catch (Exception e){
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, e);
        }
        return  "{}";
    }

    @WebMethod(operationName = "consultarCliente")
    public String consultarCliente(@WebParam(name = "key") final String key,
                                   @WebParam(name = "codigoEmpresaZW") final Integer codigoEmpresaZW,
                                   @WebParam(name = "email") final String email,
                                   @WebParam(name = "cpf") final String cpf) {
        ConsultaClienteWS consultaClienteWS = new ConsultaClienteWS();
        try {
            String cpfConsultar = cpf;
            if ((cpfConsultar != null) && (cpfConsultar.length() == 11)) {
                cpfConsultar = cpfConsultar.substring(0, 3) + "." + cpfConsultar.substring(3, 6) + "." + cpfConsultar.substring(6, 9) + "-" + cpfConsultar.substring(9);
            }
            String chave = key;
            if ((key == null) || (key.trim().equals(""))) {
                consultaClienteWS.setMsgErro("O parãmetro key ã obrigatãrio");
                return (new JSONObject(consultaClienteWS)).toString();
            }
            if (((cpf == null) || (cpf.trim().equals(""))) && ((email == null) || (email.trim().equals("")))) {
                consultaClienteWS.setMsgErro("ã necessãrio informar ao menos um dos parãmetros(cpf ou email)");
                return (new JSONObject(consultaClienteWS)).toString();
            }
            consultaClienteWS = DaoAuxiliar.retornarAcessoControle(chave).getClienteDao().consultarCliente(codigoEmpresaZW, email.toLowerCase(), cpfConsultar, null);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            consultaClienteWS.setMsgErro("ERRO AO CONSULTAR OS DADOS DO CLIENTE. ERRO:" + ex.getMessage());
        }
        JSONObject jsonObject = new JSONObject(consultaClienteWS);
        return jsonObject.toString();
    }

    @WebMethod(operationName = "consultarClienteComTelefone")
    public String consultarClienteComTelefone(@WebParam(name = "key") final String key,
                                              @WebParam(name = "codigoEmpresaZW") final Integer codigoEmpresaZW,
                                              @WebParam(name = "email") final String email,
                                              @WebParam(name = "cpf") final String cpf,
                                              @WebParam(name = "telefone") final String telefone) {
        ConsultaClienteWS consultaClienteWS = new ConsultaClienteWS();
        try {
            String cpfConsultar = cpf;
            if ((cpfConsultar != null) && (cpfConsultar.length() == 11)) {
                cpfConsultar = cpfConsultar.substring(0, 3) + "." + cpfConsultar.substring(3, 6) + "." + cpfConsultar.substring(6, 9) + "-" + cpfConsultar.substring(9);
            }

            String telefoneConsultar = telefone;
            if ((telefoneConsultar != null) && ((telefoneConsultar.length() == 10) || (telefoneConsultar.length() == 11))) {
                telefoneConsultar = "(" + telefoneConsultar.substring(0, 2) + ")" + telefoneConsultar.substring(2);
            }

            if ((key == null) || (key.trim().equals(""))) {
                consultaClienteWS.setMsgErro("O parãmetro key ã obrigatãrio");
                return (new JSONObject(consultaClienteWS)).toString();
            }

            int qtdNaoInformado = 0;
            if ((cpf == null) || (cpf.trim().equals(""))) {
                qtdNaoInformado++;
            }

            if ((email == null) || (email.trim().equals(""))) {
                qtdNaoInformado++;
            }

            if ((telefone == null) || (telefone.trim().equals(""))) {
                qtdNaoInformado++;
            }

            if (qtdNaoInformado > 1) {
                consultaClienteWS.setMsgErro("ã necessãrio informar ao menos dois dos parãmetros(cpf, email ou telefone)");
                return (new JSONObject(consultaClienteWS)).toString();
            }

            if ((codigoEmpresaZW == null) || (codigoEmpresaZW <= 0)) {
                consultaClienteWS.setMsgErro("O parãmetro codigoEmpresaZW ã obrigatãrio");
                return (new JSONObject(consultaClienteWS)).toString();
            }
            consultaClienteWS = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarClienteComTelefone(codigoEmpresaZW, email, cpfConsultar, null, telefoneConsultar);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            consultaClienteWS.setMsgErro("ERRO AO CONSULTAR OS DADOS DO CLIENTE. ERRO:" + ex.getMessage());
        }
        JSONObject jsonObject = new JSONObject(consultaClienteWS);
        return jsonObject.toString();
    }


    @WebMethod(operationName = "consultarFrequenciaAluno")
    public String consultarFrequenciaAluno(@WebParam(name = "key") final String key,
                                                @WebParam(name = "codigoCliente") int codigoCliente) {
        try{
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().consultarFrequenciaAluno(codigoCliente);
        }catch (Exception e){
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, e);
        }
        return  "{}";
    }


    @WebMethod
    public ClienteWS logarUsuarioMovel(@WebParam(name = "key") final String key,
                                       @WebParam(name = "email") final String email,
                                       @WebParam(name = "senha") final String senha,
                                       @WebParam(name = "chaveRede") final String chaveRede) throws ConsistirException {
        UsuarioMovelVO usuario = null;
        LoginSiteRedeEmpresaVO loginSiteRedeEmpresaVO = null;
        ClienteWS clienteWS;
        try {
            try {

                String emailOUCPFConsultar = email;

                boolean buscarPorCPF = false;
                if (!UteisValidacao.emptyString(emailOUCPFConsultar) && !emailOUCPFConsultar.contains("@")) {
                    buscarPorCPF = true;
                    String cpfConsultar = emailOUCPFConsultar;
                    if (cpfConsultar.length() == 11) {
                        cpfConsultar = cpfConsultar.substring(0, 3) + "." + cpfConsultar.substring(3, 6) + "." + cpfConsultar.substring(6, 9) + "-" + cpfConsultar.substring(9);
                    }
                    emailOUCPFConsultar = cpfConsultar;
                }

                String chave = key;
                if ((chaveRede != null) && (!chaveRede.equals(""))) {
                    OAMDService oamdService = new OAMDService();
                    try {
                        loginSiteRedeEmpresaVO = oamdService.consultarChaveDeEmailDaRede(chaveRede, emailOUCPFConsultar, buscarPorCPF);
                        chave = (loginSiteRedeEmpresaVO != null) ? loginSiteRedeEmpresaVO.getChaveZW() : null;
                        if (chave == null)
                            throw new ConsistirException("não foi encontrado nenhum usuário para a rede de empresa informada.");
                    } finally {
                        oamdService = null;
                    }
                }

                if (buscarPorCPF) {
                    usuario = DaoAuxiliar.retornarAcessoControle(chave).getUsuarioMovelDao().consultarPorCPFSenha(emailOUCPFConsultar, Uteis.encriptar(senha));
                } else {
                    usuario = DaoAuxiliar.retornarAcessoControle(chave).getUsuarioMovelDao().consultarPorUsuarioSenha(emailOUCPFConsultar, Uteis.encriptar(senha));
                }
            } catch (Exception ex) {
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            }

            if (usuario != null && usuario.getCodigo() > 0) {
                try {
                    usuario.getCliente().setEmpresa(DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(usuario.getCliente().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                } catch (Exception ex) {
                    throw new ConsistirException("não foi possível encontrar empresa do cliente");
                }
                clienteWS = usuario.getCliente().toWS();
                if (loginSiteRedeEmpresaVO != null) {
                    clienteWS.setIdEmpresaFinanceiroRede(loginSiteRedeEmpresaVO.getEmpresafinanceiro_codigo());
                    clienteWS.setChaveZW(loginSiteRedeEmpresaVO.getChaveZW());
                }
                return clienteWS;
            } else {
                throw new ConsistirException("Login/Senha Invãlido.");
            }
        } catch (ConsistirException ex) {
            clienteWS = new ClienteWS();
            clienteWS.setNome(ex.getMessage());
        }
        return clienteWS;
    }

    @WebMethod
    public List<ClienteMensagemWS> consultarMensagensCliente(@WebParam(name = "key") final String key,
                                                             @WebParam(name = "cliente") int cliente) throws ConsistirException {

        try {
            List<ClienteMensagemVO> mensagens = DaoAuxiliar.retornarAcessoControle(key).getClienteMsgDao().
                    consultarPorCliente(cliente, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            List<ClienteMensagemWS> mensagensCliente = new ArrayList<ClienteMensagemWS>();
            for (ClienteMensagemVO clienteMensagemVO : mensagens) {
                mensagensCliente.add(clienteMensagemVO.toWS());
            }

            return mensagensCliente;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ConsistirException(e.getMessage());
        }
    }

    @WebMethod
    public String consultarColaboradoresTreino(
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "nome") String nome,
            @WebParam(name = "incluirPersonais") Boolean incluirPersonais,
            @WebParam(name = "todosProfessores") Boolean todosProfessores) throws Exception {
        if (UteisValidacao.emptyString(nome)) {
            throw new Exception("Informe o nome para a consulta");
        }
        StringBuilder sb = new StringBuilder(" SELECT co.codigo as codigocolaborador,");
        sb.append("\n EXISTS(SELECT codigo FROM tipocolaborador t WHERE t.colaborador = co.codigo AND t.descricao = 'TW') AS professor,");
        sb.append("\n EXISTS(SELECT codigo FROM tipocolaborador t WHERE t.colaborador = co.codigo AND t.descricao IN ('PT','PI','PE')) AS personal,");
        sb.append("\n pe.nome, pe.codigo as codigopessoa FROM colaborador co ");
        sb.append("\n INNER JOIN pessoa pe ON co.pessoa = pe.codigo");
        if (!todosProfessores) {
            sb.append("\n INNER JOIN tipocolaborador tc ON tc.colaborador = co.codigo");
        }
        sb.append("\n WHERE co.situacao = 'AT' AND co.empresa = ").append(empresa);
        sb.append("\n AND pe.nome LIKE '").append(nome.toUpperCase()).append("%'");
        if (!todosProfessores) {
            sb.append("\n AND tc.descricao IN ('").append(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()).append("'");
            if (incluirPersonais) {
                sb.append(",'").append(TipoColaboradorEnum.PERSONAL_TRAINER.getSigla()).append("'");
                sb.append(",'").append(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla()).append("'");
                sb.append(",'").append(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla()).append("'");
            }
            sb.append(") GROUP BY pe.nome, pe.codigo, co.codigo ");

        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sb.toString(), DaoAuxiliar.retornarAcessoControle(key).getCon());
        JSONArray json = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("codigoColaborador", rs.getInt("codigocolaborador"));
            obj.put("nome", rs.getString("nome"));
            obj.put("codigoPessoa", rs.getInt("codigopessoa"));
            obj.put("tipo", rs.getBoolean("professor") ? "TW" : rs.getBoolean("personal") ? "PT" : "NA");
            json.put(obj);
        }
        return json.toString();
    }

    @WebMethod
    public String consultarModalidadesEmpresa(@WebParam(name = "key") String key,
                                              @WebParam(name = "empresa") Integer empresa) throws Exception {
        Map<Integer, String> modalidades = DaoAuxiliar.retornarAcessoControle(key).getModalidadeDao().consultarModalidadesSimplificado(empresa);
        JSONArray json = new JSONArray();
        for (Integer codigoModalidade : modalidades.keySet()) {
            JSONObject o = new JSONObject();
            o.put("codigoModalidade", codigoModalidade);
            o.put("nome", modalidades.get(codigoModalidade));
            json.put(o);
        }
        return json.toString();
    }

    @WebMethod
    public String consultarModalidades(@WebParam(name = "key") String key) throws Exception {
        Map<Integer, String> modalidades = DaoAuxiliar.retornarAcessoControle(key).getModalidadeDao().consultarModalidadesSimplificado();
        JSONArray json = new JSONArray();
        for (Integer codigoModalidade : modalidades.keySet()) {
            JSONObject o = new JSONObject();
            o.put("codigoModalidade", codigoModalidade);
            o.put("nome", modalidades.get(codigoModalidade));
            json.put(o);
        }
        return json.toString();
    }

    @WebMethod
    public String consultarAmbientes(@WebParam(name = "key") String key) throws Exception {
        List<AmbienteVO> ambientes = DaoAuxiliar.retornarAcessoControle(key).getAmbienteDao().consultarAmbientesSimplificado();
        JSONArray json = new JSONArray();
        for (AmbienteVO ambiente : ambientes) {
            JSONObject o = new JSONObject();
            o.put("codigoAmbiente", ambiente.getCodigo());
            o.put("capacidade", ambiente.getCapacidade());
            o.put("nome", ambiente.getDescricao());
            json.put(o);
        }
        return json.toString();
    }

    @WebMethod
    public String consultarClientePorNomeCpf(
            @WebParam(name = "key") String key,
            @WebParam(name = "nome") String nome,
            @WebParam(name = "cpf") String cpf,
            @WebParam(name = "empresa") Integer empresa) throws Exception {
        JSONArray json = new JSONArray();
        List<ClienteVO> clientes = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorNomeCPF(
                nome, cpf, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        try {
            for (ClienteVO clienteVO : clientes) {
                JSONObject obj = new JSONObject();
                obj.put("codigo", clienteVO.getCodigo());
                obj.put("nome", clienteVO.getPessoa().getNome());
                obj.put("cpf", clienteVO.getPessoa().getCfp());
                obj.put("empresa", clienteVO.getEmpresa().getCodigo());
                obj.put("matricula", clienteVO.getMatricula());
                json.put(obj);
            }

        } catch (Exception ex) {
            json = new JSONArray();

            JSONObject erro = new JSONObject();
            erro.put("erro", ex.getMessage());
            json.put(erro);
        }

        return json.toString();
    }

    @WebMethod
    public String recuperarUsuarioMovel(
            @WebParam(name = "key") final String key,
            @WebParam(name = "email") final String email,
            @WebParam(name = "empresa") final Integer empresa) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().recuperarUsuarioMovel(email, empresa, key);
            return "Email enviado com sucesso";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String consultarContratosCliente(@WebParam(name = "key") final String key,
                                            @WebParam(name = "cliente") int cliente,
                                            @WebParam(name = "registros") int registros) {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            List list = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().
                    consultarPorCodigoPessoaEPessoaOriginalOrdenadoPelaDataVencimentoContrato(clienteVO.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS, registros);

            if (list != null && list.size() > 0) {
                ContratoVO contratoVO = (ContratoVO) list.get(0);
                if (contratoVO.getVigenciaAte().before(Calendario.hoje()) || !contratoVO.getSituacao().equals("AT")) {
                    List<ContratoDependenteVO> listContratoDependente = DaoAuxiliar.retornarAcessoControle(key).getContratoDependenteDao().findAllByContratoOrderByDataFinal(clienteVO);
                    if (listContratoDependente != null && listContratoDependente.size() > 0 && listContratoDependente.get(0).getDataFinal().after(contratoVO.getVigenciaAte()) &&
                            listContratoDependente.get(0).getContrato().getSituacao().equals("AT")) {
                        list = new ArrayList();
                        list.add(listContratoDependente.get(0).getContrato());
                    }
                }
            } else {
                List<ContratoDependenteVO> listContratoDependente = DaoAuxiliar.retornarAcessoControle(key).getContratoDependenteDao().findAllByContratoOrderByDataFinal(clienteVO);
                if (listContratoDependente != null && listContratoDependente.size() > 0 && listContratoDependente.get(0).getContrato().getSituacao().equals("AT")) {
                    list = new ArrayList();
                    list.add(listContratoDependente.get(0).getContrato());
                }
            }


            JSONArray contratos = new JSONArray();
            for (Object obj : list) {
                ContratoVO contrato = (ContratoVO) obj;
                consultarDadosContrato(key, contrato, clienteVO);
                contrato.setEmpresa(empresaVO);
                contrato.verificarQualBotaoReferenteASituacaoContratoSeraApresentado(clienteVO, list);
                ContratoWS contratoWS = contrato.toWS(true);
                //Caso configuracao da empresa não permita renovar o contrato via app setar o permite renovar como falso.
                if (!empresaVO.isPermiteRenovarContratoViaAPP()) {
                    contratoWS.setPermiteRenovar(false);
                }
                JSONObject contratoObj = new JSONObject(contratoWS);
                contratos.put(contratoObj);
            }

            return contratos.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarProdutosCliente(@WebParam(name = "key") final String key,
                                           @WebParam(name = "cliente") int cliente,
                                           @WebParam(name = "registros") int registros) {

        try {
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

            List list = DaoAuxiliar.retornarAcessoControle(key).getMovProdutoDao().consultarPorCodigoPessoaParaHistoricoCompras(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_PAGAMENTOS_TELA_CLIENTE);

            JSONArray movProdutos = new JSONArray();
            Integer i = 1;
            for (Object obj : list) {
                if (i <= registros) {
                    MovProdutoVO movProduto = (MovProdutoVO) obj;
                    if ((!movProduto.getProduto().getTipoProduto().equals("RN") && !movProduto.getProduto().getTipoProduto().equals("MA") && !movProduto.getProduto().getTipoProduto().equals("RE")) || movProduto.getTotalFinal() > 0) {
                        JSONObject movProdutoObj = new JSONObject(movProduto.toWS());
                        movProdutos.put(movProdutoObj);
                        i++;
                    }
                } else {
                    break;
                }
            }

            return movProdutos.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarParcelasCliente(@WebParam(name = "key") final String key,
                                           @WebParam(name = "cliente") int cliente,
                                           @WebParam(name = "registros") int registros,
                                           @WebParam(name = "emAberto") boolean emAberto) {

        try {
            String ctx = key.contains("_") ? key.split("_")[0] : key;
            boolean ignorarParcelaEmRemessa = true;
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(ctx).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            List<MovParcelaVO> list = null;
            if (key.contains("_")) {
                String nomeTotem = extrairNome(key);
                JSONObject json = new JSONObject(configuracaoTotem(ctx, clienteVO.getEmpresa().getCodigo(), nomeTotem));
                if (json != null && json.toString() != "{}") {
                    ignorarParcelaEmRemessa = !json.getBoolean("APRESENTAR_PARCELAS_EA_BOLETO");
                }
            }
            if (emAberto) {
                list = DaoAuxiliar.retornarAcessoControle(ctx).getMovParcelaDao().consultarPorCodigoPessoaSituacao(clienteVO.getPessoa().getCodigo(), "EA", null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,ignorarParcelaEmRemessa, registros);
            } else {
                list = DaoAuxiliar.retornarAcessoControle(ctx).getMovParcelaDao().consultarPorCodigoPessoaSituacao(clienteVO.getPessoa().getCodigo(), "", null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,false, registros);
            }

            JSONArray movParcelas = new JSONArray();
            for (MovParcelaVO movParcela : list) {
                movParcelas.put(new JSONObject(movParcela.toWS()));
            }

            return movParcelas.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    public static String extrairNome(String entrada) {
        String padrao = "_(.*)";
        Pattern pattern = Pattern.compile(padrao);
        Matcher matcher = pattern.matcher(entrada);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    @WebMethod
    public String consultarAutorizacaoCobranca(@WebParam(name = "key") final String key,
                                               @WebParam(name = "cliente") int cliente) {

        try {
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

            List<AutorizacaoCobrancaClienteVO> list = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoCobrancaDao().consultarPorCliente(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            JSONArray autorizacoes = new JSONArray();
            for (Object obj : list) {
                AutorizacaoCobrancaClienteVO autorizacao = (AutorizacaoCobrancaClienteVO) obj;
                JSONObject autorizacoesObj = new JSONObject(autorizacao.toWS()) ;
                autorizacoes.put(autorizacoesObj);
            }

            return autorizacoes.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarDadosBITreino(@WebParam(name = "key") final String key,
                                         @WebParam(name = "dataInicio") final String dataInicio,
                                         @WebParam(name = "dataFim") final String dataFim,
                                         @WebParam(name = "dataInicioAvencer") final String dataInicioAvencer,
                                         @WebParam(name = "dataFimAvencer") final String dataFimAvencer,
                                         @WebParam(name = "professor") final Integer professor,
                                         @WebParam(name = "empresa") final Integer empresa) {
        try {
            Date inicio = dataInicio == null || dataInicio.isEmpty() ? null : Uteis.getDate(dataInicio);
            Date fim = dataFim == null || dataFim.isEmpty() ? null : Uteis.getDate(dataFim);
            Date inicioVencer = dataInicioAvencer == null || dataInicioAvencer.isEmpty() ? null : Uteis.getDate(dataInicioAvencer);
            Date fimVencer = dataFimAvencer == null || dataFimAvencer.isEmpty() ? null : Uteis.getDate(dataFimAvencer);

            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if(!UteisValidacao.emptyNumber(empresaVO.getNrDiasAvencer())){
                fimVencer = Uteis.somarDias(fim, empresaVO.getNrDiasAvencer());
            }

            DadosBITreinoJSON dados = new DadosBITreinoJSON();
            if (professor == null || professor == 0) {
                dados.setAlunosAtivosForaTreino(DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().obterAlunosAtivosSemUsuarioMovel(empresa));
                dados.setCodsAlunosAtivosForaTreino(DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().obterCodsAlunosAtivosSemUsuarioMovel(empresa));
            }
            if (professor != null && professor > 0) {
                DaoAuxiliar.retornarAcessoControle(key).getHistoricoVinculoDao().preencherDadosBITreino(dados, fim, professor);
                List<Integer> novosNovos = DaoAuxiliar.retornarAcessoControle(key).getVinculoDao().obterNumeroNovosNaCarteira(empresa, inicio, fim, professor, true);
                List<Integer> novosTrocaram = DaoAuxiliar.retornarAcessoControle(key).getVinculoDao().obterNumeroNovosNaCarteira(empresa, inicio, fim, professor, false);
                dados.setNovosNaCarteira(novosNovos.size()+novosTrocaram.size());
                dados.setNovosNaCarteiraNovos(novosNovos.size());
                dados.setNovosNaCarteiraTrocaram(novosTrocaram.size());
                dados.setCodsNovosCarteiraNovos(Uteis.concatenarListaInteger(novosNovos));
                dados.setCodsNovosCarteiraTrocaram(Uteis.concatenarListaInteger(novosTrocaram));

                List<Integer> trocas = DaoAuxiliar.retornarAcessoControle(key).getHistoricoVinculoDao().obterTrocasCarteira(empresa, inicio, fim, professor);
                dados.setTrocasCarteira(trocas.size());
                dados.setCodsTrocasCarteira(Uteis.concatenarListaInteger(trocas));
            }
            List<Integer> listaVencer = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().obterContratosAVencer(professor, inicioVencer, fimVencer, empresa);
            dados.setaVencer(listaVencer.size());
            dados.setCodsAVencer(Uteis.concatenarListaInteger(listaVencer));

            List<Integer> listaRenovados = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().clientesComContratosPrevistosPeriodo(empresa, inicio, fim,professor, true, null, null);
            dados.setRenovados(listaRenovados.size());
            dados.setCodsRenovados(Uteis.concatenarListaInteger(listaRenovados));

            List<Integer> listaPrevisaoRenovacao = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().clientesComContratosPrevistosPeriodo(empresa, inicio, fim, professor, false, null, null);

            List<Integer> listaNaoRenovados = Uteis.diferencaEntreListas(listaPrevisaoRenovacao,listaRenovados);
            dados.setCodsNaoRenovados(Uteis.concatenarListaInteger(listaNaoRenovados));
            dados.setNaoRenovados(listaNaoRenovados.size());

            List<Integer> listaAcessaramMes = DaoAuxiliar.retornarAcessoControle(key).getAcessoClienteDao().alunosAcessoPorProfessorPorPeriodo(empresa, professor, inicio, fim);
            dados.setCodsAlunosAcessaram(Uteis.concatenarListaInteger(listaAcessaramMes));
            dados.setAlunosAcessaram(listaAcessaramMes.size());

            return dados.toJSON();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }


    @WebMethod
    public String consultarClientesRenovaramOuNao(@WebParam(name = "key") final String key,
                                                  @WebParam(name = "dataInicio") final String dataInicio,
                                                  @WebParam(name = "dataFim") final String dataFim,
                                                  @WebParam(name = "professor") final Integer professor,
                                                  @WebParam(name = "empresa") final Integer empresa,
                                                  @WebParam(name = "naoRenovaram") final String naoRenovaram) {
        try {
            Date inicio = dataInicio == null || dataInicio.isEmpty() ? null : Uteis.getDate(dataInicio);
            Date fim = dataFim == null || dataFim.isEmpty() ? null : Uteis.getDate(dataFim);
            ContratoInterfaceFacade contratoDAO = DaoAuxiliar.retornarAcessoControle(key).getContratoDao();
            JSONArray json = contratoDAO.obterListaContratosBITreino(professor, inicio, fim, empresa, Boolean.getBoolean(naoRenovaram));
            return json.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }


    @WebMethod
    public String consultarClientesAtivosForaTreino(
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "modalidades") String modalidades) throws Exception {
        JSONArray json = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().obterListaAlunosAtivosForaTreino(empresa, modalidades.replaceAll("\\-", ","));
        return json.toString();
    }

    @WebMethod
    public String consultarClientesForaTreino(
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa) throws Exception {
        JSONArray json = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().obterListaAlunosForaTreino(empresa);
        return json.toString();
    }

    @WebMethod(operationName = "verificarUsuarioMovel")
    public String verificarUsuarioMovel(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoCliente") Integer codigoCliente,
            @WebParam(name = "matricula") Integer matricula
    ) throws Exception {
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigo(codigoCliente);
        clienteVO.setCodigoMatricula(matricula);
        DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().verificarUsuarioMovel(key, clienteVO);
        return "OK";
    }

    @WebMethod
    public String consultarModalidadesAtivosForaTreino(
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa) throws Exception {
        JSONArray json = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().obterModalidadesAlunosAtivosForaTreino(empresa);
        return json.toString();
    }


    @WebMethod
    public String consultarNrAlunosAtivosForaTreino(@WebParam(name = "key") final String key,
                                                    @WebParam(name = "empresa") final Integer empresa) {
        try {
            Integer nr = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao().obterAlunosAtivosSemUsuarioMovel(empresa);
            return nr.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarAlunos(@WebParam(name = "key") final String key,
                                  @WebParam(name = "codigos") final String codigos,
                                  @WebParam(name = "filter") final String filter) {
        try {
            ClienteInterfaceFacade clienteDAO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao();
            JSONArray json = clienteDAO.obterAlunos(codigos,filter);
            return json.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod
    public String consultarAcessosDia(@WebParam(name = "key") final String key,
                                      @WebParam(name = "dataInicio") final String dataInicio,
                                      @WebParam(name = "dataFim") final String dataFim,
                                      @WebParam(name = "professor") final Integer professor,
                                      @WebParam(name = "empresa") final Integer empresa) {
        try {
            Date inicio = dataInicio == null || dataInicio.isEmpty() ? null : Uteis.getDate(dataInicio);
            Date fim = dataFim == null || dataFim.isEmpty() ? null : Uteis.getDate(dataFim);
            AcessoClienteInterfaceFacade acessoDAO = DaoAuxiliar.retornarAcessoControle(key).getAcessoClienteDao();
            JSONArray json = acessoDAO.obterAcessosPorDia(professor, inicio, fim, empresa);
            return json.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod
    public String consultarAcessosListaDia(@WebParam(name = "key") final String key,
                                           @WebParam(name = "dataInicio") final String dataInicio,
                                           @WebParam(name = "dataFim") final String dataFim,
                                           @WebParam(name = "professor") final Integer professor,
                                           @WebParam(name = "empresa") final Integer empresa,
                                           @WebParam(name = "treino") final String treino) {
        try {
            Boolean alunosTreino = Boolean.valueOf(treino);
            Date inicio = dataInicio == null || dataInicio.isEmpty() ? null : Uteis.getDate(dataInicio);
            Date fim = dataFim == null || dataFim.isEmpty() ? null : Uteis.getDate(dataFim);
            AcessoClienteInterfaceFacade acessoDAO = DaoAuxiliar.retornarAcessoControle(key).getAcessoClienteDao();
            JSONArray json = acessoDAO.obterListaAcessosPorDia(professor, inicio, fim, empresa, alunosTreino);
            return json.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod
    public String consultarDadosGame(@WebParam(name = "key") final String key,
                                     @WebParam(name = "dataInicio") final String mes,
                                     @WebParam(name = "empresa") final Integer empresa,
                                     @WebParam(name = "nrMesesAnterior") final Integer nrMesesAnterior) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            List<DadosGameJSON> dados = dao.montarDadosGame(mes, nrMesesAnterior, empresa);
            JSONArray json = new JSONArray(dados.isEmpty() ? Arrays.asList(new DadosGameJSON[]{new DadosGameJSON()}) : dados);
            return json.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarConsultoresGame(@WebParam(name = "key") final String key,
                                           @WebParam(name = "dataInicio") final String mes,
                                           @WebParam(name = "empresa") final Integer empresa,
                                           @WebParam(name = "nrMesesAnterior") final Integer nrMesesAnterior) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            JSONArray json = dao.obterConsultores(mes, nrMesesAnterior, empresa);
            return json.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarDetalhesDF(@WebParam(name = "key") final String key,
                                      @WebParam(name = "dataInicio") final String mes,
                                      @WebParam(name = "empresa") final Integer empresa,
                                      @WebParam(name = "nrMesesAnterior") final Integer nrMesesAnterior) {
        try {
            DFSinteticoDetalheInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDfDetalheDao();
            List<DFSinteticoDetalheVO> lista = dao.consultar(mes, nrMesesAnterior, empresa);
            JSONArray json = new JSONArray();
            for (DFSinteticoDetalheVO d : lista) {
                JSONObject obj = new JSONObject();
                obj.put("lista", d.getDados());
                obj.put("mesAno", Uteis.getDataAplicandoFormatacao(d.getDataMes(), "MM/yyyy"));
                json.put(obj);
            }
            return json.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }


    @WebMethod
    public String analiseVendaDuracao(@WebParam(name = "key") final String key,
                                      @WebParam(name = "dataInicio") final String mes,
                                      @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            JSONArray json = dao.analiseVendasPorDuracao(mes, empresa);
            return json.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String obterVencimentosContratos(@WebParam(name = "key") final String key,
                                            @WebParam(name = "dataInicio") final String mes,
                                            @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            JSONArray json = dao.obterVencimentos(mes, empresa);
            return json.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String gravarConfiguracoesGame(@WebParam(name = "key") final String key,
                                          @WebParam(name = "empresa") final Integer empresa,
                                          @WebParam(name = "config") final String config) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            dao.salvarConfigs(empresa, config);
            return "OK";
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String obterConfiguracoesGame(@WebParam(name = "key") final String key,
                                         @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            return dao.obterConfigs(empresa);
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarEmpresasSimples(@WebParam(name = "key") String key) throws Exception {
        return DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarEmpresasJson().toString();
    }

    @WebMethod
    public String atualizarDadosGerenciais(@WebParam(name = "key") final String key,
                                           @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGerencialPmgInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGerenciaisDao();
            TicketMedioInterfaceFacade tmdao = DaoAuxiliar.retornarAcessoControle(key).getTicketMedioDao();
            Conexao.guardarConexaoForJ2SE(key, dao.getCon());
            tmdao.gerarDadosSintetico(empresa);
            dao.gerarDadosPMG(empresa, key, Calendario.hoje(), null);
            return "OK";
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String atualizarDadosGerenciaisDia(@WebParam(name = "key") final String key,
                                              @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGerencialPmgInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGerenciaisDao();
            Conexao.guardarConexaoForJ2SE(key, dao.getCon());
            dao.gerarDadosDiaPMG(empresa, key, Calendario.hoje(), null);
            return "OK";
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String obterValorVendas(@WebParam(name = "key") final String key,
                                   @WebParam(name = "empresa") final Integer empresa,
                                   @WebParam(name = "dataConsultar") final String dataConsultar,
                                   @WebParam(name = "tipoDado") final String tipoDado) throws Exception {
        Connection c = null;

        try {
            if (dataConsultar == null) {
                throw new Exception("Parãmetro dataConsultar deve ser informado.");
            }

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Parãmetro empresa deve ser informado.");
            }
            Connection con = DaoAuxiliar.retornarAcessoControle(key).getCon();
            c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            Date data = Uteis.getDate(dataConsultar);
            DadosGerencialPmg dao = new DadosGerencialPmg(c);
            if(tipoDado != null && tipoDado.equalsIgnoreCase("PERIODO")) {
                return dao.obterValorVendasPeriodo(empresa, key, data, "");
            }
            return dao.obterValorVendasDia(empresa, key, data, tipoDado);
        } catch (Exception ex) {
            return ex.getMessage();
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @WebMethod
    public String obterDadosContratoPorDuracao(@WebParam(name = "key") final String key,
                                               @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            return dao.contratosPorDuracao(empresa).toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarFaturamentoDuracao(@WebParam(name = "key") final String key,
                                              @WebParam(name = "dataInicio") final String mes,
                                              @WebParam(name = "empresa") final Integer empresa,
                                              @WebParam(name = "nrMesesAnterior") final Integer nrMesesAnterior) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            return dao.obterFaturamentoDuracao(mes, nrMesesAnterior, empresa).toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarMetasCRM(@WebParam(name = "key") final String key,
                                    @WebParam(name = "dataInicio") final String mes,
                                    @WebParam(name = "empresa") final Integer empresa,
                                    @WebParam(name = "nrMesesAnterior") final Integer nrMesesAnterior) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            return dao.obterMetasCRM(mes, nrMesesAnterior, empresa).toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarDadosSolicitarAtendimento(@WebParam(name = "key") final String key,
                                                     @WebParam(name = "empresaZW") final Integer empresaZW,
                                                     @WebParam(name = "codigoUsuarioZW") final Integer codigoUsuarioZW) {
        try {
            JSONObject obj = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().getEmpresa().obterParametrosAtendimentoSol(empresaZW, codigoUsuarioZW);
            return obj.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarAtestadoCliente(@WebParam(name = "key") final String key,
                                           @WebParam(name = "matricula") final Integer matriculaCliente) {
        try {
            JSONArray obj = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().getAtestado().consultarAtestadoTreino(key, matriculaCliente);
            return obj.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERROR:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarMeioRecuperarLogin")
    public String consultarMeioRecuperarLogin(
            @WebParam(name = "key") String key,
            @WebParam(name = "dataNascimento") String dataNascimento,
            @WebParam(name = "cpf") String cpf) {

        try {

            List<UsuarioVO> usuarioVOList = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarUsuarioDataNascimentoCPF(dataNascimento, cpf);

            if (UteisValidacao.emptyList(usuarioVOList)) {
                throw new Exception("não foi encontrado nenhum usuário com os dados informados.");

            } else if (usuarioVOList.size() > 1) {
                throw new Exception("Hã mais de um usuário com a Data de Nascimento e CPF informados.");

            } else {
                UsuarioVO usuarioVO = usuarioVOList.get(0);

                //Validar se a senha do usuário já expirou
                if (!usuarioVO.getAdministrador()) {
                    boolean senhaExpirada = false;
                    if (usuarioVO.getDataUltimaAlteracaoSenha() != null) {
                        Integer qtdDiasExpirarSenha = DaoAuxiliar.retornarAcessoControle(key).getConfiguracaoDao().buscarQtdDiasParaExpirarSenha();
                        Date dataExpiracao = Uteis.obterDataFutura2(usuarioVO.getDataUltimaAlteracaoSenha(), qtdDiasExpirarSenha);
                        long qtdDiasFaltaExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), dataExpiracao);
                        if (qtdDiasFaltaExpirar <= 0) {
                            senhaExpirada = true;
                        }
                    } else {
                        throw new Exception("A data da ãltima alteração de senha desse usuário não está preenchida.");
                    }

                    if (senhaExpirada) {
                        throw new Exception("A senha está expirada. não serã possível recuperar a senha.");
                    }
                }

                //Validar a situacao do colaborador
                if (!usuarioVO.getColaboradorVO().getCodigo().equals(0) && usuarioVO.getColaboradorVO().getSituacao().equals("NA")) {
                    throw new Exception("Usuário desativado. não serã possível recuperar sua senha.");
                }


                boolean temEmail = false;
                boolean tokenConfigurado = false;
                boolean emailConfigurado = false;

                //VERIFICAR SE A EMPRESA ESTã CONFIGURADA PARA ENVIAR SMS ----- E O USUãRIO TEM NãMERO DE CELULAR VãLIDO
                boolean temSMS = false;
                    List<TelefoneVO> listaTelefones = DaoAuxiliar.retornarAcessoControle(key).getTelefoneDao().consultarTelefones(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyList(listaTelefones)) {
                        for (TelefoneVO tel : listaTelefones) {
                            if (tel.getTipoTelefone().equals("CE") && Uteis.validarTelefoneCelular(tel.getNumero())) {
                                temSMS = true;
                            }
                        }
                    }



                //VERIFICAR SE A EMPRESA ESTã CONFIGURADA PARA ENVIAR EMAIL ----- E O USUãRIO TEM E-MAIL
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
                if (!UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                    emailConfigurado = true;
                    List<EmailVO> listaEmails = DaoAuxiliar.retornarAcessoControle(key).getEmailDao().consultarEmails(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyList(listaEmails)) {
                        for (EmailVO email : listaEmails) {
                            if (UteisValidacao.validaEmail(email.getEmail())) {
                                temEmail = true;
                            }
                        }
                    }
                }

                JSONObject json = new JSONObject();
                json.put("codUsuario", usuarioVO.getCodigo());
                json.put("username", usuarioVO.getUsername());
                json.put("temEmail", temEmail);
                json.put("temSMS", temSMS);
                json.put("tokenConfigurado", tokenConfigurado);
                json.put("emailConfigurado", emailConfigurado);

                return json.toString();
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }


    @WebMethod(operationName = "recuperarLogin")
    public String recuperarLogin(
            @WebParam(name = "key") String key,
            @WebParam(name = "codUsuario") Integer codUsuario,
            @WebParam(name = "username") String username,
            @WebParam(name = "urlLogin") String urlLogin,
            @WebParam(name = "meioEnvio") String meioEnvio) {

        try {

            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(usuarioVO.getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

            if (meioEnvio.equals("SMS")) {

                String loginEnviar = "";
                UsuarioEmailVO usuarioEmailVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioEmailDao().consultarPorUsuario(usuarioVO.getCodigo());
                if (!UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
                    loginEnviar = usuarioEmailVO.getEmail();
                } else {
                    loginEnviar = usuarioVO.getUsername();
                }

                List<TelefoneVO> listaTelefones = DaoAuxiliar.retornarAcessoControle(key).getTelefoneDao().consultarTelefones(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<TelefoneVO> listaTelefoneValidos = new ArrayList<TelefoneVO>();
                for (TelefoneVO tel : listaTelefones) {
                    if (tel.getTipoTelefone().equals("CE") && Uteis.validarTelefoneCelular(tel.getNumero())) {
                        listaTelefoneValidos.add(tel);
                    }
                }

                Integer senhaAleatoria = UteisValidacao.gerarNumeroRandomico(10000, 50000);
                StringBuffer textoSMS = new StringBuffer();
                textoSMS.append(empresaVO.getNome());
                textoSMS.append("\r\nLogin: ").append(loginEnviar);
                textoSMS.append("\r\nNova Senha: ").append(senhaAleatoria);

                String retornoSMS = "";
                boolean smsEnviado = false;
                StringBuilder numeroSMSEnviado = new StringBuilder();

                SmsController smsController = new SmsController("nrBEjwDCDAYPP5rfmoFaxA==", "tP8dW8hW2dD4oW3bM0rC9fP6hB2pI1gZ", TimeZone.getTimeZone(empresaVO.getTimeZoneDefault()));

                for (TelefoneVO telefoneVO : listaTelefoneValidos) {
                    retornoSMS = smsController.sendMessage(null, new Message().setMsg(textoSMS.toString()).setNumero(telefoneVO.getNumero()));
                    if (retornoSMS.toUpperCase().contains("STATUS: OK")) {
                        smsEnviado = true;

                        StringBuilder numero = new StringBuilder();
                        numero.append(telefoneVO.getNumero().substring(0, 6));
                        numero.append("****").append(telefoneVO.getNumero().substring(telefoneVO.getNumero().length() - 2, telefoneVO.getNumero().length()));

                        if (numeroSMSEnviado.toString().isEmpty()) {
                            numeroSMSEnviado.append(numero);
                        } else {
                            numeroSMSEnviado.append(", ").append(numero);
                        }
                    }
                }

                if (!smsEnviado) {
                    throw new Exception("SMS não foi enviado a senha não foi alterada.");
                } else {
                    Integer qtdDiasParaExpirarSenha = DaoAuxiliar.retornarAcessoControle(key).getConfiguracaoDao().buscarQtdDiasParaExpirarSenha();
                    Date dataExpiracao = Uteis.somarDias(Calendario.hoje(), 2);
                    dataExpiracao  = Uteis.somarDias(dataExpiracao, -qtdDiasParaExpirarSenha);

                    usuarioVO.setDataUltimaAlteracaoSenha(dataExpiracao);
                    usuarioVO.setSenha(senhaAleatoria.toString());
                    DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().alterarSenhaUsuario(usuarioVO, true);
                    return "SMS Enviado com sucesso para o(s) número(s): " + numeroSMSEnviado;
                }


            } else if (meioEnvio.equals("EMAIL")) {

                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
                List<EmailVO> listaEmails = DaoAuxiliar.retornarAcessoControle(key).getEmailDao().consultarEmails(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<EmailVO> listaEmailsValidos = new ArrayList<EmailVO>();
                for (EmailVO email : listaEmails) {
                    if (UteisValidacao.validaEmail(email.getEmail())) {
                        listaEmailsValidos.add(email);
                    }
                }

                //GERAR A URL CRIPTOGRAFADA PARA ENVIAR POR EMAIL
                JSONObject json = new JSONObject();
                json.put("codUsuario", usuarioVO.getCodigo());
                json.put("username", usuarioVO.getUsername());
                Calendar dataValidadeURL = Calendar.getInstance();
                dataValidadeURL.add(Calendar.HOUR, 12);
                json.put("dataValidadeURL", dataValidadeURL.getTimeInMillis());
                Calendar dataGeracaoURL = Calendar.getInstance();
                json.put("dataGeracaoURL", dataGeracaoURL.getTimeInMillis());

                String cripto = Uteis.encriptar(json.toString(), "alt_pwd");

                String urlAcessar = urlLogin + "/" + key + "/altSenha=" + cripto;

                String mensagemSuperior = "Alguém solicitou recentemente uma alteração na senha da sua conta do ZillyonWeb. Se vocã tiver solicitado, pode redefini-la aqui:";
                String mensagemInferior = "Se não quiser alterar a senha ou não tiver feito essa solicitação, basta ignorar e excluir esta mensagem. " +
                        "<br><br>Para manter seu login seguro, não encaminhe este e-mail para ninguãm. <br><br>Obrigado!<br>" + empresaVO.getNome();

                StringBuilder corpoEmail = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().gerarCorpoEmail(usuarioVO, mensagemSuperior, mensagemInferior, "Redefinir Senha", urlAcessar, false);

                String assunto = "Recuperação de Login - " + empresaVO.getNome();

                UteisEmail email = new UteisEmail();
                email.novo(assunto, configuracaoSistemaCRMVO);
                String[] emails = new String[listaEmailsValidos.size()];
                int i = 0;
                for (EmailVO emailVO : listaEmailsValidos) {
                    emails[i] = emailVO.getEmail();
                    i++;
                }
                email.enviarEmailN(emails, corpoEmail.toString(), assunto, empresaVO.getNome());

                return "E-mail(s) enviado(s) com sucesso para: " + listaEmailsValidos + " acesse o email para alterar a senha de acesso.";
            } else {
                throw new Exception("Meio de Recuperação não informado");
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "alterarSenhaUsuario")
    public String alterarSenhaUsuario(
            @WebParam(name = "key") String key,
            @WebParam(name = "codUsuario") Integer codUsuario,
            @WebParam(name = "username") String username,
            @WebParam(name = "novaSenha") String novaSenha) {
        String retorno = "";
        try {

            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
            usuarioVO.setSenha(novaSenha);
            DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().alterarSenhaUsuario(usuarioVO, true);
            sincronizarUsuarioTreino(key, usuarioVO);
            retorno = "Senha Alterada com Sucesso.";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            retorno = ex.getMessage();
            return retorno;
        }
        return retorno;
    }

    private void sincronizarUsuarioTreino(String key, UsuarioVO usuarioVO) {
        try {
            if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("USUÁRIO NÃO ENCONTRADO");
            }

            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getCon());
            usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            UsuarioControle usuarioControle = new UsuarioControle();
            usuarioControle.setKey(key);
            usuarioControle.setUsuarioVO(null);
            usuarioControle.editarParametro(usuarioVO);
            usuarioControle.preencherUsuarioEmail();
            usuarioControle.sincronizarUsuarioMovel(true, key);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @WebMethod(operationName = "senhaAlteradaAposGeracaoLink")
    public boolean senhaAlteradaAposGeracaoLink(
            @WebParam(name = "key") String key,
            @WebParam(name = "codUsuario") Integer codUsuario,
            @WebParam(name = "timeInMillisGeracaoURL") String timeInMillisGeracaoURL) {
        try {

            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_MINIMOS);

            Calendar dataUltimaAlteracaoSenha = Calendar.getInstance();
            dataUltimaAlteracaoSenha.setTime(usuarioVO.getDataUltimaAlteracaoSenha());
            return dataUltimaAlteracaoSenha.getTimeInMillis() > Long.parseLong(timeInMillisGeracaoURL);

        } catch (Exception ex) {
            return false;
        }
    }

    @WebMethod(operationName = "salvarCampanha")
    public String salvarCampanha(@WebParam(name = "campanha") String campanha) {
        String retorno = "";
        try {
            String dadosCampanha = Uteis.desencriptar(campanha, CHAVE_APP_LOGIN);
            JSONArray jsonArray = new JSONArray(dadosCampanha);
            Uteis.salvarArquivo("dadosCampanha",dadosCampanha,PropsService.getPropertyValue(PropsService.diretorioArquivos));
            retorno = "Campanha Alterada com Sucesso.";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return  "ERRO"+ex.getMessage();
        }
        return retorno;
    }

    @WebMethod
    public String atualizarDadosGerenciaisPorMes(@WebParam(name = "key") final String key,
                                                 @WebParam(name = "empresa") final Integer empresa,
                                                 @WebParam(name = "mes") final String mes) {
        try {
            DadosGerencialPmgInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGerenciaisDao();
            TicketMedioInterfaceFacade tmdao = DaoAuxiliar.retornarAcessoControle(key).getTicketMedioDao();
            Conexao.guardarConexaoForJ2SE(key, dao.getCon());
            tmdao.gerarDadosSintetico(empresa);
            Date diaAtualizar = Uteis.obterUltimoDiaMes(Uteis.getDate(mes, "dd/MM/yyyy"));
            if (Uteis.getMesData(diaAtualizar) == Uteis.getMesData(Calendario.hoje()) &&
                    Uteis.getAnoData(diaAtualizar) == Uteis.getAnoData(Calendario.hoje())) {
                diaAtualizar = Calendario.hoje();
            }
            dao.gerarDadosPMG(empresa, key, diaAtualizar, null);
            return "OK";
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod(operationName = "nomenclaturaVendaCredito")
    public String nomenclaturaVendaCredito(@WebParam(name = "key") String key,
                                           @WebParam(name = "matricula") String matricula) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.updateConfiguracaoSistema();
            ConfiguracaoSistemaVO config = acessoControle.getConfiguracaoSistemaVO();
            JSONObject dadosSaldoAluno = acessoControle.getSituacaoClienteSinteticoDWDao().obterDadosSaldoAluno(matricula);
            dadosSaldoAluno.put("nomenclatura", OpcoesNomenclaturaVendaCreditoEnum.getDescricao(config.getNomenclaturaVendaCredito()));
            return dadosSaldoAluno.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return  "ERRO"+ex.getMessage();
        }
    }

    enum MetodosPermissoes{
        CONSULTARCLIENTESPELONOME("CHAVENEOZW"),
        CONSULTARCLIENTEPELAMATRICULA("CHAVENEOZW");

        String[] chavesPermitidas;

        private MetodosPermissoes(String ... chaves){
            chavesPermitidas = chaves;
        }
    }

    @WebMethod(operationName = "enviarMensagemAoUsuario")
    public String enviarMensagemAoUsuario(@WebParam(name = "key") String key,
                                          @WebParam(name = "codigoUsuario") Integer codigoUsuario,
                                          @WebParam(name = "mensagem") String mensagem,
                                          @WebParam(name = "titulo") String titulo,
                                          @WebParam(name = "codigoExterno") Integer codigoExterno,
                                          @WebParam(name = "origem") String origem) {
        try {
            Integer codigoSM = DaoAuxiliar.retornarAcessoControle(key).getSocialMailingDao().inserirAvisoAoUsuario(codigoUsuario, null, mensagem, titulo, codigoExterno, origem);
            return codigoSM.toString();
        } catch (Exception ex) {
            Uteis.logar(String.format("%s => ERRO: %s", this.getClass().getName(), ex.getMessage()));
            return  "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "enviarMensagemAoUsuarioUsername")
    public String enviarMensagemAoUsuarioUsername(@WebParam(name = "key") String key,
                                                  @WebParam(name = "username") String username,
                                                  @WebParam(name = "mensagem") String mensagem,
                                                  @WebParam(name = "titulo") String titulo,
                                                  @WebParam(name = "codigoExterno") Integer codigoExterno,
                                                  @WebParam(name = "origem") String origem) {
        try {
            Integer codigoSM = DaoAuxiliar.retornarAcessoControle(key).getSocialMailingDao().inserirAvisoAoUsuario(null, username, mensagem, titulo, codigoExterno, origem);
            return codigoSM.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return  "ERRO: "+ex.getMessage();
        }
    }

    @WebMethod(operationName = "enviarNotificacaoUsuario")
    public String enviarNotificacaoUsuario(@WebParam(name = "key") String key,
                                           @WebParam(name = "codUsuario") Integer codUsuario,
                                           @WebParam(name = "username") String username,
                                           @WebParam(name = "mensagem") String mensagem,
                                           @WebParam(name = "link") String link,
                                           @WebParam(name = "tipoNotificacao") Integer tipoNotificacao) {
        try {
            String retorno = "";
            retorno = DaoAuxiliar.retornarAcessoControle(key).getNotificacaoUsuarioDao().enviarNotificacaoUsuario(codUsuario, username, mensagem, link, tipoNotificacao);
            if (!retorno.startsWith("OK")) {
                throw new Exception(retorno);
            }
            return retorno;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return  "ERRO: "+ex.getMessage();
        }
    }

    @WebMethod(operationName = "alterarSenhaUsuarioEmail")
    public String alterarSenhaUsuarioEmail(
            @WebParam(name = "key") String key,
            @WebParam(name = "email") String email,
            @WebParam(name = "token") String token) {
        String retorno = "";
        try {
            String tokenDecrip = Uteis.desencriptar(token, "tknp455w0rd");
            JSONObject jsonTkn = new JSONObject(tokenDecrip);
            Date horaAlteracao = Uteis.getDate(jsonTkn.optString("horaAlteracao"), "dd/MM/yyyy HH:mm:ss");
            Long minutosEntreDatas = Uteis.minutosEntreDatas(horaAlteracao, Calendario.hoje());
            if(minutosEntreDatas.intValue() > 5){
                throw new Exception("Token expirado");
            }

            Boolean gerarSenhaNova = jsonTkn.optBoolean("gerarSenhaNova");
            if(!gerarSenhaNova){
                String senhaAntiga = jsonTkn.optString("senhaAntiga");
                try {
                    UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getControleAcessoDao().verificarLoginUsuarioApp(email, senhaAntiga, true);
                }catch (Exception e){
                    throw new Exception("Senha atual não está correta!");
                }
            }

            UsuarioEmailVO usuarioEmailVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioEmailDao().consultarPorEmail(email);
            if(UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())){
                throw new Exception("Usuário "+email+" não encontrado na chave "+key);
            }
            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(usuarioEmailVO.getUsuario(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
            usuarioVO.setSenha(jsonTkn.optString("senhaue"));
            DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().alterarSenhaUsuario(usuarioVO, true);

            if (jsonTkn.optBoolean("enviarSMS")){
                EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(usuarioVO.getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String tokenSMS = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().obterTokenSMS(usuarioVO.getColaboradorVO().getEmpresa().getCodigo());
                List<TelefoneVO> listaTelefones = DaoAuxiliar.retornarAcessoControle(key).getTelefoneDao().consultarTelefones(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<TelefoneVO> listaTelefoneValidos = new ArrayList<TelefoneVO>();
                for (TelefoneVO tel : listaTelefones) {
                    if (tel.getTipoTelefone().equals("CE")) {
                        String telfone = "55" + tel.getNumero().replace("(", "").replace(")", "");
                        tel.setNumero(telfone);
                        listaTelefoneValidos.add(tel);
                    }
                }

                StringBuffer textoSMS = new StringBuffer();
                textoSMS.append(empresaVO.getNome());
                textoSMS.append("\r\nSua senha foi alterada ");
                textoSMS.append("\r\n Senha: ").append(jsonTkn.optString("senhaue"));
                SmsController smsController = new SmsController(tokenSMS, key, TimeZone.getTimeZone(empresaVO.getTimeZoneDefault()));

                String retornoSMS = "";
                boolean smsEnviado = false;
                StringBuilder numeroSMSEnviado = new StringBuilder();

                if (listaTelefoneValidos.isEmpty()) {
                    throw new Exception("Nãmero não cadastrado");
                }
                for (TelefoneVO telefoneVO : listaTelefoneValidos) {
                    retornoSMS = smsController.sendMessage(null, new Message().setMsg(textoSMS.toString()).setNumero(telefoneVO.getNumero()));
                    if (retornoSMS.toUpperCase().contains("STATUS: OK")) {
                        smsEnviado = true;

                        StringBuilder numero = new StringBuilder();
                        numero.append(telefoneVO.getNumero().substring(0, 6));
                        numero.append("****").append(telefoneVO.getNumero().substring(telefoneVO.getNumero().length() - 2, telefoneVO.getNumero().length()));

                        if (numeroSMSEnviado.toString().isEmpty()) {
                            numeroSMSEnviado.append(numero);
                        } else {
                            numeroSMSEnviado.append(", ").append(numero);
                        }
                    }
                }

                if (!smsEnviado) {
                    throw new Exception(retornoSMS.replace("ERRO", "").replace(":", ""));
                } else {
                    return "SMS Enviado com sucesso para o nãmero: " + numeroSMSEnviado;
                }
            }

            if(jsonTkn.optBoolean("enviaremail")){
                String[] emailArr = new String[]{email};
                StringBuilder html = new StringBuilder();
                html.append("<h1>Sua senha foi alterada</h1><br/>");
                html.append("A sua senha gerada ã: <b>").append(jsonTkn.optString("senhaue")).append("</b><br/>");
                html.append("Entre no ADM ou no App do gestor e altere para uma senha pessoal.");

                DaoAuxiliar.retornarAcessoControle(key).enviarEmail(
                        emailArr, "Sua senha do ADM foi alterada",
                        html);
            }
            retorno = "Senha Alterada com Sucesso.";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            retorno = ex.getMessage();
            return retorno;
        }
        return retorno;
    }

    @WebMethod
    public String consultarConsultores(@WebParam(name = "key") final String key,
                                       @WebParam(name = "empresa") int empresa) throws Exception {
        try {
            String msgRetorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().consultarColaboradores(empresa);
            if (msgRetorno.startsWith("ERRO")) {
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, msgRetorno);
}
            return msgRetorno;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @WebMethod
    public String listaClientesEstacionamentoSelfit(@WebParam(name = "key") final String key,
                                                    @WebParam(name = "listaPessoas") final String listaPessoas) {
        try {
            ConsultaTO consulta = ConsultasCadastradasTO.obterConsultaAlunoEstacionamento(Uteis.somarDias(Calendario.hoje(),-1));
            String sqlConsulta = consulta.getSQLMontada() + " WHERE " + consulta.getWhere() + " AND cli.pessoa in (" + listaPessoas + ") ";
            return DaoAuxiliar.retornarAcessoControle(key).getClienteDao().obterImportacaoClinteEstacionamento(sqlConsulta,1);
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarCodigoAcesso")
    public String consultarCodigoAcesso(@WebParam(name = "key") final String key,
                                        @WebParam(name = "codacesso") final Integer codigo) {
        String codigoAcesso = "";
        try {
            if ((key == null) || (key.trim().equals(""))) {
                return "";
            }
            codigoAcesso = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarCodigoAcesso(codigo);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return codigoAcesso;
    }

    @WebMethod(operationName = "consultarClientePorCodigoAcesso")
    public String consultarClientePorCodigoAcesso(@WebParam(name = "key") final String key,
                                                  @WebParam(name = "codigoEmpresaZW") final Integer codigoEmpresaZW,
                                                  @WebParam(name = "codacesso") final String codacesso) {
        ConsultaClienteWS consultaClienteWS = new ConsultaClienteWS();
        try {

            String chave = key;
            if ((key == null) || (key.trim().equals(""))) {
                consultaClienteWS.setMsgErro("O parãmetro key ã obrigatãrio");
                return (new JSONObject(consultaClienteWS)).toString();
            }
            consultaClienteWS = DaoAuxiliar.retornarAcessoControle(chave).getClienteDao().consultarCliente(codigoEmpresaZW, null, null, codacesso);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            consultaClienteWS.setMsgErro("ERRO AO CONSULTAR OS DADOS DO CLIENTE. ERRO:" + ex.getMessage());
        }
        JSONObject jsonObject = new JSONObject(consultaClienteWS);
        return jsonObject.toString();
    }

    @WebMethod
    public String atualizarFotoPerfilAluno(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "imagem") String foto) {
        try {
            String fotoKey = DaoAuxiliar.retornarAcessoControle(key).getContratoAssinaturaDigitalService().alterarFotoAluno(
                    key, null, codigo, foto, 0, null);
            return Uteis.getPaintFotoDaNuvem(fotoKey);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String obterQuestionario(
            @WebParam(name = "key") String key,
            @WebParam(name = "tipo") String tipo,
            @WebParam(name = "empresa") Integer empresa) {
        try {
            QuestionarioVO questionario = DaoAuxiliar.retornarAcessoControle(key)
                    .getQuestionarioDao().obterQuestionario(empresa, tipo);
            JSONObject objectToJSON = UteisJSON.objectToJSON(questionario, false, false);
            return objectToJSON.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String responderQuestionario(
            @WebParam(name = "key") String key,
            @WebParam(name = "json") String json,
            @WebParam(name = "questionario") Integer questionario,
            @WebParam(name = "cliente") Integer cliente,
            @WebParam(name = "apenasValidar") String apenasValidar) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            QuestionarioClienteVO questionarioCliente = DaoAuxiliar.retornarAcessoControle(key)
                    .getQuestionarioDao().responderQuestionario(cliente, questionario, new JSONObject(json), "MA", Boolean.valueOf(apenasValidar));
            return questionarioCliente.getCodigo().toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String listaAlunoPontos(@WebParam(name = "Key") final String key,
                                         @WebParam(name = "situacao")String situacao,
                                         @WebParam(name = "analitico")boolean analitico,
                                         @WebParam(name = "cliente")Integer cliente,
                                         @WebParam(name = "nomecliente")final String nomeCliente){

        try {
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getHistoricoPontos().consultaTotalPontosAlunosTreino(situacao,analitico,cliente,nomeCliente, null,null);
            return array.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod
    public String listaAlunoPontosData(@WebParam(name = "Key") final String key,
                                       @WebParam(name = "situacao") String situacao,
                                       @WebParam(name = "analitico") boolean analitico,
                                       @WebParam(name = "cliente") Integer cliente,
                                       @WebParam(name = "nomecliente") final String nomeCliente,
                                       @WebParam(name = "dataInicio") final String dataInicio,
                                       @WebParam(name = "dataFinal") final String dataFinal) {

        try {
            Date dataInicioPesquisar = Uteis.getDate(dataInicio, "dd/MM/yyyy");
            Date dataFinalPesquisar = Uteis.getDate(dataFinal, "dd/MM/yyyy");
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getHistoricoPontos().consultaTotalPontosAlunosTreino(situacao, analitico, cliente, nomeCliente, dataInicioPesquisar, dataFinalPesquisar);
            return array.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod
    public String consultarOperacoesExcecoes(@WebParam(name = "key") final String key,
                                     @WebParam(name = "empresa") final Integer empresa) {
        try {
            DadosGameInterfaceFacade dao = DaoAuxiliar.retornarAcessoControle(key).getDadosGameDao();
            List<SimplesJSON> simplesJSONS = dao.operacoesExcecoes(empresa);
            JSONArray bi = new JSONArray(simplesJSONS);
            return bi.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String persistirLeadRD(
            @WebParam(name = "lead") final String lead,
            @WebParam(name = "empresa") int empresa,
            @WebParam(name = "key") final String key) {

        try {
            DaoAuxiliar.retornarAcessoControle(key).getIntegracaoRDService().processarNovaLead(lead, empresa);
            notificarRecursoEmpresa(DaoAuxiliar.retornarAcessoControle(key).getCon(), key, RecursoSistema.ENVIOU_LEAD_RDSTATION_PARA_CRM, String.valueOf(empresa));
            return "sucesso";
        } catch (Exception ex) {
            try {
                RDStationServiceInterface rdStationServiceInterface = new RDStationServiceImpl(DaoAuxiliar.retornarAcessoControle(key).getCon());
                String retorno = rdStationServiceInterface.persistirLeadRDOLD(lead, empresa, key);
                notificarRecursoEmpresa(DaoAuxiliar.retornarAcessoControle(key).getCon(), key, RecursoSistema.ENVIOU_LEAD_RDSTATION_PARA_CRM, String.valueOf(empresa));
                return retorno;
            }catch (Exception e){
                Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
                return "Erro:" + ex.getMessage();
            }
        }
    }

    @WebMethod
    public String consultarParcelasVencidas(@WebParam(name = "key") final String key,
                                            @WebParam(name = "pessoa") int pessoa) {
        try {
            List<MovParcelaVO> list = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao().consultarParcelasVencidasPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONArray movParcelas = new JSONArray();
            for (MovParcelaVO obj : list) {
                JSONObject movParcelaObj = new JSONObject(obj.toWS());
                movParcelas.put(movParcelaObj);
            }
            return movParcelas.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarJustificativasOperacao(@WebParam(name = "key") final String key,
                                                  @WebParam(name = "empresa") Integer empresa,
                                                  @WebParam(name = "tipo") String tipo) {
        try {
            List<JustificativaOperacaoVO> justs = DaoAuxiliar.retornarAcessoControle(key).
                    getJustificativaOperacaoDao().consultarPorTipoOperacao(tipo, empresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            JSONArray justificativas = new JSONArray();
            for (JustificativaOperacaoVO obj : justs) {
                JSONObject json = new JSONObject();
                json.put("codigo", obj.getCodigo());
                json.put("descricao", obj.getDescricao());
                justificativas.put(json);
            }
            return justificativas.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String listaProdutosAtestado(@WebParam(name = "key") final String key) {
        return listaProdutosServicosGeral(key, TipoProduto.ATESTADO);
    }

    @WebMethod
    public String listaProdutosServicos(@WebParam(name = "key") final String key) {
        return listaProdutosServicosGeral(key, TipoProduto.SERVICO);
    }

    private String listaProdutosServicosGeral(String key, TipoProduto tipoProduto) {
        try {
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().listaJsonProdutosPorTipo(tipoProduto);
            return array.toString();
       } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String lancarProdutoAtestado(@WebParam(name = "key") final String key,
                                        @WebParam(name = "json") String json) {
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().getCon());

            JSONObject jsonInfo = new JSONObject(json);

            boolean incluir = jsonInfo.getBoolean("incluir");
            Integer cliente = jsonInfo.getInt("cliente");
            Integer usuario = jsonInfo.getInt("usuario");
            Integer produto = jsonInfo.getInt("produto");
            Integer avaliacaoFisicaTW = jsonInfo.getInt("avaliacaoFisica");
            boolean parqpositivo = jsonInfo.getBoolean("parqpositivo");
            String observacao = jsonInfo.getString("observacao");
            Date dataInicioDt = Uteis.getDate(jsonInfo.getString("dataInicio"), "dd/MM/yyyy");
            Date dataFinalDt = Uteis.getDate(jsonInfo.getString("dataFinal"), "dd/MM/yyyy");

            UsuarioInterfaceFacade usuarioDAO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao();
            UsuarioVO usuarioVO = UteisValidacao.emptyNumber(usuario) ? usuarioDAO.getUsuarioRecorrencia() : usuarioDAO.consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            ProdutoVO produtoVO = DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().consultarPorCodigo(produto, Uteis.NIVELMONTARDADOS_MINIMOS);

            if (incluir) {
                VendaAvulsaVO vendaAvulsaVO = DaoAuxiliar.retornarAcessoControle(key).getVendaAvulsaDao().gerarVendaAtestado(clienteVO, dataInicioDt, dataFinalDt, produtoVO, produtoVO.getValorFinal(), usuarioVO, clienteVO.getEmpresa());
                AtestadoVO atestadoVO = DaoAuxiliar.retornarAcessoControle(key).getAtestadoDao().incluirAtestado(vendaAvulsaVO, new ArquivoVO(), observacao, parqpositivo, avaliacaoFisicaTW);
            }
            clienteVO.setParqPositivo(parqpositivo);
            DaoAuxiliar.retornarAcessoControle(key).getClienteDao().alterarParqCliente(clienteVO);
            return "Sucesso";

        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String obterValorProdutoCfgEmpresa(@WebParam(name = "key") final String key,
                                              @WebParam(name = "produto") Integer produto,
                                              @WebParam(name = "empresa") Integer empresa) {
        try {
            ProdutoVO produtoVO = DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().obterProdutoCfgEmpresa(produto, empresa);
            return produtoVO.getValorFinal().toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    /**
     * Não removi esse serviço, pois não sei, se é somente no treinoWeb que está sendi utilizado
     * @param key
     * @param cliente
     * @param produto
     * @return
     */
    @WebMethod
    public String validarAlunoProdutoVigente(@WebParam(name = "key") final String key,
                                             @WebParam(name = "cliente") Integer cliente,
                                             @WebParam(name = "produto") Integer produto) {
        try {
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getMovProdutoDao().consultarProdutosVigenciaCliente(cliente, produto, false);
            return array.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String validarProdutoVigente(@WebParam(name = "key") final String key,
                                             @WebParam(name = "cliente") Integer cliente,
                                             @WebParam(name = "produto") Integer produto) {
        try {
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getMovProdutoDao().consultarProdutosVigenciaCliente(cliente, produto, true);
            return array.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String lancarProdutoCliente(@WebParam(name = "key") final String key,
                                       @WebParam(name = "cliente") Integer cliente,
                                       @WebParam(name = "usuario") Integer usuario,
                                       @WebParam(name = "produto") Integer produto,
                                       @WebParam(name = "vencimento") String vencimento) {
        try {
            Date vencimentoDt = Uteis.getDate(vencimento, "dd/MM/yyyy");
            String vendaProdutoCliente = DaoAuxiliar.retornarAcessoControle(key).getVendaAvulsaDao().gerarVendaProdutoCliente(cliente, produto, usuario, vencimentoDt, null);
            return vendaProdutoCliente;
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarDadosOperacaoContrato(
            @WebParam(name = "key") final String key,
            @WebParam(name = "contrato") final Integer contrato,
            @WebParam(name = "tipoOperacao") final String tipoOperacao) {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ContratoVO contratoVO = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            DadosContratoOperacaoWS dados = DaoAuxiliar.retornarAcessoControle(key).getContratoOperacaoDao().obterDadosOperacaoContrato(contratoVO, tipoOperacao);
            JSONObject dadosJson = new JSONObject(dados);
            return dadosJson.toString();
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String validarDadosOperacaoContrato(
            @WebParam(name = "key") final String key,
            @WebParam(name = "contrato") final Integer contrato,
            @WebParam(name = "tipoOperacao") final String tipoOperacao,
            @WebParam(name = "dataInicio") final String dataInicio,
            @WebParam(name = "dataFinal") final String dataFinal,
            @WebParam(name = "produto") final Integer produto,
            @WebParam(name = "justificativa") final Integer justificativa) {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ContratoVO contratoVO = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Date dtInicio = Uteis.getDate(dataInicio, "dd/MM/yyyy");
            Date dtFim = Uteis.getDate(dataFinal, "dd/MM/yyyy");
            DadosRetornoOperacaoWS dados = DaoAuxiliar.retornarAcessoControle(key).getContratoOperacaoDao().validarDadosOperacaoContrato(contratoVO, tipoOperacao, dtInicio, dtFim, produto, justificativa);
            JSONObject dadosJson = new JSONObject(dados);
            return dadosJson.toString();
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String gravarDadosOperacaoContrato(
            @WebParam(name = "key") final String key,
            @WebParam(name = "contrato") final Integer contrato,
            @WebParam(name = "tipoOperacao") final String tipoOperacao,
            @WebParam(name = "dataInicio") final String dataInicio,
            @WebParam(name = "dataFinal") final String dataFinal,
            @WebParam(name = "produto") final Integer produto,
            @WebParam(name = "justificativa") final Integer justificativa,
            @WebParam(name = "obs") final String obs,
            @WebParam(name = "origemSistema") final Integer origemSistema) {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ContratoVO contratoVO = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_FECHAMENTO_ACESSO);
            Date dtInicio = Uteis.getDate(dataInicio, "dd/MM/yyyy");
            Date dtFim = Uteis.getDate(dataFinal, "dd/MM/yyyy");
            String dados = DaoAuxiliar.retornarAcessoControle(key).getContratoOperacaoDao().gravarDadosOperacaoContrato(contratoVO, tipoOperacao, dtInicio, dtFim, produto, justificativa, obs, OrigemSistemaEnum.getOrigemSistema(origemSistema));
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            contratoVO = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);
            consultarDadosContrato(key, contratoVO, clienteVO);
            JSONObject contratoObj = new JSONObject(contratoVO.toWS(false));
            return contratoObj.toString();
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    private void consultarDadosContrato(String key, ContratoVO contrato, ClienteVO clienteVO) throws Exception {
        contrato.setPlano(DaoAuxiliar.retornarAcessoControle(key).getPlanoDao().consultarPorChavePrimaria(contrato.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS));
        contrato.setContratoDuracao(new ContratoDuracaoVO());
        contrato.getContratoDuracao().setNumeroMeses(DaoAuxiliar.retornarAcessoControle(key).getContratoDuracaoDao().consultarDuracaoMeses(contrato.getCodigo()));
        contrato.setContratoModalidadeVOs(DaoAuxiliar.retornarAcessoControle(key).getContratoModalidadeDao().consultarContratoModalidades(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO));
        contrato.setContratoCondicaoPagamento(DaoAuxiliar.retornarAcessoControle(key).getContratoCondicaoPagamentoDao().consultarContratoCondicaoPagamentos(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contrato.setContratoHorario(DaoAuxiliar.retornarAcessoControle(key).getContratoHorarioDao().consultarContratoHorarios(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

        String situacaoSub = "";
        if (contrato.getSituacao().equals("AT")) {
            situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteAtivo(contrato, clienteVO);

        }
        if (contrato.getSituacao().equals("TR")) {
            situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteTrancado(contrato, clienteVO);

        }
        if (contrato.getSituacao().equals("IN")) {
            situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteInativo(contrato, clienteVO);
        }
        if (contrato.getSituacao().equals("CA")) {
            situacaoSub = "CA";
        }
        contrato.setSituacaoSubordinada(situacaoSub);
        ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(DaoAuxiliar.retornarAcessoControle(key).getCon());
        ContratoRecorrenciaVO contratoRecorrenciaVO = contratoRecorrenciaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        contratoRecorrenciaDAO = null;
        if (contratoRecorrenciaVO == null) {
            contratoRecorrenciaVO = new ContratoRecorrenciaVO();
        }
        contrato.setContratoRecorrenciaVO(contratoRecorrenciaVO);
    }

    @WebMethod
    public String lancarConvite(@WebParam(name = "key") final String key,
                                @WebParam(name = "convidou") Integer convidou,
                                @WebParam(name = "convidado") Integer convidado){
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ClienteVO convidadoVO = new ClienteVO();
            convidadoVO.setCodigo(convidado);

            ClienteVO convidouVO = new ClienteVO();
            convidouVO.setCodigo(convidou);

            UsuarioVO usuarioRecorrencia = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().getUsuarioRecorrencia();

            DaoAuxiliar.retornarAcessoControle(key).getConviteDao().lancarConviteValidando(usuarioRecorrencia, convidadoVO, convidouVO);

            return "SUCESSO";
        }catch (Exception e){
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod
    public String validarConvites(@WebParam(name = "key") final String key,
                                @WebParam(name = "convidou") final Integer convidou){
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            DaoAuxiliar.retornarAcessoControle(key).getConviteDao().validarConvite(convidou);
            return "SUCESSO";
        }catch (Exception e){
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod
    public String configuracaoTotem(
            @WebParam(name = "key") final String key,
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "totem") final String totem
            ) {
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            List<TotemTO> totens = DaoAuxiliar.retornarAcessoControle(key).getConfiguracaoTotemDao().obterConfigs(empresa, key);
            if(UteisValidacao.emptyList(totens)){
                TotemTO t = new TotemTO();
                t.setTotem("TOTEM");
                t.setKey(key);
                t.setEmpresa(empresa);
                for(ConfigTotemEnum cfg : ConfigTotemEnum.values()){
                    ConfiguracaoEmpresaTotemVO cfgvo = new ConfiguracaoEmpresaTotemVO();
                    cfgvo.setEmpresa(empresa);
                    cfgvo.setValor(cfg.getPadrao());
                    cfgvo.setConfigTotem(cfg);
                    t.getConfigs().put(cfg, cfgvo);
                }
                totens.add(t);
            }

            Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> mapa = new HashMap<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO>();
            if(UteisValidacao.emptyString(totem) || totens.size() == 1){
                mapa = totens.get(0).getConfigs();
            }else{
                for(TotemTO tt : totens){
                    String nometotem = tt.getTotem().replace("_", "").replace(" ", "");
                    String requestTotem = totem.replace("_", "");
                    if(Uteis.retirarAcentuacaoRegex(nometotem).equals(requestTotem)){
                        mapa = tt.getConfigs();
                    }
                }
            }
            JSONObject dadosJson = new JSONObject();
            for (ConfigTotemEnum cfg : mapa.keySet()) {
                if(cfg != null){
                    dadosJson.put(cfg.name(), mapa.get(cfg).getValor());
                }
            }
            try {

                if (mapa.get(ConfigTotemEnum.USAR_DCO).getValorAsBoolean()) {
                    JSONArray arrayBancos = new JSONArray();
                    List<ConvenioCobrancaVO> convenios = DaoAuxiliar.retornarAcessoControle(key).getConvenioCobrancaDao().consultarPorTiposESituacao(
                            new TipoConvenioCobrancaEnum[]{TipoConvenioCobrancaEnum.DCO_CAIXA, TipoConvenioCobrancaEnum.DCO_ITAU,
                                    TipoConvenioCobrancaEnum.DCO_HSBC, TipoConvenioCobrancaEnum.DCO_BB,
                                    TipoConvenioCobrancaEnum.DCO_BRADESCO, TipoConvenioCobrancaEnum.DCO_SANTANDER},
                            0, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false);
                    for(ConvenioCobrancaVO cv : convenios){
                        BancoVO banco = cv.getBanco();
                        if(banco != null
                                && !UteisValidacao.emptyNumber(banco.getCodigo())){
                            JSONObject json = new JSONObject();
                            json.put("codigo", banco.getCodigo());
                            json.put("nome", banco.getNome());
                            arrayBancos.put(json);
                        }
                    }
                    dadosJson.put("bancos", arrayBancos);
                }
                if (mapa.get(ConfigTotemEnum.USAR_PINPAD_CREDITO).getValorAsBoolean()) {
                    JSONObject configPinpad = new JSONObject();
                    FormaPagamentoVO fpC = DaoAuxiliar.retornarAcessoControle(key).getFormaPagamentoDao().consultarPorChavePrimaria(
                            mapa.get(ConfigTotemEnum.FORMA_PAGAMENTO_CREDITO).getValorAsInt(),
                            Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if(fpC.getPinpad().getPinpad() == 1) {
                        configPinpad.put("cnpj", "");
                        configPinpad.put("pdv", "");
                        configPinpad.put("authentication", PropsService.getPropertyValue(PropsService.authenticationKeyCappta));
                        dadosJson.put("pinpad", configPinpad);
                    }
                } else if (mapa.get(ConfigTotemEnum.USAR_PINPAD_DEBITO).getValorAsBoolean()) {
                    JSONObject configPinpad = new JSONObject();
                    FormaPagamentoVO fpD = DaoAuxiliar.retornarAcessoControle(key).getFormaPagamentoDao().consultarPorChavePrimaria(
                            mapa.get(ConfigTotemEnum.USAR_PINPAD_DEBITO).getValorAsInt(),
                            Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if(fpD.getPinpad().getPinpad() == 1) {
                    configPinpad.put("cnpj", "");
                    configPinpad.put("pdv", "");
                    configPinpad.put("authentication", PropsService.getPropertyValue(PropsService.authenticationKeyCappta));
                    dadosJson.put("pinpad", configPinpad);
                    }
                }
            } catch (Exception e) {
            }
            return dadosJson.toString();
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public ClienteWS consultarDadosCliente(
            @WebParam(name = "key") final String key,
            @WebParam(name = "matricula") final String matricula) throws ConsistirException {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO == null) {
                Uteis.logarDebug("Cliente não encontrado com a matricula: " + matricula);
                throw new Exception("Cliente não encontrado");
            }
            clienteVO.setPontuacao(DaoAuxiliar.retornarAcessoControle(key).getHistoricoPontos().obterPontosTotalPorCliente(clienteVO.getCodigo()));
            return clienteVO.toWS();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            throw new ConsistirException("ERRO: " + ex.getMessage());
        }
    }

    @WebMethod
    public String retornoTrancamento(@WebParam(name = "key") final String key,
                                     @WebParam(name = "contrato") int contrato,
                                     @WebParam(name = "origemSistema") Integer origemSistema) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }

        try {
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            ContratoVO contratoVO = acessoControle.getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_TODOS);
            if (contratoVO == null) {
                throw new Exception("Contrato não encontrado.");
            }

            RetornoTrancamentoContratoControle retornoTrancamentoContratoControle = new RetornoTrancamentoContratoControle();
            retornoTrancamentoContratoControle.setTrancamentoContratoVO(new TrancamentoContratoVO());
            retornoTrancamentoContratoControle.getTrancamentoContratoVO().setContratoVO(contratoVO);
            retornoTrancamentoContratoControle.getTrancamentoContratoVO().setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(origemSistema));
            retornoTrancamentoContratoControle.setListaContratoVOs(new ArrayList<ContratoVO>());
            retornoTrancamentoContratoControle.getListaContratoVOs().add(contratoVO);
            retornoTrancamentoContratoControle.obterUltimoTracamentoContrato();
            retornoTrancamentoContratoControle.tratarHorariosTurmasOcupacao();
            if (retornoTrancamentoContratoControle.getNecessitaManutencao()) {
                throw new Exception("Necessãrio verificar a turma, por favor compareãa a academia");
            }
            if (retornoTrancamentoContratoControle.getTrancamentoContratoVO().getApresentarPanelClienteRetornoForaPrazo()) {
                throw new Exception("Aluno trancado vencido, por favor compareãa a academia.");
            }
            retornoTrancamentoContratoControle.getTrancamentoContratoVO().setResponsavelOperacao(DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().getUsuarioRecorrencia());
            retornoTrancamentoContratoControle.gravarRetornoSemInteracaoComUsuario();
            return "Sucesso";
        } catch (Exception ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String alterarEndereco(@WebParam(name = "ctx") final String ctx,
                                  @WebParam(name = "matricula") String matricula,
                                  @WebParam(name = "codigo") Integer codigo,
                                  @WebParam(name = "tipo") String tipo,
                                  @WebParam(name = "cep") String cep,
                                  @WebParam(name = "bairro") String bairro,
                                  @WebParam(name = "numero") String numero,
                                  @WebParam(name = "complemento") String complemento,
                                  @WebParam(name = "endereco") String endereco,
                                  @WebParam(name = "enderecoCorrenspondencia") boolean enderecoCorrespondencia,
                                  @WebParam(name = "excluir") boolean excluir,
                                  @WebParam(name = "origemSistema") Integer origemSistema) {
        try {
            //se o codigo for 0 ã para adicionar
            if (UteisValidacao.emptyNumber(codigo)) {
                ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(ctx).getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

                EnderecoVO novo = new EnderecoVO();
                novo.setTipoEndereco(tipo);
                novo.setCep(cep);
                novo.setBairro(bairro);
                novo.setNumero(numero);
                novo.setComplemento(complemento);
                novo.setEndereco(endereco);
                novo.setEnderecoCorrespondencia(enderecoCorrespondencia);
                novo.setPessoa(clienteVO.getPessoa().getCodigo());
                DaoAuxiliar.retornarAcessoControle(ctx).getEnderecoDao().incluir(novo);
                return "Endereão adicionado com sucesso. Cãdigo " + novo.getCodigo();

            } else {

                EnderecoVO enderecoVO = DaoAuxiliar.retornarAcessoControle(ctx).getEnderecoDao().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                    if (excluir) {
                        DaoAuxiliar.retornarAcessoControle(ctx).getEnderecoDao().excluir(enderecoVO);
                        return "Endereão excluãdo com sucesso.";
                    } else {
                        enderecoVO.setTipoEndereco(tipo);
                        enderecoVO.setCep(cep);
                        enderecoVO.setBairro(bairro);
                        enderecoVO.setNumero(numero);
                        enderecoVO.setComplemento(complemento);
                        enderecoVO.setEndereco(endereco);
                        enderecoVO.setEnderecoCorrespondencia(enderecoCorrespondencia);
                        DaoAuxiliar.retornarAcessoControle(ctx).getEnderecoDao().alterar(enderecoVO);
                        return "Endereão alterado com sucesso.";
                    }
                } else {
                    throw new Exception("Endereão não encontrado.");
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String alterarEmail(@WebParam(name = "ctx") final String ctx,
                               @WebParam(name = "matricula") String matricula,
                               @WebParam(name = "codigo") Integer codigo,
                               @WebParam(name = "email") String email,
                               @WebParam(name = "emailCorrespondencia") boolean emailCorrespondencia,
                               @WebParam(name = "excluir") boolean excluir,
                               @WebParam(name = "origemSistema") Integer origemSistema) {
        try {

            //se o codigo for 0 ã para adicionar
            if (UteisValidacao.emptyNumber(codigo)) {
                ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(ctx).getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

                EmailVO novo = new EmailVO();
                novo.setEmail(email);
                novo.setEmailCorrespondencia(emailCorrespondencia);
                novo.setBloqueadoBounce(false);
                novo.setPessoa(clienteVO.getPessoa().getCodigo());
                DaoAuxiliar.retornarAcessoControle(ctx).getEmailDao().incluir(novo);
                return "Email adicionado com sucesso. Cãdigo " + novo.getCodigo();

            } else {

                EmailVO emailVO = DaoAuxiliar.retornarAcessoControle(ctx).getEmailDao().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(emailVO.getCodigo())) {
                    if (excluir) {
                        DaoAuxiliar.retornarAcessoControle(ctx).getEmailDao().excluir(emailVO);
                        return "Email excluãdo com sucesso.";
                    } else {
                        emailVO.setEmail(email);
                        emailVO.setEmailCorrespondencia(emailCorrespondencia);
                        DaoAuxiliar.retornarAcessoControle(ctx).getEmailDao().alterar(emailVO);
                        return "Email alterado com sucesso.";
                    }
                } else {
                    throw new Exception("Email não encontrado.");
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String alterarTelefone(@WebParam(name = "ctx") final String ctx,
                                  @WebParam(name = "matricula") String matricula,
                                  @WebParam(name = "codigo") Integer codigo,
                                  @WebParam(name = "tipo") String tipo,
                                  @WebParam(name = "numero") String numero,
                                  @WebParam(name = "descricao") String descricao,
                                  @WebParam(name = "excluir") boolean excluir,
                                  @WebParam(name = "origemSistema") Integer origemSistema) {
        try {

            //se o codigo for 0 ã para adicionar
            if (UteisValidacao.emptyNumber(codigo)) {

                ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(ctx).getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

                TelefoneVO novo = new TelefoneVO();
                novo.setDescricao(descricao);
                novo.setTipoTelefone(tipo);
                novo.setNumero(numero);
                novo.setPessoa(clienteVO.getPessoa().getCodigo());
                DaoAuxiliar.retornarAcessoControle(ctx).getTelefoneDao().incluir(novo);
                return "Telefone adicionado com sucesso. Cãdigo " + novo.getCodigo();

            } else {


                TelefoneVO telefoneVO = DaoAuxiliar.retornarAcessoControle(ctx).getTelefoneDao().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(telefoneVO.getCodigo())) {
                    if (excluir) {
                        DaoAuxiliar.retornarAcessoControle(ctx).getTelefoneDao().excluir(telefoneVO);
                        return "Telefone excluãdo com sucesso.";
                    } else {
                        telefoneVO.setTipoTelefone(tipo);
                        telefoneVO.setNumero(numero);
                        telefoneVO.setDescricao(descricao);
                        DaoAuxiliar.retornarAcessoControle(ctx).getTelefoneDao().alterar(telefoneVO);
                        return "Telefone alterado com sucesso.";
                    }
                } else {
                    throw new Exception("Telefone não encontrado.");
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String consultarParcelaVenda(@WebParam(name = "key") final String key,
                                        @WebParam(name = "venda") int venda) {

        try {
            MovParcelaVO movParcelaVO = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao().
                    consultarPorCodigoVendaAvulsa(venda, "", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (movParcelaVO.getSituacao().toUpperCase().equals("RG")) {
                movParcelaVO.setSituacao("PG");
            }
            JSONObject movParcelaObj = new JSONObject(movParcelaVO.toWS());
            return movParcelaObj.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String listaAlunoPontosApp(@WebParam(name = "Key") final String key,
                                      @WebParam(name = "matriculacliente") String matriculaCliente,
                                      @WebParam(name = "analitico") boolean analitico) {

        try {
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getHistoricoPontos().consultaTotalPontosAlunosApp(matriculaCliente,analitico);
            return array.toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod
    public String consultarContratosRenovar(@WebParam(name = "key") final String key,
                                            @WebParam(name = "cliente") int cliente) {

        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(cliente, false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);

            List list = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarContratosRenovar(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

            JSONArray contratos = new JSONArray();
            for (Object obj : list) {
                ContratoVO contrato = (ContratoVO) obj;
                consultarDadosContrato(key, contrato, clienteVO);
                contrato.verificarQualBotaoReferenteASituacaoContratoSeraApresentado(clienteVO, list);
                contrato.setRenovacao(true);
                JSONObject contratoObj = new JSONObject(contrato.toWS(false));
                contratos.put(contratoObj);
            }

            return contratos.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarPrimeiraParcelaContrato(@WebParam(name = "key") final String key,
                                                   @WebParam(name = "contrato") int contrato) {

        try {
            List<MovParcelaVO> parcelas = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao().consultarPrimeirasParcelasContrato(contrato, 1);
            JSONArray movParcelas = new JSONArray();
            for (MovParcelaVO movParcela : parcelas) {
                if (movParcela.getSituacao().toUpperCase().equals("PG")
                        || movParcela.getSituacao().toUpperCase().equals("RG")) {
                    break;
                }
                JSONObject movParcelaObj = new JSONObject(movParcela.toWS());
                movParcelas.put(movParcelaObj);
            }
            return movParcelas.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String obterContratoOperacao(@WebParam(name = "key") final String key,
                                        @WebParam(name = "contrato") int contrato) {
        try {
            List<ContratoOperacaoVO> operacoes = DaoAuxiliar.retornarAcessoControle(key).getContratoOperacaoDao().consultarPorContrato(contrato,  false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            JSONArray contratoOperacoes = new JSONArray();
            for (ContratoOperacaoVO contratoOperacaoVO : operacoes) {
                JSONObject contratoOpObj = new JSONObject(contratoOperacaoVO.toWS());
                contratoOperacoes.put(contratoOpObj);
            }
            return contratoOperacoes.toString();
        } catch (Exception ex) {
            return "ERRO:" + ex.getMessage();
        }
    }

    private String tratarException(Exception ex) {
        Uteis.logar(ex, this.getClass());
        return (new JSONObject(ex)).toString();
    }


    @WebMethod
    public String incluirAssinaturaDigital(
            @WebParam(name = "key") String key,
            @WebParam(name = "contrato") Integer contrato,
            @WebParam(name = "imagem") String assinatura) {
        try {
            ContratoAssinaturaDigitalServiceInterface cadService = new ContratoAssinaturaDigitalServiceImpl(DaoAuxiliar.retornarAcessoControle(key).getCon());
            cadService.incluirAssinaturaSimples(key, contrato, assinatura);
            return "Sucesso";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultaGenerica")
    public String consultaGenerica(@WebParam(name = "key") final String key,
            @WebParam(name = "consultaSQL") final String consultaSQL) {
        AcessoControle acessoControle = null;
        ResultSet rs = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }
        try {
            rs = SuperFacadeJDBC.criarConsulta(consultaSQL, acessoControle.getCon());
            final JSONArray arr = UteisJSON.convert(rs);
            return arr.toString();
        } catch (Exception ex) {
            TelegramService.enviarErro(key,ex,consultaSQL);
            return tratarException(ex);
        }
    }

    @WebMethod(operationName = "consultaGenericaPaginada")
    public String consultaGenericaPaginada(@WebParam(name = "key") final String key,
            @WebParam(name = "consultaSQL") final String consultaSQL,
            @WebParam(name = "pagina") Integer pagina,
            @WebParam(name = "itensPorPagina") Integer itensPorPagina) {
        AcessoControle acessoControle = null;
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        } catch (Exception ex) {
            return tratarException(ex);
        }
        try {
            ResultSet rsCount = SuperFacadeJDBC.criarConsulta(
                    String.format("select count(1) row_count from (%s) t", consultaSQL),
                    acessoControle.getCon());
            Integer rowCount = 0;
            if (rsCount.next()){
                rowCount = rsCount.getInt("row_count");
            }
            int pages = rowCount > itensPorPagina ? rowCount / itensPorPagina : 1;
            int resto = rowCount % itensPorPagina;
            if (pages > 1 && resto > 0) {
                pages++;
            }
            int offset = itensPorPagina * pagina;
            final String sql = itensPorPagina < rowCount
                    ? String.format(" %s limit %s offset %s", consultaSQL, itensPorPagina, offset)
                    : String.format(" %s ", consultaSQL);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql,
                    acessoControle.getCon());

            JSONObject oRetorno = new JSONObject();
            final JSONArray result = UteisJSON.convert(rs);
            oRetorno.put("rows", rowCount);
            oRetorno.put("offset", offset);
            oRetorno.put("currentPage", pagina);
            oRetorno.put("pages", pages);
            oRetorno.put("limit", itensPorPagina);
            oRetorno.put("result", result);
            return oRetorno.toString();
        } catch (Exception ex) {
            return tratarException(ex);
        }
    }

    private StringBuilder gerarCorpoEmailAtivarUsuarioApp(
            final String nome, final String token) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoTreinoToken.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        String[] imagens = new String[]{"topo_emailP4CT0.png"};
        Map<String, File> mapaImagem = new HashMap<String, File>();
        for (String imagem : imagens) {
            File arqImg = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + imagem).toURI());
            mapaImagem.put(imagem, arqImg);
        }
        UteisEmail.criarImagensEmailTreino(mapaImagem);
        final String aux = texto.toString()
                .replaceAll("#NEW_USER", nome)
                .replaceAll("#TOKEN_NEW", token);
        return new StringBuilder(aux);
    }

    @WebMethod
    public String enviarEmailTokenApp(
            @WebParam(name = "key") String key,
            @WebParam(name = "nome") String nome,
            @WebParam(name = "email") String email,
            @WebParam(name = "token") String token) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).enviarEmail(new String[]{email}, "Confirmação para Acesso ao Pacto Treino",
                    gerarCorpoEmailAtivarUsuarioApp(nome, token));
            return "E-mail com o Token foi enviado com sucesso para " + email;
        } catch (Exception ex) {
            String msg = "Encontrado problema ao enviar e-mail para " + email + " verifique as configurações de envio : " + ex.getMessage();
            return msg;
        }
    }


    @WebMethod
    public String obterEnderecos(
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "chaveAcesso") String chaveAcesso) {
        try {
            JSONObject objeto = new JSONObject();
            JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().obterEnderecosMapa(empresa);
            objeto.put("enderecos", array);
            objeto.put("empresa", DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarParaMapa(empresa));
            return objeto.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String enviarCodigoVerificacao(
            @WebParam(name = "key") String key,
            @WebParam(name = "codEmpresa") Integer codEmpresa,
            @WebParam(name = "codCliente") Integer codCliente,
            @WebParam(name = "telefone") String telefone,
            @WebParam(name = "email") String email) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ClienteTokenVO obj = acessoControle.getClienteTokenDao().incluir(codCliente);
            EmpresaVO empresa = acessoControle.getEmpresaDao().consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String retorno = "";
            if (!UteisValidacao.emptyString(email)) {
                retorno = acessoControle.getTokenService().enviarEmail(empresa, email, obj);
            }
            if (!UteisValidacao.emptyString(telefone)) {
                retorno = acessoControle.getTokenService().enviarSMS(empresa, telefone, obj);
            }
            return "sucesso " + retorno;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String validarCodigoVerificacao(
            @WebParam(name = "key") String key,
            @WebParam(name = "codCliente") Integer codCliente,
            @WebParam(name = "codigoVerificacao") String codigoVerificacao) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ClienteTokenVO tokenVO = acessoControle.getClienteTokenDao().verificar(codCliente, codigoVerificacao);
            return acessoControle.getClienteTokenDao().utilizar(tokenVO);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
}
    }

     @WebMethod
    public String persistirLeadBuzz(
            @WebParam(name = "lead") final String lead,
            @WebParam(name = "empresa") int empresa,
            @WebParam(name = "key") final String key) {

        try {
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoBuzzService().processarNovaLead(lead, empresa);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "Erro:" + ex.getMessage();
        }
    }

    @WebMethod
    public String persistirLeadGenerico(
            @WebParam(name = "lead") final String lead,
            @WebParam(name = "empresa") int empresa,
            @WebParam(name = "key") final String key) {

        try {
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoLeadGenericaService().processarNovaLead(lead, empresa, key);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "Erro:" + ex.getMessage();
        }
    }

    @WebMethod
    public String verificarStatusLeadBuzz(
            @WebParam(name = "lead") final String codigoConversao,
            @WebParam(name = "empresa") int empresa,
            @WebParam(name = "key") final String key) {

        try {
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoBuzzService().consultarStatusIndicacao(codigoConversao);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "Erro:" + ex.getMessage();
        }
    }


    @WebMethod
    public String lancarProdutoClienteVAlor(@WebParam(name = "key") final String key,
                                       @WebParam(name = "cliente") Integer cliente,
                                       @WebParam(name = "usuario") Integer usuario,
                                       @WebParam(name = "produto") Integer produto,
                                       @WebParam(name = "vencimento") String vencimento,
                                       @WebParam(name = "valor") Double valor) {
        try {
            Date vencimentoDt = Uteis.getDate(vencimento, "dd/MM/yyyy");
            String vendaProdutoCliente = DaoAuxiliar.retornarAcessoControle(key).getVendaAvulsaDao().gerarVendaProdutoCliente(cliente, produto, usuario, vencimentoDt, valor);
            return vendaProdutoCliente;
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String persistirClienteJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirClienteJSON(key, json, empresa);
        } catch (Exception ex) {
            try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }
    @WebMethod
    public String categorias(
            @WebParam(name = "key") final String key) {
        try {
            org.json.JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().categorias();
            return array.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }

    }

    @WebMethod
    public String profissoes(
            @WebParam(name = "key") final String key) {
        try {
            org.json.JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().profissoes();
            return array.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }

    }

    @WebMethod
    public String atualizarFotoPerfilAlunoCPF(
            @WebParam(name = "key") String key,
            @WebParam(name = "cpf") String cpf,
            @WebParam(name = "imagem") String foto) {
        try {

            String fotoKey = "";
            JSONObject json = new JSONObject();
            JSONArray jsons = new JSONArray();
            List<Integer> codigos = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().codigoPessoaCpf(cpf);

            for (int i = 0; i < codigos.size(); i++) {
                fotoKey = DaoAuxiliar.retornarAcessoControle(key).getContratoAssinaturaDigitalService().alterarFotoAluno(
                        key, null, codigos.get(i), foto, 0, null);

                jsons.put("Codigo: " + codigos.get(i));
                jsons.put("Foto: " + fotoKey);
                json.put("Lista", jsons);
            }

            return String.valueOf(json);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }


    @WebMethod
    public String classificacoes(
            @WebParam(name = "key") final String key) {
        try {
            org.json.JSONArray array = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().classificacoes();
            return array.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }

    }

    @WebMethod
    public String consultarParcelasVencidasSESC(@WebParam(name = "key") final String key,
                                            @WebParam(name = "matricula") final String matriculaSesc) {
        try {
            Integer pessoa = 0;
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select pessoa from cliente where matriculaSesc = '" + matriculaSesc + "'", DaoAuxiliar.retornarAcessoControle(key).getCon());
            if(rs.next()){
                pessoa = rs.getInt("pessoa");
            }
            List<MovParcelaVO> list = DaoAuxiliar.retornarAcessoControle(key).getMovParcelaDao().consultarParcelasVencidasPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONArray movParcelas = new JSONArray();
            for (MovParcelaVO obj : list) {
                JSONObject movParcelaObj = new JSONObject(obj.toWS());
                movParcelas.put(movParcelaObj);
            }
            return movParcelas.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod
    public String consultarResumoPeriodo(@WebParam(name = "key") final String key,
                                         @WebParam(name = "inicio") final String inicio,
                                         @WebParam(name = "fim") final String fim){
        try {
            org.json.JSONObject jsonObject = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().resumoPeriodo(Uteis.getDate(inicio), Uteis.getDate(fim), false);
            return jsonObject.toString();
        }catch (Exception e){
            return e.getMessage();
        }

    }



    @WebMethod(operationName = "consultarQuantidadeAcessosClientesAgrupadosDia")
    public String consultarQuantidadeAcessosClientesAgrupadosDia(
            @WebParam(name = "key") String contexto,
            @WebParam(name = "codCliente") Integer codigoCliente,
            @WebParam(name = "dataInicial") Long dataInicialMillis,
            @WebParam(name = "dataFinal") Long dataFinalMillis) {

        Date dataInicial = new Date(dataInicialMillis);
        Date dataFinal = new Date(dataFinalMillis);

        try {
            return DaoAuxiliar
                    .retornarAcessoControle(contexto)
                    .getAcessoClienteDao()
                    .consultarQuantidadeAcessosClientePeriodo(codigoCliente, dataInicial, dataFinal);
        } catch (Exception e) {
            return e.getMessage();
        }

    }

    @WebMethod
    public String persistirContratoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirContratoJSON(json, empresa);
        } catch (Exception ex) {
            try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod
    public String persistirPagamentosContratoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirPagamentosContratoJSON(json, empresa);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

     @WebMethod
    public String persistirOperacoesContratoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
         org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirOperacoesContratoJSON(json, empresa);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod
    public String persistirCancelarContratoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirCancelarContratoJSON(json, empresa, key);
        } catch (Exception ex) {
            try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

     @WebMethod
    public String persistirAcessosClienteJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
         org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirAcessosJSON(json, empresa, key);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    /**
     * Persiste o log, a partir do conteãdo recebido em <code>json</code>, que ã transformado em um {@link LogJSON}.
     *
     * @return "" quando a persistãncia ocorrer com sucesso, ou, com o valor da exceção ocorrida.
     */
    @WebMethod(operationName = "persistirLogAuditoria")
    public String persistirLogAuditoria(@WebParam(name = "key") String key,
                                        @WebParam(name = "json") final String json) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key)
                    .getIntegracaoCadastrosDao()
                    .persistirLogAuditoriaJSON(json);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRRO: " + ex.getMessage();
        }
    }

    @WebMethod
    public String consultarParcelasEmAberto(@WebParam(name = "key") final String key,
                                            @WebParam(name = "pessoa") int pessoa) {
        try {
            List<MovParcelaVO> list = DaoAuxiliar.retornarAcessoControle(key).
                    getMovParcelaDao().consultarParcelasEmAbertoPessoa(pessoa,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONArray movParcelas = new JSONArray();
            for (MovParcelaVO obj : list) {
                JSONObject movParcelaObj = new JSONObject(obj.toWS());
                movParcelas.put(movParcelaObj);
            }
            return movParcelas.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod(operationName = "gravarBaixaProtheus")
    public String gravarBaixaProtheus(@WebParam(name = "key") final String key,
                                  @WebParam(name = "recno") final String recno,
                                    @WebParam(name = "titulo") final String titulo) {
        String retorno;
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            DaoAuxiliar.retornarAcessoControle(key).getMovPagamentoDao().incluirBaixaProtheus(recno, titulo);
            retorno = "OK";
        }catch (Exception ex) {
            retorno = "ERRO: " + ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }
        return retorno;
    }

    @WebMethod(operationName = "liberarCobrancaBaixaProtheus")
    public String liberarCobrancaBaixaProtheus(@WebParam(name = "key") final String key,
                                  @WebParam(name = "recno") final String recno) {
        String retorno;
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            DaoAuxiliar.retornarAcessoControle(key).getMovPagamentoDao().liberarCobrancaBaixaProtheus(recno);
            retorno = "OK";
        }catch (Exception ex) {
            retorno = "ERRO: " + ex.getMessage();
            Uteis.logar(ex, this.getClass());
        }
        return retorno;
    }

    @WebMethod(operationName = "consultarInfoFinanceiras")
    public String consultarInfoFinanceiras(@WebParam(name = "key") String key, @WebParam(name = "codEmpresa") Integer codEmpresa) {
        String retorno;
        try {
            JSONArray jsonArray = new JSONArray();
            WebserviceControle webserviceControle = new WebserviceControle(key);
            FinanceiroPacto financeiroPacto = new FinanceiroPacto(webserviceControle.getCon());
            TResultadoParcelaConsultada parcelasAcademia = financeiroPacto.obterParcelasAcademia(key, codEmpresa, 4);

            List<Integer> idsReferencia = new ArrayList<Integer>();
            for (ParcelaConsultada parcelaConsultada : parcelasAcademia.getParcelas()){
                idsReferencia.add(parcelaConsultada.getCodigoParcela());
            }

            ThreadLocal<FacadeFactory> facadeFactory = new ThreadLocal<FacadeFactory>();
            facadeFactory.set(new FacadeFactory());

            String obj = facadeFactory.get().getLoteNFSe(webserviceControle.getCon()).pegarLinkNFSeDoIdExterno(idsReferencia);
            if (!obj.equals("")) {
                JSONObject result = new JSONObject(obj);
                JSONArray arrayRpsReferencia = result.optJSONArray("listaArrayRpsReferencia");
                for (int i = 0; i < arrayRpsReferencia.length(); i++) {
                    JSONObject objReferencia = arrayRpsReferencia.getJSONObject(i);
                    String idRef = objReferencia.optString("IdReferencia");
                    if (!UteisValidacao.emptyString(idRef)) {
                        for (ParcelaConsultada parcelaConsultada : parcelasAcademia.getParcelas()) {
                            int codParcela = parcelaConsultada.getCodigoParcela();
                            if (idRef.contains(Integer.toString(codParcela))) {
                                Integer idLote = objReferencia.getInt("IdRps");
                                String urlNFe = Uteis.getUrlModuloNFSe() + "/nota?rps=" + idLote;
                                parcelaConsultada.setUrlBoleto(urlNFe);
                            }
                        }
                    }
                }
            }
            for (ParcelaConsultada parcelaConsultada : parcelasAcademia.getParcelas()){
                idsReferencia.add(parcelaConsultada.getCodigoParcela());
                ParcelaWS parcelaWS = new ParcelaWS();
                parcelaWS.setValor(parcelaConsultada.getValor());
                parcelaWS.setCodigoParcela(parcelaConsultada.getCodigoParcela());
                parcelaWS.setDataVencimento(parcelaConsultada.getDataVencimentoApresentar());
                parcelaWS.setDescricao(parcelaConsultada.getDescricao());
                parcelaWS.setSituacao(parcelaConsultada.getSituacao());
                parcelaWS.setUrlBoleto(parcelaConsultada.getUrlBoleto());
                parcelaWS.setValor(parcelaConsultada.getValor());
                JSONObject jsonObject = new JSONObject(parcelaWS);
                jsonArray.put(jsonObject);
            }

            retorno = jsonArray.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }

        return retorno;
    }

    @WebMethod(operationName = "downloadBoleto")
    public String downloadBoleto(@WebParam(name = "key") String key,
                                 @WebParam(name = "codigoParcela") Integer codigoParcela,
                                 @WebParam(name = "dataVencimento") String dataVencimento){
        String retorno = "";
        try{
            WebserviceControle webserviceControle = new WebserviceControle(key);
            FinanceiroPacto financeiroPacto = new FinanceiroPacto(webserviceControle.getCon());

            Boolean calcularCobrancasExtras;
            if (Calendario.maiorOuIgual(Uteis.getDate(dataVencimento), Calendario.hoje())) {
                calcularCobrancasExtras = false;
            } else {
                Calendar calendar = Calendario.getInstance();
                int horaDoDia = calendar.get(Calendar.HOUR_OF_DAY);
                calcularCobrancasExtras = horaDoDia >= 17;
            }

            TResultadoBoleto resultadoBoleto = financeiroPacto.regerarParcela(codigoParcela, calcularCobrancasExtras);
            String nomeArquivo;
            if (resultadoBoleto.getResultado().getValue().equals(TResultadoTransacaoWS.rtOK.getValue())) {
                if (resultadoBoleto.getNomeArquivo().contains("\\")) {
                    nomeArquivo = resultadoBoleto.getNomeArquivo().split("boleto\\\\")[1];
                } else {
                    nomeArquivo = resultadoBoleto.getNomeArquivo();
                }
                if (!nomeArquivo.equals("")) {
                    retorno = Uteis.getUrlBoletosFinanceiroPacto() + nomeArquivo;
                }
            } else {
                throw new ConsistirException("Arquivo não encontrado");
            }
        }catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
        return retorno;
    }

    @WebMethod(operationName = "solicitacoesConcluidas")
    public String solicitacoesConcluidas(@WebParam(name = "key") String key,
                                         @WebParam(name = "email") String email){
        try {
            JSONArray jsonArray = new JSONArray();
            ThreadLocal<FacadeFactory> facadeFactory = new ThreadLocal<FacadeFactory>();
            facadeFactory.set(new FacadeFactory());
            List<SolicitacaoJSON> listaSolicitacoesConcluidas = facadeFactory.get().getDashboardService()
                    .buscarSolicitacoesConcluidasBMP(email, LIMIT_CONSULTA_SOLICITACOES_CONCLUIDAS);
            for (SolicitacaoJSON solicitacaoJSON : listaSolicitacoesConcluidas){
                facadeFactory.get().getDashboardService().buscarAndamentosDaSolicitacao(solicitacaoJSON);
                JSONObject obj = new JSONObject(new SolicitacaoWS(solicitacaoJSON));
                jsonArray.put(obj);
            }
            return jsonArray.toString();
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
    }

    @WebMethod(operationName = "solicitacoesEmAberto")
    public String solicitacoesEmAberto(@WebParam(name = "key") String key,
                                       @WebParam(name = "email") String email){
        try {
            JSONArray jsonArray = new JSONArray();
            ThreadLocal<FacadeFactory> facadeFactory = new ThreadLocal<FacadeFactory>();
            facadeFactory.set(new FacadeFactory());
            List<SolicitacaoJSON> listaSolicitacoesConcluidas = facadeFactory.get().getDashboardService().buscarSolicitacoesEmAbertoServiceBMP(email);
            for (SolicitacaoJSON solicitacaoJSON : listaSolicitacoesConcluidas){
                facadeFactory.get().getDashboardService().buscarAndamentosDaSolicitacao(solicitacaoJSON);
                JSONObject obj = new JSONObject(new SolicitacaoWS(solicitacaoJSON));
                jsonArray.put(obj);
            }
            return jsonArray.toString();
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
    }

    @WebMethod(operationName = "consultarEstatisticaSolicitacao")
    public String consultarStatisticaSolicitacao(@WebParam(name = "email") String email){
        try{
            ThreadLocal<FacadeFactory> facadeFactory = new ThreadLocal<FacadeFactory>();
            facadeFactory.set(new FacadeFactory());
            EstatisticaSolicitacaoJSON estatisticaSolicitacaoJSON  = facadeFactory.get().
                    getDashboardService().consultarEstatisticaSolicitacao(email, LIMIT_SOLICITACOES_ESTATISTICA);
            return new JSONObject(estatisticaSolicitacaoJSON).toString();
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
    }

    @WebMethod
    public String deletarClienteImportacaoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirClienteJSON(key, json, empresa);
        } catch (Exception ex) {
            try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }


    @WebMethod
    public String deletarContratoImportacaoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().deletarContratoJson(json, empresa);
        } catch (Exception ex) {
            try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod
    public String deletarPagamentosContratoImportacaoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().deletarPagamentosContratoJSON(json, empresa);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod
    public String persistirProdutoJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirProdutosJSON(json, empresa);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod
    public String persistirVendaAvulsaJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirVendasAvulsasJSON(json, empresa);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }

    @WebMethod
    public String persistirPagamentosVendaAvulsaJSON(
            @WebParam(name = "empresa") final Integer empresa,
            @WebParam(name = "json") final String json,
            @WebParam(name = "key") final String key) {
        org.json.JSONObject retorno = new org.json.JSONObject();
        try {
            Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
            retorno = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoImportacaoDao().persistirPagamentosVendaAvulsaJSON(json, empresa);
        } catch (Exception ex) {
             try {
                retorno.put("status", "error");
                retorno.put("codigoRegistroZW", "0");
                retorno.put("mensagem", ex.getMessage());
            } catch (org.json.JSONException ignored) {
            }
        }
        return retorno.toString();
    }


    @WebMethod(operationName = "buscarEstatisticasMovidesk")
    public String buscarEstatisticasMovidesk(@WebParam(name = "id") String id){
        try{
            ThreadLocal<FacadeFactory> facadeFactory = new ThreadLocal<FacadeFactory>();
            facadeFactory.set(new FacadeFactory());

            EstatisticaTicketJSON estatisticaTicketJSON  = facadeFactory.get().
                    getDashboardService().consultarEstatisticaMovidesk(id);
            return new JSONObject(estatisticaTicketJSON).toString();
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "{"+ERRO +":'"+ex.getMessage()+"'}";
        }
    }

    @WebMethod(operationName = "marcarAvaliacaoFeedGestao")
    public String marcarAvaliacaoFeedGestao (@WebParam(name = "key") final String key,
                                             @WebParam(name = "codigoHistorico") Integer codigoHistorico,
                                             @WebParam(name = "usuario") Integer usuario,
                                             @WebParam(name = "liked") boolean liked,
                                             @WebParam(name = "disliked") boolean disliked) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).getFeedGestaoDao().marcarAvaliacao(codigoHistorico, usuario, liked, disliked);
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "ERRO: " + ex.getMessage();
        }

        return "Sucesso";
    }

    @WebMethod(operationName = "marcarComoLidaFeedGestao")
    public String marcarComoLidaFeedGestao (@WebParam(name = "key") final String key,
                                            @WebParam(name = "codigoHistorico") final Integer codigoHistorico,
                                            @WebParam(name = "codUsuario") Integer codUsuario) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).getFeedGestaoDao().marcarLida(codigoHistorico, codUsuario);
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "ERRO: " + ex.getMessage();
        }

        return "Sucesso";
    }

    @WebMethod(operationName = "consultarFeedGestao")
    public String consultarFeedGestao (@WebParam(name = "key") final String key,
                                       @WebParam(name = "empresa") Integer empresa,@WebParam(name = "codUsuario") Integer codUsuario,
                                       @WebParam(name = "perfil") Integer perfil) {
        try {
            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            List<FeedGestaoVO> feedGestaoVOS = DaoAuxiliar.retornarAcessoControle(key).getFeedGestaoDao().obterTodos(empresa, usuarioVO, perfil);
            JSONArray array = new JSONArray();
            for (FeedGestaoVO f : feedGestaoVOS) {
                JSONObject o = UteisJSON.objectToJSON(f, false);
                array.put(o);
            }
            return array.toString();
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarClienteJson")
    public String consultarClienteJson(@WebParam(name = "key") String key,
                                       @WebParam(name = "json") String json) {
        try {

            JSONObject obj = new JSONObject(json);

            String email = obj.optString("email");
            String cpf = obj.optString("cpf");
            String cnpj = obj.optString("cnpj");
            int empresa = obj.optInt("empresa");
            int cliente =  obj.optInt("cliente");
            boolean consultarEndereco = obj.optBoolean("consultarEndereco");
            boolean consultarQtdDependentes = obj.optBoolean("consultarQtdDependentes");
            boolean telaCadastroVisitante = obj.optBoolean("telaCadastroVisitante");

            List<ConsultaClienteWS> clientes = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarClienteEmailOuCPFouCnpj(email, cpf, cnpj, empresa, cliente, consultarEndereco, consultarQtdDependentes);
            if(clientes.isEmpty()) {
                //Está regra considera tbm os clientesExternoVindoDeAutorizacao
                List<ClienteVO> clientesExternoVindoDeAutorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCpfMock(cpf);
                clientes = clientesExternoVindoDeAutorizacao.stream().map(cVo -> ConsultaClienteWS.buildlClienteVoAutorizacao(cVo,cpf)).collect(Collectors.toList());
            }
            if (UteisValidacao.emptyList(clientes)){
                throw new Exception("Nenhum cliente encontrado");
            } else if (telaCadastroVisitante && !UteisValidacao.emptyNumber(empresa)) {
                boolean permiteProsseguirMesmoCpfCadastroVisitante = DaoAuxiliar.retornarAcessoControle(key).getVendasConfigDao().config(empresa).isPermiteProsseguirMesmoCpfCadastroVisitante();
                if (!permiteProsseguirMesmoCpfCadastroVisitante) {
                    throw new Exception("Este CPF informado já está cadastrado no sistema. Não será possível prosseguir, procure a recepção da academia!");
                }
            }

            JSONArray jsonArray = new JSONArray();
            for (ConsultaClienteWS consultaClienteWS : clientes) {
                jsonArray.put(new JSONObject(consultaClienteWS));
            }
            return jsonArray.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "enviarEmailGenerico")
    public String enviarEmailGenerico(@WebParam(name = "key") String key,
                              @WebParam(name = "json") String json) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().enviarEmail(json);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "obterPontosPorCliente")
    public String obterPontosPorCliente(@WebParam(name = "key") String key,
                                      @WebParam(name = "cliente") final Integer cliente) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getHistoricoPontos().obterPontosTotalPorCliente(cliente).toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "obterTiposColaboradoes")
    public String obterTiposColaboradoes(@WebParam(name = "key") String key, @WebParam(name = "colaborador") Integer colaborador) {
        try {
            Map<Integer, List<String>> tiposDosColaboradores = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().tiposDosColaboradores(colaborador);
            JSONArray array = new JSONArray();
            for (Integer k : tiposDosColaboradores.keySet()){
                List<String> tipos = tiposDosColaboradores.get(k);
                JSONObject json = new JSONObject();
                json.put("colaborador", k);
                String tiposstr = "";
                for(String s : tipos){
                    tiposstr += "," + s;
                }
                json.put("tipos", tiposstr.replaceFirst(",", ""));
                array.put(json);
            }
            return array.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "checkInGymPass")
    public String checkInGymPass(@WebParam(name = "key") String key,
                                 @WebParam(name = "json") String json,
                                 @WebParam(name = "empresa") final int empresa) {
        CheckinGympassService checkinGympassService = new CheckinGympassService();
        return checkinGympassService.checkInGymPass(key, json, empresa);
    }


    @WebMethod(operationName = "consultarIntegracaoFeraJson")
    public String consultarIntegracaoFeraJson(@WebParam(name = "key") String key) {

        try {
            String data = Calendario.getData(Calendario.hoje(), "yyyyMMdd");
            String nomeArquivo = PropsService.getPropertyValue(PropsService.diretorioArquivos) + "/" + ("IntegracaoFera-" + key + "-" + data + ".xml");

            File retorno = new File(nomeArquivo);

//            retorno.setPath(this.getServletContext().getRealPath("relatorio")+ File.separator);
            if (retorno.exists()) {
                return nomeArquivo;
            } else {
                List<ConsultaIntegracaoFera> clientes = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarIntegracaoFera();
                String retornoTreino = TreinoWSConsumer.obterTodosProgramasAluno(key);
                JSONArray jsonArrayTW = new JSONArray(retornoTreino.toString());

                List<ConsultaIntegracaoFera> consultaIntegracaoFeraList = new ArrayList<ConsultaIntegracaoFera>();
                int it = 0;
                for (int i = 0; i < clientes.size(); i++) {

                    ConsultaIntegracaoFera csf = new ConsultaIntegracaoFera();
                    csf.setCodigo(clientes.get(i).getCodigo());
                    csf.setNome(clientes.get(i).getNome());
                    csf.setEmpresa(clientes.get(i).getEmpresa());
                    csf.setDataVencimentoPlano(clientes.get(i).getDataVencimentoPlano());
                    csf.setStatus(clientes.get(i).getStatus());
                    csf.setEmail(clientes.get(i).getEmail());
                    FrequenciaAlunoFeraJSON frequenciaAlunoFeraJSON = clientes.get(i).getFrequencia();
                    frequenciaAlunoFeraJSON.set15Dias(clientes.get(i).getFrequencia().get15Dias());
                    frequenciaAlunoFeraJSON.set30Dias(clientes.get(i).getFrequencia().get30Dias());
                    csf.setFrequencia(frequenciaAlunoFeraJSON);
                    csf.setUltimaVisita(clientes.get(i).getUltimaVisita());
                    csf.setDataNascimento(clientes.get(i).getDataNascimento());
                    csf.setEmpresaUsaFreePass(clientes.get(i).getEmpresaUsaFreePass());
                    csf.setDataCadastro(clientes.get(i).getDataCadastro());
                    csf.setSexo(clientes.get(i).getSexo());

                    JSONObject objTreino = jsonArrayTW.getJSONObject(it);
                    ConsultaIntegracaoFera csfT = new ConsultaIntegracaoFera();
                    csfT.setCodigo(objTreino.getInt("codigocliente"));
                    csfT.setDataVencimentoTreino(objTreino.getString("dataPrevistaTermino"));
                    if (csf.getCodigo() == csfT.getCodigo()) {
                        csf.setDataVencimentoTreino(csfT.getDataVencimentoTreino());
                        if (it < (jsonArrayTW.length() - 1)) {
                            it++;
                        }
                        consultaIntegracaoFeraList.add(csf);
                    } else if (csf.getCodigo() > csfT.getCodigo()) {
                        if (it < (jsonArrayTW.length() - 1)) {
                            i--;
                            it++;
                        } else if (it >= (jsonArrayTW.length() - 1)) {
                            consultaIntegracaoFeraList.add(csf);
                        }

                    } else if (csf.getCodigo() < csfT.getCodigo()) {
                        consultaIntegracaoFeraList.add(csf);
                    }

                }

                if (UteisValidacao.emptyList(clientes)) {
                    throw new Exception("Nenhum cliente encontrado");
                }

                org.json.JSONArray jsonArray = new org.json.JSONArray();
                for (ConsultaIntegracaoFera consultaIntegracaoFera : clientes) {
                    org.json.JSONObject objJ = new org.json.JSONObject(consultaIntegracaoFera.toJSON());
                    jsonArray.put(objJ);
                }
                String xml = XML.toString(jsonArray, "array");
                StringBuilder stringBuilder = new StringBuilder("<root>");
                stringBuilder.append(xml);
                stringBuilder.append("</root>");
                StringUtilities.saveToFile(stringBuilder, nomeArquivo);

                return nomeArquivo;
            }

        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "buscarLocaisAcesso")
    public String buscarLocaisAcesso(@WebParam(name = "key") String key,
                                     @WebParam(name = "empresa") final Integer empresa) {
        try {
            List<ColetorVO> lista = DaoAuxiliar.retornarAcessoControle(key).getColetorDao().consultarPorEmpresa(empresa);

            JSONArray json = new JSONArray();
            for (ColetorVO coletor : lista) {
                json.put(coletor.toJSON());
            }
            return json.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarLocalAcessoPorNFC")
    public String consultarLocalAcessoPorNFC(@WebParam(name = "key") String key,
                                             @WebParam(name = "empresa") final String nfc) {
        try {
            ColetorVO coletor = DaoAuxiliar.retornarAcessoControle(key).getColetorDao().consultarPorNFC(nfc);

            return coletor.toJSON().toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "apiWeHelp")
    public String apiWeHelp(@WebParam(name = "tokenEmpresa") String tokenEmpresa,
                            @WebParam(name = "empresa") int empresa,
                            @WebParam(name = "data") String data) {

        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            java.sql.Date dataConsulta = new java.sql.Date(format.parse(data).getTime());
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(tokenEmpresa).getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!empresaVO.isIntegracaoWeHelpHabilitada()) {
                return "A integração com a Wehelp não está habilitada para essa empresa.";
            }
            JSONArray clientes = DaoAuxiliar.retornarAcessoControle(tokenEmpresa).getAcessoClienteDao().obterAcessosDiaWeHelpClientes(empresa,dataConsulta, empresaVO.isCpfCodigoInternoWeHelp());
            JSONArray colaboradores = DaoAuxiliar.retornarAcessoControle(tokenEmpresa).getAcessoClienteDao().obterAcessosDiaWeHelpColaboradores(empresa,dataConsulta, empresaVO.isCpfCodigoInternoWeHelp());
            JSONArray result = new JSONArray();
            for (int i = 0; i < clientes.length(); i++) {
                    result.put(clientes.get(i));
                }
            for (int i = 0; i < colaboradores.length(); i++) {
                    result.put(colaboradores.get(i));
                }
            return result.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "Não foi possivel pesquisar os acessos deste dia.";
        }
    }


    @WebMethod
    public String persistirCodeLeadRD(
            @WebParam(name = "code") final String code,
            @WebParam(name = "empresa")final int empresa,
            @WebParam(name = "key") final String chave) {

        try {
            boolean temCodigoValido = DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().temCodigoValido(empresa);
            boolean existeCodigoInserido = DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().existeCodigoInserido(empresa);

            if (!temCodigoValido && !existeCodigoInserido){
                return DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().processarCodeRDStation(code, empresa,chave);
            }else if (!temCodigoValido){
                return DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().atualizarCodeRDStation(code, empresa,chave);
            }
            return "SUCESSO: já existe codigo valido na base de dados.";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String persistirTokenLeadRD(
            @WebParam(name = "access_token") final String access_token,
            @WebParam(name = "refresh_token") final String refresh_token,
            @WebParam(name = "empresa")final int empresa,
            @WebParam(name = "key") final String chave) {

        try {
            String persistirtokenRD = DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().processarTokenRDStation(access_token,refresh_token, empresa,chave);
            if (persistirtokenRD.toUpperCase().startsWith("SUCESSO:")){
                return "SUCESSO: foi persistido no banco o acess_token e o refresh_token";
            }else {
                return persistirtokenRD;
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String acessTokenValido(
            @WebParam(name = "empresa")final int empresa,
            @WebParam(name = "key") final String chave) {

        try {
            boolean temTokenAuth2Valido = DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().temTokenAuth2Valido(empresa);
            if (temTokenAuth2Valido){
                return DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().obterAcessToken(empresa);
            }else{
                return "";
            }
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String buscaIDSecret(
            @WebParam(name = "empresa")final int empresa,
            @WebParam(name = "key") final String chave) {

        try {
                return DaoAuxiliar.retornarAcessoControle(chave).getIntegracaoLeadGenericaService().buscaIDSecret(empresa);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarFotoKeyPessoa")
    public String consultarFotoKeyPessoa (
            @WebParam(name = "codigoPessoa") final Integer codigoPessoa,
            @WebParam(name = "key") final String key) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().obterFotoKey(codigoPessoa);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "buscarClientesPesquisaTW")
    public String buscarClientesPesquisaTW(@WebParam(name = "key") String key) {
        try {
            JSONArray json = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarDadosClienteConsultaTW(null);
            return json.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod(operationName = "situacaoUsuario")
    public Boolean situacaoUsuario (
            @WebParam(name = "codigoUsuario") final Integer codigoUsuario,
            @WebParam(name = "codigoEmpresa") final Integer codigoEmpresa,
            @WebParam(name = "key") final String key) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarSituacao(codigoUsuario, codigoEmpresa);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
        }
        return false;
    }

    @WebMethod(operationName = "atualizarGeolocalizacao")
    public String atualizarGeolocalizacao(
            @WebParam(name = "codigo") final Integer codigo,
            @WebParam(name = "latitude") final String latitude,
            @WebParam(name = "longitude") final String longitude,
            @WebParam(name = "key") final String key) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().atualizarGeolocalizacao(latitude, longitude, codigo);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "enderecosGeolocalizar")
    public String enderecosGeolocalizar(
            @WebParam(name = "key") final String key) {
        try {
            JSONArray jsonArray = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().enderecosGeolocalizar();
            return jsonArray.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "enderecosGeolocalizarAposHabilitarChave")
    public String enderecosGeolocalizarAposHabilitarChave(
            @WebParam(name = "key") final String key) {
        try {
            JSONArray jsonArray = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().enderecosGeolocalizarAposHabilitarChave();
            return jsonArray.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "notificarRecursoEmpresaGeolocalizacao")
    public String notificarRecursoEmpresaGeolocalizacao(
            @WebParam(name = "empresa") String empresa,
            @WebParam(name = "chave") String chave){
        try {
            notificarRecursoEmpresa(DaoAuxiliar.retornarAcessoControle(chave).getCon(), chave, RecursoSistema.ABRIU_TELA_GEOLOCALIZACAO, empresa);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }


    @WebMethod(operationName = "obterEnderecoCodigo")
    public String obterEnderecoCodigo(
            @WebParam(name = "codigo") final Integer codigo,
            @WebParam(name = "key") final String key) {
        try {
            JSONObject jsonObject = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().obterEndereco(codigo);
            return jsonObject.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "obterEnderecoEmpresa")
    public String obterEnderecoEmpresa(
            @WebParam(name = "key") final String key) {
        try {
            JSONArray jsonArray = DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().obterEnderecoEmpresa();
            return jsonArray.toString();
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "updateGeolocEmpresa")
    public String updateGeolocEmpresa(
            @WebParam(name = "key") final String key,
            @WebParam(name = "json") String json) {
        try {
            DaoAuxiliar.retornarAcessoControle(key).getEnderecoDao().updateGeolocalizacaoEmpresa(json);
            return "Sucess";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "atualizarBoletoPJBank")
    public String atualizarBoletoPJBank(@WebParam(name = "json") String json,
                                        @WebParam(name = "chave") String chave) {
        Boleto boletoDAO;
        try (Connection c = new DAO().obterConexaoEspecifica(chave)) {
            boletoDAO = new Boleto(c);
            boletoDAO.processarBoletoPJBank(json, "WebHook");
            return "sucesso";
        } catch (Exception e) {
            e.printStackTrace();
            return "ERRO:" + e.getMessage();
        } finally {
            boletoDAO = null;
        }
    }

    @WebMethod
    public String gerarUsuarioMovelColaborador(
            @WebParam(name = "key") String key,
            @WebParam(name = "codigoColaborador") Integer codigoColaborador,
            @WebParam(name = "senha") String senha,
            @WebParam(name = "username") String username) {

        try {

            if (UteisValidacao.emptyNumber(codigoColaborador)) {
                throw new ConsistirException("Código de colaborador não informado");
            }

            ColaboradorVO colaboradorVO = DaoAuxiliar.retornarAcessoControle(key).
                    getColaboradorDao().consultarPorChavePrimaria(codigoColaborador, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            /**
             * Como determinar o Email (username): 1. Se o usãrio informar um
             * novo e-mail, o sistema deve verificar se este já existe
             * cadastrado e incluir se não existe; 2. Se o usuário não informar
             * um e-mail, o sistema deve procurar o primeiro cadastrado e
             * utilizã-lo como 'username';
             */

            Email emailDao = DaoAuxiliar.retornarAcessoControle(key).getEmailDao();
            List<EmailVO> listaEmails = emailDao.consultarEmails(colaboradorVO.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boolean emailExiste = false;
            for (EmailVO emailVO : listaEmails) {
                if (username != null && emailVO.getEmail().equalsIgnoreCase(username)) {
                    emailExiste = true;
                    break;
                }
            }

            if (UteisValidacao.emptyString(username)) {
                username = listaEmails.isEmpty() ? null : listaEmails.get(0).getEmail();
            }
            if (UteisValidacao.emptyString(username)) {
                throw new ConsistirException("Nenhum Email cadastrado/informado");
            }

            if (!emailExiste) {
                EmailVO emailNovo = new EmailVO();
                emailNovo.setEmail(username);
                emailNovo.setEmailCorrespondencia(false);
                emailNovo.setPessoa(colaboradorVO.getPessoa().getCodigo());
                emailDao.incluir(emailNovo, false);
            }

            username = username.toLowerCase();
            if (!UteisEmail.getValidEmail(username)) {
                throw new ConsistirException(String.format("Email %s inválido", new Object[]{username}));
            }

            UsuarioMovelInterfaceFacade usuarioMovelDao = DaoAuxiliar.retornarAcessoControle(key).getUsuarioMovelDao();
            UsuarioMovelVO usuarioMovelVO = usuarioMovelDao.consultarPorColaborador(colaboradorVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            colaboradorVO = DaoAuxiliar.retornarAcessoControle(key).
                    getColaboradorDao().consultarPorChavePrimaria(codigoColaborador, Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA);

            boolean senhaAlterada = false;
            if (usuarioMovelVO.getCodigo() > 0) {
                usuarioMovelVO.setNome(username);
                usuarioMovelVO.setSenha(senha);
                usuarioMovelVO.setAtivo(true);
                if(UteisValidacao.emptyString(senha)) {
                    usuarioMovelDao.alterarSemSenha(usuarioMovelVO);
                }else{
                    usuarioMovelDao.alterar(usuarioMovelVO);
                    senhaAlterada = true;
                }
            } else {
                if (UteisValidacao.emptyString(senha)) {
                    String uuid = UUID.randomUUID().toString();
                    senha = uuid.substring(0, uuid.indexOf("-"));
                }

                usuarioMovelVO = new UsuarioMovelVO();
                usuarioMovelVO.setAtivo(true);
                usuarioMovelVO.setColaborador(colaboradorVO);
                usuarioMovelVO.setNome(username);
                usuarioMovelVO.setEmpresa(colaboradorVO.getEmpresa().getCodigo());
                usuarioMovelVO.setOrigem("TW");
                usuarioMovelVO.setSenha(senha);
                //Para caso gerado senha randomica a mesma seja atribuida ao corpo do e-mail enviado ao aluno.
                usuarioMovelDao.incluir(usuarioMovelVO);
            }

            TreinoWSConsumer.sincronizarUsuario(key, usuarioMovelVO.toUsuarioTreino());

            return senhaAlterada ? "Cadastro alterado com sucesso para " + username : "Cadastro realizado com sucesso para " + username;
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            if (ex.getMessage() != null
                    && ex.getMessage().contains("username_ukey")) {
                String detail = "";
                try {
                    detail = DaoAuxiliar.retornarAcessoControle(key).
                            getUsuarioMovelDao().obterUsuarioMesmoNome(username);
                } catch (Exception ignored) {
                }
                return "ERRO: Já existe um usuário com este email! " + detail;
            } else {
                return "ERRO: " + ex.getMessage();
            }
        }
    }
    @WebMethod(operationName = "enviarEmailAngular")
    public String enviarEmailAngular(@WebParam(name = "key") String key,
                                      @WebParam(name = "json") String json) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getIntegracaoCadastrosDao().enviarEmailAngular(json);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            return "ERRO:" + ex.getMessage();
        }
    }

    @WebMethod
    public String inserirWebHookClienteRD(
            @WebParam(name = "code") final String accessToken,
            @WebParam(name = "empresa")final int empresa,
            @WebParam(name = "key") final String chave,
            @WebParam(name = "idEventWebHook") final String idEventWebHook) {

        try {
            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            String url = "https://api.rd.services/integrations/webhooks";
            Map<String, String> heders = new HashMap<>();
            JSONObject jsonBody = new JSONObject();

            if (!UteisValidacao.emptyString(accessToken)) {
                heders.put("Authorization", "Bearer "+accessToken);
                String uuidWebHook = buscarIdWebHookAlterar(executeRequestHttpService, url, heders);

                jsonBody.put("entity_type", "CONTACT");
                jsonBody.put("event_type", idEventWebHook);
                jsonBody.put("url", PropsService.getPropertyValue(PropsService.urlZWAPI)+"/prest/lead/" + chave + "/cadastrarLeadRD?emp=" + empresa);
                jsonBody.put("http_method", "POST");
                String relations[] = {"COMPANY", "CONTACT_FUNNEL"};
                jsonBody.put("include_relations", relations);

                if(!UteisValidacao.emptyString(uuidWebHook)){
                    if (executeRequestHttpService.executeRequestPutWebHookRD(url+"/"+uuidWebHook, jsonBody.toString(), heders) == 200) {
                        return "SUCESS";
                    }else {
                        return "FAILED";
                    }
                }else{
                    if (executeRequestHttpService.executeRequestAddWebHookRD(url, jsonBody.toString(), heders) == 201) {
                        return "SUCESS";
                    }else {
                        return "FAILED";
                    }
                }

            }
            return "EXISTING";
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    private String buscarIdWebHookAlterar(ExecuteRequestHttpService executeRequestHttpService, String url, Map<String, String> heders) throws IOException {
        String webhooks = executeRequestHttpService.executeRequestGetWebHookRD(url, heders);
        JSONArray jsonArray = new JSONArray(new JSONObject(webhooks).get("webhooks").toString());
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject obj = (JSONObject) jsonArray.get(i);
            if(obj.getString("url").contains("/cadastrarLeadRD?")){
               return obj.getString("uuid");
            }
        }

        return "";
    }
}
