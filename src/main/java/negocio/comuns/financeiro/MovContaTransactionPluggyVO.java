package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created by <PERSON> on 19/07/2023.
 */

public class MovContaTransactionPluggyVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private MovContaVO movConta;
    private String idTransaction;

    private Date dataOperacao;
    private PluggyJaRecebidoZwVO pluggyJaRecebidoZw;
    @NaoControlarLogAlteracao
    private PluggyTransactionDTO pluggyTransactionDTO;

    public MovContaTransactionPluggyVO() {
    }

    public MovContaTransactionPluggyVO(PluggyTransactionDTO pluggyTransactionDTO) {
        if (!UteisValidacao.emptyNumber(pluggyTransactionDTO.getMovContaVO().getCodigo())) {
            this.movConta = pluggyTransactionDTO.getMovContaVO();
        }
        if (!UteisValidacao.emptyString(pluggyTransactionDTO.getId())) {
            this.idTransaction = pluggyTransactionDTO.getId();
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MovContaVO getMovConta() {
        if (movConta == null) {
            return new MovContaVO();
        }
        return movConta;
    }

    public void setMovConta(MovContaVO movConta) {
        this.movConta = movConta;
    }

    public String getIdTransaction() {
        if (UteisValidacao.emptyString(idTransaction)) {
            return "";
        }
        return idTransaction;
    }

    public void setIdTransaction(String idTransaction) {
        this.idTransaction = idTransaction;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getDataQuitacaoApresentarComHHMMSS() {
        if(dataOperacao == null){
            return "";
        }
        return Uteis.getDataComHHMMSS(getDataOperacao());
    }

    public PluggyJaRecebidoZwVO getPluggyJaRecebidoZw() {
        return pluggyJaRecebidoZw;
    }

    public void setPluggyJaRecebidoZw(PluggyJaRecebidoZwVO pluggyJaRecebidoZw) {
        this.pluggyJaRecebidoZw = pluggyJaRecebidoZw;
    }

    public PluggyTransactionDTO getPluggyTransactionDTO() {
        if (pluggyTransactionDTO == null) {
            return new PluggyTransactionDTO();
        }
        return pluggyTransactionDTO;
    }

    public void setPluggyTransactionDTO(PluggyTransactionDTO pluggyTransactionDTO) {
        this.pluggyTransactionDTO = pluggyTransactionDTO;
    }
}
