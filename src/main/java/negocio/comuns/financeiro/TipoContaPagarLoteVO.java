/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import negocio.comuns.arquitetura.SuperVO;

/**
 * <AUTHOR> 05/07/2024
 */
public class TipoContaPagarLoteVO extends SuperVO {

    private String descricao;
    private String uriImagem;
    private boolean selecionadoParaFiltrar;
    private TipoContaPagarLoteEnum tipoContaPagarLoteEnum;
    private int totalRegistrosElegiveis;
    private int totalRegistrosNaoElegiveis;

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUriImagem() {
        return uriImagem;
    }

    public void setUriImagem(String uriImagem) {
        this.uriImagem = uriImagem;
    }

    public boolean isSelecionadoParaFiltrar() {
        return selecionadoParaFiltrar;
    }

    public void setSelecionadoParaFiltrar(boolean selecionadoParaFiltrar) {
        this.selecionadoParaFiltrar = selecionadoParaFiltrar;
    }

    public TipoContaPagarLoteEnum getTipoContaPagarLoteEnum() {
        return tipoContaPagarLoteEnum;
    }

    public void setTipoContaPagarLoteEnum(TipoContaPagarLoteEnum tipoContaPagarLoteEnum) {
        this.tipoContaPagarLoteEnum = tipoContaPagarLoteEnum;
    }

    public boolean isPix() {
        return this.tipoContaPagarLoteEnum != null && this.tipoContaPagarLoteEnum == TipoContaPagarLoteEnum.PAYLOAD_PIX;
    }

    public boolean isBoleto() {
        return this.tipoContaPagarLoteEnum != null && this.tipoContaPagarLoteEnum == TipoContaPagarLoteEnum.BOLETO;
    }

    public boolean isBoletoConsumo() {
        return this.tipoContaPagarLoteEnum != null && this.tipoContaPagarLoteEnum == TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO;
    }

    public boolean isTransferencia() {
        return this.tipoContaPagarLoteEnum != null && this.tipoContaPagarLoteEnum == TipoContaPagarLoteEnum.TRANSFERENCIA;
    }

    public boolean isOutros() {
        return this.tipoContaPagarLoteEnum != null && this.tipoContaPagarLoteEnum == TipoContaPagarLoteEnum.OUTROS;
    }

    //Não apagar, está sendo usado no front
    public String getTitleTipoApresentar() {
        if (this.isPix()) {
            return "Exibir pagamentos através de Pix";
        } else if (this.isBoleto()) {
            return "Exibir pagamentos através de Boleto Bancário";
        } else if (this.isBoletoConsumo()) {
            return "Exibir pagamentos através de Boleto de contas de consumo";
        } else if (this.isTransferencia()) {
            return "Exibir pagamentos através de Transferência Bancária";
        } else if (this.isOutros()) {
            return "Exibir demais lançamentos";
        }
        return "";
    }

    public int getTotalRegistrosElegiveis() {
        return totalRegistrosElegiveis;
    }

    public void setTotalRegistrosElegiveis(int totalRegistrosElegiveis) {
        this.totalRegistrosElegiveis = totalRegistrosElegiveis;
    }

    public int getTotalRegistrosNaoElegiveis() {
        return totalRegistrosNaoElegiveis;
    }

    public void setTotalRegistrosNaoElegiveis(int totalRegistrosNaoElegiveis) {
        this.totalRegistrosNaoElegiveis = totalRegistrosNaoElegiveis;
    }
}
