package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.UnidadeMedidaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.VendaAvulsa;

import java.util.Date;

/**
 * Reponsável por manter os dados da entidade ItemVendaAvulsa. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see VendaAvulsa
 */
public class ItemVendaAvulsaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer vendaAvulsa;
    protected Integer quantidade;
    protected Integer quantidadeAgenda;
    protected Boolean usouQuantidadeAgenda;
    protected Double valorParcial;
    protected Date dataVenda;
    protected Date dataValidade;
    private String movpagamentos;
    @ChaveEstrangeira
    protected DescontoVO tabelaDesconto;
    @ChaveEstrangeira
    /** Atributo responsável por manter o objeto relacionado da classe <code>Produto</code>.*/
    private ProdutoVO produto;
    @ChaveEstrangeira
    private UsuarioVO responsavelAutorizacaoDesconto;
    @NaoControlarLogAlteracao
    private boolean descontoManual = false;
    private Double valorDescontoManual = 0.0;
    @NaoControlarLogAlteracao
    private boolean apresentarDescontoManual = false;
    @NaoControlarLogAlteracao
    private Double valorDescontoManualRec = 0.0;
    private PacoteVO pacoteVO;
    private Integer pacotePersonal;
    private Boolean taxaAgendada = false;
    private Boolean multaJuros = false;
    @NaoControlarLogAlteracao
    private VendaAvulsaVO vendaAvulsaVO;
    @NaoControlarLogAlteracao
    private boolean edicaoVendaAvulsa = false;
    @NaoControlarLogAlteracao
    private boolean somarItemAdicionar = false;
    @NaoControlarLogAlteracao
    private Boolean vigenciaJaCalculada = false;

    /**
     * Construtor padrão da classe <code>ItemVendaAvulsa</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ItemVendaAvulsaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ItemVendaAvulsaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ItemVendaAvulsaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getProduto() == null) || (obj.getProduto().getCodigo() == 0)) {
            throw new ConsistirException("O campo PRODUTO (Item Venda Avulsa) deve ser informado.");
        }
        if (obj.getValorParcial() < 0) {
            throw new ConsistirException("Valor Parcial de um Produto não pode ser negativo.");
        }
        if (obj.getQuantidade() <= 0){
            throw new ConsistirException("Informe uma quantidade para o produto.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
//        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
//            return;
//        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setQuantidade(1);
        setValorParcial(0.0);
        setTabelaDesconto(new DescontoVO());
        setProduto(new ProdutoVO());
        setUsouQuantidadeAgenda(Boolean.FALSE);
        setResponsavelAutorizacaoDesconto(new UsuarioVO());
        setPacoteVO(new PacoteVO());
    }

    /**
     * Retorna o objeto da classe <code>Produto</code> relacionado com (<code>ItemVendaAvulsa</code>).
     */
    public ProdutoVO getProduto() {
        if (produto == null) {
            produto = new ProdutoVO();
        }
        return (produto);
    }

    /**
     * Define o objeto da classe <code>Produto</code> relacionado com (<code>ItemVendaAvulsa</code>).
     */
    public void setProduto(ProdutoVO obj) {
        this.produto = obj;
    }

    public DescontoVO getTabelaDesconto() {
        return tabelaDesconto;
    }

    public void setTabelaDesconto(DescontoVO tabelaDesconto) {
        this.tabelaDesconto = tabelaDesconto;
    }

    public String getValorParcialApresentar() {
        return Formatador.formatarValorMonetario(getValorParcial());
    }

    public Double getValorParcial() {
        if (valorParcial == null) {
            valorParcial = 0.0;
        }
        if (!UteisValidacao.emptyNumber(pacotePersonal) || taxaAgendada || multaJuros) {
            return valorParcial;
        }
        if (tabelaDesconto.getCodigo() != 0 && tabelaDesconto.getTipoDesconto().equals(TipoDesconto.VA) && !descontoManual) {
            valorParcial = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((produto.getValorFinal() * quantidade) - tabelaDesconto.getValor());
        } else if (tabelaDesconto.getCodigo() != 0 && tabelaDesconto.getTipoDesconto().equals(TipoDesconto.PE) && !descontoManual) {
            valorParcial = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((produto.getValorFinal() * quantidade) - Uteis.arredondarForcando2CasasDecimaisMantendoSinal(((produto.getValorFinal() * quantidade) * (tabelaDesconto.getValor() / 100))));
        }
        if (tabelaDesconto.getCodigo() == 0 && !descontoManual && !produto.getArmario()) {
            valorParcial = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((produto.getValorFinal() * quantidade));
        }
        if (descontoManual && !apresentarDescontoManual) {
            valorParcial = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((produto.getValorFinal() * quantidade) - valorDescontoManual);
        }
        return valorParcial;
    }

    public void setValorParcial(Double valorParcial) {
        this.valorParcial = valorParcial;
    }

    public String getQuantidadeUnidadeMedida() {
        if (getProduto().getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
            Double quantidade = (getQuantidade().doubleValue() / 1000);
            return quantidade.toString();
        } else {
            return getQuantidade().toString();
        }
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getVendaAvulsa() {
        if (vendaAvulsa == null) {
            vendaAvulsa = 0;
        }
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public Date getDataVenda() {
        return dataVenda;
    }

    public void setDataVenda(Date dataVenda) {
        this.dataVenda = dataVenda;
    }

    public Integer getQuantidadeAgenda() {
        return quantidadeAgenda;
    }

    public void setQuantidadeAgenda(Integer quantidadeAgenda) {
        this.quantidadeAgenda = quantidadeAgenda;
    }

    public Boolean getUsouQuantidadeAgenda() {
        return usouQuantidadeAgenda;
    }

    public void setUsouQuantidadeAgenda(Boolean usouQuantidadeAgenda) {
        this.usouQuantidadeAgenda = usouQuantidadeAgenda;
    }

    public void setDescontoManual(boolean descontoManual) {
        this.descontoManual = descontoManual;
    }

    public boolean getDescontoManual() {
        return descontoManual;
    }

    public void setResponsavelAutorizacaoDesconto(UsuarioVO responsavelAutorizacaoDesconto) {
        this.responsavelAutorizacaoDesconto = responsavelAutorizacaoDesconto;
    }

    public UsuarioVO getResponsavelAutorizacaoDesconto() {
        return responsavelAutorizacaoDesconto;
    }

    public void setValorDescontoManual(Double valorDescontoManual) {
        this.valorDescontoManual = valorDescontoManual;
    }

    public Double getValorDescontoManual() {
        return valorDescontoManual;
    }

    public void setApresentarDescontoManual(boolean apresentarDescontoManual) {
        this.apresentarDescontoManual = apresentarDescontoManual;
    }

    public boolean getApresentarDescontoManual() {
        return apresentarDescontoManual;
    }

    public void setValorDescontoManualRec(Double valorDescontoManualRec) {
        this.valorDescontoManualRec = valorDescontoManualRec;
    }

    public Double getValorDescontoManualRec() {
        return valorDescontoManualRec;
    }

    public PacoteVO getPacoteVO() {
        if(pacoteVO==null){
            pacoteVO = new PacoteVO();
        }
        return pacoteVO;
    }

    public void setPacoteVO(PacoteVO pacoteVO) {
        this.pacoteVO = pacoteVO;
    }

	public void setMovpagamentos(String movpagamentos) {
		this.movpagamentos = movpagamentos;
	}

	public String getMovpagamentos() {
		return movpagamentos;
	}

    public Integer getPacotePersonal() {
        if(pacotePersonal == null){
            pacotePersonal = 0;
        }
        return pacotePersonal;
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    public Boolean getTaxaAgendada() {
        return taxaAgendada;
    }

    public void setTaxaAgendada(Boolean taxaAgendada) {
        this.taxaAgendada = taxaAgendada;
    }

    public double getValorFinalItem() {
        if (taxaAgendada) {
            return getValorParcial();
        }
        return getProduto().getValorFinal();
    }

    public Boolean getMultaJuros() {
        return multaJuros;
    }

    public void setMultaJuros(Boolean multaJuros) {
        this.multaJuros = multaJuros;
    }


    public boolean isEdicaoVendaAvulsa() {
        return edicaoVendaAvulsa;
    }

    public void setEdicaoVendaAvulsa(boolean edicaoVendaAvulsa) {
        this.edicaoVendaAvulsa = edicaoVendaAvulsa;
    }

    public boolean isSomarItemAdicionar() {
        return somarItemAdicionar;
    }

    public void setSomarItemAdicionar(boolean somarItemAdicionar) {
        this.somarItemAdicionar = somarItemAdicionar;
    }

    public Boolean getVigenciaJaCalculada() {
        return vigenciaJaCalculada;
    }

    public void setVigenciaJaCalculada(Boolean vigenciaJaCalculada) {
        this.vigenciaJaCalculada = vigenciaJaCalculada;
    }

    public void setPacotePersonal(Integer pacotePersonal) {
        this.pacotePersonal = pacotePersonal;
    }
}
