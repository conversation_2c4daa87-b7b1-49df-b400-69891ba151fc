package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.integracao.conciliadora.StatusPagamentoConciliadora;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.MovPagamento;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * Reponsável por manter os dados da entidade MovPagamento. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class MovPagamentoVO extends SuperVO implements Cloneable {

    @ChavePrimaria
    protected Integer codigo;
    protected Date dataPagamento;
    protected Date dataLancamento;
    protected Date dataQuitacao;
    protected Double valor;
    protected Double valorTotal;
    protected Boolean credito;
    protected String creditoApresentar;
    protected Integer nrParcelaCartaoCredito;
    protected String saldoContaCorrenteCliente;
    protected String nomePagador;
    private String nomeAlunosDaParcela;
    protected String responsavelPagador;
    protected String tipoPagador;
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenio;
    @ChaveEstrangeira
    protected OperadoraCartaoVO operadoraCartaoVO;
    @ChaveEstrangeira
    protected AdquirenteVO adquirenteVO;
    @NaoControlarLogAlteracao
    private List<PagamentoMovParcelaVO> pagamentoMovParcelaVOs;
    @Lista
    private List<ChequeVO> chequeVOs;
    @Lista
    private List<CartaoCreditoVO> cartaoCreditoVOs;
    @NaoControlarLogAlteracao
    protected PessoaVO pessoa;
    @NaoControlarLogAlteracao
    protected UsuarioVO responsavelPagamento;
    @NaoControlarLogAlteracao
    protected ReciboPagamentoVO reciboPagamento;
    @NaoControlarLogAlteracao
    private boolean pagamentoAvulso = false;
    @ChaveEstrangeira
    protected FormaPagamentoVO formaPagamento;
    protected Boolean opcaoPagamentoCheque;
    protected Boolean opcaoPagamentoCartaoCredito;
    protected Boolean opcaoPagamentoCartaoDebito;
    protected Boolean opcaoPagamentoDinheiro;
    protected Boolean opcaoPagamentoBoleto;
    protected Boolean opcaoPagamentoContaCorrenteCliente;
    protected Boolean mostraContaCorrenteAcademia;
    protected Boolean movPagamentoEscolhida;
    protected Boolean apresentarCampoCPF;
    protected Boolean apresentarCampoCNPJ;
    protected ChequeVO chequeTransiente;//existe apenas enquanto emitindo recibo
    private Double valorReceberOuDevolverContaCorrente = 0.0;
    private Boolean usarPagamentoDigital = false;
    private Boolean usarPagamentoAprovaFacil = false;
    private Boolean usarPagamentoDebitoOnline = false;
    private Date dataAlteracaoManual;
    private EmpresaVO empresa;
    private String observacao = "";
    @NaoControlarLogAlteracao
    private boolean movPagamentoEscolhidaFinan = false;
    private String autorizacaoCartao;
    private String nsu;
    private Integer movPagamentoOrigemCredito;
    @NaoControlarLogAlteracao
    private Date dataPrevistaDeposito = null;
    @NaoControlarLogAlteracao
    private String produtosPagos = "";
    @NaoControlarLogAlteracao
    private String produtosPagosCancelados = "";
    private Integer movconta = null;
    @NaoControlarLogAlteracao
    private String matriculaPagador = "";
    @NaoControlarLogAlteracao //campo para usar no CE
    private Integer nrCheques;
    private Boolean cupomEmitido = false;
    private Boolean nfseEmitido = false;
    private boolean nfceEmitido = false;
    @NaoControlarLogAlteracao
    private String contaFinanceiro = "";
    @NaoControlarLogAlteracao
    private Date dataMovimento;
    @NaoControlarLogAlteracao
    private int id_recebe;
    @NaoControlarLogAlteracao
    private List<ChequeVO> chequesCanceladoVOs;
    @NaoControlarLogAlteracao
    private List<CartaoCreditoVO> cartoesCanceladoVOs;
    @NaoControlarLogAlteracao
    private String cpfPagador;
    private List<CartaoCreditoVO> listaCartaoExcluirVinculoComNFSe;
    private List<ChequeVO> listaChequeExcluirVinculoComNFSe;
    private Boolean depositoCC = false; // Identificar se a origem do movPagamento foi um depósito realizado na Conta Corrente.
    @NaoControlarLogAlteracao
    private String dataAux = "";
    @NaoControlarLogAlteracao
    private Integer tipoParcelamento = 2;
    @NaoControlarLogAlteracao
    private Boolean opcaoPagamentoCartaoDebitoOnline = false;
    private Date dataPagamentoOriginal;
    @NaoControlarLogAlteracao
    private Double valorPP = 0.0; // usado nos calculo dos produtos pagos
    @NaoControlarLogAlteracao
    private Double valorPPCancelado = 0.0; // usado nos calculo dos produtos pagos
    private List<CentroCustoTO> listaCentroCusto;
    @NaoControlarLogAlteracao
    private boolean pagamentoAberto = false;
    @NaoControlarLogAlteracao
    private Date dataCobrancaTransacao; // atributo transient
    @NaoControlarLogAlteracao
    private Double valorPagaContrato = 0.0; // usado nos calculos de cancelamento parcial de cartões
    @NaoControlarLogAlteracao
    private Double valorEstornado = 0.0; // usado nos calculos de cancelamento parcial de cartões
    private String parcelasPagas; // usado gestão de recebíveis
    private boolean opcaoPagamentoParceiroFidelidade = false;
    private boolean usarParceiroFidelidade = false;
    private List<SelectItem> listaSelectItemTabelaParceiroFidelidade;
    private TabelaParceiroFidelidadeVO tabelaParceiroFidelidadeVO;
    private Integer pontosParceiroFidelidade;
    private String cpfParceiroFidelidade;
    private TipoPontoParceiroFidelidadeEnum tipoPontoParceiroFidelidade = TipoPontoParceiroFidelidadeEnum.NENHUM;
    private String msgParceiroFidelidade; //usado em tela
    private Double multiplicadorParceiroFidelidade;
    private boolean parceiroFidelidadeProcessado = false;
    private String senhaParceiroFidelidade;
    private List<ProdutoParceiroFidelidadeVO> produtosParceiroFidelidade;
    private List<SelectItem> selectItemsProdutosParceiroFidelidade;
    private String codigoExternoProdutoParceiroFidelidade;
    private String numeroUnicoTransacao;
    private String respostaRequisicaoPinpad;
    @NaoControlarLogAlteracao
    private String tipoParcelamentoStone;
    @NaoControlarLogAlteracao
    private StatusPagamentoConciliadora statusConciliadora = StatusPagamentoConciliadora.NENHUM;
    private String nomeOperadoraCartao;
    @NaoControlarLogAlteracao
    private boolean apresentarPagamentoOnline = false;
    @NaoControlarLogAlteracao
    private boolean validarOperadora = true;
    @NaoControlarLogAlteracao
    private boolean validarNrVezes = true;
    @NaoControlarLogAlteracao
    private boolean exibirDataCreditoBoleto = false;
    private boolean enviadoConciliadora = false;
    @NaoControlarLogAlteracao
    private ContratoVO contratoVO;
    @NaoControlarLogAlteracao
    private String nomeResponsavelRecibo;
    @NaoControlarLogAlteracao //apenas para debito
    private boolean antecipacao;
    @NaoControlarLogAlteracao //apenas para debito
    private Date dataPgtoOriginalAntesDaAntecipacao;
    @NaoControlarLogAlteracao //apenas para debito
    private Double taxaAntecipacao;
    private String documentoIntegracaoSesi = "";
    @NaoControlarLogAlteracao
    private PessoaVO pessoaVODoContratoDoMovPagamento;
    @NaoControlarLogAlteracao
    private int codMatriculaDoContratoDoMovPagamento;
    @NaoControlarLogAlteracao //apenas para debito
    private ClienteVO clienteVO;


    /**
     * Construtor padrão da classe <code>MovPagamento</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public MovPagamentoVO() {
        super();
        inicializarDados();
    }
  
    public Object getClone(MovPagamentoVO mov) throws CloneNotSupportedException {
        return (MovPagamentoVO) mov.clone();
    }

    public static JSONArray toJSON(List<MovPagamentoVO> movPagamentoVOS){
        JSONArray jsonArray = new JSONArray();
        for (MovPagamentoVO movPagamentoVO: movPagamentoVOS) {
            jsonArray.put(movPagamentoVO.toJSON());
        }
        return jsonArray;
    }

    public JSONObject toJSON(){
        JSONObject json =  new JSONObject();
        json.put("codigo", codigo);
        json.put("dataPagamento", dataPagamento);
        json.put("dataLancamento", dataLancamento);
        json.put("valor", valor);
        json.put("nrParcelaCartaoCredito", nrParcelaCartaoCredito);
        json.put("nomePagador", nomePagador);
        json.put("adquirenteVO", adquirenteVO == null || adquirenteVO.getCodigo().equals(0) ? null : adquirenteVO.toJSON());
        json.put("formaPagamento", getFormaPagamento().getCodigo().equals(0) ? null : getFormaPagamento().toJSON());
        json.put("operadoraCartao", getOperadoraCartaoVO().getCodigo().equals(0) ? null : getOperadoraCartaoVO().toJSON());
        json.put("parcelas", toJSONPagamentoMovParcelas(pagamentoMovParcelaVOs));
        json.put("autorizacaoCartao", autorizacaoCartao);
        json.put("produtosPagos", produtosPagos);
        json.put("cpfPagador", cpfPagador);


        return json;
    }

    private JSONArray toJSONPagamentoMovParcelas(List<PagamentoMovParcelaVO> pagamentoMovParcelaVOs) {
        JSONArray jsonArray = new JSONArray();
        for(PagamentoMovParcelaVO pagamentoMovParcelaVO:pagamentoMovParcelaVOs){
            jsonArray.put(pagamentoMovParcelaVO.toJSON());
        }

        return jsonArray;
    }

    public String getPagamento_Apresentar() {
        return getFormaPagamento().getDescricao();
    }
    public String getEmpresa_Apresentar(){
        return getEmpresa().getNome();
    }
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovPagamentoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(MovPagamentoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Empresa deve ser informada."); // campo novo
        }
        if (obj.getDataPagamento() == null) {
            throw new ConsistirException("O campo DATA PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getDataLancamento() == null) {
            throw new ConsistirException("O campo DATA LANÇAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if ((obj.getFormaPagamento() == null)
                || (obj.getFormaPagamento().getCodigo() == 0)) {
            throw new ConsistirException("O campo FORMA PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if ((obj.getResponsavelPagamento() == null)
                || (obj.getResponsavelPagamento().getCodigo() == 0)) {
            throw new ConsistirException("O campo RESPONSÁVEL pelo PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getNomePagador().equals("")) {
            throw new ConsistirException("O campo NOME DO PAGADOR (Movimento do Pagamento) deve ser informado.");
        }

        if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getNrParcelaCartaoCredito() == 0) {
            throw new ConsistirException("O campo PARCELAS  do CARTÃO CRÉDITO (Movimento do Pagamento) deve ser informado.");

        }
        if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
            Iterator i = obj.getChequeVOs().iterator();
            while (i.hasNext()) {
                ChequeVO novoCheque = (ChequeVO) i.next();
                ChequeVO.validarDados(novoCheque);
            }
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovPagamentoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDadosEspecial(MovPagamentoVO obj, Double valorResiduo,
                                            OpcoesPinpadEnum opcoesPinpadEnum) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        boolean cappta = obj.getFormaPagamento().getPinpad().isCAPPTA();

        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Empresa deve ser informada."); // campo novo
        }
        if (obj.getDataPagamento() == null) {
            throw new ConsistirException("O campo DATA PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getDataLancamento() == null) {
            throw new ConsistirException("O campo DATA LANÇAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if ((obj.getFormaPagamento() == null)
                || (obj.getFormaPagamento().getCodigo() == 0)) {
            throw new ConsistirException("O campo FORMA PAGAMENTO (Movimento do Pagamento) deve ser informado.");
        }
        if (obj.getNomePagador().equals("")) {
            throw new ConsistirException("O campo NOME DO PAGADOR (Movimento do Pagamento) deve ser informado.");
        }
        if ((obj.getPessoa() == null
                || obj.getPessoa().getCodigo() == 0)
                && obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")
                && (valorResiduo < 0)) {
            throw new ConsistirException("O Pagamento com cheque para um (CONSUMIDOR) o campo RESÍDUO não pode ficar NEGATIVO.");
        }

        if (opcoesPinpadEnum == null || !opcoesPinpadEnum.equals(OpcoesPinpadEnum.GETCARD)) {

            if ((opcoesPinpadEnum == null || !opcoesPinpadEnum.equals(OpcoesPinpadEnum.STONE_CONNECT)) &&
                    !cappta && (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA") || obj.getFormaPagamento().getTipoFormaPagamento().equals("CD"))) {
                if (obj.isValidarOperadora() && (obj.getOperadoraCartaoVO() == null || obj.getOperadoraCartaoVO().getCodigo() == 0)) {
                    throw new ConsistirException("O campo OPERADORA de CARTÃO (Movimento do Pagamento) deve ser informado.");
                }
            }

            if ((opcoesPinpadEnum == null || !opcoesPinpadEnum.equals(OpcoesPinpadEnum.STONE_CONNECT)) &&
                    !cappta && obj.isValidarNrVezes() &&
                    obj.getFormaPagamento().getTipoFormaPagamento().equals("CA") &&
                    obj.getNrParcelaCartaoCredito() == 0) {
                throw new ConsistirException("O campo PARCELAS  do CARTÃO CREDITO (Movimento do Pagamento) deve ser informado.");
            }
        }

        if (opcoesPinpadEnum == null &&
                !cappta && (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA") || obj.getFormaPagamento().getTipoFormaPagamento().equals("CD"))) {
            if (obj.getEmpresa().isObrigatorioPreencherCamposCartao() && UteisValidacao.emptyString(obj.getAutorizacaoCartao())) {
                throw new ConsistirException("O campo AUTORIZAO (Movimento do Pagamento) deve ser informado.");
            }
        }

        if (opcoesPinpadEnum == null &&
                !cappta && (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA") || obj.getFormaPagamento().getTipoFormaPagamento().equals("CD"))) {
            if (obj.getEmpresa().isObrigatorioPreencherCamposCartao() && obj.getFormaPagamento().isApresentarNSU() && UteisValidacao.emptyString(obj.getNsu())) {
                throw new ConsistirException("O campo NSU (Movimento do Pagamento) deve ser informado.");
            }
        }

        if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
            Iterator i = obj.getChequeVOs().iterator();
            while (i.hasNext()) {
                ChequeVO novoCheque = (ChequeVO) i.next();
                ChequeVO.validarDados(novoCheque);
            }
            for(int l = 0; l < (obj.getChequeVOs().size() -1); l++){
                for(int k = l+1; k < obj.getChequeVOs().size(); k++  ){
                    if(obj.getChequeVOs().get(l).getNumero().equals(obj.getChequeVOs().get(k).getNumero())
                            && obj.getChequeVOs().get(l).getAgencia().equals(obj.getChequeVOs().get(k).getAgencia())
                            && obj.getChequeVOs().get(l).getBanco().getCodigo().equals(obj.getChequeVOs().get(k).getBanco().getCodigo())
                            && obj.getChequeVOs().get(l).getConta().equals(obj.getChequeVOs().get(k).getConta())){
                          throw new ConsistirException("Dois ou mais cheques apresentam os mesmos dados. São os cheques de número: "+obj.getChequeVOs().get(l).getNumero()
                                  +".\n Não é possível adicionar cheques com informações iguais, corrija os dados desses cheques antes de confirmar o pagamento.");


                    }
                }
            }
        }

    }

    public void realizarUpperCaseDados() {
        setNomePagador(getNomePagador().toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(0);
        setDataPagamento(Calendario.hoje());
        setDataLancamento(Calendario.hoje());
        setDataQuitacao(Calendario.hoje());
        setNomePagador("");
        setTipoPagador("");
        setNrParcelaCartaoCredito(0);
        setValor(0.0);
        setValorTotal(0.0);
        setSaldoContaCorrenteCliente("");
        setPagamentoMovParcelaVOs(new ArrayList<>());
        setChequeVOs(new ArrayList<>());
        setOpcaoPagamentoCheque(false);
        setOpcaoPagamentoCartaoCredito(false);
        setOpcaoPagamentoCartaoDebito(false);
        setOpcaoPagamentoDinheiro(false);
        setOpcaoPagamentoBoleto(false);
        setOpcaoPagamentoContaCorrenteCliente(false);
        setMovPagamentoEscolhida(false);
        setMostraContaCorrenteAcademia(true);
        setResponsavelPagamento(new UsuarioVO());
        setReciboPagamento(new ReciboPagamentoVO());
        setApresentarCampoCPF(false);
        setApresentarCampoCNPJ(false);
        setNsu("");
        setAutorizacaoCartao("");
        setCredito(false);
        setMovPagamentoOrigemCredito(0);
    }

    public void preencherDataDeAcordoComTipo() {
        if (getFormaPagamento().getTipoFormaPagamento().equals("CD")) {
            setDataPagamento(Uteis.obterDataFutura2(getDataPagamento(), getEmpresa().getNrDiasCompensacao()));
        }
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>PagamentoMovParcelaVO</code>
     * ao List <code>pagamentoMovParcelaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>PagamentoMovParcela</code> - getMovParcela().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>PagamentoMovParcelaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjPagamentoMovParcelaVOs(PagamentoMovParcelaVO obj, Connection connection) throws Exception {
        PagamentoMovParcelaVO.validarDadosComConexao(obj, connection);
        int index = 0;
        Iterator i = getPagamentoMovParcelaVOs().iterator();
        while (i.hasNext()) {
            PagamentoMovParcelaVO objExistente = (PagamentoMovParcelaVO) i.next();
            if (objExistente.getMovParcela().getCodigo().equals(obj.getMovParcela().getCodigo())) {
                getPagamentoMovParcelaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPagamentoMovParcelaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>PagamentoMovParcelaVO</code>
     * no List <code>pagamentoMovParcelaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>PagamentoMovParcela</code> - getMovParcela().getCodigo() - como identificador (key) do objeto no List.
     * @param movParcela  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjPagamentoMovParcelaVOs(Integer movParcela) throws Exception {
        int index = 0;
        Iterator i = getPagamentoMovParcelaVOs().iterator();
        while (i.hasNext()) {
            PagamentoMovParcelaVO objExistente = (PagamentoMovParcelaVO) i.next();
            if (objExistente.getMovParcela().getCodigo().equals(movParcela)) {
                getPagamentoMovParcelaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>PagamentoMovParcelaVO</code>
     * no List <code>pagamentoMovParcelaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>PagamentoMovParcela</code> - getMovParcela().getCodigo() - como identificador (key) do objeto no List.
     * @param movParcela  Parâmetro para localizar o objeto do List.
     */
    public PagamentoMovParcelaVO consultarObjPagamentoMovParcelaVO(Integer movParcela) throws Exception {
        Iterator i = getPagamentoMovParcelaVOs().iterator();
        while (i.hasNext()) {
            PagamentoMovParcelaVO objExistente = (PagamentoMovParcelaVO) i.next();
            if (objExistente.getMovParcela().getCodigo().equals(movParcela)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ChequeVO</code>
     * ao List <code>ChequeVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>Cheque</code> - getMovParcela().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>ChequeVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjChequeVOs(ChequeVO obj) throws Exception {
        ChequeVO.validarDados(obj);
        int index = 0;
        Iterator i = getChequeVOs().iterator();
        while (i.hasNext()) {
            ChequeVO objExistente = (ChequeVO) i.next();
            if (objExistente.getNumero().equals(obj.getNumero())) {
                getChequeVOs().set(index, obj);
                return;
            }
            index++;
        }
        getChequeVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>ChequeVO</code>
     * no List <code>ChequeVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>Cheque</code> - getMovParcela().getCodigo() - como identificador (key) do objeto no List.
     * @param marcador  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjChequeVOs(Integer marcador) throws Exception {
        int index = 0;
        Iterator i = getChequeVOs().iterator();
        while (i.hasNext()) {
            ChequeVO objExistente = (ChequeVO) i.next();
            if (objExistente.getMarcador().equals(marcador)) {
                getChequeVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>ChequeVO</code>
     * no List <code>ChequeVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>Cheque</code> - getMovParcela().getCodigo() - como identificador (key) do objeto no List.
     * @param numeroChque  Parâmetro para localizar o objeto do List.
     */
    public ChequeVO consultarObjChequeVO(String numeroChque) throws Exception {
        Iterator i = getChequeVOs().iterator();
        while (i.hasNext()) {
            ChequeVO objExistente = (ChequeVO) i.next();
            if (objExistente.getNumero().equals(numeroChque)) {
                return objExistente;
            }
        }
        return null;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO obj) {
        this.formaPagamento = obj;
    }

    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return pessoa;
    }

    public void setPessoa(PessoaVO obj) {
        this.pessoa = obj;
    }

    public List<PagamentoMovParcelaVO> getPagamentoMovParcelaVOs() {
        return (pagamentoMovParcelaVOs);
    }

    public void setPagamentoMovParcelaVOs(List pagamentoMovParcelaVOs) {
        this.pagamentoMovParcelaVOs = pagamentoMovParcelaVOs;
    }

    public String getVencimentosParcelas(){
        String vencimentos = "";
        for (PagamentoMovParcelaVO pagamentoMovParcela: getPagamentoMovParcelaVOs()) {
            if(pagamentoMovParcela.getMovParcela() != null){
                vencimentos += pagamentoMovParcela.getMovParcela().getDataVencimento_Apresentar() + ", ";
            }
        }

        return Uteis.removerUltimosCaracteres(vencimentos, 2);
    }

    public String getNumerosParcelas(){
        String numeros = "";
        for (PagamentoMovParcelaVO pagamentoMovParcela: getPagamentoMovParcelaVOs()) {
            if(pagamentoMovParcela.getMovParcela() != null && pagamentoMovParcela.getMovParcela().getNumeroParcela() != null){
                numeros += Integer.toString(pagamentoMovParcela.getMovParcela().getNumeroParcela()) + ", ";
            }
        }

        return Uteis.removerUltimosCaracteres(numeros, 2);
    }

    public String getCodigosParcelas(){
        String numeros = "";
        for (PagamentoMovParcelaVO pagamentoMovParcela: getPagamentoMovParcelaVOs()) {
            if(pagamentoMovParcela.getMovParcela() != null){
                numeros += Integer.toString(pagamentoMovParcela.getMovParcela().getCodigo()) + ", ";
            }
        }

        return Uteis.removerUltimosCaracteres(numeros, 2);
    }

    public OperadoraCartaoVO getOperadoraCartaoVO() {
        if (operadoraCartaoVO == null) {
            operadoraCartaoVO = new OperadoraCartaoVO();
        }
        return operadoraCartaoVO;
    }

    public void setOperadoraCartaoVO(OperadoraCartaoVO operadoraCartaoVO) {
        this.operadoraCartaoVO = operadoraCartaoVO;
    }

    public String getNomePagador() {
        if (nomePagador == null) {
            nomePagador = "";
        }
        return (nomePagador);
    }
    
    public String getNomePagadorMin() {
        return getNomePagador().toLowerCase();
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public Date getDataLancamento() {
        return (dataLancamento);
    }

    public String getDataLancamento_Apresentar() {
        return (Uteis.getDataComHora(dataLancamento));
    }

    public String getDataLancamentoSemHora_Apresentar() {
        return (Uteis.getData(dataLancamento));
    }

    public String getDataLancamentoSemHoraYYYYMMDD() {
        return (Uteis.getDataAplicandoFormatacao(dataLancamento, "yyyyMMdd"));
    }

    public String getDataLancamentoOrdenacao() {
        return Uteis.getDataAplicandoFormatacao(dataLancamento, "yyyyMMddHHMM");
    }

    public String getDataAlteracaoManualSemHora_Apresentar() {
        return (Uteis.getData(dataAlteracaoManual));
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataPagamento() {
        return (dataPagamento);
    }

    public String getDataPagamento_Apresentar() {
        return (Uteis.getDataComHora(dataPagamento));
    }

    public String getDataQuitacaoYYYYMMDD() {
        return (Uteis.getDataAplicandoFormatacao(dataQuitacao, "yyyyMMdd"));
    }

    public String getDataPagamentoYYYYMMDD() {
        return (Uteis.getDataAplicandoFormatacao(dataPagamento, "yyyyMMdd"));
    }

    public String getDataPagamentoOrdenacao() {
        return (Uteis.getDataAplicandoFormatacao(dataPagamento, "yyyyMMddHHmm"));
    }

    public String getDataPagamentoSemHora_Apresentar() {
        return (Uteis.getData(dataPagamento));
    }

    public String getDataQuitacaoSemHora_Apresentar() {
        return (Uteis.getData(dataQuitacao));
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getOpcaoPagamentoCartaoCredito() {
        return opcaoPagamentoCartaoCredito;
    }

    public void setOpcaoPagamentoCartaoCredito(Boolean opcaoPagamentoCartaoCredito) {
        this.opcaoPagamentoCartaoCredito = opcaoPagamentoCartaoCredito;
    }

    public Boolean getOpcaoPagamentoCartaoDebito() {
        return opcaoPagamentoCartaoDebito;
    }

    public void setOpcaoPagamentoCartaoDebito(Boolean opcaoPagamentoCartaoDebito) {
        this.opcaoPagamentoCartaoDebito = opcaoPagamentoCartaoDebito;
    }

    public Boolean getOpcaoPagamentoBoleto() {
        return opcaoPagamentoBoleto;
    }

    public void setOpcaoPagamentoBoleto(Boolean opcaoPagamentoBoleto) {
        this.opcaoPagamentoBoleto = opcaoPagamentoBoleto;
    }

    public Boolean getOpcaoPagamentoContaCorrenteCliente() {
        return opcaoPagamentoContaCorrenteCliente;
    }

    public void setOpcaoPagamentoContaCorrenteCliente(Boolean opcaoPagamentoContaCorrenteCliente) {
        this.opcaoPagamentoContaCorrenteCliente = opcaoPagamentoContaCorrenteCliente;
    }

    public Boolean getOpcaoPagamentoDinheiro() {
        return opcaoPagamentoDinheiro;
    }

    public void setOpcaoPagamentoDinheiro(Boolean opcaoPagamentoDinheiro) {
        this.opcaoPagamentoDinheiro = opcaoPagamentoDinheiro;
    }

    public Boolean getOpcaoPagamentoCheque() {
        return opcaoPagamentoCheque;
    }

    public void setOpcaoPagamentoCheque(Boolean opcaoPagamentoCheque) {
        this.opcaoPagamentoCheque = opcaoPagamentoCheque;
    }

    public Double getValor() {
        return valor;
    }

    public String getValorOrdenacao(){
        return this.nomePagador + Uteis.adicionarValorEsquerda("0", String.valueOf(Double.valueOf(this.valor * 100).intValue()), 10);
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValorTotal() {
        return valorTotal;
    }
    public String getValorTotal_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorTotal);
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Boolean getCredito() {
        if (credito == null) {
            credito = false;
        }
        return credito;
    }

    public void setCredito(Boolean credito) {
        this.credito = credito;
        if (this.credito) {
            setCreditoApresentar(" (CCC)");
        } else {
            setCreditoApresentar("");
        }
    }

    public String getCreditoApresentar() {
        return creditoApresentar;
    }

    public void setCreditoApresentar(String creditoApresentar) {
        this.creditoApresentar = creditoApresentar;
    }

    public String getSaldoContaCorrenteCliente() {
        if (saldoContaCorrenteCliente == null) {
            saldoContaCorrenteCliente = "";
        }
        return saldoContaCorrenteCliente;
    }

    public void setSaldoContaCorrenteCliente(String saldoContaCorrenteCliente) {
        this.saldoContaCorrenteCliente = saldoContaCorrenteCliente;
    }

    public List<ChequeVO> getChequeVOs() {
        return chequeVOs;
    }

    public void setChequeVOs(List<ChequeVO> chequeVOs) {
        this.chequeVOs = chequeVOs;
    }

    public Integer getNrParcelaCartaoCredito() {
        return nrParcelaCartaoCredito;
    }

    public void setNrParcelaCartaoCredito(Integer nrParcelaCartaoCredito) {
        this.nrParcelaCartaoCredito = nrParcelaCartaoCredito;
    }

    public Boolean getMovPagamentoEscolhida() {
        return movPagamentoEscolhida;
    }

    public void setMovPagamentoEscolhida(Boolean movPagamentoEscolhida) {
        this.movPagamentoEscolhida = movPagamentoEscolhida;
    }

    public UsuarioVO getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(UsuarioVO responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Boolean getMostraContaCorrenteAcademia() {
        return mostraContaCorrenteAcademia;
    }

    public void setMostraContaCorrenteAcademia(Boolean mostraContaCorrenteAcademia) {
        this.mostraContaCorrenteAcademia = mostraContaCorrenteAcademia;
    }

    public JRDataSource getListaCheque() {
        return new JRBeanArrayDataSource(getChequeVOs().toArray());
    }

    public Boolean getApresentarCampoCPF() {
        return apresentarCampoCPF;
    }

    public void setApresentarCampoCPF(Boolean apresentarCampoCPF) {
        this.apresentarCampoCPF = apresentarCampoCPF;
    }

    public Boolean getApresentarCampoCNPJ() {
        return apresentarCampoCNPJ;
    }

    public void setApresentarCampoCNPJ(Boolean apresentarCampoCNPJ) {
        this.apresentarCampoCNPJ = apresentarCampoCNPJ;
    }

    public String getTipoPagador() {
        return tipoPagador;
    }

    public void setTipoPagador(String tipoPagador) {
        this.tipoPagador = tipoPagador;
    }

    public List<CartaoCreditoVO> getCartaoCreditoVOs() {
        if (cartaoCreditoVOs == null) {
            setCartaoCreditoVOs(new ArrayList<CartaoCreditoVO>());
        }
        return cartaoCreditoVOs;
    }

    public void setCartaoCreditoVOs(List<CartaoCreditoVO> cartaoCreditoVOs) {
        this.cartaoCreditoVOs = cartaoCreditoVOs;
    }

    public Boolean getNaoApresentarContaCorrente() {
        return !getFormaPagamento().getTipoFormaPagamento().equals("CC");
    }

    public static void adicionarChequesEmListaMovPagamento(List<MovPagamentoVO> movPagamentoVOsRef,
            List<ChequeVO> chequeVOs) {
        Iterator i = chequeVOs.iterator();
        while (i.hasNext()) {
            ChequeVO chequeVO = (ChequeVO) i.next();
            //obtém o cheque da lista e cria um novo MovPagamentoVO
            //com as informações disponíveis: valor e forma de pagamento
            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setValor(chequeVO.getValor());
            FormaPagamentoVO frmPagamento = new FormaPagamentoVO();
            frmPagamento.setTipoFormaPagamento("CH");
            movPagamentoVO.setFormaPagamento(frmPagamento);
            movPagamentoVO.setChequeTransiente(chequeVO);
            movPagamentoVOsRef.add(movPagamentoVO);
        }

    }

    public void setChequeTransiente(ChequeVO chequeTransiente) {
        this.chequeTransiente = chequeTransiente;
    }

    public ChequeVO getChequeTransiente() {
        return chequeTransiente;
    }

    public void setValorReceberOuDevolverContaCorrente(Double valorReceberOuDevolverContaCorrente) {
        if (valorReceberOuDevolverContaCorrente != null) {
            this.valorReceberOuDevolverContaCorrente =
                    Uteis.arredondarForcando2CasasDecimaisMantendoSinal(
                    valorReceberOuDevolverContaCorrente);
        } else {
            this.valorReceberOuDevolverContaCorrente = valorReceberOuDevolverContaCorrente;
        }
    }

    public Double getValorReceberOuDevolverContaCorrente() {
        return valorReceberOuDevolverContaCorrente;
    }

    public void setUsarPagamentoDigital(Boolean usarPagamentoDigital) {
        this.usarPagamentoDigital = usarPagamentoDigital;
    }

    public Boolean getUsarPagamentoDigital() {
        return usarPagamentoDigital;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public String getDataQuitacao_Apresentar() {
        return Uteis.getData(dataQuitacao);
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public ConvenioCobrancaVO getConvenio() {
        if (convenio == null) {
            convenio = new ConvenioCobrancaVO();
        }
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public String getDataAlteracaoManual_Apresentar() {
        return Uteis.getData(dataAlteracaoManual);
    }

    public Boolean getUsarPagamentoAprovaFacil() {
        return usarPagamentoAprovaFacil;
    }

    public void setUsarPagamentoAprovaFacil(Boolean usarPagamentoAprovaFacil) {
        this.usarPagamentoAprovaFacil = usarPagamentoAprovaFacil;
    }
    public String getNomeOperadorCartaoApresentar(){
        return getOperadoraCartaoVO().getDescricao();
    }
    public String getNomeMinOperadorCartaoApresentar(){
        return getOperadoraCartaoVO().getDescricaoMin();
    }
    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public void setMovPagamentoEscolhidaFinan(boolean movPagamentoEscolhidaFinan) {
        this.movPagamentoEscolhidaFinan = movPagamentoEscolhidaFinan;
    }

    public boolean getMovPagamentoEscolhidaFinan() {
        return movPagamentoEscolhidaFinan;
    }

    public void setAutorizacaoCartao(String autorizacaoCartao) {
        this.autorizacaoCartao = autorizacaoCartao;
    }

    public String getAutorizacaoCartao() {
        if (autorizacaoCartao == null) {
            autorizacaoCartao = "";
        }
        return autorizacaoCartao;
    }

    public Date getDataPrevistaDeposito() {
        return dataPrevistaDeposito;
    }

    public void setDataPrevistaDeposito(Date dataPrevistaDeposito) {
        this.dataPrevistaDeposito = dataPrevistaDeposito;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public String getProdutosPagos() {
        if (produtosPagos == null) {
            produtosPagos = "";
        }
        return produtosPagos;
    }

    public String getProdutosPagosCancelados() {
        return produtosPagosCancelados;
    }

    public void setProdutosPagosCancelados(String produtosPagosCancelados) {
        if (produtosPagosCancelados == null) {
            produtosPagosCancelados = "";
        }
        this.produtosPagosCancelados = produtosPagosCancelados;
    }

    public void setPagamentoAvulso(boolean pagamentoAvulso) {
        this.pagamentoAvulso = pagamentoAvulso;
    }

    public boolean isPagamentoAvulso() {
        return pagamentoAvulso;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public boolean isEntrouNoCaixa() {
        return (getMovconta() != null && getMovconta() != 0);
    }

    public Integer getMovconta() {
        return movconta;
    }

    public void setMovconta(Integer movconta) {
        this.movconta = movconta;
    }

    public void setMovPagamentoOrigemCredito(Integer movpagamentoorigemcredito) {
        this.movPagamentoOrigemCredito = movpagamentoorigemcredito;
    }

    public Integer getMovPagamentoOrigemCredito() {
        return movPagamentoOrigemCredito;
    }

    public void setMatriculaPagador(String matriculaPagador) {
        this.matriculaPagador = matriculaPagador;
    }

    public String getMatriculaPagador() {
        return matriculaPagador;
    }

    public Long getMatriculaRelatorio() {
        return UteisValidacao.emptyString(matriculaPagador) ? 0L : Long.valueOf(matriculaPagador);
    }

    public void setNrCheques(Integer nrCheques) {
		this.nrCheques = nrCheques;
	}

	public Integer getNrCheques() {
		return nrCheques;
	}

    public String getNrVezes(){
		return getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla()) ?
				nrCheques + (nrCheques > 1 ? " cheques":" cheque") :
                getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()) ?
							nrParcelaCartaoCredito + (nrParcelaCartaoCredito > 1 ? " parcelas":" parcela") :
								"";
	}

    public int getQtdVezes() {
        return getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla()) ? nrCheques :
                getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()) ? nrParcelaCartaoCredito : 1;
    }

	public void setCupomEmitido(Boolean cupomEmitido) {
		this.cupomEmitido = cupomEmitido;
	}

	public Boolean getCupomEmitido() {
		return cupomEmitido;
	}

        public boolean getConsumidor(){
		return UteisValidacao.emptyString(matriculaPagador);
	}

    public String getContaFinanceiro() {
        if(contaFinanceiro == null){
            contaFinanceiro = "";
        }
        return contaFinanceiro;
    }

    public String getContaFinanceiroMin() {
        return getContaFinanceiro().toLowerCase();
    }

    public void setContaFinanceiro(String contaFinanceiro) {
        this.contaFinanceiro = contaFinanceiro;
    }

    public Date getDataMovimento() {
        return dataMovimento;
    }

    public void setDataMovimento(Date dataMovimento) {
        this.dataMovimento = dataMovimento;
    }

    public String getDataMovimento_Apresentar() {
        return Uteis.getData(dataMovimento);
    }

    public String getDataMovimentoOrdenacao() {
        String ordenacao = "";
        if(this.dataMovimento != null)
            ordenacao =  Uteis.adicionarValorEsquerda("0", String.valueOf(this.codigo), 10) + Uteis.getData(dataMovimento, "yyyyMMddHHMM");
        return ordenacao;
    }

    public RecebivelTO getRecebivelTO() throws Exception {
        RecebivelTO recebivelTO = new RecebivelTO();
        recebivelTO.setCodigo(this.getCodigo());
        recebivelTO.setCodigoUnico("MP-"+this.getCodigo());
        recebivelTO.setDataPagamento(this.getDataPagamento());
        recebivelTO.setDataLancamento(this.getDataLancamento());
        recebivelTO.setDataQuitacao(this.getDataQuitacao());
        recebivelTO.setDataCompensacao(this.getDataPagamento());
        recebivelTO.setValor(Uteis.arredondarForcando2CasasDecimais(this.getValor()));
        recebivelTO.setValorTotal(Uteis.arredondarForcando2CasasDecimais(this.getValorTotal()));
        recebivelTO.setPagamentoMovParcelaVOs(this.getPagamentoMovParcelaVOs());
        //credito?
        recebivelTO.setCreditoApresentar(this.getCreditoApresentar());
        recebivelTO.setNrParcelaCartaoCredito(this.getNrParcelaCartaoCredito() == 0 ? null : this.getNrParcelaCartaoCredito());
        recebivelTO.setSaldoContaCorrenteCliente(this.getSaldoContaCorrenteCliente());
        recebivelTO.setNomePagador(this.getNomePagador());
        recebivelTO.setCpfPagador(this.getCpfPagador());
        recebivelTO.setTipoPagador(this.getTipoPagador());
        recebivelTO.setNomePessoa(this.getNomePagador());
        //convenio, OperadoraCartao, pagamentoMovParcelaVOs, chequeVos, cartaoCreditoVOs
        recebivelTO.setOperadora(this.getNomeOperadoraCartao());
        recebivelTO.setCodigoPessoa(this.getPessoa().getCodigo());
        //responsavelPagamento
        recebivelTO.setRecibo(this.getReciboPagamento().getCodigo());
        //pagamentoAvulso, formaPagamento, opcaoPagamentoCheque, opcaoPagamentoCartaoCredito, opcaoPagamentoCartaoDebito,
        //opcaoPagamentoDinheiro, opcaoPagamentoTransferencia, opcaoPagamentoBoleto, opcaoPagamentoContaCorrenteCliente,
        //mostraContaCorrenteAcademia, movPagamentoEscolhida, apresentarCampoCPF, apresentarCampoCNPJ, chequeTransiente;
        recebivelTO.setValorReceberOuDevolverContaCorrente(Uteis.arredondarForcando2CasasDecimais(this.getValorReceberOuDevolverContaCorrente()));
        //usarPagamentoDigital, usarPagamentoAprovaFacil
        recebivelTO.setDataAlteracaoManual(this.getDataAlteracaoManual());
        //empresa
        recebivelTO.setObservacao(this.getObservacao());
        //movPagamentoEscolhidaFinan;
        recebivelTO.setAutorizacaoCartao(this.getAutorizacaoCartao());
        recebivelTO.setMovPagamentoOrigemCredito(this.getMovPagamentoOrigemCredito() == 0 ? null : this.getMovPagamentoOrigemCredito());
        recebivelTO.setDataPrevistaDeposito(this.getDataPrevistaDeposito());
        //produtosPagos;
        recebivelTO.setMovConta(this.getMovconta() == 0 ? null : this.getMovconta());
        recebivelTO.setMatricula(this.getMatriculaPagador());
        recebivelTO.setNrCheques(this.getNrCheques());
        //cupomEmitido;
        recebivelTO.setContaFinanceiro(this.getContaFinanceiro());
        recebivelTO.setDataMovimento(this.getDataMovimento());
        
        if(UteisValidacao.emptyString(this.getProdutosPagos())){
            recebivelTO.setProdutos("CONTA CORRENTE");
            recebivelTO.setPlanoContrato("");
            recebivelTO.setModalidade("");
        } else {
            MovPagamento movPagamento = new MovPagamento();
            String produtosPlanos = movPagamento.consultarDescricaoProdutosPagosPlano(this.getProdutosPagos());
            String[] valores = produtosPlanos.split("\\?");
            recebivelTO.setProdutos(valores.length > 0 ? valores[0] : "");
            recebivelTO.setPlanoContrato(valores.length > 1 ? valores[1] : "");
            recebivelTO.setModalidade(valores.length > 2 ? valores[2] : "");
        }
        recebivelTO.setNsu(this.getNsu());
        recebivelTO.setAutorizacao(this.getAutorizacaoCartao());
        recebivelTO.setAutorizacaoCartao(this.getAutorizacaoCartao());
        recebivelTO.setEmpresa(this.getEmpresa());
        recebivelTO.setVencimentosParcelas(this.getVencimentosParcelas());
        recebivelTO.setNumerosParcelas(this.getNumerosParcelas());
        recebivelTO.setCodigosParcelas(this.getCodigosParcelas());
        recebivelTO.setUsuarioVO(this.getResponsavelPagamento());
        recebivelTO.setFormaPagamento(this.getFormaPagamento());
        return recebivelTO;
    }

    public Boolean getNfseEmitido() {
        return nfseEmitido;
    }

    public void setNfseEmitido(Boolean nfseEmitido) {
        this.nfseEmitido = nfseEmitido;
    }

    public boolean getDiferenteChequeCartao(){
        return !getFormaPagamento().getTipoFormaPagamento().equals("CH")
                && !getFormaPagamento().getTipoFormaPagamento().equals("CA");
    }

    public String getNsu() {
        if (nsu == null) {
            nsu = "";
        }
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public int getId_recebe() {
        return id_recebe;
    }

    public void setId_recebe(int id_recebe) {
        this.id_recebe = id_recebe;
    }


    public String getValorTotalApresentar() {
      return Formatador.formatarValorMonetarioSemMoeda(valorTotal);
    }

    public List<ChequeVO> getChequesCanceladoVOs() {
        return chequesCanceladoVOs;
    }

    public void setChequesCanceladoVOs(List<ChequeVO> chequesCanceladoVOs) {
        this.chequesCanceladoVOs = chequesCanceladoVOs;
    }

    public List<CartaoCreditoVO> getCartoesCanceladoVOs() {
        return cartoesCanceladoVOs;
    }

    public void setCartoesCanceladoVOs(List<CartaoCreditoVO> cartoesCanceladoVOs) {
        this.cartoesCanceladoVOs = cartoesCanceladoVOs;
    }

    public String getCpfPagador() {
        return cpfPagador;
    }

    public void setCpfPagador(String cpfPagador) {
        this.cpfPagador = cpfPagador;
    }

    public List<CartaoCreditoVO> getListaCartaoExcluirVinculoComNFSe() {
        if (this.listaCartaoExcluirVinculoComNFSe == null){
            this.listaCartaoExcluirVinculoComNFSe = new ArrayList<CartaoCreditoVO>();
        }
        return listaCartaoExcluirVinculoComNFSe;
    }

    public void setListaCartaoExcluirVinculoComNFSe(List<CartaoCreditoVO> listaCartaoExcluirVinculoComNFSe) {
        this.listaCartaoExcluirVinculoComNFSe = listaCartaoExcluirVinculoComNFSe;
    }

    public List<ChequeVO> getListaChequeExcluirVinculoComNFSe() {
        if (this.listaChequeExcluirVinculoComNFSe == null){
            this.listaChequeExcluirVinculoComNFSe = new ArrayList<ChequeVO>();
        }
        return listaChequeExcluirVinculoComNFSe;
    }

    public void setListaChequeExcluirVinculoComNFSe(List<ChequeVO> listaChequeExcluirVinculoComNFSe) {
        this.listaChequeExcluirVinculoComNFSe = listaChequeExcluirVinculoComNFSe;
    }

    public Boolean getDepositoCC() {
        return depositoCC;
}

    public void setDepositoCC(Boolean depositoCC) {
        this.depositoCC = depositoCC;
    }

    public String getDataAux() {
        return dataAux;
    }

    public void setDataAux(String dataAux) {
        this.dataAux = dataAux;
    }


    public Boolean getUsarPagamentoDebitoOnline() {
        return usarPagamentoDebitoOnline;
    }

    public void setUsarPagamentoDebitoOnline(Boolean usarPagamentoDebitoOnline) {
        this.usarPagamentoDebitoOnline = usarPagamentoDebitoOnline;
    }

    public Date getDataPagamentoOriginal() {
        return dataPagamentoOriginal;
    }

    public void setDataPagamentoOriginal(Date dataPagamentoOriginal) {
        this.dataPagamentoOriginal = dataPagamentoOriginal;
    }
    
    public String getDataPagamentoOriginal_Apresentar() {
        return (Uteis.getDataComHora(dataPagamentoOriginal));
    }


    public String getDataPagamentoOriginalYYYYMMDD() {
        return (Uteis.getDataAplicandoFormatacao(dataPagamentoOriginal, "yyyyMMdd"));
    }

    public String getDataPagamentoOriginalOrdenacao() {
        return (Uteis.getDataAplicandoFormatacao(dataPagamentoOriginal, "yyyyMMddHHmm"));
    }

    public String getDataPagamentoOriginalSemHora_Apresentar() {
        return (Uteis.getData(dataPagamentoOriginal));
    }

    public Integer getTipoParcelamento() {
        return tipoParcelamento;
    }

    public void setTipoParcelamento(Integer tipoParcelamento) {
        this.tipoParcelamento = tipoParcelamento;
    }

    public Double getValorPP() {
        return valorPP;
    }

    public void setValorPP(Double valorPP) {
        this.valorPP = valorPP;
    }

    public Double getValorPPCancelado() {
        return valorPPCancelado;
    }

    public void setValorPPCancelado(Double valorPPCancelado) {
        this.valorPPCancelado = valorPPCancelado;
    }

    public List<CentroCustoTO> getListaCentroCusto() {
        if (this.listaCentroCusto == null){
            this.listaCentroCusto = new ArrayList<CentroCustoTO>();
        }
        return listaCentroCusto;
    }

    public void setListaCentroCusto(List<CentroCustoTO> listaCentroCusto) {
        this.listaCentroCusto = listaCentroCusto;
    }

    public String getMensagemDescricaoCentroCustos(){
        String descricao = "";
        if (getListaCentroCusto().size() > 1) {
            StringBuilder sb = new StringBuilder("Este recebível faz parte de mais de um Centro de Custos:&#10;\n");
            for (int i = 0;i< listaCentroCusto.size();i++) {
                CentroCustoTO centroCustoTO = listaCentroCusto.get(i);
                sb.append("| " + (i + 1) + "- " + centroCustoTO.getDescricao() + " &#10;");
            }
            descricao = sb.toString().replaceFirst("\\|", "");
        }
        return descricao;
    }

    public AdquirenteVO getAdquirenteVO() {
        if(adquirenteVO == null){
            adquirenteVO = new AdquirenteVO();
        }
        return adquirenteVO;
    }

    public void setAdquirenteVO(AdquirenteVO adquirenteVO) {
        this.adquirenteVO = adquirenteVO;
    }

    public boolean isPagamentoAberto() {
        return pagamentoAberto;
    }

    public void setPagamentoAberto(boolean pagamentoAberto) {
        this.pagamentoAberto = pagamentoAberto;
    }

    public Date getDataCobrancaTransacao() {
        return dataCobrancaTransacao;
    }

    public void setDataCobrancaTransacao(Date dataCobrancaTransacao) {
        this.dataCobrancaTransacao = dataCobrancaTransacao;
    }

    public Double getValorPagaContrato() {
        return valorPagaContrato;
    }

    public void setValorPagaContrato(Double valorPagaContrato) {
        this.valorPagaContrato = valorPagaContrato;
    }

    public Double getValorEstornado() {
        return valorEstornado;
    }

    public void setValorEstornado(Double valorEstornado) {
        this.valorEstornado = valorEstornado;
    }

    public boolean isNfceEmitido() {
        return nfceEmitido;
    }

    public void setNfceEmitido(boolean nfceEmitido) {
        this.nfceEmitido = nfceEmitido;
    }

    public String getParcelasPagas() {
        if (parcelasPagas == null) {
            parcelasPagas = "";
        }
        return parcelasPagas;
    }

    public void setParcelasPagas(String parcelasPagas) {
        this.parcelasPagas = parcelasPagas;
    }

    public boolean isOpcaoPagamentoParceiroFidelidade() {
        return opcaoPagamentoParceiroFidelidade;
    }

    public void setOpcaoPagamentoParceiroFidelidade(boolean opcaoPagamentoParceiroFidelidade) {
        this.opcaoPagamentoParceiroFidelidade = opcaoPagamentoParceiroFidelidade;
    }

    public TabelaParceiroFidelidadeVO getTabelaParceiroFidelidadeVO() {
        if (tabelaParceiroFidelidadeVO == null) {
            tabelaParceiroFidelidadeVO = new TabelaParceiroFidelidadeVO();
        }
        return tabelaParceiroFidelidadeVO;
    }

    public void setTabelaParceiroFidelidadeVO(TabelaParceiroFidelidadeVO tabelaParceiroFidelidadeVO) {
        this.tabelaParceiroFidelidadeVO = tabelaParceiroFidelidadeVO;
    }

    public String getCpfParceiroFidelidade() {
        if (cpfParceiroFidelidade == null) {
            cpfParceiroFidelidade = "";
        }
        return cpfParceiroFidelidade;
    }

    public void setCpfParceiroFidelidade(String cpfParceiroFidelidade) {
        this.cpfParceiroFidelidade = cpfParceiroFidelidade;
    }

    public String getMsgParceiroFidelidade() {
        if (msgParceiroFidelidade == null) {
            msgParceiroFidelidade = "";
        }
        return msgParceiroFidelidade;
    }

    public void setMsgParceiroFidelidade(String msgParceiroFidelidade) {
        this.msgParceiroFidelidade = msgParceiroFidelidade;
    }

    public List<SelectItem> getListaSelectItemTabelaParceiroFidelidade() {
        if (listaSelectItemTabelaParceiroFidelidade == null) {
            listaSelectItemTabelaParceiroFidelidade = new ArrayList<SelectItem>();
        }
        return listaSelectItemTabelaParceiroFidelidade;
    }

    public void setListaSelectItemTabelaParceiroFidelidade(List<SelectItem> listaSelectItemTabelaParceiroFidelidade) {
        this.listaSelectItemTabelaParceiroFidelidade = listaSelectItemTabelaParceiroFidelidade;
    }

    public TipoPontoParceiroFidelidadeEnum getTipoPontoParceiroFidelidade() {
        return tipoPontoParceiroFidelidade;
    }

    public void setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum tipoPontoParceiroFidelidade) {
        this.tipoPontoParceiroFidelidade = tipoPontoParceiroFidelidade;
    }

    public String getDescricaoTotalPontos() {
        return getFormaPagamento().getTipoParceiro().getSimbolo() + " " + getPontosParceiroFidelidade() + " = " + getValorTotal_Apresentar();
    }

    public Integer getPontosParceiroFidelidade() {
        if (pontosParceiroFidelidade == null) {
            pontosParceiroFidelidade = 0;
        }
        return pontosParceiroFidelidade;
    }

    public void setPontosParceiroFidelidade(Integer pontosParceiroFidelidade) {
        this.pontosParceiroFidelidade = pontosParceiroFidelidade;
    }

    public boolean isUsarParceiroFidelidade() {
        return usarParceiroFidelidade;
    }

    public void setUsarParceiroFidelidade(boolean usarParceiroFidelidade) {
        this.usarParceiroFidelidade = usarParceiroFidelidade;
    }

    public Double getMultiplicadorParceiroFidelidade() {
        if (multiplicadorParceiroFidelidade == null) {
            multiplicadorParceiroFidelidade = 0.0;
        }
        return multiplicadorParceiroFidelidade;
    }

    public void setMultiplicadorParceiroFidelidade(Double multiplicadorParceiroFidelidade) {
        this.multiplicadorParceiroFidelidade = multiplicadorParceiroFidelidade;
    }

    public boolean isParceiroFidelidadeProcessado() {
        return parceiroFidelidadeProcessado;
    }

    public void setParceiroFidelidadeProcessado(boolean parceiroFidelidadeProcessado) {
        this.parceiroFidelidadeProcessado = parceiroFidelidadeProcessado;
    }

    public String getSenhaParceiroFidelidade() {
        if (senhaParceiroFidelidade == null) {
            senhaParceiroFidelidade = "";
        }
        return senhaParceiroFidelidade;
    }

    public void setSenhaParceiroFidelidade(String senhaParceiroFidelidade) {
        this.senhaParceiroFidelidade = senhaParceiroFidelidade;
    }

    public List<SelectItem> getSelectItemsProdutosParceiroFidelidade() {
        if (selectItemsProdutosParceiroFidelidade == null) {
            selectItemsProdutosParceiroFidelidade = new ArrayList<SelectItem>();
        }
        return selectItemsProdutosParceiroFidelidade;
    }

    public void setSelectItemsProdutosParceiroFidelidade(List<SelectItem> selectItemsProdutosParceiroFidelidade) {
        this.selectItemsProdutosParceiroFidelidade = selectItemsProdutosParceiroFidelidade;
    }

    public List<ProdutoParceiroFidelidadeVO> getProdutosParceiroFidelidade() {
        if (produtosParceiroFidelidade == null) {
            produtosParceiroFidelidade = new ArrayList<ProdutoParceiroFidelidadeVO>();
        }
        return produtosParceiroFidelidade;
    }

    public void setProdutosParceiroFidelidade(List<ProdutoParceiroFidelidadeVO> produtosParceiroFidelidade) {
        this.produtosParceiroFidelidade = produtosParceiroFidelidade;
    }

    public String getCodigoExternoProdutoParceiroFidelidade() {
        if (codigoExternoProdutoParceiroFidelidade == null) {
            codigoExternoProdutoParceiroFidelidade = "";
        }
        return codigoExternoProdutoParceiroFidelidade;
    }

    public void setCodigoExternoProdutoParceiroFidelidade(String codigoExternoProdutoParceiroFidelidade) {
        this.codigoExternoProdutoParceiroFidelidade = codigoExternoProdutoParceiroFidelidade;
    }

    public void setNumeroUnicoTransacao(String numeroUnicoTransacao) {
        this.numeroUnicoTransacao = numeroUnicoTransacao;
    }

    public String getNumeroUnicoTransacao() {
        if (numeroUnicoTransacao == null || numeroUnicoTransacao.equals("numerounicotransacao")) {
            numeroUnicoTransacao = "";
        }
        return numeroUnicoTransacao;
    }

    public void setRespostaRequisicaoPinpad(String respostaRequisicaoPinpad) {
        this.respostaRequisicaoPinpad = respostaRequisicaoPinpad;
    }

    public String getRespostaRequisicaoPinpad() {
        if (respostaRequisicaoPinpad == null) {
            respostaRequisicaoPinpad = "";
        }
        return respostaRequisicaoPinpad;
    }

    public String getTipoParcelamentoStone() {
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(String tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public StatusPagamentoConciliadora getStatusConciliadora() {
        if (statusConciliadora == null) {
            statusConciliadora = StatusPagamentoConciliadora.NENHUM;
        }
        return statusConciliadora;
    }

    public void setStatusConciliadora(StatusPagamentoConciliadora statusConciliadora) {
        this.statusConciliadora = statusConciliadora;
    }

    public String getNomeOperadoraCartao() {
        return nomeOperadoraCartao;
    }

    public void setNomeOperadoraCartao(String nomeOperadoraCartao) {
        this.nomeOperadoraCartao = nomeOperadoraCartao;
    }

    public boolean isPermitePeloCodigoIntegracaoHabilitado(Integer nrParcelas) {
        return this.getOperadoraCartaoVO().getCodigoIntegracaoAPF() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoVindi() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoCielo() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoERede() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoMaxiPago() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoFitnessCard() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoGetNet() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoMundiPagg() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoPagarMe() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoFacilitePay() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoOnePayment() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoStripe() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoPinBank() != null
                || this.getOperadoraCartaoVO().getCodigoIntegracaoPagBank() != null
                || (this.getOperadoraCartaoVO().getCodigoIntegracaoStoneOnline() != null && nrParcelas <= 1)
                || (this.getOperadoraCartaoVO().getCodigoIntegracaoStoneOnlineV5() != null && nrParcelas <= 1);
    }

    public boolean isApresentarPagamentoOnline() {
        return apresentarPagamentoOnline;
    }

    public void setApresentarPagamentoOnline(boolean apresentarPagamentoOnline) {
        this.apresentarPagamentoOnline = apresentarPagamentoOnline;
    }

    public boolean isValidarOperadora() {
        return validarOperadora;
    }

    public void setValidarOperadora(boolean validarOperadora) {
        this.validarOperadora = validarOperadora;
    }

    public boolean isValidarNrVezes() {
        return validarNrVezes;
    }

    public void setValidarNrVezes(boolean validarNrVezes) {
        this.validarNrVezes = validarNrVezes;
    }

    public boolean isExibirDataCreditoBoleto() {
        return exibirDataCreditoBoleto;
    }

    public void setExibirDataCreditoBoleto(boolean exibirDataCreditoBoleto) {
        this.exibirDataCreditoBoleto = exibirDataCreditoBoleto;
    }

    public boolean isEnviadoConciliadora() {
        return enviadoConciliadora;
    }

    public void setEnviadoConciliadora(boolean enviadoConciliadora) {
        this.enviadoConciliadora = enviadoConciliadora;
    }

    public String getUsuarioResponsavelApresentar() {
        return (!UteisValidacao.emptyString(this.getNomeResponsavelRecibo())) ? this.getNomeResponsavelRecibo() : "";
    }

    public Integer getReciboPagamentoApresentar() {
        return (this.getReciboPagamento() != null) ? this.getReciboPagamento().getCodigo() : null;
    }

    public Integer getContratoReciboApresentar() {
        return (this.getContratoVO() != null) ? this.getContratoVO().getCodigo() : null;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            return new ContratoVO();
        }
        return contratoVO;
    }
    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getNomeResponsavelRecibo() {
        if (UteisValidacao.emptyString(nomeResponsavelRecibo)) {
            return "";
        }
        return nomeResponsavelRecibo;
    }

    public void setNomeResponsavelRecibo(String nomeResponsavelRecibo) {
        this.nomeResponsavelRecibo = nomeResponsavelRecibo;
    }

    public String getLabelTipoPinPad() {
        Set<Integer> tiposPinpad = new HashSet<>();
        for (PinPadVO pinPadVO : this.getFormaPagamento().getListaPinPad()) {
            tiposPinpad.add(pinPadVO.getOpcoesPinpadEnum().getCodigo());
        }

        if (tiposPinpad.size() == 1) {
            OpcoesPinpadEnum opcoesPinpadEnum = OpcoesPinpadEnum.fromCodigo(tiposPinpad.stream().findFirst().get());
            if (opcoesPinpadEnum != null) {
                return opcoesPinpadEnum.getNome();
            }
        }
        return "Pinpad";
    }

    public boolean isAntecipacao() {
        return antecipacao;
    }

    public void setAntecipacao(boolean antecipacao) {
        this.antecipacao = antecipacao;
    }

    public Date getDataPgtoOriginalAntesDaAntecipacao() {
        return dataPgtoOriginalAntesDaAntecipacao;
    }

    public void setDataPgtoOriginalAntesDaAntecipacao(Date dataPgtoOriginalAntesDaAntecipacao) {
        this.dataPgtoOriginalAntesDaAntecipacao = dataPgtoOriginalAntesDaAntecipacao;
    }

    public String getDataPgtoOriginalAntesDaAntecipacaoApresentar() {
        if (dataPgtoOriginalAntesDaAntecipacao == null) {
            return "";
        }
        return Uteis.getDataAplicandoFormatacao(dataPgtoOriginalAntesDaAntecipacao, "dd/MM/yyyy");
    }

    public Double getTaxaAntecipacao() {
        if (taxaAntecipacao == null) {
            return 0.0;
        }
        return taxaAntecipacao;
    }

    public void setTaxaAntecipacao(Double taxaAntecipacao) {
        this.taxaAntecipacao = taxaAntecipacao;
    }

    public String getResponsavelPagador() {
        if(responsavelPagador == null){
            responsavelPagador = "";
        }
        return responsavelPagador;
    }

    public void setResponsavelPagador(String responsavelPagador) {
        this.responsavelPagador = responsavelPagador;
    }

    public String getNomeAlunosDaParcela() {
        return nomeAlunosDaParcela;
    }

    public void setNomeAlunosDaParcela(String nomeAlunosDaParcela) {
        this.nomeAlunosDaParcela = nomeAlunosDaParcela;
    }

    public String getDocumentoIntegracaoSesi() {
        return documentoIntegracaoSesi;
    }

    public void setDocumentoIntegracaoSesi(String documentoIntegracaoSesi) {
        this.documentoIntegracaoSesi = documentoIntegracaoSesi;
    }

    public PessoaVO getPessoaVODoContratoDoMovPagamento() {
        if (pessoaVODoContratoDoMovPagamento == null) {
            pessoaVODoContratoDoMovPagamento = new PessoaVO();
        }
        return pessoaVODoContratoDoMovPagamento;
    }

    public void setPessoaVODoContratoDoMovPagamento(PessoaVO pessoaVODoContratoDoMovPagamento) {
        this.pessoaVODoContratoDoMovPagamento = pessoaVODoContratoDoMovPagamento;
    }

    public int getCodMatriculaDoContratoDoMovPagamento() {
        return codMatriculaDoContratoDoMovPagamento;
    }

    public void setCodMatriculaDoContratoDoMovPagamento(int codMatriculaDoContratoDoMovPagamento) {
        this.codMatriculaDoContratoDoMovPagamento = codMatriculaDoContratoDoMovPagamento;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            return new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }
}
