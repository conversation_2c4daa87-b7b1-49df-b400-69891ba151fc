package negocio.comuns.plano;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ReposicaoVO;

/**
 *
 * <AUTHOR>
 */
public class ConsultarTurmaTO extends SuperVO {
    private static final long serialVersionUID = -4993262028273913781L;

    private boolean domingo;
    private boolean segunda;
    private boolean terca;
    private boolean quarta;
    private boolean quinta;
    private boolean sexta;
    private boolean sabado;
    private boolean h0001as0200;
    private boolean h0201as0400;
    private boolean h0401as0600;
    private boolean h0601as0800;
    private boolean h0801as1000;
    private boolean h1001as1200;
    private boolean h1201as1400;
    private boolean h1401as1600;
    private boolean h1601as1800;
    private boolean h1801as2000;
    private boolean h2001as2200;
    private boolean h2201as0000;
    private TurmaVO turma = new TurmaVO();
    private EmpresaVO empresa = new EmpresaVO();
    private ModalidadeVO modalidade = new ModalidadeVO();
    private ColaboradorVO professor = new ColaboradorVO();
    private AmbienteVO ambiente = new AmbienteVO();
    private List<SelectItem> listaModalidade = new ArrayList<SelectItem>();
    private List<SelectItem> listaTurma = new ArrayList<SelectItem>();
    private List<SelectItem> listaProfessor = new ArrayList<SelectItem>();
    private List<SelectItem> listaEmpresa = new ArrayList<SelectItem>();
    private List<SelectItem> listaAmbiente = new ArrayList<SelectItem>();
    
    private List<SelectItem> listaNivel = new ArrayList<SelectItem>();
    private boolean modoReposicao = false;
    private boolean exibirReposicoes = true;
    private ReposicaoVO reposicao = new ReposicaoVO();
    private NivelTurmaVO nivel = new NivelTurmaVO();

    private Integer pessoaOperacao;

    public ConsultarTurmaTO() {
    }

    public void validarDados() throws Exception {
        if (!getValidarDados().booleanValue()) {
            return;
        }
        if (getEmpresa() == null || getEmpresa().getCodigo().intValue() == 0) {
            throw new Exception("Informe a Empresa.");
        }
        /* if (getModalidade() == null || getModalidade().getCodigo().intValue() == 0) {
        throw new Exception("Informe a Modalidade.");
        }*/
    }

    public boolean isDomingo() {
        return domingo;
    }

    public void setDomingo(boolean domingo) {
        this.domingo = domingo;
    }

    public boolean isSegunda() {
        return segunda;
    }

    public void setSegunda(boolean segunda) {
        this.segunda = segunda;
    }

    public boolean isTerca() {
        return terca;
    }

    public void setTerca(boolean terca) {
        this.terca = terca;
    }

    public boolean isQuarta() {
        return quarta;
    }

    public void setQuarta(boolean quarta) {
        this.quarta = quarta;
    }

    public boolean isQuinta() {
        return quinta;
    }

    public void setQuinta(boolean quinta) {
        this.quinta = quinta;
    }

    public boolean isSexta() {
        return sexta;
    }

    public void setSexta(boolean sexta) {
        this.sexta = sexta;
    }

    public boolean isSabado() {
        return sabado;
    }

    public void setSabado(boolean sabado) {
        this.sabado = sabado;
    }

    public boolean isH0001as0200() {
        return h0001as0200;
    }

    public void setH0001as0200(boolean h0001as0200) {
        this.h0001as0200 = h0001as0200;
    }

    public boolean isH0201as0400() {
        return h0201as0400;
    }

    public void setH0201as0400(boolean h0201as0400) {
        this.h0201as0400 = h0201as0400;
    }

    public boolean isH0401as0600() {
        return h0401as0600;
    }

    public void setH0401as0600(boolean h0401as0600) {
        this.h0401as0600 = h0401as0600;
    }

    public boolean isH0601as0800() {
        return h0601as0800;
    }

    public void setH0601as0800(boolean h0601as0800) {
        this.h0601as0800 = h0601as0800;
    }

    public boolean isH0801as1000() {
        return h0801as1000;
    }

    public void setH0801as1000(boolean h0801as1000) {
        this.h0801as1000 = h0801as1000;
    }

    public boolean isH1001as1200() {
        return h1001as1200;
    }

    public void setH1001as1200(boolean h1001as1200) {
        this.h1001as1200 = h1001as1200;
    }

    public boolean isH1201as1400() {
        return h1201as1400;
    }

    public void setH1201as1400(boolean h1201as1400) {
        this.h1201as1400 = h1201as1400;
    }

    public boolean isH1401as1600() {
        return h1401as1600;
    }

    public void setH1401as1600(boolean h1401as1600) {
        this.h1401as1600 = h1401as1600;
    }

    public boolean isH1601as1800() {
        return h1601as1800;
    }

    public void setH1601as1800(boolean h1601as1800) {
        this.h1601as1800 = h1601as1800;
    }

    public boolean isH1801as2000() {
        return h1801as2000;
    }

    public void setH1801as2000(boolean h1801as2000) {
        this.h1801as2000 = h1801as2000;
    }

    public boolean isH2001as2200() {
        return h2001as2200;
    }

    public void setH2001as2200(boolean h2001as2200) {
        this.h2001as2200 = h2001as2200;
    }

    public boolean isH2201as0000() {
        return h2201as0000;
    }

    public void setH2201as0000(boolean h2201as0000) {
        this.h2201as0000 = h2201as0000;
    }

    public TurmaVO getTurma() {
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorVO professor) {
        this.professor = professor;
    }

    public AmbienteVO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteVO ambiente) {
        this.ambiente = ambiente;
    }

    public List<SelectItem> getListaModalidade() {
        return listaModalidade;
    }

    public void setListaModalidade(List<SelectItem> listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public List<SelectItem> getListaTurma() {
        return listaTurma;
    }

    public void setListaTurma(List<SelectItem> listaTurma) {
        this.listaTurma = listaTurma;
    }

    public List<SelectItem> getListaProfessor() {
        return listaProfessor;
    }

    public void setListaProfessor(List<SelectItem> listaProfessor) {
        this.listaProfessor = listaProfessor;
    }

    public List<SelectItem> getListaEmpresa() {
        return listaEmpresa;
    }

    public void setListaEmpresa(List<SelectItem> listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }

    public List<SelectItem> getListaAmbiente() {
        return listaAmbiente;
    }

    public void setListaAmbiente(List<SelectItem> listaAmbiente) {
        this.listaAmbiente = listaAmbiente;
    }

    public boolean isModoReposicao() {
        return modoReposicao;
    }

    public void setModoReposicao(boolean modoReposicao) {
        this.modoReposicao = modoReposicao;
    }

    public ReposicaoVO getReposicao() {
        return reposicao;
    }

    public void setReposicao(ReposicaoVO reposicao) {
        this.reposicao = reposicao;
    }

    public boolean isExibirReposicoes() {
        return exibirReposicoes;
    }

    public void setExibirReposicoes(boolean exibirReposicoes) {
        this.exibirReposicoes = exibirReposicoes;
    }

    public String getOnComplete() {
        if (modoReposicao) {
            return "Richfaces.showModalPanel('panelDadosReposicao');";
        } else {
            return "abrirPopup('./listaAlunosTurma.jsp', 'ConsultarTurmaControle', 820, 595);";
        }
    }

	public void setListaNivel(List<SelectItem> listaNivel) {
		this.listaNivel = listaNivel;
	}

	public List<SelectItem> getListaNivel() {
		return listaNivel;
	}

	public void setNivel(NivelTurmaVO nivel) {
		this.nivel = nivel;
	}

	public NivelTurmaVO getNivel() {
		return nivel;
	}

    public Integer getPessoaOperacao() {
        return pessoaOperacao;
    }

    public void setPessoaOperacao(Integer pessoaOperacao) {
        this.pessoaOperacao = pessoaOperacao;
    }
}
