package negocio.comuns.arquitetura.response;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TokenResponseTO extends SuperJSON {

    private UsuarioResponseTO user;
    private List<EmpresaResponseTO> unidadesEmpresa;
    private PerfilUsuarioDTO perfilUsuario;


    public TokenResponseTO(UsuarioVO user, List<EmpresaVO> empresas, Integer empresaLogada) {
        this.user = new UsuarioResponseTO(user);

        List<EmpresaResponseTO> empresaResponseTOList = new ArrayList<>();
        empresas.forEach(it -> empresaResponseTOList.add(new EmpresaResponseTO(it)));
        unidadesEmpresa = empresaResponseTOList;

        PerfilAcessoVO perfilAcessoVO = null;
        for (UsuarioPerfilAcessoVO upaVO : user.getUsuarioPerfilAcessoVOs()) {
            if (upaVO.getEmpresa().getCodigo().equals(empresaLogada)) {
                perfilAcessoVO = upaVO.getPerfilAcesso();
                break;
            }
        }
        if (perfilAcessoVO != null) {
            this.perfilUsuario = new PerfilUsuarioDTO(perfilAcessoVO);
        }
    }

    public UsuarioResponseTO getUser() {
        return user;
    }

    public void setUser(UsuarioResponseTO user) {
        this.user = user;
    }

    public List<EmpresaResponseTO> getUnidadesEmpresa() {
        return unidadesEmpresa;
    }

    public void setUnidadesEmpresa(List<EmpresaResponseTO> unidadesEmpresa) {
        this.unidadesEmpresa = unidadesEmpresa;
    }

    public PerfilUsuarioDTO getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(PerfilUsuarioDTO perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }
}
