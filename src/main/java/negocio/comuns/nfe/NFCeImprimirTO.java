package negocio.comuns.nfe;

import negocio.comuns.arquitetura.SuperTO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


public class NFCeImprimirTO extends SuperTO {

    private Integer id_DadosImpressaoNFCe;
    private String chaveAcesso;
    private String nomeFantasia;
    private String cnpjPrestador;
    private String cpfConsumidor;
    private String enderecoConsumidor;
    private String enderecoPrestador;
    private String infoPrestador;
    private String telefonePrestador;
    private String informacoesNota;
    private String nomeConsumidor;
    private String protocolo;
    private String qtdProdutos;
    private String razaoSocialPrestador;
    private String subTotal;
    private String valorTotal;
    private Integer idEmpresa;
    private Integer idNFCe;
    private InputStream qrCode;
    private String urlQRCode;

    private List<NFCeProdutoImprimirTO> produtos;
    private List<NFCeFormaPagamentoImprimirTO> pagamentos;
    private String observacoes;

    public List<NFCeProdutoImprimirTO> getProdutos() {
        if (produtos == null) {
            produtos = new ArrayList<NFCeProdutoImprimirTO>();
        }
        return produtos;
    }

    public void setProdutos(List<NFCeProdutoImprimirTO> produtos) {
        this.produtos = produtos;
    }

    public List<NFCeFormaPagamentoImprimirTO> getPagamentos() {
        if (pagamentos == null) {
            pagamentos = new ArrayList<NFCeFormaPagamentoImprimirTO>();
        }
        return pagamentos;
    }

    public void setPagamentos(List<NFCeFormaPagamentoImprimirTO> pagamentos) {
        this.pagamentos = pagamentos;
    }

    public JRDataSource getProdutosJr() {
        return new JRBeanArrayDataSource(getProdutos().toArray());
    }

    public JRDataSource getPagamentosJr() {
        return new JRBeanArrayDataSource(getPagamentos().toArray());
    }

    public String getChaveAcesso() {
        if (chaveAcesso == null) {
            chaveAcesso = "";
        }
        return chaveAcesso;
    }

    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }

    public String getCnpjPrestador() {
        if (cnpjPrestador == null) {
            cnpjPrestador = "";
        }
        return cnpjPrestador;
    }

    public void setCnpjPrestador(String cnpjPrestador) {
        this.cnpjPrestador = cnpjPrestador;
    }

    public String getCpfConsumidor() {
        if (cpfConsumidor == null) {
            cpfConsumidor = "";
        }
        return cpfConsumidor;
    }

    public void setCpfConsumidor(String cpfConsumidor) {
        this.cpfConsumidor = cpfConsumidor;
    }

    public String getEnderecoConsumidor() {
        if (enderecoConsumidor == null) {
            enderecoConsumidor = "";
        }
        return enderecoConsumidor;
    }

    public void setEnderecoConsumidor(String enderecoConsumidor) {
        this.enderecoConsumidor = enderecoConsumidor;
    }

    public String getEnderecoPrestador() {
        if (enderecoPrestador == null) {
            enderecoPrestador = "";
        }
        return enderecoPrestador;
    }

    public void setEnderecoPrestador(String enderecoPrestador) {
        this.enderecoPrestador = enderecoPrestador;
    }

    public String getInfoPrestador() {
        if (infoPrestador == null) {
            infoPrestador = "";
        }
        return infoPrestador;
    }

    public void setInfoPrestador(String infoPrestador) {
        this.infoPrestador = infoPrestador;
    }

    public String getInformacoesNota() {
        if (informacoesNota == null) {
            informacoesNota = "";
        }
        return informacoesNota;
    }

    public void setInformacoesNota(String informacoesNota) {
        this.informacoesNota = informacoesNota;
    }

    public String getNomeConsumidor() {
        if (nomeConsumidor == null) {
            nomeConsumidor = "";
        }
        return nomeConsumidor;
    }

    public void setNomeConsumidor(String nomeConsumidor) {
        this.nomeConsumidor = nomeConsumidor;
    }

    public String getProtocolo() {
        if (protocolo == null) {
            protocolo = "";
        }
        return protocolo;
    }

    public void setProtocolo(String protocolo) {
        this.protocolo = protocolo;
    }

    public String getQtdProdutos() {
        if (qtdProdutos == null) {
            qtdProdutos = "";
        }
        return qtdProdutos;
    }

    public void setQtdProdutos(String qtdProdutos) {
        this.qtdProdutos = qtdProdutos;
    }

    public String getRazaoSocialPrestador() {
        if (razaoSocialPrestador == null) {
            razaoSocialPrestador = "";
        }
        return razaoSocialPrestador;
    }

    public void setRazaoSocialPrestador(String razaoSocialPrestador) {
        this.razaoSocialPrestador = razaoSocialPrestador;
    }

    public String getSubTotal() {
        if (subTotal == null) {
            subTotal = "";
        }
        return subTotal;
    }

    public void setSubTotal(String subTotal) {
        this.subTotal = subTotal;
    }

    public String getValorTotal() {
        if (valorTotal == null) {
            valorTotal = "";
        }
        return valorTotal;
    }

    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getId_DadosImpressaoNFCe() {
        if (id_DadosImpressaoNFCe == null) {
            id_DadosImpressaoNFCe = 0;
        }
        return id_DadosImpressaoNFCe;
    }

    public void setId_DadosImpressaoNFCe(Integer id_DadosImpressaoNFCe) {
        this.id_DadosImpressaoNFCe = id_DadosImpressaoNFCe;
    }

    public Integer getIdEmpresa() {
        if (idEmpresa == null) {
            idEmpresa = 0;
        }
        return idEmpresa;
    }

    public void setIdEmpresa(Integer idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public Integer getIdNFCe() {
        if (idNFCe == null) {
            idNFCe = 0;
        }
        return idNFCe;
    }

    public void setIdNFCe(Integer idNFCe) {
        this.idNFCe = idNFCe;
    }

    public String getNomeArquivoPDF() {
        return getIdNFCe() + " - NFCe";
    }

    public InputStream getQrCode() {
        return qrCode;
    }

    public void setQrCode(InputStream qrCode) {
        this.qrCode = qrCode;
    }

    public String getNomeFantasia() {
        if (nomeFantasia == null) {
            nomeFantasia = "";
        }
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getTelefonePrestador() {
        if (telefonePrestador == null) {
            telefonePrestador = "";
        }
        return telefonePrestador;
    }

    public void setTelefonePrestador(String telefonePrestador) {
        this.telefonePrestador = telefonePrestador;
    }

    public String getUrlQRCode() {
        if (urlQRCode == null) {
            urlQRCode = "";
        }
        return urlQRCode;
    }

    public void setUrlQRCode(String urlQRCode) {
        this.urlQRCode = urlQRCode;
    }

    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }

    public String getObservacoes() {
        return observacoes;
    }
}
