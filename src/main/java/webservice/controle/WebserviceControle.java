/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package webservice.controle;

import acesso.webservice.BaseAcessoWS;
import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaColaborador;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaClienteInterfaceFacade;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaColaboradorInterfaceFacade;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.dao.CupomFiscal;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.socialmailing.interfaces.SocialMailingInterfaceFacade;
import br.com.pactosolucoes.spivi.SpiviService;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.acesso.DadosAcessoOffline;
import negocio.facade.jdbc.acesso.PessoaFotoLocalAcesso;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.Risco;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.contrato.ContratoCondicaoPagamento;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoHorario;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.crm.TextoPadrao;
import negocio.facade.jdbc.crm.optin.Optin;
import negocio.facade.jdbc.feed.FeedGestao;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.pactoprint.CarteirinhaCliente;
import negocio.facade.jdbc.pactoprint.LocalImpressao;
import negocio.facade.jdbc.plano.PlanoExcecao;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.acesso.AcessoColaboradorInterfaceFacade;
import negocio.interfaces.acesso.AutorizacaoAcessoGrupoEmpresarialInterfaceFacade;
import negocio.interfaces.acesso.ColetorInterfaceFacade;
import negocio.interfaces.acesso.IntegracaoAcessoGrupoEmpresarialInterfaceFacade;
import negocio.interfaces.acesso.LiberacaoAcessoInterfaceFacade;
import negocio.interfaces.acesso.LocalAcessoInterfaceFacade;
import negocio.interfaces.acesso.ServidorFacialInterfaceFacade;
import negocio.interfaces.acesso.ValidacaoLocalAcessoInterfaceFacade;
import negocio.interfaces.arquitetura.LogApiInterfaceFacade;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.arquitetura.LogTotalPassInterfaceFacade;
import negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioEmailInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioTelefoneInterfaceFacade;
import negocio.interfaces.basico.*;
import negocio.interfaces.contrato.*;
import negocio.interfaces.crm.ConfiguracaoSistemaCRMInterfaceFacade;
import negocio.interfaces.crm.EventoInterfaceFacade;
import negocio.interfaces.crm.FeriadoInterfaceFacade;
import negocio.interfaces.crm.GrupoColaboradorInterfaceFacade;
import negocio.interfaces.crm.HistoricoContatoInterfaceFacade;
import negocio.interfaces.crm.IndicacaoInterfaceFacade;
import negocio.interfaces.crm.ModeloMensagemInterfaceFacade;
import negocio.interfaces.crm.OAuth2RDStationInterfaceFacade;
import negocio.interfaces.crm.ObjecaoInterfaceFacade;
import negocio.interfaces.crm.PassivoInterfaceFacade;
import negocio.interfaces.crm.TextoPadraoInterfaceFacade;
import negocio.interfaces.crm.optin.OptinInterfaceFacade;
import negocio.interfaces.estoque.BalancoInterfaceFacade;
import negocio.interfaces.estoque.CardexInterfaceFacade;
import negocio.interfaces.estoque.CompraInterfaceFacade;
import negocio.interfaces.estoque.ProdutoEstoqueInterfaceFacade;
import negocio.interfaces.feed.FeedGestaoInterfaceFacade;
import negocio.interfaces.financeiro.*;
import negocio.interfaces.gestao.GestaoICVInterfaceFacade;
import negocio.interfaces.nfe.NotaFiscalDeServicoInterfaceFacade;
import negocio.interfaces.pactoprint.CarteirinhaClienteInterfaceFacade;
import negocio.interfaces.pactoprint.LocalImpressaoInterfaceFacade;
import negocio.interfaces.plano.*;
import negocio.interfaces.vendas.VendasConfigInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import relatorio.controle.crm.CarteirasRel;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.impl.api.TokenService;
import servicos.adm.CreditoDCCService;
import servicos.integracao.interfaces.IntegracaoLeadGenericaServiceInterface;
import servicos.interfaces.BoletoServiceInterface;
import servicos.interfaces.ConviteAulaExperimentalServiceInterface;

import java.io.File;
import java.sql.Connection;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import servicos.integracao.interfaces.rd.IntegracaoRDServiceInterface;
import servicos.integracao.interfaces.buzzlead.IntegracaoBuzzLeadServiceInterface;
import servicos.propriedades.PropsService;
import servlet.appGestor.Interface.MSAlunoService;
import servlet.appGestor.Interface.MSFinanceiroService;

/**
 *
 * <AUTHOR>
 */
public class WebserviceControle {

    private String key = null;
    private MovParcelaInterfaceFacade movParcelaDao = null;
    private MovProdutoParcela movProdutoParcela = null;
    private SocialMailingInterfaceFacade socialMailingDao = null;
    private EmpresaInterfaceFacade empresaDao = null;
    private LocalAcessoInterfaceFacade localAcessoDao = null;
    private ServidorFacialInterfaceFacade servidorFacialDao = null;
    private PeriodoAcessoClienteInterfaceFacade periodoAcessoDao = null;
    private ClienteMensagemInterfaceFacade clienteMsgDao = null;
    private ContratoInterfaceFacade contratoDao = null;
    private ColaboradorInterfaceFacade colaboradorDao = null;
    private AcessoColaboradorInterfaceFacade acessoColaboradorDao = null;
    private ColetorInterfaceFacade coletorDao = null;
    private UsuarioInterfaceFacade usuarioDao = null;
    private UsuarioEmailInterfaceFacade usuarioEmailDao = null;
    private UsuarioTelefoneInterfaceFacade usuarioTelefoneDao = null;
    private AcessoClienteInterfaceFacade acessoClienteDao = null;
    private LiberacaoAcessoInterfaceFacade liberacaoAcessoDao = null;
    private ClienteInterfaceFacade clienteDao = null;
    private VendasConfigInterfaceFacade vendasConfigDao = null;
    private HorarioDisponibilidadeInterfaceFacade horarioDisponibilidadeDao = null;
    private ContratoModalidadeTurmaInterfaceFacade contratoModalidadeTurmaDao = null;
    private ContratoModalidadeHorarioTurmaInterfaceFacade contratoModalidadeHorarioTurmaDao = null;
    private ContratoCondicaoPagamento contratoCondicaoPagamentoDao = null;
    private MovProdutoInterfaceFacade movProdutoDao = null;
    private UtilizacaoAvaliacaoFisicaInterfaceFacade utilizacaoAvaliacaoFisicaDao = null;
    private Risco riscoDao = null;
    private ValidacaoLocalAcessoInterfaceFacade validacaoLocalAcessoDao = null;
    private Permissao permissaoDao = null;
    private ControleAcesso controleAcessoDao = null;
    private PessoaInterfaceFacade pessoaDao = null;
    private CategoriaInterfaceFacade categoriaDao = null;
    private Endereco enderecoDao = null;
    private Telefone telefoneDao = null;
    private Email emailDao = null;
    private ConfiguracaoSistemaInterfaceFacade configuracaoDao = null;
    private ConfiguracaoEmpresaTotemInterfaceFacade configuracaoTotemDao = null;
    private ConfiguracaoSistemaCRMInterfaceFacade configuracaoCRMDao = null;
    private ContratoHorario contratoHorarioDao = null;
    private HorarioInterfaceFacade horarioDao = null;
    private PlanoInterfaceFacade planoDao = null;
    private PlanoExcecaoInterfaceFacade planoExcecaoDao = null;
    private ContratoModalidade contratoModalidadeDao = null;
    private ContratoDuracao contratoDuracaoDao = null;
    private HistoricoContratoInterfaceFacade historicoContratoDao = null;
    private HorarioTurmaInterfaceFacade horarioTurmaDao = null;
    private UsuarioPerfilAcesso usuarioPerfilAcessoDao = null;
    private LogControleUsabilidadeInterfaceFacade logControleUsabilidadeDao = null;
    private TipoColaboradorInterfaceFacade tipoColaboradorDao = null;
    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDao = null;
    private PessoaFotoLocalAcesso pessoaFotoLocalAcessoDao = null;
    private ClienteClassificacao clienteClassificacaoDao = null;
    private DadosAcessoOffline dadosAcessoOffline = null;
    private ZillyonWebFacade zwFacade = null;
    private MatriculaAlunoHorarioTurmaInterfaceFacade matriculaAlunoHorarioTurmaDAO= null;
    private ConfirmacaoEmailCompraInterfaceFacede confirmacaoEmailCompra;
    private BoletoPJBankInterfaceFacade boletoPJBankInterfaceFacade;
    private BoletoInterfaceFacade boletoInterfaceFacade;
    private AutorizacaoCobrancaColaboradorInterfaceFacade autorizacaoCobrancaColaboradorDao;

    /**
     * con Conexão com o BD. Única para cada Banco de dados.
     */
    protected Connection con = null;
    private static ConcurrentHashMap<String, Boolean> preparando = new ConcurrentHashMap<>();
    protected ConfiguracaoSistemaVO configuracaoSistemaVO;
    protected PlanoVO planoVO;
    private UteisEmail uteisEmail = null;
    private HistoricoContatoInterfaceFacade historicoContatoDao = null;
    private ConfiguracaoSistemaCRMVO configCRM = null;
    private AutorizacaoAcessoGrupoEmpresarialInterfaceFacade autorizacaoDao = null;
    private IntegracaoAcessoGrupoEmpresarialInterfaceFacade integracaoDao = null;
    private UsuarioMovelInterfaceFacade usuarioMovelDao = null;
    private ReposicaoInterfaceFacade reposicaoDao = null;
    public long ultimoUpdate = 0;
    private ProfissaoInterfaceFacade profissaoDao = null;
    private GrauInstrucaoInterfaceFacade grauInstrucaoDao = null;
    private ClassificacaoInterfaceFacade classificacaoDao = null;
    private GrupoInterfaceFacade grupoDao = null;
    private ParentescoInterfaceFacade parentescoDao = null;
    private PaisInterfaceFacade paisDao = null;
    private CidadeInterfaceFacade cidadeDao = null;
    private PerguntaInterfaceFacade perguntaDao = null;
    private QuestionarioInterfaceFacade questionarioDao = null;
    private QuestionarioClienteInterfaceFacade questionarioClienteDao = null;
    private CategoriaProdutoInterfaceFacade categoriaProdutoDao = null;
    private ProdutoInterfaceFacade produtoDao = null;
    private DescontoInterfaceFacade descontoDao = null;
    private CondicaoPagamentoInterfaceFacade condicaoPagamentoDao = null;
    private ModalidadeInterfaceFacade modalidadeDao = null;
    private AmbienteInterfaceFacade ambienteDao = null;
    private NivelTurmaInterfaceFacade nivelTurmaDao = null;
    private TurmaInterfaceFacade turmaDao = null;
    private MovPagamentoInterfaceFacade movPagamentoDao = null;
    private MovContaInterfaceFacade movContaDao = null;
    private VendaAvulsaInterfaceFacade vendaAvulsaDao = null;
    private AtestadoInterfaceFacade atestadoDao = null;
    private MovimentoContaCorrenteClienteInterfaceFacade movContaCorrenteClienteDao = null;
    private BancoInterfaceFacade bancoDao = null;
    private ContaCorrenteInterfaceFacade contaCorrenteDao = null;
    private TipoRetornoInterfaceFacade tipoRetornoDao = null;
    private TipoRemessaInterfaceFacade tipoRemessaDao = null;
    private ConvenioCobrancaInterfaceFacade convenioCobrancaDao = null;
    private FormaPagamentoInterfaceFacade formaPagamentoDao = null;
    private PinPadInterfaceFacade pinPadDao = null;
    private OperadoraCartaoInterfaceFacade operadoraCartaoDao = null;
    private MetaFinanceiraEmpresaInterfaceFacade metaFinanceiraEmpresaDao = null;
    private PerfilAcessoInterfaceFacade perfilAcessoDao = null;
    private LogInterfaceFacade logDao = null;
    private LogApiInterfaceFacade logApiDao = null;
    private LogTotalPassInterfaceFacade logTotalPassDao = null;
    private PlanoTextoPadraoInterfaceFacade planoTextoPadraoDao = null;
    private ConvenioDescontoInterfaceFacade convenioDescontoDao = null;
    private JustificativaOperacaoInterfaceFacade justificativaOperacaoDao = null;
    private BalancoInterfaceFacade balancoDao = null;
    private CardexInterfaceFacade cardexDao = null;
    private CompraInterfaceFacade compraDao = null;
    private ProdutoEstoqueInterfaceFacade produtoEstoqueDao = null;
    private ComposicaoInterfaceFacade composicaoDao = null;
    private CupomFiscal cupomFiscalDao = null;
    private ReciboPagamento reciboPagamentoDao = null;
    private AtualizadorBD atualizadorBDDao = null;
    private CarteirasRel carteirasRel = null;
    private RecebivelAvulsoInterfaceFacade recebivelAvulso = null;
    private ClienteObservacaoInterfaceFacade clienteObservacaoDao = null;
    private TextoPadraoInterfaceFacade textoPadraoDao = null;
    private VinculoInterfaceFacade vinculoDao = null;
    private ComissaoGeralConfiguracaoInterfaceFacade comissaoGeralConfiguracaoDao = null;
    private GestaoNotasInterfaceFacade gestaoNotasDao = null;
    private LancamentoProdutoColetivoInterfaceFacade lancamentoProdutoColetivoDao = null;
    private TicketMedioInterfaceFacade ticketMedioDao = null;
    private GestaoICVInterfaceFacade gestaoICVInterfaceFacade = null;
    private PassivoInterfaceFacade passivoDao = null;
    private PresencaInterfaceFacade presencaDao = null;
    private ContratoOperacaoInterfaceFacade contratoOperacaoDao = null;
    private FeriadoInterfaceFacade feriadoDao = null;
    private ModeloMensagemInterfaceFacade modeloMensagemDao = null;
    private EventoInterfaceFacade eventoDao = null;
    private ObjecaoInterfaceFacade objecaoDao = null;
    private GrupoColaboradorInterfaceFacade grupoColaboradorDao = null;
    private IndicacaoInterfaceFacade indicacaoDao = null;
    private CreditoDCCService creditoDCC = null;
    private IntegracaoCadastros integracaoCadastrosDao = null;
    private IntegracaoImportacao integracaoImportacaoDao = null;
    private ConviteInterfaceFacade conviteDao = null;
    private AutorizacaoCobrancaClienteInterfaceFacade autorizacaoCobrancaDao = null;
    private ContratoRecorrenciaInterfaceFacade contratoRecorrenciaDao = null;
    private HistoricoVinculoInterfaceFacade historicoVinculoDao = null;
    private NFSeEmitida nfSeEmitida = null;
    private NotaFiscalConsumidorEletronicaInterfaceFacade notaFiscalConsumidorEletronica = null;
    private DadosGameInterfaceFacade dadosGameDao = null;
    private DadosGerencialPmgInterfaceFacade dadosGerenciaisDao = null;
    private MonitoramentoInterfaceFacade monitoramentoDao = null;
    private DFSinteticoDetalheInterfaceFacade dfDetalheDao = null;
    private ContratoDuracaoCreditoTreinoInterfaceFacade contratoDuracaoCreditoTreino;
    private TipoConviteAulaExperimentalInterfaceFacade tipoConviteAulaExperimental;
    private ConviteAulaExperimentalServiceInterface conviteAulaExperimentalService;
    private BoletoServiceInterface boletoServiceInterface;
    private NotaFiscalDeServicoInterfaceFacade notaFiscalServicoDao;
    private AulaDesmarcadaInterfaceFacade aulaDesmarcadaDao;
    private NotificacaoUsuarioInterfaceFacade notificacaoUsuarioDao;
    private BaseAcessoWS baseAcessoWS;
    private ControleCreditoTreinoInterfaceFacade controleCreditoTreino;
    private EstadoInterfaceFacade estadoDao;
    private TurmasServiceInterface turmaService;
    private MSFinanceiroService msFinanceiroService;
    private MSAlunoService msAlunoService;
    private ContratoAssinaturaDigitalServiceInterface contratoAssinaturaDigitalService;
    private HistoricoPontosInterfaceFacade historicoPontos = null;
    private BrindeInterfaceFacade brinde = null;
    private IntegracaoRDServiceInterface integracaoRDService;
    private IntegracaoBuzzLeadServiceInterface integracaoBuzzService;
    private IntegracaoLeadGenericaServiceInterface integracaoLeadGenericaService;
    private OAuth2RDStationInterfaceFacade oAuth2RDStationInterfaceFacade;
    private ClienteTokenInterfaceFacade clienteTokenDao;
    private TokenService tokenService;
    private GympassInterfaceFacade gympass;
    private TotalpassInterfaceFacade totalpassDao = null;
    private FeedGestaoInterfaceFacade feedGestaoDao;
    private TipoContaInterfaceFacade tipoContaDao;
    private ContaInterfaceFacade contaDao;
    private ControleTaxaPersonalInterfaceFacade taxapersonalDao = null;
    private ContratoAssinaturaDigitalInterfaceFacade contratoAssinaturaDigitalDao;
    private PessoaAnexoInterfaceFacade pessoaAnexoDao;
    private AulaAvulsaDiariaInterfaceFacade aulaAvulsaDiariaDao;
    private ContratoDependenteInterfaceFacade contratoDependenteDao = null;
    private AfastamentoContratoDependenteInterfaceFacade afastamentoContratoDependenteInterfaceFacade = null;

    private SpiviService spiviService;

    private OptinInterfaceFacade optinDao;
    
    private LogIntegracoesInterfaceFacade logintegracoesDao;
    private LocalImpressaoInterfaceFacade localImpressao = null;
    private CarteirinhaClienteInterfaceFacade carteirinhaCliente = null;

    public WebserviceControle(String key) throws Exception {
        con = initConnection(key);
        this.key = key;
    }
    
    public WebserviceControle(Connection con) throws Exception {
        this.con = con;
        this.key = "";
    }

    private Connection initConnection(final String key) throws Exception {
        con = null;
        return new DAO().obterConexaoEspecifica(key);
    }

    public String getKey() {
        return key;
    }

    public ValidacaoLocalAcessoInterfaceFacade getValidacaoLocalAcessoDao() {
        validacaoLocalAcessoDao.setCon(con);
        return validacaoLocalAcessoDao;
    }

    public void setValidacaoLocalAcessoDao(ValidacaoLocalAcessoInterfaceFacade validacaoLocalAcessoDao) {
        this.validacaoLocalAcessoDao = validacaoLocalAcessoDao;
    }

    public AcessoClienteInterfaceFacade getAcessoClienteDao() {
        acessoClienteDao.setCon(con);
        return acessoClienteDao;
    }

    public void setAcessoClienteDao(AcessoClienteInterfaceFacade acessoClienteDao) {
        this.acessoClienteDao = acessoClienteDao;
    }

    public AcessoColaboradorInterfaceFacade getAcessoColaboradorDao() {
        acessoColaboradorDao.setCon(con);
        return acessoColaboradorDao;
    }

    public void setAcessoColaboradorDao(AcessoColaboradorInterfaceFacade acessoColaboradorDao) {
        this.acessoColaboradorDao = acessoColaboradorDao;
    }

    public ClienteInterfaceFacade getClienteDao() {
        clienteDao.setCon(con);
        return clienteDao;
    }

    public VendasConfigInterfaceFacade getVendasConfigDao() {
        vendasConfigDao.setCon(con);
        return vendasConfigDao;
    }

    public void setVendasConfigDao(VendasConfigInterfaceFacade vendasConfigDao) {
        this.vendasConfigDao = vendasConfigDao;
    }

    public void setClienteDao(ClienteInterfaceFacade clienteDao) {
        this.clienteDao = clienteDao;
    }

    public ClienteMensagemInterfaceFacade getClienteMsgDao() {
        clienteMsgDao.setCon(con);
        return clienteMsgDao;
    }

    public void setClienteMsgDao(ClienteMensagemInterfaceFacade clienteMsgDao) {
        this.clienteMsgDao = clienteMsgDao;
    }

    public ColaboradorInterfaceFacade getColaboradorDao() {
        colaboradorDao.setCon(con);
        return colaboradorDao;
    }

    public void setColaboradorDao(ColaboradorInterfaceFacade colaboradorDao) {
        this.colaboradorDao = colaboradorDao;
    }

    public ColetorInterfaceFacade getColetorDao() {
        coletorDao.setCon(con);
        return coletorDao;
    }

    public void setColetorDao(ColetorInterfaceFacade coletorDao) {
        this.coletorDao = coletorDao;
    }

    public ContratoInterfaceFacade getContratoDao() {
        contratoDao.setCon(con);
        return contratoDao;
    }

    public void setContratoDao(ContratoInterfaceFacade contratoDao) {
        this.contratoDao = contratoDao;
    }

    public AfastamentoContratoDependenteInterfaceFacade getAfastamentoContratoDependenteInterfaceFacade() {
        afastamentoContratoDependenteInterfaceFacade.setCon(con);
        return afastamentoContratoDependenteInterfaceFacade;
    }

    public void setAfastamentoContratoDependenteInterfaceFacade(AfastamentoContratoDependenteInterfaceFacade afastamentoDAO) {
        this.afastamentoContratoDependenteInterfaceFacade = afastamentoDAO;
    }

    public ContratoDependenteInterfaceFacade getContratoDependenteDao() {
        contratoDependenteDao.setCon(con);
        return contratoDependenteDao;
    }

    public void setContratoDependenteDao(ContratoDependenteInterfaceFacade contratoDependenteDao) {
        this.contratoDependenteDao = contratoDependenteDao;
    }

    public TicketMedioInterfaceFacade getTicketMedioDao() {
        ticketMedioDao.setCon(con);
        return ticketMedioDao;
    }

    public void setTicketMedioDao(TicketMedioInterfaceFacade ticketMedioDao) {
        this.ticketMedioDao = ticketMedioDao;
    }

    public GestaoICVInterfaceFacade getGestaoICV() {
        gestaoICVInterfaceFacade.setCon(con);
        return gestaoICVInterfaceFacade;
    }

    public void setGestaoICV(GestaoICVInterfaceFacade gestaoICV) {
        this.gestaoICVInterfaceFacade = gestaoICV;
    }

    public ContratoModalidadeHorarioTurmaInterfaceFacade getContratoModalidadeHorarioTurmaDao() {
        contratoModalidadeHorarioTurmaDao.setCon(con);
        return contratoModalidadeHorarioTurmaDao;
    }

    public void setContratoModalidadeHorarioTurmaDao(ContratoModalidadeHorarioTurmaInterfaceFacade contratoModalidadeHorarioTurmaDao) {
        this.contratoModalidadeHorarioTurmaDao = contratoModalidadeHorarioTurmaDao;
    }

    public ContratoModalidadeTurmaInterfaceFacade getContratoModalidadeTurmaDao() {
        contratoModalidadeTurmaDao.setCon(con);
        return contratoModalidadeTurmaDao;
    }

    public void setContratoModalidadeTurmaDao(ContratoModalidadeTurmaInterfaceFacade contratoModalidadeTurmaDao) {
        this.contratoModalidadeTurmaDao = contratoModalidadeTurmaDao;
    }

    public EmpresaInterfaceFacade getEmpresaDao() {
        empresaDao.setCon(con);
        return empresaDao;
    }

    public void setEmpresaDao(EmpresaInterfaceFacade empresaDao) {
        this.empresaDao = empresaDao;
    }

    public HorarioDisponibilidadeInterfaceFacade getHorarioDisponibilidadeDao() {
        horarioDisponibilidadeDao.setCon(con);
        return horarioDisponibilidadeDao;
    }

    public void setHorarioDisponibilidadeDao(HorarioDisponibilidadeInterfaceFacade horarioDisponibilidadeDao) {
        this.horarioDisponibilidadeDao = horarioDisponibilidadeDao;
    }

    public LiberacaoAcessoInterfaceFacade getLiberacaoAcessoDao() {
        liberacaoAcessoDao.setCon(con);
        return liberacaoAcessoDao;
    }

    public void setLiberacaoAcessoDao(LiberacaoAcessoInterfaceFacade liberacaoAcessoDao) {
        this.liberacaoAcessoDao = liberacaoAcessoDao;
    }

    public LocalAcessoInterfaceFacade getLocalAcessoDao() {
        localAcessoDao.setCon(con);
        return localAcessoDao;
    }

    public void setLocalAcessoDao(LocalAcessoInterfaceFacade localAcessoDao) {
        this.localAcessoDao = localAcessoDao;
    }

    public ServidorFacialInterfaceFacade getServidorFacialDao() {
        servidorFacialDao.setCon(con);
        return servidorFacialDao;
    }

    public void setServidorFacialDao(ServidorFacialInterfaceFacade servidorFacialDao) {
        this.servidorFacialDao = servidorFacialDao;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public MovParcelaInterfaceFacade getMovParcelaDao() {
        movParcelaDao.setCon(con);
        return movParcelaDao;
    }

    public MovProdutoParcela getMovProdutoParcelaDao() {
        movProdutoParcela.setCon(con);
        return movProdutoParcela;
    }

    public void setMovParcelaDao(MovParcelaInterfaceFacade movParcelaDao) {
        this.movParcelaDao = movParcelaDao;
    }

    public void setMovProdutoParcelaDao(MovProdutoParcela movProdutoParcelaDao) {
        this.movProdutoParcela = movProdutoParcelaDao;
    }

    public HistoricoContatoInterfaceFacade getHistoricoContatoDao() {
        historicoContatoDao.setCon(con);
        return historicoContatoDao;
    }

    public void setHistoricoContatoDao(HistoricoContatoInterfaceFacade historicoContatoDao) {
        this.historicoContatoDao = historicoContatoDao;
    }

    public MovProdutoInterfaceFacade getMovProdutoDao() {
        movProdutoDao.setCon(con);
        return movProdutoDao;
    }

    public void setMovProdutoDao(MovProdutoInterfaceFacade movProdutoDao) {
        this.movProdutoDao = movProdutoDao;
    }

    public UtilizacaoAvaliacaoFisicaInterfaceFacade getUtilizacaoAvaliacaoFisicaDao() {
        utilizacaoAvaliacaoFisicaDao.setCon(con);
        return utilizacaoAvaliacaoFisicaDao;
    }

    public void setUtilizacaoAvaliacaoFisicaDao(UtilizacaoAvaliacaoFisicaInterfaceFacade utilizacaoAvaliacaoFisicaDao) {
        this.utilizacaoAvaliacaoFisicaDao = utilizacaoAvaliacaoFisicaDao;
    }

    public ContratoCondicaoPagamento getContratoCondicaoPagamentoDao() {
        contratoCondicaoPagamentoDao.setCon(con);
        return contratoCondicaoPagamentoDao;
    }

    public void setContratoCondicaoPagamentoDao(ContratoCondicaoPagamento contratoCondicaoPagamentoDao) {
        this.contratoCondicaoPagamentoDao = contratoCondicaoPagamentoDao;
    }


    public void setLogTotalPassDao(LogTotalPassInterfaceFacade logTotalPassDao) {
        this.logTotalPassDao = logTotalPassDao;
    }

    public LogTotalPassInterfaceFacade getLogTotalPassDao() {
        logTotalPassDao.setCon(con);
        return logTotalPassDao;
    }


    public PeriodoAcessoClienteInterfaceFacade getPeriodoAcessoDao() {
        periodoAcessoDao.setCon(con);
        return periodoAcessoDao;
    }

    public void setPeriodoAcessoDao(PeriodoAcessoClienteInterfaceFacade periodoAcessoDao) {
        this.periodoAcessoDao = periodoAcessoDao;
    }

    public UsuarioInterfaceFacade getUsuarioDao() {
        usuarioDao.setCon(con);
        return usuarioDao;
    }

    public void setUsuarioDao(UsuarioInterfaceFacade usuarioDao) {
        this.usuarioDao = usuarioDao;
    }

    public UsuarioEmailInterfaceFacade getUsuarioEmailDao() {
        usuarioEmailDao.setCon(con);
        return usuarioEmailDao;
    }

    public void setUsuarioEmailDao(UsuarioEmailInterfaceFacade usuarioEmailDao) {
        this.usuarioEmailDao = usuarioEmailDao;
    }

    public UsuarioTelefoneInterfaceFacade getUsuarioTelefoneDao() {
        usuarioTelefoneDao.setCon(con);
        return usuarioTelefoneDao;
    }

    public void setUsuarioTelefoneDao(UsuarioTelefoneInterfaceFacade usuarioTelefoneDao) {
        this.usuarioTelefoneDao = usuarioTelefoneDao;
    }

    public void setRiscoDao(Risco riscoDao) {
        this.riscoDao = riscoDao;
    }

    public Risco getRiscoDao() {
        riscoDao.setCon(con);
        return riscoDao;
    }

    public ControleAcesso getControleAcessoDao() {
        controleAcessoDao.setCon(con);
        return controleAcessoDao;
    }

    public void setControleAcessoDao(ControleAcesso controleAcessoDao) {
        this.controleAcessoDao = controleAcessoDao;
    }

    public Permissao getPermissaoDao() {
        permissaoDao.setCon(con);
        return permissaoDao;
    }

    public void setPermissaoDao(Permissao permissaoDao) {
        this.permissaoDao = permissaoDao;
    }

    public PessoaInterfaceFacade getPessoaDao() {
        pessoaDao.setCon(con);
        return pessoaDao;
    }

    public void setPessoaDao(PessoaInterfaceFacade pessoaDao) {
        this.pessoaDao = pessoaDao;
    }

    public CategoriaInterfaceFacade getCategoriaDao() {
        categoriaDao.setCon(con);
        return categoriaDao;
    }

    public void setCategoriaDao(CategoriaInterfaceFacade categoriaDao) {
        this.categoriaDao = categoriaDao;
    }

    public Endereco getEnderecoDao() {
        enderecoDao.setCon(con);
        return enderecoDao;
    }

    public void setEnderecoDao(Endereco enderecoDao) {
        this.enderecoDao = enderecoDao;
    }

    public Email getEmailDao() {
        emailDao.setCon(con);
        return emailDao;
    }

    public void setEmailDao(Email emailDao) {
        this.emailDao = emailDao;
    }

    public Telefone getTelefoneDao() {
        telefoneDao.setCon(con);
        return telefoneDao;
    }

    public void setTelefoneDao(Telefone telefoneDao) {
        this.telefoneDao = telefoneDao;
    }

    public ConfiguracaoSistemaInterfaceFacade getConfiguracaoDao() {
        configuracaoDao.setCon(con);
        return configuracaoDao;
    }

    public ConfiguracaoEmpresaTotemInterfaceFacade getConfiguracaoTotemDao() {
        configuracaoTotemDao.setCon(con);
        return configuracaoTotemDao;
    }

    public void setConfiguracaoTotemDao(ConfiguracaoEmpresaTotemInterfaceFacade configuracaoTotemDao) {
        this.configuracaoTotemDao = configuracaoTotemDao;
    }

    public void setConfiguracaoDao(ConfiguracaoSistemaInterfaceFacade configuracaoDao) {
        this.configuracaoDao = configuracaoDao;
    }

    public ContratoHorario getContratoHorarioDao() {
        contratoHorarioDao.setCon(con);
        return contratoHorarioDao;
    }

    public void setContratoHorarioDao(ContratoHorario contratoHorarioDao) {
        this.contratoHorarioDao = contratoHorarioDao;
    }

    public HorarioInterfaceFacade getHorarioDao() {
        horarioDao.setCon(con);
        return horarioDao;
    }

    public void setHorarioDao(HorarioInterfaceFacade horarioDao) {
        this.horarioDao = horarioDao;
    }

    public PlanoInterfaceFacade getPlanoDao() {
        planoDao.setCon(con);
        return planoDao;
    }

    public void setPlanoDao(PlanoInterfaceFacade planoDao) {
        this.planoDao = planoDao;
    }

    public PlanoExcecaoInterfaceFacade getPlanoExcecaoDao() {
        planoExcecaoDao.setCon(con);
        return planoExcecaoDao;
    }

    public void setPlanoExcecaoDao(PlanoExcecaoInterfaceFacade planoExcecaoDao) {
        this.planoExcecaoDao = planoExcecaoDao;
    }

    public ContratoModalidade getContratoModalidadeDao() {
        contratoModalidadeDao.setCon(con);
        return contratoModalidadeDao;
    }

    public void setContratoModalidadeDao(ContratoModalidade contratoModalidadeDao) {
        this.contratoModalidadeDao = contratoModalidadeDao;
    }

    public ContratoDuracao getContratoDuracaoDao() {
        contratoDuracaoDao.setCon(con);
        return contratoDuracaoDao;
    }

    public void setContratoDuracaoDao(ContratoDuracao contratoDuracaoDao) {
        this.contratoDuracaoDao = contratoDuracaoDao;
    }

    public HistoricoContratoInterfaceFacade getHistoricoContratoDao() {
        historicoContratoDao.setCon(con);
        return historicoContratoDao;
    }

    public void setHistoricoContratoDao(HistoricoContratoInterfaceFacade historicoContratoDao) {
        this.historicoContratoDao = historicoContratoDao;
    }

    public HorarioTurmaInterfaceFacade getHorarioTurmaDao() {
        horarioTurmaDao.setCon(con);
        return horarioTurmaDao;
    }

    public void setHorarioTurmaDao(HorarioTurmaInterfaceFacade horarioTurmaDao) {
        this.horarioTurmaDao = horarioTurmaDao;
    }

    public UsuarioPerfilAcesso getUsuarioPerfilAcessoDao() {
        usuarioPerfilAcessoDao.setCon(con);
        return usuarioPerfilAcessoDao;
    }

    public void setUsuarioPerfilAcessoDao(UsuarioPerfilAcesso usuarioPerfilAcessoDao) {
        this.usuarioPerfilAcessoDao = usuarioPerfilAcessoDao;
    }

    public LogControleUsabilidadeInterfaceFacade getLogControleUsabilidadeDao() {
        logControleUsabilidadeDao.setCon(con);
        return logControleUsabilidadeDao;
    }

    public void setLogControleUsabilidadeDao(LogControleUsabilidadeInterfaceFacade logControleUsabilidadeDao) {
        this.logControleUsabilidadeDao = logControleUsabilidadeDao;
    }

    public TipoColaboradorInterfaceFacade getTipoColaboradorDao() {
        tipoColaboradorDao.setCon(con);
        return tipoColaboradorDao;
    }

    public void setTipoColaboradorDao(TipoColaboradorInterfaceFacade tipoColaboradorDao) {
        this.tipoColaboradorDao = tipoColaboradorDao;
    }

    public SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDWDao() {
        situacaoClienteSinteticoDWDao.setCon(con);
        return situacaoClienteSinteticoDWDao;
    }

    public void setSituacaoClienteSinteticoDWDao(SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDao) {
        this.situacaoClienteSinteticoDWDao = situacaoClienteSinteticoDWDao;
    }

    public Calendar getInstanceCalendar(TimeZone tz) {
        return Calendario.hojeCalendar(tz);
    }

    public PessoaFotoLocalAcesso getPessoaFotoLocalAcessoDao() {
        pessoaFotoLocalAcessoDao.setCon(con);
        return pessoaFotoLocalAcessoDao;
    }

    public void setPessoaFotoLocalAcessoDao(PessoaFotoLocalAcesso pessoaFotoLocalAcessoDao) {
        this.pessoaFotoLocalAcessoDao = pessoaFotoLocalAcessoDao;
    }

    public ClienteClassificacao getClienteClassificacaoDao() {
        clienteClassificacaoDao.setCon(con);
        return clienteClassificacaoDao;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setClienteClassificacaoDao(ClienteClassificacao clienteClassificacaoDao) {
        this.clienteClassificacaoDao = clienteClassificacaoDao;
    }

    public DadosAcessoOffline getDadosAcessoOffline() {
        dadosAcessoOffline.setCon(con);
        return dadosAcessoOffline;
    }

    public void setDadosAcessoOffline(DadosAcessoOffline dadosAcessoOffline) {
        this.dadosAcessoOffline = dadosAcessoOffline;
    }

    public ZillyonWebFacade getZwFacade() {
        zwFacade.setCon(con);
        return zwFacade;
    }

    public void setZwFacade(ZillyonWebFacade zwFacade) {
        this.zwFacade = zwFacade;
    }
    
    public final void updateConfiguracaoSistema() throws Exception {
        if ((ultimoUpdate == 0) || (Calendario.hoje().getTime() - ultimoUpdate > 1000)) {
            configuracaoSistemaVO = getConfiguracaoDao().buscarPorCodigo(1, false,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            uteisEmail = new UteisEmail();
            configCRM = SuperControle.getConfiguracaoSMTPNoReply();
            uteisEmail.novo("", configCRM);

            Uteis.logar(null, "Configuração Sistema Atualizada para chave " + this.getKey());

            ultimoUpdate = Calendario.hoje().getTime();
        }
    }

    public AutorizacaoAcessoGrupoEmpresarialInterfaceFacade getAutorizacaoDao() {
        autorizacaoDao.setCon(con);
        return autorizacaoDao;
    }

    public void setAutorizacaoDao(AutorizacaoAcessoGrupoEmpresarialInterfaceFacade autorizacao) {
        this.autorizacaoDao = autorizacao;
    }

    public IntegracaoAcessoGrupoEmpresarialInterfaceFacade getIntegracaoDao() {
        integracaoDao.setCon(con);
        return integracaoDao;
    }

    public void setIntegracaoDao(IntegracaoAcessoGrupoEmpresarialInterfaceFacade integracaoDao) {
        this.integracaoDao = integracaoDao;
    }

    public UsuarioMovelInterfaceFacade getUsuarioMovelDao() {
        usuarioMovelDao.setCon(con);
        return usuarioMovelDao;
    }

    public void setUsuarioMovelDao(UsuarioMovelInterfaceFacade usuarioMovelDao) {
        this.usuarioMovelDao = usuarioMovelDao;
    }

    public ConfiguracaoSistemaCRMInterfaceFacade getConfiguracaoCRMDao() {
        configuracaoCRMDao.setCon(con);
        return configuracaoCRMDao;
    }

    public void setConfiguracaoCRMDao(ConfiguracaoSistemaCRMInterfaceFacade configuracaoCRMDao) {
        this.configuracaoCRMDao = configuracaoCRMDao;
    }

    public ReposicaoInterfaceFacade getReposicaoDao() {
        reposicaoDao.setCon(con);
        return reposicaoDao;
    }

    public void setReposicaoDao(ReposicaoInterfaceFacade reposicaoDao) {
        this.reposicaoDao = reposicaoDao;
    }

    public AulaDesmarcadaInterfaceFacade getAulaDesmarcadaDao() {
        aulaDesmarcadaDao.setCon(con);
        return aulaDesmarcadaDao;
    }

    public void setAulaDesmarcadaDao(AulaDesmarcadaInterfaceFacade aulaDesmarcadaDao) {
        this.aulaDesmarcadaDao = aulaDesmarcadaDao;
    }

    public void enviarEmail(final String[] emails, final String assunto,
            final StringBuilder conteudo) throws Exception {
        if (emails.length > 0) {
            updateConfiguracaoSistema();
            if (configCRM.getMailServer() != null && !configCRM.getMailServer().isEmpty()) {
                uteisEmail.enviarEmailN(emails, conteudo.toString(), assunto, "");
            }
        }
    }
    
    public void enviarEmail(final String[] emails, final String assunto,
            final StringBuilder conteudo,
            final File anexo) throws Exception {
        if (emails.length > 0) {
            updateConfiguracaoSistema();
            if (configCRM.getMailServer() != null && !configCRM.getMailServer().isEmpty()) {
                uteisEmail.addAnexo(anexo.getName(), anexo);
                uteisEmail.enviarEmailN(emails, conteudo.toString(), assunto, "");
            }
        }
    }

    public RecebivelAvulsoInterfaceFacade getRecebivelAvulso() {
        recebivelAvulso.setCon(con);
        return recebivelAvulso;
    }

    public void setRecebivelAvulso(RecebivelAvulsoInterfaceFacade recebivelAvulso) {
        this.recebivelAvulso = recebivelAvulso;
    }

    public AmbienteInterfaceFacade getAmbienteDao() {
        ambienteDao.setCon(con);
        return ambienteDao;
    }

    public void setAmbienteDao(AmbienteInterfaceFacade ambienteDao) {
        this.ambienteDao = ambienteDao;
    }

    public BancoInterfaceFacade getBancoDao() {
        bancoDao.setCon(con);
        return bancoDao;
    }

    public void setBancoDao(BancoInterfaceFacade bancoDao) {
        this.bancoDao = bancoDao;
    }

    public CategoriaProdutoInterfaceFacade getCategoriaProdutoDao() {
        categoriaProdutoDao.setCon(con);
        return categoriaProdutoDao;
    }

    public void setCategoriaProdutoDao(CategoriaProdutoInterfaceFacade categoriaProdutoDao) {
        this.categoriaProdutoDao = categoriaProdutoDao;
    }

    public CidadeInterfaceFacade getCidadeDao() {
        cidadeDao.setCon(con);
        return cidadeDao;
    }

    public void setCidadeDao(CidadeInterfaceFacade cidadeDao) {
        this.cidadeDao = cidadeDao;
    }

    public ClassificacaoInterfaceFacade getClassificacaoDao() {
        classificacaoDao.setCon(con);
        return classificacaoDao;
    }

    public void setClassificacaoDao(ClassificacaoInterfaceFacade classificacaoDao) {
        this.classificacaoDao = classificacaoDao;
    }

    public CondicaoPagamentoInterfaceFacade getCondicaoPagamentoDao() {
        condicaoPagamentoDao.setCon(con);
        return condicaoPagamentoDao;
    }

    public void setCondicaoPagamentoDao(CondicaoPagamentoInterfaceFacade condicaoPagamentoDao) {
        this.condicaoPagamentoDao = condicaoPagamentoDao;
    }

    public ContaCorrenteInterfaceFacade getContaCorrenteDao() {
        contaCorrenteDao.setCon(con);
        return contaCorrenteDao;
    }

    public void setContaCorrenteDao(ContaCorrenteInterfaceFacade contaCorrenteDao) {
        this.contaCorrenteDao = contaCorrenteDao;
    }

    public ConvenioCobrancaInterfaceFacade getConvenioCobrancaDao() {
        convenioCobrancaDao.setCon(con);
        return convenioCobrancaDao;
    }

    public void setConvenioCobrancaDao(ConvenioCobrancaInterfaceFacade convenioCobrancaDao) {
        this.convenioCobrancaDao = convenioCobrancaDao;
    }

    public DescontoInterfaceFacade getDescontoDao() {
        descontoDao.setCon(con);
        return descontoDao;
    }

    public void setDescontoDao(DescontoInterfaceFacade descontoDao) {
        this.descontoDao = descontoDao;
    }

    public FormaPagamentoInterfaceFacade getFormaPagamentoDao() {
        formaPagamentoDao.setCon(con);
        return formaPagamentoDao;
    }

    public void setFormaPagamentoDao(FormaPagamentoInterfaceFacade formaPagamentoDao) {
        this.formaPagamentoDao = formaPagamentoDao;
    }

    public GrauInstrucaoInterfaceFacade getGrauInstrucaoDao() {
        grauInstrucaoDao.setCon(con);
        return grauInstrucaoDao;
    }

    public void setGrauInstrucaoDao(GrauInstrucaoInterfaceFacade grauInstrucaoDao) {
        this.grauInstrucaoDao = grauInstrucaoDao;
    }

    public GrupoInterfaceFacade getGrupoDao() {
        grupoDao.setCon(con);
        return grupoDao;
    }

    public void setGrupoDao(GrupoInterfaceFacade grupoDao) {
        this.grupoDao = grupoDao;
    }

    public JustificativaOperacaoInterfaceFacade getJustificativaOperacaoDao() {
        justificativaOperacaoDao.setCon(con);
        return justificativaOperacaoDao;
    }

    public void setJustificativaOperacaoDao(JustificativaOperacaoInterfaceFacade justificativaOperacaoDao) {
        this.justificativaOperacaoDao = justificativaOperacaoDao;
    }

    public LogInterfaceFacade getLogDao() {
        logDao.setCon(con);
        return logDao;
    }

    public void setLogDao(LogInterfaceFacade logDao) {
        this.logDao = logDao;
    }

    public LogApiInterfaceFacade getLogApiDao() {
        logApiDao.setCon(con);
        return logApiDao;
    }


    public void setLogApiDao(LogApiInterfaceFacade logApiDao) {
        this.logApiDao = logApiDao;
    }

    public MetaFinanceiraEmpresaInterfaceFacade getMetaFinanceiraEmpresaDao() {
        metaFinanceiraEmpresaDao.setCon(con);
        return metaFinanceiraEmpresaDao;
    }

    public void setMetaFinanceiraEmpresaDao(MetaFinanceiraEmpresaInterfaceFacade metaFinanceiraEmpresaDao) {
        this.metaFinanceiraEmpresaDao = metaFinanceiraEmpresaDao;
    }

    public ModalidadeInterfaceFacade getModalidadeDao() {
        modalidadeDao.setCon(con);
        return modalidadeDao;
    }

    public void setModalidadeDao(ModalidadeInterfaceFacade modalidadeDao) {
        this.modalidadeDao = modalidadeDao;
    }

    public MovimentoContaCorrenteClienteInterfaceFacade getMovContaCorrenteClienteDao() {
        movContaCorrenteClienteDao.setCon(con);
        return movContaCorrenteClienteDao;
    }

    public void setMovContaCorrenteClienteDao(MovimentoContaCorrenteClienteInterfaceFacade movContaCorrenteClienteDao) {
        this.movContaCorrenteClienteDao = movContaCorrenteClienteDao;
    }

    public MovPagamentoInterfaceFacade getMovPagamentoDao() {
        movPagamentoDao.setCon(con);
        return movPagamentoDao;
    }

    public MovContaInterfaceFacade getMovContaDao() {
        movContaDao.setCon(con);
        return movContaDao;
    }

    public void setMovContaDao(MovContaInterfaceFacade movContaDao) {
        this.movContaDao = movContaDao;
    }

    public void setMovPagamentoDao(MovPagamentoInterfaceFacade movPagamentoDao) {
        this.movPagamentoDao = movPagamentoDao;
    }

    public NivelTurmaInterfaceFacade getNivelTurmaDao() {
        nivelTurmaDao.setCon(con);
        return nivelTurmaDao;
    }

    public void setNivelTurmaDao(NivelTurmaInterfaceFacade nivelTurmaDao) {
        this.nivelTurmaDao = nivelTurmaDao;
    }

    public OperadoraCartaoInterfaceFacade getOperadoraCartaoDao() {
        operadoraCartaoDao.setCon(con);
        return operadoraCartaoDao;
    }

    public void setOperadoraCartaoDao(OperadoraCartaoInterfaceFacade operadoraCartaoDao) {
        this.operadoraCartaoDao = operadoraCartaoDao;
    }

    public PaisInterfaceFacade getPaisDao() {
        paisDao.setCon(con);
        return paisDao;
    }

    public void setPaisDao(PaisInterfaceFacade paisDao) {
        this.paisDao = paisDao;
    }

    public ParentescoInterfaceFacade getParentescoDao() {
        parentescoDao.setCon(con);
        return parentescoDao;
    }

    public void setParentescoDao(ParentescoInterfaceFacade parentescoDao) {
        this.parentescoDao = parentescoDao;
    }

    public PerfilAcessoInterfaceFacade getPerfilAcessoDao() {
        perfilAcessoDao.setCon(con);
        return perfilAcessoDao;
    }

    public void setPerfilAcessoDao(PerfilAcessoInterfaceFacade perfilAcessoDao) {
        this.perfilAcessoDao = perfilAcessoDao;
    }

    public PerguntaInterfaceFacade getPerguntaDao() {
        perguntaDao.setCon(con);
        return perguntaDao;
    }

    public void setPerguntaDao(PerguntaInterfaceFacade perguntaDao) {
        this.perguntaDao = perguntaDao;
    }

    public PlanoTextoPadraoInterfaceFacade getPlanoTextoPadraoDao() {
        planoTextoPadraoDao.setCon(con);
        return planoTextoPadraoDao;
    }

    public void setPlanoTextoPadraoDao(PlanoTextoPadraoInterfaceFacade planoTextoPadraoDao) {
        this.planoTextoPadraoDao = planoTextoPadraoDao;
    }

    public ProdutoInterfaceFacade getProdutoDao() {
        produtoDao.setCon(con);
        return produtoDao;
    }

    public void setProdutoDao(ProdutoInterfaceFacade produtoDao) {
        this.produtoDao = produtoDao;
    }

    public ProfissaoInterfaceFacade getProfissaoDao() {
        profissaoDao.setCon(con);
        return profissaoDao;
    }

    public void setProfissaoDao(ProfissaoInterfaceFacade profissaoDao) {
        this.profissaoDao = profissaoDao;
    }

    public QuestionarioInterfaceFacade getQuestionarioDao() {
        questionarioDao.setCon(con);
        return questionarioDao;
    }

    public void setQuestionarioDao(QuestionarioInterfaceFacade questionarioDao) {
        this.questionarioDao = questionarioDao;
    }

    public TipoRemessaInterfaceFacade getTipoRemessaDao() {
        tipoRemessaDao.setCon(con);
        return tipoRemessaDao;
    }

    public void setTipoRemessaDao(TipoRemessaInterfaceFacade tipoRemessaDao) {
        this.tipoRemessaDao = tipoRemessaDao;
    }

    public TipoRetornoInterfaceFacade getTipoRetornoDao() {
        tipoRetornoDao.setCon(con);
        return tipoRetornoDao;
    }

    public void setTipoRetornoDao(TipoRetornoInterfaceFacade tipoRetornoDao) {
        this.tipoRetornoDao = tipoRetornoDao;
    }

    public TurmaInterfaceFacade getTurmaDao() {
        turmaDao.setCon(con);
        return turmaDao;
    }

    public void setTurmaDao(TurmaInterfaceFacade turmaDao) {
        this.turmaDao = turmaDao;
    }

    public VendaAvulsaInterfaceFacade getVendaAvulsaDao() {
        vendaAvulsaDao.setCon(con);
        return vendaAvulsaDao;
    }

    public void setVendaAvulsaDao(VendaAvulsaInterfaceFacade vendaAvulsaDao) {
        this.vendaAvulsaDao = vendaAvulsaDao;
    }

    public BalancoInterfaceFacade getBalancoDao() {
        balancoDao.setCon(con);
        return balancoDao;
    }

    public void setBalancoDao(BalancoInterfaceFacade balancoDao) {
        this.balancoDao = balancoDao;
    }

    public CardexInterfaceFacade getCardexDao() {
        cardexDao.setCon(con);
        return cardexDao;
    }

    public void setCardexDao(CardexInterfaceFacade cardexDao) {
        this.cardexDao = cardexDao;
    }

    public CompraInterfaceFacade getCompraDao() {
        compraDao.setCon(con);
        return compraDao;
    }

    public void setCompraDao(CompraInterfaceFacade compraDao) {
        this.compraDao = compraDao;
    }

    public ProdutoEstoqueInterfaceFacade getProdutoEstoqueDao() {
        produtoEstoqueDao.setCon(con);
        return produtoEstoqueDao;
    }

    public void setProdutoEstoqueDao(ProdutoEstoqueInterfaceFacade produtoEstoqueDao) {
        this.produtoEstoqueDao = produtoEstoqueDao;
    }

    public CupomFiscal getCupomFiscalDao() {
        cupomFiscalDao.setCon(con);
        return cupomFiscalDao;
    }

    public void setCupomFiscalDao(CupomFiscal cupomFiscalDao) {
        this.cupomFiscalDao = cupomFiscalDao;
    }

    public ReciboPagamento getReciboPagamentoDao() {
        reciboPagamentoDao.setCon(con);
        return reciboPagamentoDao;
    }

    public void setReciboPagamentoDao(ReciboPagamento reciboPagamentoDao) {
        this.reciboPagamentoDao = reciboPagamentoDao;
    }

    public ComposicaoInterfaceFacade getComposicaoDao() {
        composicaoDao.setCon(con);
        return composicaoDao;
    }

    public void setComposicaoDao(ComposicaoInterfaceFacade composicaoDao) {
        this.composicaoDao = composicaoDao;
    }

    public AtualizadorBD getAtualizadorBDDao() {
        atualizadorBDDao.setCon(con);
        return atualizadorBDDao;
    }

    public void setAtualizadorBDDao(AtualizadorBD atualizadorBDDao) {
        this.atualizadorBDDao = atualizadorBDDao;
    }

    public ConvenioDescontoInterfaceFacade getConvenioDescontoDao() {
        convenioDescontoDao.setCon(con);
        return convenioDescontoDao;
    }

    public void setConvenioDescontoDao(ConvenioDescontoInterfaceFacade convenioDescontoDao) {
        this.convenioDescontoDao = convenioDescontoDao;
    }

    public CarteirasRel getCarteirasRel() {
        carteirasRel.setCon(con);
        return carteirasRel;
    }

    public void setCarteirasRel(CarteirasRel carteirasRel) {
        this.carteirasRel = carteirasRel;
    }

    public ClienteObservacaoInterfaceFacade getClienteObservacaoDao() {
        clienteObservacaoDao.setCon(con);
        return clienteObservacaoDao;
    }

    public void setClienteObservacaoDao(ClienteObservacaoInterfaceFacade clienteObservacaoDao) {
        this.clienteObservacaoDao = clienteObservacaoDao;
    }

    public VinculoInterfaceFacade getVinculoDao() {
        vinculoDao.setCon(con);
        return vinculoDao;
    }

    public void setVinculoDao(VinculoInterfaceFacade vinculoDao) {
        this.vinculoDao = vinculoDao;
    }

    public TextoPadraoInterfaceFacade getTextoPadraoDao() {
        textoPadraoDao.setCon(con);
        return textoPadraoDao;
    }

    public void setTextoPadraoDao(TextoPadrao textoPadraoDao) {
        this.textoPadraoDao = textoPadraoDao;
    }

    public ComissaoGeralConfiguracaoInterfaceFacade getComissaoGeralConfiguracaoDao() {
        comissaoGeralConfiguracaoDao.setCon(con);
        return comissaoGeralConfiguracaoDao;
    }

    public void setComissaoGeralConfiguracaoDao(ComissaoGeralConfiguracaoInterfaceFacade comissaoGeralConfiguracaoDao) {
        this.comissaoGeralConfiguracaoDao = comissaoGeralConfiguracaoDao;
    }

    public GestaoNotasInterfaceFacade getGestaoNotasDao() {
        gestaoNotasDao.setCon(con);
        return gestaoNotasDao;
    }

    public LancamentoProdutoColetivoInterfaceFacade getLancamentoProdutoColetivoDao() {
        return lancamentoProdutoColetivoDao;
    }

    public void setLancamentoProdutoColetivoDao(LancamentoProdutoColetivoInterfaceFacade lancamentoProdutoColetivoDao) {
        this.lancamentoProdutoColetivoDao = lancamentoProdutoColetivoDao;
    }

    public void setGestaoNotasDao(GestaoNotasInterfaceFacade gestaoNotasDao) {
        this.gestaoNotasDao = gestaoNotasDao;
    }

    public PassivoInterfaceFacade getPassivoDao() {
        passivoDao.setCon(con);
        return passivoDao;
    }

    public NotaFiscalDeServicoInterfaceFacade getNotaFiscalServicoDao() {
        notaFiscalServicoDao.setCon(con);
        return notaFiscalServicoDao;
    }

    public void setNotaFiscalServicoDao(NotaFiscalDeServicoInterfaceFacade notaFiscalServicoDao) {
        this.notaFiscalServicoDao = notaFiscalServicoDao;
    }

    public void setPassivoDao(PassivoInterfaceFacade passivoDao) {
        this.passivoDao = passivoDao;
    }

    public void prepararConexao() throws Exception {
        try {
            if (preparando.containsKey(key))
                return;
            preparando.put(key, true);
            Statement stm = con.createStatement();
            stm.execute("select 1");
            stm = null;
            if (PropsService.isTrue(PropsService.debugJDBC)){
                Uteis.logarDebug(String.format("%s:%s => autoCommit => %s", con.toString(), con.getMetaData().getURL(),
                        con.getAutoCommit()));
            }
//            Uteis.logar(null, "Conexão atual em -> " + this.getClass().getSimpleName() + ": " + con.toString());
        } catch (Exception ex) {
            try {
                //
                Uteis.logar(null, Uteis.getDataComHora(new Date())
                        + " Erro na conexão (" + con + ") do PostegreSQL, "
                        + "vou tentar reiniciar a conexão do Banco de Dados."
                        + ex.getMessage());
                setCon(initConnection(key));
                Uteis.logar(null, "    -> Nova conexão: " + con.toString() + " obtida com sucesso!");
            } catch (Exception ex1) {
                throw new Exception("Não foi possível se recuperar de uma perda de conexão: " + ex1.getMessage());
            }
        } finally {
            preparando.remove(key);
        }
    }

    public PresencaInterfaceFacade getPresencaDao() {
        presencaDao.setCon(con);
        return presencaDao;
    }

    public void setPresencaDao(PresencaInterfaceFacade presencaDao) {
        this.presencaDao = presencaDao;
    }

    public ContratoOperacaoInterfaceFacade getContratoOperacaoDao() {
        contratoOperacaoDao.setCon(con);
        return contratoOperacaoDao;
    }

    public void setContratoOperacaoDao(ContratoOperacaoInterfaceFacade contratoOperacaoDao) {
        this.contratoOperacaoDao = contratoOperacaoDao;
    }

    public CreditoDCCService getCreditoDCC() {
        creditoDCC.setCon(con);
        return creditoDCC;
    }

    public void setCreditoDCC(CreditoDCCService creditoDCC) {
        this.creditoDCC = creditoDCC;
    }

    public IntegracaoCadastros getIntegracaoCadastrosDao() {
        integracaoCadastrosDao.setCon(con);
        return integracaoCadastrosDao;
    }

    public IntegracaoImportacao getIntegracaoImportacaoDao() {
        integracaoImportacaoDao.setCon(con);
        return integracaoImportacaoDao;
    }

    public ConviteInterfaceFacade getConviteDao() {
        conviteDao.setCon(con);
        return conviteDao;
    }

    public void setConviteDao(ConviteInterfaceFacade conviteDao) {
        this.conviteDao = conviteDao;
    }

    public void setIntegracaoCadastrosDao(IntegracaoCadastros integracaoCadastrosDao) {
        this.integracaoCadastrosDao = integracaoCadastrosDao;
    }

    public void setIntegracaoImportacaoDao(IntegracaoImportacao integracaoImportacaoDao) {
        this.integracaoImportacaoDao = integracaoImportacaoDao;
    }

    public FeriadoInterfaceFacade getFeriadoDao() {
        return feriadoDao;
    }

    public void setFeriadoDao(FeriadoInterfaceFacade feriadoDao) {
        this.feriadoDao = feriadoDao;
    }

    public ModeloMensagemInterfaceFacade getModeloMensagemDao() {
        return modeloMensagemDao;
    }

    public void setModeloMensagemDao(ModeloMensagemInterfaceFacade modeloMensagemDao) {
        this.modeloMensagemDao = modeloMensagemDao;
    }

    public EventoInterfaceFacade getEventoDao() {
        return eventoDao;
    }

    public void setEventoDao(EventoInterfaceFacade eventoDao) {
        this.eventoDao = eventoDao;
    }

    public ObjecaoInterfaceFacade getObjecaoDao() {
        return objecaoDao;
    }

    public void setObjecaoDao(ObjecaoInterfaceFacade objecaoDao) {
        this.objecaoDao = objecaoDao;
    }

    public GrupoColaboradorInterfaceFacade getGrupoColaboradorDao() {
        return grupoColaboradorDao;
    }

    public void setGrupoColaboradorDao(GrupoColaboradorInterfaceFacade grupoColaboradorDao) {
        this.grupoColaboradorDao = grupoColaboradorDao;
    }

    public IndicacaoInterfaceFacade getIndicacaoDao() {
        return indicacaoDao;
    }

    public void setIndicacaoDao(IndicacaoInterfaceFacade indicacaoDao) {
        this.indicacaoDao = indicacaoDao;
    }

    public AutorizacaoCobrancaClienteInterfaceFacade getAutorizacaoCobrancaDao() {
        return autorizacaoCobrancaDao;
    }

    public void setAutorizacaoCobrancaDao(AutorizacaoCobrancaClienteInterfaceFacade autorizacaoCobrancaDao) {
        this.autorizacaoCobrancaDao = autorizacaoCobrancaDao;
    }

    public ContratoRecorrenciaInterfaceFacade getContratoRecorrenciaDao() {
        return contratoRecorrenciaDao;
    }

    public void setContratoRecorrenciaDao(ContratoRecorrenciaInterfaceFacade contratoRecorrenciaDao) {
        this.contratoRecorrenciaDao = contratoRecorrenciaDao;
    }

    public HistoricoVinculoInterfaceFacade getHistoricoVinculoDao() {
        return historicoVinculoDao;
    }

    public void setHistoricoVinculoDao(HistoricoVinculoInterfaceFacade historicoVinculoDao) {
        this.historicoVinculoDao = historicoVinculoDao;
    }

    public DadosGameInterfaceFacade getDadosGameDao() {
        return dadosGameDao;
    }

    public void setDadosGameDao(DadosGameInterfaceFacade dadosGameDao) {
        this.dadosGameDao = dadosGameDao;
    }

    public DadosGerencialPmgInterfaceFacade getDadosGerenciaisDao() {
        return dadosGerenciaisDao;
    }

    public void setDadosGerenciaisDao(DadosGerencialPmgInterfaceFacade dadosGerenciaisDao) {
        this.dadosGerenciaisDao = dadosGerenciaisDao;
    }

    public MonitoramentoInterfaceFacade getMonitoramentoDao() {
        return monitoramentoDao;
    }

    public void setMonitoramentoDao(MonitoramentoInterfaceFacade monitoramentoDao) {
        this.monitoramentoDao = monitoramentoDao;
    }

    public DFSinteticoDetalheInterfaceFacade getDfDetalheDao() {
        return dfDetalheDao;
    }

    public void setDfDetalheDao(DFSinteticoDetalheInterfaceFacade dfDetalheDao) {
        this.dfDetalheDao = dfDetalheDao;
    }

    public ContratoDuracaoCreditoTreinoInterfaceFacade getContratoDuracaoCreditoTreino() {
        try {
            if (contratoDuracaoCreditoTreino != null && (contratoDuracaoCreditoTreino.getCon() == null || contratoDuracaoCreditoTreino.getCon().isClosed())) {
                Logger.getLogger(WebserviceControle.class.getName()).log(Level.INFO, "ContratoDuracaoCreditoTreinoInterfaceFacade => Conexão: Nula ou fechada");
                prepararConexao();
                contratoDuracaoCreditoTreino.setCon(getCon());
                Logger.getLogger(WebserviceControle.class.getName()).log(Level.INFO, "ContratoDuracaoCreditoTreinoInterfaceFacade => NOVA Conexão: " + contratoDuracaoCreditoTreino.getCon());
            }
        } catch (Exception ex) {
            Logger.getLogger(WebserviceControle.class.getName()).log(Level.SEVERE, null, ex);
            Uteis.logar("ContratoDuracaoCreditoTreinoInterfaceFacade contratoDuracaoCreditoTreino() contratoDuracaoCreditoTreino.getCon() => FALHOU: " + ex.getMessage());
        }
        return contratoDuracaoCreditoTreino;
    }

    public void setContratoDuracaoCreditoTreino(ContratoDuracaoCreditoTreinoInterfaceFacade contratoDuracaoCreditoTreino) {
        this.contratoDuracaoCreditoTreino = contratoDuracaoCreditoTreino;
    }

    public TurmasServiceInterface getTurmaService() {
        try {            
            if (turmaService != null && (turmaService.getCon() == null || turmaService.getCon().isClosed())) {
                Uteis.logar("TurmasServiceInterface getTurmaService() turmaService.getCon() => Conexão: " + turmaService.getCon() + " Nula ou fechada");
                prepararConexao();
                turmaService.setCon(getCon());
                Uteis.logar("TurmasServiceInterface getTurmaService() turmaService.getCon() => NOVA Conexão: " + turmaService.getCon());
            }
        } catch (Exception ex) {
            Logger.getLogger(WebserviceControle.class.getName()).log(Level.SEVERE, null, ex);
            Uteis.logar("TurmasServiceInterface getTurmaService() turmaService.getCon() => FALHOU: " + ex.getMessage());
        }
        return turmaService;
    }

    public MSFinanceiroService getAppGestorService() {
        try {
            if (msFinanceiroService != null && (msFinanceiroService.getCon() == null || msFinanceiroService.getCon().isClosed())) {
                Uteis.logar("AppGestorService getMsFinanceiroService() appGestorService.getCon() => Conexão: " + msFinanceiroService.getCon() + " Nula ou fechada");
                prepararConexao();
                msFinanceiroService.setCon(getCon());
                Uteis.logar("AppGestorService getMsFinanceiroService() appGestorService.getCon() => NOVA Conexão: " + msFinanceiroService.getCon());
            }
        } catch (Exception ex) {
            Logger.getLogger(WebserviceControle.class.getName()).log(Level.SEVERE, null, ex);
            Uteis.logar("MSFinanceiroService getAppGestorService() getAppGestorService.getCon() => FALHOU: " + ex.getMessage());
        }
        return msFinanceiroService;
    }

    public MSAlunoService getAppGestorAlunoService() {
        try {
            if (msAlunoService != null && (msAlunoService.getCon() == null || msAlunoService.getCon().isClosed())) {
                Uteis.logar("MSAlunoService getAppGestorAlunoService() getAppGestorAlunoService.getCon() => Conexão: " + msAlunoService.getCon() + " Nula ou fechada");
                prepararConexao();
                msAlunoService.setCon(getCon());
                Uteis.logar("MSAlunoService getAppGestorAlunoService() getAppGestorAlunoService.getCon() => NOVA Conexão: " + msAlunoService.getCon());
            }
        } catch (Exception ex) {
            Logger.getLogger(WebserviceControle.class.getName()).log(Level.SEVERE, null, ex);
            Uteis.logar("MSAlunoService getAppGestorAlunoService() getAppGestorAlunoService.getCon() => FALHOU: " + ex.getMessage());
        }
        return msAlunoService;
    }

    public SpiviService getSpiviService(){
        if (spiviService == null ) {
            spiviService = new SpiviService(getKey());
        }

        return spiviService;
    }

    public void setMSFinanceiroService(MSFinanceiroService msFinanceiroService) {
        this.msFinanceiroService = msFinanceiroService;
    }

    public void setMSAlunoService(MSAlunoService msAlunoService) {
        this.msAlunoService = msAlunoService;
    }

    public void setTurmaService(TurmasServiceInterface turmaService) {
        this.turmaService = turmaService;
    }

    public ContratoAssinaturaDigitalServiceInterface getContratoAssinaturaDigitalService() {
        return contratoAssinaturaDigitalService;
    }

    public void setContratoAssinaturaDigitalService(ContratoAssinaturaDigitalServiceInterface contratoAssinaturaDigitalService) {
        this.contratoAssinaturaDigitalService = contratoAssinaturaDigitalService;
    }

    public TipoConviteAulaExperimentalInterfaceFacade getTipoConviteAulaExperimental() {
        return tipoConviteAulaExperimental;
    }

    public void setTipoConviteAulaExperimental(TipoConviteAulaExperimentalInterfaceFacade tipoConviteAulaExperimental) {
        this.tipoConviteAulaExperimental = tipoConviteAulaExperimental;
    }

    public ConviteAulaExperimentalServiceInterface getConviteAulaExperimentalService() {
        return conviteAulaExperimentalService;
    }

    public void setConviteAulaExperimentalService(ConviteAulaExperimentalServiceInterface conviteAulaExperimentalService) {
        this.conviteAulaExperimentalService = conviteAulaExperimentalService;
    }

    public BoletoServiceInterface getBoletoServiceInterface() {
        return boletoServiceInterface;
    }

    public void setBoletoServiceInterface(BoletoServiceInterface boletoServiceInterface) {
        this.boletoServiceInterface = boletoServiceInterface;
    }

    public SocialMailingInterfaceFacade getSocialMailingDao() {
        socialMailingDao.setCon(con);
        return socialMailingDao;
    }

    public void setSocialMailingDao(SocialMailingInterfaceFacade socialMailingDao) {
        this.socialMailingDao = socialMailingDao;
    }

    public NotificacaoUsuarioInterfaceFacade getNotificacaoUsuarioDao() {
        return notificacaoUsuarioDao;
    }

    public void setNotificacaoUsuarioDao(NotificacaoUsuarioInterfaceFacade notificacaoUsuarioDao) {
        this.notificacaoUsuarioDao = notificacaoUsuarioDao;
    }

    public BaseAcessoWS getBaseAcessoWS() {
        return baseAcessoWS;
}

    public void setBaseAcessoWS(BaseAcessoWS baseAcessoWS) {
        this.baseAcessoWS = baseAcessoWS;
    }

    public ControleCreditoTreinoInterfaceFacade getControleCreditoTreino() {
        return controleCreditoTreino;
    }

    public void setControleCreditoTreino(ControleCreditoTreinoInterfaceFacade controleCreditoTreino) {
        this.controleCreditoTreino = controleCreditoTreino;
    }

    public EstadoInterfaceFacade getEstadoDao() {
        estadoDao.setCon(con);
        return estadoDao;
    }

    public void setEstadoDao(EstadoInterfaceFacade estadoDao) {
        this.estadoDao = estadoDao;
    }
    
    public HistoricoPontosInterfaceFacade getHistoricoPontos() {
        historicoPontos.setCon(con);
        return historicoPontos;
    }

    public void setHistoricoPontos(HistoricoPontosInterfaceFacade historicoPontos) {
        this.historicoPontos = historicoPontos;
    }
    
    public BrindeInterfaceFacade getBrinde() {
        brinde.setCon(con);
        return brinde;
    }

    public void setBrinde(BrindeInterfaceFacade brinde) {
        this.brinde = brinde;
    }

    public MatriculaAlunoHorarioTurmaInterfaceFacade getMatriculaAlunoHorarioTurmaDAO() {
        matriculaAlunoHorarioTurmaDAO.setCon(con);
        return matriculaAlunoHorarioTurmaDAO;
    }

    public void setMatriculaAlunoHorarioTurmaDAO(MatriculaAlunoHorarioTurmaInterfaceFacade matriculaAlunoHorarioTurmaDAO) {
        this.matriculaAlunoHorarioTurmaDAO = matriculaAlunoHorarioTurmaDAO;
    }
    
    public IntegracaoRDServiceInterface getIntegracaoRDService() {
        return integracaoRDService;
    }

    public void setIntegracaoRDService(IntegracaoRDServiceInterface integracaoRDService) {
        this.integracaoRDService = integracaoRDService;
    }

    public ClienteTokenInterfaceFacade getClienteTokenDao() {
        clienteTokenDao.setCon(con);
        return clienteTokenDao;
}

    public void setClienteTokenDao(ClienteTokenInterfaceFacade clienteTokenDao) {
        this.clienteTokenDao = clienteTokenDao;
    }

    public TokenService getTokenService() {
        tokenService.setCon(con);
        return tokenService;
    }

    public void setTokenService(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    public NFSeEmitida getNfSeEmitida() {
        nfSeEmitida.setCon(con);
        return nfSeEmitida;
    }

    public void setNfSeEmitida(NFSeEmitida nfSeEmitida) {
        this.nfSeEmitida = nfSeEmitida;
    }

    public IntegracaoBuzzLeadServiceInterface getIntegracaoBuzzService() {
        return integracaoBuzzService;
    }

    public void setIntegracaoBuzzService(IntegracaoBuzzLeadServiceInterface integracaoBuzzService) {
        this.integracaoBuzzService = integracaoBuzzService;
    }

    public IntegracaoLeadGenericaServiceInterface getIntegracaoLeadGenericaService() {
        return integracaoLeadGenericaService;
    }

    public void setIntegracaoLeadGenericaService(IntegracaoLeadGenericaServiceInterface integracaoLeadGenericaService) {
        this.integracaoLeadGenericaService = integracaoLeadGenericaService;
    }

    public NotaFiscalConsumidorEletronicaInterfaceFacade getNotaFiscalConsumidorEletronica() {
        notaFiscalConsumidorEletronica.setCon(con);
        return notaFiscalConsumidorEletronica;
    }

    public void setNotaFiscalConsumidorEletronica(NotaFiscalConsumidorEletronicaInterfaceFacade notaFiscalConsumidorEletronica) {
        this.notaFiscalConsumidorEletronica = notaFiscalConsumidorEletronica;
    }
    public ConfirmacaoEmailCompraInterfaceFacede getConfirmacaoEmailCompra() {
        confirmacaoEmailCompra.setCon(con);
        return confirmacaoEmailCompra;
    }

    public void setConfirmacaoEmailCompra(ConfirmacaoEmailCompraInterfaceFacede confirmacaoEmailCompra) {
        this.confirmacaoEmailCompra = confirmacaoEmailCompra;
    }

    public AtestadoInterfaceFacade getAtestadoDao() {
        atestadoDao.setCon(con);
        return atestadoDao;
    }

    public void setAtestadoDao(AtestadoInterfaceFacade atestadoDao) {
        this.atestadoDao = atestadoDao;
    }

    public PinPadInterfaceFacade getPinPadDao() {
        pinPadDao.setCon(con);
        return pinPadDao;
    }

    public void setPinPadDao(PinPadInterfaceFacade pinPadDao) {
        this.pinPadDao = pinPadDao;
    }

    public GympassInterfaceFacade getGympass() {
        gympass.setCon(con);
        return gympass;
    }

    public void setGympass(GympassInterfaceFacade gympass) {
        this.gympass = gympass;
    }


    public TotalpassInterfaceFacade getTotalpassDao() {
        totalpassDao.setCon(con);
        return totalpassDao;
    }

    public QuestionarioClienteInterfaceFacade getQuestionarioClienteDao() {
        questionarioClienteDao.setCon(con);
        return questionarioClienteDao;
    }

    public void setQuestionarioClienteDao(QuestionarioClienteInterfaceFacade questionarioClienteDao) {
        this.questionarioClienteDao = questionarioClienteDao;
    }

    public void setTotalpassDao(TotalpassInterfaceFacade totalpassDao) {
        this.totalpassDao = totalpassDao;
    }

    public FeedGestaoInterfaceFacade getFeedGestaoDao() throws Exception{
        if (feedGestaoDao == null) {
            feedGestaoDao = new FeedGestao(con);
        }
        feedGestaoDao.setCon(con);
        return feedGestaoDao;
    }

    public OAuth2RDStationInterfaceFacade getoAuth2RDStationInterfaceFacade() {
        return oAuth2RDStationInterfaceFacade;
    }

    public void setoAuth2RDStationInterfaceFacade(OAuth2RDStationInterfaceFacade oAuth2RDStationInterfaceFacade) {
        this.oAuth2RDStationInterfaceFacade = oAuth2RDStationInterfaceFacade;
    }

    public BoletoPJBankInterfaceFacade getBoletoPJBankInterfaceFacade() throws Exception {
        if (boletoPJBankInterfaceFacade == null) {
            boletoPJBankInterfaceFacade = new BoletoPJBank(con);
        }
        boletoPJBankInterfaceFacade.setCon(con);
        return boletoPJBankInterfaceFacade;
    }

    public void setBoletoPJBankInterfaceFacade(BoletoPJBankInterfaceFacade boletoPJBankInterfaceFacade) {
        this.boletoPJBankInterfaceFacade = boletoPJBankInterfaceFacade;
    }

    public TipoContaInterfaceFacade getTipoContaDao() throws Exception {
        if (tipoContaDao == null) {
            tipoContaDao = new TipoConta(con);
        }
        tipoContaDao.setCon(con);
        return tipoContaDao;
    }

    public void setTipoContaDao(TipoContaInterfaceFacade tipoContaDao) {
        this.tipoContaDao = tipoContaDao;
    }

    public ContaInterfaceFacade getContaDao() throws Exception {
        if (contaDao == null) {
            contaDao = new Conta(con);
        }
        contaDao.setCon(con);
        return contaDao;
    }

    public void setContaDao(ContaInterfaceFacade contaDao) {
        this.contaDao = contaDao;
    }

    public ControleTaxaPersonalInterfaceFacade getTaxapersonalDao() throws Exception {
        if(taxapersonalDao == null){
            taxapersonalDao = new ControleTaxaPersonal(con);
        }
        taxapersonalDao.setCon(con);
        return taxapersonalDao;
    }

    public void setTaxapersonalDao(ControleTaxaPersonalInterfaceFacade taxapersonalDao) {
        this.taxapersonalDao = taxapersonalDao;
    }

    public AutorizacaoCobrancaColaboradorInterfaceFacade getAutorizacaoCobrancaColaboradorDao() throws Exception {
        if (autorizacaoCobrancaColaboradorDao == null){
            autorizacaoCobrancaColaboradorDao = new AutorizacaoCobrancaColaborador(con);
        }
        autorizacaoCobrancaColaboradorDao.setCon(con);
        return autorizacaoCobrancaColaboradorDao;
    }

    public void setAutorizacaoCobrancaColaboradorDao(AutorizacaoCobrancaColaboradorInterfaceFacade autorizacaoCobrancaColaboradorDao) {
        this.autorizacaoCobrancaColaboradorDao = autorizacaoCobrancaColaboradorDao;
    }


    public ContratoAssinaturaDigitalInterfaceFacade getContratoAssinaturaDigitalDao() throws Exception {
        contratoAssinaturaDigitalDao.setCon(con);
        return contratoAssinaturaDigitalDao;
    }

    public void setContratoAssinaturaDigitalDao(ContratoAssinaturaDigitalInterfaceFacade contratoAssinaturaDigitalDao) {
        this.contratoAssinaturaDigitalDao = contratoAssinaturaDigitalDao;
    }

    public PessoaAnexoInterfaceFacade getPessoaAnexoDao() throws Exception {
        pessoaAnexoDao = new PessoaAnexo(con);
        return pessoaAnexoDao;
    }

    public void setPessoaAnexoDao(PessoaAnexoInterfaceFacade pessoaAnexoDao) {
        this.pessoaAnexoDao = pessoaAnexoDao;
    }

    public AulaAvulsaDiariaInterfaceFacade getAulaAvulsaDiariaDao() throws Exception {
        if (aulaAvulsaDiariaDao == null) {
            aulaAvulsaDiariaDao = new AulaAvulsaDiaria(con);
        }
        aulaAvulsaDiariaDao.setCon(con);
        return aulaAvulsaDiariaDao;
    }

    public void setAulaAvulsaDiariaDao(AulaAvulsaDiariaInterfaceFacade aulaAvulsaDiariaDao) {
        this.aulaAvulsaDiariaDao = aulaAvulsaDiariaDao;
    }

    public BoletoInterfaceFacade getBoletoInterfaceFacade() throws Exception {
        if (boletoInterfaceFacade == null) {
            boletoInterfaceFacade = new Boleto(con);
        }
        boletoInterfaceFacade.setCon(con);
        return boletoInterfaceFacade;
    }

    public void setBoletoInterfaceFacade(BoletoInterfaceFacade boletoInterfaceFacade) {
        this.boletoInterfaceFacade = boletoInterfaceFacade;
    }


    public OptinInterfaceFacade getOptinDao() throws Exception {
        if(optinDao == null) {
            optinDao = new Optin(con);
        }
        optinDao.setCon(con);
        return optinDao;
    }

    public void setOptinDao(OptinInterfaceFacade optinDao) {
        this.optinDao = optinDao;
    }

    public LogIntegracoesInterfaceFacade getLogintegracoesDao() throws Exception {
        if(logintegracoesDao == null) {
            logintegracoesDao = new LogIntegracoes(con);
        }
        logintegracoesDao.setCon(con);
        return logintegracoesDao;
    }

    public void setLogintegracoesDao(LogIntegracoesInterfaceFacade logintegracoesDao) {
        this.logintegracoesDao = logintegracoesDao;
    }

    public LocalImpressaoInterfaceFacade getLocalImpressao() throws Exception {
        if (localImpressao == null) {
            localImpressao = new LocalImpressao(con);
        }
        localImpressao.setCon(con);
        return localImpressao;
    }

    public CarteirinhaClienteInterfaceFacade getCarteirinhaCliente() throws Exception {
        if (carteirinhaCliente == null) {
            carteirinhaCliente = new CarteirinhaCliente(con);
        }
        carteirinhaCliente.setCon(con);
        return carteirinhaCliente;
    }
}


