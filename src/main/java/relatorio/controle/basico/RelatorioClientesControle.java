package relatorio.controle.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.to.ClientesRelConsultaTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.GrupoSituacaoEnum;
import br.com.pactosolucoes.enumeradores.SituacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoFiltroData;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import org.richfaces.component.UIDataTable;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ClienteRelatorioTO;
import relatorio.negocio.comuns.basico.ResumoModalidadeTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Classe de controle responsável por implementar a interação entre os componentes JSF da página relatorioCliente.jsp. Implemtação da camada
 * controle (Backing Bean).
 *
 * <AUTHOR>
 */
public class RelatorioClientesControle extends SuperControleRelatorio {

    // -----------------INICIO - ATRIBUTOS --------------------------- //
    private static final String FORMATO_DIA_SEMANA = "EEEE (dd/MM/yy)";
    private static final String ATIVO = "AT";
    private static final String INATIVO = "NA";
    private List<SelectItem> semanas = new ArrayList<SelectItem>();
    private List<ClienteRelatorioTO> listaRelatorioClientes;
    private Integer tipoFiltroData;
    private ClientesRelConsultaTO filtrosConsulta;
    private String filtros;
    private String filtrosSituacao;
    private String filtrosColaborador;
    private String filtrosModalidade;
    private String filtrosEmpresa;
    private String filtrosModalidadeDescricao;
    private String filtrosPlano;
    private String filtrosPlanoDescricao;
    private String filtrosHorario;
    private String filtrosHorarioDescricao;
    private String filtrosDuracao;
    private String filtrosSituacaoContrato;
    //-------------- nome da coluna que será agrupada
    private String colunaAgrupada;
    //--------------flags das colunas da tabela
    private boolean colMatricula;
    private boolean colNome;
    private boolean colSituacao;
    private boolean colEmpresa;
    private boolean colVinculo;
    private boolean colPlano;
    private boolean colContrato;
    private boolean colModalidade;
    private boolean colDuracao;
    private boolean colHorario;
    private boolean colInicio;
    private boolean colVence;
    private boolean colValor;
    private boolean colValorModalidade;
    private boolean colDataLancamento;
    private boolean colCategoriaCliente;
    private boolean colNivelTurma;
    private boolean mostrarConteudo;
    private Integer totalCliente;
    private Integer totalContrato;
    private Double totalValor;
    private Double totalReceita;
    private List<Map<String, String>> totalModalidade;
    private List<ResumoModalidadeTO> resumoModalidades;
    private Boolean planoAtivo;
    private Boolean planoInativo;
    private Boolean planoComPermissaoVenda;
    private Boolean colaboradorAtivo;
    private Boolean colaboradorInativo;
    private Boolean consultaPaginada;
    private Integer nrPagina;
    private Integer nrTotalConsulta;

    private Boolean contratoMatricula;
    private Boolean contratoRematricula;
    private Boolean contratoRenovacao;

    private Boolean contratoTransferido;
    private Double totalCompetencia;

    public RelatorioClientesControle() { }

    public void inicializar() {
        inicializarBean();
        try {
            validarPermissaoRelatorioCliente();
            // pesquisar empresas
            montarListaEmpresas();
            inicializarFlags();
            if (getListaEmpresas().size() == 1) {
                // se só existe uma empresa:
                //setar o codigo da pesquisa como o código da unica empresa existente
                this.getFiltrosConsulta().setCodigoEmpresa((Integer) getListaEmpresa().get(0).getValue());
            }
            inicializarEmpresa();
            this.setMostrarConteudo(false);
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            setMensagem(e.getMessage());
            e.printStackTrace();
        }
    }

    public static String getFormatoDiaSemana() {
        return FORMATO_DIA_SEMANA;
    }

    public boolean isColCategoriaCliente() {
        return colCategoriaCliente;
    }

    public void setColCategoriaCliente(boolean colCategoriaCliente) {
        this.colCategoriaCliente = colCategoriaCliente;
    }

    // ----------------- FIM - ATRIBUTOS --------------------------- //
    public void inicializarFlags() {
        this.setColMatricula(true);

        this.setColNome(true);
        this.setColSituacao(true);
        this.setColCategoriaCliente(false);
        this.setColDuracao(true);
        this.setColInicio(true);
        this.setColModalidade(true);
        this.setColEmpresa(true);
        this.setColVinculo(true);
        this.setColValor(true);
        this.setColVence(true);
        this.setColVinculo(true);
        this.setColContrato(true);
        //as colunas lancamento, plano, valor modalidade e horario
        //vão estar por default desabilitadas
        this.setColDataLancamento(false);
        this.setColValorModalidade(false);
        this.setColPlano(false);
        this.setColHorario(false);
        this.setColNivelTurma(false);

    }

    /**
     * Responsável por fazer a consulta setando filtros marcados em tela
     *
     * <AUTHOR>
     * 07/04/2011
     */
    public void consultarComFiltros() {
        try {
            inicializarFiltroSituacaoContrato();
            this.setMensagem("");
            if (UteisValidacao.emptyNumber(this.getFiltrosConsulta().getCodigoEmpresa()) && !permissao("ConsultarInfoTodasEmpresas")) {
                throw new ValidacaoException("Selecione a empresa!");
            } else {
                setarFiltros();
                Map<String, Object> consultaMap = getFacade().getRelatorioCliente().montarRelatorioClientes(this.getFiltrosConsulta());
                Object consultaObj = consultaMap.get("lista");
                List<ClienteRelatorioTO> consulta = (List<ClienteRelatorioTO>) consultaObj;
                Ordenacao.ordenarLista(consulta, "nome");
                this.setListaRelatorioClientes(consulta);
                this.setNrTotalConsulta(this.getListaConsulta().size());
                setarTotais(consultaMap);
                if (this.getListaRelatorioClientes() == null || this.getListaRelatorioClientes().isEmpty()) {
                    this.setMostrarConteudo(false);
                } else {
                    this.setMostrarConteudo(true);
                }
            }
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }

    /**
     * Responsável por setar os filtros da consulta
     *
     * @throws Exception
     * <AUTHOR>
     * 07/04/2011
     */
    public void setarFiltros() throws Exception {
        //setar datas se o tipo escolhido for periodo de semanas
        if (this.getTipoFiltroData().equals(TipoFiltroData.MES_SEMANA.getCodigo())) {
            setarDatasFiltroSemana();
        } else if (this.getTipoFiltroData().equals(TipoFiltroData.DATA.getCodigo())) {
            this.getFiltrosConsulta().setDataInicial(this.getFiltrosConsulta().getData());
            this.getFiltrosConsulta().setDataFinal(this.getFiltrosConsulta().getData());
        }

        this.getFiltrosConsulta().setNomesColaboradores(new ArrayList<String>());
        this.getFiltrosConsulta().setNomesHorarios(new ArrayList<String>());
        this.getFiltrosConsulta().setNomesModalidades(new ArrayList<String>());
        this.getFiltrosConsulta().setNomesPlanos(new ArrayList<String>());
        this.getFiltrosConsulta().setNomesSituacoes(new ArrayList<String>());
        this.getFiltrosConsulta().setListaDuracao(new ArrayList<String>());

        addNomeFiltro(this.getFiltrosConsulta().getNomesColaboradores(), this.getFiltrosColaborador());
        addNomeFiltro(this.getFiltrosConsulta().getNomesHorarios(), this.getFiltrosHorario());
        addNomeFiltro(this.getFiltrosConsulta().getNomesModalidades(), this.getFiltrosModalidade());
        addNomeFiltro(this.getFiltrosConsulta().getNomesPlanos(), this.getFiltrosPlano());
        addNomeFiltro(this.getFiltrosConsulta().getNomesSituacoes(), this.getFiltrosSituacao());
        addNomeFiltro(this.getFiltrosConsulta().getListaDuracao(), this.getFiltrosDuracao());

    }

    private void addNomeFiltro(List<String> lista, String superString) {
        String[] listaParams = superString.split(";");
        for (String param : listaParams) {
            if (!param.equals("")) {
                lista.add(param);
            }
        }

    }

    /**
     * Responsável por
     *
     * <AUTHOR>
     * 18/04/2011
     */
    public void mudarPaginacao() {
        consultarComFiltros();
    }

    /**
     * Responsável por inicializar filtros da consulta
     *
     * @throws Exception
     * <AUTHOR> 04/04/2011
     */
    public void inicializarEmpresa() throws Exception {
        this.getFiltrosConsulta().setFiltrarDataLancamento(false);
        inicializarFiltroConsultor();
        inicializarFiltroPlano();
    }

    /**
     * Responsável por
     *
     * @param mapaResult
     * <AUTHOR>
     * 12/04/2011
     */
    @SuppressWarnings("unchecked")
    private void setarTotais(Map<String, Object> mapaResult) {
        //totais
        this.setTotalCliente((Integer) mapaResult.get("totalCliente"));
        this.setTotalContrato((Integer) mapaResult.get("totalContrato"));
        this.setTotalValor((Double) mapaResult.get("totalValor"));
        this.setTotalReceita((Double) mapaResult.get("totalReceita"));
        this.setTotalCompetencia((Double) mapaResult.get("totalCompetencia"));
        Map<String, Map<String, Object>> totais = (Map<String, Map<String, Object>>) mapaResult.get("totalModalidade");
        resumoModalidades = new ArrayList<ResumoModalidadeTO>();
        //arranjar a exibição dos totais de modalidade
        this.setTotalModalidade(new ArrayList<Map<String, String>>());
        for (String nomesModalidade : totais.keySet()) {
            if (!UteisValidacao.emptyString(nomesModalidade)) {
                Map<String, Object> contadores = totais.get(nomesModalidade);
                Map<String, String> total = new HashMap<String, String>();
                total.put("modalidade", nomesModalidade);
                total.put("faturamento", Formatador.formatarValorMonetario((Double) contadores.get("faturamento")));
                total.put("total", Formatador.formatarValorMonetario((Double) contadores.get("valor")));
                total.put("contratos", contadores.get("contrato").toString());
                total.put("clientes", contadores.get("cliente").toString());
                ResumoModalidadeTO resumo = new ResumoModalidadeTO(nomesModalidade,
                        contadores.get("cliente").toString(),
                        contadores.get("contrato").toString(),
                        Formatador.formatarValorMonetario((Double) contadores.get("valor")),
                        Formatador.formatarValorMonetario((Double) contadores.get("faturamento")));
                resumoModalidades.add(resumo);
                this.getTotalModalidade().add(total);
            }
        }
    }

    /**
     * Retorna a lista de tipos de filtro de datas
     *
     * @return List<SelectItem>
     * @throws Exception
     * <AUTHOR>
     */
    public List<SelectItem> getTiposFiltroData() throws Exception {
        // criar lista para retorno
        final List<SelectItem> itens = new ArrayList<SelectItem>();
        // percorrer os valores do enumerador TipoFiltroData
        for (TipoFiltroData tipoFiltroData : TipoFiltroData.values()) {
            // adicionar o tipo de filtro na lista de retorno
            itens.add(new SelectItem(tipoFiltroData.getCodigo(), tipoFiltroData.getDescricao()));
        }
        // retornar a lista
        return itens;
    }

    /**
     * @return O campo tipoFiltroData.
     */
    public Integer getTipoFiltroData() {
        if (this.tipoFiltroData == null) {
            this.tipoFiltroData = 1; // filtro de data
        }
        return this.tipoFiltroData;
    }

    /**
     * @param tipoFiltroData O novo valor de tipoFiltroData.
     */
    public void setTipoFiltroData(final Integer tipoFiltroData) {
        this.tipoFiltroData = tipoFiltroData;
    }

    @SuppressWarnings("unchecked")
    public void inicializarFiltroConsultor() throws Exception {
        this.setFiltrosColaborador("");
        this.getFiltrosConsulta().setColaboradores(new ArrayList<Map<String, Object>>());

        if (this.getColaboradorAtivo()) {
            for (TipoColaboradorEnum tipo : TipoColaboradorEnum.values()) {
                // mapa com chave tipo colaborador e valor lista de colaboradores
                Map<String, Object> mapa = new HashMap<String, Object>();
                List<ColaboradorVO> lista = new ArrayList<ColaboradorVO>();
                // consultar colaboradores
                if (this.getColaboradorInativo()) {
                    lista = getFacade().getColaborador().consultarPorNomeTipoColaborador("",
                            getFiltrosConsulta().getCodigoEmpresa(), false, false, Uteis.NIVELMONTARDADOS_MINIMOS, tipo);
                } else if (!tipo.getSigla().equals(TipoColaboradorEnum.FORNECEDOR.getSigla()) && !tipo.getSigla().equals(TipoColaboradorEnum.COORDENADOR.getSigla()) && !tipo.getSigla().equals(TipoColaboradorEnum.ESTUDIO.getSigla())) {
                    // String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados
                    lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(tipo, ATIVO, getFiltrosConsulta().getCodigoEmpresa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
                if (!lista.isEmpty()) {
                    mapa.put("tipo", tipo.getDescricao());
                    mapa.put("lista", lista);
                    // adicionar na lista
                    this.getFiltrosConsulta().getColaboradores().add(mapa);
                }
            }
        }
        if (this.getColaboradorInativo()) {
            // mapa com chave tipo colaborador e valor lista de colaboradores
            Map<String, Object> mapa = new HashMap<String, Object>();
            // String situacao, Integer empresa, boolean controlarAcesso, int nivelMontarDados
            List<ColaboradorVO> lista = getFacade().getColaborador().consultarPorSituacao(INATIVO, getFiltrosConsulta().getCodigoEmpresa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (!lista.isEmpty()) {
                mapa.put("tipo", "Inativos");
                mapa.put("lista", lista);
                // adicionar na lista
                this.getFiltrosConsulta().getColaboradores().add(mapa);
            }
        }
        ordenarFiltrosConsultor();
    }

    @SuppressWarnings("unchecked")
    private void ordenarFiltrosConsultor() {
        //ordenar a consulta pelo numero de colaboradores de cada grupo
        Collections.sort(this.getFiltrosConsulta().getColaboradores(),
                new Comparator() {

                    public int compare(Object esquerda, Object direita) {
                        Map<String, Object> mapaD = (Map<String, Object>) direita;
                        Map<String, Object> mapaE = (Map<String, Object>) esquerda;
                        List<ColaboradorVO> listaD = (List<ColaboradorVO>) mapaD.get("lista");
                        List<ColaboradorVO> listaE = (List<ColaboradorVO>) mapaE.get("lista");
                        Integer sizeD = listaD.size();
                        Integer sizeE = listaE.size();
                        return sizeD.compareTo(sizeE);
                    }
                });
    }

    /**
     * Responsável por consultar os planos que serão utilizados como filtros da consulta
     *
     * @throws Exception
     * <AUTHOR>
     * 13/04/2011
     */
    @SuppressWarnings("unchecked")
    public void inicializarFiltroPlano() throws Exception {
        limparFiltrosPlano();
        this.getFiltrosConsulta().setPlanos(new ArrayList<>());
        Boolean ativos = null;
        //só planos ativos
        if (this.getPlanoAtivo()) {
            ativos = this.getPlanoAtivo();
        } else //só planos inativos
            if (this.getPlanoInativo()) {
                ativos = this.getPlanoAtivo();
            }
        //duas opcoes marcadas = consultar todos planos
        if (this.getPlanoAtivo() && this.getPlanoInativo()) {
            this.getFiltrosConsulta().setPlanos(getFacade().getPlano().consultarPorCodigoEmpresa(getFiltrosConsulta().getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_TODOS));
        } else {
            //pelo menos uma marcada = consultar pela opção
            if (this.getPlanoAtivo() || this.getPlanoInativo()) {
                this.getFiltrosConsulta().setPlanos(getFacade().getPlano().consultarIngressoAte(Calendario.hoje(), ativos, this.getFiltrosConsulta().getCodigoEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            }
        }

        if (getPlanoComPermissaoVenda()) {
            String filtroSelecionado = "NENHUM";
            if (getPlanoAtivo() && getPlanoInativo()) {
                filtroSelecionado = "TODOS";
            } else if (getPlanoAtivo()) {
                filtroSelecionado = "ATIVO";
            } else if (getPlanoInativo()) {
                filtroSelecionado = "INATIVO";
            }

            if (!"NENHUM".equals(filtroSelecionado)) {
                List<PlanoVO> planosFiltro = this.getFiltrosConsulta().getPlanos();
                List<PlanoVO> planosPermissaoVenda = getFacade().getPlano().consultarPorPermissaoVenda(getFiltrosConsulta().getCodigoEmpresa(), filtroSelecionado, Uteis.NIVELMONTARDADOS_TODOS);
                for (PlanoVO planoPermissao : planosPermissaoVenda) {
                    boolean estaAdicionado = false;
                    for (PlanoVO planoFiltro : planosFiltro) {
                        if (planoPermissao.getCodigo().equals(planoFiltro.getCodigo())) {
                            estaAdicionado = true;
                            break;
                        }
                    }
                    if (!estaAdicionado) {
                        planosFiltro.add(planoPermissao);
                    }
                }
                Ordenacao.ordenarLista(planosFiltro, "descricao");
                this.getFiltrosConsulta().setPlanos(planosFiltro);
            }
        }
    }

    public Object inicializarFiltroSituacaoContrato() throws Exception {
        StringBuilder sb = new StringBuilder("");
        if(this.getContratoMatricula()) {
            sb.append(",'MA'");
        }

        if (this.getContratoRematricula()) {
            sb.append(",'RE'");
        }

        if (this.getContratoRenovacao()) {
            sb.append(",'RN'");
        }

        if (this.getContratoTransferido()) {
            sb.append(",'TF'");
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(0);
        }
        this.getFiltrosConsulta().setSituacaoContrato(sb.toString());
        return true;
    }

    /**
     * Responsável por limpar Filtros Plano
     *
     * <AUTHOR>
     * 27/04/2011
     */
    private void limparFiltrosPlano() {
        this.setFiltrosDuracao("");
        this.setFiltrosHorario("");
        this.setFiltrosHorarioDescricao("");
        this.setFiltrosModalidade("");
        this.setFiltrosEmpresa("");
        this.setFiltrosModalidadeDescricao("");
        this.setFiltrosPlano("");
        this.setFiltrosPlanoDescricao("");
    }

    /**
     * Responsável por retornar uma lista de empresas cadastradas
     *
     * @return
     * @throws Exception
     * <AUTHOR> 04/04/2011
     */
    public List<SelectItem> getListaEmpresa() throws Exception {
        return getListaEmpresas();
    }

    /**
     * Retorna a lista de Meses
     *
     * @return List<SelectItem>
     * @throws Exception
     * <AUTHOR>
     */
    public List<SelectItem> getSemanas() throws Exception {
        if (semanas.isEmpty()) {
            atualizarSemanas();
        }
        return semanas;
    }

    public void atualizarSemanas() {
        Calendar calendar = Calendario.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, this.filtrosConsulta.getCodigoMes() - 1);
        List<SelectItem> itens = new ArrayList<SelectItem>();

        int aux = 1;
        do {
            String dataInicial = Formatador.formatarData(calendar.getTime(), RelatorioClientesControle.FORMATO_DIA_SEMANA);
            while ((calendar.get(Calendar.DAY_OF_WEEK) > Calendar.SUNDAY)
                    && (calendar.get(Calendar.DAY_OF_MONTH) < calendar.getActualMaximum(Calendar.DAY_OF_MONTH))) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            String dataFinal = Formatador.formatarData(calendar.getTime(), RelatorioClientesControle.FORMATO_DIA_SEMANA);
            itens.add(new SelectItem(aux++, dataInicial + " - " + dataFinal));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        } while (calendar.get(Calendar.DAY_OF_MONTH) > 1);

        semanas = itens;
    }

    /**
     * Retorna a lista de Meses
     *
     * @return List<SelectItem>
     * @throws Exception
     */
    public List<SelectItem> getMeses() throws Exception {
        // criar lista para retorno
        final List<SelectItem> itens = new ArrayList<SelectItem>();
        // percorrer os valores do enumerador TipoMesesEnum
        for (Mes tipoMeses : Mes.values()) {
            // adicionar os meses na lista de retorno
            itens.add(new SelectItem(tipoMeses.getCodigo(), tipoMeses.getDescricao()));
        }
        // setar o mês como o atual
        this.filtrosConsulta.setCodigoMes(this.pegarMesAtual());
        // retornar a lista
        return itens;
    }

    /**
     * Pegar mês atual
     *
     * @return data
     */
    public int pegarMesAtual() {
        // pegar data de hoje
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        return Uteis.getMesData(data);

    }

    public boolean isColNivelTurma() {
        return colNivelTurma;
    }

    public void setColNivelTurma(boolean colNivelTurma) {
        this.colNivelTurma = colNivelTurma;
    }

    /**
     * <AUTHOR> 06/04/2011
     */
    public void imprimirPDF() {
        try {
            setTipoRelatorio("PDF");

            if (!getListaRelatorioClientes().isEmpty()) {
                UIDataTable dataTable = (UIDataTable) context().getViewRoot().findComponent("form:itens");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(getListaRelatorioClientes(), params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(getListaRelatorioClientes());
                        }
                    }
                }
            }

            setListaRelatorio(getListaRelatorioClientes());

            setFiltros("");
            setRelatorio("sim");
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getFiltrosDescricao() {
        String filtros = "";
        //tipo de filtro de data
        switch (this.getTipoFiltroData()) {
            case 1:
                filtros += "Data: " + Uteis.getData(this.getFiltrosConsulta().getData()) + "<br/>";
                break;
            default:
                filtros += "Intervalo de Data: " + Uteis.getData(this.getFiltrosConsulta().getDataInicial()) + "-"
                        + Uteis.getData(this.getFiltrosConsulta().getDataFinal()) + "<br/>";
                break;
        }

        //filtros situacoes
        if (!this.getFiltrosConsulta().getNomesSituacoes().isEmpty()) {
            String sits = "";
            filtros += "Situações: ";
            for (String situacao : this.getFiltrosConsulta().getNomesSituacoes()) {
                sits += ", " + situacao;
            }
            sits = sits.replaceFirst(",", "");
            filtros += sits + "<br/>";
        }
        //filtros colaboradores
        if (!this.getFiltrosConsulta().getNomesColaboradores().isEmpty()) {
            String cols = "";
            filtros += "Colaboradores: ";
            for (String colaborador : this.getFiltrosConsulta().getNomesColaboradores()) {
                cols += ", " + colaborador;
            }
            cols = cols.replaceFirst(",", "");
            filtros += cols + "<br/>";
        }
        //filtros planos
        if (!this.getFiltrosConsulta().getNomesPlanos().isEmpty()) {
            String plans = "";
            filtros += "Planos: ";
            for (String plano : this.getFiltrosConsulta().getNomesPlanos()) {
                plans += ", " + plano;
            }
            plans = plans.replaceFirst(",", "");
            filtros += plans + "<br/>";
        }
        //filtros modalidades
        if (!this.getFiltrosConsulta().getNomesModalidades().isEmpty()) {
            String mods = "";
            filtros += "Modalidades: ";
            for (String modalidade : this.getFiltrosConsulta().getNomesModalidades()) {
                mods += ", " + modalidade;
            }
            mods = mods.replaceFirst(",", "");
            filtros += mods + "<br/>";
        }

        return filtros;
    }

    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        JRDataSource jr1 = new JRBeanArrayDataSource(resumoModalidades.toArray(), false);
        params.put("nomeRelatorio", "RelatorioCliente");
        params.put("nomeEmpresa", empre.getNome());
        params.put("tituloRelatorio", "Relatório Clientes");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", getListaRelatorio());
        params.put("filtros", getFiltrosDescricao());
        params.put("dataIni", Uteis.getData(negocio.comuns.utilitarias.Calendario.hoje()));
        params.put("dataFim", Uteis.getData(negocio.comuns.utilitarias.Calendario.hoje()));
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("totalClientes", totalCliente.toString());
        params.put("totalContratos", totalContrato.toString());
        params.put("totalValor", Formatador.formatarValorMonetario(totalValor));
        params.put("totalCompetencia", Formatador.formatarValorMonetario(totalCompetencia));
        params.put("listaTotais", jr1);
        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);

        params.put("colNome", colNome);
        params.put("colContrato", colContrato);
        params.put("colMatricula", colMatricula);
        params.put("colSituacao", colSituacao);
        params.put("colEmpresa", colEmpresa);
        params.put("colVinculo", colVinculo);
        params.put("colModalidade", colModalidade);
        params.put("colDuracao", colDuracao);
        params.put("colDataLancamento", colDataLancamento);
        params.put("colInicio", colInicio);
        params.put("colVence", colVence);
        params.put("colPlano", colPlano);
        params.put("colHorario", colHorario);
        params.put("colValorModalidade", colValorModalidade);
        params.put("colPlano", colPlano);
        params.put("colFaturamento", colValor);
        params.put("colCategoriaCliente", colCategoriaCliente);
    }

    @SuppressWarnings("unchecked")
    public void imprimirRelatorio() {
        try {
            setMsgAlert("");
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_CLIENTES, (!getListaRelatorioClientes().isEmpty() ? getListaRelatorioClientes().size() : null) , getFiltrosDescricao(), "pdf", "", "");
            imprimirPDF();
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            apresentarRelatorioObjetos(parametros);
            setMsgAlert("abrirPopupPDFImpressao('relatorio/"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
        }
    }

    // -----------------INICIO - GETTERS AND SETTERS --------------------------- //
    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "RelatorioCliente.jrxml");
    }

    /**
     * @return the filtros
     */
    public String getFiltros() {
        return filtros;
    }

    /**
     * @param filtros the filtros to set
     */
    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    /**
     * @return the listaRelatorio
     */
    public List<ClienteRelatorioTO> getListaRelatorioClientes() {
        if (listaRelatorioClientes == null) {
            listaRelatorioClientes = new ArrayList<ClienteRelatorioTO>();
        }
        return listaRelatorioClientes;
    }

    /**
     * @param listaRelatorioClientes the listaRelatorio to set
     */
    public void setListaRelatorioClientes(List<ClienteRelatorioTO> listaRelatorioClientes) {
        this.listaRelatorioClientes = listaRelatorioClientes;
    }

    /**
     * @return the filtros
     */
    public ClientesRelConsultaTO getFiltrosConsulta() {
        if (filtrosConsulta == null) {
            filtrosConsulta = new ClientesRelConsultaTO();
            filtrosConsulta.setData(Calendario.hoje());
            filtrosConsulta.setDataInicial(Calendario.hoje());
            filtrosConsulta.setDataFinal(Calendario.hoje());
        }
        return filtrosConsulta;
    }

    public void setFiltrosConsulta(ClientesRelConsultaTO filtrosConsulta) {
        this.filtrosConsulta = filtrosConsulta;
    }

    /**
     * Responsável por listar as situacoes de cliente ativo
     *
     * @return
     * <AUTHOR> 05/04/2011
     */
    public List<SituacaoEnum> getSituacoesAtivo() {
        return SituacaoEnum.getSituacoesPorGrupo(GrupoSituacaoEnum.ATIVO);
    }

    /**
     * Responsável por listar as situacoes de cliente ativo
     *
     * @return
     * <AUTHOR> 05/04/2011
     */
    public SituacaoEnum getSituacaoRenovado() {
        return SituacaoEnum.RENOVADO;
    }

    /**
     * Responsável por listar as situacoes de cliente VISITANTE
     *
     * @return
     * <AUTHOR> 05/04/2011
     */
    public List<SituacaoEnum> getSituacoesVisitante() {
        return SituacaoEnum.getSituacoesPorGrupo(GrupoSituacaoEnum.VISITANTE);
    }

    /**
     * Responsável por listar as situacoes de cliente TRANCADO
     *
     * @return
     * <AUTHOR> 05/04/2011
     */
    public List<SituacaoEnum> getSituacoesTrancado() {
        return SituacaoEnum.getSituacoesPorGrupo(GrupoSituacaoEnum.TRANCADO);
    }

    /**
     * Responsável por listar as situacoes de cliente INATIVO
     *
     * @return
     * <AUTHOR> 05/04/2011
     */
    public List<SituacaoEnum> getSituacoesInativo() {
        return SituacaoEnum.getSituacoesPorGrupo(GrupoSituacaoEnum.INATIVO);
    }

    /**
     * Responsável por setar datas da consulta quando o filtro de datas é por semanas
     *
     * @throws Exception
     * <AUTHOR>
     * 08/04/2011
     */
    public void setarDatasFiltroSemana() throws Exception {
        Integer codigoSemana = this.getFiltrosConsulta().getCodigoSemana();
        // pega a instancia de calendar
        Calendar calendar = Calendario.getInstance();

        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, this.getFiltrosConsulta().getCodigoMes() - 1);

        if (codigoSemana == null) {
            this.getFiltrosConsulta().setDataInicial(calendar.getTime());
            this.getFiltrosConsulta().setDataFinal(Uteis.obterUltimoDiaMesUltimaHora(calendar.getTime()));
            return;
        }

        while (codigoSemana >= 1) {
            if (codigoSemana == 1) {
                this.getFiltrosConsulta().setDataInicial(calendar.getTime());
            }
            while ((calendar.get(Calendar.DAY_OF_WEEK) > Calendar.SUNDAY)
                    && (calendar.get(Calendar.DAY_OF_MONTH) < calendar.getActualMaximum(Calendar.DAY_OF_MONTH))) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            if (codigoSemana == 1) {
                this.getFiltrosConsulta().setDataFinal(calendar.getTime());
            }
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            codigoSemana--;
        }
    }

    public void inicializarBean() {
        this.setNrPagina(10);
        this.setFiltrosConsulta(null);
        this.setTipoFiltroData(1);
        this.setConsultaPaginada(true);
        this.setFiltrosColaborador("");
        this.setFiltros("");
        this.setFiltrosSituacao("");
        this.setFiltrosSituacaoContrato("");
        this.limparFiltrosPlano();
    }

    // -----------------FIM - GETTERS AND SETTERS --------------------------- //
    public String getFiltrosSituacao() {
        return filtrosSituacao;
    }

    public void setFiltrosSituacao(String filtrosSituacao) {
        this.filtrosSituacao = filtrosSituacao;
    }

    public String getFiltrosColaborador() {
        return filtrosColaborador;
    }

    public void setFiltrosColaborador(String filtrosColaborador) {
        this.filtrosColaborador = filtrosColaborador;
    }

    public String getFiltrosModalidade() {
        return filtrosModalidade;
    }

    public void setFiltrosModalidade(String filtrosModalidade) {
        this.filtrosModalidade = filtrosModalidade;
    }

    public String getFiltrosEmpresa() {
        return filtrosEmpresa;
    }

    public void setFiltrosEmpresa(String filtrosEmpresa) {
        this.filtrosEmpresa = filtrosEmpresa;
    }

    public String getFiltrosPlano() {
        return filtrosPlano;
    }

    public void setFiltrosPlano(String filtrosPlano) {
        this.filtrosPlano = filtrosPlano;
    }

    public String getFiltrosHorario() {
        return filtrosHorario;
    }

    public void setFiltrosHorario(String filtrosHorario) {
        this.filtrosHorario = filtrosHorario;
    }

    public String getFiltrosDuracao() {
        return filtrosDuracao;
    }

    public void setFiltrosDuracao(String filtrosDuracao) {
        this.filtrosDuracao = filtrosDuracao;
    }

    public boolean getColMatricula() {
        return colMatricula;
    }

    public void setColMatricula(boolean colMatricula) {
        this.colMatricula = colMatricula;
    }

    public boolean getColNome() {
        return colNome;
    }

    public void setColNome(boolean colNome) {
        this.colNome = colNome;
    }

    public boolean getColSituacao() {
        return colSituacao;
    }

    public void setColSituacao(boolean colSituacao) {
        this.colSituacao = colSituacao;
    }

    public boolean getColEmpresa() {
        return colEmpresa;
    }

    public void setColEmpresa(boolean colEmpresa) {
        this.colEmpresa = colEmpresa;
    }

    public boolean getColVinculo() {
        return colVinculo;
    }

    public void setColVinculo(boolean colVinculo) {
        this.colVinculo = colVinculo;
    }

    public boolean getColPlano() {
        return colPlano;
    }

    public void setColPlano(boolean colPlano) {
        this.colPlano = colPlano;
    }

    public boolean getColContrato() {
        return colContrato;
    }

    public void setColContrato(boolean colContrato) {
        this.colContrato = colContrato;
    }

    public boolean getColModalidade() {
        return colModalidade;
    }

    public void setColModalidade(boolean colModalidade) {
        this.colModalidade = colModalidade;
    }

    public boolean getColDuracao() {
        return colDuracao;
    }

    public void setColDuracao(boolean colDuracao) {
        this.colDuracao = colDuracao;
    }

    public boolean getColHorario() {
        return colHorario;
    }

    public void setColHorario(boolean colHorario) {
        this.colHorario = colHorario;
    }

    public boolean getColInicio() {
        return colInicio;
    }

    public void setColInicio(boolean colInicio) {
        this.colInicio = colInicio;
    }

    public boolean getColVence() {
        return colVence;
    }

    public void setColVence(boolean colVence) {
        this.colVence = colVence;
    }

    public boolean getColValor() {
        return colValor;
    }

    public void setColValor(boolean colValor) {
        this.colValor = colValor;
    }

    public String getColunaAgrupada() {
        if (colunaAgrupada == null) {
            colunaAgrupada = "";
        }
        return colunaAgrupada;
    }

    public void setColunaAgrupada(String colunaAgrupada) {
        this.colunaAgrupada = colunaAgrupada;
    }

    public boolean getMostrarConteudo() {
        return mostrarConteudo;
    }

    public void setMostrarConteudo(boolean mostrarConteudo) {
        this.mostrarConteudo = mostrarConteudo;
    }

    public Integer getTotalCliente() {
        if (totalCliente == null) {
            totalCliente = 0;
        }
        return totalCliente;
    }

    public void setTotalCliente(Integer totalCliente) {
        this.totalCliente = totalCliente;
    }

    public Integer getTotalContrato() {
        if (totalContrato == null) {
            totalContrato = 0;
        }
        return totalContrato;
    }

    public void setTotalContrato(Integer totalContrato) {
        this.totalContrato = totalContrato;
    }

    public Double getTotalValor() {
        if (totalValor == null) {
            totalValor = 0.0;
        }
        return totalValor;
    }

    public void setTotalValor(Double totalValor) {
        this.totalValor = totalValor;
    }

    public String getTotalValorFormatado() {
        return Formatador.formatarValorMonetario(getTotalValor());
    }

    public String getTotalReceitaFormatado() {
        return Formatador.formatarValorMonetario(getTotalReceita());
    }

    public String getTotalCompetenciaFormatado() {
        return Formatador.formatarValorMonetario(getTotalCompetencia());
    }

    public boolean getPlanoAtivo() {
        if (planoAtivo == null) {
            planoAtivo = Boolean.TRUE;
        }
        return planoAtivo;
    }

    public void setPlanoAtivo(boolean planoAtivo) {

        this.planoAtivo = planoAtivo;
    }

    public boolean getPlanoInativo() {
        if (planoInativo == null) {
            planoInativo = Boolean.FALSE;
        }
        return planoInativo;
    }

    public void setPlanoInativo(boolean planoInativo) {
        this.planoInativo = planoInativo;
    }

    public Boolean getPlanoComPermissaoVenda() {
        if (planoComPermissaoVenda == null) {
            planoComPermissaoVenda = false;
        }
        return planoComPermissaoVenda;
    }

    public void setPlanoComPermissaoVenda(Boolean planoComPermissaoVenda) {
        this.planoComPermissaoVenda = planoComPermissaoVenda;
    }

    public boolean getColaboradorAtivo() {
        if (colaboradorAtivo == null) {
            colaboradorAtivo = Boolean.TRUE;
        }
        return colaboradorAtivo;
    }

    public void setColaboradorAtivo(boolean colaboradorAtivo) {
        this.colaboradorAtivo = colaboradorAtivo;
    }

    public boolean getColaboradorInativo() {
        if (colaboradorInativo == null) {
            colaboradorInativo = Boolean.FALSE;
        }
        return colaboradorInativo;
    }

    public void setColaboradorInativo(boolean colaboradorInativo) {
        this.colaboradorInativo = colaboradorInativo;
    }

    public Boolean getConsultaPaginada() {
        if (consultaPaginada == null) {
            consultaPaginada = Boolean.TRUE;
        }
        return consultaPaginada;
    }

    public void setConsultaPaginada(Boolean consultaPaginada) {
        this.consultaPaginada = consultaPaginada;
    }

    public Integer getNrPagina() {
        return nrPagina;
    }

    public void setNrPagina(Integer nrPagina) {
        this.nrPagina = nrPagina;
    }

    public Integer getNrTotalConsulta() {
        return nrTotalConsulta;
    }

    public void setNrTotalConsulta(Integer nrTotalConsulta) {
        this.nrTotalConsulta = nrTotalConsulta;
    }

    public boolean getColDataLancamento() {
        return colDataLancamento;
    }

    public void setColDataLancamento(boolean colDataLancamento) {
        this.colDataLancamento = colDataLancamento;
    }

    public boolean getColValorModalidade() {
        return colValorModalidade;
    }

    public void setColValorModalidade(boolean colValorModalidade) {
        this.colValorModalidade = colValorModalidade;
    }

    public List<Map<String, String>> getTotalModalidade() {
        if (totalModalidade == null) {
            totalModalidade = new ArrayList<Map<String, String>>();
        }
        return totalModalidade;
    }

    public void setTotalModalidade(List<Map<String, String>> totalModalidade) {
        this.totalModalidade = totalModalidade;
    }

    public Double getTotalReceita() {
        if (totalReceita == null) {
            totalReceita = 0.0;
        }
        return totalReceita;
    }

    public void setTotalReceita(Double totalReceita) {
        this.totalReceita = totalReceita;
    }

    public Boolean getContratoMatricula() {
        if (contratoMatricula == null) {
            contratoMatricula = false;
        }
        return contratoMatricula;
    }

    public void setContratoMatricula(Boolean contratoMatricula) {
        this.contratoMatricula = contratoMatricula;
    }

    public Boolean getContratoRematricula() {
        if (contratoRematricula == null) {
            contratoRematricula = false;
        }
        return contratoRematricula;
    }

    public void setContratoRematricula(Boolean contratoRematricula) {
        this.contratoRematricula = contratoRematricula;
    }

    public Boolean getContratoRenovacao() {
        if (contratoRenovacao == null) {
            contratoRenovacao = false;
        }
        return contratoRenovacao;
    }

    public void setContratoRenovacao(Boolean contratoRenovacao) {
        this.contratoRenovacao = contratoRenovacao;
    }

    public Boolean getContratoTransferido() {
        if (contratoTransferido == null) {
            contratoTransferido = false;
        }
        return contratoTransferido;
    }

    public void setContratoTransferido(Boolean contratoTransferido) {
        this.contratoTransferido = contratoTransferido;
    }

    public String getFiltrosSituacaoContrato() {
        return filtrosSituacaoContrato;
    }

    public void setFiltrosSituacaoContrato(String filtrosSituacaoContrato) {
        this.filtrosSituacaoContrato = filtrosSituacaoContrato;
    }
    public void exportarConsulta(ActionEvent evt){
    String colunaOrdenar = JSFUtilities.getColunaOrdenacao((UIDataTable) context().getViewRoot().findComponent("form:itens"));
    ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBeanValue("ExportadorListaControle");

        if(!UteisValidacao.emptyString(colunaOrdenar)) {
            String[] ordenacao = colunaOrdenar.split(":");
            Ordenacao.ordenarLista(listaRelatorioClientes, ordenacao[0]);
            if (ordenacao[1].equalsIgnoreCase("DESC")) {
                Collections.reverse(listaRelatorioClientes);
            }
        }
        exportadorListaControle.exportar(evt);
    }

    public void validarPermissaoRelatorioCliente(String nome, String desc) throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), nome, desc);
            }
        }
    }

    public void validarPermissaoRelatorioCliente() throws Exception {
        try {
            validarPermissaoRelatorioCliente("RelatorioClientes", "6.23 - Visualizar Relatório de Clientes");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            setMensagem(e.getMessage());
            e.printStackTrace();
        }
    }

    public Double getTotalCompetencia() {
        return totalCompetencia;
    }

    public void setTotalCompetencia(Double totalCompetencia) {
        this.totalCompetencia = totalCompetencia;
    }

    public String getFiltrosModalidadeDescricao() {
        return filtrosModalidadeDescricao;
    }

    public void setFiltrosModalidadeDescricao(String filtrosModalidadeDescricao) {
        this.filtrosModalidadeDescricao = filtrosModalidadeDescricao;
    }

    public String getFiltrosPlanoDescricao() {
        return filtrosPlanoDescricao;
    }

    public void setFiltrosPlanoDescricao(String filtrosPlanoDescricao) {
        this.filtrosPlanoDescricao = filtrosPlanoDescricao;
    }

    public String getFiltrosHorarioDescricao() {
        return filtrosHorarioDescricao;
    }

    public void setFiltrosHorarioDescricao(String filtrosHorarioDescricao) {
        this.filtrosHorarioDescricao = filtrosHorarioDescricao;
    }
    
    
}
