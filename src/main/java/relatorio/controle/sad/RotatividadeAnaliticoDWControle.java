package relatorio.controle.sad;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import br.com.pactosolucoes.enumeradores.IndicadorMovimentacaoContratoEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.ContratoDependenteHistorico;
import negocio.facade.jdbc.contrato.Contrato;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.basico.ResultadoBITO;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;
import relatorio.negocio.comuns.sad.RotatividadeSinteticoDWVO;
import relatorio.negocio.jdbc.sad.RotatividadeAnaliticoDW;

import javax.faces.model.SelectItem;
import java.io.File;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas rotatividadeAnaliticoDWForm.jsp rotatividadeAnaliticoDWCons.jsp) com
 * as funcionalidades da classe <code>RotatividadeAnaliticoDW</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see RotatividadeAnaliticoDW
 * @see RotatividadeAnaliticoDWVO
 */
public class RotatividadeAnaliticoDWControle extends BIControle {

    private RotatividadeAnaliticoDWVO rotatividadeAnaliticoDWVO;
    private RotatividadeSinteticoDWVO rotatividadeSinteticoDWVO;
    protected List<RotatividadeAnaliticoDWVO> listaSituacaoRotatividadeAnalitico;
    protected List<RotatividadeAnaliticoDWVO> listaApresentarRotatividadeAnalitico;
    protected List<ContratoVO> listaApresentarContratos;
    protected List<RotatividadeSinteticoDWVO> listaSituacaoRotatividadeSintetico;
    protected List<RotatividadeSinteticoDWVO> listaApresentarRotatividadeSintetico;
    protected String apresentarMes;
    protected Boolean mostrarPaginacao;
    protected Boolean apresentarAtivosMesAnterior;//Ativos do mes anterior
    protected Boolean apresentarVencidoMesAnterior;//vencidos do mes anterior
    protected Boolean apresentarVirgenteMesAnterior;//Ativos + Vencidos do mes anterior
    protected Boolean apresentarAtivosMes;//ativos do fim do mes
    protected Boolean apresentarVencidoMes;//vencidos do fim do mes
    protected Boolean apresentarTotalVirgentes;//Ativos + Vencidos do fim do mes
    protected Boolean apresentarMatriculado;//matriculados
    protected Boolean apresentarRematriculado;//rematriculados
    protected Boolean apresentarCancelado;//cancelados
    protected Boolean apresentarTrancado;//trancados
    protected Boolean apresentarContratoTransferido;//contrato transferido
    protected Boolean apresentarDesistente;//desistentes
    protected Boolean apresentarRetornoTrancado;//retornos trancamento
    protected Boolean apresentarSaldoMes;//retornos trancamento
    private Boolean apresentarDependentesInicioMes;
    private Boolean apresentarDependentesFinalMes;
    private Boolean apresentarDependentesVinculado;
    private Boolean desconsiderarCancelamentoMudancaPlano;
    private Boolean desconsiderarCancelamentoMudancaPlanoNaLista;
    private Boolean apresentarDependentesDesvinculado;
    private Boolean apresentarAgregaodresVinculado;
    protected Integer pagina;
    protected ClienteVO cliente;
    protected ConfiguracaoSistemaVO configuracaoSistemaVO = null;
    protected EmpresaVO empresaVO;

    private Boolean validadorBusinessInteligence = false;

    private List<ConfiguracaoBIVO> configuracoes;
    private Boolean exibirAgregadoresMovimentacaoContrato = false;
    private Integer qtdCheckinConsiderarAgregador = 1;
    private Integer qtdDiasConsiderarAgregador = 30;

    private boolean exibirOpcoesAgregadores = false;

    /**
     * Interface <code>RotatividadeAnaliticoDWInterfaceFacade</code> responsável
     * pela interconexão da camada de controle com a camada de negócio. Criando
     * uma independência da camada de controle com relação a tenologia de
     * persistência dos dados (DesignPatter: Façade).
     */
    public RotatividadeAnaliticoDWControle() throws Exception {
        inicializarFacades();
        inicializarRotatividade();
        try {
            if (JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA) != null) {
                setConfiguracaoSistemaVO((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
            } else {
                setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        initConfigAgregadores();

    }

    public void initConfigAgregadores() throws Exception {
        if (!UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo())) {
            configuracoes = getFacade().getConfiguracaoBI().consultarPorBI(BIEnum.ROTATIVIDADE_CONTRATO, getEmpresaFiltroBI().getCodigo());
        }
        if (!UteisValidacao.emptyList(configuracoes)){
            for (ConfiguracaoBIVO configuracaoBIVO: configuracoes){
                if (configuracaoBIVO.getConfiguracao() != null) {
                    if (configuracaoBIVO.getConfiguracao().equals(ConfiguracaoBIEnum.EXIBIR_AGREGADORES)) {
                        setExibirAgregadoresMovimentacaoContrato(configuracaoBIVO.getValorAsBoolean());
                    }
                    if (configuracaoBIVO.getConfiguracao().equals(ConfiguracaoBIEnum.QTD_CHECKIN_CONSIDERAR_AGREGADOR)) {
                        setQtdCheckinConsiderarAgregador(configuracaoBIVO.getValorAsInteger());
                    }
                    if (configuracaoBIVO.getConfiguracao().equals(ConfiguracaoBIEnum.QTD_DIAS_CONSIDERAR_AGREGADOR)){
                        setQtdDiasConsiderarAgregador(configuracaoBIVO.getValorAsInteger());
                    }
                }
            }
            try{
                if (!UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo())) {
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);

                    boolean possuiIntegracao = !UteisValidacao.emptyString(empresaVO.getCodigoGymPass()) ||
                            !UteisValidacao.emptyString(empresaVO.getTokenApiGymPass()) ||
                            !UteisValidacao.emptyString(empresaVO.getTokenAcademyGoGood()) ||
                            getFacade().getTotalPass().possuiConfiguracaoTotalPassAtiva(empresaVO.getCodigo());
                    setExibirOpcoesAgregadores(possuiIntegracao);
                } else {
                    setExibirAgregadoresMovimentacaoContrato(false);
                }
            } catch (Exception ex){
                setExibirAgregadoresMovimentacaoContrato(false);
            }
        }
    }

    public String inicializarRotatividade() {
        setRotatividadeAnaliticoDWVO(new RotatividadeAnaliticoDWVO());
        setRotatividadeSinteticoDWVO(new RotatividadeSinteticoDWVO());
        setCliente(new ClienteVO());

        preencherVisibilidade(IndicadorMovimentacaoContratoEnum.NAO_DEFINIDO);

        setListaApresentarRotatividadeAnalitico(new ArrayList<>());
        setMostrarPaginacao(false);
        setPagina(1);
        return "login";
    }

    public List consultarPorNomeEmpresa(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }

    public void irParaTelaCliente() {
        ContratoVO obj = (ContratoVO) context().getExternalContext().getRequestMap().get("contrato");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                if (obj.isIndicadorDependente()) {
                    irParaTelaCliente(obj.getClienteDependente());
                } else {
                    irParaTelaCliente(obj.getCliente());
                }

            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    @Override
    public EmpresaVO getEmpresaFiltroBI(){
        if(JSFUtilities.isJSFContext() && JSFUtilities.getManagedBean("BIControle.empresaFiltroBI") != null){
            return (EmpresaVO)JSFUtilities.getManagedBean("BIControle.empresaFiltro");
        }
        return getEmpresaVO() == null ? new EmpresaVO() :  getEmpresaVO();
    }

    public void montarRelatorioRotatividade() throws Exception {
        try {
            ResultSet listaSintetico;
            int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia()) - 1;
            int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());
            if (mes == 0) {
                mes = 12;
                ano = ano - 1;
            }
            getRotatividadeSinteticoDWVO().setQtdTotal(0);
            listaSintetico = getFacade().getRotatividadeSinteticoDW().consultarRotatividadePorSituacaoMesAnoEmpresaResultSet(mes, ano, getEmpresaFiltroBI().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            montarRotatividadeSinteticoMesDia(listaSintetico);
            montarRotatividadeAnaliticoMes();
        } catch (Exception e) {
            throw e;
        }
    }
    public void montarDadosEnviarCache( ResultadoBITO resultado){
        resultado.getResultadosBI().put("qtdMesAnterior", getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior());
        resultado.getResultadosBI().put("qtdVencido", getRotatividadeSinteticoDWVO().getQtdVencido());
        resultado.getResultadosBI().put("qtdVigenteMesAnterior", getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior());
        resultado.getResultadosBI().put("qtdTotal", getRotatividadeSinteticoDWVO().getQtdTotal());
        resultado.getResultadosBI().put("qtdVigenteMesAtual", getRotatividadeSinteticoDWVO().getQtdeVigenteMesAtual());
        resultado.getResultadosBI().put("qtdMatriculado", getRotatividadeAnaliticoDWVO().getQtdMatriculado());
        resultado.getResultadosBI().put("qtdMatriculadoHoje",getRotatividadeAnaliticoDWVO().getQtdMatriculadoHoje());
        resultado.getResultadosBI().put("qtdRematriculado", getRotatividadeAnaliticoDWVO().getQtdRematriculado());
        resultado.getResultadosBI().put("qtdRematriculadoHoje", getRotatividadeAnaliticoDWVO().getQtdRematriculadoHoje());
        resultado.getResultadosBI().put("qtdCancelado", getRotatividadeAnaliticoDWVO().getQtdCancelado());
        resultado.getResultadosBI().put("qtdCanceladoHoje", getRotatividadeAnaliticoDWVO().getQtdCanceladoHoje());
        resultado.getResultadosBI().put("qtdDesistente", getRotatividadeAnaliticoDWVO().getQtdDesistente());
        resultado.getResultadosBI().put("qtdDesistenteHoje",getRotatividadeAnaliticoDWVO().getQtdDesistenteHoje());
        resultado.getResultadosBI().put("qtdRetornoTrancamento", getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamento());
        resultado.getResultadosBI().put("qtdRetornoTrancamentoHoje", getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamentoHoje());
        resultado.getResultadosBI().put("getQtdTrancamento", getRotatividadeAnaliticoDWVO().getQtdTrancamento());
        resultado.getResultadosBI().put("getQtdTrancamentoHoje", getRotatividadeAnaliticoDWVO().getQtdTrancamentoHoje());
        resultado.getResultadosBI().put("qtdContratoTransferido", getRotatividadeAnaliticoDWVO().getQtdContratoTransferido());
        resultado.getResultadosBI().put("qtdContratoTransferidoHoje", getRotatividadeAnaliticoDWVO().getQtdContratoTransferidoHoje());
        resultado.getResultadosBI().put("qtdVencidoMes", getRotatividadeAnaliticoDWVO().getQtdVencidoMes());
        resultado.getResultadosBI().put("qtdSaldo", getRotatividadeAnaliticoDWVO().getQtdSaldo());
        resultado.getResultadosBI().put("qtdTotal", getRotatividadeSinteticoDWVO().getQtdTotal());
        resultado.getResultadosBI().put("qtdeFinalMesAtual", getRotatividadeSinteticoDWVO().getQtdeFinalMesAtual());
    }
    public void montarDadosCache( ResultadoBITO resultado){
        getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior((Integer)resultado.getResultadosBI().get("qtdMesAnterior"));
        getRotatividadeSinteticoDWVO().setQtdVencido((Integer)resultado.getResultadosBI().get("qtdVencido"));
        getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior((Integer)resultado.getResultadosBI().get("qtdVigenteMesAnterior"));
        getRotatividadeSinteticoDWVO().setQtdTotal((Integer)resultado.getResultadosBI().get("qtdTotal"));
        getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual((Integer)resultado.getResultadosBI().get("qtdVigenteMesAtual"));
        getRotatividadeAnaliticoDWVO().setQtdMatriculado((Integer)resultado.getResultadosBI().get("qtdMatriculado"));
        getRotatividadeAnaliticoDWVO().setQtdMatriculadoHoje((Integer)resultado.getResultadosBI().get("qtdMatriculadoHoje"));
        getRotatividadeAnaliticoDWVO().setQtdRematriculado((Integer)resultado.getResultadosBI().get("qtdRematriculado"));
        getRotatividadeAnaliticoDWVO().setQtdRematriculadoHoje((Integer)resultado.getResultadosBI().get("qtdRematriculadoHoje"));
        getRotatividadeAnaliticoDWVO().setQtdCancelado((Integer)resultado.getResultadosBI().get("qtdCancelado"));
        getRotatividadeAnaliticoDWVO().setQtdCanceladoHoje((Integer)resultado.getResultadosBI().get("qtdCanceladoHoje"));
        getRotatividadeAnaliticoDWVO().setQtdDesistente((Integer)resultado.getResultadosBI().get("qtdDesistente"));
        getRotatividadeAnaliticoDWVO().setQtdDesistenteHoje((Integer)resultado.getResultadosBI().get("qtdDesistenteHoje"));
        getRotatividadeAnaliticoDWVO().setQtdRetornoTrancamento((Integer)resultado.getResultadosBI().get("qtdRetornoTrancamento"));
        getRotatividadeAnaliticoDWVO().setQtdRetornoTrancamentoHoje((Integer)resultado.getResultadosBI().get("qtdRetornoTrancamentoHoje"));
        getRotatividadeAnaliticoDWVO().setQtdContratoTransferido((Integer)resultado.getResultadosBI().get("qtdContratoTransferido"));
        getRotatividadeAnaliticoDWVO().setQtdContratoTransferidoHoje((Integer)resultado.getResultadosBI().get("qtdContratoTransferidoHoje"));
        getRotatividadeAnaliticoDWVO().setQtdTrancamento((Integer)resultado.getResultadosBI().get("getQtdTrancamento"));
        getRotatividadeAnaliticoDWVO().setQtdTrancamentoHoje((Integer)resultado.getResultadosBI().get("getQtdTrancamentoHoje"));
        getRotatividadeAnaliticoDWVO().setQtdVencidoMes((Integer)resultado.getResultadosBI().get("qtdVencidoMes"));
        getRotatividadeAnaliticoDWVO().setQtdSaldo((Integer)resultado.getResultadosBI().get("qtdSaldo"));
        getRotatividadeSinteticoDWVO().setQtdTotal((Integer)resultado.getResultadosBI().get("qtdTotal"));
        getRotatividadeSinteticoDWVO().setQtdeFinalMesAtual((Integer)resultado.getResultadosBI().get("qtdeFinalMesAtual"));

        getRotatividadeAnaliticoDWVO().setQtdDependentesVinculados((Integer)resultado.getResultadosBI().get("qtdDependentesVinculados"));
        getRotatividadeAnaliticoDWVO().setQtdDependentesVinculadosHoje((Integer)resultado.getResultadosBI().get("qtdDependentesVinculadosHoje"));
        getRotatividadeAnaliticoDWVO().setQtdDependentesDesvinculados((Integer)resultado.getResultadosBI().get("qtdDependentesDesvinculados"));
        getRotatividadeAnaliticoDWVO().setQtdDependentesDesvinculadosHoje((Integer)resultado.getResultadosBI().get("qtdDependentesDesvinculadosHoje"));
        getRotatividadeSinteticoDWVO().setQtdDependentesMesAtual((Integer) resultado.getResultadosBI().get("qtdDependentesMesAtual"));
        getRotatividadeSinteticoDWVO().setQtdDependentesFinalMesAtual((Integer) resultado.getResultadosBI().get("qtdDependentesFinalMesAtual"));

        getRotatividadeSinteticoDWVO().setQtdAgregadoresVinculadosMes((Integer) resultado.getResultadosBI().get("qtdAgregadoresMesAnterior"));
        getRotatividadeAnaliticoDWVO().setQtdAgregadoresVinculadosMes((Integer) resultado.getResultadosBI().get("qtdAgregadoresMesAnterior"));
        getRotatividadeAnaliticoDWVO().setQtdAgregadorGympass((Integer)resultado.getResultadosBI().get("qtdAgregadoresGympassMesAnterior"));
        getRotatividadeAnaliticoDWVO().setQtdAgregadorGogood((Integer)resultado.getResultadosBI().get("qtdAgregadoresGogoodMesAnterior"));
        getRotatividadeAnaliticoDWVO().setQtdAgregadorTotalpass((Integer)resultado.getResultadosBI().get("qtdAgregadoresTotalpassMesAnterior"));

        getRotatividadeSinteticoDWVO().setQtdAgregadoresVinculadosHoje((Integer) resultado.getResultadosBI().get("qtdAgregadoresHoje"));
        getRotatividadeAnaliticoDWVO().setQtdAgregadoresVinculadosHoje((Integer) resultado.getResultadosBI().get("qtdAgregadoresHoje"));

    }

    public void atualizar(){
        try{
            gravarHistoricoAcessoBI(BIEnum.ROTATIVIDADE_CONTRATO);
            carregarDadosBI(true, true);
        }catch (Exception erro){
            montarErro(erro);
        }
    }
    public void carregar(){
        try{
            carregarDadosBI(false, false);
        }catch (Exception erro){
            montarErro(erro);
        }
    }
    public void carregarDadosBI(boolean atualizarCache, boolean atualizarDadosMS) throws Exception{
        ResultadoBITO resultado = obterResultadoBIDiaCache(BIEnum.ROTATIVIDADE_CONTRATO);
        if(validarResultadoBIDia(resultado) || atualizarCache){
            montarRelatorioRotatividadeTelaInicialMS(atualizarDadosMS, null);
            resultado = new ResultadoBITO();
            montarDadosEnviarCache(resultado);
            adicionarResultadoBI(BIEnum.ROTATIVIDADE_CONTRATO,resultado);
        } else {
            montarDadosCache(resultado);
        }
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.ROTATIVIDADE_CONTRATO.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        filtros.put("dataBase", Calendario.getDataComUltimaHora(getDataBaseFiltroBI()).getTime());
        filtros.put("usaPlanoCompartilhado", getConfiguracaoSistemaVO().isUsaPlanoRecorrenteCompartilhado());
        filtros.put("exibirAgregadoresMovimentacaoContrato", getExibirAgregadoresMovimentacaoContrato());
        filtros.put("qtdCheckinConsiderarAgregador", getQtdCheckinConsiderarAgregador());
        filtros.put("qtdDiasConsiderarAgregador", getQtdDiasConsiderarAgregador());
        if(getDesconsiderarCancelamentoMudancaPlano() != null && getDesconsiderarCancelamentoMudancaPlano()) {
            setDesconsiderarCancelamentoMudancaPlanoNaLista(true);
            filtros.put("desconsiderarCancelamentoMudancaPlano", getDesconsiderarCancelamentoMudancaPlano());
        } else {
            setDesconsiderarCancelamentoMudancaPlanoNaLista(false);
        }
        if (UteisValidacao.notEmptyNumber(getEmpresaFiltroBI().getCodigo())) {
            filtros.put("empresa", getEmpresaFiltroBI().getCodigo());
        }

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public void montarRelatorioRotatividadeTelaInicialMS(boolean atualizarDadosMS, String key) {
        try {
            getRotatividadeAnaliticoDWVO().setDia(getDataBaseFiltroBI());

            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(UteisValidacao.emptyString(key) ? getKey() : key, BIEnum.ROTATIVIDADE_CONTRATO, getFiltroDTO(atualizarDadosMS), atualizarDadosMS);
            JSONObject dados = new JSONObject(filtroDTO.getJsonDados());

            getRotatividadeSinteticoDWVO().setQtdTotal(new Integer(0));
            getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(0);
            getRotatividadeSinteticoDWVO().setQtdVencido(0);
            getRotatividadeSinteticoDWVO().setQtdTotal(0);
            getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual(0);

            getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(dados.getInt("qtdVigentesMesAnterior"));

            getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual(dados.getInt("qtdeVigenteMesAtual"));

            getRotatividadeSinteticoDWVO().setQtdVencido(dados.getInt("qtdVencido"));

            getRotatividadeAnaliticoDWVO().setQtdMatriculado(dados.getInt("qtdMatriculado"));

            getRotatividadeAnaliticoDWVO().setQtdMatriculadoHoje(dados.getInt("qtdMatriculadoHoje"));

            getRotatividadeAnaliticoDWVO().setQtdRematriculado(dados.getInt("qtdRematriculado"));

            getRotatividadeAnaliticoDWVO().setQtdRematriculadoHoje(dados.getInt("qtdRematriculadoHoje"));

            getRotatividadeAnaliticoDWVO().setQtdCancelado(dados.getInt("qtdCancelado"));

            getRotatividadeAnaliticoDWVO().setQtdCanceladoHoje(dados.getInt("qtdCanceladoHoje"));

            if(getDesconsiderarCancelamentoMudancaPlano() != null && getDesconsiderarCancelamentoMudancaPlano()) {
                getRotatividadeAnaliticoDWVO().setQtdCanceladoComFiltro(dados.getInt("qtdCanceladoComFiltro"));

                getRotatividadeAnaliticoDWVO().setQtdCanceladoHojeComFiltro(dados.getInt("qtdCanceladoHojeComFiltro"));
            }

            getRotatividadeAnaliticoDWVO().setQtdDesistente(dados.getInt("qtdDesistente"));

            getRotatividadeAnaliticoDWVO().setQtdDesistenteHoje(dados.getInt("qtdDesistenteHoje"));

            getRotatividadeAnaliticoDWVO().setQtdRetornoTrancamento(dados.getInt("qtdRetornoTrancamento"));

            getRotatividadeAnaliticoDWVO().setQtdRetornoTrancamentoHoje(dados.getInt("qtdRetornoTrancamentoHoje"));

            getRotatividadeAnaliticoDWVO().setQtdTrancamento(dados.getInt("qtdTrancamento"));

            getRotatividadeAnaliticoDWVO().setQtdTrancamentoHoje(dados.getInt("qtdTrancamentoHoje"));

            getRotatividadeAnaliticoDWVO().setQtdContratoTransferido(dados.getInt("qtdContratoTransferido"));

            getRotatividadeAnaliticoDWVO().setQtdContratoTransferidoHoje(dados.getInt("qtdContratoTransferidoHoje"));

            getRotatividadeAnaliticoDWVO().setQtdDependentesVinculados(dados.getInt("qtdDependentesVinculados"));
            getRotatividadeAnaliticoDWVO().setQtdDependentesVinculadosHoje(dados.getInt("qtdDependentesVinculadosHoje"));
            getRotatividadeAnaliticoDWVO().setQtdDependentesDesvinculados(dados.getInt("qtdDependentesDesvinculados"));
            getRotatividadeAnaliticoDWVO().setQtdDependentesDesvinculadosHoje(dados.getInt("qtdDependentesDesvinculadosHoje"));
            getRotatividadeSinteticoDWVO().setQtdDependentesMesAtual(dados.getInt("qtdDependentesMesAtual"));
            getRotatividadeSinteticoDWVO().setQtdDependentesFinalMesAtual(dados.getInt("qtdDependentesFinalMesAtual"));

            getRotatividadeSinteticoDWVO().setQtdAgregadoresVinculadosMes(dados.getInt("qtdAgregadoresMesAnterior"));
            getRotatividadeAnaliticoDWVO().setQtdAgregadoresVinculadosMes(dados.getInt("qtdAgregadoresMesAnterior"));

            getRotatividadeAnaliticoDWVO().setQtdAgregadorGympass(dados.getInt("qtdAgregadoresGympassMesAnterior"));
            getRotatividadeAnaliticoDWVO().setQtdAgregadorGogood(dados.getInt("qtdAgregadoresGogoodMesAnterior"));
            getRotatividadeAnaliticoDWVO().setQtdAgregadorTotalpass(dados.getInt("qtdAgregadoresTotalpassMesAnterior"));

            getRotatividadeAnaliticoDWVO().setQtdAgregadoresVinculadosHoje(dados.getInt("qtdAgregadoresHoje"));
            getRotatividadeSinteticoDWVO().setQtdAgregadoresVinculadosHoje(dados.getInt("qtdAgregadoresHoje"));

            getRotatividadeSinteticoDWVO().setQtdClienteAtivosDependentesMesAtual(dados.optInt("qtdClienteAtivosDependentesMesAtual"));
            getRotatividadeSinteticoDWVO().setQtdClienteAtivosDependentesFinalMesAtual(dados.optInt("qtdClienteAtivosDependentesFinalMesAtual"));

            getRotatividadeSinteticoDWVO().setQtdBolsistasFinalMesAtual(dados.optInt("qtdClientesBolsistasFinalMesAtual"));

            getRotatividadeAnaliticoDWVO().setQtdVencidoMes(dados.getInt("qtdVencidoMes"));

            getRotatividadeAnaliticoDWVO().setQtdSaldoDependentes(dados.getInt("qtdSaldoDependentes"));
            getRotatividadeAnaliticoDWVO().setQtdSaldo(dados.getInt("qtdSaldo"));
            getRotatividadeSinteticoDWVO().setQtdTotal(dados.getInt("qtdTotal"));

            getRotatividadeSinteticoDWVO().setQtdeFinalMesAtual(dados.getInt("qtdeFinalMesAtual"));

            setDataAtualizacao(new Date(filtroDTO.getDataGeracao()));
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarRelatorioRotatividadeTelaInicial() throws Exception {
        try {
            getRotatividadeAnaliticoDWVO().setDia(getDataBaseFiltroBI());
            int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia()) - 1;
            int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());
            if (mes == 0) {
                mes = 12;
                ano = ano - 1;
            }

            getRotatividadeAnaliticoDWVO().setDia(getDataBaseFiltroBI());
            Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
            Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

            Calendar cal = Calendario.getInstance();
            cal.setTime(dataIn);
            cal.add(Calendar.DAY_OF_MONTH, -1);

            dataIn = Uteis.obterPrimeiroDiaMes(cal.getTime());
            dataFin = Uteis.obterUltimoDiaMesUltimaHora(cal.getTime());


            String dataInicio = Uteis.getDataFormatoBD(dataIn);
            String dataFim = Uteis.getDataFormatoBD(dataFin);



            /*ResultSet listaSintetico listaSintetico = getFacade().getRotatividadeSinteticoDW().
            consultarRotatividadePorSituacaoMesAnoEmpresaResultSet(
            mes, ano, getEmpresaVO().getCodigo().intValue(),
            Uteis.NIVELMONTARDADOS_DADOSBASICOS);*/

            //montarRotatividadeSinteticoMesDia(listaSintetico);
            getRotatividadeSinteticoDWVO().setQtdTotal(new Integer(0));
            getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(0);
            getRotatividadeSinteticoDWVO().setQtdVencido(0);
            getRotatividadeSinteticoDWVO().setQtdTotal(0);
            getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual(0);

            getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(getFacade().getContrato().contar(
                    String.format(Contrato.sqlAtivosVencidosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                            dataFim,
                            dataFim,
                        dataFim,
                        dataFim
                    })));

            getRotatividadeSinteticoDWVO().setQtdVencido(getFacade().getContrato().contar(
                    String.format(Contrato.sqlVencidosInicioMesContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior()
                    + getRotatividadeSinteticoDWVO().getQtdVencido());

            getRotatividadeSinteticoDWVO().setQtdTotal(getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior());

            getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual(
                    getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior()
                    - getRotatividadeSinteticoDWVO().getQtdVencido());


            montarRotatividadeAnaliticoMes();



            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void montarRotatividadeAnaliticoMes() throws Exception {
        try {

            setListaSituacaoRotatividadeAnalitico(new ArrayList<RotatividadeAnaliticoDWVO>());
            int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
            int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());
            //para efeito de cálculo do mês será considerado a data inicio o primeiro dia do mês da data base selecionada
            //e, como data final até a zero-hora da data base selecionada
            Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
            Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

            Date dataFinAnterior = Uteis.obterDataAnterior(dataIn, 1);
            Date dataInAnterior = Uteis.obterPrimeiroDiaMes(dataFinAnterior);


            String dataInicioAnterior = Uteis.getDataFormatoBD(dataInAnterior);
            String dataFimAnterior = Uteis.getDataFormatoBD(dataFinAnterior);

            String dataInicio = Uteis.getDataFormatoBD(dataIn);
            String dataFim = Uteis.getDataFormatoBD(dataFin);

            getRotatividadeAnaliticoDWVO().setQtdMatriculado(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlMatriculadosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdMatriculadoHoje(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlMatriculadosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdRematriculado(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlRematriculadosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdRematriculadoHoje(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlRematriculadosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim
                    })));

            String sqlCount;
            if(getDesconsiderarCancelamentoMudancaPlanoNaLista()) {
                sqlCount = Contrato.sqlCanceladosContarDesconsiderarMudancaPlano;
            } else {
                sqlCount = Contrato.sqlCanceladosContar;
            }

            getRotatividadeAnaliticoDWVO().setQtdCancelado(
                    getFacade().getContrato().contar(
                    String.format(sqlCount,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdCanceladoHoje(
                    getFacade().getContrato().contar(
                    String.format(sqlCount,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdDesistente(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlDesistentesContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdDesistenteHoje(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlDesistentesContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdRetornoTrancamento(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlRetornoTrancamentosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdRetornoTrancamentoHoje(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlRetornoTrancamentosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdTrancamento(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlTrancadosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdTrancamentoHoje(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlTrancadosContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdContratoTransferido(
                    getFacade().getContrato().contar(
                            String.format(Contrato.sqlContratoTransferidoContar,
                                    new Object[]{
                                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                                            dataInicio,
                                            dataFim,
                                            dataInicio,
                                            dataFim
                                    })));

            getRotatividadeAnaliticoDWVO().setQtdContratoTransferidoHoje(
                    getFacade().getContrato().contar(
                            String.format(Contrato.sqlContratoTransferidoContar,
                                    new Object[]{
                                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                                            dataFim,
                                            dataFim,
                                            dataFim,
                                            dataFim
                                    })));

            getRotatividadeAnaliticoDWVO().setQtdVencidoMes(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlVencidosFinalMesContar,
                    new Object[]{
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataInicioAnterior,
                        dataFimAnterior,                        
                        dataInicio,
                        dataFim
                    })));

            getRotatividadeAnaliticoDWVO().setQtdSaldo(
                    getRotatividadeAnaliticoDWVO().getQtdMatriculado()
                    + getRotatividadeAnaliticoDWVO().getQtdRematriculado()
                    + getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamento()
                    - getRotatividadeAnaliticoDWVO().getQtdCancelado()
                    - getRotatividadeAnaliticoDWVO().getQtdDesistente()
                    - getRotatividadeAnaliticoDWVO().getQtdTrancamento()
                    + getRotatividadeAnaliticoDWVO().getQtdContratoTransferido());

            getRotatividadeSinteticoDWVO().setQtdTotal(
                    getRotatividadeSinteticoDWVO().getQtdTotal()
                            + getRotatividadeAnaliticoDWVO().getQtdSaldo());
            getRotatividadeSinteticoDWVO().setQtdeFinalMesAtual(
                    getRotatividadeSinteticoDWVO().getQtdTotal()
                    - getRotatividadeAnaliticoDWVO().getQtdVencidoMes());
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarRotatividadeSinteticoMesDia(ResultSet lista) throws Exception {
        try {
            getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(0);
            getRotatividadeSinteticoDWVO().setQtdVencido(0);
            getRotatividadeSinteticoDWVO().setQtdTotal(0);
            getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual(0);
            while (lista.next()) {
                getRotatividadeSinteticoDWVO().setQtdVigentesMesAnterior(lista.getInt("qtdVigentesMesAnterior"));
                getRotatividadeSinteticoDWVO().setQtdVencido(lista.getInt("vencido"));
                getRotatividadeSinteticoDWVO().setQtdTotal(lista.getInt("qtdVigentesMesAnterior"));
                getRotatividadeSinteticoDWVO().setQtdeVigenteMesAtual(lista.getInt("qtdVigentesMesAnterior") - lista.getInt("vencido"));
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void consultarRotatividadePorSituacaoMatriculados() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdMatriculado().intValue() != 0  || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlMatriculados, new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.MATRICULADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void consultarRotatividadePorSituacaoMatriculadosHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdMatriculadoHoje())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = getRotatividadeAnaliticoDWVO().getDia();
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlMatriculados, new Object[]{
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "FROM autorizacaocobrancacliente acc " +
                                        "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        ") as convenioDescricao," +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));


                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.MATRICULADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void consultarRotatividadePorSituacaoRematricula() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdRematriculado().intValue() != 0  || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlRematriculados, new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.REMATRICULADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoRematriculaHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdRematriculadoHoje())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlRematriculados, new Object[]{
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "FROM autorizacaocobrancacliente acc " +
                                        "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        ") as convenioDescricao," +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.REMATRICULADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void consultarRotatividadePorSituacaoCancelado() {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdCancelado() != 0 || getValidadorBusinessInteligence()) {
                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);

                String query;

                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_MOVIMENTACAOCONTRATOS;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                String filtrarPorCodEmpresa = UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo());

                if(getDesconsiderarCancelamentoMudancaPlanoNaLista()) {
                    query = String.format(Contrato.sqlCanceladosDesconsiderarMudancaPlano, colunas , filtrarPorCodEmpresa,dataInicio, dataFim, "%");
                } else {
                    query = String.format(Contrato.sqlCancelados, colunas , filtrarPorCodEmpresa,dataInicio, dataFim);
                }

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(query,
                               nivelMontarDados)
                );

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.CANCELADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    //
    public void consultarRotatividadePorSituacaoCanceladoHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdCanceladoHoje())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = getRotatividadeAnaliticoDWVO().getDia();
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlCancelados, new Object[]{
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "FROM autorizacaocobrancacliente acc " +
                                        "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        ") as convenioDescricao," +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_MOVIMENTACAOCONTRATOS));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.CANCELADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoTrancado() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdTrancamento().intValue() != 0 || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlTrancados, new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.TRANCADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoTrancadoHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdTrancamentoHoje())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = getRotatividadeAnaliticoDWVO().getDia();
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlTrancados, new Object[]{
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "FROM autorizacaocobrancacliente acc " +
                                        "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        ") as convenioDescricao," +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.TRANCADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoDesistente() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdDesistente().intValue() != 0 || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlDesistentes, new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.DESISTENTE);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }
    
    public void consultarRotatividadePorSituacaoRenovados() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDesistente())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlRenovados, new Object[]{
                            "*",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.NAO_DEFINIDO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoDesistenteHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDesistenteHoje())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = getRotatividadeAnaliticoDWVO().getDia();
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlDesistentes, new Object[]{
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "FROM autorizacaocobrancacliente acc " +
                                        "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        ") as convenioDescricao," +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.DESISTENTE);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoRetornoTrancamento() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamento().intValue() != 0 || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlRetornoTrancamentos, new Object[]{
                               colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));


                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.RETORNO_TRANCADO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void consultarRotatividadeSaldoMes() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdSaldo())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                setListaApresentarRotatividadeAnalitico(new ArrayList<RotatividadeAnaliticoDWVO>());
                setListaApresentarRotatividadeAnalitico(getFacade().
                        getRotatividadeAnaliticoDW().consultarRotatividadeSaldoMes(
                        dataIn, dataFin, mes, ano, getEmpresaFiltroBI().getCodigo().intValue(),
                        Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.SALDO_MES);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoRetornoTrancamentoHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamentoHoje())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = getRotatividadeAnaliticoDWVO().getDia();
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                        String.format(Contrato.sqlRetornoTrancamentos, new Object[]{
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "FROM autorizacaocobrancacliente acc " +
                                        "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        ") as convenioDescricao," +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.RETORNO_TRANCADO);
            }

            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoContratoTransferido() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdContratoTransferido().intValue() != 0  || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                                String.format(Contrato.sqlContratoTransferido, new Object[]{
                                        colunas,
                                        UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                                        dataInicio,
                                        dataFim,
                                        dataInicio,
                                        dataFim
                                }), nivelMontarDados));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.CONTRATO_TRANSFERIDO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoContratoTransferidoHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdContratoTransferido())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = getRotatividadeAnaliticoDWVO().getDia();
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);


                setListaApresentarContratos(
                        getFacade().getContrato().consultar(
                                String.format(Contrato.sqlContratoTransferido, new Object[]{
                                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                                "FROM autorizacaocobrancacliente acc " +
                                                "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                                "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                                ") as convenioDescricao," +
                                                "e.nome as nome_empresa, c.* ",
                                        UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                                        dataFim,
                                        dataFim,
                                        dataFim,
                                        dataFim
                                }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.CONTRATO_TRANSFERIDO);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    //
    public void consultarRotatividadePorSituacaoVencidoMes() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdVencidoMes().intValue() != 0 || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());
                /*setListaApresentarRotatividadeAnalitico(new ArrayList<RotatividadeAnaliticoDWVO>());
                setListaApresentarRotatividadeAnalitico(getFacade().getRotatividadeAnaliticoDW().consultarRotatividadeTodosClienteVencidoMes(
                getRotatividadeAnaliticoDWVO().getDia(),
                mes, ano, getEmpresaVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                 *
                 */

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                Date dataFinAnterior = Uteis.obterDataAnterior(dataIn, 1);
                Date dataInAnterior = Uteis.obterPrimeiroDiaMes(dataFinAnterior);


                String dataInicioAnterior = Uteis.getDataFormatoBD(dataInAnterior);
                String dataFimAnterior = Uteis.getDataFormatoBD(dataFinAnterior);

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;



//                setListaApresentarContratos(getFacade().getContrato().consultar(
//                        String.format(Contrato.sqlVencidosInicioMes,
//                        new Object[]{
//                            "*",
//                            getEmpresaVO().getCodigo(),
//                            dataInicio,
//                            dataFim,
//                            dataInicio,
//                            dataFim,
//                            dataInicio,
//                            dataFim
//                        }), Uteis.NIVELMONTARDADOS_ROBO));

                setListaApresentarContratos(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlVencidosFinalMes,
                        new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataInicioAnterior,
                            dataFimAnterior,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.VENCIDOS_MES);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    private void preencherVisibilidade(IndicadorMovimentacaoContratoEnum indicador) {
        setApresentarCancelado(IndicadorMovimentacaoContratoEnum.CANCELADO.equals(indicador));
        setApresentarDesistente(IndicadorMovimentacaoContratoEnum.DESISTENTE.equals(indicador));
        setApresentarMatriculado(IndicadorMovimentacaoContratoEnum.MATRICULADO.equals(indicador));
        setApresentarRematriculado(IndicadorMovimentacaoContratoEnum.REMATRICULADO.equals(indicador));
        setApresentarRetornoTrancado(IndicadorMovimentacaoContratoEnum.RETORNO_TRANCADO.equals(indicador));
        setApresentarTotalVirgentes(IndicadorMovimentacaoContratoEnum.TOTAL_VIGENTES.equals(indicador));
        setApresentarTrancado(IndicadorMovimentacaoContratoEnum.TRANCADO.equals(indicador));
        setApresentarContratoTransferido(IndicadorMovimentacaoContratoEnum.CONTRATO_TRANSFERIDO.equals(indicador));
        setApresentarVencidoMes(IndicadorMovimentacaoContratoEnum.VENCIDOS_MES.equals(indicador));
        setApresentarVirgenteMesAnterior(IndicadorMovimentacaoContratoEnum.VIGENTES_MES_ANTERIOR.equals(indicador));
        setApresentarAtivosMesAnterior(IndicadorMovimentacaoContratoEnum.ATIVOS_MES_ANTERIOR.equals(indicador));
        setApresentarVencidoMesAnterior(IndicadorMovimentacaoContratoEnum.VENCIDOS_MES_ANTERIOR.equals(indicador));
        setApresentarAtivosMes(IndicadorMovimentacaoContratoEnum.ATIVOS_MES.equals(indicador));
        setApresentarSaldoMes(IndicadorMovimentacaoContratoEnum.SALDO_MES.equals(indicador));
        setApresentarDependentesInicioMes(IndicadorMovimentacaoContratoEnum.DEPENDENTES_MES_ANTERIOR.equals(indicador));
        setApresentarDependentesFinalMes(IndicadorMovimentacaoContratoEnum.DEPENDENTES_MES.equals(indicador));
        setApresentarDependentesVinculado(IndicadorMovimentacaoContratoEnum.DEPENDENTES_VINCULADO.equals(indicador));
        setApresentarDependentesDesvinculado(IndicadorMovimentacaoContratoEnum.DEPENDENTES_DESVINCULADO.equals(indicador));
        setApresentarAgregaodresVinculado(IndicadorMovimentacaoContratoEnum.AGREGADORES_VINCULADOS.equals(indicador));
    }

    public void consultarRotatividadePorSituacaoVencidoAnterior() {
        try {
            if (getRotatividadeSinteticoDWVO().getQtdVencido() != 0) {
                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());

                Calendar cal = Calendario.getInstance();
                cal.setTime(dataIn);
                cal.add(Calendar.DAY_OF_MONTH, -1);

                dataIn = Uteis.obterPrimeiroDiaMes(cal.getTime());
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(cal.getTime());

                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);

                setListaApresentarContratos(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlVencidosInicioMes,
                                "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                        "        FROM autorizacaocobrancacliente acc " +
                                        "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                        "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                        "       ) as convenioDescricao, " +
                                        "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.VENCIDOS_MES_ANTERIOR);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorSituacaoVirgentesMesAnterior() throws Exception {
        try {
            if (getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior().intValue() != 0  || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia()) - 1;
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());
                int mesAtual = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int anoAtual = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());
                if (mes == 0) {
                    mes = 12;
                    ano = ano - 1;
                }

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                Calendar cal = Calendario.getInstance();
                cal.setTime(dataIn);
                cal.add(Calendar.DAY_OF_MONTH, -1);

                dataIn = Uteis.obterPrimeiroDiaMes(cal.getTime());
                dataFin = Uteis.obterUltimoDiaMesUltimaHora(cal.getTime());


                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                        "        FROM autorizacaocobrancacliente acc " +
                        "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                        "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                        "       ) as convenioDescricao, " +
                        "e.nome as nome_empresa, c.* " ;


                /*setListaApresentarRotatividadeAnalitico(new ArrayList<RotatividadeAnaliticoDWVO>());
                setListaApresentarRotatividadeAnalitico(
                getFacade().getRotatividadeAnaliticoDW().
                obterTodosClienteVirgentesMesAnteriorPorEmpresa(
                mes, ano,
                mesAtual, anoAtual,
                getEmpresaVO().getCodigo().intValue(),
                Uteis.NIVELMONTARDADOS_TELACONSULTA));*/
                setListaApresentarContratos(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlAtivosVencidos,
                        new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim,
                                dataFim,
                                dataFim,
                            dataFim
                        }), nivelMontarDados));
                getListaApresentarContratos().addAll(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlVencidosInicioMes,
                        new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataInicio,
                            dataFim,
                            dataInicio,
                            dataFim,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                if(getConfiguracaoSistemaVO().isUsaPlanoRecorrenteCompartilhado()
                        && !validadorBusinessInteligence){ //validador valida os dependentes separado
                    getListaApresentarContratos().addAll( getFacade().getContrato().consultar(String.format(ContratoDependente.SQL_DEPENDENTES_VIGENTE_LISTAR,
                            Uteis.getDataJDBC(dataFin),
                            Uteis.getDataJDBC(dataFin),
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo())) , nivelMontarDados));
                }


                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.VIGENTES_MES_ANTERIOR);
            }

            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorSituacaoVigenteInicioMes() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior())) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia()) - 1;
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                if (mes == 0) {
                    mes = 12;
                    ano = ano - 1;
                }
                /*setListaApresentarRotatividadeAnalitico(new ArrayList<RotatividadeAnaliticoDWVO>());
                setListaApresentarRotatividadeAnalitico(
                getFacade().getRotatividadeAnaliticoDW().
                consultarRotatividadeTodosClienteAtivoMes(
                getRotatividadeAnaliticoDWVO().getDia(), mes, ano,
                getEmpresaVO().getCodigo().intValue(),
                Uteis.NIVELMONTARDADOS_TELACONSULTA));*/

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                Calendar cal = Calendario.getInstance();
                cal.setTime(dataIn);
                cal.add(Calendar.DAY_OF_MONTH, -1);

                dataIn = Uteis.obterPrimeiroDiaMes(cal.getTime());
                dataFin = Uteis.obterUltimoDiaMesUltimaHora(cal.getTime());


                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);



                setListaApresentarContratos(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlAtivos,
                        new Object[]{
                            "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                    "FROM autorizacaocobrancacliente acc " +
                                    "INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                    "WHERE acc.ativa and acc.cliente = cli.codigo " +
                                    ") as convenioDescricao," +
                                   "e.nome as nome_empresa, c.* ",
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                                dataFim,
                                dataFim,
                            dataFim
                        }), Uteis.NIVELMONTARDADOS_ROBO));

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.ATIVOS_MES_ANTERIOR);
            }

            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorSituacaoVigenteMes() throws Exception {
        try {
            //if (getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior().intValue() != 0) {
            int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
            int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

            Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
            Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

            String dataInicio = Uteis.getDataFormatoBD(dataIn);
            String dataFim = Uteis.getDataFormatoBD(dataFin);
            int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
            String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                    "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                            "        FROM autorizacaocobrancacliente acc " +
                            "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                            "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                            "       ) as convenioDescricao, " +
                            "e.nome as nome_empresa, c.* " ;



            setListaApresentarContratos(getFacade().getContrato().consultar(
                    String.format(Contrato.sqlAtivos,
                    new Object[]{
                            colunas,
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                        dataFim,
                        dataFim,
                        dataFim,
                            dataFim,
                            dataFim,
                        dataFim
                    }), nivelMontarDados));


            preencherVisibilidade(IndicadorMovimentacaoContratoEnum.ATIVOS_MES);

            setMostrarPaginacao(true);

            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorSituacaoTodos() throws Exception {
        try {
            if (getRotatividadeSinteticoDWVO().getQtdTotal() != 0 || getValidadorBusinessInteligence()) {
                int mes = Uteis.getMesData(getRotatividadeAnaliticoDWVO().getDia());
                int ano = Uteis.getAnoData(getRotatividadeAnaliticoDWVO().getDia());

                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();

                Date dataFinAnterior = Uteis.obterDataAnterior(dataIn, 1);
                Date dataInAnterior = Uteis.obterPrimeiroDiaMes(dataFinAnterior);



                String dataInicio = Uteis.getDataFormatoBD(dataIn);
                String dataFim = Uteis.getDataFormatoBD(dataFin);

                String dataInicioAnterior = Uteis.getDataFormatoBD(dataInAnterior);
                String dataFimAnterior = Uteis.getDataFormatoBD(dataFinAnterior);
                int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_VALIDADOR_BI : Uteis.NIVELMONTARDADOS_ROBO;
                String colunas = getValidadorBusinessInteligence() ? "c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula, c.pessoa, cli.codigo as cliente, cli.codigomatricula, c.situacaocontrato, c.pessoaoriginal" :
                        "cli.codigo as cliente, (SELECT array_agg(cb.tipoconvenio||'^'||cb.descricao) as convenioDescricao " +
                                "        FROM autorizacaocobrancacliente acc " +
                                "                     INNER JOIN conveniocobranca cb on cb.codigo = acc.conveniocobranca " +
                                "        WHERE acc.ativa and acc.cliente = cli.codigo " +
                                "       ) as convenioDescricao, " +
                                "e.nome as nome_empresa, c.* " ;

                setListaApresentarContratos(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlAtivosVencidos,
                        new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataFim,
                            dataFim,
                            dataFim,
                                dataFim,
                                dataFim,
                            dataFim
                        }), nivelMontarDados));

//                getListaApresentarContratos().addAll(getFacade().getContrato().consultar(
//                        String.format(Contrato.sqlVencidosInicioMes,
//                        new Object[]{
//                            "*",
//                            getEmpresaVO().getCodigo(),
//                            dataInicio,
//                            dataFim,
//                            dataInicio,
//                            dataFim,
//                            dataInicio,
//                            dataFim
//                        }), Uteis.NIVELMONTARDADOS_ROBO));

                getListaApresentarContratos().addAll(getFacade().getContrato().consultar(
                        String.format(Contrato.sqlVencidosFinalMes,
                        new Object[]{
                                colunas,
                                UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo()),
                            dataFim,
                            dataInicioAnterior,
                            dataFimAnterior,
                            dataInicio,
                            dataFim
                        }), nivelMontarDados));

                if(getConfiguracaoSistemaVO().isUsaPlanoRecorrenteCompartilhado()
                        && !validadorBusinessInteligence){ //validador valida os dependentes separado
                    getListaApresentarContratos().addAll( getFacade().getContrato().consultar(String.format(ContratoDependente.SQL_DEPENDENTES_VIGENTE_LISTAR,
                            Uteis.getDataJDBC(dataFin),
                            Uteis.getDataJDBC(dataFin),
                            UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : (" and c.empresa = " + getEmpresaFiltroBI().getCodigo())) , nivelMontarDados));
                }

                if(getExibirAgregadoresMovimentacaoContrato()
                        && !validadorBusinessInteligence){
                    String dataInicioAgregador = Uteis.getDataFormatoBD(Calendario.subtrairDias(dataFin, getQtdDiasConsiderarAgregador())) + " 00:00:00";
                    String datafimAgregador = Uteis.getDataFormatoBD(dataFin) + " 23:59:59";

                    getListaApresentarContratos().addAll(getFacade().getContrato().consultar(
                            String.format(Contrato.SQL_AGREGADORES_MES_LISTAR,
                                    dataInicioAgregador,
                                    datafimAgregador,
                                    UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) ? "" : ("AND cli.empresa = " + getEmpresaFiltroBI().getCodigo()),
                                    dataInicioAgregador,
                                    getQtdCheckinConsiderarAgregador()),
                            true,
                            Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS));
                }

                preencherVisibilidade(IndicadorMovimentacaoContratoEnum.TOTAL_VIGENTES);
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public Integer obterEmpresaLogadoSistema() throws Exception {
        EmpresaVO emp = getEmpresaLogado();
        if (emp == null || emp.getCodigo().intValue() == 0) {
            return (new Integer(0));
        } else {
            return emp.getCodigo();
        }
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        rotatividadeAnaliticoDWVO = null;
        rotatividadeSinteticoDWVO = null;
        listaSituacaoRotatividadeAnalitico = new ArrayList<>();
        listaApresentarRotatividadeAnalitico = new ArrayList<>();
        listaSituacaoRotatividadeSintetico = new ArrayList<>();
        listaApresentarRotatividadeSintetico = new ArrayList<>();
        apresentarMes = "";
        mostrarPaginacao = false;
        apresentarVirgenteMesAnterior = false;
        apresentarTotalVirgentes = false;
        apresentarMatriculado = false;
        apresentarRematriculado = false;
        apresentarCancelado = false;
        apresentarTrancado = false;
        apresentarDesistente = false;
        apresentarRetornoTrancado = false;
        apresentarVencidoMes = false;
        pagina = 0;
        cliente = null;
    }

    public String getTitle() {
        return "O propósito deste relatório é: \n" +
                "i.  perceber se a academia está crescendo ou decrescendo.\n" +
                "ii. perceber os clientes que podem provocar decréscimo.\n" +
                "iii.provocar ações de venda ou retenção.";
    }

    public String getApresentarDia() {
        Date hoje = getRotatividadeAnaliticoDWVO().getDia();
        return Uteis.getData(hoje);
    }

    public RotatividadeAnaliticoDWVO getRotatividadeAnaliticoDWVO() {
        return rotatividadeAnaliticoDWVO;
    }

    public void setRotatividadeAnaliticoDWVO(RotatividadeAnaliticoDWVO rotatividadeAnaliticoDWVO) {
        this.rotatividadeAnaliticoDWVO = rotatividadeAnaliticoDWVO;
    }

    public String getApresentarMes() {
        if (apresentarMes == null) {
            apresentarMes = "";
        }
        return apresentarMes;
    }

    public void setApresentarMes(String apresentarMes) {
        this.apresentarMes = apresentarMes;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public List<RotatividadeAnaliticoDWVO> getListaApresentarRotatividadeAnalitico() {
        return listaApresentarRotatividadeAnalitico;
    }

    public void setListaApresentarRotatividadeAnalitico(List<RotatividadeAnaliticoDWVO> listaApresentarRotatividadeAnalitico) {
        this.listaApresentarRotatividadeAnalitico = listaApresentarRotatividadeAnalitico;
    }

    public List<RotatividadeSinteticoDWVO> getListaApresentarRotatividadeSintetico() {
        return listaApresentarRotatividadeSintetico;
    }

    public void setListaApresentarRotatividadeSintetico(List<RotatividadeSinteticoDWVO> listaApresentarRotatividadeSintetico) {
        this.listaApresentarRotatividadeSintetico = listaApresentarRotatividadeSintetico;
    }

    public List<RotatividadeAnaliticoDWVO> getListaSituacaoRotatividadeAnalitico() {
        return listaSituacaoRotatividadeAnalitico;
    }

    public void setListaSituacaoRotatividadeAnalitico(List<RotatividadeAnaliticoDWVO> listaSituacaoRotatividadeAnalitico) {
        this.listaSituacaoRotatividadeAnalitico = listaSituacaoRotatividadeAnalitico;
    }

    public List<RotatividadeSinteticoDWVO> getListaSituacaoRotatividadeSintetico() {
        return listaSituacaoRotatividadeSintetico;
    }

    public void setListaSituacaoRotatividadeSintetico(List<RotatividadeSinteticoDWVO> listaSituacaoRotatividadeSintetico) {
        this.listaSituacaoRotatividadeSintetico = listaSituacaoRotatividadeSintetico;
    }

    public RotatividadeSinteticoDWVO getRotatividadeSinteticoDWVO() {
        return rotatividadeSinteticoDWVO;
    }

    public void setRotatividadeSinteticoDWVO(RotatividadeSinteticoDWVO rotatividadeSinteticoDWVO) {
        this.rotatividadeSinteticoDWVO = rotatividadeSinteticoDWVO;
    }

    public Boolean getMostrarPaginacao() {
        return mostrarPaginacao;
    }

    public void setMostrarPaginacao(Boolean mostrarPaginacao) {
        this.mostrarPaginacao = mostrarPaginacao;
    }

    public String getNumeroPaginacao() {
        if (getMostrarPaginacao()) {
            return "10";
        } else {
            return "";
        }
    }

    public void alterarNumeroPagina() {
        if (!getMostrarPaginacao()) {
            setPagina(1);
        }
    }

    public Integer getPagina() {
        return pagina;
    }

    public void setPagina(Integer pagina) {
        this.pagina = pagina;
    }

    public String getAbriRichModalRotatividadePorVigenteMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdVigentesMesAnterior())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',  980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorVencido() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdVencido())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDependentesInicioMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdDependentesMesAtual())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbrirRichModalRotatividadePorAgregadoresVinculadosMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdAgregadoresVinculadosMes())) {
            return "abrirPopup('faces/rotatividadeClienteAgregadoForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbrirRichModalRotatividadePorAgregadoresVinculadosHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdAgregadoresVinculadosHoje())) {
            return "abrirPopup('faces/rotatividadeClienteAgregadoForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDependentesFinalMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdDependentesFinalMesAtual())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorVencidoMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdVencidoMes())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorMatricula() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdMatriculado())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorMatriculaHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdMatriculadoHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorRematricula() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdRematriculado())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorRematriculaHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdRematriculadoHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorCancelado() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdCancelado())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorCanceladoHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdCanceladoHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorTrancado() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdTrancamento())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorTrancadoHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdTrancamentoHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 850, 650);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDesistente() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDesistente())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDesistenteHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDesistenteHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorRetornoTrancamento() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamento())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorRetornoTrancamentoHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdRetornoTrancamentoHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorContratoTransferido() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdContratoTransferido())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorContratoTransferidoHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdContratoTransferidoHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente',980, 700);";
        }
        return "";
    }


    public String getAbriRichModalRotatividadePorSaldoMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdSaldo())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public Boolean getApresentarCancelado() {
        return apresentarCancelado;
    }

    public void setApresentarCancelado(Boolean apresentarCancelado) {
        this.apresentarCancelado = apresentarCancelado;
    }

    public Boolean getApresentarDesistente() {
        return apresentarDesistente;
    }

    public void setApresentarDesistente(Boolean apresentarDesistente) {
        this.apresentarDesistente = apresentarDesistente;
    }
    public String getTituloListaResumo(){
        if(getApresentarMatriculado()){
            return "Matriculados";
        } else if(getApresentarRematriculado()){
            return "Rematriculados";
        } else if(getApresentarCancelado()){
            return "Cancelados";
        } else if(getApresentarTrancado()){
            return "Trancados";
        } else if(getApresentarContratoTransferido()){
            return "Contratos Transferidos";
        } else if(getApresentarDesistente()){
            return "Desistentes";
        } else if(getApresentarRetornoTrancado()){
            return "Retorno Trancado";
        } else if(getApresentarSaldoMes()){
            return "Saldo do mês";
        } else if(getApresentarAtivosMesAnterior()){
            return "Total de Ativos Início do Mês";
        } else if(getApresentarVencidoMesAnterior()){
            return "Vencidos Início do Mês";
        } else if(getApresentarVirgenteMesAnterior()){
            return "Total de Ativos + Vencidos Início Mês";
        } else if(getApresentarAtivosMes()){
            return "Total de Ativos do mês";
        } else if(getApresentarVencidoMes()){
            return "Vencidos do mês";
        } else if(getApresentarTotalVirgentes()) {
            return "Total de Ativos + Vencidos do mês";
        } else if(getApresentarDependentesInicioMes()){
            return "Dependentes Início Mês";
        } else if(getApresentarDependentesFinalMes()){
            return "Dependentes do mês";
        } else if(getApresentarDependentesVinculado()){
            return "Dependentes Vinculados";
        } else if(getApresentarDependentesDesvinculado()){
            return "Dependentes Desvinculados";
        } else if(getApresentarAgregaodresVinculado()){
            return "Clientes do contrato com agregadores";
        }
        return "";
    }
    public Boolean getApresentarMatriculado() {
        return apresentarMatriculado;
    }

    public void setApresentarMatriculado(Boolean apresentarMatriculado) {
        this.apresentarMatriculado = apresentarMatriculado;
    }

    public Boolean getApresentarRematriculado() {
        return apresentarRematriculado;
    }

    public void setApresentarRematriculado(Boolean apresentarRematriculado) {
        this.apresentarRematriculado = apresentarRematriculado;
    }

    public Boolean getApresentarRetornoTrancado() {
        return apresentarRetornoTrancado;
    }

    public void setApresentarRetornoTrancado(Boolean apresentarRetornoTrancado) {
        this.apresentarRetornoTrancado = apresentarRetornoTrancado;
    }

    public Boolean getApresentarTotalVirgentes() {
        return apresentarTotalVirgentes;
    }

    public void setApresentarTotalVirgentes(Boolean apresentarTotalVirgentes) {
        this.apresentarTotalVirgentes = apresentarTotalVirgentes;
    }

    public Boolean getApresentarTrancado() {
        return apresentarTrancado;
    }

    public void setApresentarTrancado(Boolean apresentarTrancado) {
        this.apresentarTrancado = apresentarTrancado;
    }

    public Boolean getApresentarVencidoMes() {
        return apresentarVencidoMes;
    }

    public void setApresentarVencidoMes(Boolean apresentarVencidoMes) {
        this.apresentarVencidoMes = apresentarVencidoMes;
    }

    public Boolean getApresentarVirgenteMesAnterior() {
        return apresentarVirgenteMesAnterior;
    }

    public void setApresentarVirgenteMesAnterior(Boolean apresentarVirgenteMesAnterior) {
        this.apresentarVirgenteMesAnterior = apresentarVirgenteMesAnterior;
    }

    public String inicializarRotatividadeAnaliticoDWControle() {
        return "";
    }

    public void setApresentarAtivosMes(Boolean apresentarAtivosMes) {
        this.apresentarAtivosMes = apresentarAtivosMes;
    }

    public Boolean getApresentarAtivosMes() {
        return apresentarAtivosMes;
    }

    public void setApresentarAtivosMesAnterior(Boolean apresentarAtivosMesAnterior) {
        this.apresentarAtivosMesAnterior = apresentarAtivosMesAnterior;
    }

    public Boolean getApresentarAtivosMesAnterior() {
        return apresentarAtivosMesAnterior;
    }

    public void setApresentarVencidoMesAnterior(Boolean apresentarVencidoMesAnterior) {
        this.apresentarVencidoMesAnterior = apresentarVencidoMesAnterior;
    }

    public Boolean getApresentarVencidoMesAnterior() {
        return apresentarVencidoMesAnterior;
    }

    public void setApresentarSaldoMes(Boolean apresentarSaldoMes) {
        this.apresentarSaldoMes = apresentarSaldoMes;
    }

    public Boolean getApresentarSaldoMes() {
        return apresentarSaldoMes;
    }

    public void setListaApresentarContratos(List<ContratoVO> listaApresentarContratos) {
        this.listaApresentarContratos = listaApresentarContratos;
    }

    public List<ContratoVO> getListaApresentarContratos() {
        if (listaApresentarContratos == null) {
            listaApresentarContratos = new ArrayList<>();
        }
        //Sempre retorna a lista ordenada por nome.
        listaApresentarContratos.sort((Comparator) (o1, o2) -> {
            ContratoVO c1 = (ContratoVO) o1;
            ContratoVO c2 = (ContratoVO) o2;
            return c1.getPessoa().getNome().compareToIgnoreCase(c2.getPessoa().getNome());
        });

        return listaApresentarContratos;
    }

    public void imprimirRelatorio() {
        try {
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros, listaApresentarContratos);
            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            System.out.println(e.getMessage());
        }
    }

    private void prepareParams(Map<String, Object> params, List listaObjetos) throws Exception {
        Integer emp = this.getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        params.put("nomeRelatorio", "MovimentacaoContratosRel");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Movimentação de Contratos");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", listaObjetos);

        params.put("qtdMatriculado", rotatividadeAnaliticoDWVO.getQtdMatriculado());
        params.put("qtdRematriculado", rotatividadeAnaliticoDWVO.getQtdRematriculado());
        params.put("qtdTrancamento", rotatividadeAnaliticoDWVO.getQtdTrancamento());
        params.put("qtdContratoTransferido", rotatividadeAnaliticoDWVO.getQtdContratoTransferido());
        params.put("qtdCancelado", rotatividadeAnaliticoDWVO.getQtdCancelado());
        params.put("qtdDesistente", rotatividadeAnaliticoDWVO.getQtdDesistente());
        params.put("qtdRetornoTrancamento", rotatividadeAnaliticoDWVO.getQtdRetornoTrancamento());
        params.put("qtdVencidoMes", rotatividadeAnaliticoDWVO.getQtdVencidoMes());
        params.put("qtdTotal", rotatividadeSinteticoDWVO.getQtdTotal());

        params.put("filtros", getFiltros());
        params.put("apresentarCancelados",getApresentarCancelado());
        //params.put("dataIni", Uteis.getData());
        //params.put("dataFim", Uteis.getData(dataPeriodoFinal));
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "MovimentacaoContratosRel" + ".jrxml");
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getFiltros() {
        String filtros = "";
        if (getEmpresaFiltroBI() != null && getEmpresaFiltroBI().getCodigo() != null && getEmpresaFiltroBI().getCodigo() != 0) {
            filtros = "Empresa: " + getEmpresaFiltroBI().getNome() + " ";
        }
        if (rotatividadeAnaliticoDWVO.getDia() != null) {
            filtros += "Data: " + Uteis.getData(rotatividadeAnaliticoDWVO.getDia()) + " ";
        }
        if (apresentarMatriculado) {
            filtros += "- Matriculados";
        } else if (apresentarRematriculado) {
            filtros += "- Rematriculados";
        } else if (apresentarCancelado) {
            filtros += "- Cancelados";
        } else if (apresentarTrancado) {
            filtros += "- Trancados";
        } else if (apresentarContratoTransferido) {
            filtros += "- Contratos Transferidos";
        } else if (apresentarDesistente) {
            filtros += "- Desistentes";
        } else if (apresentarRetornoTrancado) {
            filtros += "- Retorno de Trancamentos";
        } else if (apresentarSaldoMes) {
            filtros += "- Saldo do Mês";
        } else if (apresentarAtivosMesAnterior) {
            filtros += "- Total de Ativos Início do Mês";
        } else if (apresentarVencidoMesAnterior) {
            filtros += "- Vencidos Início do Mês";
        } else if (apresentarVirgenteMesAnterior) {
            filtros += "- Total de Ativos + Vencidos Início Mês";
        } else if (apresentarAtivosMes) {
            filtros += "- Total de Ativos do mês";
        } else if (apresentarVencidoMes) {
            filtros += "- Vencidos do mês";
        } else if (apresentarTotalVirgentes) {
            filtros += "- Total de Ativos + Vencidos do mês";
        }
        return filtros;
    }

    public Boolean getApresentarContratoTransferido() {
        return apresentarContratoTransferido;
    }

    public void setApresentarContratoTransferido(Boolean apresentarContratoTransferido) {
        this.apresentarContratoTransferido = apresentarContratoTransferido;
    }

    public void consultarRotatividadePorDependentesInicioMes() {
        try {
            if (getRotatividadeSinteticoDWVO().getQtdDependentesMesAtual() != 0 || getValidadorBusinessInteligence()) {
                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());

                Calendar cal = Calendario.getInstance();
                cal.setTime(dataIn);
                cal.add(Calendar.DAY_OF_MONTH, -1);

                Date dataFimMesAnterior = Uteis.obterUltimoDiaMesUltimaHora(cal.getTime());

                consultarRotatividadePorDependentesVigentes(IndicadorMovimentacaoContratoEnum.DEPENDENTES_MES_ANTERIOR, dataFimMesAnterior, getEmpresaFiltroBI().getCodigo());
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorDependentesFinalMes() {
        try {
            if (getRotatividadeSinteticoDWVO().getQtdDependentesFinalMesAtual() != 0 || getValidadorBusinessInteligence()) {
                consultarRotatividadePorDependentesVigentes(IndicadorMovimentacaoContratoEnum.DEPENDENTES_MES, getRotatividadeAnaliticoDWVO().getDia(), getEmpresaFiltroBI().getCodigo());
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorDependentesVigentes(IndicadorMovimentacaoContratoEnum indicadorMovimentacaoContratoEnum, Date dataConsulta , Integer empresa ) throws Exception {
        int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA : Uteis.NIVELMONTARDADOS_ROBO;


        setListaApresentarContratos(getFacade().getContrato().consultar(
                String.format(ContratoDependente.SQL_DEPENDENTES_VIGENTE_LISTAR,
                        Uteis.getDataJDBC(dataConsulta),
                        Uteis.getDataJDBC(dataConsulta),
                        UteisValidacao.emptyNumber(empresa) ? "" : (" and c.empresa = " + empresa)) , nivelMontarDados));

        preencherVisibilidade(indicadorMovimentacaoContratoEnum);

    }

    public void consultarRotatividadePorAgregadores(IndicadorMovimentacaoContratoEnum indicadorMovimentacaoContratoEnum, Date dataConsulta, boolean consultarAgregadoresHoje, Integer empresa, Integer qtdCheckInConsiderarAgregador, Integer qtdDiasConsiderarAgregador) throws Exception {

        String dataInicio = Uteis.getDataFormatoBD(Calendario.subtrairDias(dataConsulta, qtdDiasConsiderarAgregador)) + " 00:00:00";
        String datafim = Uteis.getDataFormatoBD(dataConsulta) + " 23:59:59";

        if (consultarAgregadoresHoje) {
            setListaApresentarContratos(getFacade().getContrato().consultar(
                    String.format(Contrato.SQL_AGREGADORES_HOJE_LISTAR,
                            dataInicio,
                            datafim,
                            UteisValidacao.emptyNumber(empresa) ? "" : ("AND cli.empresa = " + empresa),
                            dataInicio,
                            qtdCheckInConsiderarAgregador,
                            Uteis.getDataFormatoBD(dataConsulta)),
                    true,
                    Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS));
        } else {
            setListaApresentarContratos(getFacade().getContrato().consultar(
                    String.format(Contrato.SQL_AGREGADORES_MES_LISTAR,
                            dataInicio,
                            datafim,
                            UteisValidacao.emptyNumber(empresa) ? "" : ("AND cli.empresa = " + empresa),
                            dataInicio,
                            qtdCheckInConsiderarAgregador),
                    true,
                    Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS));
        }
        preencherVisibilidade(indicadorMovimentacaoContratoEnum);

    }

    public Boolean getApresentarDependentesInicioMes() {
        return apresentarDependentesInicioMes;
    }

    public void setApresentarDependentesInicioMes(Boolean apresentarDependentesInicioMes) {
        this.apresentarDependentesInicioMes = apresentarDependentesInicioMes;
    }

    public Boolean getApresentarDependentesFinalMes() {
        return apresentarDependentesFinalMes;
    }

    public void setApresentarDependentesFinalMes(Boolean apresentarDependentesFinalMes) {
        this.apresentarDependentesFinalMes = apresentarDependentesFinalMes;
    }

    public Boolean getDesconsiderarCancelamentoMudancaPlano() {
        if(desconsiderarCancelamentoMudancaPlano == null){
            desconsiderarCancelamentoMudancaPlano = false;
        }
        return desconsiderarCancelamentoMudancaPlano;
    }

    public void setDesconsiderarCancelamentoMudancaPlano(Boolean desconsiderarCancelamentoMudancaPlano) {
        this.desconsiderarCancelamentoMudancaPlano = desconsiderarCancelamentoMudancaPlano;
    }

    public Boolean getDesconsiderarCancelamentoMudancaPlanoNaLista() {
        if(desconsiderarCancelamentoMudancaPlanoNaLista == null){
            desconsiderarCancelamentoMudancaPlanoNaLista = false;
        }
        return desconsiderarCancelamentoMudancaPlanoNaLista;
    }

    public void setDesconsiderarCancelamentoMudancaPlanoNaLista(Boolean desconsiderarCancelamentoMudancaPlanoNaLista) {
        this.desconsiderarCancelamentoMudancaPlanoNaLista = desconsiderarCancelamentoMudancaPlanoNaLista;
    }

    public void consultarRotatividadePorDependentesVinculados(IndicadorMovimentacaoContratoEnum indicadorMovimentacaoContratoEnum, Date datainicio, Date dataFinal, Integer empresa) throws Exception {
        int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA : Uteis.NIVELMONTARDADOS_ROBO;


        setListaApresentarContratos(
                getFacade().getContrato().consultar(
                        String.format(ContratoDependenteHistorico.SQL_DEPENDENTES_VINCULADOS_LISTAR,
                                Uteis.getDataJDBC(datainicio),
                                Uteis.getDataJDBC(dataFinal),
                                UteisValidacao.emptyNumber(empresa) ? "" : (" and c.empresa = " + empresa)) , nivelMontarDados));

        preencherVisibilidade(indicadorMovimentacaoContratoEnum);
    }

    public void consultarRotatividadePorDependentesVinculadosHoje() {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdDependentesVinculadosHoje() != 0) {
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                consultarRotatividadePorDependentesVinculados(IndicadorMovimentacaoContratoEnum.DEPENDENTES_VINCULADO, dataFin, dataFin, getEmpresaFiltroBI().getCodigo()) ;
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorAgregadoresVinculadosHoje() {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdAgregadoresVinculadosHoje() != 0) {
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                consultarRotatividadePorAgregadores(IndicadorMovimentacaoContratoEnum.AGREGADORES_VINCULADOS, dataFin, true, getEmpresaFiltroBI().getCodigo(), getQtdCheckinConsiderarAgregador(), getQtdDiasConsiderarAgregador()); ;
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorAgregadoresVinculadosMes() {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdAgregadoresVinculadosMes() != 0) {
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                consultarRotatividadePorAgregadores(IndicadorMovimentacaoContratoEnum.AGREGADORES_VINCULADOS, dataFin, false, getEmpresaFiltroBI().getCodigo(), getQtdCheckinConsiderarAgregador(), getQtdDiasConsiderarAgregador()); ;
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorDependentesVinculadosMes() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdDependentesVinculados().intValue() != 0 || getValidadorBusinessInteligence()) {
                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                consultarRotatividadePorDependentesVinculados(IndicadorMovimentacaoContratoEnum.DEPENDENTES_VINCULADO, dataIn, dataFin, getEmpresaFiltroBI().getCodigo()) ;
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorDependentesDesvinculados(IndicadorMovimentacaoContratoEnum indicadorMovimentacaoContratoEnum, Date datainicio, Date dataFinal, Integer empresa) throws Exception {
        int nivelMontarDados = getValidadorBusinessInteligence() ? Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA : Uteis.NIVELMONTARDADOS_ROBO;


        setListaApresentarContratos(
                getFacade().getContrato().consultar(
                        String.format(ContratoDependenteHistorico.SQL_DEPENDENTES_DESVINCULADOS_LISTAR,
                                Uteis.getDataJDBC(datainicio),
                                Uteis.getDataJDBC(dataFinal),
                                UteisValidacao.emptyNumber(empresa) ? "" : (" and c.empresa = " + empresa)) , nivelMontarDados));

        preencherVisibilidade(indicadorMovimentacaoContratoEnum);
    }

    public void consultarRotatividadePorDependentesDesvinculadosHoje() throws Exception {
        try {
            if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDependentesDesvinculadosHoje())) {
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                consultarRotatividadePorDependentesDesvinculados(IndicadorMovimentacaoContratoEnum.DEPENDENTES_DESVINCULADO, dataFin, dataFin, getEmpresaFiltroBI().getCodigo()) ;
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarRotatividadePorDependentesDesvinculadosMes() throws Exception {
        try {
            if (getRotatividadeAnaliticoDWVO().getQtdDependentesDesvinculados().intValue() != 0 || getValidadorBusinessInteligence()) {
                Date dataIn = Uteis.obterPrimeiroDiaMes(getRotatividadeAnaliticoDWVO().getDia());
                Date dataFin = getRotatividadeAnaliticoDWVO().getDia();
                consultarRotatividadePorDependentesDesvinculados(IndicadorMovimentacaoContratoEnum.DEPENDENTES_DESVINCULADO, dataIn, dataFin, getEmpresaFiltroBI().getCodigo()) ;
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Boolean getApresentarDependentesVinculado() {
        return apresentarDependentesVinculado;
    }

    public void setApresentarDependentesVinculado(Boolean apresentarDependentesVinculado) {
        this.apresentarDependentesVinculado = apresentarDependentesVinculado;
    }

    public Boolean getApresentarDependentesDesvinculado() {
        return apresentarDependentesDesvinculado;
    }

    public void setApresentarDependentesDesvinculado(Boolean apresentarDependentesDesvinculado) {
        this.apresentarDependentesDesvinculado = apresentarDependentesDesvinculado;
    }
    public String getAbriRichModalRotatividadePorDependentesVinculadosHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDependentesVinculadosHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDependentesVinculados() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDependentesVinculados())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDependentesDesvinculadosHoje() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDependentesDesvinculadosHoje())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDependentesDesvinculados() {
        if (!UteisValidacao.emptyNumber(getRotatividadeAnaliticoDWVO().getQtdDependentesDesvinculados())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public String getAbriRichModalRotatividadePorDependentesFimMes() {
        if (!UteisValidacao.emptyNumber(getRotatividadeSinteticoDWVO().getQtdDependentesFinalMesAtual())) {
            return "abrirPopup('faces/rotatividadeClienteForm.jsp', 'RotatividadeCliente', 980, 700);";
        }
        return "";
    }

    public Boolean getValidadorBusinessInteligence() {
        return validadorBusinessInteligence;
    }

    public void setValidadorBusinessInteligence(Boolean validadorBusinessInteligence) {
        this.validadorBusinessInteligence = validadorBusinessInteligence;
    }

    public String getItemExportacao(){
        if(getApresentarMatriculado()){
            return ItemExportacaoEnum.ROTATIVIDADE_MATRICULADO.getId();
        } else if(getApresentarRematriculado()){
            return ItemExportacaoEnum.ROTATIVIDADE_REMATRICULADO.getId();
        } else if(getApresentarCancelado()){
            return ItemExportacaoEnum.ROTATIVIDADE_CANCELADO.getId();
        } else if(getApresentarTrancado()){
            return ItemExportacaoEnum.ROTATIVIDADE_TRANCADO.getId();
        } else if(getApresentarContratoTransferido()){
            return ItemExportacaoEnum.ROTATIVIDADE_CONTRATO_TRANSFERIDO.getId();
        } else if(getApresentarDesistente()){
            return ItemExportacaoEnum.ROTATIVIDADE_DESISTENTE.getId();
        } else if(getApresentarRetornoTrancado()){
            return ItemExportacaoEnum.ROTATIVIDADE_RETORNO_TRANCADO.getId();
        } else if(getApresentarSaldoMes()){
            return ItemExportacaoEnum.ROTATIVIDADE_SALDO_MES.getId();
        } else if(getApresentarAtivosMesAnterior()){
            return ItemExportacaoEnum.ROTATIVIDADE_ATIVOS_MES_ANTERIOR.getId();
        } else if(getApresentarVencidoMesAnterior()){
            return ItemExportacaoEnum.ROTATIVIDADE_VENCIDOS_MES_ANTERIOR.getId();
        } else if(getApresentarVirgenteMesAnterior()){
            return ItemExportacaoEnum.ROTATIVIDADE_VIGENTES_MES_ANTERIOR.getId();
        } else if(getApresentarAtivosMes()){
            return ItemExportacaoEnum.ROTATIVIDADE_ATIVOS_MES.getId();
        } else if(getApresentarVencidoMes()){
            return ItemExportacaoEnum.ROTATIVIDADE_VENCIDOS_MES.getId();
        } else if(getApresentarTotalVirgentes()) {
            return ItemExportacaoEnum.ROTATIVIDADE_TOTAL_VIGENTES.getId();
        } else if(getApresentarDependentesInicioMes()){
            return ItemExportacaoEnum.ROTATIVIDADE_DEPENDENTES_MES_ANTERIOR.getId();
        } else if(getApresentarDependentesFinalMes()){
            return ItemExportacaoEnum.ROTATIVIDADE_DEPENDENTES_MES.getId();
        } else if(getApresentarDependentesVinculado()){
            return ItemExportacaoEnum.ROTATIVIDADE_DEPENDENTES_VINCULADO.getId();
        } else if(getApresentarDependentesDesvinculado()){
            return ItemExportacaoEnum.ROTATIVIDADE_DEPENDENTES_DESVINCULADO.getId();
        } else if(getApresentarAgregaodresVinculado()){
            return ItemExportacaoEnum.ROTATIVIDADE_AGREGADORESS_VINCULADO.getId();
        }
        return "";
    }

    public Boolean getExibirAgregadoresMovimentacaoContrato() {
        if (exibirAgregadoresMovimentacaoContrato == null){
            exibirAgregadoresMovimentacaoContrato = false;
        }
        return exibirAgregadoresMovimentacaoContrato;
    }

    public void setExibirAgregadoresMovimentacaoContrato(Boolean exibirAgregadoresMovimentacaoContrato) {
        this.exibirAgregadoresMovimentacaoContrato = exibirAgregadoresMovimentacaoContrato;
    }

    public Integer getQtdCheckinConsiderarAgregador() {
        if (qtdCheckinConsiderarAgregador == null){
            qtdCheckinConsiderarAgregador = 1;
        }
        return qtdCheckinConsiderarAgregador;
    }

    public void setQtdCheckinConsiderarAgregador(Integer qtdCheckinConsiderarAgregador) {
        this.qtdCheckinConsiderarAgregador = qtdCheckinConsiderarAgregador;
    }

    public Integer getQtdDiasConsiderarAgregador() {
        if (qtdDiasConsiderarAgregador == null){
            qtdDiasConsiderarAgregador = 30;
        }
        return qtdDiasConsiderarAgregador;
    }

    public void setQtdDiasConsiderarAgregador(Integer qtdDiasConsiderarAgregador) {
        this.qtdDiasConsiderarAgregador = qtdDiasConsiderarAgregador;
    }

    public Boolean getApresentarAgregaodresVinculado() {
        return apresentarAgregaodresVinculado;
    }

    public void setApresentarAgregaodresVinculado(Boolean apresentarAgregaodresVinculado) {
        this.apresentarAgregaodresVinculado = apresentarAgregaodresVinculado;
    }

    public void gravarConfiguracoes() {
        try {
            if (!UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo())) {
                getFacade().getConfiguracaoBI().incluirPorBI(BIEnum.ROTATIVIDADE_CONTRATO, getEmpresaFiltroBI().getCodigo(), configuracoes);
            }
            for (ConfiguracaoBIVO configuracaoBIVO: configuracoes){
                if (configuracaoBIVO.getConfiguracao().equals(ConfiguracaoBIEnum.EXIBIR_AGREGADORES)){
                    setExibirAgregadoresMovimentacaoContrato(configuracaoBIVO.getValorAsBoolean());
                }
                if (configuracaoBIVO.getConfiguracao().equals(ConfiguracaoBIEnum.QTD_CHECKIN_CONSIDERAR_AGREGADOR)){
                    setQtdCheckinConsiderarAgregador(configuracaoBIVO.getValorAsInteger());
                }
                if (configuracaoBIVO.getConfiguracao().equals(ConfiguracaoBIEnum.QTD_DIAS_CONSIDERAR_AGREGADOR)){
                    setQtdDiasConsiderarAgregador(configuracaoBIVO.getValorAsInteger());
                }
            }
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    private ConfiguracaoBIVO getConfiguracao(ConfiguracaoBIEnum cfg, List<ConfiguracaoBIVO> cfgs){
        for(ConfiguracaoBIVO c : cfgs){
            if(c.getConfiguracao().equals(cfg)){
                return c;
            }
        }
        return null;
    }

    public List<ConfiguracaoBIVO> getConfiguracoes() {
        return configuracoes;
    }

    public void setConfiguracoes(List<ConfiguracaoBIVO> configuracoes) {
        this.configuracoes = configuracoes;
    }

    public boolean isExibirOpcoesAgregadores() {
        return exibirOpcoesAgregadores;
    }

    public void setExibirOpcoesAgregadores(boolean exibirOpcoesAgregadores) {
        this.exibirOpcoesAgregadores = exibirOpcoesAgregadores;
    }
}
