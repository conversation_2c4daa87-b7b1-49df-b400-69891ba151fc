package relatorio.controle.crm;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.crm.FaixaHorarioAcessoClienteVO;
import negocio.comuns.crm.FiltroCarteiraTO;
import negocio.comuns.crm.InfoCarteiraTO;
import negocio.comuns.crm.VinculosTipoTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.DataTableServerSideProperties;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.FaixaHorarioAcessoCliente;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static negocio.comuns.crm.FiltroCarteiraTO.descobrirTotalQuantidadeVinculoFiltroSelecionado;

public class CarteirasRel extends SuperRelatorio {

    private Colaborador colaboradorDao;
    private Empresa empresaDao;

    public CarteirasRel() throws Exception {
        colaboradorDao = new Colaborador(getCon());
        empresaDao = new Empresa(getCon());
    }

    public CarteirasRel(Connection con) throws Exception {
        super(con);
        colaboradorDao = new Colaborador(con);
        empresaDao = new Empresa(con);
    }

    public static void montarFiltroSQLMaisAcessados(StringBuilder sqlConsulta, List<FaixaHorarioAcessoClienteVO> periodos, String cliente, FiltroCarteiraTO filtroCarteira) throws Exception {
        if (!(filtroCarteira.getPeriodoAcesso().equals("") ||
                periodos.isEmpty())) {
            sqlConsulta.append(" AND ");

            if (filtroCarteira.getPeriodoAcesso().equals("semAcesso")) {
                sqlConsulta.append("coalesce(");
            }
            sqlConsulta.append(sqlPeriodoMaisAcessados(periodos, cliente));
            if (filtroCarteira.getPeriodoAcesso().equals("semAcesso")) {
                sqlConsulta.append(", '') =  ''");
            } else {
                sqlConsulta.append(" LIKE '").append(filtroCarteira.getPeriodoAcesso()).append("' ");
            }
        }
    }

    private static String sqlPeriodoMaisAcessados(List<FaixaHorarioAcessoClienteVO> periodos, String cliente) {
        StringBuilder sql = new StringBuilder();
        sql.append("(SELECT periodo FROM ( \n");
        sql.append(" SELECT SUM(COUNT) AS SOMA, PERIODO FROM (SELECT \n");
        sql.append("CASE \n");
        for (FaixaHorarioAcessoClienteVO periodo : periodos) {
            sql.append("WHEN CAST(SUBSTRING(CAST(dthrentrada AS varchar) FROM 12 FOR 5) AS TIME)\n");
            sql.append("BETWEEN '").append((periodo.getHoraInicial())).append("' AND '").append((periodo.getHoraFinal())).append("' THEN '").append((periodo.getNomePeriodo())).append("'\n");
        }
        sql.append(" END AS PERIODO, 1 AS COUNT\n");
        sql.append(" FROM acessocliente WHERE cliente = sdw.codigocliente\n");
        sql.append(" ORDER BY dthrentrada DESC LIMIT 30) AS TB GROUP BY PERIODO ORDER BY SOMA DESC LIMIT 1) AS TBT)\n");
        //pro caso da lista de períodos conter apenas um período cadastrado, é necessário fazer este UNION para evitar um erro do postgres
        return sql.toString().replaceFirst("UNION ALL ", "");
    }

    /**
     * Início das sugestões
     */
    private List<FiltroCarteiraTO> consultarSugestoesClientesSemVinculos(Integer codEmpresa, TipoColaboradorEnum tipoVinculo, boolean desconsiderarVinculoTreino, int qtdItens) throws Exception {
        StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                "  NULL        AS codColaborador,\n" +
                "  '%s'        AS tipoVinculo,\n" +
                "  ''          AS situacao,\n" +
                "  count(*)    AS qtd,\n" +
                "  cli.empresa AS codEmpresa\n" +
                "FROM cliente cli\n" +
                "  LEFT JOIN vinculo v\n" +
                "    ON (v.cliente = cli.codigo AND tipovinculo IN (%s))\n" +
                "WHERE 1 = 1\n" +
                "      AND tipovinculo IS null\n" +
                "      AND empresa = ?\n");
        if (tipoVinculo.equals(TipoColaboradorEnum.PROFESSOR)) {
            sqlConsulta.append("      AND cli.situacao = 'AT'\n");
        }
        String strVinculo = "'"+tipoVinculo.getSigla()+"'"+
                (desconsiderarVinculoTreino ? ",'"+TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()+"'": "");

        sqlConsulta.append("GROUP BY v.tipovinculo, cli.empresa\n" +
                "ORDER BY 4, 3\n" +
                "LIMIT ").append(qtdItens).append(";");

        PreparedStatement ps = getCon().prepareStatement(String.format(sqlConsulta.toString(), tipoVinculo.getSigla(), strVinculo));
        ps.setInt(1, codEmpresa);
        ResultSet resultSet = ps.executeQuery();
        return montarDadosSugestoes(resultSet, false, desconsiderarVinculoTreino, "Clientes sem vincunlos");
    }

    private List<FiltroCarteiraTO> consultarSugestoesColaboradoresIrregulares(Integer codEmpresa, boolean colaboradoresAtivos, int qtdItens) throws Exception {
        String sinal = (colaboradoresAtivos) ? "=" : "<>";

        String sqlConsulta = "SELECT\n" +
                "  col.codigo      AS codColaborador,\n" +
                "  tc.descricao    AS tipoVinculo,\n" +
                "  col.situacao    AS situacao,\n" +
                "  count(v.codigo) AS qtd,\n" +
                "  col.empresa     AS codEmpresa\n" +
                "FROM colaborador col LEFT JOIN vinculo v\n" +
                "    ON col.codigo = v.colaborador\n" +
                "  INNER JOIN tipocolaborador tc\n" +
                "    ON tc.colaborador = col.codigo and v.tipovinculo = tc.descricao \n" +
                "  INNER JOIN cliente cli\n" +
                "    ON cli.codigo = v.cliente \n" +
                "WHERE 1 = 1\n" +
                "      AND col.situacao %s 'AT'\n" +
                "      AND col.empresa = ? AND cli.empresa = ?\n" +
                "      AND (tc.descricao IS NOT null)\n" +
                "GROUP BY col.codigo, tc.descricao, col.situacao, col.empresa\n" +
                "HAVING count(v.codigo) %s 0\n" +
                "ORDER BY 4, 3, 2" +
                "LIMIT "+qtdItens+";";

        PreparedStatement ps = getCon().prepareStatement(String.format(sqlConsulta, sinal, sinal));
        ps.setInt(1, codEmpresa);
        ps.setInt(2, codEmpresa);
        ResultSet resultSet = ps.executeQuery();
        return montarDadosSugestoes(resultSet, false, "Colaboradores irregulares");
    }

    private List<FiltroCarteiraTO> consultarSugestoesInconsistencias(Integer codEmpresa) throws Exception {
        String sqlConsulta = "SELECT\n" +
                "  null           AS codColaborador,\n" +
                "  vi.tipovinculo AS tipoVinculo,\n" +
                "  ''             AS situacao,\n" +
                "  count(*)       AS qtd,\n" +
                "  cli.empresa    AS codEmpresa,\n" +
                "  col.empresa    AS codEmpresaCol\n" +
                "FROM vinculo vi\n" +
                "  INNER JOIN cliente cli\n" +
                "    ON vi.cliente = cli.codigo\n" +
                "  LEFT JOIN colaborador col\n" +
                "    ON vi.colaborador = col.codigo\n" +
                "WHERE 1 = 1\n" +
                "      AND col.empresa <> cli.empresa\n" +
                "      AND cli.empresa = ?\n" +
                "GROUP BY vi.tipovinculo, cli.empresa, col.empresa;";

        PreparedStatement ps = getCon().prepareStatement(sqlConsulta);
        ps.setInt(1, codEmpresa);
        ResultSet resultSet = ps.executeQuery();
        return montarDadosSugestoes(resultSet, true, "codEmpresaCol", "Carteira vazia");
    }

    private List<FiltroCarteiraTO> montarDadosSugestoes(ResultSet resultSet, boolean inconsistencia, String validador) throws Exception {
        return montarDadosSugestoes(resultSet, inconsistencia, false, validador);
    }

    private List<FiltroCarteiraTO> montarDadosSugestoes(ResultSet resultSet, boolean inconsistencia, String codEmpresa, String validador) throws Exception {
        return montarDadosSugestoes(resultSet, inconsistencia, false, codEmpresa, validador);
    }

    private List<FiltroCarteiraTO> montarDadosSugestoes(ResultSet resultSet, boolean inconsistencia, boolean desconsiderarVinculoTreino, String validador) throws Exception {
        return montarDadosSugestoes(resultSet, inconsistencia, desconsiderarVinculoTreino, "codEmpresa", validador);
    }

    /**
     * Em todas as sugestões os campos que devem aparecer são: codColaborador, tipoVinculo, situacao, qtd, codEmpresa
     */
    private List<FiltroCarteiraTO> montarDadosSugestoes(ResultSet resultSet, boolean inconsistencia, boolean desconsiderarVinculoTreino, String codEmpresa, String validador) throws Exception {
        List<FiltroCarteiraTO> itens = new ArrayList<FiltroCarteiraTO>();
        while (resultSet.next()) {
            FiltroCarteiraTO filtroCarteiraTO = new FiltroCarteiraTO();
            filtroCarteiraTO.setDesconsiderarVinculoTreino(desconsiderarVinculoTreino);
            filtroCarteiraTO.setSugestao(true);
            filtroCarteiraTO.setInconsistencia(inconsistencia);
            if (resultSet.getInt("codColaborador") > 0) {
                filtroCarteiraTO.setColaborador(colaboradorDao.consultarPorChavePrimaria(resultSet.getInt("codColaborador"), Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA));
            }
            if(resultSet.getString("tipoVinculo") != null && resultSet.getString("tipoVinculo").equals("ALUNOS_SEM_PROFESSOR_TREINO")){
                filtroCarteiraTO.setAlunosTreinoSemVinculo(true);
                filtroCarteiraTO.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            }else{
                filtroCarteiraTO.setTipoVinculo(resultSet.getString("tipoVinculo"));
            }
            filtroCarteiraTO.setQtdVinculos(resultSet.getInt("qtd"));
            filtroCarteiraTO.setEmpresaVO(empresaDao.consultarPorChavePrimaria(resultSet.getInt(codEmpresa), Uteis.NIVELMONTARDADOS_MINIMOS));
            itens.add(filtroCarteiraTO);
        }
        return itens;
    }

    public List<FiltroCarteiraTO> consultarSugestoes(Integer codEmpresa, boolean desconsiderarVinculoTreino, int qtdItens) throws Exception {
        List<FiltroCarteiraTO> itens = new ArrayList<>();
        itens.addAll(consultarSugestoesInconsistencias(codEmpresa));
        itens.addAll(consultarSugestoesClientesSemVinculos(codEmpresa, TipoColaboradorEnum.CONSULTOR, false, qtdItens));
        itens.addAll(consultarSugestoesColaboradoresIrregulares(codEmpresa, false, qtdItens));
        itens.addAll(consultarSugestoesClientesSemVinculos(codEmpresa, TipoColaboradorEnum.PROFESSOR, desconsiderarVinculoTreino, qtdItens));
        itens.addAll(consultarSugestoesColaboradoresIrregulares(codEmpresa, true, qtdItens));
        itens.addAll(consultarSugestoesClientesTreinoSemProfessor(codEmpresa));
        return itens;
    }

    public List<VinculoVO> consultarTodosVinculosSugestao(FiltroCarteiraTO filtroCarteira) throws Exception {
        List<VinculoVO> vinculoVOs = new ArrayList<VinculoVO>();
        PreparedStatement ps = consultarSugestao(filtroCarteira, null);
        ResultSet rs = ps.executeQuery();
        while (rs.next()) {
            VinculoVO vinculoVO = new VinculoVO();
            vinculoVO.getCliente().setCodigo(rs.getInt("codCliente"));
            vinculoVOs.add(vinculoVO);
        }

        return vinculoVOs;
    }

    private PreparedStatement consultarSugestao(FiltroCarteiraTO filtroCarteira, DataTableServerSideProperties dataTableServerSideProperties) throws Exception {
        PreparedStatement ps = null;
        List<FaixaHorarioAcessoClienteVO> periodos = obterPeriodosDeAcesso();
        if(filtroCarteira.isAlunosTreinoSemVinculo()){
            StringBuilder sqlConsulta = new StringBuilder(" SELECT sdw.codigocliente as codCliente, sdw.matricula as codMatricula, \n");
            sqlConsulta.append("sdw.nomecliente, sdw.situacao as situacaoCliente, \n");
            sqlConsulta.append("pes.nome as nomeColaborador, \n");
            sqlConsulta.append(" datavigenciaateajustada as fimcontrato, pesorisco as risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append(" FROM cliente cli \n");
            sqlConsulta.append(" INNER JOIN situacaoclientesinteticodw sdw ON sdw.codigocliente = cli.codigo \n");
            //sqlConsulta.append(" INNER JOIN usuariomovel us ON us.cliente = cli.codigo \n");
            sqlConsulta.append(" LEFT JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.tipovinculo IN ('TW') \n");
            sqlConsulta.append(" LEFT JOIN colaborador col ON vi.colaborador = col.codigo \n");
            sqlConsulta.append(" LEFT JOIN acessocliente ac ON cli.uacodigo = ac.codigo \n");
            sqlConsulta.append(" LEFT JOIN pessoa pes ON col.pessoa = pes.codigo \n");
            sqlConsulta.append(" WHERE vi.codigo is null AND cli.empresa = ").append(filtroCarteira.getEmpresaVO().getCodigo());
            sqlConsulta.append(" AND sdw.situacao = 'AT' \n");
            sqlConsulta.append(" AND cli.codigo in (select u.cliente from usuariomovel u where u.cliente = cli.codigo) \n ");

            if(dataTableServerSideProperties != null && dataTableServerSideProperties.isClausulaLikePresente()) {
                sqlConsulta.append("\n  AND lower(sdw.nomecliente) ~ '" + dataTableServerSideProperties.getClausulaLike() + "' \n");
            }

            if (dataTableServerSideProperties != null && dataTableServerSideProperties.isParametrosOrdenacaoPresentes()) {
                sqlConsulta.append(" ORDER BY ").append(dataTableServerSideProperties.getColOrdenar()).append(" ")
                        .append(dataTableServerSideProperties.getDirOrdenar()).append("\n");
            }

            if (dataTableServerSideProperties != null && dataTableServerSideProperties.isParametrosPaginacaoPresentes()
                    && !dataTableServerSideProperties.isSemLimitAndOffSet()) {
                sqlConsulta.append(" LIMIT ").append(dataTableServerSideProperties.getLimit()).append("\n");
                sqlConsulta.append(" OFFSET ").append(dataTableServerSideProperties.getOffset()).append("\n");
            }
            ps = getCon().prepareStatement(sqlConsulta.toString());
        }else if (filtroCarteira.isClientesSemVinculo()) {
            StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                    "  cli.codigo            AS codCliente,\n" +
                    "  cli.matricula         AS codMatricula,\n" +
                    "  sdw.nomecliente        AS nomeCliente,\n" +
                    "  pes.nome              AS nomeColaborador, \n" +
                    "  sdw.situacao          AS situacaoCliente,\n" +
                    "  sdw.datavigenciaateajustada        AS fimContrato,\n" +
                    "  sdw.pesorisco     AS risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append("FROM cliente cli\n");
            if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                String strVinculo = "'" + filtroCarteira.getTipoVinculo() + "'" +
                        (filtroCarteira.isDesconsiderarVinculoTreino() ? ",'" + TipoColaboradorEnum.PROFESSOR_TREINO.getSigla() + "'" : "");
                sqlConsulta.append("  LEFT JOIN vinculo v\n ON (v.cliente = cli.codigo AND v.tipovinculo IN (").append(strVinculo).append("))\n");
            } else {
                sqlConsulta.append("  LEFT JOIN vinculo v ON (v.cliente = cli.codigo AND v.tipovinculo = '").append(filtroCarteira.getTipoVinculo()).append("')\n");
            }
            sqlConsulta.append("  LEFT JOIN colaborador col\n");
            sqlConsulta.append("  ON v.colaborador = col.codigo\n");
            sqlConsulta.append("  INNER JOIN situacaoclientesinteticodw sdw\n" +
                    "    ON cli.codigo = sdw.codigocliente\n" +
                    "  LEFT JOIN acessocliente ac\n" +
                    "    ON cli.uacodigo = ac.codigo\n" +
                    "  LEFT JOIN pessoa pes\n" +
                    "    ON col.pessoa = pes.codigo\n" +
                    "WHERE 1 = 1\n" +
                    "      AND tipovinculo IS null\n" +
                    "      AND cli.empresa = ?\n");
            if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                sqlConsulta.append("      AND sdw.situacao = 'AT'\n");
            }

            if(dataTableServerSideProperties != null && dataTableServerSideProperties.isClausulaLikePresente()) {
                sqlConsulta.append("\n  AND lower(sdw.nomecliente) ~ '" + dataTableServerSideProperties.getClausulaLike() + "' \n");
            }

            sqlConsulta.append("GROUP BY cli.codigo, cli.matricula, sdw.nomecliente, pes.nome, \n" +
                    "  sdw.situacao, sdw.datavigenciaateajustada, sdw.pesorisco,\n" +
                    "  sdw.codigocliente, ac.dthrentrada\n");

            if (dataTableServerSideProperties != null && dataTableServerSideProperties.isParametrosOrdenacaoPresentes()) {
                sqlConsulta.append(" ORDER BY ").append(dataTableServerSideProperties.getColOrdenar()).append(" ")
                        .append(dataTableServerSideProperties.getDirOrdenar()).append("\n");
            }

            if (dataTableServerSideProperties != null && dataTableServerSideProperties.isParametrosPaginacaoPresentes()
                    && !dataTableServerSideProperties.isSemLimitAndOffSet()) {
                sqlConsulta.append(" LIMIT ").append(dataTableServerSideProperties.getLimit()).append("\n");
                sqlConsulta.append(" OFFSET ").append(dataTableServerSideProperties.getOffset()).append("\n");
            }
            ps = getCon().prepareStatement(sqlConsulta.toString());
            ps.setInt(1, filtroCarteira.getEmpresaVO().getCodigo());
            return ps;
        } else if (filtroCarteira.isClientesInconsistentes()) {

            StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                    "  cli.codigo            AS codCliente,\n" +
                    "  cli.matricula         AS codMatricula,\n" +
                    "  sdw.nomecliente       AS nomeCliente,\n" +
                    "  pes.nome              AS nomeColaborador,\n" +
                    "  sdw.situacao          AS situacaoCliente,\n" +
                    "  sdw.datavigenciaateajustada        AS fimContrato,\n" +
                    "  sdw.pesorisco     AS risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append("FROM cliente cli\n" +
                    "  LEFT JOIN vinculo vi\n" +
                    "    ON (vi.cliente = cli.codigo AND vi.tipovinculo = ?)\n" +
                    "  LEFT JOIN colaborador col\n" +
                    "    ON vi.colaborador = col.codigo\n" +
                    "  INNER JOIN situacaoclientesinteticodw sdw\n" +
                    "    ON cli.codigo = sdw.codigocliente\n" +
                    "  LEFT JOIN acessocliente ac\n" +
                    "    ON cli.uacodigo = ac.codigo\n" +
                    "  LEFT JOIN pessoa pes\n" +
                    "    ON col.pessoa = pes.codigo\n" +
                    "WHERE 1 = 1\n" +
                    "      AND col.empresa <> cli.empresa\n" +
                    "      AND col.empresa = ? and cli.empresa = ?\n");

            if(dataTableServerSideProperties != null && dataTableServerSideProperties.isClausulaLikePresente()) {
                sqlConsulta.append("\n  AND lower(sdw.nomecliente) ~ '" + dataTableServerSideProperties.getClausulaLike() + "' \n");
            }

            if (dataTableServerSideProperties != null && dataTableServerSideProperties.isParametrosOrdenacaoPresentes()) {
                sqlConsulta.append(" ORDER BY ").append(dataTableServerSideProperties.getColOrdenar()).append(" ")
                        .append(dataTableServerSideProperties.getDirOrdenar()).append("\n");
            }

            if (dataTableServerSideProperties != null && dataTableServerSideProperties.isParametrosPaginacaoPresentes()
                    && !dataTableServerSideProperties.isSemLimitAndOffSet()) {
                sqlConsulta.append(" LIMIT ").append(dataTableServerSideProperties.getLimit()).append("\n");
                sqlConsulta.append(" OFFSET ").append(dataTableServerSideProperties.getOffset()).append("\n");
            }
            ps = getCon().prepareStatement(sqlConsulta.toString());
            ps.setString(1, filtroCarteira.getTipoVinculo());
            ps.setInt(2, filtroCarteira.getEmpresaVO().getCodigo());
            ps.setInt(3, filtroCarteira.getEmpresaLogado());
            return ps;
        } else if (filtroCarteira.isConsultorCarteiraVazia() || filtroCarteira.isConsultorInativoIrregular()) {
            return obterStatementConsultarJSONFiltros(filtroCarteira, dataTableServerSideProperties);
        }

        return ps;
    }

    private PreparedStatement buscarSugestaoTodos(FiltroCarteiraTO filtroCarteira) throws Exception {
        PreparedStatement ps = null;
        List<FaixaHorarioAcessoClienteVO> periodos = obterPeriodosDeAcesso();
        if(filtroCarteira.isAlunosTreinoSemVinculo()){
            StringBuilder sqlConsulta = new StringBuilder(" SELECT sdw.codigocliente as codCliente, sdw.matricula as codMatricula, \n");
            sqlConsulta.append("sdw.nomecliente, sdw.situacao as situacaoCliente, \n");
            sqlConsulta.append("pes.nome as nomeColaborador, \n");
            sqlConsulta.append(" datavigenciaateajustada as fimcontrato, pesorisco as risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append(" FROM cliente cli \n");
            sqlConsulta.append(" INNER JOIN situacaoclientesinteticodw sdw ON sdw.codigocliente = cli.codigo \n");
            sqlConsulta.append(" INNER JOIN usuariomovel us ON us.cliente = cli.codigo \n");
            sqlConsulta.append(" LEFT JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.tipovinculo IN ('TW') \n");
            sqlConsulta.append(" LEFT JOIN colaborador col ON vi.colaborador = col.codigo \n");
            sqlConsulta.append(" LEFT JOIN acessocliente ac ON cli.uacodigo = ac.codigo \n");
            sqlConsulta.append(" LEFT JOIN pessoa pes ON col.pessoa = pes.codigo \n");
            sqlConsulta.append(" WHERE vi.codigo is null AND cli.empresa = ").append(filtroCarteira.getEmpresaVO().getCodigo());
            sqlConsulta.append(" AND sdw.situacao = 'AT'");

            ps = getCon().prepareStatement(sqlConsulta.toString());
        }else if (filtroCarteira.isClientesSemVinculo()) {
            StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                    "  cli.codigo            AS codCliente,\n" +
                    "  cli.matricula         AS codMatricula,\n" +
                    "  sdw.nomecliente        AS nomeCliente,\n" +
                    "  pes.nome              AS nomeColaborador, \n" +
                    "  sdw.situacao          AS situacaoCliente,\n" +
                    "  sdw.datavigenciaateajustada        AS fimContrato,\n" +
                    "  sdw.pesorisco     AS risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append("FROM cliente cli\n");
            if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                String strVinculo = "'" + filtroCarteira.getTipoVinculo() + "'" +
                        (filtroCarteira.isDesconsiderarVinculoTreino() ? ",'" + TipoColaboradorEnum.PROFESSOR_TREINO.getSigla() + "'" : "");
                sqlConsulta.append("  LEFT JOIN vinculo v\n ON (v.cliente = cli.codigo AND v.tipovinculo IN (").append(strVinculo).append("))\n");
            } else {
                sqlConsulta.append("  LEFT JOIN vinculo v ON (v.cliente = cli.codigo AND v.tipovinculo = '").append(filtroCarteira.getTipoVinculo()).append("')\n");
            }
            sqlConsulta.append("  LEFT JOIN colaborador col\n");
            sqlConsulta.append("  ON v.colaborador = col.codigo\n");
            sqlConsulta.append("  INNER JOIN situacaoclientesinteticodw sdw\n" +
                    "    ON cli.codigo = sdw.codigocliente\n" +
                    "  LEFT JOIN acessocliente ac\n" +
                    "    ON cli.uacodigo = ac.codigo\n" +
                    "  LEFT JOIN pessoa pes\n" +
                    "    ON col.pessoa = pes.codigo\n" +
                    "WHERE 1 = 1\n" +
                    "      AND tipovinculo IS null\n" +
                    "      AND cli.empresa = ?\n");
            if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                sqlConsulta.append("      AND sdw.situacao = 'AT'\n");
            }
            sqlConsulta.append("GROUP BY cli.codigo, cli.matricula, sdw.nomecliente, pes.nome, \n" +
                    "  sdw.situacao, sdw.datavigenciaateajustada, sdw.pesorisco,\n" +
                    "  sdw.codigocliente, ac.dthrentrada\n");

            ps = getCon().prepareStatement(sqlConsulta.toString());
            ps.setInt(1, filtroCarteira.getEmpresaVO().getCodigo());
            return ps;
        } else if (filtroCarteira.isClientesInconsistentes()) {

            StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                    "  cli.codigo            AS codCliente,\n" +
                    "  cli.matricula         AS codMatricula,\n" +
                    "  sdw.nomecliente       AS nomeCliente,\n" +
                    "  pes.nome              AS nomeColaborador,\n" +
                    "  sdw.situacao          AS situacaoCliente,\n" +
                    "  sdw.datavigenciaateajustada        AS fimContrato,\n" +
                    "  sdw.pesorisco     AS risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append("FROM cliente cli\n" +
                    "  LEFT JOIN vinculo vi\n" +
                    "    ON (vi.cliente = cli.codigo AND vi.tipovinculo = ?)\n" +
                    "  LEFT JOIN colaborador col\n" +
                    "    ON vi.colaborador = col.codigo\n" +
                    "  INNER JOIN situacaoclientesinteticodw sdw\n" +
                    "    ON cli.codigo = sdw.codigocliente\n" +
                    "  LEFT JOIN acessocliente ac\n" +
                    "    ON cli.uacodigo = ac.codigo\n" +
                    "  LEFT JOIN pessoa pes\n" +
                    "    ON col.pessoa = pes.codigo\n" +
                    "WHERE 1 = 1\n" +
                    "      AND col.empresa <> cli.empresa\n" +
                    "      AND col.empresa = ? and cli.empresa = ?\n");

            ps = getCon().prepareStatement(sqlConsulta.toString());
            ps.setString(1, filtroCarteira.getTipoVinculo());
            ps.setInt(2, filtroCarteira.getEmpresaVO().getCodigo());
            ps.setInt(3, filtroCarteira.getEmpresaLogado());
            return ps;
        } else {
            StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                    "  cli.codigo            AS codCliente,\n" +
                    "  cli.matricula         AS codMatricula,\n" +
                    "  sdw.nomecliente        AS nomeCliente,\n" +
                    "  pes.nome              AS nomeColaborador, \n" +
                    "  sdw.situacao          AS situacaoCliente,\n" +
                    "  sdw.datavigenciaateajustada        AS fimContrato,\n" +
                    "  sdw.pesorisco     AS risco,\n");
            sqlConsulta.append("  ('").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
            sqlConsulta.append("FROM cliente cli\n");
            if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                String strVinculo = "'" + filtroCarteira.getTipoVinculo() + "'" +
                        (filtroCarteira.isDesconsiderarVinculoTreino() ? ",'" + TipoColaboradorEnum.PROFESSOR_TREINO.getSigla() + "'" : "");
                sqlConsulta.append("  LEFT JOIN vinculo v\n ON (v.cliente = cli.codigo AND v.tipovinculo IN (").append(strVinculo).append("))\n");
            } else {
                sqlConsulta.append("  LEFT JOIN vinculo v ON (v.cliente = cli.codigo AND v.tipovinculo = '").append(filtroCarteira.getTipoVinculo()).append("')\n");
            }
            sqlConsulta.append("  LEFT JOIN colaborador col\n");
            sqlConsulta.append("  ON v.colaborador = col.codigo\n");
            sqlConsulta.append("  INNER JOIN situacaoclientesinteticodw sdw\n" +
                    "    ON cli.codigo = sdw.codigocliente\n" +
                    "  LEFT JOIN acessocliente ac\n" +
                    "    ON cli.uacodigo = ac.codigo\n" +
                    "  LEFT JOIN pessoa pes\n" +
                    "    ON col.pessoa = pes.codigo\n" +
                    "WHERE 1 = 1\n" +
                    "      AND tipovinculo IS NOT null\n" +
                    "      AND cli.empresa = ?\n");
            if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                sqlConsulta.append("      AND sdw.situacao = 'AT'\n");
            }
            sqlConsulta.append("GROUP BY cli.codigo, cli.matricula, sdw.nomecliente, pes.nome, \n" +
                    "  sdw.situacao, sdw.datavigenciaateajustada, sdw.pesorisco,\n" +
                    "  sdw.codigocliente, ac.dthrentrada\n");

            ps = getCon().prepareStatement(sqlConsulta.toString());
            ps.setInt(1, filtroCarteira.getEmpresaVO().getCodigo());
            return ps;
        }
        return ps;
    }

    private PreparedStatement consultarStateListaClientes(FiltroCarteiraTO filtroCarteiraTO) throws Exception {
        List<FaixaHorarioAcessoClienteVO> periodos = obterPeriodosDeAcesso();

        StringBuilder sqlConsulta = new StringBuilder();

        sqlConsulta.append("SELECT DISTINCT\n" +
                "  sdw.codigocliente            AS codCliente,\n" );
        sqlConsulta.append(" (select '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  ac.dthrentrada :: DATE from acessocliente ac where sdw.codigocliente = ac.cliente order by codigo desc limit 1 ) AS diasSemAcesso\n");
        montarSQLMaisAcessados(periodos, sqlConsulta);
        sqlConsulta.append("FROM situacaoclientesinteticodw sdw\n" +
                "  INNER JOIN vinculo vi\n" +
                "    ON sdw.codigocliente = vi.cliente\n" +
                (filtroCarteiraTO.getCodigoColaborador() > 0 ? "      AND colaborador = " + filtroCarteiraTO.getCodigoColaborador() + "\n" : "") +
                (!UteisValidacao.emptyString(filtroCarteiraTO.getTipoVinculo()) ? "      AND tipovinculo = '" + filtroCarteiraTO.getTipoVinculo() + "'\n" : "") +
                "  INNER JOIN cliente cli ON cli.codigo = sdw.codigocliente" +
                "  LEFT JOIN contrato cont ON sdw.codigopessoa = cont.pessoa\n" );
        if(UteisValidacao.emptyString(filtroCarteiraTO.getFiltroJoinTabela())){
            sqlConsulta.append(filtroCarteiraTO.getFiltroJoinTabela());
        }
        if (!filtroCarteiraTO.isTodosClientes()) {
            sqlConsulta.append("INNER JOIN pessoa pes\n" +
                    "    ON sdw.codigopessoa = pes.codigo\n");
        }
        sqlConsulta.append("WHERE 1 = 1\n" +
                "      AND sdw.empresaCliente = ?\n");
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getFiltroAvancado())) {
            sqlConsulta.append("          AND ").append(filtroCarteiraTO.getFiltroAvancado()).append("\n");
        }
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getSituacaoCliente())) {
            sqlConsulta.append("      AND (sdw.situacao = ?\n");
            sqlConsulta.append("      OR sdw.situacaocontrato = ?)\n");
        }
        montarFiltroSQLMaisAcessados(sqlConsulta, periodos, "sdw.codigocliente", filtroCarteiraTO);
        if (!filtroCarteiraTO.isTodosClientes()) {
            if (filtroCarteiraTO.getInicioCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro >= ?\n");
            }
            if (filtroCarteiraTO.getFimCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro <= ?\n");
            }
        }

        PreparedStatement ps = getCon().prepareStatement(sqlConsulta.toString());
        int i = 0;
        ps.setInt(++i, filtroCarteiraTO.getEmpresaVO().getCodigo());
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getSituacaoCliente())) {
            ps.setString(++i, filtroCarteiraTO.getSituacaoCliente());
            ps.setString(++i, filtroCarteiraTO.getSituacaoCliente());
        }
        if (!filtroCarteiraTO.isTodosClientes()) {
            if (filtroCarteiraTO.getInicioCadastro() != null) {
                ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteiraTO.getInicioCadastro(), "00:00:00"));
            }
            if (filtroCarteiraTO.getFimCadastro() != null) {
                ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteiraTO.getFimCadastro(), "23:59:59"));
            }
        }

        return ps;
    }

    private String consultarJSONSugestao(FiltroCarteiraTO filtroCarteira, DataTableServerSideProperties dataTableServerSideProperties) throws Exception {
        try (PreparedStatement ps = consultarSugestao(filtroCarteira, dataTableServerSideProperties)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosJSON(rs, dataTableServerSideProperties, filtroCarteira, null);
            }
        }
    }

    private String consultarListaClientes(FiltroCarteiraTO filtroCarteira) throws Exception {
        try (PreparedStatement ps = consultarStateListaClientes(filtroCarteira)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosString(rs, null, filtroCarteira, null);
            }
        }
    }

    public String consultarSugestaoTodos(FiltroCarteiraTO filtroCarteira) throws Exception {
        try (PreparedStatement ps = buscarSugestaoTodos(filtroCarteira)) {
            try (ResultSet rs = ps.executeQuery()) {
                return montarDadosString(rs, null, filtroCarteira, null);
            }
        }
    }

    /**
     * Final das sugestões
     */

    public List<InfoCarteiraTO> consultarColaboradores(FiltroCarteiraTO filtroCarteira,Boolean organizar) throws Exception {
        List<FaixaHorarioAcessoClienteVO> periodos = obterPeriodosDeAcesso();

        List<InfoCarteiraTO> itens = new ArrayList<>();

        StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                "  col.codigo AS codColaborador,\n" +
                "  pes.nome   AS nomeColaborador,\n" +
                "  (CASE\n" +
                "   WHEN vi.tipovinculo IS null THEN tc.descricao\n" +
                "   WHEN vi.tipovinculo IS NOT null THEN vi.tipovinculo\n" +
                "   END)      AS tipovinculo,\n" +
                "  (SELECT\n" +
                "  count(*)\n" +
                "   FROM vinculo vc\n" +
                "   INNER JOIN cliente cli ON vc.cliente = cli.codigo\n" +
                "   INNER JOIN situacaoclientesinteticodw sdw ON (vc.cliente = sdw.codigocliente\n" +
                "           AND vc.colaborador = col.codigo\n" +
                "           AND sdw.empresaCliente = ?\n");
        if (!UteisValidacao.emptyString(filtroCarteira.getSituacaoCliente())) {
            adicionandoSituacaoClienteWhere(filtroCarteira, sqlConsulta);
        }
        montarFiltroSQLMaisAcessados(sqlConsulta, periodos, "sdw.codigocliente", filtroCarteira);
        sqlConsulta.append(")\n");
        if (!filtroCarteira.isTodosClientes()) {
            sqlConsulta.append("INNER JOIN pessoa pes ON sdw.codigopessoa = pes.codigo\n");
            if (filtroCarteira.getInicioCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro >= ?\n");
            }
            if (filtroCarteira.getFimCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro <= ?\n");
            }
        }
        sqlConsulta.append("   WHERE 1 = 1\n" +
                "         AND vc.colaborador = col.codigo\n" +
                "         AND vi.tipovinculo = vc.tipovinculo\n");

        if (!UteisValidacao.emptyString(filtroCarteira.getFiltroAvancado())) {
            sqlConsulta.append("         AND (").append(filtroCarteira.getFiltroAvancado()).append(")\n");
        }

        sqlConsulta.append("  )          AS qtd,\n" +
                " pes.fotokey\n" +
                " FROM colaborador col\n" +
                "  INNER JOIN pessoa pes ON col.pessoa = pes.codigo and col.empresa = ?\n" +
                "  INNER JOIN tipocolaborador tc ON tc.colaborador = col.codigo\n");
        if (!UteisValidacao.emptyString(filtroCarteira.getTipoVinculo())  && !filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())) {
            sqlConsulta.append("   AND tc.descricao = '").append(filtroCarteira.getTipoVinculo()).append("'\n");
        }else if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())){
            if (organizar ) {
                sqlConsulta.append("   AND tc.descricao <> '").append(filtroCarteira.getTipoVinculo()).append("'\n");
            }else {
                sqlConsulta.append("   AND tc.descricao = '").append(filtroCarteira.getTipoVinculo()).append("'\n");
            }
        }
        sqlConsulta.append("  LEFT JOIN grupocolaboradorparticipante grupo\n" +
                "    ON grupo.colaboradorparticipante = col.codigo\n" +
                "  LEFT JOIN vinculo vi\n" +
                "    ON vi.colaborador = col.codigo\n");
        if (!UteisValidacao.emptyString(filtroCarteira.getTipoVinculo())) {
            sqlConsulta.append("   AND vi.tipovinculo = '").append(filtroCarteira.getTipoVinculo()).append("'\n");
        }
        sqlConsulta.append("       AND (vi.tipovinculo = tc.descricao\n" +
                "            OR vi.tipovinculo NOT IN\n" +
                "               (SELECT\n" +
                "                  descricao\n" +
                "                FROM tipocolaborador\n" +
                "                WHERE colaborador = col.codigo))\n" +
                "WHERE 1 = 1\n" +
                "      AND col.situacao = 'AT'\n");
        if (filtroCarteira.getGrupoColaboradorVO().getCodigo() > 0) {
            sqlConsulta.append("      AND grupo.grupocolaborador = ?\n");
        }
        sqlConsulta.append("GROUP BY col.codigo, pes.nome, vi.tipovinculo, tc.descricao, pes.fotokey\n" +
                " ORDER BY 2;");

        try (PreparedStatement ps = getCon().prepareStatement(sqlConsulta.toString())) {
            int i = 0;
            ps.setInt(++i, filtroCarteira.getEmpresaVO().getCodigo());
            if (!UteisValidacao.emptyString(filtroCarteira.getSituacaoCliente())) {
                i = adicionandoSituacaoClienteStatement(filtroCarteira, ps, i);
            }
            if (!filtroCarteira.isTodosClientes()) {
                if (filtroCarteira.getInicioCadastro() != null) {
                    ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteira.getInicioCadastro(), "00:00:00"));
                }
                if (filtroCarteira.getFimCadastro() != null) {
                    ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteira.getFimCadastro(), "23:59:59"));
                }
            }
            ps.setInt(++i, filtroCarteira.getEmpresaVO().getCodigo());
            if (filtroCarteira.getGrupoColaboradorVO().getCodigo() > 0) {
                ps.setInt(++i, filtroCarteira.getGrupoColaboradorVO().getCodigo());
            }
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    Integer codColaborador = rs.getInt("codColaborador");
                    InfoCarteiraTO infoCarteiraTO = obterInfoCarteiraTO(itens, codColaborador);
                    infoCarteiraTO.getColaborador().getPessoa().setNome(Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(rs.getString("nomecolaborador")));
                    infoCarteiraTO.getColaborador().getPessoa().setFotoKey(rs.getString("fotokey"));

                    VinculosTipoTO vinculosTipo = new VinculosTipoTO();
                    vinculosTipo.setTipoVinculo(rs.getString("tipovinculo"));
                    vinculosTipo.setQtdVinculos(rs.getInt("qtd"));
                    infoCarteiraTO.getQtdVinculosColaborador().add(vinculosTipo);
                    if (!itens.contains(infoCarteiraTO)) {
                        itens.add(infoCarteiraTO);
                    }
                }
            }
        }
        return itens;
    }

    private int adicionandoSituacaoClienteStatement(FiltroCarteiraTO filtroCarteira, PreparedStatement ps, int i) throws SQLException {
        String[] situacoes = filtroCarteira.getSituacaoCliente().split(",");
        for (String situacao : situacoes){
            ps.setString(++i, situacao);
        }
        for (String situacao : situacoes){
            ps.setString(++i, situacao);
        }
        return i;
    }

    private void adicionandoSituacaoClienteWhere(FiltroCarteiraTO filtroCarteira, StringBuilder sqlConsulta) {
        String[] situacoes = filtroCarteira.getSituacaoCliente().split(",");
        sqlConsulta.append("          AND (sdw.situacaocontrato in (");
        String virgula ="";
        for (String situacao : situacoes){
            sqlConsulta.append(virgula);
            sqlConsulta.append("?");
            virgula = ",";
        }
        sqlConsulta.append(") OR sdw.situacao in (");
        virgula="";
        for (String situacao : situacoes){
            sqlConsulta.append(virgula);
            sqlConsulta.append("?");
            virgula = ",";
        }
        sqlConsulta.append("))");
    }

    public void consultarQuantidadeDeVinculos(FiltroCarteiraTO filtroCarteira) throws Exception {
        List<FaixaHorarioAcessoClienteVO> periodos = obterPeriodosDeAcesso();
        StringBuilder sqlConsulta = new StringBuilder("SELECT\n" +
                "  vi.tipovinculo,\n" +
                "  (SELECT count(*) FROM\n" +
                "  (SELECT DISTINCT vinculo.cliente\n" +
                "   FROM vinculo\n" +
                "     INNER JOIN situacaoclientesinteticodw sdw\n" +
                "       ON (vinculo.cliente = sdw.codigocliente)\n" +
                "          AND vinculo.colaborador = col.codigo\n" +
                "          AND sdw.empresacliente = ?\n" +
                "          AND vinculo.tipovinculo = vi.tipovinculo\n");
        if (!UteisValidacao.emptyString(filtroCarteira.getSituacaoCliente())) {
            adicionandoSituacaoClienteWhere(filtroCarteira, sqlConsulta);
        }
        montarFiltroSQLMaisAcessados(sqlConsulta, periodos, "sdw.codigocliente", filtroCarteira);
        if (!filtroCarteira.isTodosClientes()) {
            sqlConsulta.append("INNER JOIN pessoa pes\n" +
                    "    ON sdw.codigopessoa = pes.codigo\n");
            if (filtroCarteira.getInicioCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro >= ?\n");
            }
            if (filtroCarteira.getFimCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro <= ?\n");
            }
        }
        if (!UteisValidacao.emptyString(filtroCarteira.getFiltroAvancado())) {
            sqlConsulta.append("    LEFT JOIN contrato cont");
            sqlConsulta.append("        ON sdw.codigocontrato = cont.codigo\n");
            sqlConsulta.append("    LEFT JOIN plano pl");
            sqlConsulta.append("        ON pl.codigo = cont.plano \n");
            if (filtroCarteira.getFiltroAvancado().contains("cli")) {
                sqlConsulta.append("    inner join cliente cli on cli.codigo = sdw.codigocliente\n");
            }
            sqlConsulta.append("WHERE 1 = 1 AND (").append(filtroCarteira.getFiltroAvancado()).append(")\n");
        }
        sqlConsulta.append(") AS foo  ) AS qtdVinculo\n" +
                "FROM colaborador col\n" +
                "  INNER JOIN tipocolaborador tc\n" +
                "    ON (col.codigo = tc.colaborador AND col.empresa = ?)\n" +
                "  LEFT JOIN vinculo vi\n" +
                "    ON vi.colaborador = col.codigo\n" +
                "WHERE 1 = 1\n" +
                "      AND col.codigo = ?\n");
        sqlConsulta.append("GROUP BY col.codigo, vi.tipovinculo\n" +
                "ORDER BY 2");

        PreparedStatement ps = getCon().prepareStatement(sqlConsulta.toString());
        int i = 0;
        ps.setInt(++i, filtroCarteira.getEmpresaVO().getCodigo());
        if (!UteisValidacao.emptyString(filtroCarteira.getSituacaoCliente())) {
            i = adicionandoSituacaoClienteStatement(filtroCarteira, ps, i);
        }
        if (!filtroCarteira.isTodosClientes()) {
            if (filtroCarteira.getInicioCadastro() != null) {
                ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteira.getInicioCadastro(), "00:00:00"));
            }
            if (filtroCarteira.getFimCadastro() != null) {
                ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteira.getFimCadastro(), "23:59:59"));
            }
        }
        ps.setInt(++i, filtroCarteira.getEmpresaVO().getCodigo());
        ps.setInt(++i, filtroCarteira.getCodigoColaborador());
        ResultSet rs = ps.executeQuery();
        while (rs.next()) {
            String tipoVinculo = rs.getString("tipoVinculo");
            Integer qtdVinculo = rs.getInt("qtdVinculo");

            for (FiltroCarteiraTO vinculosDoColaborador : filtroCarteira.getListaDeVinculos()) {
                if (vinculosDoColaborador.getTipoVinculo().equals(tipoVinculo)) {
                    vinculosDoColaborador.setQtdVinculos(qtdVinculo);
                }
            }
        }

        for (FiltroCarteiraTO vinculosDoColaborador : filtroCarteira.getListaDeVinculos()) {
            if (filtroCarteira.getTipoVinculo().equals(vinculosDoColaborador.getTipoVinculo())) {
                vinculosDoColaborador.setSelecionado(true);
            }
        }
    }

    private InfoCarteiraTO obterInfoCarteiraTO(List<InfoCarteiraTO> lista, Integer codColaborador) {
        for (InfoCarteiraTO infoCarteira : lista) {
            if (infoCarteira.getColaborador().getCodigo().equals(codColaborador)) {
                return infoCarteira;
            }
        }
        InfoCarteiraTO infoCarteira = new InfoCarteiraTO();
        infoCarteira.getColaborador().setCodigo(codColaborador);
        return infoCarteira;
    }

    public String consultarJSON(FiltroCarteiraTO filtroCarteiraTO) throws Exception {
        return consultarJSONPaginado(filtroCarteiraTO, null);
    }

    public String consultarCodigosClientes(FiltroCarteiraTO filtroCarteiraTO) throws Exception{
        return consultarListaClientes(filtroCarteiraTO);
    }

    public String consultarCodigosClientesInconsistenciaSugestao(Integer codEmpresaLogado, FiltroCarteiraTO filtroCarteiraTO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cli.codigo as codCliente ");
        sql.append("from vinculo vi ");
        sql.append("inner join cliente cli on vi.cliente = cli.codigo ");
        sql.append("left join colaborador col on vi.colaborador = col.codigo ");
        sql.append("where 1 = 1 ");
        sql.append("and col.empresa <> cli.empresa ");
        sql.append("and cli.empresa = ? ");
        sql.append("and vi.tipovinculo = ? ");
        sql.append("and col.empresa = ? ");
        try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
            int i = 0;
            ps.setInt(++i, codEmpresaLogado);
            ps.setString(++i, filtroCarteiraTO.getTipoVinculo());
            ps.setInt(++i, filtroCarteiraTO.getEmpresaVO().getCodigo());
            return returnCodClientes(ps);
        }
    }

    public String consultarCodigosClientesSemVinculoSugestao(boolean desconsiderarVinculoTreino, Integer codEmpresaLogado, FiltroCarteiraTO filtroCarteiraTO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cli.codigo as codCliente ");
        sql.append("FROM cliente cli ");
        sql.append("LEFT JOIN vinculo v ON (v.cliente = cli.codigo AND tipovinculo IN (%s)) ");
        sql.append("where 1 = 1 ");
        sql.append("AND tipovinculo IS null ");
        sql.append("AND empresa = ? ");
        if (filtroCarteiraTO.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
            sql.append("AND cli.situacao = 'AT' ");
        }
        String strVinculo = "'"+filtroCarteiraTO.getTipoVinculo()+"'"+
                (desconsiderarVinculoTreino ? ",'"+TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()+"'": "");

        try (PreparedStatement ps = getCon().prepareStatement(String.format(sql.toString(), strVinculo))) {
            int i = 0;
            ps.setInt(++i, codEmpresaLogado);
            return returnCodClientes(ps);
        }
    }

    public String consultarCodigosClientesTreinoSemProfessorSugestao(Integer codEmpresaLogado) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select sdw.codigocliente AS codCliente ");
        sql.append("FROM situacaoclientesinteticodw sdw ");
        sql.append("INNER JOIN usuariomovel us ON us.cliente = sdw.codigocliente ");
        sql.append("LEFT JOIN vinculo vi ON vi.cliente = sdw.codigocliente ");
        sql.append("AND vi.tipovinculo IN ('TW')  ");
        sql.append("WHERE vi.codigo is null ");
        sql.append("AND sdw.empresacliente = ? ");
        sql.append("AND sdw.situacao = 'AT' ");

        try (PreparedStatement ps = getCon().prepareStatement(sql.toString())) {
            int i = 0;
            ps.setInt(++i, codEmpresaLogado);
            return returnCodClientes(ps);
        }
    }

    private String returnCodClientes(PreparedStatement ps) throws SQLException {
        try (ResultSet rs = ps.executeQuery()) {
            List<Integer> codCLientes = new ArrayList<>();
            String listCods = "";
            while (rs.next()){
                codCLientes.add(rs.getInt("codCliente"));
            }
            if(codCLientes.size() == 1){
                listCods = ""+codCLientes.get(0);
            }else if (codCLientes.size() > 0){
                for(Integer cod : codCLientes){
                    listCods += cod+",";
                }
            }
            return listCods.substring(0, listCods.length()-1);
        }
    }

    public String consultarJSONPaginado(FiltroCarteiraTO filtroCarteiraTO, DataTableServerSideProperties dataTableServerSideProperties) throws Exception {
        if (filtroCarteiraTO.isSugestao()) {
            return consultarJSONSugestao(filtroCarteiraTO, dataTableServerSideProperties);
        } else {
            return consultarJSONFiltros(filtroCarteiraTO, dataTableServerSideProperties);
        }
    }

    private String montarDadosJSON(ResultSet rs, DataTableServerSideProperties dataTableServerSideProperties, FiltroCarteiraTO filtroCarteiraTO, Integer totalFiltradosLupa) throws SQLException {
        StringBuilder json = new StringBuilder("{ \"aaData\":[");
        StringBuilder data = new StringBuilder();

        int count = 0;

        if(rs.next()){

            do{

                count++;
                data.append("[\"").append("\",");
                data.append("\"").append(rs.getString("codCliente")).append("\",");
                data.append("\"").append(rs.getString("codMatricula")).append("\",");
                data.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeCliente"))).append("\",");

                data.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomeColaborador"))).append("\",");

                SituacaoClienteEnum situacaoClienteEnum = SituacaoClienteEnum.getSituacaoCliente(rs.getString("situacaoCliente"));
                String situacao = (situacaoClienteEnum != null) ? situacaoClienteEnum.getDescricao() : "";

                data.append("\"").append(situacao).append("\",");
                if(rs.getDate("fimContrato") != null) {
                    data.append("\"").append(Calendario.getData(rs.getDate("fimContrato"), "dd/MM/yyyy")).append("\",");
                } else {
                    data.append("\"").append("-").append("\",");
                }
                data.append("\"").append(rs.getString("risco")).append("\",");
                data.append(rs.getString("diasSemAcesso")).append(",");
                data.append("\"").append(Uteis.normalizarStringJSON(rs.getString("periodoAcesso"))).append("\"],");
            }while (rs.next());


            data.deleteCharAt(data.length() - 1);

        }

        json.append(data);

        json.append("]");

        if (dataTableServerSideProperties != null) {

            if (dataTableServerSideProperties.isClausulaLikePresente() && totalFiltradosLupa != null) {
                json.append(",");
                json.append("\"iTotalDisplayRecords\":\"").append(totalFiltradosLupa).append("\",");
                json.append("\"iTotalRecords\":\"").append(count).append("\"");
            } else {
                Integer quantidadeVinculoFiltroSelecionado = descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroCarteiraTO);

                if (!UteisValidacao.emptyNumber(quantidadeVinculoFiltroSelecionado)) {
                    json.append(",");
                    json.append("\"iTotalDisplayRecords\":\"").append(quantidadeVinculoFiltroSelecionado).append("\",");
                    json.append("\"iTotalRecords\":\"").append(tratarNumeroTotalGetLimit(dataTableServerSideProperties, quantidadeVinculoFiltroSelecionado)).append("\"");
                }
            }
        }

        return json.append("}").toString();
    }

    private String montarDadosString(ResultSet rs, DataTableServerSideProperties dataTableServerSideProperties, FiltroCarteiraTO filtroCarteiraTO, Integer totalFiltradosLupa) throws SQLException {
        StringBuilder data = new StringBuilder();

        if(rs.next()){

            do{
                data.append(rs.getString("codCliente")).append(",");

            }while (rs.next());


            data.deleteCharAt(data.length() - 1);

        }


        return data.toString();
    }

    private Integer tratarNumeroTotalGetLimit(DataTableServerSideProperties dataTableServerSideProperties, Integer quantidadeVinculoFiltroSelecionado) {
        return quantidadeVinculoFiltroSelecionado < dataTableServerSideProperties.getLimit() ?
                quantidadeVinculoFiltroSelecionado : dataTableServerSideProperties.getLimit();
    }

    private int contarStatementConsultarJSONFiltrosFiltradoLupa(FiltroCarteiraTO filtroCarteiraTO, DataTableServerSideProperties dataTableServerSideProperties) throws Exception {
        PreparedStatement ps = obterStatementConsultarJSONFiltros(filtroCarteiraTO, dataTableServerSideProperties, true);
        ResultSet rs = ps.executeQuery();

        if (rs.next()) {
            return rs.getInt("totalFiltradosLupa");
        }

        return 0;
    }

    private PreparedStatement obterStatementConsultarJSONFiltros(FiltroCarteiraTO filtroCarteiraTO, DataTableServerSideProperties dataTableServerSideProperties) throws Exception {
        return obterStatementConsultarJSONFiltros(filtroCarteiraTO, dataTableServerSideProperties, false);
    }

    /**
     * Irá consultar os clientes que tem vínculo do tipo tal com o colaborador;
     */
    private PreparedStatement obterStatementConsultarJSONFiltros(FiltroCarteiraTO filtroCarteiraTO, DataTableServerSideProperties dataTableServerSideProperties, boolean isContarIgnorandoPaginacao) throws Exception {
        List<FaixaHorarioAcessoClienteVO> periodos = obterPeriodosDeAcesso();

        StringBuilder sqlConsulta = new StringBuilder();

        if (isContarIgnorandoPaginacao) {
            sqlConsulta.append("SELECT COUNT(*) AS totalFiltradosLupa FROM ( SELECT DISTINCT sdw.codigocliente ");
        } else {
            sqlConsulta.append("SELECT DISTINCT\n" +
                    "  sdw.codigocliente            AS codCliente,\n" +
                    "  sdw.matricula         AS codMatricula,\n" +
                    "  sdw.nomecliente              AS nomeCliente,\n" +
                    "  pes.nome              AS nomeColaborador,\n" +
                    "  sdw.situacao          AS situacaoCliente,\n" +
                    "  sdw.datavigenciaateajustada        AS fimContrato,\n" +
                    "  sdw.pesorisco     AS risco,\n");
            sqlConsulta.append("  ( select '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' :: DATE -  max(ac.dthrentrada :: DATE) from acessocliente ac where sdw.codigocliente = ac.cliente) AS diasSemAcesso\n");
            montarSQLMaisAcessados(periodos, sqlConsulta);
        }

        sqlConsulta.append("FROM situacaoclientesinteticodw sdw\n" +
                "  INNER JOIN vinculo vi\n" +
                "    ON sdw.codigocliente = vi.cliente\n" +
                "  INNER JOIN colaborador col\n" +
                "    ON vi.colaborador = col.codigo\n" +
                "  INNER JOIN pessoa pes\n" +
                "    ON col.pessoa = pes.codigo\n" +
                (filtroCarteiraTO.getCodigoColaborador() > 0 ? "      AND colaborador = " + filtroCarteiraTO.getCodigoColaborador() + "\n" : "") +
                (!UteisValidacao.emptyString(filtroCarteiraTO.getTipoVinculo()) ? "      AND tipovinculo = '" + filtroCarteiraTO.getTipoVinculo() + "'\n" : "") +
                "  LEFT JOIN contrato cont ON sdw.codigopessoa = cont.pessoa "+
                "  LEFT JOIN Cliente cli ON cli.codigo = vi.cliente \n" );
        if(UteisValidacao.emptyString(filtroCarteiraTO.getFiltroJoinTabela())){
            sqlConsulta.append(filtroCarteiraTO.getFiltroJoinTabela());
        }
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getFiltroAvancado())) {
            sqlConsulta.append("    LEFT JOIN plano pl");
            sqlConsulta.append("        ON pl.codigo = cont.plano \n");
        }
        if (!filtroCarteiraTO.isTodosClientes()) {
            sqlConsulta.append("INNER JOIN pessoa pes\n" +
                    "    ON sdw.codigopessoa = pes.codigo\n");
        }
        sqlConsulta.append("WHERE 1 = 1\n" +
                "      AND sdw.empresaCliente = ?\n");
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getFiltroAvancado())) {
            sqlConsulta.append("          AND ").append(filtroCarteiraTO.getFiltroAvancado()).append("\n");
        }
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getSituacaoCliente())) {
            adicionandoSituacaoClienteWhere(filtroCarteiraTO, sqlConsulta);
        }
        montarFiltroSQLMaisAcessados(sqlConsulta, periodos, "sdw.codigocliente", filtroCarteiraTO);
        if (!filtroCarteiraTO.isTodosClientes()) {
            if (filtroCarteiraTO.getInicioCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro >= ?\n");
            }
            if (filtroCarteiraTO.getFimCadastro() != null) {
                sqlConsulta.append("      AND pes.datacadastro <= ?\n");
            }
        }

        if (dataTableServerSideProperties != null) {
            String valueDeepSearch = dataTableServerSideProperties.getClausulaLike();

            if (dataTableServerSideProperties.isClausulaLikePresente()) {
                sqlConsulta.append(" AND (\n");
                if (isClausulaLikeNumber(dataTableServerSideProperties)) {
                    sqlConsulta.append("sdw.matricula::VARCHAR ~ '").append(valueDeepSearch).append("'\n");
                } else {
                    sqlConsulta.append("lower(sdw.nomecliente) ~ '").append(valueDeepSearch).append("'\n");
                }

                sqlConsulta.append(") ");
            }

            if (dataTableServerSideProperties.isParametrosOrdenacaoPresentes() && !isContarIgnorandoPaginacao) {
                sqlConsulta.append(" ORDER BY ").append(dataTableServerSideProperties.getColOrdenar()).append(" ")
                        .append(dataTableServerSideProperties.getDirOrdenar()).append("\n");
            }

            if (dataTableServerSideProperties.isParametrosPaginacaoPresentes()
                    && !dataTableServerSideProperties.isSemLimitAndOffSet() && !isContarIgnorandoPaginacao) {
                sqlConsulta.append(" LIMIT ").append(dataTableServerSideProperties.getLimit()).append("\n");
                sqlConsulta.append(" OFFSET ").append(dataTableServerSideProperties.getOffset()).append("\n");
            }
        }

        if (isContarIgnorandoPaginacao) {
            sqlConsulta.append(" ) as t1 ");
        }

        PreparedStatement ps = getCon().prepareStatement(sqlConsulta.toString());
        int i = 0;
        ps.setInt(++i, filtroCarteiraTO.getEmpresaVO().getCodigo());
        if (!UteisValidacao.emptyString(filtroCarteiraTO.getSituacaoCliente())) {
            i = adicionandoSituacaoClienteStatement(filtroCarteiraTO, ps, i);
        }
        if (!filtroCarteiraTO.isTodosClientes()) {
            if (filtroCarteiraTO.getInicioCadastro() != null) {
                ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteiraTO.getInicioCadastro(), "00:00:00"));
            }
            if (filtroCarteiraTO.getFimCadastro() != null) {
                ps.setTimestamp(++i, Uteis.getDataHoraJDBC(filtroCarteiraTO.getFimCadastro(), "23:59:59"));
            }
        }

        return ps;
    }

    public String consultarJSONFiltros(FiltroCarteiraTO filtroCarteiraTO, DataTableServerSideProperties dataTableServerSideProperties) throws Exception {
        PreparedStatement ps = obterStatementConsultarJSONFiltros(filtroCarteiraTO, dataTableServerSideProperties);

        Integer totalFiltradosLupa = null;
        if (dataTableServerSideProperties != null && dataTableServerSideProperties.isClausulaLikePresente()) {
            totalFiltradosLupa = contarStatementConsultarJSONFiltrosFiltradoLupa(filtroCarteiraTO, dataTableServerSideProperties);
        }

        ResultSet rs = ps.executeQuery();
        return montarDadosJSON(rs, dataTableServerSideProperties, filtroCarteiraTO, totalFiltradosLupa);
    }


    public List consultarParaImpressao(FiltroCarteiraTO filtroCarteiraTO, String clientesSelecionados, String ordem, String campoOrdenacao, int i) throws Exception {
        PreparedStatement ps = null;
        if (filtroCarteiraTO.isSugestao()) {
            ps = consultarSugestao(filtroCarteiraTO, null);
        } else {
            ps = obterStatementConsultarJSONFiltros(filtroCarteiraTO, null);
        }
        ResultSet rs = ps.executeQuery();
        List lista = new ArrayList();
        String[] split;
        if (!clientesSelecionados.isEmpty()){
            split = clientesSelecionados.split(",");
        }else{
            split = new String[0];
        }

        while (rs.next()) {
            if (clientesSelecionados.isEmpty() || Arrays.asList(split).contains(rs.getString("codCliente"))){
                AmostraClienteTO amostra = new AmostraClienteTO();
                amostra.setMatricula(rs.getString("codMatricula"));
                amostra.setNome(rs.getString("nomeCliente"));
                amostra.setNomeColaborador(rs.getString("nomeColaborador"));
                amostra.setSituacao(rs.getString("situacaoCliente"));
                amostra.setDataCadastro(rs.getDate("fimContrato"));
                amostra.setRisco(rs.getString("risco"));
                amostra.setDiasFalta(rs.getString("diasSemAcesso"));
                amostra.setCategoria(rs.getString("periodoAcesso"));
                lista.add(amostra);
            }
        }

        if (campoOrdenacao.equals("Cliente")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Matrícula")) {
            Ordenacao.ordenarLista(lista, "matricula");
        } else if (campoOrdenacao.equals("Colaborador")) {
            Ordenacao.ordenarLista(lista, "colaborador");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao");
        } else if (campoOrdenacao.equals("Fim Cont.")) {
            Ordenacao.ordenarLista(lista, "dataCadastro");
        } else if (campoOrdenacao.equals("Risco")) {
            Ordenacao.ordenarLista(lista, "risco");
        } else if (campoOrdenacao.equals("Sem acesso")) {
            Ordenacao.ordenarLista(lista, "diasFalta");
        } else if (campoOrdenacao.equals("Período")) {
            Ordenacao.ordenarLista(lista, "categoria");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    private List<FaixaHorarioAcessoClienteVO> obterPeriodosDeAcesso() throws Exception {
        FaixaHorarioAcessoCliente faixas = new FaixaHorarioAcessoCliente(getCon());
        return faixas.consultarFaixas();
    }

    private void montarSQLMaisAcessados(List<FaixaHorarioAcessoClienteVO> periodos, StringBuilder sqlConsulta) throws Exception {
        if (!periodos.isEmpty()) {
            sqlConsulta.append(" , ").append(sqlPeriodoMaisAcessados(periodos, "sdw.codigocliente")).append("AS periodoAcesso\n");
        } else {
            sqlConsulta.append(", '' as periodoAcesso\n");
        }
    }

    private List<FiltroCarteiraTO> consultarSugestoesClientesTreinoSemProfessor(Integer codEmpresa) throws Exception {

        StringBuilder sqlConsulta = new StringBuilder(" SELECT NULL        AS codColaborador, \n");
        sqlConsulta.append("'ALUNOS_SEM_PROFESSOR_TREINO' AS tipovinculo, \n");
        sqlConsulta.append(" '' AS situacao, \n");
        sqlConsulta.append(" count(*) AS qtd, \n");
        sqlConsulta.append(" sdw.empresacliente AS codEmpresa \n");
        sqlConsulta.append(" FROM situacaoclientesinteticodw sdw \n");
        //sqlConsulta.append(" INNER JOIN usuariomovel us ON us.cliente = sdw.codigocliente \n");
        sqlConsulta.append(" LEFT JOIN vinculo vi ON vi.cliente = sdw.codigocliente AND vi.tipovinculo IN ('TW') \n");
        sqlConsulta.append(" WHERE vi.codigo is null AND sdw.empresacliente = ").append(codEmpresa);
        sqlConsulta.append(" AND sdw.situacao = 'AT' \n");
        sqlConsulta.append(" AND sdw.codigocliente in (select u.cliente from usuariomovel u where u.cliente = sdw.codigocliente) \n");
        sqlConsulta.append("GROUP BY vi.tipovinculo, sdw.empresacliente ORDER BY 4, 3");

        PreparedStatement ps = getCon().prepareStatement(sqlConsulta.toString());
        ResultSet resultSet = ps.executeQuery();
        return montarDadosSugestoes(resultSet, false, false, "Clientes sem professor");
    }

    private boolean isClausulaLikeNumber(DataTableServerSideProperties dataTableServerSideProperties) {
        try {
            Integer.valueOf(dataTableServerSideProperties.getClausulaLike());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

}
