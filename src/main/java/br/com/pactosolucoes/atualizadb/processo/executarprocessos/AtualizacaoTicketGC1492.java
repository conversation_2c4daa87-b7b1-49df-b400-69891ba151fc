package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "13/03/2025",
        descricao = "gravar documentos da compra",
        motivacao = "GC-1492")
public class AtualizacaoTicketGC1492 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE parcelasCompraContaFinanceiro ( \n" +
                    " codigo serial NOT null, \n" +
                    " compra INTEGER NOT null, \n" +
                    " nrParcela INTEGER not null, \n" +
                    " valorParcela float not null, \n" +
                    " dataVencimento timestamp NOT null, \n" +
                    " CONSTRAINT parcelasCompraContaFinanceiro_pkey PRIMARY KEY (codigo), \n" +
                    " CONSTRAINT fk_parcelasCompraContaFinanceiro_compra FOREIGN KEY (compra) REFERENCES compra (codigo) MATCH SIMPLE ON \n" +
                    " UPDATE RESTRICT ON DELETE RESTRICT);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE documentoCompra ( \n" +
                    " codigo serial NOT null, \n" +
                    " compra INTEGER NOT null, \n" +
                    " chaveArquivo text not null, \n" +
                    " identificadorArquivo text not null, \n" +
                    " extensaoArquivo CHARACTER VARYING(20) NOT null, \n" +
                    " CONSTRAINT documentoCompra_pkey PRIMARY KEY (codigo), \n" +
                    " CONSTRAINT fk_documentoCompra_compra FOREIGN KEY (compra) REFERENCES compra (codigo) MATCH SIMPLE ON \n" +
                    " UPDATE RESTRICT ON DELETE RESTRICT);", c);
        }
    }
}
