package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.atualizadb.processo.annotations.Processo;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.vendasonline.CamposAdicionaisVendasOnlineEnum;
import importador.outros.ProcessosReguaDeCobranca;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.financeiro.enumerador.StatusPactoPayComunicacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.OpcaoPerfilAcesso;
import negocio.comuns.utilitarias.OpcoesPerfilAcesso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.acesso.IntegracaoMember;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.PinPadPedido;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR> Maciel Objetivo da classe: Agrupar todos os processos que
 *         deverão ser executados no banco de dados. Exemplo para criar um novo
 *         processo: 1º Criar um novo método nesta classe no padrão "migracaoVersaoXXX",
 *         onde "XXX é a versão atual deo banco de dados. "Vide SuperControle.versaoBD".
 *         2º Adicionar a anotação
 * @Processo no método que está sendo criado e preencher os atributos
 * necessários. O seu método será executado transparentemente pelo
 * "AtualizadorBD"
 * @see br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD
 */
@SuppressWarnings("unused")
public class ExecutarProcessos extends FacadeManager {

    private Connection con;

    public ExecutarProcessos() {
        //Pegar a conexão da sessão indiretamente.
        try {
            this.con = getFacade().getRisco().getCon();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ExecutarProcessos(Connection c) {
        //Pegar a conexão da sessão indiretamente.
        try {
            this.con = c;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public void onPostAllProcess() throws Exception {
        criarIndicesFK();
    }

    public static void main(String[] args) {
        List<String> keys = new ArrayList();
        try {
            keys.add(args[0]);
            if ("todas".equals(args[0])) {
                keys = new DAO().buscarListaChaves();
            }
            for (String k : keys) {
                Connection c = new DAO().obterConexaoEspecifica(k);
                try {
                    Conexao.guardarConexaoForJ2SE(k, c);
                    ExecutarProcessos executor = new ExecutarProcessos(c);
                    final String[] versoes = args[1].split(",");
                    for (String versao : versoes) {
                        UtilReflection.invoke(executor, String.format("migracaoVersao%s", versao));
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                } finally {
                    c.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void criarIndicesFK() throws Exception {
        Connection c = null;
        try {
            c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            String sql = "SELECT\n"
                    + "  pg_index.indexrelid :: REGCLASS,\n"
                    + "  ('create index ' || relname || '_' ||\n"
                    + "   array_to_string(column_name_list, '_') || '_idx on ' || conrelid ||\n"
                    + "   ' (' || array_to_string(column_name_list, ',') || ')') AS sql\n"
                    + "FROM (SELECT DISTINCT\n"
                    + "        conrelid,\n"
                    + "        array_agg(attname)   column_name_list,\n"
                    + "        array_agg(attnum) AS column_list\n"
                    + "      FROM pg_attribute\n"
                    + "        JOIN (SELECT\n"
                    + "                conrelid :: REGCLASS,\n"
                    + "                conname,\n"
                    + "                unnest(conkey) AS column_index\n"
                    + "              FROM (SELECT DISTINCT\n"
                    + "                      conrelid,\n"
                    + "                      conname,\n"
                    + "                      conkey\n"
                    + "                    FROM pg_constraint\n"
                    + "                      JOIN pg_class ON pg_class.oid = pg_constraint.conrelid\n"
                    + "                      JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace\n"
                    + "                    WHERE nspname !~ '^pg_' AND nspname <> 'information_schema'\n"
                    + "                   ) fkey\n"
                    + "             ) fkey\n"
                    + "          ON fkey.conrelid = pg_attribute.attrelid\n"
                    + "             AND fkey.column_index = pg_attribute.attnum\n"
                    + "      GROUP BY conrelid, conname\n"
                    + "     ) candidate_index\n"
                    + "  JOIN pg_class ON pg_class.oid = candidate_index.conrelid\n"
                    + "  LEFT JOIN pg_index ON pg_index.indrelid = conrelid\n"
                    + "                        AND indkey :: TEXT = array_to_string(column_list, ' ')\n"
                    + "WHERE indexrelid IS NULL;";

            Statement st = con.createStatement();
            ResultSet resultDados = st.executeQuery(sql);
            List<String> sqlCriarIndex = new ArrayList<String>();
            while (resultDados.next()) {
                sqlCriarIndex.add(resultDados.getString("sql"));
            }

            sqlCriarIndex.add("CREATE INDEX situacaoclientesinteticodw_codigocliente_idx ON situacaoclientesinteticodw USING btree (codigocliente);");
            sqlCriarIndex.add("CREATE INDEX situacaoclientesinteticodw_codigocontrato_idx ON situacaoclientesinteticodw USING btree (codigocontrato);");
            sqlCriarIndex.add("CREATE INDEX situacaoclientesinteticodw_codigopessoa_idx ON situacaoclientesinteticodw USING btree (codigopessoa);");
            sqlCriarIndex.add("CREATE INDEX situacaoclientesinteticodw_codigoultimocontatocrm_idx ON situacaoclientesinteticodw USING btree (codigoultimocontatocrm);");

            for (String sqlIndice : sqlCriarIndex) {
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlIndice, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    private boolean tabelaExiste(final Connection c, final String nomeTabela) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT 1 \n").
                append("FROM information_schema.tables \n").
                append("WHERE table_schema = 'public' AND table_name = '").append(nomeTabela).append("'");
        return SuperFacadeJDBC.existe(sql.toString(), c);
    }

    private boolean colunaExiste(final Connection c, final String nomeTabela, final String nomeColuna) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT 1 \n").
                append("FROM information_schema.columns \n").
                append("WHERE table_name = '").append(nomeTabela).append("' and column_name = '").append(nomeColuna).append("'");
        return SuperFacadeJDBC.existe(sql.toString(), c);
    }

    private void renomearColuna(final Connection c, final String nomeTabela,
                                final String nomeAntigo, final String nomeNovo) throws Exception {
        if (colunaExiste(c, nomeTabela, nomeAntigo)) {
            final String sql = String.format("ALTER TABLE %s RENAME %s TO %s",
                    nomeTabela, nomeAntigo, nomeNovo);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }

    private void droparTabela(final Connection c, final String... nomeTabelas) throws Exception {
        for (String nomeTabela : nomeTabelas) {
            if (tabelaExiste(c, nomeTabela)) {
                final String sql = String.format("DROP TABLE %s",
                        nomeTabela);
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }

    private void droparColuna(final Connection c, final String nomeTabela,
                              final String... nomeColunas) throws Exception {
        for (String nomeColuna : nomeColunas) {
            if (colunaExiste(c, nomeTabela, nomeColuna)) {
                final String sql = String.format("ALTER TABLE %s DROP COLUMN %s",
                        nomeTabela, nomeColuna);
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }

    private void alerarTipoColuna(final Connection c, final String nomeTabela,
                                  final String nomeColuna, final String novoTipo) throws Exception {
        if (colunaExiste(c, nomeTabela, nomeColuna)) {
            final String sql = String.format("ALTER TABLE %s ALTER COLUMN %s TYPE %s",
                    nomeTabela, nomeColuna, novoTipo);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "05/04/2023",
            descricao = "Criação da coluna para permitir um usuaário comum exluir o cliente em configurações",
            motivacao = "Permitir o usuário pactobr conceder direito para um usuario comum excluir um cliente")
    public void migracaoVersao1930() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE usuario ADD COLUMN permiteExclusaoCliente BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "05/04/2023",
            descricao = "Criação da coluna idlead  para bigint",
            motivacao = "Permitir o cadastro de lead  de precisão de até 64 bits ")
    public void migracaoVersao1931() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE lead ALTER COLUMN idlead type bigint;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "12/04/2023",
            descricao = "E2-196",
            motivacao = "E2-196")
    public void migracaoVersao1932() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN identificadorOrigem character varying (100);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN criarContaPagarAutomatico BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN cancelamentoApresentarTransacoes BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN isentarCancelamento7Dias BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "13/04/2023",
            descricao = "Processo para corrigir metas financeiras com consultores de empresa diferente da meta",
            motivacao = "SD1-7550")
    public void migracaoVersao1933() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarMetasFinanceiraConsultor.corrigirMetasFinanceiraConsultor(c);
        }
    }


    @Processo(autor = "Lucas Araujo",
            data = "18/04/2023",
            descricao = "Criação das colunas limitedeacessospordiagympass e limitedeaulaspordiagympass",
            motivacao = "Possibilitar a configuração de limites de acessos ou aulas por dia para alunos gympass")
    public void migracaoVersao1934() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN limitedeacessospordiagympass INT4;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN limitedeaulaspordiagympass INT4;", c);
        }
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "20/04/2023",
            descricao = "Configuração para que o cancelamento automático de contrato processe corretamente no cenário da engenharia, que são contratos não renováveis",
            motivacao = "E1-137")
    public void migracaoVersao1935() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN cancelarContratosNaoRenovaveisForaRecorrencia BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "28/04/2023",
            descricao = "Realizar a padronização de Ip nas assinaturas que foram feitas de forma eletrônica",
            motivacao = "SD1-7662")
    public void migracaoVersao1936() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE public.contrato set ipassinaturacontrato = 'SEM_IP' where dataassinaturacontrato is not null and length(ipassinaturacontrato) = 0;", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "19/04/2023",
            descricao = "Povoando nova permissao exclusão do cliente ",
            motivacao = "Melhoria SD1-7698")
    public void migracaoVersao1937() throws Exception {
        Connection c = null;
        try {
            c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo = " + PerfilUsuarioEnum.ADMINISTRADOR.getId(), c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (1, '9.69 - Permitir excluir cliente da base de dados','(0)(1)(2)(3)(9)(12)', "
                        + " 'ExcluirCliente', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "25/04/2023",
            descricao = "SD1-7756 - Limitando vigência de produtos de trancamento para 2 anos",
            motivacao = "SD1-7756 - Limitando vigência de produtos de trancamento para 2 anos")
    public void migracaoVersao1938() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE produto SET nrdiasvigencia = 730 WHERE nrdiasvigencia > 730 AND tipoproduto = 'TR'", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "28/04/2023",
            descricao = "Campo novo restrição de marcação de aula pelo nr vezes modalidade",
            motivacao = "SD1-7766")
    public void migracaoVersao1939() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN restringirMarcacaoAulaPorNrVezesModalidade boolean DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "14/03/2023",
            descricao = "Atributos para recurso de replicar mailing para rede de empresas",
            motivacao = "Engenharia do corpo")
    public void migracaoVersao1940() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table maladireta " +
                    "add replicarredeempresa boolean", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table public.maladiretaredeempresa (codigo serial not null constraint maladiretaredeempresa_pk primary key,\n" +
                    "maladireta integer constraint maladiretaredeempresa_maladireta_codigo_fk references public.maladireta,\n" +
                    "chaveOrigem varchar,\n" +
                    "chaveDestino varchar,\n" +
                    "empresaDestino integer,\n" +
                    "nomeunidade text,\n" +
                    "datacadastro timestamp,\n" +
                    "mensagemSituacao text,\n" +
                    "maladiretareplicado integer,\n" +
                    "dataatualizacao timestamp);", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "12/05/2023",
            descricao = "Realizar a padronização de Ip nas assinaturas que foram feitas de forma eletrônica(Reaberto)",
            motivacao = "SD1-7662")
    public void migracaoVersao1941() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE contrato set ipassinaturacontrato = 'SEM_IP' where dataassinaturacontrato is not null and coalesce(length(ipassinaturacontrato), 0) = 0;", c);
        }
    }

    @Processo(autor = "Glauco T. Camargo",
            data = "15/05/2023",
            descricao = "Adding new informations of SPC",
            motivacao = "EDC-168")
    public void migracaoVersao1942() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN codigoAssociadoSPC numeric;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "18/05/2023",
            descricao = "Exclusão de emails vazios",
            motivacao = "SD2-4577")
    public void migracaoVersao1943() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("delete from email where email = ''", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "17/02/2023",
            descricao = "Atributo para guardar foto adicionada na turma",
            motivacao = "Engenharia do corpo")
    public void migracaoVersao1944() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table turma add fotoKey varchar", c);
            String sql = "select codigo from modalidade where fotokey != '' and fotokey is not null";
            Statement st = c.createStatement();
            ResultSet rs = st.executeQuery(sql);
            List<Integer> modalidadesFotoKey = new ArrayList<>();
            while (rs.next()) {
                modalidadesFotoKey.add(rs.getInt("codigo"));
            }
            modalidadesFotoKey.forEach(codigoModalidade -> {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("update turma set fotoKey = (select fotokey from modalidade where codigo = " +
                        codigoModalidade + ") where modalidade = " + codigoModalidade, c);
            });

        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "18/05/2023",
            descricao = "E2-196",
            motivacao = "E2-196")
    public void migracaoVersao1945() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN contacriarcontapagarautomatico INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD CONSTRAINT fk_configuracaofinanceiro_contacriarcontapagarautomatico " +
                    "FOREIGN KEY (contacriarcontapagarautomatico) REFERENCES conta (codigo);", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "18/05/2023",
            descricao = "Excluindo colunas com nome errado criadas pelo cad-aux-ms",
            motivacao = "SD1-7964")
    public void migracaoVersao1946() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema DROP COLUMN IF EXISTS seqpprocessoimportacao", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema DROP COLUMN IF EXISTS textotermoresponsabilidade", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema DROP COLUMN IF EXISTS termowaiver", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema DROP COLUMN IF EXISTS mascarattelefone", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema DROP COLUMN IF EXISTS utilizarservicoesisc", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "24/05/2023",
            descricao = "Configurações para limitar o desconto por perfil de acesso",
            motivacao = "SD1-7900")
    public void migracaoVersao1947() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN limitarDescontosPorPerfil boolean DEFAULT FALSE NOT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table perfilacesso add column porcetagemDescontoContrato float4 DEFAULT 0.0;\n", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "25/05/2023",
            descricao = "Processo para corrigir contratos html que estavam sem charset, gerando caracteres inválidos durante a impressão de contrato",
            motivacao = "SD1-8021")
    public void migracaoVersao1948() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarContratosHTML.corrigirContratosHTML(c);
        }
    }

    @Processo(autor = "Hebert Frederick",
            data = "11/05/2023",
            descricao = "E3-30",
            motivacao = "E3-30")
    public void migracaoVersao1949() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE Turma ADD COLUMN visualizarProdutosGympass BOOLEAN DEFAULT TRUE;", c);
        }
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "28/02/2023",
            descricao = "EDC-232",
            motivacao = "Registrar todas as tentativas de acesso")
    public void migracaoVersao1950() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN registrarTentativasAcesso BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "31/05/2023",
            descricao = "E1-209",
            motivacao = "E1-209")
    public void migracaoVersao1951() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD permitirMudarTipoParcelamento BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "26/05/2023",
            descricao = "Cria configuração no colaborador para indicar que ele poderá acessar as outras empresas.",
            motivacao = "E1-148")
    public void migracaoVersao1952() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE colaborador ADD COLUMN permitirAcessoRedeEmpresa boolean DEFAULT FALSE NOT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE colaborador ADD COLUMN sincronizadoRedeEmpresa TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "06/06/2023",
            descricao = "Remover configuração aplicarsomentevalor do indice financeiro de reajuste.",
            motivacao = "SD1-8061")
    public void migracaoVersao1953() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE indicefinanceiroreajustepreco DROP COLUMN IF EXISTS aplicarsomentesevalormenor;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "12/06/2023",
            descricao = "Adicionar forma de verificar um produto que foi duplicado por conta de cancelamento parcial",
            motivacao = "SD1-8039")
    public void migracaoVersao1954() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table movproduto add column movProdutoBase integer;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "12/06/2023",
            descricao = "Adicionar forma de verificar um produto que foi duplicado por conta de cancelamento parcial",
            motivacao = "SD1-8039")
    public void migracaoVersao1955() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE OR REPLACE FUNCTION fn_tg_after_alterarestoque()\n" +
                    "  RETURNS trigger AS\n" +
                    "$BODY$\n" +
                    "declare\n" +
                    "    reg_prod record;\n" +
                    "    reg_itens record;\n" +
                    "    diferenca integer;\n" +
                    "    codAux integer;\n" +
                    "    codEmpresa integer;\n" +
                    "    dataAd Date;\n" +
                    "    estoqueAnterior integer;\n" +
                    "BEGIN\n" +
                    "    IF (upper(TG_RELNAME) = 'COMPRAITENS') THEN\n" +
                    "        IF upper(TG_OP) = 'INSERT' THEN\n" +
                    "            select into codEmpresa empresa from compra where codigo = NEW.compra;\n" +
                    "            select into estoqueAnterior coalesce(estoque, 0) FROM produtoestoque p where produto = new.produto and empresa = codEmpresa and situacao = 'A';\n" +
                    "            update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + new.quantidade where produto = new.produto and empresa = codEmpresa and situacao = 'A';\n" +
                    "            update produtoEstoque set ape = null where produto = new.produto and empresa = codEmpresa;\n" +
                    "            INSERT INTO historicoprodutoestoque (produtoestoque, saldoanterior, saldoatual, entidade, dataalteracao) (SELECT codigo, estoqueAnterior, estoque, 'COMPRAITENS', NOW() FROM produtoestoque p where produto = new.produto and empresa = codEmpresa and situacao = 'A');\n" +
                    "        END IF;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF (upper(TG_RELNAME) = 'COMPRA') THEN\n" +
                    "        IF upper(TG_OP) = 'UPDATE' THEN\n" +
                    "            -- remover do estoque os produtos da compra cancelada.\n" +
                    "            IF (new.cancelada <> old.cancelada) and (new.cancelada = true) THEN\n" +
                    "                FOR reg_itens in select * from compraItens where compra = old.codigo  LOOP\n" +
                    "\t\t\tselect into estoqueAnterior coalesce(estoque, 0) FROM produtoestoque p where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A';\n" +
                    "                        update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) - reg_itens.quantidade where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A' ;\n" +
                    "                        update produtoEstoque set ape = null where produto = reg_itens.produto and empresa = new.empresa;\n" +
                    "                        INSERT INTO historicoprodutoestoque (produtoestoque, saldoanterior, saldoatual, entidade, dataalteracao) (SELECT codigo, estoqueAnterior, estoque, 'COMPRA', NOW() FROM produtoestoque p where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A');\n" +
                    "                    END LOOP;\n" +
                    "            END IF;\n" +
                    "        END IF;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF (upper(TG_RELNAME) = 'BALANCOITENS') THEN\n" +
                    "        select into codEmpresa empresa from balanco where codigo = NEW.balanco;\n" +
                    "        IF upper(TG_OP) = 'INSERT' THEN\n" +
                    "\t    select into estoqueAnterior coalesce(estoque, 0) FROM produtoestoque p where produto = new.produto and empresa = codEmpresa and situacao = 'A';\n" +
                    "            update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + (new.qtdeBalanco - coalesce(new.qtdeEstoqueAnterior,0)) where produto = new.produto and empresa = codEmpresa and situacao = 'A';\n" +
                    "            update produtoEstoque set ape = null where produto = new.produto and empresa = codEmpresa;\n" +
                    "            INSERT INTO historicoprodutoestoque (produtoestoque, saldoanterior, saldoatual, entidade, dataalteracao) (SELECT codigo, estoqueAnterior, estoque, 'BALANCOITENS', NOW() FROM produtoestoque p where produto = new.produto and empresa = codEmpresa and situacao = 'A');\n" +
                    "        END IF;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF (upper(TG_RELNAME) = 'BALANCO') THEN\n" +
                    "        IF upper(TG_OP) = 'UPDATE' THEN\n" +
                    "            -- Desfazer as alterações de estoque para o balanço cancelado.\n" +
                    "            IF (new.cancelado <> old.cancelado) and (new.cancelado = true) THEN\n" +
                    "                FOR reg_itens in select * from balancoItens where balanco = old.codigo  LOOP\n" +
                    "\t\t\tselect into estoqueAnterior coalesce(estoque, 0) FROM produtoestoque p where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A';\n" +
                    "                        update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + ((reg_itens.qtdeBalanco - coalesce(reg_itens.qtdeEstoqueAnterior,0)) * -1)  where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A';\n" +
                    "                        update produtoEstoque set ape = null  where produto = reg_itens.produto and empresa = new.empresa;\n" +
                    "                        INSERT INTO historicoprodutoestoque (produtoestoque, saldoanterior, saldoatual, entidade, dataalteracao) (SELECT codigo, estoqueAnterior, estoque, 'BALANCO', NOW() FROM produtoestoque p where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A');\n" +
                    "                    END LOOP;\n" +
                    "            END IF;\n" +
                    "        END IF;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF (upper(TG_RELNAME) = 'MOVPRODUTO') THEN\n" +
                    "        IF upper(TG_OP) = 'INSERT' THEN\n" +
                    "            select into codAux codigo from produtoEstoque where produto = NEW.produto;\n" +
                    "            IF (codAux is not null AND NEW.situacao <> 'CA') then -- verifica se o produto tem controle de estoque\n" +
                    "\t\tselect into estoqueAnterior coalesce(estoque, 0) FROM produtoestoque p where produto = new.produto and empresa = new.empresa and situacao = 'A';\n" +
                    "                update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) - new.quantidade where produto = new.produto and empresa = new.empresa and situacao = 'A';\n" +
                    "                update produtoEstoque set ape = null where produto = new.produto and empresa = new.empresa;\n" +
                    "                INSERT INTO historicoprodutoestoque (produtoestoque, saldoanterior, saldoatual, entidade, dataalteracao) (SELECT codigo, estoqueAnterior, estoque, 'MOVPRODUTO', NOW() FROM produtoestoque p where produto = new.produto and empresa = new.empresa and situacao = 'A');\n" +
                    "            END IF;\n" +
                    "        END IF;\n" +
                    "\n" +
                    "        IF upper(TG_OP) = 'DELETE' THEN\n" +
                    "            select into codAux codigo from produtoEstoque where produto = old.produto and empresa = old.empresa;\n" +
                    "            IF (codAux is not null AND old.movprodutobase is null) then -- verifica se o produto tem controle de estoque\n" +
                    "\t\tselect into estoqueAnterior coalesce(estoque, 0) FROM produtoestoque p where produto = old.produto and empresa = old.empresa and situacao = 'A';\n" +
                    "                update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + old.quantidade where produto = old.produto and empresa = old.empresa and situacao = 'A';\n" +
                    "                update produtoEstoque set ape = null where produto = old.produto and empresa = old.empresa;\n" +
                    "                INSERT INTO historicoprodutoestoque (produtoestoque, saldoanterior, saldoatual, entidade, dataalteracao) (SELECT codigo, estoqueAnterior, estoque, 'DELETE MOVPRODUTO', NOW() FROM produtoestoque p where produto = old.produto and empresa = old.empresa and situacao = 'A');\n" +
                    "                -- pesquisar a data em que o produto foi adicionado ao controle de estoque.\n" +
                    "                select into dataAd cast (min(sit.dataCadastro) as date)\n" +
                    "                from produtoEstoque pe\n" +
                    "                         inner join ProdutoEstoque_alteracaoSit sit on sit.produtoEstoque = pe.codigo\n" +
                    "                where pe.empresa = old.empresa and pe.produto = old.produto and sit.situacao = 'A';\n" +
                    "                -- Gravar o cancelamento da venda para ser visualizado no Cardex, somente das vendas que foram realizadas após a data em que o produto foi adicionado ao controle de estoque.\n" +
                    "                IF (old.dataLancamento >= dataAd) THEN\n" +
                    "                    -- Registrar o cancelamento, somente para as vendas que tiveram balanço realizado após a data da venda.\n" +
                    "                    select into codAux b.codigo\n" +
                    "                    from balanco b\n" +
                    "                             inner join balancoItens bi on bi.balanco = b.codigo\n" +
                    "                    where b.dataCadastro > old.dataLancamento and b.empresa = old.empresa and bi.produto = old.produto;\n" +
                    "                    IF (codAux is not null) THEN\n" +
                    "                        insert into exclusaoMovProdutoEstoque(produto, empresa, pessoa, quantidade, dataExclusao, datavenda) values(old.produto, old.empresa, old.pessoa, old.quantidade, now(), old.dataLancamento);\n" +
                    "                    END IF;\n" +
                    "                END IF;\n" +
                    "            END IF;\n" +
                    "        END IF;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    RETURN NEW;\n" +
                    "END;\n" +
                    "$BODY$\n" +
                    "  LANGUAGE plpgsql VOLATILE\n" +
                    "  COST 100;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "13/06/2023",
            descricao = "Alterando configurações convênio desconto.",
            motivacao = "SD1-8067")
    public void migracaoVersao1956() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniodesconto DROP COLUMN IF EXISTS pagarematricula;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniodesconto DROP COLUMN IF EXISTS pagamatricula;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniodesconto ADD COLUMN isentarRematricula BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniodesconto ADD COLUMN isentarMatricula BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "23/05/2023",
            descricao = "Gravar valor original da conta",
            motivacao = "E2-269")
    public void migracaoVersao1957() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN valorPago double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN valorOriginalAlterado double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN apresentarValorPago BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movconta SET valorpago = valor, valororiginalalterado = valor WHERE dataquitacao is not null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movconta SET valororiginalalterado = valor WHERE dataquitacao is null;", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "23/05/2023",
            descricao = "Alterar o tamanho dos campos Email e razaoSocial",
            motivacao = "E1-203")
    public void migracaoVersao1958() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ALTER COLUMN razaosocial TYPE CHARACTER VARYING(100);;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ALTER COLUMN email TYPE CHARACTER VARYING(100);", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "25/05/2023",
            descricao = "Adicionando Fase Agendamento de Ligação nas ordenações da meta",
            motivacao = "Adicionando Fase Agendamento de Ligação nas ordenações da meta")
    public void migracaoVersao1959() throws Exception {
        Connection c = null;
        try {
            c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update configuracaosistemacrm set ordenacaometas = ('UG,'||ordenacaometas);", c);
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "12/06/2023",
            descricao = "Adicionar forma de verificar um produto que foi duplicado por conta de cancelamento parcial",
            motivacao = "SD1-8039")
    public void migracaoVersao1960() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table movproduto add column renovavelAutomaticamente  BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto add column renovavelAutomaticamente  BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "23/06/2023",
            descricao = "Aumentar qtd. caracteres de colunas do financeiro",
            motivacao = "SD2-4895")
    public void migracaoVersao1961() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ALTER COLUMN descricao TYPE VARCHAR(200);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE agendamentofinanceiro ALTER COLUMN descricao TYPE VARCHAR(200);", c);
        }
    }


    @Processo(autor = "Rodrigo Estulano",
            data = "05/05/2023",
            descricao = "Integração Boleto Asaas",
            motivacao = "SD2-4512")
    public void migracaoVersao1962() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN idasaas text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN dataAlteracaoIdAsaas timestamp without time zone;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE boleto ADD COLUMN transactionReceiptUrl text", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pix ADD COLUMN transactionReceiptUrl text", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN enviarNotificacoes boolean default false", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN cobrarMultaJurosAsaas boolean default false", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN valorMultaAsaas REAL", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN valorJurosAsaas REAL", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE asaasempresa (codigo serial not null primary key,\n" +
                    "empresa integer,\n" +
                    "id text,\n" +
                    "apiKey text,\n" +
                    "walletId text,\n" +
                    "ambiente integer, \n" +
                    "dataCriacao timestamp without time zone, \n" +
                    "paramsEnvio text, \n" +
                    "paramsResposta text, \n" +
                    "companyType integer)", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE asaasempresahistorico (codigo serial not null primary key,\n" +
                    "empresa integer,\n" +
                    "dataCriacao timestamp without time zone, \n" +
                    "paramsEnvio text, \n" +
                    "paramsResposta text)", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "16/06/2023",
            descricao = "Pesquisa automatica SPC",
            motivacao = "E1-233")
    public void migracaoVersao1963() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN pesquisaautomaticaspc boolean default false;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "21/06/2023",
            descricao = "Incluir tabela ip localizacao consentimento parq vendas online",
            motivacao = "E3-125")
    public void migracaoVersao1964() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table public.nowLocationIpVendaOnline (codigo serial not null constraint nowlocationipvendaonline_pk primary key,\n" +
                    "cliente integer constraint nowLocationIpVendaOnline_cliente_codigo_fk references public.cliente,\n" +
                    "data varchar,\n" +
                    "cidade varchar,\n" +
                    "estado varchar,\n" +
                    "localizacao varchar,\n" +
                    "pais varchar,\n" +
                    "ip varchar);", c);
        }
    }

    @Processo(autor = "Wellinton Santos",
            data = "05/06/2023",
            descricao = "Atributos para a configuração com a hubspot",
            motivacao = "Integração do lead com a hubspot")
    public void migracaoVersao1965() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table maladireta " +
                    "add replicarredeempresa boolean", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.oauth2hubspot(\n" +
                            "\tcodigo serial4 NOT NULL,\n" +
                            "\tcodigoempresa int4 NULL,\n" +
                            "\tcodigoauth text NULL,\n" +
                            "\taccess_token text NULL,\n" +
                            "\trefresh_token text NULL,\n" +
                            "\tatulizado timestamp NULL,\n" +
                            "\tCONSTRAINT oauth2hubspot_pk PRIMARY KEY (codigo));", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.configuracaoempresahubspot (\n" +
                            "\tcodigo serial4 NOT NULL,\n" +
                            "\tempresa int4 NULL,\n" +
                            "\tempresausahub bool NULL,\n" +
                            "\thoraexpiracao varchar(5) NULL,\n" +
                            "\tclientid varchar(200) NULL,\n" +
                            "\tclientsecret text NULL,\n" +
                            "\turl_instalacao text NULL,\n" +
                            "\turl_redirect text NULL,\n" +
                            "\tCONSTRAINT configuracaoempresahubspot_pk PRIMARY KEY (codigo)\n" +
                            ");", c);

        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "06/07/2023",
            descricao = "Exibir valor total do plano no vendas online",
            motivacao = "SD2-4982")
    public void migracaoVersao1966() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN apresentarvalortotaldoplanonateladeselecaodoplano boolean default false;", c);
        }
    }

    @Processo(autor = "Hebert Frederick",
            data = "19/06/2023",
            descricao = "Cria tabela configurações da total pass.",
            motivacao = "E3-15")
    public void migracaoVersao1967() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append(" CREATE TABLE public.configtotalpass (\n")
                    .append(" codigo serial4 NOT NULL,\n")
                    .append(" codigototalpass varchar(255) NULL,\n")
                    .append(" apikey varchar(30) NULL,\n")
                    .append(" empresa_codigo int4 NULL,\n")
                    .append(" nome varchar(255) NULL,\n")
                    .append(" limitedeacessospordia int4 NULL,\n")
                    .append(" limitedeaulaspordia int4 NULL,\n")
                    .append(" inativo bool NULL,\n")
                    .append(" datalancamento timestamp NULL,\n")
                    .append(" usuariolancou_codigo int4 NULL,\n")
                    .append(" permitirwod bool NULL,\n")
                    .append(" CONSTRAINT configtotalpass_pkey PRIMARY KEY (codigo)\n")
                    .append(" );\n")
                    .append(" CREATE INDEX idx_configtotalpass_empresa_codigo ON public.configtotalpass USING btree (empresa_codigo);\n")
                    .append(" ALTER TABLE public.configtotalpass ADD CONSTRAINT fk2dfe8aaa5c7d2781 FOREIGN KEY (empresa_codigo) REFERENCES public.empresa(codigo);\n")
                    .append(" ALTER TABLE public.configtotalpass ADD CONSTRAINT fk2dfe8aaadb2214b6 FOREIGN KEY (usuariolancou_codigo) REFERENCES public.usuario(codigo);");

            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE Turma ADD COLUMN visualizarProdutosTotalpass BOOLEAN DEFAULT TRUE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE periodoacessocliente ADD COLUMN tipototalpass BOOLEAN DEFAULT false;", c);


        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "31/07/2023",
            descricao = "Adicionar coluna cor na tabela turma",
            motivacao = "E3-204")
    public void migracaoVersao1968() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE turma ADD COLUMN cor varchar(10);", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "07/06/2023",
            descricao = "Emails dos responsáveis.",
            motivacao = "E1-77")
    public void migracaoVersao1969() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table pessoa add emailpai text", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table pessoa add emailmae text", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "27/07/2023",
            descricao = "Configurações permitir remover assinatura.",
            motivacao = "E1-268")
    public void migracaoVersao1970() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo = " + PerfilUsuarioEnum.ADMINISTRADOR.getId(), c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (1, '9.70 - Permite remover assinatura digital de contratos','(0)(1)(2)(3)(9)(12)', "
                        + " 'RemoverAssinatura', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "26/07/2023",
            descricao = "Permitir informar tipo de plano de conta filho",
            motivacao = "E2-262")
    public void migracaoVersao1971() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN permitirTipoPlanoContaFilho BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "14/07/2023",
            descricao = "Integração Pluggy",
            motivacao = "SD2-4915")
    public void migracaoVersao1972() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN valorOriginalAntesDaConciliacao float", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE pluggyItem (codigo serial4 NOT NULL primary key, empresa int4, id text," +
                    " dadosRetorno text, ativo boolean default false);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE movContaTransactionPluggy (codigo serial NOT NULL," +
                    " movconta serial, idTransaction text, dataoperacao timestamp);", c);

            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo = " + PerfilUsuarioEnum.ADMINISTRADOR.getId(), c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (3, '9.71 - Conciliação de Contas a Pagar','(0)(1)(2)(3)(9)(12)', "
                        + " 'ConciliacaoContasPagar', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Kévio Castro",
            data = "03/08/2023",
            descricao = "Excluir constraint de clientes marcados",
            motivacao = "Resolver problema de deadlock em banco de produção")
    public void migracaoVersao1973() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table public.clientesmarcados drop constraint clientesmarcados_usuario_fkey;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create index clientesmarcados_usuario_index on public.clientesmarcados (usuario);", c);
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "04/08/2023",
            descricao = "Migração configs Integrações Leads CRM para nova tela",
            motivacao = "E4-7")
    public void migracaoVersao1974() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN integracaoWeHelpHabilitada BOOLEAN DEFAULT TRUE;", c);
            } catch (Exception ignore) {
            }
            try {
                // BuzzLead
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.configuracaointegracaobuzzlead (\n" +
                        "\tcodigo serial4 NOT NULL,\n" +
                        "\ttokenbuzzlead varchar(255) NULL,\n" +
                        "\thabilitada bool NULL,\n" +
                        "\tempresa int4 NULL,\n" +
                        "\thoralimite varchar(5) NULL,\n" +
                        "\tresponsavelpadrao int4 NULL,\n" +
                        "\tacaoobjecao int4 NULL,\n" +
                        "\tCONSTRAINT configuracaointegracaobuzzlead_pkey PRIMARY KEY (codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaobuzzlead_empresa_fkey FOREIGN KEY (empresa) REFERENCES public.empresa(codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaobuzzlead_usuario_fkey FOREIGN KEY (responsavelpadrao) REFERENCES public.usuario(codigo)\n" +
                        ");", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaobuzzlead_empresa_idx ON public.configuracaointegracaobuzzlead USING btree (empresa);\n", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaobuzzlead_responsavelpadrao_idx ON public.configuracaointegracaobuzzlead USING btree (responsavelpadrao);", c);
            } catch (Exception ignore) {
            }

            try {
                // Wordpress
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.configuracaointegracaowordpress (\n" +
                        "\tcodigo serial4 NOT NULL,\n" +
                        "\thabilitada bool NULL,\n" +
                        "\tempresa int4 NULL,\n" +
                        "\thoralimite varchar(5) NULL,\n" +
                        "\tresponsavelpadrao int4 NULL,\n" +
                        "\tacaoobjecao int4 NULL,\n" +
                        "\tCONSTRAINT configuracaointegracaowordpress_pkey PRIMARY KEY (codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaowordpress_empresa_fkey FOREIGN KEY (empresa) REFERENCES public.empresa(codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaowordpress_usuario_fkey FOREIGN KEY (responsavelpadrao) REFERENCES public.usuario(codigo)\n" +
                        ");", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaowordpress_empresa_idx ON public.configuracaointegracaowordpress USING btree (empresa);\n", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaowordpress_responsavelpadrao_idx ON public.configuracaointegracaowordpress USING btree (responsavelpadrao);", c);
            } catch (Exception ignore) {
            }
            try {
                // Join (leads)
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.configuracaointegracaojoin (\n" +
                        "\tcodigo serial4 NOT NULL,\n" +
                        "\thabilitada bool NULL,\n" +
                        "\tempresa int4 NULL,\n" +
                        "\thoralimite varchar(5) NULL,\n" +
                        "\tresponsavelpadrao int4 NULL,\n" +
                        "\tacaoobjecao int4 NULL,\n" +
                        "\tCONSTRAINT configuracaointegracaojoin_pkey PRIMARY KEY (codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaojoin_empresa_fkey FOREIGN KEY (empresa) REFERENCES public.empresa(codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaojoin_usuario_fkey FOREIGN KEY (responsavelpadrao) REFERENCES public.usuario(codigo)\n" +
                        ");", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaojoin_empresa_idx ON public.configuracaointegracaojoin USING btree (empresa);\n", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaojoin_responsavelpadrao_idx ON public.configuracaointegracaojoin USING btree (responsavelpadrao);", c);
            } catch (Exception ignore) {
            }
            try {
                // Generica (leads)
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.configuracaointegracaogenericaleads (\n" +
                        "\tcodigo serial4 NOT NULL,\n" +
                        "\thabilitada bool NULL,\n" +
                        "\tempresa int4 NULL,\n" +
                        "\thoralimite varchar(5) NULL,\n" +
                        "\tresponsavelpadrao int4 NULL,\n" +
                        "\tacaoobjecao int4 NULL,\n" +
                        "\tCONSTRAINT configuracaointegracaogenericaleads_pkey PRIMARY KEY (codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaogenericaleads_empresa_fkey FOREIGN KEY (empresa) REFERENCES public.empresa(codigo),\n" +
                        "\tCONSTRAINT configuracaointegracaogenericaleads_usuario_fkey FOREIGN KEY (responsavelpadrao) REFERENCES public.usuario(codigo)\n" +
                        ");", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaogenericaleads_empresa_idx ON public.configuracaointegracaogenericaleads USING btree (empresa);\n", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaointegracaogenericaleads_responsavelpadrao_idx ON public.configuracaointegracaogenericaleads USING btree (responsavelpadrao);", c);
            } catch (Exception ignore) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaoempresahubspot ADD COLUMN horalimite VARCHAR(5);", c);
            } catch (Exception e) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaoempresahubspot ADD COLUMN responsavelpadrao INT4;", c);
            } catch (Exception e) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaoempresahubspot ADD COLUMN acaoobjecao INT4;", c);
            } catch (Exception e) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE configuracaoempresahubspot config_hub \n " +
                        " SET horalimite = config_rd.horalimite, responsavelpadrao = config_rd.responsavelpadrao, acaoobjecao = config_rd.acaoobjecao \n" +
                        " FROM configuracaoempresardstation config_rd WHERE config_rd.empresa = config_hub.empresa;", c);
            } catch (Exception e) {
            }
            try {
                ProcessoPovoarConfigsIntegracoes.povoarConfiguracaoIntegracaoBuzzLead(c);
            } catch (Exception e) {
            }
            try {
                ProcessoPovoarConfigsIntegracoes.povoarConfiguracaoIntegracaoWordPress(c);
            } catch (Exception e) {
            }
            try {
                ProcessoPovoarConfigsIntegracoes.povoarConfiguracaoIntegracaoJoin(c);
            } catch (Exception e) {
            }
            try {
                ProcessoPovoarConfigsIntegracoes.povoarConfiguracaoIntegracaoGenericaLeads(c);
            } catch (Exception e) {
            }
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "28/07/2023",
            descricao = "Incluir tabela para manter info de qual empresa foi feito o checkin",
            motivacao = "E3-80")
    public void migracaoVersao1975() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table public.infocheckin (" +
                            "codigo serial not null constraint infocheckin_pk primary key,\n" +
                            "cliente integer constraint infocheckin_cliente_codigo_fk references public.cliente,\n" +
                            "empresa integer constraint infocheckin_empresa_codigo_fk references public.empresa,\n" +
                            "token varchar(100),\n" +
                            "integracao integer,\n" +
                            "cancelado boolean default false,\n" +
                            "periodoacesso integer);", c);
        }
    }


    @Processo(autor = "Glauco Troncha Camargo",
            data = "10/08/2023",
            descricao = "Ajuste na integridade dos dados de MovConta",
            motivacao = "E1-258")
    public void migracaoVersao1976() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update movconta\n" +
                    "set valorpago = valor\n" +
                    "where valorpago = 0\n" +
                    "and valor > 0\n" +
                    "and dataquitacao  is not null", c);
        }
    }

    @Processo(autor = "Ivan Alves",
            data = "17/08/2023",
            descricao = "Incluir campo para habilitar ou não o relatório quinzenal da f360",
            motivacao = "SD1-8752")
    public void migracaoVersao1977() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN integracaoF360Quinzenal BOOLEAN DEFAULT TRUE;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "15/08/2023",
            descricao = "Configuração FacilitePay",
            motivacao = "SD2-5298")
    public void migracaoVersao1978() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN concContasPagarFacilitePay BOOLEAN DEFAULT TRUE;", c);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "28/08/2023",
            descricao = "Nova Venda Avulsa",
            motivacao = "Nova Venda Avulsa")
    public void migracaoVersao1979() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendaavulsa ADD COLUMN nova BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "25/08/2023",
            descricao = "Aumentando tamanho do campo ipAceiteTermosPacto",
            motivacao = "SD1-8886")
    public void migracaoVersao1980() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ALTER COLUMN ipAceiteTermosPacto TYPE VARCHAR(40)", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "25/08/2023",
            descricao = "Inclusão do visitante recorrente na ordenação da meta",
            motivacao = "SD1-8836")
    public void migracaoVersao1981() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update configuracaosistemacrm  set ordenacaometas = concat(ordenacaometas ,  ',VR') ", c);
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "08/08/2023",
            descricao = "Incluir coluna apresentarPactoApp",
            motivacao = "APP-3817")
    public void migracaoVersao1982() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table produto add apresentarpactoapp boolean default false;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "23/08/2023",
            descricao = "Incluir tabela para manter info de parcelas do pinpadpedido",
            motivacao = "SD2-5342")
    public void migracaoVersao1983() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table public.pinpadpedidomovparcela (" +
                            "codigo serial not null constraint pinpadpedidomovparcela_pk primary key,\n" +
                            "pinpadpedido integer constraint pinpadpedidomovparcela_pinpadpedido_fk references public.pinpadpedido ON DELETE CASCADE,\n" +
                            "movparcela integer constraint pinpadpedidomovparcela_movparcela_fk references public.movparcela ON DELETE CASCADE);", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "04/09/2023",
            descricao = "Adicionar coluna enviaTelefoneEmail",
            motivacao = "E1-325")
    public void migracaoVersao1984() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table empresaconfigestacionamento add enviatelefoneemail boolean default false;", c);
        }
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "04/07/2023",
            descricao = "Afastamento para dependente de plano compartilhado",
            motivacao = "E1-236")
    public void migracaoVersao1985() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table dependenteplanocompartilhado rename to contratodependente;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter sequence dependenteplanocompartilhado_codigo_seq rename to contratodependente_codigo_seq", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table contratodependente add column datafinalajustada date;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table contratodependente add column posicaodependente integer;", c);


            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table contratodependentehistorico(\n" +
                            "   codigo serial not null primary key,\n" +
                            "   contratodependente integer,\n" +
                            "   dependente integer,\n" +
                            "   iniciodependencia date,\n" +
                            "   finaldependencia date\n" +
                            ");", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE contratodependentehistorico\n" +
                            "ADD CONSTRAINT fk_contratodependentehistorico_dependente\n" +
                            "FOREIGN KEY (dependente) REFERENCES cliente (codigo);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE contratodependentehistorico\n" +
                            "ADD CONSTRAINT fk_contratodependentehistorico_contratodependente\n" +
                            "FOREIGN KEY (contratodependente) REFERENCES contratodependente (codigo);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "update contratodependente\n" +
                            "            set datafinal = contrato.vigenciaateajustada,\n" +
                            "                    datafinalajustada = contrato.vigenciaateajustada\n" +
                            "            from contrato\n" +
                            "            where contratodependente.contrato = contrato.codigo", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "UPDATE contratodependente\n" +
                            "            SET posicaodependente = subquery.sequencial\n" +
                            "            FROM (\n" +
                            "                    SELECT codigo, contrato, ROW_NUMBER() OVER (PARTITION BY contrato ORDER BY contrato) AS sequencial\n" +
                            "                    FROM contratodependente\n" +
                            "            ) AS subquery\n" +
                            "            WHERE contratodependente.codigo = subquery.codigo;", c);


            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "insert into contratodependentehistorico (contratodependente, dependente, iniciodependencia, finaldependencia)\n" +
                            "select cd.codigo, cliente, datainicio, case when datafinalajustada < now() then datafinalajustada else null end \n" +
                            "from contratodependente cd \n" +
                            "left join contratodependentehistorico cdh on cdh.contratodependente = cd.codigo \n" +
                            "where cliente is not null and cdh.codigo is null", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "insert into contratodependente (titular, cliente, datainicio, datafinal, contrato, datafinalajustada, posicaodependente)\n" +
                            "select cli.codigo, null, c.vigenciade, c.vigenciaate, c.codigo, c.vigenciaateajustada, 1 from contrato c\n" +
                            "inner join cliente cli on c.pessoa = cli.pessoa\n" +
                            "inner join plano pl on c.plano = pl.codigo \n" +
                            "left join contratodependente cd on cd.contrato = c.codigo\n" +
                            "where pl.quantidadecompartilhamentos > 0\n" +
                            "and c.vigenciaateajustada > now()\n" +
                            "and cd.codigo is null", c);


            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table afastamentocontratodependente(\n" +
                            "   codigo serial not null primary key,\n" +
                            "   contratodependente integer,\n" +
                            "   dataregistro timestamp,\n" +
                            "   inicioafastamento date,\n" +
                            "   finalafastamento date,\n" +
                            "   nrdiassomar integer,\n" +
                            "   observacao text,\n" +
                            "   tipoafastamento varchar(20),\n" +
                            "   justificativaoperacao integer\n" +
                            ");", c);

        }
    }


    @Processo(autor = "Glauco Troncha Camargo",
            data = "08/09/2023",
            descricao = "Creating the LogApi table",
            motivacao = "E1-319")
    public void migracaoVersao1986() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table logapi (" +
                            "codigo serial not null constraint logapi_pk primary key,\n" +
                            "descricaotoken varchar,\n" +
                            "datauso timestamp,\n" +
                            "ip varchar(30),\n" +
                            "method varchar,\n" +
                            "uri varchar,\n" +
                            "params text);", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "11/07/2023",
            descricao = "Replicação do modelo de contrato",
            motivacao = "E1-218")
    public void migracaoVersao1987() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE modelocontratoredeempresa (\n" +
                            "codigo serial4 NOT NULL primary key,\n" +
                            "modeloContrato INTEGER,\n" +
                            "chave VARCHAR(255),\n" +
                            "empresaZw INTEGER,\n" +
                            "dataCadastro DATE,\n" +
                            "dataAtualizacao DATE,\n" +
                            "qtdeEmpresasRede INTEGER,\n" +
                            "qtdeModeloContratosReplicados INTEGER, \n" +
                            "qtdeModeloContratosNaoReplicados INTEGER, \n" +
                            "modelocontratoreplicado INTEGER, \n" +
                            "nomeUnidade VARCHAR(255), \n" +
                            "mensagemSituacao VARCHAR(255));", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD permitirReplicarModeloContratoRedeEmpresa BOOLEAN DEFAULT FALSE; \n" +
                    "ALTER TABLE planotextopadrao ALTER COLUMN descricao TYPE VARCHAR(55);", c);

        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "12/09/2023",
            descricao = "Customização Sesi Cnpj Cliente",
            motivacao = "E2-301")
    public void migracaoVersao1988() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN cnpjclientesesi VARCHAR(18);", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "30/08/2023",
            descricao = "E2-374",
            motivacao = "E2-374")
    public void migracaoVersao1989() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE clientemensagem ADD COLUMN dataRegistro TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE clientemensagem ADD COLUMN dataAtualizacao TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "25/08/2023",
            descricao = "Incluir campos para gerar pix com dados do responsável",
            motivacao = "SD1-5349")
    public void migracaoVersao1990() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN UtilizarNomeResponsavelNoPixMenorIdade BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN UtilizarNomeResponsavelNoPixMaiorIdade BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "18/09/2023",
            descricao = "Inserir contratos dependentes contratos com plano compartilhado",
            motivacao = "E1-349")
    public void migracaoVersao1991() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "insert into contratodependente (titular, cliente, datainicio, datafinal, contrato, datafinalajustada, posicaodependente)\n" +
                            "select cli.codigo, null, c.vigenciade, c.vigenciaate, c.codigo, c.vigenciaateajustada, 1 from contrato c\n" +
                            "inner join cliente cli on c.pessoa = cli.pessoa\n" +
                            "inner join plano pl on c.plano = pl.codigo \n" +
                            "left join contratodependente cd on cd.contrato = c.codigo\n" +
                            "where pl.quantidadecompartilhamentos > 0\n" +
                            "and c.vigenciaateajustada > now()\n" +
                            "and cd.codigo is null", c);
        }
    }

    @Processo(autor = "Wellington Santos",
            data = "24/08/2023",
            descricao = "Incluir tabela configuracaointegracaobotconversa para  configuração do fluxo botconversa",
            motivacao = "E3-251")
    public void migracaoVersao1992() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.configuracaointegracaobotconversa (\n" +
                            "\tcodigo serial4 NOT NULL,\n" +
                            "\turlwebhoobotconversa varchar(200) NULL,\n" +
                            "\tdescricao varchar(300) NULL,\n" +
                            "\tempresa int4 NULL,\n" +
                            "\ttipofluxo varchar(1) NULL,\n" +
                            "\tfase varchar(2) NULL,\n" +
                            "\tCONSTRAINT configuracaointegracaobotconversa_pkey PRIMARY KEY (codigo)\n" +
                            ");", c);
        }
    }

    @Processo(autor = "Wellington Santos",
            data = "24/08/2023",
            descricao = "Criação da coluna urlwebhoobotconversa ",
            motivacao = "E3-251")
    public void migracaoVersao1993() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table maladireta  add column urlwebhoobotconversa varchar(200) NULL", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "12/09/2023",
            descricao = "FacilitePay",
            motivacao = "FacilitePay")
    public void migracaoVersao1994() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operadoracartao ADD COLUMN codigoIntegracaoFacilitePay Integer;", c);
        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "05/09/2023",
            descricao = "Adicionar coluna conciliarSemNumeroParcela",
            motivacao = "SD2-5437")
    public void migracaoVersao1995() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table configuracaosistema add conciliarsemnumeroparcela boolean default false;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "19/09/2023",
            descricao = "Incluir campo configuração gerar parcela de multa separada no cancelamento antecipado",
            motivacao = "E1-316")
    public void migracaoVersao1996() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN cancelamentoantecipadogerarparcelamultaseparada BOOLEAN DEFAULT FALSE;", c);
        }
    }


    @Processo(autor = "Rodrigo Estulano",
            data = "20/09/2023",
            descricao = "Criação configuração redirecionamento tela de Pagamento Online",
            motivacao = "SD2-5524")
    public void migracaoVersao1997() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN irTelaPagamentoCartaoCreditoFormaPagamento BOOLEAN DEFAULT TRUE;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "25/09/2023",
            descricao = "Adicionar coluna gerarIndicacaoParaCadastroConvidadosVendasOnline",
            motivacao = "E1-358")
    public void migracaoVersao1998() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table configuracaosistemacrm add gerarindicacaoparacadastroconvidadosvendasonline boolean default false;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "26/09/2023",
            descricao = "Adicionar DELETE ON CASCADE no contratodependentehistorico",
            motivacao = "E1-365")
    public void migracaoVersao1999() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratodependentehistorico DROP CONSTRAINT fk_contratodependentehistorico_contratodependente;", c);
            } catch (Exception ignore) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratodependentehistorico ADD CONSTRAINT fk_contratodependentehistorico_contratodependente FOREIGN KEY (contratodependente) REFERENCES public.contratodependente (codigo) ON DELETE CASCADE;", c);
            } catch (Exception ignore) {
            }
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "21/09/2023",
            descricao = "SD1-9172 - Fix estorno contrato com dependentes",
            motivacao = "SD1-9172 - Fix estorno contrato com dependentes")
    public void migracaoVersao2000() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratodependentehistorico DROP CONSTRAINT fk_contratodependentehistorico_contratodependente", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratodependentehistorico ADD CONSTRAINT fk_contratodependentehistorico_contratodependente FOREIGN KEY (contratodependente) REFERENCES contratodependente(codigo) ON DELETE CASCADE", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "12/09/2023",
            descricao = "Criar a tabela empresahotsite",
            motivacao = "SD1-8737")
    public void migracaoVersao2001() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table empresahotsite (codigo serial not null constraint empresahotsite_pk primary key, \n" +
                            "empresa integer constraint empresahotsite_empresa_fk references public.empresa ON DELETE CASCADE, ativo bool);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "insert into empresahotsite(empresa, ativo)\n" +
                            "select codigo, true from empresa ;", c);
        }
    }

    @Processo(autor = "Hebert Frederick",
            data = "25/09/2023",
            descricao = "Incluir tabela logtotalpass para gravar log para comunicação com API TotalPass",
            motivacao = "E3-298")
    public void migracaoVersao2002() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append(" CREATE TABLE public.logtotalpass ( \n")
                    .append(" codigo serial4 NOT NULL,\n")
                    .append(" pessoa integer,\n")
                    .append(" uri text null,\n")
                    .append(" apikey varchar(30),\n")
                    .append(" dataregistro timestamp NULL,\n")
                    .append(" json text NULL,\n")
                    .append(" tempo_resposta bigint,\n")
                    .append(" resposta varchar(15),\n")
                    .append(" tipo varchar(100),\n")
                    .append(" empresa int4 NULL,\n")
                    .append(" ip varchar(100),\n")
                    .append(" usuario integer,\n")
                    .append(" CONSTRAINT logtotalpass_pkey PRIMARY KEY (codigo) \n")
                    .append(" );\n")
                    .append(" CREATE INDEX logtotalpass_empresa_idx ON public.logtotalpass USING btree (empresa);\n")
                    .append(" ALTER TABLE public.logtotalpass ADD CONSTRAINT fk_logtotalpass_empresa FOREIGN KEY (empresa) REFERENCES public.empresa(codigo);");

            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }


    @Processo(autor = "Hebert Frederick",
            data = "26/09/2023",
            descricao = "Incluir coluna origem para gravar log para comunicação com API TotalPass",
            motivacao = "E3-311")
    public void migracaoVersao2003() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table logtotalpass add column origem varchar(15);", c);
        }
    }

    @Processo(autor = "Alisson Melo",
            data = "26/09/2023",
            descricao = "Criação de coluna de bloqueio de acesso para alunos com par-q não assinado",
            motivacao = "SD2-5606")
    public void migracaoVersao2004() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN bloquearAcessoAlunoParqNaoAssinado BOOLEAN DEFAULT FALSE;", c);
        }
    }


    @Processo(autor = "Rodrigo Estulano",
            data = "27/09/2023",
            descricao = "Criar nova config cobrança automática bloqueada link de cadastro",
            motivacao = "SD2-5626")
    public void migracaoVersao2005() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    " ALTER TABLE empresa ADD COLUMN gerarAutCobrancaComCobAutBloqueada BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "03/10/2023",
            descricao = "Adicionar coluna cancelamentoAntecipadoCancelarNaDataCancelamentoInformada",
            motivacao = "E1-316")
    public void migracaoVersao2006() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN cancelamentoAntecipadoCancelarNaDataCancelamentoInformada BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "03/10/2023",
            descricao = "Criação de nova configuração de venda contrato concomitante vendas online",
            motivacao = "SD2-5669")
    public void migracaoVersao2007() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN permiteContratoConcomitanteComParcelaEmAberto BOOLEAN DEFAULT TRUE;", c);
        }
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "05/10/2023",
            descricao = "Correção de produto gerado de forma errada em todos os bancos",
            motivacao = "IN-526")
    public void migracaoVersao2008() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            IntegracaoMember integracaoMemberDAO = new IntegracaoMember(c);
            Produto produtoDAO = new Produto(c);
            Cliente clienteDAO = new Cliente(c);
            List<IntegracaoMemberVO> integracoes = integracaoMemberDAO.consultarIntegracoesPorEmpresa(0);

            if (integracoes.isEmpty()) {
                List<ProdutoVO> produtosFreepassIntegracao = produtoDAO.consultarPorDescricaoTipoProduto("FREEPASS - IN", "FR", false, Uteis.NIVELMONTARDADOS_MINIMOS);

                for (ProdutoVO produtoFreepassIntegracao : produtosFreepassIntegracao) {
                    List<ClienteVO> clientesComFreepass = clienteDAO.consultarClientesComProdutoFreepass(produtoFreepassIntegracao.getCodigo());

                    for (ClienteVO clienteComFreepass : clientesComFreepass) {
                        clienteDAO.removerFreePass(clienteComFreepass);
                        SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE situacaoclientesinteticodw SET freepass = false WHERE codigocliente = " + clienteComFreepass.getCodigo(), c);
                        SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM periodoacessocliente WHERE tipoacesso = 'PL' AND pessoa = " + clienteComFreepass.getPessoa().getCodigo() + " AND datafinalacesso > now();", c);
                    }

                    SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM produto WHERE codigo = " + produtoFreepassIntegracao.getCodigo(), c);
                }
            }
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "04/10/2023",
            descricao = "Atualizar coluna código para ser do tipo serial",
            motivacao = "SD1-9352")
    public void migracaoVersao2009() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE SEQUENCE public.modalidadecomissaocolaborador_codigo_seq START WITH 1;", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.modalidadecomissaocolaborador ALTER COLUMN codigo SET DEFAULT nextval('modalidadecomissaocolaborador_codigo_seq');", c);
            } catch (Exception ignore) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE SEQUENCE public.turmacomissaocolaborador_codigo_seq START WITH 1;", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.turmacomissaocolaborador ALTER COLUMN codigo SET DEFAULT nextval('turmacomissaocolaborador_codigo_seq');", c);
            } catch (Exception ignore) {
            }
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE SEQUENCE public.alunocomissaocolaborador_codigo_seq START WITH 1;", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.alunocomissaocolaborador ALTER COLUMN codigo SET DEFAULT nextval('alunocomissaocolaborador_codigo_seq');", c);
            } catch (Exception ignore) {
            }
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "04/10/2023",
            descricao = "Adicionar coluna campanhaCupomDescontoIndicacoes",
            motivacao = "E1-377")
    public void migracaoVersao2010() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN campanhaCupomDescontoIndicacoes int4;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "16/10/2023",
            descricao = "Corrigir aulas desmarcadas do contrato anterior para os contrato que tiveram renovação automatica",
            motivacao = "SD1-9379")
    public void migracaoVersao2011() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE auladesmarcada ad SET contrato = con1.codigo, contratoanterior = con2.codigo\n" +
                    "FROM contrato con1\n" +
                    "INNER JOIN contrato con2 ON con2.codigo = con1.contratobaseadorenovacao\n" +
                    "INNER JOIN empresa emp ON emp.codigo = con1.empresa\n" +
                    "WHERE ad.contrato = con2.codigo AND ad.empresa = con1.empresa AND ad.datareposicao is null AND con1.situacao = 'AT'\n" +
                    "AND con2.renovavelAutomaticamente = true AND emp.adicionarAulasDesmarcadasContratoAnterior = true", c);
        }
    }

    @Processo(autor = "Michel Farias Maia",
            data = "27/09/2023",
            descricao = "Cria a coluna para salvar a opção de emissão do valor total do produto em notas por Faturamento",
            motivacao = "SD2-5594")
    public void migracaoVersao2012() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN emitevalortotalfaturamento BOOLEAN DEFAULT false;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "10/10/2023",
            descricao = "Criação de nova configuração de venda de produtos para alunos de outras unidades",
            motivacao = "SD2-5659")
    public void migracaoVersao2013() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN permiteVendaProdutoAlunoOutraUnidade BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "18/10/2023",
            descricao = "Nova coluna para pessoa ms tabela horarioacessosistema",
            motivacao = "Coluna para horarios do usuario tela nova do cliente")
    public void migracaoVersao2014() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE horarioacessosistema ADD COLUMN horario INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE horarioacessosistema ADD CONSTRAINT fk_horarioacessosistema_horario " +
                    "FOREIGN KEY (horario) REFERENCES horario (codigo);", c);
        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "05/10/2023",
            descricao = "Criação de nova configuração de venda contrato para CNPJ no vendas online",
            motivacao = "SD2-5677")
    public void migracaoVersao2015() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN exibirTipoDocumentoTelaVendasOnline BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN nomeResponsavelEmpresa VARCHAR(50) NULL", c);
        }
    }

    @Processo(autor = "Kévio Castro",
            data = "23/10/2023",
            descricao = "Criação código para configuração do financeiro",
            motivacao = "Criar chave primaria para configuração do financeiro para facilitar a manipulação dos registros")
    public void migracaoVersao2016() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create sequence public.configuracaofinanceiro_codigo_seq;\n" +
                    "\n" +
                    "alter table public.configuracaofinanceiro\n" +
                    "    add codigo integer default nextval('public.configuracaofinanceiro_codigo_seq'::regclass);\n" +
                    "\n" +
                    "alter table public.configuracaofinanceiro\n" +
                    "    add constraint configuracaofinanceiro_pk\n" +
                    "        primary key (codigo);", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "19/10/2023",
            descricao = "Adicionar coluna permitirAlterarDataFinalContratoNoCancelamento",
            motivacao = "E1-412")
    public void migracaoVersao2017() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN permitirAlterarDataFinalContratoNoCancelamento BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "19/10/2023",
            descricao = "E1-413",
            motivacao = "E1-413")
    public void migracaoVersao2018() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN identificadorOrigemTipo Integer default 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movconta SET identificadorOrigemTipo = " + TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo() + " WHERE descricao ilike 'ESTORNO STONE CANCELAMENTO - NSU%' and identificadorOrigem ilike '%NSU%' and coalesce(identificadorOrigemTipo,0) = 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movconta SET identificadorOrigemTipo = " + TipoConciliacaoEnum.CHARGEBACK.getCodigo() + " WHERE descricao ilike 'CHARGEBACK STONE CANCELAMENTO - NSU%' and identificadorOrigem ilike '%NSU%' and coalesce(identificadorOrigemTipo,0) = 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN planoContasLancamentoAutomaticoSaida INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN planoContasLancamentoAutomaticoEntrada INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN centroCustoLancamentoAutomaticoSaida INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN centroCustoLancamentoAutomaticoEntrada INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE configuracaofinanceiro SET planoContasLancamentoAutomaticoSaida = planocontasdevolucao WHERE planoContasLancamentoAutomaticoSaida is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE configuracaofinanceiro SET centroCustoLancamentoAutomaticoSaida = centrocustodevolucao WHERE centroCustoLancamentoAutomaticoSaida is null;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "28/10/2023",
            descricao = "Configuração FacilitePay",
            motivacao = "E2-999")
    public void migracaoVersao2019() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ALTER COLUMN concContasPagarFacilitePay SET DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayReguaCobranca BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set facilitePayReguaCobranca = (select (p.ComunicadoAtrasoAtivo or p.CobrancaAntecipadaAtivo or p.ComunicadoCartaoAtivo) from pactopayconfig p where p.empresa = empresa.codigo ) where facilitePayReguaCobranca = false;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "20/10/2023",
            descricao = "Corrigir aulas desmarcadas do contrato anterior para todos os contratos(Antes limitava a renovação automatica)",
            motivacao = "SD1-9544")
    public void migracaoVersao2020() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE auladesmarcada ad SET contrato = con1.codigo, contratoanterior = con2.codigo\n" +
                    "FROM contrato con1\n" +
                    "INNER JOIN contrato con2 ON con2.codigo = con1.contratobaseadorenovacao\n" +
                    "INNER JOIN empresa emp ON emp.codigo = con1.empresa\n" +
                    "WHERE ad.contrato = con2.codigo AND ad.empresa = con1.empresa AND ad.datareposicao is null AND con1.situacao = 'AT'\n" +
                    "AND emp.adicionarAulasDesmarcadasContratoAnterior = true", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "03/11/2023",
            descricao = "Configuração FacilitePay",
            motivacao = "E2-999")
    public void migracaoVersao2021() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN lido BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN clicou BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN datalido TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN dataclicou TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }


    @Processo(autor = "Maurin Noleto",
            data = "01/11/2023",
            descricao = "Criacao de novo campo para registrar CPF do Responsavel Empresa",
            motivacao = "SD2-5945")
    public void migracaoVersao2022() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN cpfResponsavelEmpresa VARCHAR(15) NULL", c);
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "27/10/2023",
            descricao = "Cria a coluna para salvar a opção de obrigar preenchimento de todos os campos de registro de compra durante o pagamento de cartão de crédito via maquininha",
            motivacao = "SD1-9834")
    public void migracaoVersao2023() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN obrigatorioPreencherCamposCartao BOOLEAN DEFAULT false;", c);
        }
    }


    @Processo(autor = "Michel Farias Maia",
            data = "26/10/2023",
            descricao = "adicionar coluna GerarNotaFiscalComDesconto",
            motivacao = "SD2-5417")
    public void migracaoVersao2024() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN GerarNotaFiscalComDesconto BOOLEAN DEFAULT false;", c);
        }
    }

    @Processo(autor = "Kévio Castro",
            data = "08/11/2023",
            descricao = "adicionar coluna nomecampodescricao",
            motivacao = "Otimizar o log pra unificação de consultas")
    public void migracaoVersao2025() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table public.log\n" +
                    "    add nomecampodescricao varchar;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "06/11/2023",
            descricao = "Configuração Informar favorecido ao realizar a movimentação de recebíveis",
            motivacao = "E1-409")
    public void migracaoVersao2026() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN informarfavorecidoaorealizarmovimentacaorecebiveis BOOLEAN DEFAULT FALSE;", c);
        }
    }


    @Processo(autor = "Rodrigo Estulano",
            data = "07/11/2023",
            descricao = "Integração Pluggy Contas a Receber",
            motivacao = "SD2-5936")
    public void migracaoVersao2027() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN concContasReceberFacilitePay BOOLEAN DEFAULT FALSE;", c);
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo = " + PerfilUsuarioEnum.ADMINISTRADOR.getId(), c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (3, '9.72 - Conciliação de Contas a Receber','(0)(1)(2)(3)(9)(12)', "
                        + " 'ConciliacaoContasReceber', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Kévio Castro",
            data = "08/11/2023",
            descricao = "adicionar coluna nomecampodescricao",
            motivacao = "Otimizar o log pra unificação de consultas")
    public void migracaoVersao2028() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table public.log\n" +
                    "    add nomecampodescricao varchar;", c);
        }
    }

    @Processo(autor = "Kévio Castro",
            data = "09/11/2023",
            descricao = "adicionar coluna codigo para configuação de campo do cliente",
            motivacao = "Otimizar consultar e registros de log de forma padronizada")
    public void migracaoVersao2029() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create sequence public.configuracaosistemacadastrocliente_codigo_seq;\n" +
                    "alter table public.configuracaosistemacadastrocliente\n" +
                    "    add codigo integer default nextval('public.configuracaosistemacadastrocliente_codigo_seq'::regclass);\n" +
                    "alter table public.configuracaosistemacadastrocliente\n" +
                    "    drop constraint configuracaosistemacliente_pkey;\n" +
                    "alter table public.configuracaosistemacadastrocliente\n" +
                    "    add constraint configuracaosistemacadastrocliente_pk\n" +
                    "        primary key (codigo);\n" +
                    "create unique index configuracaosistemacadastrocliente_unique_index\n" +
                    "    on public.configuracaosistemacadastrocliente (nome, visitante);", c);
        }
    }

    @Processo(autor = "Kévio Castro",
            data = "09/11/2023",
            descricao = "adicionar coluna tags na tabela de logs",
            motivacao = "Otimizar consultar de logs")
    public void migracaoVersao2030() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table public.log\n" +
                    "    add tags varchar;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "12/04/2023",
            descricao = "E2-196",
            motivacao = "E2-196")
    public void migracaoVersao2031() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN identificadorDados text;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "14/11/2023",
            descricao = "Token para liberação de operações no ZW",
            motivacao = "SD2-6058")
    public void migracaoVersao2032() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE tokenOperacao (codigo serial not null, dataRegistro TIMESTAMP WITHOUT TIME ZONE, usuario int, token text, utilizado BOOLEAN DEFAULT FALSE)", c);
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "16/11/2023",
            descricao = "Adicionar informações para integrações",
            motivacao = "SD1-9396")
    public void migracaoVersao2033() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto add column crsesi integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto add column projetosesi integer;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "21/11/2023",
            descricao = "Adicionar informações para integrações",
            motivacao = "SD1-9396")
    public void migracaoVersao2034() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto add column contafinanceirasesi integer;", c);
        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "17/11/2023",
            descricao = "Configuração Multa e Juros Boleto Itaú no Sistema ou no Portal do Itaú",
            motivacao = "SD2-6031")
    public void migracaoVersao2035() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN gerarMultaEJurosRemessaItauCNAB400 BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "21/11/2023",
            descricao = "Webhook Pix",
            motivacao = "IN-583")
    public void migracaoVersao2036() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table pixwebhook drop column pix;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table pixwebhook add column tipoConvenio int;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE pixWebhookDetalhe (codigo serial not null, pixwebhook int, txid text, pix int, dataRegistro TIMESTAMP WITHOUT TIME ZONE, processado BOOLEAN DEFAULT FALSE, dataProcessamento TIMESTAMP WITHOUT TIME ZONE)", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "31/10/2023",
            descricao = "Incluir tabela para integração com a bitrix24",
            motivacao = "SD1-9410")
    public void migracaoVersao2037() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table configuracaoempresabitrix24(\n" +
                            "\t\tcodigo serial4 NOT null , \n" +
                            "\t\tempresa varchar(50), \n" +
                            "\t\turl text NULL, \n" +
                            "\t\tacaoobjecao Int4,\n" +
                            "\t\thabilitada bool,\n" +
                            "\t\tresponsavelpadrao INT4,\n" +
                            "\t\tacao varchar(100),\n" +
                            "CONSTRAINT configuracaoempresabitrix_pk PRIMARY KEY (codigo))\n;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "24/11/2023",
            descricao = "Webhook Pix",
            motivacao = "IN-583")
    public void migracaoVersao2038() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pix ADD COLUMN pagoOrigemWebhook BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "05/06/2023",
            descricao = "E3-6",
            motivacao = "E3-6")
    public void migracaoVersao2039() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table usuario add column perfilTw integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table usuario add column statusTw integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table usuario add column tipoTw integer;", c);
        }
    }

    @Processo(autor = "Welison Jefferson",
            data = "23/11/2023",
            descricao = "Propagar contrato assinado na renovação de contrato",
            motivacao = "GC-51")
    public void migracaoVersao2040() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN manterContratoAssinadoNaRenovacaoContrato BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contrato ADD COLUMN primeiroContratoBaseadoRenovacao INT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_contrato_primeiroContratoBaseadoRenovacao ON public.contrato USING btree (primeiroContratoBaseadoRenovacao);", c);
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "16/10/2023",
            descricao = "Criação de documentação da assinatura termo de aceite APP",
            motivacao = "APP-4190")
    public void migracaoVersao2041() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE termoaceite (\n" +
                    "                             codigo SERIAL PRIMARY KEY,\n" +
                    "                             descricao VARCHAR(255) UNIQUE NOT NULL)", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE termoaceiteassinatura (\n" +
                    "                                 codigo SERIAL PRIMARY KEY,\n" +
                    "                                 data TIMESTAMP NOT NULL,\n" +
                    "                                 nome VARCHAR(255) NOT NULL,\n" +
                    "                                 cpf VARCHAR(14) NOT NULL,\n" +
                    "                                 ip VARCHAR(255) NOT NULL,\n" +
                    "                                 termoaceite INT NOT NULL,\n" +
                    "                                 FOREIGN KEY (termoaceite) REFERENCES termoaceite(codigo))", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "04/12/2023",
            descricao = "Webhook Pix",
            motivacao = "IN-583")
    public void migracaoVersao2042() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pixwebhook ADD COLUMN pixwebhookoamd INT;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "06/12/2023",
            descricao = "Ajustando dados do Pessoa-ms",
            motivacao = "M1-34")
    public void migracaoVersao2043() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table usuarioperfilacesso add column bloquearacessoacademia boolean;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table usuarioperfilacesso add column bloquearAcessoSistema boolean;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "06/12/2023",
            descricao = "gleidson - pratique",
            motivacao = "gleidson - pratique")
    public void migracaoVersao2044() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD tokenApiConversao VARCHAR;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "06/12/2023",
            descricao = "Nova configuração Pacto Flow",
            motivacao = "M2-349")
    public void migracaoVersao2045() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN apresentarPactoFlow BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "06/12/2023",
            descricao = "Adicionar configuração cancelamentoAutomaticoAntecipadoContratoForaRecorrencia",
            motivacao = "GC-27")
    public void migracaoVersao2046() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN cancelamentoAutomaticoAntecipadoContratoForaRecorrencia BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "13/12/2023",
            descricao = "cria tabela para manter informacoes para a migracao do sistema Pacto",
            motivacao = "manter infos relevantes para a migracao")
    public void migracaoVersao2047() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table infomigracao(\n" +
                    "\tcodigo serial primary key,\n" +
                    "\ttipoinfo int not null,\n" +
                    "\tusuario int,\n" +
                    "\tinfo text\t\n" +
                    ");", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "14/12/2023",
            descricao = "Meta TPV FacilitePay",
            motivacao = "Meta TPV FacilitePay")
    public void migracaoVersao2048() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN valorMetaFacilitePay double precision;", c);
        }
    }

    @Processo(autor = "Denis Silva",
            data = "11/07/2023",
            descricao = "incluindo coluna de unificado para diferenciar perfil unificado bdzillyon e bdmusc",
            motivacao = "E4-315")
    public void migracaoVersao2049() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.usuarioperfilacesso ADD unificado boolean NOT NULL DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.perfilacesso ADD unificado boolean NOT NULL DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.permissao ADD unificado boolean NOT NULL DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.permissao ADD codigorecurso int NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.perfilacesso ADD codigoperfilzw int NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.perfilacesso ADD codigoperfiltreino int NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.permissao ADD sistema varchar NULL DEFAULT 'bdzillyon';", c);
        }
    }

    @Processo(autor = "Denis Silva",
            data = "30/11/2023",
            descricao = "incluindo coluna de perfil legado na tabela usuario",
            motivacao = "E4-315")
    public void migracaoVersao2050() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.usuario ADD perfil_legado varchar(255) NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.usuarioperfilacesso add modulospermitidos varchar(255);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "15/12/2023",
            descricao = "Criar tabela para registrar informações da requisição de novas contas facilitePay",
            motivacao = "evitar perder dados importantes em caso de erro")
    public void migracaoVersao2051() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table infoNovoMerchantPagoLivre(\n" +
                    " codigo serial primary key, \n" +
                    " dataRegistro timestamp without time zone, \n" +
                    " paramsEnvio text, \n" +
                    " paramsResposta text,\n" +
                    " tipoConvenioCobranca int \n" +
                    ");", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "15/12/2023",
            descricao = "Tela do Aluno",
            motivacao = "Tela do Aluno")
    public void migracaoVersao2052() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE infomigracao ADD COLUMN origem character varying (5);", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "15/12/2023",
            descricao = "Configuração sistema",
            motivacao = "M1-7")
    public void migracaoVersao2053() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE ConfiguracaoSistema ADD COLUMN termoresponsabilidadeExaluno  boolean NOT NULL DEFAULT false;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "28/12/2023",
            descricao = "cria tabela para armazenar log generico",
            motivacao = "cria tabela para armazenar log generico")
    public void migracaoVersao2054() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table loggenerico(\n" +
                    "codigo serial primary key,\n" +
                    "dataregistro TIMESTAMP WITHOUT TIME zone,\n" +
                    "tipo int,\n" +
                    "usuario int,\n" +
                    "info text\t\n" +
                    ");", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "05/01/2024",
            descricao = "Nova coluna configuração nova convênio de cobrança",
            motivacao = "M2-135")
    public void migracaoVersao2055() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN apresentarInativoNoPactoPay  boolean NOT NULL DEFAULT false;", c);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "05/01/2024",
            descricao = "alguns bancos estão sem coluna datafinalvigencia no produto",
            motivacao = "E3-419")
    public void migracaoVersao2056() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto add column datafinalvigencia timestamp null;", c);
        }
    }

    @Processo(autor = "Michel Farias Maia",
            data = "08/01/2024",
            descricao = "adicionar coluna ignorarCodigoDeBarrasEmissaoNfce",
            motivacao = "M2-568")
    public void migracaoVersao2057() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN ignorarCodigoDeBarrasEmissaoNfce BOOLEAN DEFAULT false;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "03/01/2024",
            descricao = "Taxa Pix valor absoluto",
            motivacao = "M2-595")
    public void migracaoVersao2058() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE formapagamento ADD COLUMN taxaPixValorAbsoluto boolean NOT NULL DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN planoContasTaxaPix int;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN centroCustoTaxaPix int;", c);
        }
    }


    @Processo(autor = "Anna Carolina",
            data = "09/01/2024",
            descricao = "Incluir coluna respostaApi para associar resposta da API na tabela logtotalpass",
            motivacao = "M2 - 454")
    public void migracaoVersao2059() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table logtotalpass add column respostaApi varchar(255);",
                    c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "02/01/2024",
            descricao = "FacilitePay - Conciliação Cartão de crédito",
            motivacao = "FacilitePay - Conciliação Cartão de crédito")
    public void migracaoVersao2060() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayConciliacaoCartao BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set facilitePayConciliacaoCartao = exists(select codigo from extratodiarioitem e where e.empresa = empresa.codigo);", c);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "10/01/2024",
            descricao = "atualiza o valor faturado da tabela movproduto de produtos vendidos pela nova tela de venda avulsa",
            motivacao = "corrigir problema da venda avulsa nova")
    public void migracaoVersao2061() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update movproduto m set valorfaturado = totalfinal \n" +
                    "where totalfinal > 0 and (valorfaturado = 0 or valorfaturado is null) and vendaavulsa > 0\n" +
                    "and exists (select codigo from vendaavulsa where nova and codigo = m.vendaavulsa)", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "09/01/2024",
            descricao = "Correção nome coluna despesaVeloc nas metas financeiras da empresa",
            motivacao = "M1-630")
    public void migracaoVersao2062() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE metafinanceiraempresa SET despesaveloc = despesavelo WHERE (despesavelo IS NOT NULL AND despesavelo <> 0) AND (despesaveloc IS NULL OR despesaveloc = 0);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE metafinanceiraempresa DROP COLUMN despesavelo;", c);
        }
    }


    @Processo(autor = "Kévio Castro",
            data = "10/01/2024",
            descricao = "Definição de valor padrão para nova permissão 2.85",
            motivacao = "Controlar a permissão para transferência de alunos com contratos ativos")
    public void migracaoVersao2063() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO public.permissao (codperfilacesso, nomeentidade, valorfinal, valorinicial, valorespecifico, tipopermissao,\n" +
                    "                              tituloapresentacao, permissoes,  unificado, codigorecurso,\n" +
                    "                              sistema)\n" +
                    "SELECT codperfilacesso,\n" +
                    "       'PermissaoTransferirClienteEmpresaContratoAtivo',\n" +
                    "       '',\n" +
                    "       '',\n" +
                    "       '',\n" +
                    "       2,\n" +
                    "       '2.85 - Permissão para transferir clientes com contrato ativo',\n" +
                    "       '(0)(1)(2)(3)(9)(12)',\n" +
                    "       false,\n" +
                    "       null,\n" +
                    "       'bdzillyon'\n" +
                    "FROM permissao\n" +
                    "WHERE nomeentidade = 'PermissaoTransferirClienteEmpresa';", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "11/01/2024",
            descricao = "Liberar acesso de plano personal em todas as unidades da academia, evitando bloqueios devido a nova regra de validação de plano empresa para plano personal",
            motivacao = "M1-628")
    public void migracaoVersao2064() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE planoempresa pem SET acesso = TRUE FROM plano pln WHERE pem.plano = pln.codigo AND pln.planopersonal;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "13/01/2024",
            descricao = "Retirar a possibilidade de colocar o dia 31 como vencimento",
            motivacao = "M1-477")
    public void migracaoVersao2065() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update plano set diasvencimentoprorata = replace(diasvencimentoprorata, ',31', '') where diasvencimentoprorata like '%,31%';", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "12/01/2024",
            descricao = "Nova coluna configuração financeiro",
            motivacao = "M2-631")
    public void migracaoVersao2066() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movconta ADD COLUMN retiradaAutomaticaRecebivelOrigemCancelamento  boolean NOT NULL DEFAULT false;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "15/01/2023",
            descricao = "Adicionar coluna gerarParcelasValorDiferenteRenovacao",
            motivacao = "GC-29")
    public void migracaoVersao2067() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planorecorrencia ADD COLUMN gerarParcelasValorDiferenteRenovacao BOOLEAN DEFAULT false;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "16/01/2023",
            descricao = "Garantir integridade da coluna statusTw da tabela usuario",
            motivacao = "APPS-343")
    public void migracaoVersao2068() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE usuario u SET statustw = 0 FROM colaborador c WHERE u.colaborador = c.codigo AND c.situacao = 'AT' AND (u.statustw <> 0 or u.statustw is null);", c);
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "17/01/2023",
            descricao = "Garantir integridade da coluna statusTw da tabela usuario - 2",
            motivacao = "APPS-362")
    public void migracaoVersao2069() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE usuario u SET statustw = 0 FROM colaborador c WHERE u.colaborador = c.codigo AND c.situacao = 'AT' AND (u.statustw <> 0 or u.statustw is null);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "17/01/2023",
            descricao = "Nova configuração apresentar produto para app Pacto Flow",
            motivacao = "M2-723")
    public void migracaoVersao2070() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN apresentarPactoFlow BOOLEAN DEFAULT false;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "17/01/2024",
            descricao = "Ajustar sequence errada para evitar erro no hibernate",
            motivacao = "IN-539")
    public void migracaoVersao2071() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER SEQUENCE integracaosesipessoa_codigo_seq RENAME TO integracaosesi_codigo_seq;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "17/01/2023",
            descricao = "Adicionando uma forma mais fácil de ver as solicitações aos serviços de importação, via api",
            motivacao = "M1-668")
    public void migracaoVersao2072() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE logIntegracoes (\n" +
                    "    codigo serial4 NOT NULL PRIMARY KEY,\n" +
                    "    servico TEXT,\n" +
                    "    dadosrecebidos TEXT,\n" +
                    "    resultado TEXT,\n" +
                    "    dataLancamento TIMESTAMP\n" +
                    ");", c);

        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "19/01/2023",
            descricao = "Adicionando uma forma mais fácil de ver as solicitações aos serviços de importação, via api",
            motivacao = "M2-754")
    public void migracaoVersao2073() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            //Corrigir ENTRADAS incorretas financeiro
            try {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT mr.codigo as codMovContaRateio \n");
                sql.append("FROM movconta mc \n");
                sql.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql.append("WHERE tipooperacao = " + TipoOperacaoLancamento.DEPOSITO.getCodigo() + " \n");
                sql.append("AND mr.tipoes = " + TipoES.SAIDA.getCodigo() + " \n");
                sql.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update = "UPDATE movcontarateio SET tipoes = " + TipoES.ENTRADA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir SAÍDAS incorretas financeiro
            try {
                StringBuilder sql2 = new StringBuilder();
                sql2.append("SELECT mr.codigo as codMovContaRateio \n");
                sql2.append("FROM movconta mc \n");
                sql2.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql2.append("WHERE tipooperacao = " + TipoOperacaoLancamento.PAGAMENTO.getCodigo() + " \n");
                sql2.append("AND mr.tipoes = " + TipoES.ENTRADA.getCodigo() + " \n");
                sql2.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql2.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update2 = "UPDATE movcontarateio SET tipoes = " + TipoES.SAIDA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update2, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir TRANSFERÊNCIAS incorretas financeiro
            try {
                StringBuilder sql3 = new StringBuilder();
                sql3.append("SELECT mr.codigo as codMovContaRateio \n");
                sql3.append("FROM movconta mc \n");
                sql3.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql3.append("WHERE tipooperacao = " + TipoOperacaoLancamento.TRANSFERENCIA.getCodigo() + " \n");
                sql3.append("AND mc.descricao ilike '%saída de valor%' \n");
                sql3.append("AND mr.tipoes = " + TipoES.ENTRADA.getCodigo() + " \n");
                sql3.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql3.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update3 = "UPDATE movcontarateio SET tipoes = " + TipoES.SAIDA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update3, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir TRANSFERÊNCIAS incorretas financeiro
            try {
                StringBuilder sql3 = new StringBuilder();
                sql3.append("SELECT mr.codigo as codMovContaRateio \n");
                sql3.append("FROM movconta mc \n");
                sql3.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql3.append("WHERE tipooperacao = " + TipoOperacaoLancamento.TRANSFERENCIA.getCodigo() + " \n");
                sql3.append("AND mc.descricao not ilike '%saída de valor%' \n");
                sql3.append("AND mr.tipoes = " + TipoES.SAIDA.getCodigo() + " \n");
                sql3.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql3.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update3 = "UPDATE movcontarateio SET tipoes = " + TipoES.ENTRADA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update3, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "15/01/2024",
            descricao = "Conciliação lançamento já recebido pelo zw",
            motivacao = "M2-240")
    public void migracaoVersao2074() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movcontatransactionpluggy ADD COLUMN pluggyJaRecebidoZw int DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movcontatransactionpluggy ALTER COLUMN movconta DROP NOT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE pluggyJaRecebidoZw (codigo serial NOT NULL," +
                    " movcontatransactionpluggy int, descricao text, dataoperacao timestamp, datavencimento timestamp, valor float);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "11/01/2024",
            descricao = "Nova coluna zeroDollar configuração nova convênio de cobrança",
            motivacao = "M2-627")
    public void migracaoVersao2075() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN verificacaoZeroDollar boolean NOT NULL DEFAULT false;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "22/01/2024",
            descricao = "Adicionar coluna usaConfiguracaoEmailManual",
            motivacao = "M1-749")
    public void migracaoVersao2076() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table configuracaosistemacrm add usaConfiguracaoEmailManual boolean default false;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "29/01/2024",
            descricao = "Período freepass que será usado no treino para registrar a situação temporaria do cliente",
            motivacao = "M1-172")
    public void migracaoVersao2077() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE situacaoclientesinteticodw ADD COLUMN freePassInicio TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE situacaoclientesinteticodw ADD COLUMN freePassFim TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }

    @Processo(autor = "Denis Silva",
            data = "30/01/2024",
            descricao = "inclusão da coluna camposadicionaisproduto na tabela vendasonlineconfig",
            motivacao = "GC-64")
    public void migracaoVersao2078() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.vendasonlineconfig ADD camposadicionaisproduto text NULL;", c);
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT \n"
                    + "codigo, camposadicionais \n"
                    + "FROM vendasonlineconfig ", c);
            while (rs.next()) {
                String sql = "UPDATE public.vendasonlineconfig \n" +
                        "SET  camposadicionaisproduto='" + rs.getString("camposadicionais") + "' \n" +
                        "WHERE codigo= " + rs.getInt("codigo") + "";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "26/01/2024",
            descricao = "Ajustando negociações Erradas",
            motivacao = "M1-246")
    public void migracaoVersao2079() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update historicocontrato set tipohistorico = 'RE', descricao= 'REMATRICULADO' where contrato in (select contratoresponsavelrenovacaomatricula from contrato c where contratoresponsavelrenovacaomatricula > 0 and  exists (select * from contratooperacao where contrato = c.codigo and tipooperacao = 'CA' )) and tipohistorico = 'RN';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato co set contratobaseadorematricula = contratobaseadorenovacao where codigo  in  (select contratoresponsavelrenovacaomatricula from contrato c where contratoresponsavelrenovacaomatricula > 0 and  exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA' ));", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato co set contratobaseadorenovacao = 0 where codigo  in  (select contratoresponsavelrenovacaomatricula from contrato c where contratoresponsavelrenovacaomatricula > 0 and  exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA' ));", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato c set contratoresponsavelrematriculamatricula  = contratoresponsavelrenovacaomatricula  where  contratoresponsavelrenovacaomatricula > 0 and  exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA');", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato c set contratoresponsavelrenovacaomatricula = 0  where  contratoresponsavelrenovacaomatricula > 0 and  exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA')", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "26/01/2024",
            descricao = "PinPadPedido - GetCard",
            motivacao = "PinPadPedido - GetCard")
    public void migracaoVersao2080() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pinpadpedido ADD COLUMN idExternoCancel CHARACTER VARYING(120);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pinpadpedido ADD COLUMN paramsRespCancel TEXT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pinpadpedido ADD COLUMN pinpadpedidoOrigem INTEGER;", c);
            PinPadPedido pinPadPedidoDAO = new PinPadPedido(c);
            pinPadPedidoDAO.ajustarPedidos();
            pinPadPedidoDAO = null;
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "13/01/2024",
            descricao = "voltar possibilidade de colocar o dia 31 como vencimento",
            motivacao = "M1-877")
    public void migracaoVersao2081() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update plano set diasvencimentoprorata = diasvencimentoprorata||',31'  where diasvencimentoprorata not like '%,31%' and CHAR_LENGTH(diasvencimentoprorata) > 65", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "06/02/2024",
            descricao = "Remover inicio_contrato, parq e dia_vencimento dos camposAdicionasProduto",
            motivacao = "GC-472")
    public void migracaoVersao2082() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT \n"
                    + "codigo, camposadicionaisproduto \n"
                    + "FROM vendasonlineconfig ", c);

            while (rs.next()) {
                String camposAdicionaisProduto = rs.getString("camposadicionaisproduto");
                if (!UteisValidacao.emptyString(camposAdicionaisProduto)) {
                    String camposAdicionaisProdutoAjustado = "";
                    for (String campo : camposAdicionaisProduto.split(";")) {
                        if (!campo.equals(CamposAdicionaisVendasOnlineEnum.INICIO_CONTRATO.name())
                                && !campo.equals(CamposAdicionaisVendasOnlineEnum.ParQ.name())
                                && !campo.equals(CamposAdicionaisVendasOnlineEnum.DIA_VENCIMENTO.name())
                        ) {
                            camposAdicionaisProdutoAjustado += ";" + campo;
                        }
                    }
                    camposAdicionaisProdutoAjustado = camposAdicionaisProdutoAjustado.replaceFirst(";", "");
                    String sql = "UPDATE public.vendasonlineconfig \n" +
                            "SET camposadicionaisproduto = '" + camposAdicionaisProdutoAjustado + "' \n" +
                            "WHERE codigo= " + rs.getInt("codigo");
                    SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                }
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "08/02/2024",
            descricao = "cria tabela para armazenar infomigracaohistorico",
            motivacao = "cria tabela para armazenar infomigracaohistorico")
    public void migracaoVersao2083() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table infomigracaohistorico( \n" +
                    "codigo serial primary key, \n" +
                    "dataregistro TIMESTAMP WITHOUT TIME zone, \n" +
                    "tipoinfo int not null, \n" +
                    "usuario int, \n" +
                    "origem character varying (5), \n" +
                    "info text);", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "02/02/2024",
            descricao = "Cria a o campo dataAlteracaoSituacao e a trigger para atualizar o campo",
            motivacao = "M1-723")
    public void migracaoVersao2084() throws Exception {

        StringBuilder sqltrigger = new StringBuilder();
        StringBuilder sql = new StringBuilder();
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movparcela ADD COLUMN dataAlteracaoSituacao TIMESTAMP DEFAULT CURRENT_DATE;", c);
            sqltrigger.append("CREATE OR REPLACE FUNCTION atualizar_data_situacao()\n" +
                    "RETURNS TRIGGER AS $$\n" +
                    "BEGIN\n" +
                    "    -- Verifica se houve alteração no campo situacao\n" +
                    "    IF NEW.situacao <> OLD.situacao THEN\n" +
                    "        -- Atualiza o campo datasituacao para a data atual\n" +
                    "        NEW.dataAlteracaoSituacao := CURRENT_DATE;\n" +
                    "    END IF;\n" +
                    "    \n" +
                    "    RETURN NEW;\n" +
                    "END;\n" +
                    "$$ LANGUAGE plpgsql;\n" +
                    "\n" +
                    "-- Cria a trigger para a tabela \n" +
                    "CREATE TRIGGER trigger_atualizar_data_situacao\n" +
                    "BEFORE UPDATE ON movparcela\n" +
                    "FOR EACH row EXECUTE PROCEDURE atualizar_data_situacao();");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqltrigger.toString(), c);
            sql.append("BEGIN;\n" +
                    "\n" +
                    "-- Primeiro UPDATE\n" +
                    "UPDATE movparcela\n" +
                    "SET dataAlteracaoSituacao = CASE \n" +
                    "                                WHEN dataAlteracaoManual IS NULL THEN dataregistro\n" +
                    "                                ELSE dataAlteracaoManual\n" +
                    "                            END\n" +
                    "WHERE situacao IN ('PG', 'EA', 'RG');\n" +
                    "\n" +
                    "-- Segundo UPDATE\n" +
                    "UPDATE movparcela\n" +
                    "SET dataAlteracaoSituacao = t.dataalteracao\n" +
                    "FROM (\n" +
                    "    SELECT * FROM log \n" +
                    "    WHERE nomeentidade = 'MOVPARCELA' AND operacao = 'CANCELAMENTO - PARCELA'\n" +
                    ") t\n" +
                    "WHERE CAST(t.chaveprimaria AS INT) = movparcela.codigo;\n" +
                    "\n" +
                    "-- Terceiro UPDATE\n" +
                    "UPDATE movparcela\n" +
                    "SET dataAlteracaoSituacao = c.vigenciaateajustada\n" +
                    "FROM contrato c\n" +
                    "WHERE c.codigo = movparcela.contrato AND movparcela.dataalteracaosituacao = '2024-02-02 00:00:00.000';\n" +
                    "\n" +
                    "COMMIT;\n");

            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "25/01/2024",
            descricao = "Integração Pix Banco Inter",
            motivacao = "M2-328")
    public void migracaoVersao2085() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.token (codigo SERIAL NOT NULL,\n" +
                    "data_gerado TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "access_token TEXT,\n" +
                    "token_type TEXT,\n" +
                    "expires_in BIGINT,\n" +
                    "scope TEXT, \n" +
                    "vezes_utilizado INT, \n" +
                    "tipotokenenum INT);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "14/02/2024",
            descricao = "Correção objetos movcontarateio gravados com tipo de entrada e saída incorretos no banco",
            motivacao = "M2-991")
    public void migracaoVersao2086() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            //Corrigir ENTRADAS incorretas financeiro
            try {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT mr.codigo as codMovContaRateio \n");
                sql.append("FROM movconta mc \n");
                sql.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql.append("WHERE tipooperacao = " + TipoOperacaoLancamento.DEPOSITO.getCodigo() + " \n");
                sql.append("AND mr.tipoes = " + TipoES.SAIDA.getCodigo() + " \n");
                sql.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update = "UPDATE movcontarateio SET tipoes = " + TipoES.ENTRADA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir SAÍDAS incorretas financeiro
            try {
                StringBuilder sql2 = new StringBuilder();
                sql2.append("SELECT mr.codigo as codMovContaRateio \n");
                sql2.append("FROM movconta mc \n");
                sql2.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql2.append("WHERE tipooperacao = " + TipoOperacaoLancamento.PAGAMENTO.getCodigo() + " \n");
                sql2.append("AND mr.tipoes = " + TipoES.ENTRADA.getCodigo() + " \n");
                sql2.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql2.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update2 = "UPDATE movcontarateio SET tipoes = " + TipoES.SAIDA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update2, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir TRANSFERÊNCIAS SAÍDA incorretas financeiro
            try {
                StringBuilder sql3 = new StringBuilder();
                sql3.append("SELECT mr.codigo as codMovContaRateio \n");
                sql3.append("FROM movconta mc \n");
                sql3.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql3.append("WHERE tipooperacao = " + TipoOperacaoLancamento.TRANSFERENCIA.getCodigo() + " \n");
                sql3.append("AND mc.descricao ilike '%saída de valor%' \n");
                sql3.append("AND mr.tipoes = " + TipoES.ENTRADA.getCodigo() + " \n");
                sql3.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql3.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update3 = "UPDATE movcontarateio SET tipoes = " + TipoES.SAIDA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update3, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir TRANSFERÊNCIAS ENTRADA incorretas financeiro
            try {
                StringBuilder sql4 = new StringBuilder();
                sql4.append("SELECT mr.codigo as codMovContaRateio \n");
                sql4.append("FROM movconta mc \n");
                sql4.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql4.append("WHERE tipooperacao = " + TipoOperacaoLancamento.TRANSFERENCIA.getCodigo() + " \n");
                sql4.append("AND mc.descricao = 'TRANSFERENCIA ENTRE CONTAS' \n");
                sql4.append("AND mr.tipoes = " + TipoES.SAIDA.getCodigo() + " \n");
                sql4.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql4.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update4 = "UPDATE movcontarateio SET tipoes = " + TipoES.ENTRADA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update4, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "04/12/2023",
            descricao = "Adiciona validações para assinatura do termo de aceite e nova coluna de email",
            motivacao = "APPS-423")
    public void migracaoVersao2087() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM termoaceiteassinatura", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura ADD COLUMN email varchar(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura ALTER COLUMN termoaceite SET NOT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura\n" +
                    "    ADD CONSTRAINT unique_cpf_per_termoaceite\n" +
                    "        UNIQUE (cpf, termoaceite);", c);
        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "11/01/2024",
            descricao = "Add coluna statusConciliacaoRedeOnline para validacao em Processo Conciliacao",
            motivacao = "M2-558")
    public void migracaoVersao2088() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN statusConciliacaoRedeOnline int NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN requestIdConciliacaoRedeOnline varchar(50) NULL;", c);
        }
    }


    @Processo(autor = "Joao Alcides",
            data = "08/02/2024",
            descricao = "cria tabela para armazenar aluno fixo em aula coletiva",
            motivacao = "cria tabela para armazenar aluno fixo em aula coletiva")
    public void migracaoVersao2089() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table alunofixoaula( \n" +
                    "codigo serial primary key, \n" +
                    "dataregistro TIMESTAMP WITHOUT TIME zone, \n" +
                    "datafinal TIMESTAMP WITHOUT TIME zone, \n" +
                    "dataremovido TIMESTAMP WITHOUT TIME zone, \n" +
                    "horarioturma int constraint alunofixoaula_horarioturma_fk references public.horarioturma ON DELETE CASCADE, \n" +
                    "cliente int constraint alunofixoaula_cliente_fk references public.cliente ON DELETE CASCADE, \n" +
                    "usuario int constraint alunofixoaula_usuario_fk references public.usuario ON DELETE CASCADE, \n" +
                    "usuarioremoveu int constraint alunofixoaula_usuarioremoveu_fk references public.usuario ON DELETE CASCADE, \n" +
                    "origem character varying (5), \n" +
                    "tipo int);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_alunofixoaula_cliente_codigo ON public.alunofixoaula USING btree (cliente);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_alunofixoaula_horarioturma_codigo ON public.alunofixoaula USING btree (horarioturma);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE alunohorarioturma ADD COLUMN alunofixoaula INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE turma ADD COLUMN permitefixar boolean default false;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "16/02/2024",
            descricao = "M1-999 - Estorno de produto personal quando houve assinatura",
            motivacao = "M1-999 - Estorno de produto personal quando houve assinatura")
    public void migracaoVersao2090() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planopersonalassinaturadigital DROP CONSTRAINT planopersonalassinaturadigital_planopersonal_fk;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planopersonalassinaturadigital ADD CONSTRAINT planopersonalassinaturadigital_planopersonal_fk FOREIGN KEY (taxapersonal) REFERENCES controletaxapersonal(codigo) ON DELETE CASCADE;", c);
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "26/01/2024",
            descricao = "Criando campos de distancia de identificação para servidor facial",
            motivacao = "E5-37")
    public void migracaoVersao2091() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE servidorfacial ADD COLUMN distanciaIdentificacao INTEGER;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "16/12/2023",
            descricao = "Integração com Ceopag",
            motivacao = "Integração com Ceopag")
    public void migracaoVersao2092() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operadoracartao ADD COLUMN codigoIntegracaoCeopag Integer;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "19/02/2024",
            descricao = "Ajustar modelos de contratos vazio em planos",
            motivacao = "GC-495")
    public void migracaoVersao2093() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            try {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT ptp.descricao as modelo, ptp.codigo as codigoModelo, p.codigo as codigoPlano \n");
                sql.append("FROM plano p \n");
                sql.append("INNER JOIN planotextopadrao ptp on ptp.codigo = p.planotextopadrao \n");
                sql.append("WHERE ((ptp.texto is null) or (ptp.texto = '')) \n");

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql.toString());
                List<Integer> modelosVazios = new ArrayList<>();
                while (rs.next()) {
                    try {
                        modelosVazios.add(rs.getInt("codigoModelo"));
                        StringBuilder update = new StringBuilder();
                        update.append("UPDATE plano SET planotextopadrao = \n");
                        update.append("(SELECT codigo FROM planotextopadrao where descricao ILIKE '%" + rs.getString("modelo") + "%' and texto is not null and texto <> '' LIMIT 1) \n");
                        update.append("WHERE codigo = " + rs.getInt("codigoPlano"));
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update.toString(), c);
                    } catch (Exception ex) {
                    }
                }

                if (!modelosVazios.isEmpty()) {
                    Iterator i = modelosVazios.iterator();
                    while (i.hasNext()) {
                        SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM planotextopadrao where codigo = " + i.next(), c);
                    }
                }
            } catch (Exception ex) {
            }
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "15/02/2024",
            descricao = "Acrescentando coluna para que o contrato seja renovado com o mesmo valor que foi negociado",
            motivacao = "M1-654")
    public void migracaoVersao2094() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN renovarAutomaticamenteUtilizandoValorBaseContrato BOOLEAN DEFAULT FALSE;", c);
        }
    }


    @Processo(autor = "Athos Feitosa",
            data = "29/01/2024",
            descricao = "Nova configuração para ativar/desativar configuração SESI - CE",
            motivacao = "GC-401")
    public void migracaoVersao2095() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN sesiCe BOOLEAN DEFAULT false;", c);
        }
    }

    @Processo(autor = "Athos Feitosa", data = "31/01/2024", descricao = "Novos campos no perfil do aluno", motivacao = "GC-451")
    public void migracaoVersao2096() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN necessidadesEspeciaisSesiCe VARCHAR(255), ADD COLUMN dataValidadeCadastroSesiCe DATE, ADD COLUMN razaoSocialEmpresaSesiCe VARCHAR(255), ADD COLUMN statusMatriculaSesiCe VARCHAR(255);", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "21/02/2024",
            descricao = "Criação da coluna diasParaVencimentoParq",
            motivacao = "GC-420")
    public void migracaoVersao2097() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN diasParaVencimentoParq Integer default 0;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "07/03/2024",
            descricao = "Permissão Pessoas",
            motivacao = "E2-857")
    public void migracaoVersao2098() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            Hashtable<String, OpcaoPerfilAcesso> mapa = OpcoesPerfilAcesso.inicializarPessoas();
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso from permissao where nomeentidade ilike 'Cliente'", c);
            while (rs.next()) {
                Integer codperfilacesso = rs.getInt("codperfilacesso");
                for (String key : mapa.keySet()) {
                    OpcaoPerfilAcesso opcaoPerfilAcesso = mapa.get(key);
                    if (opcaoPerfilAcesso.getTitulo().startsWith("13.")) {
                        boolean existe = SuperFacadeJDBC.existe("select codperfilacesso from permissao where codperfilacesso = " + codperfilacesso + " and nomeentidade ilike '" + opcaoPerfilAcesso.getNome() + "'", c);
                        if (existe) {
                            continue;
                        }
                        String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                                + " VALUES (" + opcaoPerfilAcesso.getTipo() + ", '" + opcaoPerfilAcesso.getTitulo() + "', '(0)(1)(2)(3)(9)(12)', '" + opcaoPerfilAcesso.getNome() + "', " + codperfilacesso + ")";
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                    }
                }
            }
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "26/02/2024",
            descricao = "Criando tabela para vincular assinatura digital do cancelamento de contrato",
            motivacao = "GC-471")
    public void migracaoVersao2099() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contrato ADD COLUMN dataassinaturacancelamento timestamp NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.cancelamentoassinaturadigital(\n" +
                            "\tcodigo serial4 NOT NULL,\n" +
                            "contrato int constraint codigo_contrato_fk references public.contrato ON DELETE CASCADE, \n" +
                            "contratooperacao int constraint codigo_contratooperacao_fk references public.contratooperacao ON DELETE CASCADE, \n" +
                            "usuarioresponsavel int constraint codigo_usuario_fk references public.usuario ON DELETE CASCADE, \n" +
                            "\tcodigoempresa int4 NULL,\n" +
                            "\tassinatura text NULL,\n" +
                            "\tlancamento timestamp NULL,\n" +
                            "\tCONSTRAINT cancelamentoassinaturadigital_pk PRIMARY KEY (codigo));", c);
        }
    }


    @Processo(autor = "Marcos Andre",
            data = "08/03/2024",
            descricao = "Ajustando cadastro errado de cidade",
            motivacao = "M1-1149")
    public void migracaoVersao2100() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update pessoa set cidade = 27, estado = 27 where cidade = 11 and exists(select codigo from cidade where codigo = 11 and nome = 'CAMPO GRANDE' and estado = 5 ) " +
                    " and exists(select codigo from cidade where codigo = 27 and nome = 'CAMPO GRANDE' and estado = 27 );", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("delete from cidade where codigo = 11 and nome = 'CAMPO GRANDE'  and estado = 5;", c);


        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "01/03/2024",
            descricao = "Criação de nova configuração de agendamento aula link de visitante no vendas online",
            motivacao = "GC-57")
    public void migracaoVersao2101() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN habilitarAgendamentoAulaExperimentalLinkVisitante BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN tipoAulasLinkVisitante Integer DEFAULT 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN utilizouAulaExprimentalVendasOnline BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE aulavendasonlinelinkvisitante (codigo serial not null primary key,\n" +
                    "datalancamento timestamp without time zone, \n" +
                    "dataexclusao timestamp without time zone, \n" +
                    "turma int constraint aulavendasonlinelinkvisitante_turma_fk references public.turma ON DELETE CASCADE, \n" +
                    "modalidade int constraint aulavendasonlinelinkvisitante_modalidade_fk references public.modalidade ON DELETE CASCADE, \n" +
                    "vendasonlineconfig int constraint aulavendasonlinelinkvisitante_vendasonlineconfig_fk references public.vendasonlineconfig ON DELETE CASCADE)", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "22/02/2024",
            descricao = "Ajustado tamanho dos campos de estacionamento",
            motivacao = "E5-37")
    public void migracaoVersao2102() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresaconfigestacionamento ALTER COLUMN ftpuser TYPE VARCHAR(50);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresaconfigestacionamento ALTER COLUMN ftppass TYPE VARCHAR(50);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "19/01/2023",
            descricao = "Adicionando uma forma mais fácil de ver as solicitações aos serviços de importação, via api",
            motivacao = "M2-754")
    public void migracaoVersao2103() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            //Corrigir ENTRADAS incorretas financeiro
            try {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT mr.codigo as codMovContaRateio \n");
                sql.append("FROM movconta mc \n");
                sql.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql.append("WHERE tipooperacao = " + TipoOperacaoLancamento.DEPOSITO.getCodigo() + " \n");
                sql.append("AND mr.tipoes = " + TipoES.SAIDA.getCodigo() + " \n");
                sql.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update = "UPDATE movcontarateio SET tipoes = " + TipoES.ENTRADA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir SAÍDAS incorretas financeiro
            try {
                StringBuilder sql2 = new StringBuilder();
                sql2.append("SELECT mr.codigo as codMovContaRateio \n");
                sql2.append("FROM movconta mc \n");
                sql2.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql2.append("WHERE tipooperacao = " + TipoOperacaoLancamento.PAGAMENTO.getCodigo() + " \n");
                sql2.append("AND mr.tipoes = " + TipoES.ENTRADA.getCodigo() + " \n");
                sql2.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql2.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update2 = "UPDATE movcontarateio SET tipoes = " + TipoES.SAIDA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update2, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir TRANSFERÊNCIAS incorretas financeiro
            try {
                StringBuilder sql3 = new StringBuilder();
                sql3.append("SELECT mr.codigo as codMovContaRateio \n");
                sql3.append("FROM movconta mc \n");
                sql3.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql3.append("WHERE tipooperacao = " + TipoOperacaoLancamento.TRANSFERENCIA.getCodigo() + " \n");
                sql3.append("AND mc.descricao ilike '%saída de valor%' \n");
                sql3.append("AND mr.tipoes = " + TipoES.ENTRADA.getCodigo() + " \n");
                sql3.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql3.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update3 = "UPDATE movcontarateio SET tipoes = " + TipoES.SAIDA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update3, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }

            //Corrigir TRANSFERÊNCIAS incorretas financeiro
            try {
                StringBuilder sql4 = new StringBuilder();
                sql4.append("SELECT mr.codigo as codMovContaRateio \n");
                sql4.append("FROM movconta mc \n");
                sql4.append("INNER JOIN movcontarateio mr ON mc.codigo = mr.movconta \n");
                sql4.append("WHERE tipooperacao = " + TipoOperacaoLancamento.TRANSFERENCIA.getCodigo() + " \n");
                sql4.append("AND mc.descricao not ilike '%saída de valor%' \n");
                sql4.append("AND mc.descricao not ilike '%Pagamento de parcela de cheque devolvido%' \n");
                sql4.append("AND mr.tipoes = " + TipoES.SAIDA.getCodigo() + " \n");
                sql4.append("AND mc.datalancamento::date >= '2023-11-01' \n"); //lançamento a partir de novembro

                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery(sql4.toString());
                while (rs.next()) {
                    try {
                        int codMovContaRateio = rs.getInt("codMovContaRateio");
                        String update4 = "UPDATE movcontarateio SET tipoes = " + TipoES.ENTRADA.getCodigo() + " WHERE codigo = " + codMovContaRateio;
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(update4, c);
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
            }
        }
    }

    @Processo(autor = "Denis Silva",
            data = "06/03/2024",
            descricao = "Preparando tabela para migração SESI-CE",
            motivacao = "GC-62")
    public void migracaoVersao2104() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.modalidade ALTER COLUMN nome TYPE varchar(100) USING nome::varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.modalidade ADD idexterno varchar NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.turma ADD idexterno varchar NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.turma ALTER COLUMN descricao TYPE varchar(200) USING descricao::varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.turma ALTER COLUMN identificador TYPE varchar(200) USING identificador::varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.contrato ADD xnumpro varchar(20) NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.horarioturma ALTER COLUMN identificadorturma TYPE varchar(250) USING identificadorturma::varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.modalidade ADD idexternoturma varchar NULL;", c);
        }
    }

    @Processo(autor = "Denis Silva",
            data = "06/03/2024",
            descricao = "GC-411 - Conciliação automática de recebiveis",
            motivacao = "GC-411 - Conciliação automática de recebiveis")
    public void migracaoVersao2105() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD descricaomovimentacaoautomaticadebito varchar(100) NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD descricaomovimentacaoautomaticacredito varchar(100) NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD contamovimentacaoautomaticadebito int4 NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD contamovimentacaoautomaticacredito int4 NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD favorecidomovimentacaoautomaticadebito int4 NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD favorecidomovimentacaoautomaticacredito int4 NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD conciliacaoautomaticarecebiveis boolean NULL DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD CONSTRAINT configuracaofinanceiro_contamovimentacaoautomaticadebito_fk FOREIGN KEY (contamovimentacaoautomaticadebito) REFERENCES public.conta(codigo);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD CONSTRAINT configuracaofinanceiro_contamovimentacaoautomaticacredito_fk FOREIGN KEY (contamovimentacaoautomaticacredito) REFERENCES public.conta(codigo);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD CONSTRAINT configuracaofinanceiro_favorecidomovimentacaoautomaticadebito_fk FOREIGN KEY (favorecidomovimentacaoautomaticadebito) REFERENCES public.pessoa(codigo);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD CONSTRAINT configuracaofinanceiro_favorecidomovimentacaoautomaticacreditofk FOREIGN KEY (favorecidomovimentacaoautomaticacredito) REFERENCES public.pessoa(codigo);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaofinanceiro_contamovimentacaoautomaticadebito_idx ON public.configuracaofinanceiro (contamovimentacaoautomaticadebito);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaofinanceiro_contamovimentacaoautomaticacredito_idx ON public.configuracaofinanceiro (contamovimentacaoautomaticacredito);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaofinanceiro_favorecidomovimentacaoautomaticadebito_idx ON public.configuracaofinanceiro (favorecidomovimentacaoautomaticadebito);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX configuracaofinanceiro_favorecidomovimentacaoautomaticacredito_idx ON public.configuracaofinanceiro (favorecidomovimentacaoautomaticacredito);", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "15/03/2024",
            descricao = "Setando tipo de frequencia para 1 quando quantidade maxima de frequencia for maior que 0, pois não estava sendo validada a quantidade de acessos semanais dos alunos",
            motivacao = "M1-1294")
    public void migracaoVersao2106() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE plano SET tipofrequencia = 1 WHERE quantidademaximafrequencia IS NOT NULL AND quantidademaximafrequencia > 0;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "20/03/2024",
            descricao = "Acrescentando informação para bloquear matriculas em turma que com possibilidade de lotação futura",
            motivacao = "M1-1271")
    public void migracaoVersao2107() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE turma ADD COLUMN  bloquearLotacaoFutura BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "03/03/2024",
            descricao = "Conciliação PagoLivre",
            motivacao = "Conciliação PagoLivre")
    public void migracaoVersao2108() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem ADD COLUMN observacao text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem ALTER COLUMN estabelecimento TYPE VARCHAR(200);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem ALTER COLUMN nsu TYPE VARCHAR(200);", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "02/03/2024",
            descricao = "Integração members EVO - config. sincronização pós importação",
            motivacao = "E5-37")
    public void migracaoVersao2109() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN consultorPadrao integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN professorPadrao integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN planoPadrao integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN modalidadePadrao integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN horarioPadrao integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN diasCarencia integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN dddPadrao varchar(2)", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN dataInicialConsiderarLancamentos date", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE IntegracaoMember ADD COLUMN memberFreePass boolean default true", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaavulsadiaria ADD COLUMN id_venda integer", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaavulsadiaria ADD COLUMN idExternoItemVenda integer", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movparcela ADD COLUMN idexterno integer", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ALTER COLUMN correspondencia_zd TYPE text", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "07/03/2024",
            descricao = "Criação da coluna bloquearAcessoSemTermoResponsabilidade para impedir que alunos acessem a academia sem aceitar o termo de responsabilidade",
            motivacao = "M1-1187")
    public void migracaoVersao2110() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN bloquearAcessoSemTermoResponsabilidade BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "26/03/2024",
            descricao = "Retirando a parte 7 dias da mensagem de dos clientes da academia",
            motivacao = "M1-1417")
    public void migracaoVersao2111() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE clientemensagem SET mensagem = REPLACE (mensagem, '- 7 dias ', '') WHERE tipomensagem = 'ES'", c);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "22/03/2024",
            descricao = "E2-771",
            motivacao = "E2-771")
    public void migracaoVersao2112() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE Turma ADD COLUMN niveis text;", c);
        }
    }

    @Processo(autor = "Denis Silva",
            data = "25/03/2024",
            descricao = "Incluindo coluna para identficar conciliação já processada",
            motivacao = "GC-578 - Conciliação automática de recebiveis")
    public void migracaoVersao2113() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta ADD movcontaautomaticaprocessado varchar(255) NULL;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "01/04/2024",
            descricao = "Importação Token",
            motivacao = "Importação Token")
    public void migracaoVersao2114() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN tokenPagoLivre VARCHAR(120);", c);
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "15/03/2024",
            descricao = "Adiciona validações para assinatura do termo de aceite de acordo com jurídico",
            motivacao = "APPS-764")
    public void migracaoVersao2115() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM termoaceiteassinatura", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura ADD COLUMN codigomatricula INT NOT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura ADD CONSTRAINT uk_codigomatricula_termoaceite UNIQUE (codigomatricula, termoaceite);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura ALTER COLUMN cpf DROP NOT NULL", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE termoaceiteassinatura DROP CONSTRAINT unique_cpf_per_termoaceite;", c);
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "28/03/2024",
            descricao = "Processo para corrigir aulas desmaracadas de contratos anteriores que não foram para os contratos atuais dos alunos",
            motivacao = "M1-1446")
    public void migracaoVersao2116() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustaAulaDesmarcadaContratoAnterior.corrigirAulasDesmarcadasDeContratosAnteriores(c);
        }
    }

    @Processo(autor = "Athos Feitosa",
            data = "27/03/2024",
            descricao = "Criação da coluna qtddiaprimeiraparcelavencidaestornarcontrato_origemvendasonline para criar nova config semelhante que valide apenas contratos de origem Vendas 2.0",
            motivacao = "GC-583")
    public void migracaoVersao2117() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN qtddiaprimeiraparcelavencidaestornarcontrato_origemvendasonline INT DEFAULT 0;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "07/03/2024",
            descricao = "Aba Notas Fiscais",
            motivacao = "E2-831")
    public void migracaoVersao2118() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update permissao set nomeentidade = 'AlunoAbaNotaFiscal', " +
                    "tituloapresentacao = '13.10 - Ver aba Nota Fiscal' " +
                    "where nomeentidade ilike 'MostrarAbaNotaFiscaisPerfilAluno';", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "15/03/2024",
            descricao = "Correções para que o sistema permita que o contrato normal com índice seja renovado se baseando no valor base do contrato anterior, e não se baseando no valor atual do plano",
            motivacao = "M1-780")
    public void migracaoVersao2119() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contrato ADD COLUMN valorBaseNegociado float4 DEFAULT 0.0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE contrato SET valorBaseNegociado = valorbasecalculo;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratorecorrencia ADD COLUMN valorMensalNegociado float4 DEFAULT 0.0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE contratorecorrencia SET valorMensalNegociado = valormensal;", c);
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "28/03/2024",
            descricao = "Processo para corrigir sintético de alunos com coluna codigoContrato diferente do contrato vigente atual, e também descontar créditos que não foram descontado anteriormente",
            motivacao = "M1-1447")
    public void migracaoVersao2120() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            //ProcessarAcessoContratosCredito.executarProcesso(c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "04/04/2024",
            descricao = "Excluindo coluna situacao da tabela acessocolaborador, criada de forma errada pelo relatorio-ms",
            motivacao = "M1-1509")
    public void migracaoVersao2121() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE acessocolaborador DROP COLUMN IF EXISTS situacao;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "02/04/2024",
            descricao = "Corrigir estado da cidade de cruzeiro",
            motivacao = "M1-1396")
    public void migracaoVersao2122() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update cidade set estado = (select codigo from estado where sigla = 'SP') where nome ilike 'CRUZEIRO';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update pessoa set estado = (select codigo from estado where sigla = 'SP') where cidade in (select codigo from cidade where nome ilike 'CRUZEIRO');", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "23/03/2024",
            descricao = "FacilitePay - Stone Connect",
            motivacao = "FacilitePay - Stone Connect")
    public void migracaoVersao2123() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayStoneConnect BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set facilitePayStoneConnect = exists(\n" +
                    "select cc.codigo \n" +
                    "from conveniocobranca cc\n" +
                    "inner join conveniocobrancaempresa cce on cce.conveniocobranca = cc.codigo\n" +
                    "where cc.tipoconvenio = 36 and cce.empresa = empresa.codigo);", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "09/04/2024",
            descricao = "Cliente observação",
            motivacao = "Cliente observação")
    public void migracaoVersao2124() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE clienteobservacao ADD COLUMN importante BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE clienteobservacao ADD COLUMN dataAlteracao timestamp without time zone;", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "05/03/2024",
            descricao = "Cria o campo grupoInativo na tabela Grupo e define o valor padrão para false",
            motivacao = "M1-8")
    public void migracaoVersao2125() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE grupo ADD COLUMN grupoInativo BOOLEAN DEFAULT false;", c);
        }
    }


    @Processo(autor = "Matheus Cassimiro",
            data = "09/04/2024",
            descricao = "Criando permissão para relatório de frequencia e ocupação de turmas",
            motivacao = "M1-1546")
    public void migracaoVersao2126() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO permissao(tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso, unificado, sistema)\n" +
                    "SELECT tipopermissao, '9.73 - Relatório Frequêcia e Ocupação de Turmas', '(0)(1)(2)(3)(9)(12)', 'FrequenciaOcupacaoTurmasRel', codperfilacesso, unificado, sistema FROM permissao p WHERE tituloapresentacao ILIKE '2.43%'", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "05/04/2024",
            descricao = "Processo para excluir emails duplicados por pessoa no sistema",
            motivacao = "M1-1493")
    public void migracaoVersao2127() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            AjustarEmailsDuplicadosPorPessoa.corrigirEmailsDuplicadosPorPessoa(c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "09/04/2024",
            descricao = "Criando permissão para relatório de frequencia e ocupação de turmas",
            motivacao = "M1-1546")
    public void migracaoVersao2128() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE permissao SET tituloapresentacao = '9.73 - Relatório Frequência e Ocupação de Turmas' WHERE tituloapresentacao ILIKE '9.73%'", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "10/12/2023",
            descricao = "FacilitePay",
            motivacao = "FacilitePay")
    public void migracaoVersao2129() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoCartaoWhatsApp BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoAtrasoWhatsApp BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoResultadoCobrancaWhatsApp BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoCartaoApp BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoAtrasoApp BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoResultadoCobrancaApp BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE PactoPayCobrancaAntecipada ADD COLUMN meioEnvio integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE PactoPayCobrancaAntecipada SET meioEnvio = " + MeioEnvio.EMAIL.getCodigo() + " WHERE coalesce(meioEnvio,0) = 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayCDLSPC BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set facilitePayCDLSPC = (select coalesce(codigoAssociadoSPC,0) > 0 from empresa e where e.codigo = empresa.codigo ) where facilitePayCDLSPC = false;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "22/04/2024",
            descricao = "Correção dia semana numero nulo nos horarioturma",
            motivacao = "M1-1666")
    public void migracaoVersao2130() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE horarioturma SET diasemananumero = 2 WHERE diasemana = 'SG' AND diasemananumero IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE horarioturma SET diasemananumero = 3 WHERE diasemana = 'TR' AND diasemananumero IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE horarioturma SET diasemananumero = 4 WHERE diasemana = 'QA' AND diasemananumero IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE horarioturma SET diasemananumero = 5 WHERE diasemana = 'QI' AND diasemananumero IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE horarioturma SET diasemananumero = 6 WHERE diasemana = 'SX' AND diasemananumero IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE horarioturma SET diasemananumero = 7 WHERE diasemana = 'SB' AND diasemananumero IS NULL;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "24/04/2024",
            descricao = "Recurso Padrao - Venda Avulsa",
            motivacao = "Recurso Padrao - Venda Avulsa")
    public void migracaoVersao2131() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("insert into infomigracao(tipoinfo, usuario, info, origem) " +
                    "select " + TipoInfoMigracaoEnum.VENDA_AVULSA.getId() + ",codigo,'true','ZW' from usuario " +
                    "where codigo not in (select usuario from infomigracao i  where tipoinfo = " + TipoInfoMigracaoEnum.VENDA_AVULSA.getId() + " )", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "24/04/2024",
            descricao = "Reaproveitamento de token pix inter por convênio de cobrança",
            motivacao = "M2-1581")
    public void migracaoVersao2132() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE token ADD COLUMN conveniocobranca INT DEFAULT NULL;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "26/04/2024",
            descricao = "Recurso Padrão",
            motivacao = "Recurso Padrão")
    public void migracaoVersao2133() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN tiposInfoMigracaoPadrao TEXT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM infomigracaohistorico where tipoinfo in (" + TipoInfoMigracaoEnum.obterCodigos(false) + ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.tabelagenericaenum (\n" +
                    "\tnomeenum character varying,\n" +
                    "\tcodigo character varying,\n" +
                    "\tdescricao text\n" +
                    ");", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "01/05/2024",
            descricao = "Recurso Padrão",
            motivacao = "Recurso Padrão")
    public void migracaoVersao2134() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE infomigracaohistorico ADD COLUMN usuario_ativo BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update infomigracaohistorico set usuario_ativo = (select exists(\n" +
                    "select co.codigo \n" +
                    "from usuario u \n" +
                    "inner join colaborador co on co.codigo = u.colaborador\n" +
                    "where co.situacao = 'AT' \n" +
                    "and u.codigo = infomigracaohistorico.usuario)) where usuario_ativo is null;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "02/05/2024",
            descricao = "Bi - FacilitePay",
            motivacao = "Bi - FacilitePay")
    public void migracaoVersao2135() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE origemcobranca ADD COLUMN reguacobranca BOOLEAN;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "03/05/2024",
            descricao = "Recurso Padrão",
            motivacao = "Recurso Padrão")
    public void migracaoVersao2136() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update infomigracaohistorico set usuario_ativo = null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update infomigracaohistorico set usuario_ativo = (\n" +
                    "select exists(\n" +
                    "select \n" +
                    "u1.codigo\n" +
                    "from usuario u1 \n" +
                    "inner join colaborador c1 on c1.codigo = u1.colaborador \n" +
                    "inner join pessoa p1 on p1.codigo = c1.pessoa \n" +
                    "where exists(select c2.codigo from colaborador c2 where c2.situacao = 'AT' and c2.pessoa = p1.codigo)\n" +
                    "and u1.codigo = infomigracaohistorico.usuario)\n" +
                    ") where usuario_ativo is null;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "01/05/2024",
            descricao = "M1-1748 - Remove colunas que foram criadas de forma errada pelo produto-ms",
            motivacao = "M1-1748 - Remove colunas que foram criadas de forma errada pelo produto-ms")
    public void migracaoVersao2137() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto DROP COLUMN IF EXISTS posicaoestoque", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto DROP COLUMN IF EXISTS selecionado", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "07/05/2024",
            descricao = "Recurso Padrão",
            motivacao = "Recurso Padrão")
    public void migracaoVersao2138() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN tiposInfoMigracaoPadrao TEXT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_logconciliadora_movpagamento ON public.logconciliadora USING btree (movpagamento);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "13/05/2024",
            descricao = "Configuração enviar email usuário movel",
            motivacao = "M2-1710")
    public void migracaoVersao2139() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN enviarEmailUsuarioMovelAutomaticamente BOOLEAN DEFAULT TRUE;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "14/05/2024",
            descricao = "Adicionando informações do retorno da solicitação do mywellness",
            motivacao = "Verificar as integrações realizadas separadamente")
    public void migracaoVersao2140() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table integracaomywellness add column retorno text;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "14/05/2024",
            descricao = "Refatoração de campos do projeto movimentação automática da conciliação",
            motivacao = "M2-1701")
    public void migracaoVersao2141() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro RENAME COLUMN conciliacaoautomaticarecebiveis TO movimentacaoAutomaticaRecebiveisConciliacao;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta RENAME COLUMN movcontaautomaticaprocessado TO infoMovimentacaoAutomaticaConciliacao;", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "19/05/2024",
            descricao = "Adicionar a possibilidade de bloquear a alteração da data da primeira parcelas na negociação",
            motivacao = "M1-1880")
    public void migracaoVersao2142() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    // a condição de validar se tem algum permissão já  cadastrada abaixo,
                    // garante que esse processo só vai adicionar a permissão na primeira vez que rodar.
                    // importante avaliar isso, pois processo pode rodar mais de uma vez em algum banco
                    + "FROM perfilacesso p where not exists( select nomeentidade from permissao where nomeentidade = 'dataPrimeiraParcela') ", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '4.47 - Alterar data da primeira parcela na negociação','(0)(1)(2)(3)(9)(12)', "
                        + " 'dataPrimeiraParcela', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "21/05/2024",
            descricao = "Configuração para buscar todos fornecedores independente da unidade",
            motivacao = "M2-1746")
    public void migracaoVersao2143() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro ADD COLUMN buscarFornecedorTodasUnidades BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "04/04/2024",
            descricao = "Adicionar coluna direcionaragendamentosexperimentaisagenda",
            motivacao = "GC-512")
    public void migracaoVersao2144() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "alter table configuracaosistemacrm add direcionaragendamentosexperimentaisagenda boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE alunohorarioturma ADD COLUMN passivo INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaconfirmada ADD COLUMN passivo INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE reposicao ADD COLUMN passivo INTEGER;", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "10/04/2024",
            descricao = "Criação de nova configuração conciliação de contas a pagar/receber",
            motivacao = "M2-1409")
    public void migracaoVersao2145() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE pluggyAccountBloqueio (codigo serial not null primary key,\n" +
                    "id text, \n" +
                    "pluggyitem text)", c);
        }
    }

    @Processo(autor = "Athos Feitosa",
            data = "23/04/2024",
            descricao = "Nova configuração para ativar/desativar lançamentos de multa",
            motivacao = "GC-11")
    public void migracaoVersao2146() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa\n" +
                    "ADD COLUMN aplicarmultamudancaplano BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "08/05/2024",
            descricao = "Nova configuração para ativar/desativar integracao AZURE AD",
            motivacao = "GC-401")
    public void migracaoVersao2147() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN loginatravesazuread BOOLEAN DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN azureadtenatid varchar(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN azureadclientid varchar(255);", c);
        }
    }

    @Processo(autor = "Marcos Andre",
            data = "24/11/2023",
            descricao = "Download de Notas",
            motivacao = "IN-465")
    public void migracaoVersao2148() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE solicitacao (codigo serial not null constraint solicitacao_pk primary key, tipo int, status int, empresa int,resultado text, dadossolicitacao text, usuariosolicitante int, datalancamento TIMESTAMP WITHOUT TIME ZONE,  dataprocessamento TIMESTAMP WITHOUT TIME ZONE)", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "05/06/2024",
            descricao = "Criar indice coluna xnumpro contrato",
            motivacao = "GC-739")
    public void migracaoVersao2149() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_contrato_xnumpro ON contrato USING btree (xnumpro);", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "05/06/2024",
            descricao = "Nova coluna para guardar nome empresa cliente acessou integracao",
            motivacao = "GC-683")
    public void migracaoVersao2150() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE acessocliente ADD COLUMN nomeCodEmpresaAcessou varchar(255);", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "01/05/2024",
            descricao = "Configuração FacilitePay",
            motivacao = "E2-999")
    public void migracaoVersao2151() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN dataReguaCobrancaV2 TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE configuracaosistema SET dataReguaCobrancaV2 = NOW() WHERE dataReguaCobrancaV2 IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_pessoa ON public.pactopaycomunicacao USING btree (pessoa);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN status INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN versao INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopaycomunicacao SET versao = 1 WHERE coalesce(versao,0) = 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_status ON public.pactopaycomunicacao USING btree (status);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopaycomunicacao SET status = " + StatusPactoPayComunicacaoEnum.PROCESSADO.getId() + " WHERE status is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN transacao INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_transacao ON public.pactopaycomunicacao USING btree (transacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN pix INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_pix ON public.pactopaycomunicacao USING btree (pix);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN boleto INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_boleto ON public.pactopaycomunicacao USING btree (boleto);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN autorizacaocobrancacliente INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_autorizacaoCobrancaCliente ON public.pactopaycomunicacao USING btree (autorizacaocobrancacliente);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao DROP COLUMN lido;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao DROP COLUMN clicou;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.pactopaycomunicacaolog (\n" +
                    "\tcodigo serial primary key,\n" +
                    "\tdataregistro TIMESTAMP WITHOUT TIME zone,\n" +
                    "\tpactopaycomunicacao integer,\n" +
                    "\toperacao text,\n" +
                    "\tdados text\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacaolog_pactopaycomunicacao ON public.pactopaycomunicacaolog USING btree (pactopaycomunicacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.pactopaycomunicacaomovparcela (\n" +
                    "\tcodigo serial primary key,\n" +
                    "\tpactopaycomunicacao integer,\n" +
                    "\tmovparcela integer\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacaomovparcela_pactopaycomunicacao ON public.pactopaycomunicacaomovparcela USING btree (pactopaycomunicacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacaomovparcela_movparcela ON public.pactopaycomunicacaomovparcela USING btree (movparcela);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "06/06/2024",
            descricao = "Novas colunas para MovConta",
            motivacao = "M2-1834")
    public void migracaoVersao2152() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movConta ADD COLUMN tipoContaPagarLote int", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movConta ADD COLUMN payloadPix text ", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "11/06/2024",
            descricao = "Configuração plano para permitir turmas no vendas online",
            motivacao = "GC-700")
    public void migracaoVersao2153() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN permitirTurmasVendasOnline BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "11/06/2024",
            descricao = "Novos campos para o local de acesso",
            motivacao = "M5-424")
    public void migracaoVersao2154() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE localacesso ADD COLUMN servidorFacialInner VARCHAR(50) DEFAULT '';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE localacesso ADD COLUMN portaServidorFacialInner INT4 DEFAULT 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE coletor ADD COLUMN usaFacial boolean DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Ivan Alves de Oliveira",
            data = "13/06/2024",
            descricao = "Corrigir assinatura digital do contrato duplicado",
            motivacao = "M1-2045")
    public void migracaoVersao2155() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT cad.contrato,\n"
                    + "(CASE WHEN max(case when coalesce(assinatura, '') <> '' then cad.codigo else 0 end) > 0\n"
                    + "THEN max(case when coalesce(assinatura, '') <> '' then cad.codigo else 0 end) ELSE min(cad.codigo) END) as codigo_manter\n"
                    + "FROM contratoassinaturadigital cad INNER JOIN contrato con ON con.codigo = cad.contrato GROUP BY cad.contrato HAVING count(*) > 1;", c);
            while (rs.next()) {
                String sql = "DELETE FROM contratoassinaturadigital WHERE contrato = " + rs.getInt("contrato") + " AND codigo <> " + rs.getInt("codigo_manter");
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratoassinaturadigital DROP CONSTRAINT IF EXISTS contratoassinaturadigital_contrato_key;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratoassinaturadigital ADD UNIQUE (contrato);", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "13/06/2024",
            descricao = "Corrigir campo cancelamentoAntecipadoPlanos com espaçamentos",
            motivacao = "M1-2096")
    public void migracaoVersao2156() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET cancelamentoAntecipadoPlanos = REPLACE(cancelamentoAntecipadoPlanos, ' ', '')", c);
        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "20/06/2024",
            descricao = "Movimento Custodia tipo ES errado",
            motivacao = "M2-1909")
    public void migracaoVersao2157() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movcontarateio SET tipoes = 1 WHERE codigo IN (" +
                    "    SELECT m2.codigo FROM movconta m " +
                    "    INNER JOIN movcontarateio m2 ON m2.movconta = m.codigo " +
                    "    WHERE m.tipooperacao = 8 AND m2.tipoes = 2);", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "20/06/2024",
            descricao = "Apagar registro de grupos inexistentes de contratos",
            motivacao = "M1-2156")
    public void migracaoVersao2158() throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT 'UPDATE contrato SET grupo = NULL WHERE codigo = ' || con.codigo || ';' AS updateSQL\n" +
                    "FROM contrato con\n" +
                    "WHERE NOT EXISTS (SELECT codigo FROM grupo WHERE codigo = con.grupo)\n" +
                    "AND (con.grupo IS NOT NULL AND con.grupo <> 0)", c);
            while (rs.next()) {
                String sqlUpdate = rs.getString("updateSQL");
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlUpdate, c);
            }
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contrato ADD CONSTRAINT fk_contrato_grupo FOREIGN KEY (grupo) REFERENCES public.grupo(codigo);", c);
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "27/06/2024",
            descricao = "Nova coluna para gravar infos adicionais vendas online",
            motivacao = "M1-2045")
    public void migracaoVersao2159() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlinelog ADD COLUMN utm_data TEXT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_vendasonlinelog_cliente ON public.vendasonlinelog USING btree (cliente);", c);
        }
    }

    @Processo(autor = "Rodrigo Estulano",
            data = "03/07/2024",
            descricao = "Abrir pix expirados dos últimos 10 dias",
            motivacao = "M1-2045")
    public void migracaoVersao2160() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update pix set status = 'ATIVA' where codigo in (select codigo from pix p where data::date >= now() - INTERVAL '10 days' and status = 'EXPIRADA')", c);
        }
    }

    @Processo(autor = "Maurin Noleto",
            data = "01/07/2024",
            descricao = "Nova coluna boleana se está executando cobrança Link Pagamento",
            motivacao = "M2-1928")
    public void migracaoVersao2161() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE tokenvendasonline ADD COLUMN emProcessamento BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "13/06/2024",
            descricao = "Validacao acesso por produto gym pass",
            motivacao = "GC-610")
    public void migracaoVersao2162() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.infocheckin ADD COLUMN produtoGymPass varchar;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "10/06/2024",
            descricao = "Video e observacao plano no checkout vendas",
            motivacao = "GC-747")
    public void migracaoVersao2163() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN videoSiteUrl TEXT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN observacaoSite TEXT;", c);
        }
    }

    @Processo(autor = "Lucas Araujo",
            data = "15/05/2024",
            descricao = "Configuração plano para permitir turmas no vendas online",
            motivacao = "GC-700")
    public void migracaoVersao2164() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN permitirTurmasVendasOnline BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "04/06/2024",
            descricao = "limite de reposições de aulas coletivas",
            motivacao = "E2-1034")
    public void migracaoVersao2165() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN limitereposicaoaulacoletiva integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table alunohorarioturmadesmarcado ADD COLUMN contrato integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table alunohorarioturmadesmarcado ADD COLUMN aulareposta integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table alunohorarioturmadesmarcado ADD COLUMN reposicao boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table alunohorarioturmadesmarcado ADD COLUMN manternarenovacao boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table alunohorarioturmadesmarcado ADD COLUMN diareposta timestamp;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table alunohorarioturmadesmarcado ADD COLUMN datalimitereposicao timestamp;", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "07/06/2024",
            descricao = "Ajuste gênero",
            motivacao = "Ajuste gênero")
    public void migracaoVersao2166() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update pessoa set genero = 'MA' where genero in ('MC','MT');", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update pessoa set genero = 'FE' where genero in ('FC','FT');", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "20/05/2024",
            descricao = "Permissão aba CRM",
            motivacao = "E2-840")
    public void migracaoVersao2167() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            Hashtable<String, OpcaoPerfilAcesso> mapa = OpcoesPerfilAcesso.inicializarPessoas();
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso from permissao where nomeentidade ilike 'Cliente'", c);
            while (rs.next()) {
                Integer codperfilacesso = rs.getInt("codperfilacesso");
                for (String key : mapa.keySet()) {
                    OpcaoPerfilAcesso opcaoPerfilAcesso = mapa.get(key);
                    if (opcaoPerfilAcesso.getTitulo().startsWith("13.09")) {
                        boolean existe = SuperFacadeJDBC.existe("select codperfilacesso from permissao where codperfilacesso = " + codperfilacesso + " and nomeentidade ilike '" + opcaoPerfilAcesso.getNome() + "'", c);
                        if (existe) {
                            continue;
                        }
                        String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                                + " VALUES ("+opcaoPerfilAcesso.getTipo()+", '" + opcaoPerfilAcesso.getTitulo() + "', '(0)(1)(2)(3)(9)(12)', '" + opcaoPerfilAcesso.getNome() + "', " + codperfilacesso + ")";
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                    }
                }
            }
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "09/05/2024",
            descricao = "Nova venda avulsa",
            motivacao = "Nova venda avulsa")
    public void migracaoVersao2168() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE itemvendaavulsa ADD COLUMN pacotepersonal integer;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "10/06/2024",
            descricao = "Video e observacao plano no checkout vendas",
            motivacao = "GC-747")
    public void migracaoVersao2169() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN videoSiteUrl TEXT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN observacaoSite TEXT;", c);
        }
    }

    @Processo(autor = "Denis Silva",
            data = "27/05/2024",
            descricao = "Criando tabela de turmavideo",
            motivacao = "TW-15")
    public void migracaoVersao2170() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.turmavideo (\n" +
                    "codigo serial4 NOT NULL, \n" +
                    "turma_codigo int4 NOT NULL, \n" +
                    "linkvideo varchar NOT NULL, \n" +
                    "professor bool NULL DEFAULT false, \n" +
                    "CONSTRAINT turmavideo_pk PRIMARY KEY (codigo), \n" +
                    "CONSTRAINT turmavideo_fk FOREIGN KEY (turma_codigo) REFERENCES public.turma(codigo));", c);
        }
    }

    @Processo(autor = "Athos Franco",
            data = "18/03/2024",
            descricao = "Adição de coluna 'diasativos' na tabela 'itemcampanha' e 'diasativospontuacaoacesso' na tabela 'empresa' para registrar os dias que serão pontuados os acessos - clube de vantagens",
            motivacao = "GC-561")
    public void migracaoVersao2171() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE itemcampanha ADD COLUMN diasativos VARCHAR(255) DEFAULT 'SEG,TER,QUA,QUI,SEX,SAB,DOM';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN diasativospontuacaoacesso VARCHAR(255) DEFAULT 'SEG,TER,QUA,QUI,SEX,SAB,DOM';", c);
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "05/05/2024",
            descricao = "Recurso Padrão",
            motivacao = "Recurso Padrão")
    public void migracaoVersao2172() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN tiposInfoMigracaoPadrao TEXT;", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "30/04/2024",
            descricao = "Adicionar colunas gerenciar forma de pagamento por plano produto vendas online",
            motivacao = "GC-464")
    public void migracaoVersao2173() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconvenio ADD COLUMN formapagamento INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN usarformapagamentoplanoproduto BOOLEAN default false;", c);
        }
    }

    @Processo(autor = "Matheus Cassimiro",
            data = "30/05/2024",
            descricao = "Acrescentando campo nome registro no cadastro de colaborador/cliente/visistante",
            motivacao = "Acrescentando campo nome registro no cadastro de colaborador/cliente/visistante")
    public void migracaoVersao2174() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN nomeRegistro varchar(120) NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO configuracaosistemacadastrocliente (nome,obrigatorio,mostrar,pendente,visitante,validarcatraca) VALUES ('Nome Registro',false,false,false,true,false);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO configuracaosistemacadastrocliente (nome,obrigatorio,mostrar,pendente,visitante,validarcatraca) VALUES ('Nome Registro',false,false,false,false,false);", c);
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "10/05/2024",
            descricao = "Adicionar coluna indicado",
            motivacao = "GC-695")
    public void migracaoVersao2175() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE alunohorarioturma ADD COLUMN indicado INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaconfirmada ADD COLUMN indicado INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE reposicao ADD COLUMN indicado INTEGER;", c);
        }
    }

    @Processo(autor = "Athos Feitosa",
            data = "07/06/2024",
            descricao = "Criação de nova configuração de pre-cadastro para leads",
            motivacao = "GC-650")
    public void migracaoVersao2176() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN habilitarPreCadastro BOOLEAN DEFAULT FALSE;", c);
        }
    }

    @Processo(autor = "Anderson Xavier",
            data = "31/05/2024",
            descricao = "Adicionar coluna cnae",
            motivacao = "M2-1649")
    public void migracaoVersao2177() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN cnae VARCHAR;", c);
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "14/06/2024",
            descricao = "Povoando nova permissao pacto app 7.74",
            motivacao = "APPS-1304")
    public void migracaoVersao2178() throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso", c);
            while (rs.next()) {
                String sqlTemPermissao = "SELECT * FROM permissao WHERE codperfilacesso = " + rs.getInt("codigo") + " AND nomeentidade = 'PactoApp'";
                if (SuperFacadeJDBC.existe(sqlTemPermissao, c)) {
                    continue;
                }

                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.99 - Pacto App','(0)(1)(2)(3)(9)(12)', "
                        + " 'PactoApp', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }
}
