/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.negocio;

import br.com.pactosolucoes.ce.negocio.interessado.FormaContatoVO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import org.json.JSONArray;
import org.json.JSONObject;
import controle.arquitetura.RoboControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.PerfilAcessoControle;
import controle.arquitetura.security.UsuarioControle;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClassificacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoEmailFechamentoMetaVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.GrauInstrucaoVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.ParentescoVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.PerguntaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TabelaZWEnum;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.crm.ConfiguracaoDiasMetasTO;
import negocio.comuns.crm.ConfiguracaoDiasPosVendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.crm.TextoPadraoVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasPagamentoDigitalEnum;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ComposicaoModalidadeVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.CondicaoPagamentoParcelaVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.HorarioDisponibilidadeVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisJSON;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.comuns.utilitarias.UtilWS;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.PlanoContasSugeridoServico;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.io.ByteArrayOutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe criada para incluir dados padrões do sistema em um banco novo
 *
 * <AUTHOR>
 */
public class PovoadorDadosBancoInicial extends SuperEntidade {

    private RoboVO roboVO;
    private PaisVO paisVO;
    private List<EstadoVO> listaEstados;
    private EmpresaVO empresaVO;
    private List<ModalidadeVO> listaModalidades;
    private List<ComposicaoVO> listaComposicao;
    private List<CondicaoPagamentoVO> listaCondicaoPagamento;
    private List<ConvenioDescontoVO> listaConveniosDescontoVOs;
    private List<PerguntaVO> listaPerguntaVOs;
    private List<TurmaVO> listaTurmaVO;
    private QuestionarioVO questionario1;
    private List<PerguntaClienteVO> listaPerguntaCliente;
    private List<ColaboradorVO> listaColaboradores;
    private List<NivelTurmaVO> listaNivelTurma;
    private List<UsuarioVO> listaUsuarios;
    private List<PlanoTextoPadraoVO> listaPlanoTextoPadrao;
    private List<ProdutoVO> listaProdutos;
    private List<LocalAcessoVO> listaAcessos;
    private List<QuestionarioClienteVO> listaQuestionarioCliente;
    private List<PlanoProdutoSugeridoVO> listaProdutosSugeridos;
    private PlanoVO planoVO;
    private List<PlanoDuracaoVO> listaDuracoes;
    private List<PlanoHorarioVO> listaHorariosVOs;
    private ClienteVO clienteVO = new ClienteVO();
    private List<MovProdutoVO> listaMovProduto;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
    private Map<Integer, TipoContaVO> tiposContasVO = new HashMap<Integer, TipoContaVO>();
    private Map<String, Map<Integer, Integer>>mapaInclusao = new HashMap<String, Map<Integer, Integer>>();

    public PovoadorDadosBancoInicial() throws Exception {
        super();
        inicializarDados();
    }

    public void inicializarDados() throws Exception {
        roboVO = new RoboVO();
        paisVO = new PaisVO();
        planoVO = new PlanoVO();
        listaEstados = new ArrayList<EstadoVO>();
        empresaVO = new EmpresaVO();
        listaModalidades = new ArrayList<ModalidadeVO>();
        listaComposicao = new ArrayList<ComposicaoVO>();
        listaCondicaoPagamento = new ArrayList<CondicaoPagamentoVO>();
        listaConveniosDescontoVOs = new ArrayList<ConvenioDescontoVO>();
        listaPerguntaVOs = new ArrayList<PerguntaVO>();
        listaTurmaVO = new ArrayList<TurmaVO>();
        listaPerguntaCliente = new ArrayList<PerguntaClienteVO>();
        listaColaboradores = new ArrayList<ColaboradorVO>();
        listaNivelTurma = new ArrayList<NivelTurmaVO>();
        listaUsuarios = new ArrayList<UsuarioVO>();
        listaPlanoTextoPadrao = new ArrayList<PlanoTextoPadraoVO>();
        listaProdutos = new ArrayList<ProdutoVO>();
        listaAcessos = new ArrayList<LocalAcessoVO>();
        listaQuestionarioCliente = new ArrayList<QuestionarioClienteVO>();
        listaProdutosSugeridos = new ArrayList<PlanoProdutoSugeridoVO>();
        listaDuracoes = new ArrayList<PlanoDuracaoVO>();
        listaMovProduto = new ArrayList<MovProdutoVO>();
        configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    /**
     * Método usado para inserir todos os dados padrões do banco inicial
     */
    public void inserirDadosBancoInicial() throws Exception {
        try {
            povoarBancoInicialViaOAMD();

            /*
            gravarPerfisDeAcessos();
            gravarNumeroMatricula(0);
            // insere o primeiro registro do robô
            gravarRegistroRobo(Uteis.getDataJDBCTimestamp(Calendario.hoje()),
                    Uteis.getDataJDBCTimestamp(Calendario.hoje()), Uteis.getDataJDBCTimestamp(Calendario.hoje()),
                    "INÍCIO DO SISTEMA");
            // insere categorias de produtos padrões
            gravarCategoriasProdutos();
            // insere produtos necessarios nas Operacoes de Contrato categoria de produto é um item obrigatorio, se nao colocar deixe com valor 0
            gravarProdutos();
            // insere estados em uma lista referente ao país que será cadastrado.
            gravarPais();
            // inserindo ambientes
            gravarAmbientes();
            // inserindo categorias
            gravarCategoriaClientes();
            // inserindo perguntas referentes aos questionarios já cadastrados anteriormente pelo arquivo Zillyon.sql que possui os creates das tabelas
            gravarPerguntasQuestionario();

            gravarEmpresa();
            gravarModalidades();
            gravarModalidadesEmpresa();
            gravarComposicoes();
            gravarComposicoesModalidade();
            gravarFormasPagamento();
            gravarCondicoesPagamento();
            gravarCondicoesPagamentoParcela();
            gravarTiposDesconto();
            gravarGrausInstrucao();
            gravarConveniosDesconto();
            gravarConveniosDescontoConfiguracao();
            gravarJustificativasOperacao();
            gravarGrupos();
            gravarClassificacoes();
            gravarParentescos();
            gravarProfissoes();
            gravarHorarios();
            gravarColaboradores();
            gravarTiposColaborador();

            gravarUsuarioEUsuarioPerfilAcesso(false, 0, listaColaboradores.get(0).getCodigo(), "CE", "P4ZW15PMG",
                    "PACTOBR", "PACTO - MÉTODO DE GESTÃO", empresaVO.getCodigo(), getFacade().getPerfilAcesso().consultarPorNome("ADMINISTRADOR"));
            gravarUsuarioEUsuarioPerfilAcesso(false, 0, listaColaboradores.get(1).getCodigo(), "CE", "P4ZW15PMG",
                    "EMPRESARIO", "NOME DO EMPRESARIO", empresaVO.getCodigo(), getFacade().getPerfilAcesso().consultarPorNome("ADMINISTRADOR"));

            gravarUsuariosMoveis();

            gravarNiveisTurma();
            gravarTurmas();
            gravarHorariosTurma();
            gravarPlanosTextoPadrao();
            gravarPlano();
            gravarLocaisAcesso();
            gravarColetores();
            gravarBancos();
            gravarOperadorasCartao();

            Integer numeroMatricula = consultaNumeroMatricula();
            if (numeroMatricula.equals(0)) {
                gravarClientes();
                gravarPerguntasCliente();
                gravarQuestionarioCliente(listaPerguntaCliente.get(0), listaQuestionarioCliente.get(0).getCodigo());
                gravarQuestionarioCliente(listaPerguntaCliente.get(1), listaQuestionarioCliente.get(0).getCodigo());
                gravarQuestionarioCliente(listaPerguntaCliente.get(2), listaQuestionarioCliente.get(0).getCodigo());
                gravarQuestionarioCliente(listaPerguntaCliente.get(3), listaQuestionarioCliente.get(0).getCodigo());
                gravarQuestionarioCliente(listaPerguntaCliente.get(4), listaQuestionarioCliente.get(0).getCodigo());
                gravarQuestionarioCliente(listaPerguntaCliente.get(5), listaQuestionarioCliente.get(0).getCodigo());
                gravarRespostasQuestionarioCliente();

                gravarSituacoes();
            }

            //planos de conta e centro de custos padrões
            gravarPlanoContas();
            gravarCentroCustos();
            gravarConfiguracoesCRM();
            //gravar grupo de colaborador
            gravarGruposColaborador();
            gravarTiposAmbiente();
            gravarFormasContato();
            gravarTipoContas();
            gravarContas();
            gravarObjecoes();
            gravarTextosPadroesCRM();
            gravarTipoDocumentos();*/
            // ---Finalizar banco inicial setando o campo rodarSqlsBancoInicial com
            // valor false - não retirar esse método - deve ser sempre o último método-------
        } finally {
            setarFalseCampoRodarSqlsBancoInicial();
        }
    }

    public void gravarGruposColaborador() throws Exception {
        //analisar se os grupos de colaborador já estão preenchidas
        boolean existe = getFacade().getGrupoColaborador().consultarSeExiste();
        //povoar os dados
        //obs.: considerar combo de tipoGrupo do metodo inicializarDominioTipoGrupo da classe Dominio
        if (!existe) {
            List<EmpresaVO> empresa = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            if (!empresa.isEmpty() && !listaUsuarios.isEmpty()) {
                gravarGrupoColaborador(empresa.get(0).getCodigo(), "VENDAS", "CO", listaUsuarios.get(0).getCodigo(), listaUsuarios.get(0).getColaboradorVO());
                gravarGrupoColaborador(empresa.get(0).getCodigo(), "PROFESSOR", "PR", listaUsuarios.get(0).getCodigo(), listaUsuarios.get(0).getColaboradorVO());
            }
        }
    }

    public void gravarGrupoColaborador(int empresa, String descricao, String tipoGrupo, int codigoGerente, ColaboradorVO colaboradorVO) throws Exception {
        GrupoColaboradorVO grupoColaboradorVO = new GrupoColaboradorVO();
        grupoColaboradorVO.setDescricao(descricao);
        grupoColaboradorVO.setEmpresa(new EmpresaVO());
        grupoColaboradorVO.getEmpresa().setCodigo(empresa);
        grupoColaboradorVO.setTipoGrupo(tipoGrupo);
        grupoColaboradorVO.setGerente(new UsuarioVO());
        grupoColaboradorVO.getGerente().setCodigo(codigoGerente);

        GrupoColaboradorParticipanteVO participanteVO = new GrupoColaboradorParticipanteVO();
        participanteVO.setTipoVisao("AC");
        participanteVO.setColaboradorParticipante(colaboradorVO);
        grupoColaboradorVO.getGrupoColaboradorParticipanteVOs().add(participanteVO);

        getFacade().getGrupoColaborador().incluirSemCommit(grupoColaboradorVO);
    }

    public void gravarConfiguracoesCRM() throws Exception {
        //analisar se as configuracoes do sistema do crm ja estao preenchidas
        boolean existeConfSistCRM = getFacade().getConfiguracaoSistemaCRM().consultarSeExisteConfiguracaoSistemaCRM();
        //povoar os dados
        if (!existeConfSistCRM) {
            //incluir configuracoes dias pos venda em configurações
            List<ConfiguracaoDiasPosVendaVO> listaConfiguracaoDiasPosVendaVOs = new ArrayList<ConfiguracaoDiasPosVendaVO>();
            incluirNaListaConfiguracoesDiasPosVenda("E-MAIL OU LIGACAO: DE BOAS VINDAS", 1, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("ORIENTACAO", 15, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("RELACIONAMENTO INICIAL", 45, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("ENVOLVIMENTO", 90, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("REORIENTACAO", 135, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("FEEDBACK", 180, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("APRECIACAO", 225, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("CLIENTE ESPECIAL", 270, listaConfiguracaoDiasPosVendaVOs);
            incluirNaListaConfiguracoesDiasPosVenda("REPLANEJAMENTO", 315, listaConfiguracaoDiasPosVendaVOs);

            List<ConfiguracaoDiasMetasTO> listaConfiguracaoDiasExAlunos = new ArrayList<ConfiguracaoDiasMetasTO>();
            incluirNaListaConfiguracoesDiasExAlunos("RESGATE", 45, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("90 dias", 90, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("150 dias", 150, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("240 dias", 240, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("360 dias", 360, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("540 dias", 540, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("720 dias", 720, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("1260 dias", 1260, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("1800 dias", 1800, listaConfiguracaoDiasExAlunos);
            incluirNaListaConfiguracoesDiasExAlunos("2520 dias", 2520, listaConfiguracaoDiasExAlunos);

            //gravar lista de emails de fechamento de meta
            List<ConfiguracaoEmailFechamentoMetaVO> listaConfiguracoes = new ArrayList<ConfiguracaoEmailFechamentoMetaVO>();
            ConfiguracaoEmailFechamentoMetaVO conf = new ConfiguracaoEmailFechamentoMetaVO();
            conf.setEmail("<EMAIL>");
            conf.setEmpresa(empresaVO);
            listaConfiguracoes.add(conf);
            getFacade().getConfiguracaoSistemaCRM().salvarEmailsFechamento(listaConfiguracoes);

            //gravar os dados de configuração
            gravarConfiguracaoCRM("PACTO SOFTWARE E GESTÃO", "<EMAIL>", "mail.pacto.vc",
                    "<EMAIL>", "impteste2015", false, false, 7, 7, 7, 10, 16, 7, false, false,
                    30, 30, 30, listaConfiguracaoDiasPosVendaVOs, listaConfiguracaoDiasExAlunos);
        }
    }

    public ConfiguracaoSistemaCRMVO gravarConfiguracaoCRM(String remetentePadrao, String emailPadrao,
            String mailServer, String login, String senha, boolean abertoSabado,
            boolean abertoDomingo, Integer nrFaltaPlanoMensal,
            Integer nrFaltaPlanoTrimestral, Integer nrFaltaPlanoAcimaSemestral,
            Integer nrDiasParaClientePreveRenovacao, Integer nrDiasParaClientePrevePerda,
            Integer nrRisco, boolean conexaoSegura, boolean dividirFase,
            Integer nrDiasAnterioresAgendamento, Integer nrDiasPosterioresAgendamento,
            Integer nrDiasLimiteAgendamentoFuturo, List<ConfiguracaoDiasPosVendaVO> listaConfiguracaoDiasPosVendaVOs,
            List<ConfiguracaoDiasMetasTO> listaConfiguracaoDiasExAlunos) throws Exception {
        ConfiguracaoSistemaCRMVO conf = new ConfiguracaoSistemaCRMVO();
        conf.setRemetentePadrao(remetentePadrao);
        conf.setEmailPadrao(emailPadrao);
        conf.setMailServer(mailServer);
        conf.setLogin(login);
        conf.setSenha(senha);
        conf.setAbertoDomingo(abertoDomingo);
        conf.setAbertoSabado(abertoSabado);
        conf.setNrFaltaPlanoMensal(nrFaltaPlanoMensal);
        conf.setNrFaltaPlanoTrimestral(nrFaltaPlanoTrimestral);
        conf.setNrFaltaPlanoAcimaSemestral(nrFaltaPlanoAcimaSemestral);
        conf.setNrDiasParaClientePreveRenovacao(nrDiasParaClientePreveRenovacao);
        conf.setNrDiasParaClientePrevePerda(nrDiasParaClientePrevePerda);
        conf.setNrRisco(nrRisco);
        conf.setConexaoSegura(conexaoSegura);
        conf.setDividirFase(dividirFase);
        conf.setNrDiasAnterioresAgendamento(nrDiasAnterioresAgendamento);
        conf.setNrDiasPosterioresAgendamento(nrDiasPosterioresAgendamento);
        conf.setNrDiasLimiteAgendamentoFuturo(nrDiasLimiteAgendamentoFuturo);
        conf.setConfiguracaoDiasPosVendaVOs(listaConfiguracaoDiasPosVendaVOs);
        conf.setConfiguracaoDiasMetasExAlunos(listaConfiguracaoDiasExAlunos);

        getFacade().getConfiguracaoSistemaCRM().incluirSemCommit(conf);
        return conf;
    }

    public void incluirNaListaConfiguracoesDiasPosVenda(String descricao, int nrDia, List<ConfiguracaoDiasPosVendaVO> listaConfiguracaoDiasPosVendaVOs) {
        ConfiguracaoDiasPosVendaVO confDias = new ConfiguracaoDiasPosVendaVO();
        confDias.setDescricao(descricao);
        confDias.setNrDia(nrDia);
        listaConfiguracaoDiasPosVendaVOs.add(confDias);
    }

    public void incluirNaListaConfiguracoesDiasExAlunos(String descricao, int nrDia, List<ConfiguracaoDiasMetasTO> listaConfiguracaoDiasExAlunos) {
        ConfiguracaoDiasMetasTO confDias = new ConfiguracaoDiasMetasTO();
        confDias.setDescricao(descricao);
        confDias.setNrDia(nrDia);
        listaConfiguracaoDiasExAlunos.add(confDias);
    }

    public void gravarPlanoContas() throws Exception {
        boolean planoContaExistente = getFacade().getFinanceiro().getPlanoConta().consultarSeExistePlanoContas();
        if (!planoContaExistente) {
            PlanoContasSugeridoServico povoador = new PlanoContasSugeridoServico(con);
            povoador.povoar();
        }
    }

    public void gravarCentroCustos() throws Exception {
        boolean centroCustoExistente = getFacade().getFinanceiro().getCentroCusto().consultarSeExisteCentroCustos();
        if (!centroCustoExistente) {
            gravarCentroCusto("001", "Academia");
            gravarCentroCusto("002", "Adm");
            gravarCentroCusto("003", "Loja");
        }
    }

    private void gravarCentroCusto(String codigoCentroCustos, String nome) throws Exception {
        CentroCustoTO centroCustos = new CentroCustoTO();
        centroCustos.setCodigoCentro(codigoCentroCustos);
        centroCustos.setDescricao(nome);
        getFacade().getFinanceiro().getCentroCusto().incluir(centroCustos);
    }

    private void gravarFormasContato() throws Exception {
        // TODO Esse tipo de inserção de dado dificulta I18N
        gravarFormaContato("Telefone");
        gravarFormaContato("Email");
        gravarFormaContato("Presença");
        gravarFormaContato("Fax");
        gravarFormaContato(50, "Operacao do Sistema");
    }

    private void gravarFormaContato(int codigo, String descricao) throws Exception {
        Boolean jaExiste = getFacade().getCentralEventosFacade().existeFormasContatoNome(descricao);
        if (!jaExiste) {
            FormaContatoVO obj = new FormaContatoVO();
            obj.setCodigo(codigo);
            obj.setDescricao(descricao);
            getFacade().getCentralEventosFacade().incluirFormaContato(obj);
        }
    }

    private void gravarFormaContato(String descricao)
            throws Exception {
        Boolean jaExiste = getFacade().getCentralEventosFacade().existeFormasContatoNome(descricao);
        if (!jaExiste) {
            FormaContatoVO obj = new FormaContatoVO();
            obj.setDescricao(descricao);
            getFacade().getCentralEventosFacade().incluirFormaContatoSemCodigo(obj);
        }
    }

    private void gravarTipoContas() throws Exception {
        gravarTipoConta("Caixa", ComportamentoConta.CAIXA);
        gravarTipoConta("Banco", ComportamentoConta.BANCO);
        gravarTipoConta("Cofre", ComportamentoConta.COFRE);
        gravarTipoConta("Pendencia", ComportamentoConta.PENDENCIAS);
        gravarTipoConta("Devolução", ComportamentoConta.DEVOLUCOES);
        gravarTipoConta("Custodia", ComportamentoConta.CUSTODIA);
    }

    private void gravarTipoConta(String descricao, ComportamentoConta comportamentoConta) throws Exception {
        TipoContaVO tipoContaVO = new TipoContaVO();
        tipoContaVO.setDescricao(descricao);
        tipoContaVO.setComportamento(comportamentoConta);
        getFacade().getFinanceiro().getTipoConta().incluir(tipoContaVO);

        tiposContasVO.put(comportamentoConta.getCodigo(), tipoContaVO);
    }

    private void gravarContas() throws Exception {
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(1), "Caixa Financeiro $ (Movimento)", "<p>Conta onde se paga despesas imediatas, e guarda o dinheiro em esp&eacute;cie da academia.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(3), "Cofre (Cheques)", "<p>Conta onde se movimenta vendas de cheques pr&eacute;-datados da empresa.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(6), "Cartões Crédito (Cielo ou Redecard)", "<p>Conta onde se movimenta as vendas em cart&otilde;es da empresa.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(6), "Cartões de Debito (Cielo ou Redecard)", "<p>Conta onde se movimenta as vendas em cart&otilde;es de debito da empresa. Eles ficam nela ate compensar, normalmente em 2 dias.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(2), "Banco da Empresa", "<p>Conta do banco da empresa, onde se faz movimenta&ccedil;&otilde;es financeiras e &ldquo;deposita&rdquo; cheques e cart&otilde;es.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(6), "Banco (Custódia)", "<p>Conta onde se armazena os cheques que est&atilde;o em custodiados no banco. Ap&oacute;s a compensa&ccedil;&atilde;o, devem ser depositados na conta do banco.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(5), "Devoluções (Cheques devolvidos)", "<p>Conta onde ficam cheques ou outra forma de pagamento que voltou.</p>", true, true);
        gravarConta(empresaVO.getCodigo(), tiposContasVO.get(4), "Pendência", "<p>Conta onde ficam armazenados: vales, adiantamentos e dentre outros tipos de pend&ecirc;ncias.</p>", true, true);
    }

    private void gravarConta(int empresa, TipoContaVO tipoConta, String descricao, String observacao, boolean ativa, boolean mostrarBi) throws Exception {
        ContaVO conta = new ContaVO();
        conta.getEmpresa().setCodigo(empresa);
        conta.setTipoConta(tipoConta);
        conta.setDescricao(descricao);
        conta.setObservacao(observacao);
        conta.setAtiva(ativa);
        conta.setMostrarBi(mostrarBi);
        getFacade().getFinanceiro().getConta().incluir(conta);
    }

    private void gravarObjecoes() throws Exception {
        gravarObjecao("MORA LONGE", "VISITANTE", "", "OB");
        gravarObjecao("ACHOU CARO", "VISITANTE", "", "OB");
        gravarObjecao("NÃO GOSTOU DA ACADEMIA", "VISITANTE", "", "OB");
        gravarObjecao("SÓ VEIO CONHECER", "VISITANTE", "", "OB");
        gravarObjecao("NÃO POSSUI CHEQUE/CARTÃO", "VISITANTE", "", "OB");
        gravarObjecao("VAI CONSULTAR PAI OU MÃE", "RENOVAÇÃO / DESISTENTE", "", "MD");
        gravarObjecao("ESTA EM OUTRA ACADEMIA", "RENOVAÇÃO / DESISTENTE", "", "MD");
        gravarObjecao("MUDOU DE BAIRRO, CIDADE OU PAIS", "RENOVAÇÃO / DESISTENTE", "", "MD");
        gravarObjecao("ESTA DOENTE", "RENOVAÇÃO / DESISTENTE", "", "MD");
        gravarObjecao("ESTA COM PREGUIÇA", "RENOVAÇÃO / DESISTENTE", "", "MD");
        gravarObjecao("INSATISFEITO(A) COM ACADEMIA", "RENOVAÇÃO / DESISTENTE", "", "MD");
    }

    private void gravarObjecao(String descricao, String grupo, String comentario, String tipoGrupo) throws Exception {
        ObjecaoVO objecaoVO = new ObjecaoVO();
        objecaoVO.setDescricao(descricao);
        objecaoVO.setGrupo(grupo);
        objecaoVO.setComentario(comentario);
        objecaoVO.setTipoGrupo(tipoGrupo);
        getFacade().getObjecao().incluir(objecaoVO);
    }

    private void gravarTextosPadroesCRM() throws Exception {
        gravarTextoPadraoCRM(FasesCRMEnum.VINTE_QUATRO_HORAS, "24 horas", "<p class='MsoNormal'>&ldquo;Ol&aacute; Mariazinha, bom dia! Voc&ecirc; nos fez uma visita no dia &ldquo;tal&rdquo; e me recordo que seu objetivo &eacute; perder peso e melhorar a qualidade do sono... (ver no BV)&rdquo;. O motivo de minha liga&ccedil;&atilde;o &eacute; justamente este... n&oacute;s temos uma aula especial e gostar&iacute;amos de convid&aacute;-lo para participar, gratuitamente. Voc&ecirc; ter&aacute; o benef&iacute;cio que est&aacute; procurando. Vamos agendar ent&atilde;o...?&rdquo;</p>", "");
        gravarTextoPadraoCRM(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, "Confirmação de agendamento", "<p class='MsoNormal'>Ol&aacute; Bom dia!&nbsp;<strong><em>Jo&atilde;ozinho</em></strong>&nbsp;&ldquo;agendamos para hoje/amanh&atilde;, as 15h00min uma aula experimental para voc&ecirc; e estou ligando para confirmar sua presen&ccedil;a&rdquo;... (ou&ccedil;a e se confirmado informe quem ele dever&aacute; procur&aacute;-la na Academia, assim ele ficar&aacute; mais &agrave; vontade) e se ele disser que n&atilde;o ser&aacute; poss&iacute;vel comparecer, pergunte se podem agendar para o dia xx, as xx:00 horas...</p>", "");
        gravarTextoPadraoCRM(FasesCRMEnum.POS_VENDA, "Pós-Venda (30 dias)", "<p class='MsoNormal'>&ldquo;Ol&aacute;, bom dia! Tudo bem com voc&ecirc;? Pode falar agora? Eu sou a Marli e estou ligando para saber de voc&ecirc; o que est&aacute; achando de suas atividades aqui conosco, no caso &eacute; muscula&ccedil;&atilde;o que voc&ecirc; faz, certo? Ent&atilde;o que bom que est&aacute; gostando, aproveitando ent&atilde;o esta oportunidade, voc&ecirc; n&atilde;o gostaria que eu ligasse para algum conhecido seu, amigo ou parente, para convid&aacute;-lo para vir tamb&eacute;m para a Academia? Pode ser bom para ele tamb&eacute;m. O que acha? Se tiver eu posso ligar para convid&aacute;-la. Ap&oacute;s isto, pegue os dados e pergunte sobre esta pessoa, o que ela faz. Assim ligue ent&atilde;o para a pessoa indicada.</p>", "https://docs.google.com/forms/d/1N5OE3RgPMEDSf974m4dcYwA9Kx2VFqgH23A13Kw8Oog/edit");
        gravarTextoPadraoCRM(FasesCRMEnum.DESISTENTES, "Desistente", "<p class='MsoNormal'>Ol&aacute; &ldquo;Senhor(a) ..... &ndash; percebi que n&atilde;o renovou seu plano, por&eacute;m gostaria de contar com sua aten&ccedil;&atilde;o para que possamos melhorar nossos servi&ccedil;os. Na sua opini&atilde;o, se pud&eacute;ssemos fazer alguma coisa para mant&ecirc;-lo como nosso cliente por mais tempo, o que poder&iacute;amos fazer? Fazer interpreta&ccedil;&atilde;o da resposta e escolher a op&ccedil;&atilde;o adequada para a situa&ccedil;&atilde;o.</p>", "");
    }

    private void gravarTextoPadraoCRM(FasesCRMEnum fasesCRM, String descricao, String mensagemPadrao, String linkdocs) throws Exception {
        TextoPadraoVO textoPadraoVO = new TextoPadraoVO();
        textoPadraoVO.setFaseCRM(fasesCRM);
        textoPadraoVO.setDescricao(descricao);
        textoPadraoVO.setMensagemPadrao(mensagemPadrao);
        textoPadraoVO.setEmpresa(empresaVO);
        textoPadraoVO.setLinkDocs(linkdocs);
        getFacade().getTextoPadrao().incluir(textoPadraoVO);
    }

    private void gravarTiposAmbiente() {
        /*
         * INSERT INTO tipoambiente (codigo, descricao, duracaominimahrs,
         * qtdmaximareservasdia, tempoadicionalposteriormin, horarioinicial,
         * horariofinal) VALUES (1, 'Salão', 1, 2, 30, '00:00:00', '23:59:00'),
         * (2, 'Quadra', 1, 10, 5, '07:00:00', '22:00:00'), (3, 'Piscina', 2, 3,
         * 15, '07:00:00', '20:00:00'), (4, 'Quiosque', 3, 3, 30, '07:00:00',
         * '22:00:00');
         */
    }

    public void gravarPerfisDeAcessos() throws Exception {
        gravarPerfilDeAcessoTodasPermissoes(1, "ADMINISTRADOR");
        gravarPerfilAcessoConsultor();
        gravarPerfilAcessoGerente();
        gravarPerfilAcessoProfessor();
    }

    /**
     * Seta os atributos de permissão passados como parâmetro para o objeto
     * Permissão
     */
    public static PermissaoVO incluirPermissao(String valorFinal, String valorInicial,
            String valorEspecifico, int tipoPermissao,
            String tituloApresentacao, String permissoes, String nomeEntidade,
            int codPerfilAcesso) throws Exception {
        PermissaoVO permissao = new PermissaoVO();
        permissao.setValorInicial(valorInicial);
        permissao.setValorFinal(valorFinal);
        permissao.setValorEspecifico(valorEspecifico);
        permissao.setTipoPermissao(tipoPermissao);
        permissao.setTituloApresentacao(tituloApresentacao);
        permissao.setPermissoes(permissoes);
        permissao.setNomeEntidade(nomeEntidade);
        permissao.setCodPerfilAcesso(codPerfilAcesso);
        return permissao;
    }

    /**
     * Grava perfil de acesso incluindo todas as permissões para o mesmo
     */
    public void gravarPerfilDeAcessoTodasPermissoes(int codigo, String nome) throws Exception {
        // pesquisa por esse codigo antes de gravar
        List<PerfilAcesso> listaAcessosCons = getFacade().getPerfilAcesso().consultarPorNome("ADMINISTRADOR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaAcessosCons.isEmpty()) {
            // perfil ADMINISTRADOR
            PerfilAcessoVO perfilAcesso = new PerfilAcessoVO();
            perfilAcesso.setNome("ADMINISTRADOR");
            perfilAcesso.setPermissaoVOs(obterPermissoesAdiministrador());
            getFacade().getPerfilAcesso().incluirSemCommit(perfilAcesso);
        }
    }

    public static List<PermissaoVO> obterPermissoesAdiministrador() throws Exception {
        List<PermissaoVO> perfilAcesso = new ArrayList<PermissaoVO>();
        PerfilAcessoControle perfilAcessoControle = new PerfilAcessoControle(true);
        perfilAcessoControle.setMarcarTudo(true);
        perfilAcessoControle.setAdicionarHintsWiki(false);
        perfilAcessoControle.setListaPermissoes(perfilAcessoControle.inicializaListaPermissoes());
        if (perfilAcessoControle.getMarcarTudo()) {
            // percorrer a lista de entidades marcando as entidades e dando
            // permissão total
            for (Map<String, Object> mapa : perfilAcessoControle.getListaPermissoes()) {
                for (PermissaoVO entidade : (List<PermissaoVO>) mapa.get("entidades")) {
                    if(permissoesRetiradasAdministrador().contains(entidade.getNomeEntidade())){
                        continue;
                    }
                    entidade.setSelecionado(true);
                    entidade.setPermissoes("(0)(1)(2)(3)(9)(12)");
                    perfilAcesso.add(entidade);
                }
                for (PermissaoVO entidade : (List<PermissaoVO>)mapa.get("acoes")) {
                    if(permissoesRetiradasAdministrador().contains(entidade.getNomeEntidade())){
                        continue;
                    }
                    entidade.setSelecionado(true);
                    entidade.setPermissoes("(0)(1)(2)(3)(9)(12)");
                    perfilAcesso.add(entidade);
                }
            }
        }
        return perfilAcesso;
    }
    public static String permissoesRetiradasAdministrador(){
        StringBuilder permissoes = new StringBuilder();
        permissoes.append("ConviteAulaExperimental");
        permissoes.append("LancarVisualizarLancamentosEmpresas,");
        permissoes.append("VisualizarMetasFinanceirasTodasEmpresas,");
        permissoes.append("CancelarSessao,");
        permissoes.append("AgendaEstudio,");
        permissoes.append("AgendarSessaoForaVigencia,");
        permissoes.append("AgendarPacoteForaPeriodoAgendamento,");
        permissoes.append("AlterarMatricula,");
        permissoes.append("AdicionarAlterarSenhaAcesso,");
        permissoes.append("ColaboradorInativoSocialMailing");
        return permissoes.toString();
    }
    /**
     * Grava perfil de acesso de um tipo especifico chamado CONSULTOR
     *
     * @throws Exception
     */
    public static List<PermissaoVO> obterPermissoesConsultor() throws Exception{
        List<PermissaoVO> perfilAcesso = new ArrayList<PermissaoVO>();
        perfilAcesso.add(incluirPermissao("", "", "", 1, "1.03 - Usuário", "(0)", "Usuario", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "1.07 - Permissão para liberar acesso", "(0)", "LiberacaoAcesso", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "1.09 - Consultar autorização de acesso em grupo empresarial", "(0)", "ConsultarAutorizacaoAGE", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "1.10 - Incluir / Alterar autorização de acesso em grupo empresarial", "(0)", "IncluirAutorizacaoAGE", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.01 - Categoria de Clientes", "(0)(9)(1)", "Categoria", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.02 - Cidade", "(0)(1)(2)(3)(9)(12)", "Cidade", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.03 - Classificação", "(0)(1)(2)(3)(9)(12)", "Classificacao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.04 - Cliente", "(0)(1)(2)(3)(9)(12)", "Cliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.07 - Colaborador", "(0)(1)(2)(9)(12)", "Colaborador", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.09 - Email", "(0)(1)(2)(3)(9)(12)", "Email", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.11 - Endereço", "(0)(1)(2)(3)(9)(12)", "Endereco", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.13 - Grau de Instrução", "(0)(1)(2)(3)(9)(12)", "GrauInstrucao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.16 - Movimento de Conta Corrente do Cliente", "(0)(1)(2)(3)(9)(12)", "MovimentoContaCorrenteCliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.17 - País", "(0)(1)(2)(3)(9)(12)", "Pais", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.18 - Parentesco", "(0)(1)(2)(3)(9)(12)", "Parentesco", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.19 - Pergunta", "(0)(1)(2)(3)(9)(12)", "Pergunta", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.20 - Pergunta Cliente", "(0)(1)(2)(3)(9)(12)", "PerguntaCliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.21 - Pessoa", "(0)(1)(2)(3)(9)(12)", "Pessoa", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.22 - Profissão", "(0)(1)(2)(3)(9)(12)", "Profissao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.23 - Questionário", "(0)", "Questionario", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.24 -Questionário Cliente", "(0)(1)(2)(3)(9)(12)", "QuestionarioCliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.25 - Questionário Pergunta", "(0)(1)(2)(3)(9)(12)", "QuestionarioPergunta", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.26 - Resposta da Pergunta", "(0)(1)(2)(3)(9)(12)", "RespostaPergunta", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.27 - Resposta da Pergunta do Cliente", "(0)(1)(2)(3)(9)(12)", "RespostaPerguntaCliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.28 - Telefone", "(0)(1)(2)(3)(9)(12)", "Telefone", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.29 - Incluir Vínculos Cliente/Colaborador", "(1)(9)", "Vinculo", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.30 - Visualizar valor CAC no contrato na página do cliente", "(0)(1)(2)(3)(9)(12)", "BiLtv", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.31 - Lançar mensagem para catraca", "(0)(1)(2)(3)(9)(12)", "LancarMensagemCatraca", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.32 - Lançar aviso ao consultor", "(0)(1)(2)(3)(9)(12)", "LancarMensagemConsultor", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.35 - Lança Observação", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObservacao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.36 - Lança Observação Geral", "(0)(1)(2)(3)(9)(12)", "ObservacaoCliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.40 - Permissão para Acessar o Social Mailing", "(0)(1)(2)(3)(9)(12)", "PermissaoAcessarSocialMailing", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.43 - Visualizar Business Intelligence", "(0)(1)(2)(3)(9)(12)", "VisualizarBI", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.44 - Operação - Caixa em Aberto", "(0)(1)(2)(3)(9)(12)", "CaixaEmAberto", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.49 - Lançamento de atestado de aptidão física", "(0)(1)(2)(3)(9)(12)", "LancamentoAtestadoAptidaoFisica", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.51 - Lançar Free Pass", "(0)(1)(2)(3)(9)(12)", "PermissaoFreePass", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.53 - Fechar Negociação do contrato", "(0)(1)(2)(3)(9)(12)", "FecharNegociacaoContrato", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.07 - Cancelamento Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "CancelamentoContrato_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.08 - Liberar Cancelamento Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Liberar_CancelamentoContrato_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.09 - Valor Manual Cancelamento Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "ValorManual_CancelamentoContrato_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.12 - Atestado para Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Atestado_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.13 - Férias  para Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Carencia_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.14 - Manutenção Modalidade - Autorizar", "(0)(1)(2)(3)(9)(12)", "ManutencaoModalidade_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.15 - Alterar Horário - Autorizar", "(0)(1)(2)(3)(9)(12)", "AlterarHorario_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.17 - Liberar Manutenção Modalidade", "(0)(1)(2)(3)(9)(12)", "LiberarManutencaoModalidade", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.21 - Incluir Reposição em Turmas - Autorizar", "(0)(1)(2)(3)(9)(12)", "IncluirReposicaoEmTurmas_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.22 - Excluir Reposição em Turmas - Autorizar", "(0)(1)(2)(3)(9)(12)", "ExcluirReposicaoEmTurmas_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.23 - Remarcar Aula Perdida - Autorizar", "(0)(1)(2)(3)(9)(12)", "RemarcarAulaPerdida_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.25 - Liberar multa e custos de Cancelamento de Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "LiberarMultaCustosCancelamento_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.27 - Liberar Data de Cancelamento Retroativo - Autorizar", "(0)(1)(2)(3)(9)(12)", "LiberarDataCancelamentoRetroativo_Autorizar", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.28 - Alterar Tipo do Cancelamento", "(0)(1)(2)(3)(9)(12)", "AlterarTipoCancelamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.30 - Valor Manual Percentual Multa Cancelamento - Autorizar", "(0)(1)(2)(3)(9)(12)", "ValorManualPercentualMultaCancelamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.34 - Adicionar/Alterar Senha Acesso - Autorizar", "(0)(1)(2)(3)(9)(12)", "AdicionarAlterarSenhaAcesso", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.01 - Aula Avulsa", "(0)(1)(2)(3)(9)(12)", "AulaAvulsaDiaria", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.02 - Banco", "(0)(9)(1)", "Banco", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.03 - Conta Corrente", "(0)(9)(1)", "ContaCorrente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.04 - Convênio de Cobrança", "(0)(9)(1)", "ConvenioCobranca", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.06 - Forma de Pagamento", "(0)(9)(1)", "FormaPagamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.07 - Movimento de Pagamento", "(0)(1)(2)(3)(9)(12)", "MovPagamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.08 - Movimento da Parcela", "(0)(1)(2)(3)(9)(12)", "MovParcela", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.11 - Venda Avulsa", "(0)(1)(2)(3)(9)(12)", "VendaAvulsa", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.15 - Operadora de Cartão", "(0)(9)(1)", "OperadoraCartao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.16 - Venda de Consumidor", "(0)(9)(1)", "VendaConsumidor", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.19 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento", "(0)(1)(2)(3)(9)(12)", "MovPagamento_AutorizaPagamentoPosteriorDataVencimento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.22 - Edição de Pagamento", "(0)(1)(2)(3)(9)(12)", "EdicaoPagamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.23 - Venda Avulsa - Consultor", "(0)(1)(2)(3)(9)(12)", "ConsultorVendaAvulsa", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.25 - Cancelamento de Parcelas em Aberto", "(0)(1)(2)(3)(9)(12)", "CancelamentoParcela", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.29 - Renegociação de Parcelas", "(0)(1)(2)(3)(9)(12)", "RenegociacaoParcelas", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.30 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Retroativa", "(0)(1)(2)(3)(9)(12)", "MovPagamento_AutorizaPagamentoDataRetroativa", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.33 - Permitir nova conta rateada entre plano de contas.", "(0)(1)(2)(3)(9)(12)", "NovaContaPlanoContasRatear", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.01 - Ambiente", "(0)", "Ambiente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.02 - Categoria de Produto", "(0)", "CategoriaProduto", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.03 - Condição de Pagamento", "(0)", "CondicaoPagamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.04 - Desconto", "(0)", "Desconto", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.05 - Horário", "(0)", "Horario", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.06 - Modalidade", "(0)", "Modalidade", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.07 - Nível da Turma", "(0)", "NivelTurma", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.08 - Pacote", "(0)", "Composicao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.09 - Plano", "(0)", "Plano", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.10 - Produto", "(0)", "Produto", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.11 - Modelo de Contrato e Recibo", "(0)", "PlanoTextoPadrao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.12 - Turma", "(0)", "Turma", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.01 - Fechamento de Caixa Por Operador", "(0)(1)(2)(3)(9)(12)", "CaixaPorOperadorRel", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.03 - Lista Chamada", "(0)(1)(2)(3)(9)(12)", "ListaChamada", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.07 - Parcela Em Aberto", "(0)(1)(2)(3)(9)(12)", "ParcelaEmAbertoRel", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.09 - Relatórios de Acessos - Lista e Totalizador", "(0)(1)(2)(3)(9)(12)", "TotalizadorFrequenciaRel", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.11 - BI - Índice de Renovação (IR)", "(0)(1)(2)(3)(9)(12)", "IndiceRenovacao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.12 - BI - Conversão de Vendas", "(0)(1)(2)(3)(9)(12)", "IndiceConversao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.14 - BI - Grupo de Risco", "(0)(1)(2)(3)(9)(12)", "Business", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.15 - BI - Pendências de Clientes", "(0)(1)(2)(3)(9)(12)", "PendenciaCliente", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.16 - BI - Contrato Recorrência", "(0)(1)(2)(3)(9)(12)", "ContratoRecorrencia", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.22 - Geral de Clientes", "(0)(1)(2)(3)(9)(12)", "GeralClientes", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.23 - Relatório de Clientes", "(0)(1)(2)(3)(9)(12)", "RelatorioClientes", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.01 - Módulo CRM", "(0)(1)(2)(3)(9)(12)", "ModuloCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.02 - Definir Layout", "(0)(1)(2)(3)(9)(12)", "DefinirLayout", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.03 - Índice de Renovação CRM", "(0)(1)(2)(3)(9)(12)", "IndiceRenovacaoCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.04 - Índice de Conversão CRM", "(0)(1)(2)(3)(9)(12)", "IndiceConversaoCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.05 - Rotatividade CRM", "(0)(1)(2)(3)(9)(12)", "RotatividadeCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.07 - Pendência Cliente CRM", "(0)(1)(2)(3)(9)(12)", "PendenciaClienteCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.09 - Modelo Mensagem", "(0)(1)(2)(3)(9)(12)", "ModeloMensagem", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.10 - Mailing", "(0)(1)(2)(3)(9)(12)", "MalaDireta", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.12 - Cliente Potencial", "(0)(1)(2)(3)(9)(12)", "Passivo", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.13 - Indicação", "(0)(1)(2)(3)(9)(12)", "Indicacao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.14 - Feriado", "(0)(9)(1)", "Feriado", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.15 - Agenda", "(0)(1)(2)(3)(9)(12)", "Agenda", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.16 - Abertura de Meta", "(0)(1)(2)(3)(9)(12)", "AberturaMeta", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.17 - Evento", "(0)", "Evento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.18 - Indicador de Vendas", "(0)(1)(2)(3)(9)(12)", "IndicadorVenda", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.19 - Indicador de Retenção", "(0)(1)(2)(3)(9)(12)", "IndicadorRetencao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.20 - Objeção", "(0)(1)(2)(3)(9)(12)", "Objecao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.21 - Visualizar Meta", "(0)(1)(2)(3)(9)(12)", "VisualizarMeta", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.22 - Histórico Contato", "(0)(1)(2)(3)(9)(12)", "HistoricoContato", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.23 - Realizar Contato", "(0)(1)(2)(3)(9)(12)", "RealizarContato", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.24 - Meta Agendamento", "(0)(1)(2)(3)(9)(12)", "MetaAgendamento", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.25 - Meta Vinte Quatro Horas", "(0)(1)(2)(3)(9)(12)", "MetaVinteQuatroHoras", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.26 - Meta Renovação", "(0)(1)(2)(3)(9)(12)", "MetaRenovacao", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.27 - Meta Pós Venda", "(0)(1)(2)(3)(9)(12)", "MetaPosVenda", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.30 - Meta Indicados", "(0)(1)(2)(3)(9)(12)", "MetaIndicado", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.31 - Meta Passivos", "(0)(1)(2)(3)(9)(12)", "MetaPassivo", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.32 - Meta Grupo Risco", "(0)(1)(2)(3)(9)(12)", "MetaGrupoRisco", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.33 - Meta Perda", "(0)(1)(2)(3)(9)(12)", "MetaPerda", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.34 - Meta Aniversariante", "(0)(1)(2)(3)(9)(12)", "MetaAniversariante", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.35 - Meta Faltosos", "(0)(1)(2)(3)(9)(12)", "MetaFaltosos", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.36 - Meta Vencidos", "(0)(1)(2)(3)(9)(12)", "MetaVencidos", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.37 - Meta Conversão de Agendados", "(0)(1)(2)(3)(9)(12)", "MetaConversaoAgendados", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.38 - Meta Agendados de Amanhã", "(0)(1)(2)(3)(9)(12)", "MetaLigacaoAgendamentoAmanha", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "7.39 - Liberar Trocar de Colaboradores Abertura Dia", "(0)(1)(2)(3)(9)(12)", "LiberarTrocarColabordorAberturaDia", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.41 - Meta Ex-Alunos", "(0)(1)(2)(3)(9)(12)", "MetaExAlunos", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.42 - Meta Conversão de Ex-Alunos", "(0)(1)(2)(3)(9)(12)", "MetaConversaoExAlunos", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.43 - Meta Visitantes Antigos", "(0)(1)(2)(3)(9)(12)", "MetaVisitantesAntigos", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.49 - Pesquisar meta passada", "(0)(1)(2)(3)(9)(12)", "PesquisarMetaPassadaCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.50 - Visualizar Business Intelligence CRM", "(0)(1)(2)(3)(9)(12)", "BusinessIntelligenceCRM", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "9.32 - Visualizar lançamentos", "(0)(1)(2)(3)(9)(12)", "VisualizarLancamentos", 2));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "9.33 - Retirada automática de recebíveis do financeiro", "(0)(1)(2)(3)(9)(12)", "RetiradaAutomaticaRecebiveisFinanceiro", 2));
        return perfilAcesso;
    }

    public static List<PermissaoVO> obterPermissoesGerente() throws Exception {
        List<PermissaoVO> perfilAcesso = new ArrayList<PermissaoVO>();
        perfilAcesso.add(incluirPermissao("", "", "", 1, "1.03 - Usuário", "(0)(1)(2)(3)(9)(12)", "Usuario", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "1.07 - Permissão para liberar acesso", "(0)(1)(2)(3)(9)(12)", "LiberacaoAcesso", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "1.08 - Permissão para alterar senha de outro usuário", "(0)(1)(2)(3)(9)(12)", "Alterar Senhas", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "1.10 - Incluir / Alterar autorização de acesso em grupo empresarial", "(0)(1)(2)(3)(9)(12)", "IncluirAutorizacaoAGE", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 4, "12.01 - Cadastrar Compra", "(0)(1)(2)(3)(9)(12)", "CadastrarCompra", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 4, "12.05 - Visualizar Cardex", "(0)(1)(2)(3)(9)(12)", "VisualizarCardex", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 4, "12.06 - Adicionar Produto ao Controle de Estoque", "(0)(1)(2)(3)(9)(12)", "ConfigurarProdutoEstoque", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 4, "12.07 - Alterar Situação do Produto Estoque", "(0)(1)(2)(3)(9)(12)", "AlterarSituacaoProdutoEstoque", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 4, "12.08 - Visualizar Posição do Estoque", "(0)(1)(2)(3)(9)(12)", "VisualizarPosicaoEstoque", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.01 - Categoria de Clientes", "(0)(1)(2)(3)(9)(12)", "Categoria", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.02 - Cidade", "(0)(1)(2)(3)(9)(12)", "Cidade", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.03 - Classificação", "(0)(1)(2)(3)(9)(12)", "Classificacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.04 - Cliente", "(0)(1)(2)(3)(9)(12)", "Cliente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.05 - Cliente Classificação", "(0)(1)(2)(3)(9)(12)", "ClienteClassificacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.06 - Cliente Grupo", "(0)(1)(2)(3)(9)(12)", "ClienteGrupo", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.07 - Colaborador", "(0)(1)(2)(3)(9)(12)", "Colaborador", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.08 - Configuração do Sistema", "(0)(1)(2)(3)(9)(12)", "ConfiguracaoSistema", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.09 - Email", "(0)(1)(2)(3)(9)(12)", "Email", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.11 - Endereço", "(0)(1)(2)(3)(9)(12)", "Endereco", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.12 - Familiar", "(0)(1)(2)(3)(9)(12)", "Familiar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.13 - Grau de Instrução", "(0)(1)(2)(3)(9)(12)", "GrauInstrucao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.14 - Grupo", "(0)(1)(2)(3)(9)(12)", "Grupo", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.16 - Movimento de Conta Corrente do Cliente", "(0)(1)(2)(3)(9)(12)", "MovimentoContaCorrenteCliente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.17 - País", "(0)(1)(2)(3)(9)(12)", "Pais", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.18 - Parentesco", "(0)(1)(2)(3)(9)(12)", "Parentesco", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.19 - Pergunta", "(0)(1)(2)(3)(9)(12)", "Pergunta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.20 - Pergunta Cliente", "(0)(1)(2)(3)(9)(12)", "PerguntaCliente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.21 - Pessoa", "(0)(1)(2)(3)(9)(12)", "Pessoa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.22 - Profissão", "(0)(1)(2)(3)(9)(12)", "Profissao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.23 - Questionário", "(0)(1)(2)(3)(9)(12)", "Questionario", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.24 -Questionário Cliente", "(0)(1)(2)(3)(9)(12)", "QuestionarioCliente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.25 - Questionário Pergunta", "(0)(1)(2)(3)(9)(12)", "QuestionarioPergunta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.26 - Resposta da Pergunta", "(0)(1)(2)(3)(9)(12)", "RespostaPergunta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.27 - Resposta da Pergunta do Cliente", "(0)(1)(2)(3)(9)(12)", "RespostaPerguntaCliente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.28 - Telefone", "(0)(1)(2)(3)(9)(12)", "Telefone", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.29 - Incluir Vínculos Cliente/Colaborador", "(0)(1)(2)(3)(9)(12)", "Vinculo", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.30 - Alterar Vínculo Cliente/Consultor", "(0)(1)(2)(3)(9)(12)", "AlterarVinculoConsultor", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.31 - Lançar mensagem para catraca", "(0)(1)(2)(3)(9)(12)", "LancarMensagemCatraca", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.32 - Lançar aviso ao consultor", "(0)(1)(2)(3)(9)(12)", "LancarMensagemConsultor", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.33 - Lançar aviso médico", "(0)(1)(2)(3)(9)(12)", "LancarMensagemMedico", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.34 - Lançar objetivo do aluno academia", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObjetivoAluno", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.35 - Lança Observação", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObservacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.36 - Lança Observação Geral", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObservacaoGeral", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.37 - Exclusão de Visitantes após Data de Cadastro", "(0)(1)(2)(3)(9)(12)", "ExclusaoVisitantes", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.38 - Desconto em produto de Venda Avulsa", "(0)(1)(2)(3)(9)(12)", "DescontoVendaAvulsa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.39 - Alterar data de Venda Avulsa", "(0)(1)(2)(3)(9)(12)", "DataVendaAvulsa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.40 - Permissão para Acessar o Social Mailing", "(0)(1)(2)(3)(9)(12)", "PermissaoAcessarSocialMailing", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.41 - Permissão para alterar a data de vencimento de parcelas", "(0)(1)(2)(3)(9)(12)", "AlterarDataVencimentoParcela", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.43 - Visualizar Business Intelligence", "(0)(1)(2)(3)(9)(12)", "VisualizarBI", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.44 - Operação - Caixa em Aberto", "(0)(1)(2)(3)(9)(12)", "CaixaEmAberto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.49 - Lançamento de atestado de aptidão física", "(0)(1)(2)(3)(9)(12)", "LancamentoAtestadoAptidaoFisica", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.51 - Lançar Free Pass", "(0)(1)(2)(3)(9)(12)", "PermissaoFreePass", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.52 - Permissão para transferir cliente de empresa", "(0)(1)(2)(3)(9)(12)", "PermissaoTransferirClienteEmpresa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.53 - Fechar Negociação do contrato", "(0)(1)(2)(3)(9)(12)", "FecharNegociacaoContrato", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.54 - Enviar SMS no Social Mailing", "(0)(1)(2)(3)(9)(12)", "EnviarSMSSocialMailing", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.55 - Tamanho de Armário", "(0)(1)(2)(3)(9)(12)", "TamanhoArmario", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.01 - Contrato", "(0)(1)(2)(3)(9)(12)", "Contrato", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.02 - Configurações do Convênio de Desconto", "(0)(1)(2)(3)(9)(12)", "ConfiguracaoConvenio", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.03 - Convênio de Desconto", "(0)(1)(2)(3)(9)(12)", "ConvenioDesconto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.04 - Justificativa de Operação", "(0)(1)(2)(3)(9)(12)", "JustificativaOperacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.05 - Movimentação do Produto", "(0)(1)(2)(3)(9)(12)", "MovProduto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.06 - Liberação de Data Base", "(0)(1)(2)(3)(9)(12)", "DataBase", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.07 - Cancelamento Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "CancelamentoContrato_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.08 - Liberar Cancelamento Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Liberar_CancelamentoContrato_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.09 - Valor Manual Cancelamento Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "ValorManual_CancelamentoContrato_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.11 - Bonus para Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Bonus_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.12 - Atestado para Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Atestado_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.13 - Férias  para Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "Carencia_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.14 - Manutenção Modalidade - Autorizar", "(0)(1)(2)(3)(9)(12)", "ManutencaoModalidade_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.15 - Alterar Horário - Autorizar", "(0)(1)(2)(3)(9)(12)", "AlterarHorario_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.17 - Liberar Manutenção Modalidade", "(0)(1)(2)(3)(9)(12)", "LiberarManutencaoModalidade", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.18 - Atestado, Trancamento ou Férias com Frequência - Autorizar", "(0)(1)(2)(3)(9)(12)", "AtestadoCarencia_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.19 - Estorno de Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "EstornoContrato_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.20 - Alterar Contrato Agendado/Espontâneo - Autorizar", "(0)(1)(2)(3)(9)(12)", "Alterar_Contrato_AgendadoEspontaneo_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.21 - Incluir Reposição em Turmas - Autorizar", "(0)(1)(2)(3)(9)(12)", "IncluirReposicaoEmTurmas_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.22 - Excluir Reposição em Turmas - Autorizar", "(0)(1)(2)(3)(9)(12)", "ExcluirReposicaoEmTurmas_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.23 - Remarcar Aula Perdida - Autorizar", "(0)(1)(2)(3)(9)(12)", "RemarcarAulaPerdida_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.24 - Estorno de Operação de Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "EstornoOperacaoContrato_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.25 - Liberar multa e custos de Cancelamento de Contrato - Autorizar", "(0)(1)(2)(3)(9)(12)", "LiberarMultaCustosCancelamento_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.26 - Alterar Consultor do Contrato", "(0)(1)(2)(3)(9)(12)", "AlterarConsultorContrato", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.27 - Liberar Data de Cancelamento Retroativo - Autorizar", "(0)(1)(2)(3)(9)(12)", "LiberarDataCancelamentoRetroativo_Autorizar", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.28 - Alterar Tipo do Cancelamento", "(0)(1)(2)(3)(9)(12)", "AlterarTipoCancelamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.30 - Valor Manual Percentual Multa Cancelamento - Autorizar", "(0)(1)(2)(3)(9)(12)", "ValorManualPercentualMultaCancelamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.32 - Lançar Planos Tipo Bolsa - Autorizar", "(0)(1)(2)(3)(9)(12)", "LancarPlanosTipoBolsa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.01 - Aula Avulsa", "(0)(1)(2)(3)(9)(12)", "AulaAvulsaDiaria", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.02 - Banco", "(0)(1)(2)(3)(9)(12)", "Banco", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.03 - Conta Corrente", "(0)(1)(2)(3)(9)(12)", "ContaCorrente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.04 - Convênio de Cobrança", "(0)(1)(2)(3)(9)(12)", "ConvenioCobranca", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.06 - Forma de Pagamento", "(0)(1)(2)(3)(9)(12)", "FormaPagamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.07 - Movimento de Pagamento", "(0)(1)(2)(3)(9)(12)", "MovPagamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.08 - Movimento da Parcela", "(0)(1)(2)(3)(9)(12)", "MovParcela", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.09 - Tipo de Remessa", "(0)(1)(2)(3)(9)(12)", "TipoRemessa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.10 - Tipo de Retorno", "(0)(1)(2)(3)(9)(12)", "TipoRetorno", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.11 - Venda Avulsa", "(0)(1)(2)(3)(9)(12)", "VendaAvulsa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.12 - Estorno de Recibo", "(0)(1)(2)(3)(9)(12)", "EstornoRecibo", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.13 - Estorno do Contrato", "(0)(1)(2)(3)(9)(12)", "EstornoContrato", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.14 - Estorno de Movimentação Produto", "(0)(1)(2)(3)(9)(12)", "EstornoMovProduto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.15 - Operadora de Cartão", "(0)(9)(1)", "OperadoraCartao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.16 - Venda de Consumidor", "(0)(1)(2)(3)(9)(12)", "VendaConsumidor", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.17 - Cupons Fiscais", "(0)(1)(2)(3)(9)(12)", "CupomFiscal", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "4.18 - Envio NFSe", "(0)(1)(2)(3)(9)(12)", "EnvioNFSe", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.19 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento", "(0)(1)(2)(3)(9)(12)", "MovPagamento_AutorizaPagamentoPosteriorDataVencimento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.20 - Gestão Transações", "(0)(1)(2)(3)(9)(12)", "GestaoTransacoes", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.21 - Gestão Comissão", "(0)(1)(2)(3)(9)(12)", "GestaoComissao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.22 - Edição de Pagamento", "(0)(1)(2)(3)(9)(12)", "EdicaoPagamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.23 - Venda Avulsa - Consultor", "(0)(1)(2)(3)(9)(12)", "ConsultorVendaAvulsa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.24 - Desconto no Plano", "(0)(1)(2)(3)(9)(12)", "DescontoPlano", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.25 - Cancelamento de Parcelas em Aberto", "(0)(1)(2)(3)(9)(12)", "CancelamentoParcela", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.27 - Comissão Variável", "(0)(1)(2)(3)(9)(12)", "ComissaoVariavel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.29 - Renegociação de Parcelas", "(0)(1)(2)(3)(9)(12)", "RenegociacaoParcelas", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.30 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Retroativa", "(0)(1)(2)(3)(9)(12)", "MovPagamento_AutorizaPagamentoDataRetroativa", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "4.33 - Permitir nova conta rateada entre plano de contas.", "(0)(1)(2)(3)(9)(12)", "NovaContaPlanoContasRatear", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.01 - Ambiente", "(0)(1)(2)(3)(9)(12)", "Ambiente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.02 - Categoria de Produto", "(0)(1)(2)(3)(9)(12)", "CategoriaProduto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.03 - Condição de Pagamento", "(0)(1)(2)(3)(9)(12)", "CondicaoPagamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.04 - Desconto", "(0)(1)(2)(3)(9)(12)", "Desconto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.05 - Horário", "(0)(1)(2)(3)(9)(12)", "Horario", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.06 - Modalidade", "(0)(1)(2)(3)(9)(12)", "Modalidade", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.07 - Nível da Turma", "(0)(1)(2)(3)(9)(12)", "NivelTurma", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.08 - Pacote", "(0)(1)(2)(3)(9)(12)", "Composicao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.09 - Plano", "(0)(1)(2)(3)(9)(12)", "Plano", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.10 - Produto", "(0)(1)(2)(3)(9)(12)", "Produto", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.11 - Modelo de Contrato e Recibo", "(0)(1)(2)(3)(9)(12)", "PlanoTextoPadrao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.12 - Turma", "(0)(1)(2)(3)(9)(12)", "Turma", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.01 - Fechamento de Caixa Por Operador", "(0)(1)(2)(3)(9)(12)", "CaixaPorOperadorRel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.02 - Situação de Clientes Sintético", "(0)(1)(2)(3)(9)(12)", "ClienteRel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.03 - Lista Chamada", "(0)(1)(2)(3)(9)(12)", "ListaChamada", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.07 - Parcela Em Aberto", "(0)(1)(2)(3)(9)(12)", "ParcelaEmAbertoRel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.08 - Saldo Conta Corrente de Cliente", "(0)(1)(2)(3)(9)(12)", "SaldoContaCorrenteRel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.09 - Relatórios de Acessos - Lista e Totalizador", "(0)(1)(2)(3)(9)(12)", "TotalizadorFrequenciaRel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.10 - Contratos Por Duração", "(0)(1)(2)(3)(9)(12)", "ClientePorDuracaoRel", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.11 - BI - Índice de Renovação (IR)", "(0)(1)(2)(3)(9)(12)", "IndiceRenovacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.12 - BI - Conversão de Vendas", "(0)(1)(2)(3)(9)(12)", "IndiceConversao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.14 - BI - Grupo de Risco", "(0)(1)(2)(3)(9)(12)", "Business", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.15 - BI - Pendências de Clientes", "(0)(1)(2)(3)(9)(12)", "PendenciaCliente", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.16 - BI - Contrato Recorrência", "(0)(1)(2)(3)(9)(12)", "ContratoRecorrencia", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.17 - Liberar Relatório de Fechamento de Caixa por Operador - Todos os dias", "(0)(1)(2)(3)(9)(12)", "LiberarTodosDiasRelatorioFechamentoCaixaOperador", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.18 - Liberar Relatório de Fechamento de Caixa por Operador - Todos os colaboradores", "(0)(1)(2)(3)(9)(12)", "LiberarTodosColaboradoresRelatorioFechamentoCaixaOperador", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.19 - Justificar Liberação de Acesso", "(0)(1)(2)(3)(9)(12)", "GestaoControleAcesso", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.20 - Enviar Email de Fechamento de Acesso", "(0)(1)(2)(3)(9)(12)", "GestaoControleAcessoEmail", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.21 - BI Ticket Médio", "(0)(1)(2)(3)(9)(12)", "TicketMedio", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.20 - Enviar Email de Fechamento de Acesso", "(0)(1)(2)(3)(9)(12)", "GestaoControleAcessoEmail", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.22 - Geral de Clientes", "(0)(1)(2)(3)(9)(12)", "GeralClientes", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "6.23 - Relatório de Clientes", "(0)(1)(2)(3)(9)(12)", "RelatorioClientes", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.01 - Módulo CRM", "(0)(1)(2)(3)(9)(12)", "ModuloCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.02 - Definir Layout", "(0)(1)(2)(3)(9)(12)", "DefinirLayout", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.03 - Índice de Renovação CRM", "(0)(1)(2)(3)(9)(12)", "IndiceRenovacaoCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.04 - Índice de Conversão CRM", "(0)(1)(2)(3)(9)(12)", "IndiceConversaoCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.05 - Rotatividade CRM", "(0)(1)(2)(3)(9)(12)", "RotatividadeCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.07 - Pendência Cliente CRM", "(0)(1)(2)(3)(9)(12)", "PendenciaClienteCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.08 - Grupo Colaborador", "(0)(1)(2)(3)(9)(12)", "GrupoColaborador", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.09 - Modelo Mensagem", "(0)(1)(2)(3)(9)(12)", "ModeloMensagem", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.10 - Mailing", "(0)(1)(2)(3)(9)(12)", "MalaDireta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.11 - Organizador de Carteira", "(0)(1)(2)(3)(9)(12)", "OrganizadorCarteira", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.12 - Cliente Potencial", "(0)(1)(2)(3)(9)(12)", "Passivo", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.13 - Indicação", "(0)(1)(2)(3)(9)(12)", "Indicacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.14 - Feriado", "(0)(1)(2)(3)(9)(12)", "Feriado", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.15 - Agenda", "(0)(1)(2)(3)(9)(12)", "Agenda", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.16 - Abertura de Meta", "(0)(1)(2)(3)(9)(12)", "AberturaMeta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.17 - Evento", "(0)(1)(2)(3)(9)(12)", "Evento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.18 - Indicador de Vendas", "(0)(1)(2)(3)(9)(12)", "IndicadorVenda", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.19 - Indicador de Retenção", "(0)(1)(2)(3)(9)(12)", "IndicadorRetencao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.20 - Objeção", "(0)(1)(2)(3)(9)(12)", "Objecao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.21 - Visualizar Meta", "(0)(1)(2)(3)(9)(12)", "VisualizarMeta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.22 - Histórico Contato", "(0)(1)(2)(3)(9)(12)", "HistoricoContato", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.23 - Realizar Contato", "(0)(1)(2)(3)(9)(12)", "RealizarContato", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.24 - Meta Agendamento", "(0)(1)(2)(3)(9)(12)", "MetaAgendamento", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.25 - Meta Vinte Quatro Horas", "(0)(1)(2)(3)(9)(12)", "MetaVinteQuatroHoras", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.26 - Meta Renovação", "(0)(1)(2)(3)(9)(12)", "MetaRenovacao", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.27 - Meta Pós Venda", "(0)(1)(2)(3)(9)(12)", "MetaPosVenda", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.30 - Meta Indicados", "(0)(1)(2)(3)(9)(12)", "MetaIndicado", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.31 - Meta Passivos", "(0)(1)(2)(3)(9)(12)", "MetaPassivo", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.32 - Meta Grupo Risco", "(0)(1)(2)(3)(9)(12)", "MetaGrupoRisco", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.33 - Meta Perda", "(0)(1)(2)(3)(9)(12)", "MetaPerda", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.34 - Meta Aniversariante", "(0)(1)(2)(3)(9)(12)", "MetaAniversariante", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.35 - Meta Faltosos", "(0)(1)(2)(3)(9)(12)", "MetaFaltosos", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.36 - Meta Vencidos", "(0)(1)(2)(3)(9)(12)", "MetaVencidos", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.37 - Meta Conversão de Agendados", "(0)(1)(2)(3)(9)(12)", "MetaConversaoAgendados", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.38 - Meta Agendados de Amanhã", "(0)(1)(2)(3)(9)(12)", "MetaLigacaoAgendamentoAmanha", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "7.39 - Liberar Trocar de Colaboradores Abertura Dia", "(0)(1)(2)(3)(9)(12)", "LiberarTrocarColabordorAberturaDia", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "7.40 - Permissão para Visualizar Todas Carteiras", "(0)(1)(2)(3)(9)(12)", "PermitirVisualizarTodasCarteiras", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.41 - Meta Ex-Alunos", "(0)(1)(2)(3)(9)(12)", "MetaExAlunos", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.42 - Meta Conversão de Ex-Alunos", "(0)(1)(2)(3)(9)(12)", "MetaConversaoExAlunos", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.43 - Meta Visitantes Antigos", "(0)(1)(2)(3)(9)(12)", "MetaVisitantesAntigos", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.46 - Meta Extra", "(0)(1)(2)(3)(9)(12)", "CrmExtraCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.47 - Totalizador de Metas", "(0)(1)(2)(3)(9)(12)", "TotalizadorMeta", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.49 - Pesquisar meta passada", "(0)(1)(2)(3)(9)(12)", "PesquisarMetaPassadaCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.50 - Visualizar Business Intelligence CRM", "(0)(1)(2)(3)(9)(12)", "BusinessIntelligenceCRM", 3));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "9.33 - Retirada automática de recebíveis do financeiro", "(0)(1)(2)(3)(9)(12)", "RetiradaAutomaticaRecebiveisFinanceiro", 3));
        return perfilAcesso;
    }

    public static List<PermissaoVO> obterPermissoesProfessor() throws Exception {
        List<PermissaoVO> perfilAcesso = new ArrayList<PermissaoVO>();
        perfilAcesso.add(incluirPermissao("", "", "", 2, "1.07 - Permissão para liberar acesso", "(0)(1)(2)(3)(9)(12)", "LiberacaoAcesso", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 3, "11.01 - Cancelar Sessões", "(0)(1)(2)(3)(9)(12)", "CancelarSessao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "11.02 - Agenda Estúdio", "(0)(1)(2)(3)(9)(12)", "AgendaEstudio", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.01 - Categoria de Clientes", "(0)(9)(1)", "Categoria", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.02 - Cidade", "(0)(1)(2)(3)(9)(12)", "Cidade", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.03 - Classificação", "(0)", "Classificacao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.04 - Cliente", "(0)(1)(2)(3)(9)(12)", "Cliente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.06 - Cliente Grupo", "(1)(9)", "ClienteGrupo", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.07 - Colaborador", "(0)", "Colaborador", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.08 - Configuração do Sistema", "(0)(1)(2)(3)(9)(12)", "ConfiguracaoSistema", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.09 - Email", "(0)(1)(2)(3)(9)(12)", "Email", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.11 - Endereço", "(0)(1)(2)(3)(9)(12)", "Endereco", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.13 - Grau de Instrução", "(0)(1)(2)(3)(9)(12)", "GrauInstrucao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.14 - Grupo", "(0)", "Grupo", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.16 - Movimento de Conta Corrente do Cliente", "(0)(1)(2)(3)(9)(12)", "MovimentoContaCorrenteCliente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.17 - País", "(0)(1)(2)(3)(9)(12)", "Pais", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.18 - Parentesco", "(0)(1)(2)(3)(9)(12)", "Parentesco", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.19 - Pergunta", "(0)(1)(2)(3)(9)(12)", "Pergunta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.20 - Pergunta Cliente", "(0)(1)(2)(3)(9)(12)", "PerguntaCliente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.21 - Pessoa", "(0)(1)(2)(3)(9)(12)", "Pessoa", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.22 - Profissão", "(0)(1)(2)(3)(9)(12)", "Profissao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.23 - Questionário", "(0)", "Questionario", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.24 -Questionário Cliente", "(0)(1)(2)(3)(9)(12)", "QuestionarioCliente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.25 - Questionário Pergunta", "(0)(1)(2)(3)(9)(12)", "QuestionarioPergunta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.26 - Resposta da Pergunta", "(0)(1)(2)(3)(9)(12)", "RespostaPergunta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.27 - Resposta da Pergunta do Cliente", "(0)(1)(2)(3)(9)(12)", "RespostaPerguntaCliente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "2.28 - Telefone", "(0)(1)(2)(3)(9)(12)", "Telefone", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.32 - Lançar aviso ao consultor", "(0)(1)(2)(3)(9)(12)", "LancarMensagemConsultor", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.33 - Lançar aviso médico", "(0)(1)(2)(3)(9)(12)", "LancarMensagemMedico", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.34 - Lançar objetivo do aluno academia", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObjetivoAluno", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.35 - Lança Observação", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObservacao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.36 - Lança Observação Geral", "(0)(1)(2)(3)(9)(12)", "LancarMensagemObservacaoGeral", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.40 - Permissão para Acessar o Social Mailing", "(0)(1)(2)(3)(9)(12)", "PermissaoAcessarSocialMailing", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.43 - Visualizar Business Intelligence", "(0)(1)(2)(3)(9)(12)", "VisualizarBI", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.53 - Fechar Negociação do contrato", "(0)(1)(2)(3)(9)(12)", "FecharNegociacaoContrato", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "2.54 - Enviar SMS no Social Mailing", "(0)(1)(2)(3)(9)(12)", "EnviarSMSSocialMailing", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.01 - Contrato", "(0)(1)(2)(3)(9)(12)", "Contrato", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.02 - Configurações do Convênio de Desconto", "(0)(9)(1)", "ConfiguracaoConvenio", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.03 - Convênio de Desconto", "(0)(9)(1)", "ConvenioDesconto", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.04 - Justificativa de Operação", "(0)(9)(1)", "JustificativaOperacao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "3.05 - Movimentação do Produto", "(0)(1)(2)(3)(9)(12)", "MovProduto", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.21 - Incluir Reposição em Turmas - Autorizar", "(0)(1)(2)(3)(9)(12)", "IncluirReposicaoEmTurmas_Autorizar", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.22 - Excluir Reposição em Turmas - Autorizar", "(0)(1)(2)(3)(9)(12)", "ExcluirReposicaoEmTurmas_Autorizar", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "3.23 - Remarcar Aula Perdida - Autorizar", "(0)(1)(2)(3)(9)(12)", "RemarcarAulaPerdida_Autorizar", 4)); perfilAcesso.add(incluirPermissao("", "", "", 1, "5.01 - Ambiente", "(0)", "Ambiente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.06 - Modalidade", "(0)", "Modalidade", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.07 - Nível da Turma", "(0)", "NivelTurma", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.12 - Turma", "(0)", "Turma", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "5.61 - Convite Aula Experimental", "(0)", "ConviteAulaExperimental", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.03 - Lista Chamada", "(0)(1)(2)(3)(9)(12)", "ListaChamada", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.09 - Relatórios de Acessos - Lista e Totalizador", "(0)(1)(2)(3)(9)(12)", "TotalizadorFrequenciaRel", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.11 - BI - Índice de Renovação (IR)", "(0)(1)(2)(3)(9)(12)", "IndiceRenovacao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.14 - BI - Grupo de Risco", "(0)(1)(2)(3)(9)(12)", "Business", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.23 - Relatório de Clientes", "(0)(1)(2)(3)(9)(12)", "RelatorioClientes", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.22 - Geral de Clientes", "(0)(1)(2)(3)(9)(12)", "GeralClientes", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "6.15 - BI - Pendências de Clientes", "(0)(1)(2)(3)(9)(12)", "PendenciaCliente", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.01 - Módulo CRM", "(0)(1)(2)(3)(9)(12)", "ModuloCRM", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.02 - Definir Layout", "(0)(1)(2)(3)(9)(12)", "DefinirLayout", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.03 - Índice de Renovação CRM", "(0)(1)(2)(3)(9)(12)", "IndiceRenovacaoCRM", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.04 - Índice de Conversão CRM", "(0)(1)(2)(3)(9)(12)", "IndiceConversaoCRM", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.05 - Rotatividade CRM", "(0)(1)(2)(3)(9)(12)", "RotatividadeCRM", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.07 - Pendência Cliente CRM", "(0)(1)(2)(3)(9)(12)", "PendenciaClienteCRM", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.09 - Modelo Mensagem", "(0)(9)(1)", "ModeloMensagem", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.10 - Mailing", "(0)(1)(2)(3)(9)(12)", "MalaDireta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.12 - Cliente Potencial", "(0)(1)(2)(3)(9)(12)", "Passivo", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.13 - Indicação", "(0)(1)(2)(3)(9)(12)", "Indicacao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.14 - Feriado", "(0)(9)(1)", "Feriado", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.15 - Agenda", "(0)(1)(2)(3)(9)(12)", "Agenda", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.16 - Abertura de Meta", "(0)(1)(2)(3)(9)(12)", "AberturaMeta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.17 - Evento", "(0)", "Evento", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.18 - Indicador de Vendas", "(0)(1)(2)(3)(9)(12)", "IndicadorVenda", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.19 - Indicador de Retenção", "(0)(1)(2)(3)(9)(12)", "IndicadorRetencao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.20 - Objeção", "(0)(1)(2)(3)(9)(12)", "Objecao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.21 - Visualizar Meta", "(0)(1)(2)(3)(9)(12)", "VisualizarMeta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.22 - Histórico Contato", "(0)(1)(2)(3)(9)(12)", "HistoricoContato", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.23 - Realizar Contato", "(0)(1)(2)(3)(9)(12)", "RealizarContato", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.24 - Meta Agendamento", "(0)(1)(2)(3)(9)(12)", "MetaAgendamento", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.26 - Meta Renovação", "(0)(1)(2)(3)(9)(12)", "MetaRenovacao", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.27 - Meta Pós Venda", "(0)(1)(2)(3)(9)(12)", "MetaPosVenda", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.30 - Meta Indicados", "(0)(1)(2)(3)(9)(12)", "MetaIndicado", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.31 - Meta Passivos", "(0)(1)(2)(3)(9)(12)", "MetaPassivo", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.32 - Meta Grupo Risco", "(0)(1)(2)(3)(9)(12)", "MetaGrupoRisco", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.34 - Meta Aniversariante", "(0)(1)(2)(3)(9)(12)", "MetaAniversariante", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.35 - Meta Faltosos", "(0)(1)(2)(3)(9)(12)", "MetaFaltosos", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 1, "7.36 - Meta Vencidos", "(0)(1)(2)(3)(9)(12)", "MetaVencidos", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 2, "7.39 - Liberar Trocar de Colaboradores Abertura Dia", "(0)(1)(2)(3)(9)(12)", "LiberarTrocarColabordorAberturaDia", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 3, "7.41 - Meta Ex-Alunos", "(0)(1)(2)(3)(9)(12)", "MetaExAlunos", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 3, "7.47 - Totalizador de Metas", "(0)(1)(2)(3)(9)(12)", "TotalizadorMeta", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 3, "7.49 - Pesquisar meta passada", "(0)(1)(2)(3)(9)(12)", "PesquisarMetaPassadaCRM", 4));
        perfilAcesso.add(incluirPermissao("", "", "", 3, "7.50 - Visualizar Business Intelligence CRM", "(0)(1)(2)(3)(9)(12)", "BusinessIntelligenceCRM", 4));

        return perfilAcesso;
    }

    public void gravarPerfilAcessoConsultor() throws Exception {
        // pesquisa por esse codigo antes de gravar
        List<PerfilAcesso> listaAcessosCons = getFacade().getPerfilAcesso().consultarPorNome("CONSULTOR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        // perfil CONSULTOR
        if (listaAcessosCons.isEmpty()) {
            PerfilAcessoVO perfilAcesso = new PerfilAcessoVO();
            perfilAcesso.setNome("CONSULTOR");
            perfilAcesso.setPermissaoVOs(obterPermissoesConsultor());
            getFacade().getPerfilAcesso().incluirSemCommit(perfilAcesso);
        }
    }

    /**
     * Seta os atributos padrões do perfil GERENTE e grava no banco
     *
     * @throws Exception
     */
    public void gravarPerfilAcessoGerente() throws Exception {
        // pesquisa por esse codigo antes de gravar
        List<PerfilAcesso> listaAcessosCons = getFacade().getPerfilAcesso().consultarPorNome("GERENTE", false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        // perfil GERENTE
        if (listaAcessosCons.isEmpty()) {
            PerfilAcessoVO perfilAcesso = new PerfilAcessoVO();
            perfilAcesso.setNome("GERENTE");
            perfilAcesso.setPermissaoVOs(obterPermissoesGerente());
            getFacade().getPerfilAcesso().incluirSemCommit(perfilAcesso);
        }
    }

    /**
     * Seta os atributos padrões do perfil PROFESSOR e grava no banco
     *
     * @throws Exception
     *
     */
    public void gravarPerfilAcessoProfessor() throws Exception {
        // pesquisa por esse codigo antes de gravar
        List<PerfilAcesso> listaAcessosCons = getFacade().getPerfilAcesso().consultarPorNome("PROFESSOR", false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        // perfil PROFESSOR
        if (listaAcessosCons.isEmpty()) {
            PerfilAcessoVO perfilAcesso = new PerfilAcessoVO();
            perfilAcesso.setNome("PROFESSOR");
            perfilAcesso.setPermissaoVOs(obterPermissoesProfessor());
            getFacade().getPerfilAcesso().incluirSemCommit(perfilAcesso);
        }
    }

    /**
     * Inicia a geração de matricula com o valor zero
     *
     * @param numeroMatricula
     * @throws SQLException
     * @throws Exception
     *
     */
    public void gravarNumeroMatricula(int numeroMatricula) throws Exception {
        boolean existeMatricula = this.consultarExisteNumeroMatricula();
        if (!existeMatricula) {
            try {
                String sql = "insert into numeroMatricula values(?)";
                PreparedStatement sqlInserir = con.prepareStatement(sql);
                sqlInserir.setInt(1, numeroMatricula);
                sqlInserir.execute();

            } catch (Exception e) {
                throw e;
            }
        }
    }

    public boolean consultarExisteNumeroMatricula() throws SQLException,
            Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "select * from numeromatricula";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return true;
        }
        return false;
    }

    /**
     * Grava o primeiro registro do robô
     *
     * @throws Exception
     */
    public void gravarRegistroRobo(Date data, Date dataHoraInicio,
            Date dataHoraFim, String texto) throws Exception {
        boolean existeRobo = getFacade().getRobo().consultarRotinaRobo(
                Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        if (!existeRobo) {
            roboVO.setDia(data);
            roboVO.setDataHoraInicio(dataHoraInicio);
            roboVO.setDataHoraFim(dataHoraFim);
            roboVO.setTexto(texto);
            roboVO.setRotinaProcessada(true);
            getFacade().getRobo().incluir(roboVO);
        }
    }

    public void gravarCategoriasProdutos() throws Exception {
        gravarCategoriaProduto("DEVOLUÇÃO DINHEIRO");
        gravarCategoriaProduto("QUITAÇÃO CANCELAMENTO");
        gravarCategoriaProduto("CONTA CORRENTE ALUNO");
        gravarCategoriaProduto("FAZ PARTE DOS PLANOS");
        gravarCategoriaProduto("SERVIÇOS");
        gravarCategoriaProduto("SUPLEMENTOS OU ALIMENTOS");
        gravarCategoriaProduto("LOJA");
    }

    /**
     * @throws Exception
     */
    public void gravarCategoriaProduto(String descricao) throws Exception {
        List<CategoriaProdutoVO> listaCategoria = getFacade().getCategoriaProduto().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaCategoria.isEmpty()) {
            CategoriaProdutoVO categoriaProdutoVO = new CategoriaProdutoVO();
            categoriaProdutoVO.setDescricao(descricao);
            getFacade().getCategoriaProduto().incluir(categoriaProdutoVO);
        }
    }

    public void gravarProdutos() throws Exception {
        int codigoCategoriaDevolucao = getFacade().getCategoriaProduto().consultarPorDescricao("DEVOLUÇÃO DINHEIRO"); //1
        int codigoCategoriaQuitacao = getFacade().getCategoriaProduto().consultarPorDescricao("QUITAÇÃO CANCELAMENTO"); //2
        int codigoContaCorrenteAluno = getFacade().getCategoriaProduto().consultarPorDescricao("CONTA CORRENTE ALUNO"); //3
        int codigoCategoriaServicos = getFacade().getCategoriaProduto().consultarPorDescricao("SERVIÇOS"); //5
        int codigoCategoriaFazParteDosPlanos = getFacade().getCategoriaProduto().consultarPorDescricao("FAZ PARTE DOS PLANOS"); //9

        gravarProduto(false, "RD", null, 0.0, 0.0, 0, null, null, "", "DEVOLUÇÃO DE RECEBÍVEIS - CANCELAMENTO", null);
        gravarProduto(false, "CC", null, 0.0, 0.0, 0, null, null, "", "DEPÓSITO CONTA CORRENTE ALUNO", codigoContaCorrenteAluno);
        gravarProduto(false, "SS", null, null, 0.0, null, null, null, "", "PRODUTO GENÉRICO", null);
        gravarProduto(false, "AC", null, 0.0, 0.0, 0, null, null, "", "ACERTO CONTA CORRENTE ALUNO", codigoContaCorrenteAluno);
        gravarProduto(false, "DV", null, 0.0, 0.0, 0, null, null, "", "DEVOLUÇÃO DE DINHEIRO - CANCELAMENTO", codigoCategoriaDevolucao);
        gravarProduto(false, "QU", null, 0.0, 0.0, 0, null, null, "", "QUITAÇÃO DE DINHEIRO - CANCELAMENTO", codigoCategoriaQuitacao);
        gravarProduto(false, "MM", null, 0.0, 0.0, 0, null, null, "", "MANUTENÇÃO - MODALIDADE", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "AH", null, 0.0, 0.0, 0, null, null, "", "ALTERAR - HORÁRIO", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "PM", null, 0.0, 0.0, 0, null, null, "", "PLANO", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "MA", null, 0.0, 0.0, 30, null, null, "", "MATRÍCULA", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "RE", null, 0.0, 0.0, 20, null, null, "", "REMATRÍCULA", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "RN", null, 0.0, 0.0, 0, null, null, "", "RENOVAÇÃO", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "DE", null, 0.0, 0.0, 0, null, null, "", "DESCONTO EXTRA", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "SE", null, 0.0, 0.0, 0, null, null, "", "RECEBER DÉBITO", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "SE", null, 0.0, 0.0, 0, null, null, "", "CUSTO ADMINISTRATIVO DO CANCELAMENTO", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "SE", null, 0.0, 0.0, 90, null, null, "ID", "AVALIAÇÃO FÍSICA", codigoCategoriaServicos);
        gravarProduto(false, "AA", null, 0.0, 0.0, 1, null, null, "ID", "VOCÊ DEVE CRIAR A AULA NO CADASTRO DE PRODUTOS", codigoCategoriaServicos);
        gravarProduto(false, "DI", null, 0.0, 0.0, 7, null, null, "ID", "VOCÊ DEVE CRIAR A DIARIA NO CADASTRO DE PRODUTOS", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "FR", null, 0.0, 0.0, 1, null, null, "ID", "1 DIA DE AULA EXPERIMENTAL", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "FR", null, 0.0, 0.0, 3, null, null, "ID", "3 DIAS DE AULA EXPERIMENTAL", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "DR", null, 0.0, 0.0, 0, null, null, "", "DESCONTO POR RENOVAÇÃO ANTECIPADA", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "FR", null, 0.0, 0.0, 7, null, null, "ID", "7 DIAS DE AULA EXPERIMENTAL", codigoCategoriaFazParteDosPlanos);
        gravarProduto(false, "DC", null, 0.0, 0.0, 0, null, null, "", "DEVOLUÇÃO DE CRÉDITO - CONTA CORRENTE DO CLIENTE", null);
        gravarProduto(false, "AT", null, null, null, null, null, null, "VV", "ATESTADO DE APTIDÃO FÍSICA", codigoContaCorrenteAluno);
        gravarProduto(false, "CP", null, 0.0, 0.0, 0, null, null, "", "CRÉDITO DE PERSONAL INTERNO", codigoCategoriaServicos);
        gravarProduto(false, "CP", null, 0.0, 0.0, 0, null, null, "", "CRÉDITO DE PERSONAL EXTERNO", codigoCategoriaServicos);
    }

    /**
     * Seta os valores dos atributos passados como parâmetro e grava no banco
     *
     * @throws Exception
     */
    public void gravarProduto(boolean desativado, String tipoProduto,
            DescontoVO desconto, Double valorBaseCalculo, Double valorFinal,
            Integer nrDiasVigencia, Date dataFinalVigenciaFixa,
            Date dataInicioVigencia, String tipoVigencia, String descricao,
            Integer categoriaProduto) throws Exception {
        List<ProdutoVO> listaProdutoVO = getFacade().getProduto().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaProdutoVO.isEmpty()) {
            ProdutoVO produto = new ProdutoVO();
            produto.setDesativado(desativado);
            produto.setTipoProduto(tipoProduto);
            produto.setDesconto(desconto);
            produto.setValorBaseCalculo(valorBaseCalculo);
            produto.setValorFinal(valorFinal);
            produto.setNrDiasVigencia(nrDiasVigencia);
            produto.setDataFinalVigencia(dataFinalVigenciaFixa);
            produto.setDataInicioVigencia(dataInicioVigencia);
            produto.setTipoVigencia(tipoVigencia);
            produto.setDescricao(descricao);
            produto.setCategoriaProduto(new CategoriaProdutoVO());
            produto.getCategoriaProduto().setCodigo(categoriaProduto);

            getFacade().getProduto().incluir(produto);
            listaProdutos.add(produto);
        } else {
            listaProdutos.addAll(listaProdutoVO);
        }
    }

    public void gravarPais() throws Exception {
        incluirEstadoNaListaEstados("GO", "GOIÁS");
        incluirEstadoNaListaEstados("SP", "SÃO PAULO");
        incluirEstadoNaListaEstados("RJ", "RIO DE JANEIRO");
        incluirEstadoNaListaEstados("MG", "MINAS GERAIS");
        incluirEstadoNaListaEstados("MT", "MATO GROSSO");
        incluirEstadoNaListaEstados("RS", "RIO GRANDE DO SUL");
        incluirEstadoNaListaEstados("AC", "ACRE");
        incluirEstadoNaListaEstados("AL", "ALAGOAS");
        incluirEstadoNaListaEstados("AP", "AMAPÁ");
        incluirEstadoNaListaEstados("AM", "AMAZONAS");
        incluirEstadoNaListaEstados("BA", "BAHIA");
        incluirEstadoNaListaEstados("CE", "CEARÁ");
        incluirEstadoNaListaEstados("DF", "DISTRITO FEDERAL");
        incluirEstadoNaListaEstados("ES", "ESPÍRITO SANTO");
        incluirEstadoNaListaEstados("RR", "RORAIMA");
        incluirEstadoNaListaEstados("MA", "MARANHÃO");
        incluirEstadoNaListaEstados("PA", "PARÁ");
        incluirEstadoNaListaEstados("PB", "PARAÍBA");
        incluirEstadoNaListaEstados("PR", "PARANÁ");
        incluirEstadoNaListaEstados("PE", "PERNAMBUCO");
        incluirEstadoNaListaEstados("PI", "PIAUÍ");
        incluirEstadoNaListaEstados("RN", "RIO GRANDE DO NORTE");
        incluirEstadoNaListaEstados("RO", "RONDÔNIA");
        incluirEstadoNaListaEstados("TO", "TOCANTINS");
        incluirEstadoNaListaEstados("SC", "SANTA CATARINA");
        incluirEstadoNaListaEstados("SE", "SERGIPE");
        incluirEstadoNaListaEstados("MS", "MATO GROSSO DO SUL");
        // insere pais - Brasil
        gravarPaisComEstados("BRASIL", listaEstados);
        gravarCidades();

    }

    /**
     * Inclui estado na listaEstados atributo dessa classe
     */
    public void incluirEstadoNaListaEstados(String sigla, String nome) {
        EstadoVO estadoVO = new EstadoVO();
        estadoVO.setSigla(sigla);
        estadoVO.setDescricao(nome);
        listaEstados.add(estadoVO);
    }

    /**
     * Gravando pais passando uma lista de estados que pertencem a mesma
     */
    public void gravarPaisComEstados(String nome, List<EstadoVO> listaEstados) throws Exception {
        PaisVO pais = getFacade().getPais().consultarPorNome(nome, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (pais == null) {
            paisVO = new PaisVO();
            paisVO.setNome(nome);
            paisVO.setEstadoVOs(listaEstados);
            getFacade().getPais().incluirSemCommit(paisVO);
        } else {
            paisVO = pais;
        }
    }

    public void gravarCidades() throws Exception {
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("GO", listaEstados), "GOIÂNIA", "GOIANIA");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("MG", listaEstados), "BELO HORIZONTE", "BELO HORIZONTE");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("SP", listaEstados), "SÃO PAULO", "SAO PAULO");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("RJ", listaEstados), "RIO DE JANEIRO", "RIO DE JANEIRO");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("PA", listaEstados), "BELÉM", "BELEM");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("PE", listaEstados), "RECIFE", "RECIFE");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("DF", listaEstados), "BRASÍLIA", "BRASILIA");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("DF", listaEstados), "TAGUATINGA", "TAGUATINGA");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("DF", listaEstados), "CRUZEIRO", "CRUZEIRO");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("MT", listaEstados), "CUIABÁ", "CUIABA");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("MT", listaEstados), "CAMPO GRANDE", "CAMPO GRANDE");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("TO", listaEstados), "PALMAS", "PALMAS");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("MG", listaEstados), "UBERLÂNDIA", "UBERLANDIA");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("MG", listaEstados), "UBERABA", "UBERABA");
        gravarCidade(paisVO.getCodigo(), obterCodigoEstado("MG", listaEstados), "LAVRAS", "LAVRAS");
    }

    public int obterCodigoEstado(String sigla, List<EstadoVO> listaEstados) {
        for (EstadoVO estado : listaEstados) {
            if (estado.getSigla() != null && !estado.getSigla().isEmpty() && estado.getSigla().equalsIgnoreCase(sigla)) {
                if (estado.getCodigo() != null && estado.getCodigo() != 0) {
                    return estado.getCodigo();
                }
            }
        }
        return 0;
    }

    public void gravarCidade(int pais, int estado, String nome, String nomesemacento) throws Exception {
        try {
            CidadeVO cidadeVO = new CidadeVO();
            cidadeVO.setPais(new PaisVO());
            cidadeVO.getPais().setCodigo(pais);
            cidadeVO.setEstado(new EstadoVO());
            cidadeVO.getEstado().setCodigo(estado);
            cidadeVO.setNome(nome);
            cidadeVO.setNomeSemAcento(nomesemacento);
            getFacade().getCidade().incluir(cidadeVO);
        } catch (Exception ex) {
            Logger.getLogger(RoboControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void gravarAmbientes() throws Exception {
        //tipo modulo (TU para turma do ZW), (CE para o modulo Central de Eventos), (SS para modulo estudio)
        gravarAmbiente("PISCINA (TURMA)", 40, "TU", 1);
        gravarAmbiente("ESTÚDIO (SESSÃO)", 4, "SS", 1);
    }

    /**
     * Seta os dados do ambiente e grava no banco
     */
    public void gravarAmbiente(String descricao, Integer capacidade, String tipoModulo, int situacaoAmbiente) throws Exception {
        List<AmbienteVO> listaAmbiente = getFacade().getAmbiente().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaAmbiente.isEmpty()) {
            AmbienteVO ambienteVO = new AmbienteVO();
            ambienteVO.setDescricao(descricao);
            ambienteVO.setCapacidade(capacidade);
            ambienteVO.setTipoModulo(tipoModulo);
            ambienteVO.setSituacaoAmbiente(situacaoAmbiente);
            getFacade().getAmbiente().incluir(ambienteVO);
        }
    }

    public void gravarCategoriaClientes() throws Exception {
        gravarCategoria(0, "AL", "ALUNO");
        gravarCategoria(0, "AL", "VISITANTE AULA EXPERIMENTAL");
        gravarCategoria(0, "SO", "SÓCIO CLUBE");
        gravarCategoria(0, "AL", "CLIENTE DE PERSONAL INTERNO");
        gravarCategoria(0, "AL", "CLIENTE DE PERSONAL EXTERNO");
    }

    /**
     * Grava categoria de clientes padrões
     */
    public void gravarCategoria(int nrConvitePermitido, String tipoCategoria, String nome) throws Exception {
        List<CategoriaVO> listaCategoria = getFacade().getCategoria().consultarPorNome(nome, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaCategoria.isEmpty()) {
            CategoriaVO categoriaVO = new CategoriaVO();
            categoriaVO.setNrConvitePermitido(0);
            categoriaVO.setNome(nome);
            categoriaVO.setTipoCategoria(tipoCategoria);
            getFacade().getCategoria().incluir(categoriaVO);
        }
    }

    public void gravarPerguntasQuestionario() throws Exception {
        // //1ª Pergunta inserindo respostas da primeira pergunta
        List<RespostaPerguntaVO> listaRespostasPerguntas1 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("EMAGRECIMENTO", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("AUMENTO DE MASSA", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("DEFINIÇÃO MUSCULAR", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("QUALIDADE DE VIDA", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("CONDICIONAMENTO", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("ALÍVIO DE STRESS", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("REABILITAÇÃO", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("LAZER", listaRespostasPerguntas1);
        incluirRespostasPerguntasNaListaRespostas("FAZER AMIGOS", listaRespostasPerguntas1);

        // gravando pergunta com as respostas da lista acima
        gravarPergunta("ME", "QUAIS SÃO SEUS OBJETIVOS?", listaRespostasPerguntas1);

        // 2ª Pergunta
        // inserindo respostas da segunda pergunta
        List<RespostaPerguntaVO> listaRespostasPerguntas2 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("MENOS DE UM MÊS", listaRespostasPerguntas2);
        incluirRespostasPerguntasNaListaRespostas("MAIS OU MENOS 3 MESES", listaRespostasPerguntas2);
        incluirRespostasPerguntasNaListaRespostas("MAIS OU MENOS 6 MESES", listaRespostasPerguntas2);
        incluirRespostasPerguntasNaListaRespostas("MAIS OU MENOS 1 ANO", listaRespostasPerguntas2);
        incluirRespostasPerguntasNaListaRespostas("A MAIS DE UM ANO", listaRespostasPerguntas2);

        // gravando pergunta com as respostas da lista acima
        // listaRespostasPerguntas1
        gravarPergunta("SE", "HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?", listaRespostasPerguntas2);

        // 3ª Pergunta
        // gravando pergunta que não possui resposta, pois é uma caixa de texto
        // que o usuário preenche
        gravarPergunta("TE", "O QUE TE FEZ PARAR DA ÚLTIMA VEZ?", new ArrayList<RespostaPerguntaVO>());

        // 4ª Pergunta
        // inserindo respostas para a quarta pergunta
        List<RespostaPerguntaVO> listaRespostasPerguntas4 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("TEMPO", listaRespostasPerguntas4);
        incluirRespostasPerguntasNaListaRespostas("FINANCEIRO", listaRespostasPerguntas4);
        incluirRespostasPerguntasNaListaRespostas("DISPOSIÇÃO", listaRespostasPerguntas4);

        // gravando pergunta com as respostas
        gravarPergunta("ME", "O QUE TE IMPEDIU DE COMEÇAR ANTES?", listaRespostasPerguntas4);

        // 5ª pergunta
        // inserindo respostas para as perguntas
        List<RespostaPerguntaVO> listaRespostasPerguntas5 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("MUSCULAÇÃO", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("CORRIDA", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("STEP", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("ABDOMINAL", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("GLÚTEO", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("PILATES", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("PUMP", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("BODY COMBAT", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("SPINNING", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("JUMP", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("LUTAS", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("RITMOS", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("ALONGAMENTO", listaRespostasPerguntas5);
        incluirRespostasPerguntasNaListaRespostas("TREIN. FUNCIONAL", listaRespostasPerguntas5);

        // gravando pergunta com as respostas da lista acima
        gravarPergunta("ME", "QUAIS AS ATIVIDADES DE INTERESSE?", listaRespostasPerguntas5);

        // 6ª pergunta
        // inserindo respostas para as perguntas
        List<RespostaPerguntaVO> listaRespostasPerguntas6 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("3 MESES", listaRespostasPerguntas6);
        incluirRespostasPerguntasNaListaRespostas("6 MESES", listaRespostasPerguntas6);
        incluirRespostasPerguntasNaListaRespostas("9 MESES", listaRespostasPerguntas6);
        incluirRespostasPerguntasNaListaRespostas("12 MESES", listaRespostasPerguntas6);
        incluirRespostasPerguntasNaListaRespostas("MAIS", listaRespostasPerguntas6);

        // gravando pergunta com as respostas da lista acima
        gravarPergunta("SE", "EM QUANTO TEMPO DESEJA ALCANÇAR SEUS OBJETIVOS?", listaRespostasPerguntas6);

        // 7ª pergunta
        // inserindo respostas para as perguntas
        List<RespostaPerguntaVO> listaRespostasPerguntas7 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("AMIGOS", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("PASSANDO EM FRENTE", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("EX-ALUNO", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("MALA DIRETA", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("LISTA TELEFÔNICA", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("FOLDER", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("OUTDOOR", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("INTERNET", listaRespostasPerguntas7);
        incluirRespostasPerguntasNaListaRespostas("OUTROS", listaRespostasPerguntas7);
        // gravando pergunta com as respostas da lista acima
        gravarPergunta("ME", "COMO CONHECEU A ACADEMIA?", listaRespostasPerguntas7);

        // 8ª pergunta
        // inserindo respostas para as perguntas
        List<RespostaPerguntaVO> listaRespostasPerguntas8 = new ArrayList<RespostaPerguntaVO>();
        incluirRespostasPerguntasNaListaRespostas("MANHÃ", listaRespostasPerguntas8);
        incluirRespostasPerguntasNaListaRespostas("TARDE", listaRespostasPerguntas8);
        incluirRespostasPerguntasNaListaRespostas("NOITE", listaRespostasPerguntas8);

        gravarPergunta("ME", "PERÍODO RESERVADO PARA TREINAR", listaRespostasPerguntas8);

        // ----------------------------------------------Cadastro de
        // Questionário com as
        // perguntas-----------------------------------------------------------
       /* int pergunta1 = getFacade().getPergunta().consultarPorDescricao("QUAIS SÃO SEUS OBJETIVOS?");
         int pergunta2 = getFacade().getPergunta().consultarPorDescricao("HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?");
         int pergunta3 = getFacade().getPergunta().consultarPorDescricao("O QUE TE FEZ PARAR DA ÚLTIMA VEZ?");
         int pergunta4 = getFacade().getPergunta().consultarPorDescricao("O QUE TE IMPEDIU DE COMEÇAR ANTES?");
         int pergunta5 = getFacade().getPergunta().consultarPorDescricao("QUAIS AS ATIVIDADES DE INTERESSE? ");
         int pergunta6 = getFacade().getPergunta().consultarPorDescricao("EM QUANTO TEMPO DESEJA ALCANÇAR SEUS OBJETIVOS?");
         int pergunta7 = getFacade().getPergunta().consultarPorDescricao("COMO CONHECEU A ACADEMIA?");
         */
        questionario1 = getFacade().getQuestionario().consultarPorDescricao("BV MATRICULA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        QuestionarioVO questionario2 = getFacade().getQuestionario().consultarPorDescricao("BV REMATRICULA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        QuestionarioVO questionario3 = getFacade().getQuestionario().consultarPorDescricao("BV RETORNO", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(0).getCodigo(), questionario2);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(1).getCodigo(), questionario2);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(2).getCodigo(), questionario2);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(3).getCodigo(), questionario3);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(0).getCodigo(), questionario3);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(1).getCodigo(), questionario3);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(4).getCodigo(), questionario1);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(0).getCodigo(), questionario1);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(5).getCodigo(), questionario1);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(3).getCodigo(), questionario1);
        gravarPerguntasRespostasQuestionario(listaPerguntaVOs.get(6).getCodigo(), questionario1);
    }

    /**
     * Seta os valores dos atributos do RespostaPerguntaVO
     */
    public void incluirRespostasPerguntasNaListaRespostas(String resposta, List<RespostaPerguntaVO> listaRespostasPerguntas) {
        RespostaPerguntaVO respostaPergunta = new RespostaPerguntaVO();
        respostaPergunta.setDescricaoRespota(resposta);
        listaRespostasPerguntas.add(respostaPergunta);
    }

    /**
     * Grava perguntas no banco de dados
     */
    public void gravarPergunta(String tipoPergunta, String descricao, List<RespostaPerguntaVO> respostasPerguntasVOs) throws Exception {
        List<PerguntaVO> listaPerguntaVO = getFacade().getPergunta().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaPerguntaVO.isEmpty()) {
            PerguntaVO perguntaVO = new PerguntaVO();
            perguntaVO.setTipoPergunta(tipoPergunta);
            perguntaVO.setDescricao(descricao);
            perguntaVO.setRespostaPerguntaVOs(respostasPerguntasVOs);
            getFacade().getPergunta().incluirSemCommit(perguntaVO);
            listaPerguntaVOs.add(perguntaVO);
        } else {
            listaPerguntaVOs.addAll(listaPerguntaVO);
        }
    }

    /**
     * Grava perguntas e respostas dos questionarios
     */
    public void gravarPerguntasRespostasQuestionario(Integer codPergunta, QuestionarioVO questionario) throws Exception {
        Boolean jaCadastrado = getFacade().getQuestionarioPergunta().consultarQuestionarioPerguntasPorQuestionarioEPergunta(
                questionario.getCodigo(), codPergunta,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!jaCadastrado) {
            QuestionarioPerguntaVO questionarioPerguntaVO = new QuestionarioPerguntaVO();
            questionarioPerguntaVO.setPergunta(new PerguntaVO());
            questionarioPerguntaVO.getPergunta().setCodigo(codPergunta);
            questionarioPerguntaVO.setQuestionario(questionario.getCodigo());
            getFacade().getQuestionarioPergunta().incluir(questionarioPerguntaVO);
        }
    }

    /**
     * Grava uma empresa padrão específica
     */
    public void gravarEmpresa() throws Exception {
        List<EmpresaVO> listaEmpresa = getFacade().getEmpresa().consultarPorNome("ACADEMIA NOME",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaEmpresa.isEmpty()) {
            empresaVO.setNrDiasAvencer(10);
            empresaVO.setToleranciaPagamento(5);
            empresaVO.setQtdFaltaPeso1(4);
            empresaVO.setQtdFaltaInicioPeso2(5);
            empresaVO.setQtdFaltaTerminoPeso2(7);
            empresaVO.setQtdFaltaPeso3(8);
            empresaVO.setFotoRelatorio(new byte[0]);
            empresaVO.setFoto(new byte[0]);
            empresaVO.setAlturaFotoEmpresa("160");
            empresaVO.setAlturaFotoRelatorio("56");
            empresaVO.setLarguraFotoEmpresa("120");
            empresaVO.setLarguraFotoRelatorio("150");
            empresaVO.setCarenciaRenovacao(15);
            empresaVO.setMascaraMatricula("XXXXXX");
            empresaVO.setNrDiasVigenteQuestionarioVista(30);
            empresaVO.setNrDiasVigenteQuestionarioRetorno(30);
            empresaVO.setNrDiasVigenteQuestionarioRematricula(30);
            empresaVO.setPermiteContratosConcomintante(false);
            empresaVO.setPermiteSituacaoAtestadoContrato(true);
            empresaVO.getQuestionarioReMatricula().setCodigo(2);
            empresaVO.getQuestionarioRetorno().setCodigo(3);
            empresaVO.getQuestionarioPrimeiraVisita().setCodigo(1);
            empresaVO.setJuroParcela(0.0);
            empresaVO.setMulta(0.0);
            empresaVO.setFax("");
            empresaVO.setSite("HTTP://WWW.PACTO.VC");
            empresaVO.setEmail("<EMAIL>");
            empresaVO.setTelComercial1("(62)32515820");
            empresaVO.setTelComercial2("");
            empresaVO.setTelComercial3("");
            empresaVO.setInscEstadual("ISENTO");
            empresaVO.setCNPJ("11.111.111/1111-11");
            empresaVO.setCEP("74.270-170");
            empresaVO.setEstado(new EstadoVO());
            empresaVO.getEstado().setCodigo(1);
            empresaVO.setCidade(new CidadeVO());
            empresaVO.getCidade().setCodigo(1);
            empresaVO.setPais(paisVO);
            empresaVO.setComplemento("");
            empresaVO.setNumero("246");
            empresaVO.setSetor("JARDIM AMÉRICA");
            empresaVO.setEndereco("RUA C 200");
            empresaVO.setRazaoSocial("PACTO SOFTWARE E GESTÃO LTDA");
            empresaVO.setNome("PACTO SOFTWARE E GESTÃO");
            empresaVO.setCarencia(5);
            empresaVO.setNrDiasProrata(20);
            empresaVO.setSomaDv(0);
            empresaVO.setQtdDiasCobrarRematricula(90);
            getFacade().getEmpresa().incluir(empresaVO);
        } else {
            empresaVO = listaEmpresa.get(0);
        }
    }

    public void gravarModalidades() throws Exception {
        gravarModalidade(false, true, true, 150.0, true, "IMPORTAÇÃO", 5);
        gravarModalidade(false, false, false, 150.0, true, "FITNESS", 7);
    }

    /**
     * Grava modalidades padrões no banco de dados
     */
    public void gravarModalidade(Boolean modalidadeDefault, Boolean utilizarTurma, Boolean utilizarProduto, Double valorMensal,
            boolean ativo, String nome, int nrVezes) throws Exception {
        List<ModalidadeVO> modalidadeVOs = getFacade().getModalidade().consultarPorNome(nome, 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (modalidadeVOs.isEmpty()) {
            ModalidadeVO modalidadeVO = new ModalidadeVO();
            modalidadeVO.setModalidadeDefault(modalidadeDefault);
            modalidadeVO.setUtilizarTurma(utilizarTurma);
            modalidadeVO.setUtilizarProduto(utilizarProduto);
            modalidadeVO.setValorMensal(valorMensal);
            modalidadeVO.setAtivo(ativo);
            modalidadeVO.setNome(nome);
            modalidadeVO.setNrVezes(nrVezes);
            getFacade().getModalidade().incluirSemCommit(modalidadeVO);
            listaModalidades.add(modalidadeVO);
        } else {
            listaModalidades.addAll(modalidadeVOs);
        }
    }

    public void gravarModalidadesEmpresa() throws Exception {
        gravarModalidadeEmpresa(empresaVO.getCodigo(), listaModalidades);
    }

    /**
     * Grava as modalidades vinculadas a empresa
     */
    public void gravarModalidadeEmpresa(int empresa, List<ModalidadeVO> listaModalidadeVOs)
            throws Exception {
        List<ModalidadeEmpresaVO> modalidadeEmpresaVOs = getFacade().getModalidadeEmpresa().consultarPorNomeEmpresa(empresaVO.getNome(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (modalidadeEmpresaVOs.isEmpty()) {
            for (ModalidadeVO mod : listaModalidadeVOs) {
                ModalidadeEmpresaVO modalidadeEmpresaVO = new ModalidadeEmpresaVO();
                modalidadeEmpresaVO.setEmpresa(new EmpresaVO());
                modalidadeEmpresaVO.getEmpresa().setCodigo(empresa);
                modalidadeEmpresaVO.setModalidade(mod.getCodigo());
                getFacade().getModalidadeEmpresa().incluir(modalidadeEmpresaVO);
            }
        }
    }

    public void gravarComposicoes() throws Exception {
        gravarComposicao(false, false, 150.00, "PACOTE FITNESS", empresaVO.getCodigo());
    }

    /**
     * Grava composicao
     */
    public void gravarComposicao(boolean composicaoDefault, boolean composicaoAdicional, Double precoComposicao, String descricao, int codEmpresa) throws Exception {
        List<ComposicaoVO> composicaoVOs = getFacade().getComposicao().consultarPorDescricao(descricao, codEmpresa, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (composicaoVOs.isEmpty()) {
            ComposicaoVO composicaoVO = new ComposicaoVO();
            composicaoVO.setComposicaoDefault(composicaoDefault);
            composicaoVO.setComposicaoAdicional(composicaoAdicional);
            composicaoVO.setPrecoComposicao(precoComposicao);
            composicaoVO.setDescricao(descricao);
            composicaoVO.setEmpresa(new EmpresaVO());
            composicaoVO.getEmpresa().setCodigo(codEmpresa);
            getFacade().getComposicao().incluirSemCommit(composicaoVO, Boolean.FALSE);
            listaComposicao.add(composicaoVO);
        } else {
            listaComposicao.addAll(composicaoVOs);
        }
    }

    public void gravarComposicoesModalidade() throws Exception {
        gravarComposicaoModalidade(75.0, 150.0, listaComposicao.get(0).getCodigo(), listaModalidades.get(0).getCodigo());
        gravarComposicaoModalidade(75.0, 150.0, listaComposicao.get(0).getCodigo(), listaModalidades.get(1).getCodigo());
    }

    /**
     * Grava o vinculo da composicao e da modalidade padrão
     */
    public void gravarComposicaoModalidade(Double valormensalcomposicao, Double precomodalidade, int composicao, int modalidade) throws Exception {
        Boolean jaCadastrado = getFacade().getComposicaoModalidade().consultarPorCodigoModalidadeComposicao(modalidade, composicao);
        if (!jaCadastrado) {
            ComposicaoModalidadeVO composicaoModalidadeVO = new ComposicaoModalidadeVO();
            composicaoModalidadeVO.setValorMensalComposicao(valormensalcomposicao);
            composicaoModalidadeVO.setPrecoModalidade(precomodalidade);
            composicaoModalidadeVO.setComposicao(composicao);
            composicaoModalidadeVO.setModalidade(new ModalidadeVO());
            composicaoModalidadeVO.getModalidade().setCodigo(modalidade);
            getFacade().getComposicaoModalidade().incluir(composicaoModalidadeVO, true);
        }
    }

    public void gravarFormasPagamento() throws Exception {
        gravarFormaPagamento(0.0, "AV", new ConvenioCobrancaVO(), "DINHEIRO", true, false, false);
        gravarFormaPagamento(0.0, "CA", new ConvenioCobrancaVO(), "CARTÃO DE CRÉDITO", true, false, true);
        gravarFormaPagamento(0.0, "CD", new ConvenioCobrancaVO(), "CARTÃO DE DÉBITO", true, false, true);
        gravarFormaPagamento(0.0, "CH", new ConvenioCobrancaVO(), "CHEQUE", true, false, true);
        gravarFormaPagamento(0.0, "CC", new ConvenioCobrancaVO(), "CONTA CORRENTE DO CLIENTE", true, false, false);
    }

    /**
     * Grava formas de pagamento padrões
     */
    public void gravarFormaPagamento(Double taxaCartao, String tipoFormaPagamento, ConvenioCobrancaVO convenioCobranca,
            String descricao, boolean ativo, boolean somenteFinanceiro, boolean compensacaoDiasUteis) throws Exception {
        List<FormaPagamentoVO> listaFormaPagamento = getFacade().getFormaPagamento().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaFormaPagamento.isEmpty()) {
            FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
            formaPagamento.setTaxaCartao(taxaCartao);
            formaPagamento.setTipoFormaPagamento(tipoFormaPagamento);
            formaPagamento.setConvenioCobrancaVO(convenioCobranca);
            formaPagamento.setDescricao(descricao);
            formaPagamento.setSomenteFinanceiro(somenteFinanceiro);
            formaPagamento.setAtivo(ativo);
            formaPagamento.setCompensacaoDiasUteis(compensacaoDiasUteis);
            getFacade().getFormaPagamento().incluir(formaPagamento);
        }
    }

    public void gravarCondicoesPagamento() throws Exception {
        gravarCondicaoPagamento(0, 0.0, false, false, 1, "A VISTA: DINHEIRO ");
        gravarCondicaoPagamento(0, 0.0, false, false, 1, "A VISTA: CHEQUE OU CARTÃO");
        gravarCondicaoPagamento(30, 0.0, false, true, 2, "EM 2 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 3, "EM 3 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 4, "EM 4 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 5, "EM 5 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 6, "EM 6 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 7, "EM 7 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 8, "EM 8 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 9, "EM 9 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 10, "EM 10 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 11, "EM 11 VEZES");
        gravarCondicaoPagamento(30, 0.0, false, true, 12, "EM 12 VEZES");
    }

    public void gravarCondicaoPagamento(int intervaloEntreParcela, Double percentualValorEntrada, Boolean condicaoPagamentoDefault,
            Boolean entrada, int nrParcelas, String descricao) throws Exception {
        List<CondicaoPagamentoVO> listaCondicaoPagamentoCons = getFacade().getCondicaoPagamento().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaCondicaoPagamentoCons.isEmpty()) {
            CondicaoPagamentoVO condicaoPagamentoVO = new CondicaoPagamentoVO();
            condicaoPagamentoVO.setIntervaloEntreParcela(intervaloEntreParcela);
            condicaoPagamentoVO.setPercentualValorEntrada(percentualValorEntrada);
            condicaoPagamentoVO.setCondicaoPagamentoDefault(condicaoPagamentoDefault);
            condicaoPagamentoVO.setEntrada(entrada);
            condicaoPagamentoVO.setNrParcelas(nrParcelas);
            condicaoPagamentoVO.setDescricao(descricao);
            getFacade().getCondicaoPagamento().incluirSemCommit(condicaoPagamentoVO);
            listaCondicaoPagamento.add(condicaoPagamentoVO);
        } else {
            listaCondicaoPagamento.addAll(listaCondicaoPagamentoCons);
        }
    }

    public void gravarCondicoesPagamentoParcela() throws Exception {
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(1).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(1).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(2).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(2).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(2).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(3).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(3).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(3).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(3).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(4).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(4).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(4).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(4).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(4).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(5).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(5).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(5).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(180, 7, 0.0, listaCondicaoPagamento.get(6).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(180, 7, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(210, 8, 0.0, listaCondicaoPagamento.get(7).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(180, 7, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(210, 8, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(240, 9, 0.0, listaCondicaoPagamento.get(8).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(180, 7, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(210, 8, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(240, 9, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(270, 10, 0.0, listaCondicaoPagamento.get(9).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(180, 7, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(210, 8, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(240, 9, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(270, 10, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(300, 11, 0.0, listaCondicaoPagamento.get(10).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(30, 2, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(60, 3, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(90, 4, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(120, 5, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(150, 6, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(180, 7, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(210, 8, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(240, 9, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(270, 10, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(300, 11, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(330, 12, 0.0, listaCondicaoPagamento.get(11).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(12).getCodigo());
        gravarCondicoesPagamentoParcela(0, 1, 0.0, listaCondicaoPagamento.get(0).getCodigo());
    }

    public void gravarCondicoesPagamentoParcela(int nrDiasParcela, int nrParcela, Double percentualParcela, int codCondicaoPagamentoVO) throws Exception {
        List<CondicaoPagamentoParcelaVO> listaCondicaoPagamentoParcelaVOs = getFacade().getCondicaoPagamentoParcela().consultarCondicaoPagamentoParcelas(codCondicaoPagamentoVO,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaCondicaoPagamentoParcelaVOs.isEmpty()) {
            CondicaoPagamentoParcelaVO condicaoPagamentoParcelaVO = new CondicaoPagamentoParcelaVO();
            condicaoPagamentoParcelaVO.setNrDiasParcela(nrDiasParcela);
            condicaoPagamentoParcelaVO.setNrParcela(nrParcela);
            condicaoPagamentoParcelaVO.setCondicaoPagamento(codCondicaoPagamentoVO);
            getFacade().getCondicaoPagamentoParcela().incluir(condicaoPagamentoParcelaVO);
        }
    }

    public void gravarTiposDesconto() throws Exception {
        gravarTipoDesconto("MA", TipoDesconto.PE, 100.0, "100% NA MATRICULA");
        gravarTipoDesconto("RE", TipoDesconto.PE, 100.0, "100% NA REMATRÍCULA");
        gravarTipoDesconto("MA", TipoDesconto.PE, 50.0, "50% NA MATRICULA");
        gravarTipoDesconto("RE", TipoDesconto.PE, 50.0, "50% NA REMATRÍCULA");
    }

    public void gravarTipoDesconto(String tipoProduto, TipoDesconto tipoDesconto, Double valor, String descricao) throws Exception {
        List<DescontoVO> listaDescontos = getFacade().getDesconto().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaDescontos.isEmpty()) {
            DescontoVO descontoVO = new DescontoVO();
            descontoVO.setTipoProduto(tipoProduto);
            descontoVO.setTipoDesconto(tipoDesconto);
            descontoVO.setValor(valor);
            descontoVO.setDescricao(descricao);
            getFacade().getDesconto().incluir(descontoVO);
        }
    }

    public void gravarGrausInstrucao() throws Exception {
        gravarGrauInstrucao("SEM ESTUDO");
        gravarGrauInstrucao("ENSINO FUNDAMENTAL");
        gravarGrauInstrucao("ENSINO MÉDIO");
        gravarGrauInstrucao("ENSINO SUPERIOR");
        gravarGrauInstrucao("PÓS-GRADUAÇÃO");
        gravarGrauInstrucao("MESTRADO");
        gravarGrauInstrucao("DOUTORADO");
        gravarGrauInstrucao("PÓS-DOUTORADO");
    }

    public void gravarGrauInstrucao(String descricao) throws Exception {
        List<GrauInstrucaoVO> listaGrauInstrucao = getFacade().getGrauInstrucao().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaGrauInstrucao.isEmpty()) {
            GrauInstrucaoVO grauInstrucaoVO = new GrauInstrucaoVO();
            grauInstrucaoVO.setDescricao(descricao);
            getFacade().getGrauInstrucao().incluir(grauInstrucaoVO);
        }
    }

    public void gravarConveniosDesconto() throws Exception {
        gravarConvenioDesconto(false, false, Uteis.getDate("31/12/2025"), 1, Uteis.getDate("31/12/2025"), Uteis.getDate("02/11/2011"), Uteis.getDate("02/11/2011"), "CONVENIO DE DESCONTO");
    }

    public void gravarConvenioDesconto(Boolean isentarMatricula, Boolean isentarRematricula, Date dataAutorizacao,
            int codResponsavelAutorizacao, Date dataFinalVigencia, Date dataInicioVigencia, Date dataAssinatura, String descricao) throws Exception {
        List<ConvenioDescontoVO> convenioDescontoVOs = getFacade().getConvenioDesconto().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (convenioDescontoVOs.isEmpty()) {
            ConvenioDescontoVO convenioDescontoVO = new ConvenioDescontoVO();
            convenioDescontoVO.setIsentarMatricula(isentarMatricula);
            convenioDescontoVO.setIsentarRematricula(isentarRematricula);
            convenioDescontoVO.setDataAutorizacao(dataAutorizacao);
            convenioDescontoVO.setResponsavelAutorizacao(new UsuarioVO());
            convenioDescontoVO.getResponsavelAutorizacao().setCodigo(codResponsavelAutorizacao);
            convenioDescontoVO.setDataInicioVigencia(dataInicioVigencia);
            convenioDescontoVO.setDataFinalVigencia(dataFinalVigencia);
            convenioDescontoVO.setDataAssinatura(dataAssinatura);
            convenioDescontoVO.setDescricao(descricao);
            convenioDescontoVO.setEmpresa(empresaVO);
            getFacade().getConvenioDesconto().incluirSemCommit(convenioDescontoVO);

            listaConveniosDescontoVOs.add(convenioDescontoVO);
        } else {
            listaConveniosDescontoVOs.addAll(convenioDescontoVOs);
        }
    }

    public void gravarConveniosDescontoConfiguracao() throws Exception {
        gravarConvenioDescontoConfiguracao(listaConveniosDescontoVOs.get(0).getCodigo(), "VE", 0.0, 15.0, 1);
        gravarConvenioDescontoConfiguracao(listaConveniosDescontoVOs.get(0).getCodigo(), "VE", 0.0, 30.0, 3);
        gravarConvenioDescontoConfiguracao(listaConveniosDescontoVOs.get(0).getCodigo(), "VE", 0.0, 30.0, 6);
    }

    public void gravarConvenioDescontoConfiguracao(int codConvenioDesconto, String tipoDesconto, Double porcentagemDesconto, Double valorDesconto, int duracao) throws Exception {
        ConvenioDescontoConfiguracaoVO convenioDescontoConfiguracaoVO = new ConvenioDescontoConfiguracaoVO();
        convenioDescontoConfiguracaoVO.setConvenioDesconto(codConvenioDesconto);
        convenioDescontoConfiguracaoVO.setTipoDesconto(tipoDesconto);
        convenioDescontoConfiguracaoVO.setPorcentagemDesconto(porcentagemDesconto);
        convenioDescontoConfiguracaoVO.setValorDesconto(valorDesconto);
        convenioDescontoConfiguracaoVO.setDuracao(duracao);
        getFacade().getConvenioDescontoConfiguracao().incluir(convenioDescontoConfiguracaoVO);
    }

    public void gravarJustificativasOperacao() throws Exception {
        gravarJustificativaOperacao(empresaVO.getCodigo(), "BO", "AJUSTE DE IMPORTAÇÃO");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "BO", "AUTORIZAÇÃO DIRETORIA");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "AT", "ATESTADO DE LESÃO");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "AT", "ATESTADO DE GRIPE");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "AT", "ATESTADO DE RECOMENTAÇÃO MEDICA");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CA", "MUDANÇA DE CIDADE");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CA", "SEM JUSTIFICATIVA");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "TR", "TRANCAMENTO POR FALTA DE TEMPO");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "TR", "TRANCAMENTO POR VIAGEM LONGA");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "TR", "TRANCAMENTO POR FÉRIAS");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "BO", "DATAS PRECISAM SER AJUSTADAS");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CR", "FERIAS");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CR", "VAI VIAJAR");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CR", "ESTA SEM TEMPO DE VIR A ACADEMIA");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "AT", "ATESTADO POR OUTROS MOTIVOS");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CR", "FERIAS POR OUTROS MOTIVOS");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CA", "SEM TEMPO DE TREINAR");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CA", "INSATISFAÇÃO COM O SERVIÇO PRESTADO");
        gravarJustificativaOperacao(empresaVO.getCodigo(), "CA", "NÃO TEM COMO CONTINUAR PAGANDO");
    }

    public void gravarJustificativaOperacao(int empresa, String tipoOperacao, String descricao) throws Exception {
        List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs = getFacade().getJustificativaOperacao().consultarPorDescricao(descricao,
                empresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaJustificativaOperacaoVOs.isEmpty()) {
            JustificativaOperacaoVO justificativaOperacaoVO = new JustificativaOperacaoVO();
            justificativaOperacaoVO.setTipoOperacao(tipoOperacao);
            justificativaOperacaoVO.setEmpresa(new EmpresaVO());
            justificativaOperacaoVO.getEmpresa().setCodigo(empresa);
            justificativaOperacaoVO.setDescricao(descricao);
            getFacade().getJustificativaOperacao().incluirSemPermissao(justificativaOperacaoVO);
        }
    }

    public void gravarGrupos() throws Exception {
        gravarGrupo("PE", 0.0, 10.0, "AMIGOS DA FAMILIA");
        gravarGrupo("PE", 0.0, 3.0, "GRUPO DE CORRIDA DO JOAQUIM");
        gravarGrupo("PE", 0.0, 0.0, "ESCALADA DO SÁBADO");
        gravarGrupo("PE", 0.0, 0.0, "FAMÍLIA SILVA, JOÃO");
    }

    public void gravarGrupo(String tipoDesconto, Double valorDescontoGrupo, Double percentualDescontoGrupo, String descricao) throws Exception {
        List<GrupoVO> listaGrupo = getFacade().getGrupo().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaGrupo.isEmpty()) {
            GrupoVO grupoVO = new GrupoVO();
            grupoVO.setTipoDesconto(tipoDesconto);
            grupoVO.setValorDescontoGrupo(valorDescontoGrupo);
            grupoVO.setPercentualDescontoGrupo(percentualDescontoGrupo);
            grupoVO.setDescricao(descricao);
            getFacade().getGrupo().incluir(grupoVO);
        }
    }

    public void gravarClassificacoes() throws Exception {
        gravarClassificacao("ATLETA");
        gravarClassificacao("INTERATIVO");
        gravarClassificacao("ALTO RISCO DE SAIR E VOLTAR");
        gravarClassificacao("MULHER ATIVA");
        gravarClassificacao("ALUNO DE PERSONAL");
        gravarClassificacao("ALUNO DIRETIVO");
        gravarClassificacao("ALUNO INFLUENTE");
        gravarClassificacao("ALUNO ESTÁVEL");
        gravarClassificacao("ALUNO CONFORMIDADE");
        gravarClassificacao("VIAJA MUITO");
        gravarClassificacao("MELHOR IDADE");
        gravarClassificacao("FILHO VEM COM FAMILIA");
        gravarClassificacao("ESTUDANTE VEM COM COLEGAS");
        gravarClassificacao("CLIENTE CULTO");
    }

    public void gravarClassificacao(String nome) throws Exception {
        List<ClassificacaoVO> listaClassificacaoVOs = getFacade().getClassificacao().consultarPorNome(nome, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaClassificacaoVOs.isEmpty()) {
            ClassificacaoVO classificacaoVO = new ClassificacaoVO();
            classificacaoVO.setNome(nome);
            getFacade().getClassificacao().incluir(classificacaoVO);
        }
    }

    public void gravarParentescos() throws Exception {
        gravarParentesco(150, "CÔNJUGE");
        gravarParentesco(150, "FILHO");
        gravarParentesco(150, "FILHA");
        gravarParentesco(150, "PAI");
        gravarParentesco(150, "MÃE");
    }

    public void gravarParentesco(int idadeLimiteDependencia, String descricao) throws Exception {
        List<ParentescoVO> listaParentescoVOs = getFacade().getParentesco().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaParentescoVOs.isEmpty()) {
            ParentescoVO parentescoVO = new ParentescoVO();
            parentescoVO.setIdadeLimiteDependencia(idadeLimiteDependencia);
            parentescoVO.setDescricao(descricao);
            getFacade().getParentesco().incluir(parentescoVO);
        }
    }

    public void gravarProfissoes() throws Exception {
        gravarProfissao("RECEPCIONISTA");
        gravarProfissao("CONSULTOR(A) DE VENDAS");
        gravarProfissao("PROFESSOR(A) DE EDUCAÇÃO FÍSICA");
        gravarProfissao("ADMINISTRADOR(A)");
        gravarProfissao("MÉDICO(A)");
        gravarProfissao("ADVOGADO(A)");
        gravarProfissao("VENDEDOR(A)");
        gravarProfissao("ENGENHEIRO(A) CIVIL");
        gravarProfissao("DOMÉSTICA");
        gravarProfissao("EMPRESÁRIO(A)");
        gravarProfissao("PROFESSOR(A)");
        gravarProfissao("NUTRICIONISTA");
    }

    public void gravarProfissao(String descricao) throws Exception {
        List<ProfissaoVO> profissaoVOs = getFacade().getProfissao().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (profissaoVOs.isEmpty()) {
            ProfissaoVO profissaoVO = new ProfissaoVO();
            profissaoVO.setDescricao(descricao);
            getFacade().getProfissao().incluir(profissaoVO);
        }
    }

    public void gravarHorarios() throws Exception {
        HorarioVO horarioVO = povoarHorarioVO(false, true, true, true, true, true, true, true, false, "LIVRE: 06:00 AS 23:00");
        List<HorarioVO> listaHorarioVOs = getFacade().getHorario().consultarPorDescricao(horarioVO.getDescricao(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaHorarioVOs.isEmpty()) {
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, false,
                    false, false, false, false, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, false, false, false,
                    false, false, false, false, false, false, false,
                    false, ""));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    "Domingo"));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, false, false, false,
                    false, false, false, false, false, false, false,
                    false, "Segunda"));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, false, false, false,
                    false, false, false, false, false, false, false,
                    false, "Terça"));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, false, false, false,
                    false, false, false, false, false, false, false,
                    false, "Quarta"));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, false, false, false,
                    false, false, false, false, false, false, false,
                    false, "Quinta"));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, false, false, false,
                    false, false, false, false, false, false, false,
                    false, "Sexta"));
            horarioVO.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, true,
                    true, true, true, true, true, true, true, false,
                    false, false, false, false, false, false, false,
                    false, false, false, "Sábado"));
            getFacade().getHorario().incluirSemCommit(horarioVO);
        }

        HorarioVO horarioVO2 = povoarHorarioVO(false, true, false, false, false, false, false, false, false, "HORARIO DA TURMA");
        List<HorarioVO> listaHorarioVOs2 = getFacade().getHorario().consultarPorDescricao(horarioVO2.getDescricao(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaHorarioVOs2.isEmpty()) {
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, ""));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, true,
                    "Domingo"));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, "Segunda"));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, "Terça"));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, "Quarta"));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, "Quinta"));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, "Sexta"));
            horarioVO2.getHorarioDisponibilidadeVOs().add(
                    gravarHorarioDisponibilidade(false, false, false, true,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, false,
                    false, false, false, false, false, false, "Sábado"));
            getFacade().getHorario().incluirSemCommit(horarioVO2);
        }
    }

    public HorarioVO povoarHorarioVO(Boolean horarioDefault, Boolean domingo,
            Boolean segunda, Boolean terca, Boolean quarta, Boolean quinta,
            Boolean sexta, Boolean sabado, Boolean livre, String descricao)
            throws Exception {
        HorarioVO horarioVO = new HorarioVO();
        horarioVO.setDescricao(descricao);
        horarioVO.setHorarioDefault(horarioDefault);
        horarioVO.setDomingo(domingo);
        horarioVO.setSegunda(segunda);
        horarioVO.setTerca(terca);
        horarioVO.setQuarta(quarta);
        horarioVO.setQuinta(quinta);
        horarioVO.setSexta(sexta);
        horarioVO.setSabado(sabado);
        return horarioVO;
    }

    public HorarioDisponibilidadeVO gravarHorarioDisponibilidade(
            Boolean noturno, Boolean vespertino, Boolean matutino,
            Boolean desenhartodos, Boolean hora2330, Boolean hora2300,
            Boolean hora2230, Boolean hora2200, Boolean hora2130,
            Boolean hora2100, Boolean hora2030, Boolean hora2000,
            Boolean hora1930, Boolean hora1900, Boolean hora1830,
            Boolean hora1800, Boolean hora1730, Boolean hora1700,
            Boolean hora1630, Boolean hora1600, Boolean hora1530,
            Boolean hora1500, Boolean hora1430, Boolean hora1400,
            Boolean hora1330, Boolean hora1300, Boolean hora1230,
            Boolean hora1200, Boolean hora1130, Boolean hora1100,
            Boolean hora1030, Boolean hora1000, Boolean hora0930,
            Boolean hora0900, Boolean hora0830, Boolean hora0800,
            Boolean hora0730, Boolean hora0700, Boolean hora0630,
            Boolean hora0600, Boolean hora0530, Boolean hora0500,
            Boolean hora0430, Boolean hora0400, Boolean hora0330,
            Boolean hora0300, Boolean hora0230, Boolean hora0200,
            Boolean hora0130, Boolean hora0100, Boolean hora0030,
            Boolean hora0000, String identificador)
            throws Exception {
        HorarioDisponibilidadeVO horarioDisponibilidadeVO = new HorarioDisponibilidadeVO();
        horarioDisponibilidadeVO.setNoturno(noturno);
        horarioDisponibilidadeVO.setVespertino(vespertino);
        horarioDisponibilidadeVO.setMatutino(matutino);
        horarioDisponibilidadeVO.setDesenharTodos(desenhartodos);
        horarioDisponibilidadeVO.setHora2330(hora2330);
        horarioDisponibilidadeVO.setHora2300(hora2300);
        horarioDisponibilidadeVO.setHora2230(hora2230);
        horarioDisponibilidadeVO.setHora2200(hora2200);
        horarioDisponibilidadeVO.setHora2130(hora2130);
        horarioDisponibilidadeVO.setHora2100(hora2100);
        horarioDisponibilidadeVO.setHora2030(hora2030);
        horarioDisponibilidadeVO.setHora2000(hora2000);
        horarioDisponibilidadeVO.setHora1930(hora1930);
        horarioDisponibilidadeVO.setHora1900(hora1900);
        horarioDisponibilidadeVO.setHora1830(hora1830);
        horarioDisponibilidadeVO.setHora1800(hora1800);
        horarioDisponibilidadeVO.setHora1730(hora1730);
        horarioDisponibilidadeVO.setHora1700(hora1700);
        horarioDisponibilidadeVO.setHora1630(hora1630);
        horarioDisponibilidadeVO.setHora1600(hora1600);
        horarioDisponibilidadeVO.setHora1530(hora1530);
        horarioDisponibilidadeVO.setHora1500(hora1500);
        horarioDisponibilidadeVO.setHora1430(hora1430);
        horarioDisponibilidadeVO.setHora1400(hora1400);
        horarioDisponibilidadeVO.setHora1330(hora1330);
        horarioDisponibilidadeVO.setHora1300(hora1300);
        horarioDisponibilidadeVO.setHora1230(hora1230);
        horarioDisponibilidadeVO.setHora1200(hora1200);
        horarioDisponibilidadeVO.setHora1130(hora1130);
        horarioDisponibilidadeVO.setHora1100(hora1100);
        horarioDisponibilidadeVO.setHora1030(hora1030);
        horarioDisponibilidadeVO.setHora1000(hora1000);
        horarioDisponibilidadeVO.setHora0930(hora0930);
        horarioDisponibilidadeVO.setHora0900(hora0900);
        horarioDisponibilidadeVO.setHora0830(hora0830);
        horarioDisponibilidadeVO.setHora0800(hora0800);
        horarioDisponibilidadeVO.setHora0730(hora0730);
        horarioDisponibilidadeVO.setHora0700(hora0700);
        horarioDisponibilidadeVO.setHora0630(hora0630);
        horarioDisponibilidadeVO.setHora0600(hora0600);
        horarioDisponibilidadeVO.setHora0530(hora0530);
        horarioDisponibilidadeVO.setHora0500(hora0500);
        horarioDisponibilidadeVO.setHora0430(hora0430);
        horarioDisponibilidadeVO.setHora0400(hora0400);
        horarioDisponibilidadeVO.setHora0330(hora0330);
        horarioDisponibilidadeVO.setHora0300(hora0300);
        horarioDisponibilidadeVO.setHora0230(hora0230);
        horarioDisponibilidadeVO.setHora0200(hora0200);
        horarioDisponibilidadeVO.setHora0130(hora0130);
        horarioDisponibilidadeVO.setHora0100(hora0100);
        horarioDisponibilidadeVO.setHora0030(hora0030);
        horarioDisponibilidadeVO.setHora0000(hora0000);
        horarioDisponibilidadeVO.setIdentificador(identificador);
        return horarioDisponibilidadeVO;
    }

    public PessoaVO povoarPessoa(String webPage, byte[] foto,
            int codGrauInstrucaoVO, String sexo, String naturalidade,
            String nacionalidade, String estadoCivil, PaisVO paisVO,
            EstadoVO estadoVO, CidadeVO cidadeVO, String rgUf, String rgOrgao,
            String rg, String cpf, String nomeMae, String nomePai,
            Date dataNasc, String nome, Date dataCadastro, int profissao)
            throws Exception {
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setWebPage(webPage);
        pessoaVO.setFoto(foto);
        pessoaVO.setGrauInstrucao(new GrauInstrucaoVO());
        pessoaVO.getGrauInstrucao().setCodigo(codGrauInstrucaoVO);
        pessoaVO.setSexo(sexo);
        pessoaVO.setNaturalidade(naturalidade);
        pessoaVO.setNacionalidade(nacionalidade);
        pessoaVO.setEstadoCivil(estadoCivil);
        pessoaVO.setPais(paisVO);
        pessoaVO.setEstadoVO(estadoVO);
        pessoaVO.setCidade(cidadeVO);
        pessoaVO.setRgUf(rgUf);
        pessoaVO.setRgOrgao(rgOrgao);
        pessoaVO.setRg(rg);
        pessoaVO.setCfp(cpf);
        pessoaVO.setNomeMae(nomeMae);
        pessoaVO.setNomePai(nomePai);
        pessoaVO.setDataNasc(dataNasc);
        pessoaVO.setNome(nome);
        pessoaVO.setDataCadastro(dataCadastro);
        pessoaVO.setProfissao(new ProfissaoVO());
        pessoaVO.getProfissao().setCodigo(profissao);
        return pessoaVO;
    }

    public void gravarColaboradores() throws Exception {
        CidadeVO cidadeVO = new CidadeVO();
        cidadeVO.setCodigo(1);

        EstadoVO estadoVO = new EstadoVO();
        estadoVO.setCodigo(1);

        int codigoProfissao = getFacade().getProfissao().consultarCodigoPorDescricao("ADMINISTRADOR");

        PessoaVO pessoaVO = povoarPessoa("WWW.PACTO.VC", null, 0, "M", "", "BRASILEIRA", "S", new PaisVO(), estadoVO, cidadeVO, "", "", "", "", "", "", Uteis.getDate("02/11/1987"), "PACTO - MÉTODO DE GESTÃO", Uteis.getDate("26/02/2010"), codigoProfissao);

        gravarColaborador(true, "5000010001", "", "AT", pessoaVO, 40.0, empresaVO.getCodigo());
        gravarEndereco(pessoaVO, "CO", "74.270-170", "JARDIM AMÉRICA", "246", "", "RUA C 200", true);
        gravarEmail(pessoaVO, true, "<EMAIL>");
        gravarTelefone(pessoaVO.getCodigo(), "CO", "(62)32515820");

        gravarColaborador(false, "5000020006", "", "AT",
                povoarPessoa("", null, 0, "", "", "", "", new PaisVO(), new EstadoVO(), new CidadeVO(), "", "", "", "", "", "", Uteis.getDate("01/01/1980"), "NOME DO EMPRESARIO", Uteis.getDate("02/11/1987"), codigoProfissao),
                0.0, empresaVO.getCodigo());
    }

    private void gravarUsuariosMoveis() throws Exception {
        gravarUsuarioMovel("pactobr", "1abc.", listaColaboradores.get(0), new ClienteVO(), listaUsuarios.get(0).getCodigo());
        gravarUsuarioMovel("empresario", "1abc.", listaColaboradores.get(1), new ClienteVO(), listaUsuarios.get(1).getCodigo());
    }

    public void gravarColaborador(boolean funcionario, String codAcesso, String codAcessoAlternativo, String situacao, PessoaVO pessoa, double porcComissao, int codEmpresa) throws Exception {
        ColaboradorVO colaboradorVOJaExistente = getFacade().getColaborador().consultarPorNomeColaborador(pessoa.getNome(), codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (colaboradorVOJaExistente.getCodigo() == 0) {
            ColaboradorVO colaboradorVO = new ColaboradorVO();
            colaboradorVO.setFuncionario(funcionario);
            colaboradorVO.setCodAcesso(codAcesso);
            colaboradorVO.setCodAcessoAlternativo(codAcessoAlternativo);
            colaboradorVO.setSituacao(situacao);
            colaboradorVO.setPessoa(pessoa);
            colaboradorVO.setPorcComissao(porcComissao);
            colaboradorVO.setEmpresa(new EmpresaVO());
            colaboradorVO.getEmpresa().setCodigo(codEmpresa);
            getFacade().getColaborador().incluirSemCommit(colaboradorVO);
            listaColaboradores.add(colaboradorVO);
        } else {
            listaColaboradores.add(colaboradorVOJaExistente);
        }
    }

    public void gravarTiposColaborador() throws Exception {
        List listaTipoColaborador = getFacade().getTipoColaborador().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaTipoColaborador.isEmpty()) {
            gravarTipoColaborador(listaColaboradores.get(0).getCodigo(), TipoColaboradorEnum.CONSULTOR.getSigla());
            gravarTipoColaborador(listaColaboradores.get(0).getCodigo(), TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            gravarTipoColaborador(listaColaboradores.get(0).getCodigo(), TipoColaboradorEnum.COORDENADOR.getSigla());
            gravarTipoColaborador(listaColaboradores.get(0).getCodigo(), TipoColaboradorEnum.PROFESSOR.getSigla());
            gravarTipoColaborador(listaColaboradores.get(1).getCodigo(), TipoColaboradorEnum.COORDENADOR.getSigla());
        }
    }

    public void gravarTipoColaborador(int codColaborador, String descricao) throws Exception {
        TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
        tipoColaboradorVO.setColaborador(codColaborador);
        tipoColaboradorVO.setDescricao(descricao);
        getFacade().getTipoColaborador().incluir(tipoColaboradorVO);
    }

    /**
     * Grava um usuario padrão
     */
    public void gravarUsuarioEUsuarioPerfilAcesso(boolean administrador, int codClienteVO, int codColaboradorVO, String tipoUsuario,
            String senha, String username, String nome, int codEmpresa, int perfilAcesso) throws Exception {
        List<UsuarioVO> listaUsuarioVOs = getFacade().getUsuario().consultarPorUsername(username, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaUsuarioVOs.isEmpty()) {
            // atributos de usuario
            UsuarioVO usuarioVO = new UsuarioVO();
            usuarioVO.setAdministrador(administrador);
            usuarioVO.setNome(nome);
            usuarioVO.setUsername(username);
            usuarioVO.setSenha(senha);
            usuarioVO.setClienteVO(new ClienteVO());
            usuarioVO.getClienteVO().setCodigo(codClienteVO);
            usuarioVO.setColaboradorVO(new ColaboradorVO());
            usuarioVO.getColaboradorVO().setCodigo(codColaboradorVO);
            usuarioVO.setTipoUsuario(tipoUsuario);
            usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
            // atributos de UsuarioPerfilAcessoVO
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = new UsuarioPerfilAcessoVO();
            usuarioPerfilAcessoVO.setEmpresa(new EmpresaVO());
            usuarioPerfilAcessoVO.getEmpresa().setCodigo(codEmpresa);
            usuarioPerfilAcessoVO.setPerfilAcesso(new PerfilAcessoVO());
            usuarioPerfilAcessoVO.getPerfilAcesso().setCodigo(perfilAcesso);
            usuarioVO.getUsuarioPerfilAcessoVOs().add(usuarioPerfilAcessoVO);
            //acrescentado lista de horários de acesso ao sistema padrões
            UsuarioControle usuarioContr = new UsuarioControle(true);
            usuarioContr.novo();
            usuarioVO.setUsuarioHorarioAcessoSistemaVOs(new ArrayList());
            for (List<HorarioAcessoSistemaVO> listaHorariosAcessos : usuarioContr.getMapHorariosAcessoSistema().values()) {
                Iterator i = listaHorariosAcessos.iterator();
                while (i.hasNext()) {
                    HorarioAcessoSistemaVO objExistente = (HorarioAcessoSistemaVO) i.next();
                    usuarioVO.getUsuarioHorarioAcessoSistemaVOs().add(objExistente);
                }
            }
            usuarioVO.setValidarDados(false);
            getFacade().getUsuario().incluirSemCommit(usuarioVO);
            listaUsuarios.add(usuarioVO);
        } else {
            listaUsuarios.addAll(listaUsuarioVOs);
        }
    }

    public void gravarPerfilAcessoUsuario(int codEmpresa, int perfilAcesso, int usuario) throws Exception {
        UsuarioVO consultarPorCodigoUsuario = getFacade().getUsuario().consultarPorCodigoUsuario(usuario, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (consultarPorCodigoUsuario.getCodigo() == 0) {
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = new UsuarioPerfilAcessoVO();
            usuarioPerfilAcessoVO.setEmpresa(new EmpresaVO());
            usuarioPerfilAcessoVO.getEmpresa().setCodigo(codEmpresa);
            usuarioPerfilAcessoVO.setPerfilAcesso(new PerfilAcessoVO());
            usuarioPerfilAcessoVO.getPerfilAcesso().setCodigo(perfilAcesso);
            usuarioPerfilAcessoVO.setUsuario(usuario);
            getFacade().getUsuarioPerfilAcesso().incluir(usuarioPerfilAcessoVO);
        }
    }

    public void gravarNiveisTurma() throws Exception {
        gravarNivelTurma("INICIANTE");
        gravarNivelTurma("INTERMEDIÁRIO");
        gravarNivelTurma("AVANÇADO");
        gravarNivelTurma("SN");
    }

    public void gravarNivelTurma(String descricao) throws Exception {
        List consultarPorDescricao = getFacade().getNivelTurma().consultarPorDescricao(descricao, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (consultarPorDescricao.isEmpty()) {
            NivelTurmaVO nivelTurmaVO = new NivelTurmaVO();
            nivelTurmaVO.setDescricao(descricao);
            getFacade().getNivelTurma().incluir(nivelTurmaVO);
            listaNivelTurma.add(nivelTurmaVO);
        } else {
            listaNivelTurma.addAll(consultarPorDescricao);
        }
    }

    public void gravarTurmas() throws Exception {
        gravarTurma(true, empresaVO.getCodigo(), 100, 0, 0, 0, Uteis.getDate("15/12/2025"), Uteis.getDate("15/12/2010"),
                getFacade().getModalidade().consultarPorNomeModalidade("IMPORTAÇÃO", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo(), "TI", "TURMA IMPORTAÇÃO");
    }

    public void gravarTurma(boolean bloquearMatriculasAcimaLimite,
            int codEmpresa, int idadeMaxima, int idadeMaximaMeses,
            int idadeMinima, int idadeMinimaMeses, Date dataFinalVigencia,
            Date dataInicialVigencia, int modalidade, String identificador,
            String descricao) throws Exception {
        List<TurmaVO> listaTurmas = getFacade().getTurma().consultarPorIdentificador(identificador, codEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaTurmas.isEmpty()) {
            TurmaVO turmaVO = new TurmaVO();
            turmaVO.setBloquearMatriculasAcimaLimite(bloquearMatriculasAcimaLimite);
            turmaVO.setEmpresa(new EmpresaVO());
            turmaVO.getEmpresa().setCodigo(codEmpresa);
            turmaVO.setIdadeMaxima(idadeMaxima);
            turmaVO.setIdadeMaximaMeses(idadeMaximaMeses);
            turmaVO.setIdadeMinima(idadeMinima);
            turmaVO.setIdadeMinimaMeses(idadeMinimaMeses);
            turmaVO.setDataInicialVigencia(dataInicialVigencia);
            turmaVO.setDataFinalVigencia(dataFinalVigencia);
            turmaVO.setModalidade(new ModalidadeVO());
            turmaVO.getModalidade().setCodigo(modalidade);
            turmaVO.setIdentificador(identificador);
            turmaVO.setDescricao(descricao);
            getFacade().getTurma().incluir(turmaVO);
            listaTurmaVO.add(turmaVO);
        } else {
            listaTurmaVO.addAll(listaTurmas);
        }

    }

    public void gravarHorariosTurma() throws Exception {
        int codigoAmbiente = getFacade().getAmbiente().consultarCodigoPorDescricao("PISCINA (TURMA)");
        int codigoColaborador = listaColaboradores.get(0).getCodigo();
        gravarHorarioTurma(2, 5, "TI", "AT", "SG", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "08:59", "08:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(2, 5, "TI", "AT", "SG", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "09:59", "09:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(2, 5, "TI", "AT", "SG", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "10:59", "10:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(4, 5, "TI", "AT", "QA", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "08:59", "08:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(4, 5, "TI", "AT", "QA", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "09:59", "09:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(4, 5, "TI", "AT", "QA", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "10:59", "10:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(6, 5, "TI", "AT", "SX", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "08:59", "08:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(6, 5, "TI", "AT", "SX", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "09:59", "09:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(6, 5, "TI", "AT", "SX", listaNivelTurma.get(0).getCodigo(), codigoAmbiente, codigoColaborador, "10:59", "10:00", listaTurmaVO.get(0).getCodigo());

        gravarHorarioTurma(2, 10, "TI", "AT", "SG", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "15:59", "15:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(2, 10, "TI", "AT", "SG", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "16:59", "16:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(2, 10, "TI", "AT", "SG", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "17:59", "17:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(4, 10, "TI", "AT", "QA", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "15:59", "15:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(4, 10, "TI", "AT", "QA", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "16:59", "16:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(4, 10, "TI", "AT", "QA", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "17:59", "17:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(6, 10, "TI", "AT", "SX", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "15:59", "15:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(6, 10, "TI", "AT", "SX", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "16:59", "16:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(6, 10, "TI", "AT", "SX", listaNivelTurma.get(1).getCodigo(), codigoAmbiente, codigoColaborador, "17:59", "17:00", listaTurmaVO.get(0).getCodigo());

        gravarHorarioTurma(3, 10, "TI", "AT", "TR", listaNivelTurma.get(2).getCodigo(), codigoAmbiente, codigoColaborador, "18:59", "18:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(3, 10, "TI", "AT", "TR", listaNivelTurma.get(2).getCodigo(), codigoAmbiente, codigoColaborador, "19:59", "19:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(3, 10, "TI", "AT", "TR", listaNivelTurma.get(2).getCodigo(), codigoAmbiente, codigoColaborador, "20:59", "20:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(5, 10, "TI", "AT", "QI", listaNivelTurma.get(2).getCodigo(), codigoAmbiente, codigoColaborador, "18:59", "18:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(5, 10, "TI", "AT", "QI", listaNivelTurma.get(2).getCodigo(), codigoAmbiente, codigoColaborador, "19:59", "19:00", listaTurmaVO.get(0).getCodigo());
        gravarHorarioTurma(5, 10, "TI", "AT", "QI", listaNivelTurma.get(2).getCodigo(), codigoAmbiente, codigoColaborador, "20:59", "20:00", listaTurmaVO.get(0).getCodigo());
        //this.atualizarSequencePostgreSQL("horarioTurma", 8);
    }

    public void gravarHorarioTurma(int diaSemanaNumero, int nrMaximoAluno, String identificadorTurma, String situacao, String diaSemana,
            int nivelTurma, int ambiente, int professor, String horaFinal, String horaInicial, int turma) throws Exception {
        HorarioTurmaVO hor = new HorarioTurmaVO();
        hor.setDiaSemana(diaSemana);
        hor.setHoraInicial(horaInicial);
        hor.setHoraFinal(horaFinal);
        hor.setProfessor(new ColaboradorVO());
        hor.getProfessor().setCodigo(professor);
        HorarioTurmaVO horarioJaExistente = getFacade().getHorarioTurma().
                consultarPorProfessorDiaDaSemanaHorarioInicialFinal(hor, empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (horarioJaExistente == null) {
            HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
            horarioTurmaVO.setDiaSemanaNumero(diaSemanaNumero);
            horarioTurmaVO.setNrMaximoAluno(nrMaximoAluno);
            horarioTurmaVO.setIdentificadorTurma(identificadorTurma);
            horarioTurmaVO.setSituacao(situacao);
            horarioTurmaVO.setDiaSemana(diaSemana);
            horarioTurmaVO.setNivelTurma(new NivelTurmaVO());
            horarioTurmaVO.getNivelTurma().setCodigo(nivelTurma);
            horarioTurmaVO.setAmbiente(new AmbienteVO());
            horarioTurmaVO.getAmbiente().setCodigo(ambiente);
            horarioTurmaVO.setProfessor(new ColaboradorVO());
            horarioTurmaVO.getProfessor().setCodigo(professor);
            horarioTurmaVO.setHoraInicial(horaInicial);
            horarioTurmaVO.setHoraFinal(horaFinal);
            horarioTurmaVO.setTurma(turma);
            getFacade().getHorarioTurma().incluir(horarioTurmaVO);
        }
    }

    public void atualizarSequencePostgreSQL(String nomeTabela, int numero) {
        String sql = "SELECT SETVAL(pg_get_serial_sequence('" + nomeTabela
                + "', 'codigo'), " + numero + ", true)";
        Statement stm;
        try {
            consultar(getIdEntidade(), false);
            stm = con.createStatement();
            stm.execute(sql);
        } catch (Exception ex) {
            Logger.getLogger(PovoadorDadosBancoInicial.class.getName()).log(
                    Level.SEVERE, null, ex);
        }
    }

    public void gravarPlanosTextoPadrao() throws Exception {
        gravarPlanoTextoPadrao(obterTextoContrato(), "AT", listaUsuarios.get(0).getCodigo(), Uteis.getDate("20/03/2014"), "NOME DA EMPRESA | CPS 2015", null);
    }

    public void gravarPlanoTextoPadrao(StringBuilder texto, String situacao,
            int responsavelDefinicao, Date dataDefinicao, String descricao,
            byte[] imagemLogo) throws Exception {
        List<PlanoTextoPadraoVO> planoTextoPadraoJaExistente = getFacade().getPlanoTextoPadrao().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (planoTextoPadraoJaExistente.isEmpty()) {
            PlanoTextoPadraoVO planoTextoPadraoVO = new PlanoTextoPadraoVO();
            planoTextoPadraoVO.setTexto(texto.toString());
            planoTextoPadraoVO.setSituacao(situacao);
            planoTextoPadraoVO.setResponsavelDefinicao(new UsuarioVO());
            planoTextoPadraoVO.getResponsavelDefinicao().setCodigo(responsavelDefinicao);
            planoTextoPadraoVO.setTipoContrato("PL");
            planoTextoPadraoVO.setDataDefinicao(dataDefinicao);
            planoTextoPadraoVO.setDescricao(descricao);
            getFacade().getPlanoTextoPadrao().incluirSemCommit(planoTextoPadraoVO);
            listaPlanoTextoPadrao.add(planoTextoPadraoVO);
        } else {
            listaPlanoTextoPadrao.addAll(planoTextoPadraoJaExistente);
        }
    }

    public StringBuilder obterTextoContrato() {
        StringBuilder texto = new StringBuilder("");
        texto.append("<!DOCTYPE html PUBLIC '-//W3C//DTD XHTML 1.0 Transitional//EN' 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd'><html><head><title>Untitled document</title></head><body>");
        texto.append("<div style='text-align: right;'><img style='border-style: none; width: 200px; height: 56px;' src='acesso?emp' alt='' /></div>");
        texto.append("<div>");
        texto.append("<div><span style='font-size: x-small;'><strong>NOME: [(50){}Nome_Cliente] &nbsp; &nbsp; &nbsp;MATR&Iacute;CULA: [(10){}Matricula_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>ENDERE&Ccedil;O: &nbsp;[(40){}Endereco_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>CIDADE/UF: [(50){}Cidade_Empresa]/[(50){}Estado_Empresa] &nbsp; CEP: [(10){}CEP_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>TELEFONE CELULAR: [(20){}Telefone_Celular_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>CPF: [(14){}Cpf_Cliente] &nbsp; RG: [(20){}Rg_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>E-MAIL: [(40){}Email_Cliente] &nbsp; &nbsp; NASCIMENTO: [(20){}DataNasc_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br />CONTRATADA: [(50){}RazaoSocial_Empresa] , CNPJ: &nbsp;[(18){}Cnpj_Empresa] , com sede &agrave; &nbsp;[(50){}Endereco_Empresa] [(50){}Complemento_Empresa] - &nbsp;[(50){}Cidade_Empresa] - [(50){}Estado_Empresa] [(5){ - N&ordm;: &nbsp;}Numero_Empresa] &nbsp;[(10){ - CEP: }Cep_Empresa], neste ato representada por seu diretor ou funcion&aacute;rio abaixo assinado.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>Os contratantes t&ecirc;m pactuado o que estipula as cl&aacute;usulas seguintes:</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>PRIMEIRA: O objeto deste contrato &eacute; a presta&ccedil;&atilde;o de servi&ccedil;os especializados, na &aacute;rea de atividade f&iacute;sica, pela CONTRATADA ao (&agrave;) CONTRATANTE, na modalidade [(50){}Nome_Modalidade], em suas depend&ecirc;ncias anteriormente descritas, de acordo com as condi&ccedil;&otilde;es estabelecidas no regulamento da CONTRATADA, que integra este contrato e que o(a) CONTRATANTE declara ter lido e anu&iacute;do.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>SEGUNDA: O presente contrato ter&aacute; a dura&ccedil;&atilde;o de [(10){}Duracao_Contrato] meses, tendo seu per&iacute;odo de vig&ecirc;ncia de [(20){}VigenciaDe_Contrato] at&eacute; [(20){}VigenciaAte_Contrato].</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>Modalidades: &nbsp;[(50){}Nome_Modalidade]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>TERCEIRA: Pelos servi&ccedil;os descritos na cl&aacute;usula 1&ordf;, o (a) CONTRATANTE pagar&aacute; a import&acirc;ncia total de R$ [(15){}ValorFinal_Contrato] da seguinte forma:</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>[(60){} valor_MovPagamento, &nbsp;(20) { Vezes: } parcelasCC_MovPagamento, (60){ &nbsp; Banco: &nbsp; }banco_Cheque, (20){ &nbsp; Agencia: }agencia_Cheque, (20){ &nbsp; N.: }numero_Cheque, (20){ &nbsp; CC: &nbsp;}contaCorrente_Cheque,(20){ &nbsp; &nbsp;Vlr.: }valor_Cheque,(20) { &nbsp; Dt Compensa&ccedil;&atilde;o: }dataCompensacao_Cheque ]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>QUARTA: S&atilde;o obriga&ccedil;&otilde;es do CONTRATANTE:</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>I) A indica&ccedil;&atilde;o M&eacute;dica ser&aacute; necess&aacute;ria ser apresentada, caso o CONTRATENTE seja portador de &nbsp;problemas de sa&uacute;de .</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>II) Avalia&ccedil;&atilde;o F&iacute;sica &eacute; recomendada para verificar as necessidades individuais, constatar alguma patologia e para a prescri&ccedil;&atilde;o do treinamento personalizado.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>III) O (a) CONTRATANTE declara neste ato estar em plenas condi&ccedil;&otilde;es de sa&uacute;de, apto a realizar atividades f&iacute;sicas, e n&atilde;o portar mol&eacute;stia contagiosa que possa prejudicar os demais frequentadores, isentando a CONTRATADA de qualquer responsabilidade sobre qualquer acidente dentro de suas depend&ecirc;ncias.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>V) Usar roupas adequadas para a pr&aacute;tica de atividades f&iacute;sicas, sendo proibido o uso de sapatos, chinelos, sand&aacute;lias, bermuda ou cal&ccedil;a jeans, bem como o comparecimento &agrave;s salas de aula de p&eacute;s descal&ccedil;os e sem camisa na sala de muscula&ccedil;&atilde;o.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>VI) Manter conduta ilibada e compat&iacute;vel com a moral e os bons costumes, nas depend&ecirc;ncias da CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>VII) Zelar pelo bom uso e conserva&ccedil;&atilde;o dos equipamentos e instala&ccedil;&otilde;es da CONTRATADA, responsabilizando-se pela indeniza&ccedil;&atilde;o de qualquer dano a que der causa, exceto se decorrente do uso natural.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>VIII) &Eacute; proibido comercializar produtos ou servi&ccedil;os nas depend&ecirc;ncias da CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>IX) &Eacute; proibido comer na &aacute;rea de treinamento &nbsp;da CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>X) &Eacute; vedada a entrada e a circula&ccedil;&atilde;o de animais na CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>XI) &nbsp;N&atilde;o &eacute; permitido fumar, ingerir bebida alco&oacute;lica ou fazer uso de ester&oacute;ides e anabolizantes no interior da CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>XII) &Eacute; proibida a utiliza&ccedil;&atilde;o das partes comuns da CONTRATADA, para a distribui&ccedil;&atilde;o de propostas comerciais, folhetos, pe&ccedil;as promocionais, cupons e expedientes deste g&ecirc;nero. S&atilde;o igualmente vedadas realiza&ccedil;&otilde;es p&uacute;blicas, demonstra&ccedil;&atilde;o com mercadorias, propaganda com cartazes ou atividades de vendedores ambulantes, anunciadores, aliciadores em geral, rifas e/ou qualquer tipo de angaria&ccedil;&atilde;o de recursos financeiros, seja qual for a natureza ou produto, salvo se com autoriza&ccedil;&atilde;o pr&eacute;via e escrita da &nbsp;CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>XIII) N&atilde;o ser&aacute; permitida, em hip&oacute;tese alguma, a atua&ccedil;&atilde;o do CONTRATANTE dos servi&ccedil;os de forma a caracterizar trabalho como instrutor e/ou personal trainer.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>QUINTA: A responsabilidade da CONTRATADA com rela&ccedil;&atilde;o &agrave; seguran&ccedil;a de alunos menores de idade limita-se ao tempo de dura&ccedil;&atilde;o das aulas, enquanto estiverem aos cuidados dos professores t&atilde;o somente nas depend&ecirc;ncias da CONTRATADA. &Eacute; atribui&ccedil;&atilde;o dos pais, respons&aacute;veis ou acompanhantes dos menores zelarem pela sua seguran&ccedil;a antes e depois das aulas, bem como no trajeto resid&ecirc;ncia/academia/resid&ecirc;ncia.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>SEXTA: A CONTRATADA, n&atilde;o se responsabiliza pela perda, dano ou extravio, de objetos e pertences pessoais de valor deixados pelos clientes em suas depend&ecirc;ncias, bem como eventuais furtos ou danos causados aos ve&iacute;culos deixados estacionados na rua. Est&aacute; dispon&iacute;vel o sistema de arm&aacute;rios rotativos de propriedade do CONTRATANTE, sem ter controle do que &eacute; colocado e/ou levado pelo cliente de dentro dos arm&aacute;rios. Ap&oacute;s seu uso, os arm&aacute;rios dever&atilde;o ser deixados abertos. &nbsp;A utiliza&ccedil;&atilde;o do arm&aacute;rio ser&aacute; permitida somente durante a perman&ecirc;ncia do CONTRATANTE nas depend&ecirc;ncias da CONTRATADA, e os arm&aacute;rios encontrados fechados ap&oacute;s o hor&aacute;rio de funcionamento, ser&atilde;o abertos, e os objetos neles contidos ser&atilde;o guardados por 15 (quinze) dias, ficando &agrave; disposi&ccedil;&atilde;o dos interessados, sendo doados ap&oacute;s este per&iacute;odo, sem direito a indeniza&ccedil;&atilde;o.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>S&Eacute;TIMA: As normas constantes dos avisos e orienta&ccedil;&otilde;es afixados no interior das instala&ccedil;&otilde;es da CONTRATADA, que n&atilde;o estiverem contempladas neste instrumento, passam a fazer parte integrante deste contrato, sendo certo que o seu n&atilde;o cumprimento poder&aacute; acarretar na rescis&atilde;o antecipada ou a n&atilde;o renova&ccedil;&atilde;o do mesmo.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>OITAVA: Toda e qualquer sugest&atilde;o, cr&iacute;tica ou elogio, dever&atilde;o ser encaminhados por e-mail &agrave; <EMAIL>, que analisar&aacute; cada caso, conforme crit&eacute;rios estabelecidos pela dire&ccedil;&atilde;o.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>NONA: &Agrave; CONTRATADA &eacute; reservado o direito de rescis&atilde;o unilateral do presente contrato de presta&ccedil;&atilde;o de servi&ccedil;os, caso sejam descumpridas quaisquer cl&aacute;usulas contratuais pelo(a) CONTRATANTE, sem preju&iacute;zo da aplica&ccedil;&atilde;o de eventuais san&ccedil;&otilde;es, inclusive pecuni&aacute;rias, decorrentes de danos causados &agrave; CONTRATADA.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>D&Eacute;CIMA: Os casos omissos neste regulamento dever&atilde;o ser analisados pela dire&ccedil;&atilde;o.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>I. Funcionamento</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp; A i9 Academia funcionar&aacute; ininterruptamente por 12 (doze) meses por ano, abrindo em hor&aacute;rios especiais nos feriados memor&aacute;veis federais, estaduais e municipais.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(b) &nbsp; &nbsp;Na eventualidade de a i9 Academia fechar para manuten&ccedil;&atilde;o, o aluno ter&aacute; direito &agrave; reposi&ccedil;&atilde;o da aula, que ser&aacute; agendada pela i9 Academia. Fica expressamente ressalvado que a reposi&ccedil;&atilde;o tem car&aacute;ter pessoal e intransfer&iacute;vel e a falta do aluno na data agendada acarretar&aacute; a perda do direito de reposi&ccedil;&atilde;o.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>II. Pagamentos</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp; O aluno dever&aacute; estar sempre em dia com os pagamentos. Ap&oacute;s o vencimento, o plano ser&aacute; automaticamente rescindido, ficando resguardado &agrave; i9 Academia a cobran&ccedil;a dos dias utilizados e n&atilde;o pagos pelo aluno</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(b) &nbsp; &nbsp;O aluno somente poder&aacute; frequentar a i9 Academia enquanto estiver em dia com os pagamentos, sendo que estes dever&atilde;o ser feitos independentemente da frequ&ecirc;ncia.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(c) &nbsp; &nbsp; Para os alunos que optarem pelo d&eacute;bito autom&aacute;tico do cart&atilde;o de cr&eacute;dito que sofrerem reajuste peri&oacute;dico de pre&ccedil;os previsto neste contrato, haver&aacute; a adequa&ccedil;&atilde;o autom&aacute;tica do valor junto &agrave; administradora do cart&atilde;o.&nbsp;</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>III. Rescis&atilde;o</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp;O pedido de cancelamento do d&eacute;bito autom&aacute;tico em cart&atilde;o de cr&eacute;dito dever&aacute; ser feito com uma anteced&ecirc;ncia m&iacute;nima de 40 (quarenta) dias, tempo necess&aacute;rio para solicitar &agrave; administradora o cancelamento dos d&eacute;bitos.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(b) &nbsp; O aluno poder&aacute; manifestar sua desist&ecirc;ncia, hip&oacute;tese essa em que poder&atilde;o ser devolvidos os valores pagos, pois, para suprimento dos encargos administrativo-financeiros internos, ser&aacute; cobrada uma taxa de 15%. Al&eacute;m da referida taxa, ser&aacute; procedido o desconto das taxas cobradas pela administradora pelo cancelamento e antecipa&ccedil;&atilde;o dos pagamentos e dos tributos oriundos desta opera&ccedil;&atilde;o.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(c) &nbsp; &nbsp;Ap&oacute;s a solicita&ccedil;&atilde;o do aluno, o pedido de cancelamento &eacute; feito imediatamente pela i9 Academia &agrave; operadora do cart&atilde;o. N&atilde;o ser&aacute; permitido cancelamento ou estorno da opera&ccedil;&atilde;o, de dias aos quais o aluno tiver comparecido a i9 Academia.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(d) &nbsp; &nbsp;Caso haja devolu&ccedil;&atilde;o em esp&eacute;cie, ser&aacute; disponibilizada na sede da i9 Academia.&nbsp;</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>IV. Reajustes de Pre&ccedil;o</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp; &nbsp;O valor de mensalidade &eacute; reajustado anualmente. Todos os demais planos s&atilde;o considerados promocionais e variam de acordo com o per&iacute;odo do ano;</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>V. Cess&atilde;o, Transfer&ecirc;ncias E PRORROGA&Ccedil;&Otilde;ES</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp; O presente contrato &eacute; firmado em car&aacute;ter pessoal e intransfer&iacute;vel, sendo vedada a cess&atilde;o ou transfer&ecirc;ncia onerosa ou gratuita a terceiros, pelo aluno ou respons&aacute;vel, ainda que temporariamente, de qualquer dos direitos pelo mesmo atribu&iacute;do, sem a pr&eacute;via consulta ao Gerente Comercial.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(b) &nbsp;As prorroga&ccedil;&otilde;es obedecem a regras pr&eacute;-estabelecidas e fixadas pelo programa de gest&atilde;o, pela pol&iacute;tica de negocia&ccedil;&atilde;o da i9 Academia e variando de plano para plano. Quaisquer situa&ccedil;&otilde;es que fujam a regra ser&atilde;o analisadas pelo Gerente Comercial.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(c) &nbsp; &nbsp;A modalidade Pilates, por ser um servi&ccedil;o terceirizado, n&atilde;o h&aacute; prorroga&ccedil;&otilde;es e sim reposi&ccedil;&otilde;es. Reposi&ccedil;&otilde;es estas, que dever&atilde;o ser agendadas e acordadas diretamente com a instrutora da modalidade, com base em sua agenda e hor&aacute;rios dispon&iacute;veis para tais.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>VI. Menores de 21 anos</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp; Assinam este termo, juntamente com seu pai, m&atilde;e ou respons&aacute;vel legal, respondendo este solidariamente com aqueles por todos os seus atos ou omiss&otilde;es, renunciando ao benef&iacute;cio de ordem conferido pelo C&oacute;digo Civil.</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>VII. Renova&ccedil;&atilde;o do Plano</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>(a) &nbsp;O presente contrato extingue-se automaticamente quando o Plano contratado acaba ou/vence, havendo a necessidade de uma nova ades&atilde;o no instante da renova&ccedil;&atilde;o/contrata&ccedil;&atilde;o de um novo plano.&nbsp;</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>Ap&oacute;s ter lido o presente termo, compreendido e concordado com os seus termos e condi&ccedil;&otilde;es, firmo o presente, declaro que estou em perfeitas condi&ccedil;&otilde;es de sa&uacute;de para realizar atividades f&iacute;sicas, e isento a academia de qualquer responsabilidade sobre eventuais acidentes e/ou danos f&iacute;sicos que venha a sofrer, assim como autorizo o uso da minha imagem e nome para fins leg&iacute;timos.&nbsp;</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>[(50){}Cidade_Empresa] - [(50){}Estado_Empresa], [(10){}DtLancamento_Contrato].</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>_____________________________________________________</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>CONTRATANTE: [(50){}Nome_Cliente] - CPF: &nbsp;[(14){}Cpf_Cliente]</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong><br /></strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>________________________________________________________</strong></span></div>");
        texto.append("<div><span style='font-size: x-small;'><strong>RESPONS&Aacute;VEL: [(50){}Responsavel_Contrato] - [(50){}Nome_Empresa]</strong></span></div>");
        texto.append("</div>");
        texto.append("</body></html>");
        return texto;

    }

    public byte[] obterImagem() throws Exception {
        // logomarca de contrato padrão
        if (context() != null) {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = null;
            SuperControle.paintFotoEmpresa(arrayOutputStream, buffer);
            return arrayOutputStream.toByteArray();
        } else {
            return null;
        }
    }

    public List<PlanoComposicaoVO> incluirComposicoesPlano() throws Exception {
        List<PlanoComposicaoVO> listaComposicoesVOs = new ArrayList<PlanoComposicaoVO>();
        listaComposicoesVOs.add(incluirNaListaPlanoComposicao(listaComposicao.get(0).getCodigo(), 1));
        return listaComposicoesVOs;
    }

    public List<PlanoModalidadeVO> incluirModalidadesPlano() throws Exception {
        List<PlanoModalidadeVO> listaPlanoModalidade = new ArrayList<PlanoModalidadeVO>();
        List<PlanoModalidadeVezesSemanaVO> listaModalidadesVezesSemana = new ArrayList<PlanoModalidadeVezesSemanaVO>();
        // inclui vezes semana da modalidade
        listaModalidadesVezesSemana.add(incluirNaListaPlanoModalidadeVezesSemana(7, "", "", 0.0, 0.0, 1));
        // inclui modalidade
        listaPlanoModalidade.add(incluirNaListaPlanoModalidade("7 Vezes - R$ 150,00, ", listaModalidades.get(1).getCodigo(), 1, listaModalidadesVezesSemana));

        List<PlanoModalidadeVezesSemanaVO> listaModalidadesVezesSemana1 = new ArrayList<PlanoModalidadeVezesSemanaVO>();
        listaModalidadesVezesSemana1.add(incluirNaListaPlanoModalidadeVezesSemana(2, "RE", "VE", 30.0, 0.0, 2));
        listaModalidadesVezesSemana1.add(incluirNaListaPlanoModalidadeVezesSemana(3, "RE", "VE", 20.0, 0.0, 2));
        listaModalidadesVezesSemana1.add(incluirNaListaPlanoModalidadeVezesSemana(5, "", "", 0.0, 0.0, 2));
        listaPlanoModalidade.add(incluirNaListaPlanoModalidade("2 Vezes - R$ 120,00, 3 Vezes - R$ 130,00, 5 Vezes - R$ 150,00, ", listaModalidades.get(0).getCodigo(), 1, listaModalidadesVezesSemana1));

        return listaPlanoModalidade;
    }

    public void incluirCondicoesPagamentoNaDuracaoPlano() throws Exception {
        listaDuracoes = new ArrayList<PlanoDuracaoVO>();

        int codCondicaoPagamento1 = listaCondicaoPagamento.get(0).getCodigo();
        int codCondicaoPagamento2 = listaCondicaoPagamento.get(1).getCodigo();
        int codCondicaoPagamento3 = listaCondicaoPagamento.get(2).getCodigo();
        int codCondicaoPagamento4 = listaCondicaoPagamento.get(3).getCodigo();
        int codCondicaoPagamento5 = listaCondicaoPagamento.get(4).getCodigo();
        int codCondicaoPagamento6 = listaCondicaoPagamento.get(5).getCodigo();
        int codCondicaoPagamento7 = listaCondicaoPagamento.get(6).getCodigo();
        int codCondicaoPagamento8 = listaCondicaoPagamento.get(7).getCodigo();
        int codCondicaoPagamento9 = listaCondicaoPagamento.get(8).getCodigo();
        int codCondicaoPagamento10 = listaCondicaoPagamento.get(9).getCodigo();
        int codCondicaoPagamento11 = listaCondicaoPagamento.get(10).getCodigo();
        int codCondicaoPagamento12 = listaCondicaoPagamento.get(11).getCodigo();
        int codCondicaoPagamento13 = listaCondicaoPagamento.get(12).getCodigo();

        List<PlanoCondicaoPagamentoVO> listaCondicoesPagamento1 = new ArrayList<PlanoCondicaoPagamentoVO>();
        listaCondicoesPagamento1.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento1, 1, "", "", 0.0, 1));
        listaCondicoesPagamento1.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento13, 1, "", "", 0.0, 1));
        listaDuracoes.add(incluirNaListaPlanoDuracao(150.0, 150.0, 0.0, "AC", "PD", 0, 0.0, 1, 1, 1, 0, listaCondicoesPagamento1));

        List<PlanoCondicaoPagamentoVO> listaCondicoesPagamento2 = new ArrayList<PlanoCondicaoPagamentoVO>();
        listaCondicoesPagamento2.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento1, 2, "", "", 0.0, 1));
        listaCondicoesPagamento2.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento13, 2, "", "", 0.0, 1));
        listaCondicoesPagamento2.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento2, 2, "", "", 0.0, 0));
        listaCondicoesPagamento2.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento3, 2, "", "", 0.0, 0));
        listaDuracoes.add(incluirNaListaPlanoDuracao(120.0, 120.0, 30.0, "RE", "VE", 30, 0.0, 3, 1, 3, 15, listaCondicoesPagamento2));

        List<PlanoCondicaoPagamentoVO> listaCondicoesPagamento3 = new ArrayList<PlanoCondicaoPagamentoVO>();
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento1, 3, "", "", 0.0, 1));
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento13, 3, "", "", 0.0, 1));
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento2, 3, "", "", 0.0, 2));
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento3, 3, "", "", 0.0, 3));
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento4, 3, "", "", 0.0, 4));
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento5, 3, "", "", 0.0, 5));
        listaCondicoesPagamento3.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento6, 3, "", "", 0.0, 6));
        listaDuracoes.add(incluirNaListaPlanoDuracao(110.0, 110.0, 40.0, "RE", "VE", 40, 0.0, 6, 1, 6, 30, listaCondicoesPagamento3));

        List<PlanoCondicaoPagamentoVO> listaCondicoesPagamento4 = new ArrayList<PlanoCondicaoPagamentoVO>();
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento13, 4, "", "", 0.0, 1));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento1, 4, "", "", 0.0, 1));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento2, 4, "", "", 0.0, 2));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento3, 4, "", "", 0.0, 3));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento4, 4, "", "", 0.0, 4));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento5, 4, "", "", 0.0, 5));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento6, 4, "", "", 0.0, 6));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento7, 4, "", "", 0.0, 7));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento8, 4, "", "", 0.0, 8));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento9, 4, "", "", 0.0, 9));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento10, 4, "", "", 0.0, 10));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento11, 4, "", "", 0.0, 11));
        listaCondicoesPagamento4.add(incluirNaListaPlanoCondicaoPagamento(0.0, codCondicaoPagamento12, 4, "", "", 0.0, 12));
        listaDuracoes.add(incluirNaListaPlanoDuracao(100.0, 100.0, 50.0, "RE", "VE", 50, 0.0, 12, 1, 12, 45, listaCondicoesPagamento4));
    }

    public void incluirHorariosPlano() throws Exception {
        listaHorariosVOs = new ArrayList<PlanoHorarioVO>();
        HorarioVO horarioLivre = getFacade().getHorario().consultarPorDescricao("LIVRE: 06:00 AS 23:00", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        HorarioVO horarioTurma = getFacade().getHorario().consultarPorDescricao("HORARIO DA TURMA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        listaHorariosVOs.add(incluirNaListaPlanoHorario("", "", 0.0, 0.0, 1, horarioLivre));
        listaHorariosVOs.add(incluirNaListaPlanoHorario("", "", 0.0, 0.0, 1, horarioTurma));
    }

    public void incluirProdutosSugeridosPlano() {
        // obs.: o tipo de produto é necessário pois o sistema valida se o tipo de produto MA,RE,RN
        // foram incluidos nos produtos sugeridos do plano

        int codigoProdutoMatricula = 0;
        int codigoProdutoRematricula = 0;
        int codigoProdutoRenovacao = 0;
        int codigoProdutoAvaliacao = 0;

        for (ProdutoVO produtoVO : listaProdutos) {
            if ("MATRÍCULA".equals(produtoVO.getDescricao())) {
                codigoProdutoMatricula = produtoVO.getCodigo();
            } else if ("REMATRÍCULA".equals(produtoVO.getDescricao())) {
                codigoProdutoRematricula = produtoVO.getCodigo();
            } else if ("RENOVAÇÃO".equals(produtoVO.getDescricao())) {
                codigoProdutoRenovacao = produtoVO.getCodigo();
            } else if ("AVALIAÇÃO FÍSICA".equals(produtoVO.getDescricao())) {
                codigoProdutoAvaliacao = produtoVO.getCodigo();
            }
        }

        listaProdutosSugeridos.add(incluirNaListaPlanoProdutoSugerido(true, codigoProdutoMatricula, 50.0, 1, "MA"));
        listaProdutosSugeridos.add(incluirNaListaPlanoProdutoSugerido(true, codigoProdutoRematricula, 50.0, 1, "RE"));
        listaProdutosSugeridos.add(incluirNaListaPlanoProdutoSugerido(true, codigoProdutoRenovacao, 0.0, 1, "RN"));
        listaProdutosSugeridos.add(incluirNaListaPlanoProdutoSugerido(false, codigoProdutoAvaliacao, 30.0, 1, "SE"));
    }

    public void gravarPlano() throws Exception {
        incluirCondicoesPagamentoNaDuracaoPlano();
        incluirHorariosPlano();
        incluirProdutosSugeridosPlano();

        int codigoProdutoTaxaCancelamento = 0;
        int codigoProdutoPadraoGerarParcelas = 0;

        for (ProdutoVO produtoVO : listaProdutos) {
            if ("PLANO".equals(produtoVO.getDescricao())) {
                codigoProdutoPadraoGerarParcelas = produtoVO.getCodigo();
            } else if ("CUSTO ADMINISTRATIVO DO CANCELAMENTO".equals(produtoVO.getDescricao())) {
                codigoProdutoTaxaCancelamento = produtoVO.getCodigo();
            }
        }

        gravarPlano(false, 10.0, codigoProdutoTaxaCancelamento, listaPlanoTextoPadrao.get(0).getCodigo(), codigoProdutoPadraoGerarParcelas,
                false, Uteis.getDate("31/12/2025"), Uteis.getDate("31/12/2025"), Uteis.getDate("26/02/2010"),
                empresaVO.getCodigo(), "PLANO IMPORTACAO", false, "", incluirModalidadesPlano(),
                listaDuracoes, incluirComposicoesPlano(), listaHorariosVOs, listaProdutosSugeridos);
    }

    public void gravarPlano(
            boolean permitePagarComBoleto, double percentualMultaCancelamento,
            int produtoTaxaCancelamento, int planoTextoPadrao,
            int produtoPadraoGerarParcelasContrato, boolean bolsa,
            Date ingressoAte, Date vigenciaAte, Date vigenciaDe, int empresa,
            String descricao, boolean proRataObrigatorio,
            String diasVencimentoProRata,
            List<PlanoModalidadeVO> listaModalidades,
            List<PlanoDuracaoVO> listaDuracoes,
            List<PlanoComposicaoVO> listaComposicoesVOs,
            List<PlanoHorarioVO> listaHorariosVOs,
            List<PlanoProdutoSugeridoVO> listaProdutoSugeridoVOs)
            throws Exception {
        List<PlanoVO> listaPlanoVOs = getFacade().getPlano().consultarPorDescricao(descricao, 0, false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaPlanoVOs.isEmpty()) {
            planoVO = new PlanoVO();
            planoVO.setPermitePagarComBoleto(permitePagarComBoleto);
            planoVO.setPercentualMultaCancelamento(percentualMultaCancelamento);
            planoVO.setProdutoTaxaCancelamento(new ProdutoVO());
            planoVO.getProdutoTaxaCancelamento().setCodigo(produtoTaxaCancelamento);
            planoVO.setPlanoTextoPadrao(new PlanoTextoPadraoVO());
            planoVO.getPlanoTextoPadrao().setCodigo(planoTextoPadrao);
            planoVO.setProdutoPadraoGerarParcelasContrato(new ProdutoVO());
            planoVO.getProdutoPadraoGerarParcelasContrato().setCodigo(produtoPadraoGerarParcelasContrato);
            planoVO.setBolsa(bolsa);
            planoVO.setIngressoAte(ingressoAte);
            planoVO.setVigenciaAte(vigenciaAte);
            planoVO.setVigenciaDe(vigenciaDe);
            planoVO.setEmpresa(new EmpresaVO());
            planoVO.getEmpresa().setCodigo(empresa);
            planoVO.setDescricao(descricao);
            planoVO.setProrataObrigatorio(proRataObrigatorio);
            planoVO.setDiasVencimentoProrata(diasVencimentoProRata);
            planoVO.setPlanoModalidadeVOs(listaModalidades);
            planoVO.setPlanoDuracaoVOs(listaDuracoes);
            planoVO.setPlanoHorarioVOs(listaHorariosVOs);
            planoVO.setPlanoProdutoSugeridoVOs(listaProdutoSugeridoVOs);
            planoVO.setPlanoComposicaoVOs(listaComposicoesVOs);
            getFacade().getPlano().incluirSemCommit(planoVO);
        }
    }

    public PlanoComposicaoVO incluirNaListaPlanoComposicao(int composicao,
            int plano) throws Exception {
        PlanoComposicaoVO planoComposicaoVO = new PlanoComposicaoVO();
        planoComposicaoVO.setComposicao(new ComposicaoVO());
        planoComposicaoVO.getComposicao().setCodigo(composicao);
        planoComposicaoVO.setPlano(plano);
        return planoComposicaoVO;
    }

    public PlanoModalidadeVO incluirNaListaPlanoModalidade(
            String listaVezesSemana, int modalidade, int plano,
            List<PlanoModalidadeVezesSemanaVO> listaModalidadeVezesSemana)
            throws Exception {
        PlanoModalidadeVO planoModalidadeVO = new PlanoModalidadeVO();
        planoModalidadeVO.setListaVezesSemana(listaVezesSemana);
        planoModalidadeVO.setModalidade(new ModalidadeVO());
        planoModalidadeVO.getModalidade().setCodigo(modalidade);
        planoModalidadeVO.setPlano(plano);
        planoModalidadeVO.setPlanoModalidadeVezesSemanaVOs(listaModalidadeVezesSemana);
        return planoModalidadeVO;
    }

    public PlanoDuracaoVO incluirNaListaPlanoDuracao(
            Double valorDesejadoMensal, Double valorDesejadoParcela,
            Double valorDesejado, String tipoOperacao, String tipoValor,
            int valorEspecifico, Double percentualDesconto,
            int nrMaximoParcelasCondPagamento, int plano, int numeroMeses,
            int carencia, List<PlanoCondicaoPagamentoVO> listaCondicoesPagamento)
            throws Exception {
        PlanoDuracaoVO planoDuracaoVO = new PlanoDuracaoVO();
        planoDuracaoVO.setValorDesejadoMensal(valorDesejadoMensal);
        planoDuracaoVO.setValorDesejadoParcela(valorDesejadoParcela);
        planoDuracaoVO.setValorDesejado(valorDesejado);
        planoDuracaoVO.setTipoOperacao(tipoOperacao);
        planoDuracaoVO.setTipoValor(tipoValor);
        planoDuracaoVO.setValorEspecifico(valorDesejado);
        planoDuracaoVO.setPercentualDesconto(percentualDesconto);
        planoDuracaoVO.setNrMaximoParcelasCondPagamento(nrMaximoParcelasCondPagamento);
        planoDuracaoVO.setPlano(plano);
        planoDuracaoVO.setNumeroMeses(numeroMeses);
        planoDuracaoVO.setCarencia(carencia);
        planoDuracaoVO.setPlanoCondicaoPagamentoVOs(listaCondicoesPagamento);
        return planoDuracaoVO;
    }

    public PlanoCondicaoPagamentoVO incluirNaListaPlanoCondicaoPagamento(
            Double percentualDesconto, int condicaoPagamento,
            int planoDuracao, String tipoOperacao, String tipoValor,
            Double valorEspecifico, int qtdParcela) {

        PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();
        planoCondicaoPagamentoVO.setPercentualDesconto(percentualDesconto);
        planoCondicaoPagamentoVO.setCondicaoPagamento(new CondicaoPagamentoVO());
        planoCondicaoPagamentoVO.getCondicaoPagamento().setCodigo(condicaoPagamento);
        planoCondicaoPagamentoVO.setPlanoDuracao(planoDuracao);
        planoCondicaoPagamentoVO.setTipoOperacao(tipoOperacao);
        planoCondicaoPagamentoVO.setTipoValor(tipoValor);
        planoCondicaoPagamentoVO.setValorEspecifico(valorEspecifico);
        if (qtdParcela != 0) {
            planoCondicaoPagamentoVO.setQtdParcela(qtdParcela);
        }
        return planoCondicaoPagamentoVO;
    }

    public PlanoModalidadeVezesSemanaVO incluirNaListaPlanoModalidadeVezesSemana(
            int nrVezesSemana, String tipoOperacao, String tipoValor,
            Double valorEspecifico, Double percentualDesconto,
            int planoModalidade) throws Exception {
        PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
        planoModalidadeVezesSemanaVO.setNrVezes(nrVezesSemana);
        planoModalidadeVezesSemanaVO.setTipoOperacao(tipoOperacao);
        planoModalidadeVezesSemanaVO.setValorEspecifico(valorEspecifico);
        planoModalidadeVezesSemanaVO.setPercentualDesconto(percentualDesconto);
        planoModalidadeVezesSemanaVO.setPlanoModalidade(planoModalidade);
        return planoModalidadeVezesSemanaVO;
    }

    public PlanoHorarioVO incluirNaListaPlanoHorario(String tipoOperacao,
            String tipoValor, Double valorEspecifico,
            Double percentualDesconto, int plano, HorarioVO horario) throws Exception {
        PlanoHorarioVO horarioVO = new PlanoHorarioVO();
        horarioVO.setTipoOperacao(tipoOperacao);
        horarioVO.setTipoValor(tipoValor);
        horarioVO.setValorEspecifico(valorEspecifico);
        horarioVO.setPercentualDesconto(percentualDesconto);
        horarioVO.setPlano(plano);
        horarioVO.setHorario(horario);
        return horarioVO;
    }

    public PlanoProdutoSugeridoVO incluirNaListaPlanoProdutoSugerido(
            boolean obrigatorio, int produto, Double valorProduto, int plano,
            String tipoProduto) {
        PlanoProdutoSugeridoVO planoProdutoSugeridoVO = new PlanoProdutoSugeridoVO();
        planoProdutoSugeridoVO.setObrigatorio(obrigatorio);
        planoProdutoSugeridoVO.setProduto(new ProdutoVO());
        planoProdutoSugeridoVO.getProduto().setCodigo(produto);
        planoProdutoSugeridoVO.setValorProduto(valorProduto);
        planoProdutoSugeridoVO.setPlano(plano);
        planoProdutoSugeridoVO.getProduto().setTipoProduto(tipoProduto);
        return planoProdutoSugeridoVO;
    }

    public void gravarLocaisAcesso() throws Exception {
        gravarLocalAcesso("PACTO - ACESSO", "ZILLYONWEB01", empresaVO, 0, 0, "ZILLYONWEB01", 5100);
        gravarLocalAcesso("RECEPCAO EMPRESA", "ZILLYONWEB01", empresaVO, 0, 0, "ZILLYONWEB01", 5100);
    }

    public void gravarLocalAcesso(String descricao, String nomecomputador,
            EmpresaVO empresa, int tempoentreacessos, int tempoentreacessoscolaborador, String servidorimpressoes,
            int portaservidorimp) throws Exception {
        List<LocalAcessoVO> listaAcessosCons = getFacade().getLocalAcesso().consultarPorNome(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaAcessosCons.isEmpty()) {
            LocalAcessoVO acessoVO = new LocalAcessoVO();
            acessoVO.setDescricao(descricao);
            acessoVO.setNomeComputador(nomecomputador);
            acessoVO.setEmpresa(empresa);
            acessoVO.setTempoEntreAcessos(tempoentreacessos);
            acessoVO.setTempoEntreAcessosColaborador(tempoentreacessoscolaborador);
            acessoVO.setServidorImpressoes(servidorimpressoes);
            acessoVO.setPortaServidorImp(portaservidorimp);
            getFacade().getLocalAcesso().incluirSemCommit(acessoVO);
            listaAcessos.add(acessoVO);
        } else {
            listaAcessos.addAll(listaAcessosCons);
        }
    }

    public void gravarColetores() throws Exception {
        gravarColetor("ENTRADA", "MODELO_COLETOR_TRIXSTANDARD",
                "{9BA4FF57-9B43-5343-A570-92F433E3A275}", "1",
                "MODOTRANSMISSAO_COLETOR_AUTO", 9600, 512, 1, 5000, 2, 5000,
                "SEJA BEM VINDO", "SENTIDOACESSO_COLETOR_ENTRADA", true,
                null, false, listaAcessos.get(0).getCodigo(), 0, 1, 2, false);
        gravarColetor("CATRACA", "MODELO_COLETOR_TRIXSTANDARD", "", "1",
                "MODOTRANSMISSAO_COLETOR_AUTO", 9600, 512, 1, 5000, 2, 5000,
                "", "SENTIDOACESSO_COLETOR_INDIFERENTE", true, null, false, listaAcessos.get(1).getCodigo(),
                0, 1, 1, false);
    }

    public void gravarColetor(String descricao, String modelo, String numserie,
            String portacomunicacao, String modotransmissao,
            int veloctransmissao, int resolucaodpi, int releentrada,
            int temporeleentrada, int relesaida, int temporelesaida,
            String msgdisplay, String sentidoacesso, boolean aguardagiro,
            byte[] arquivoprograma, boolean padraocadastro, int localacesso,
            int sensorentrada, int sensorsaida, int numeroterminal, boolean padraocadastrofacial)
            throws Exception {
        List<ColetorVO> listaColetores = getFacade().getColetor().consultarPorDescricao(descricao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaColetores.isEmpty()) {
            try {
                ColetorVO coletorVO = new ColetorVO();
                coletorVO.setDesativado(true);
                coletorVO.setDescricao(descricao);
                coletorVO.setModelo(modelo);
                coletorVO.setNumSerie(numserie);
                coletorVO.setPortaComunicacao(portacomunicacao);
                coletorVO.setModoTransmissao(modotransmissao);
                coletorVO.setVelocTransmissao(veloctransmissao);
                coletorVO.setResolucaoDPI(resolucaodpi);
                coletorVO.setReleEntrada(releentrada);
                coletorVO.setTempoReleEntrada(temporeleentrada);
                coletorVO.setReleSaida(relesaida);
                coletorVO.setTempoReleSaida(temporelesaida);
                coletorVO.setMsgDisplay(msgdisplay);
                coletorVO.setSentidoAcesso(sentidoacesso);
                coletorVO.setAguardaGiro(aguardagiro);
                coletorVO.setArquivoPrograma(arquivoprograma);
                coletorVO.setPadraoCadastro(padraocadastro);
                coletorVO.setLocalAcesso(localacesso);
                coletorVO.setSensorEntrada(sensorentrada);
                coletorVO.setSensorSaida(sensorsaida);
                coletorVO.setNumeroTerminal(numeroterminal);
                coletorVO.setPadraoCadastroFacial(padraocadastrofacial);
                getFacade().getColetor().incluirSemCommit(coletorVO);
            } catch (Exception e) {
                if (!e.getMessage().toUpperCase().contains("JÁ EXISTE OUTRO")) {
                    throw e;
                }
            }
        }
    }

    public void gravarBancos() throws Exception {
        gravarBanco(1, "BANCO DO BRASIL");
        gravarBanco(3, "BANCO DA AMAZONIA S.A.");
        gravarBanco(4, "BANCO DO NORDESTE DO BRASIL S.");
        gravarBanco(8, "BANCO SANTANDER MERIDIONAL S.A");
        gravarBanco(20, "BANCO DO ESTADO DE ALAGOAS S.A");
        gravarBanco(21, "BANESTES S.A BANCO DO ESTADO D");
        gravarBanco(22, "CREDIREAL");
        gravarBanco(24, "BANCO DE PERNAMBUCO S.A.-BANDE");
        gravarBanco(25, "BANCO ALFA S/A");
        gravarBanco(26, "BANCO DO ESTADO DO ACRE S.A.");
        gravarBanco(27, "BANCO DO ESTADO DE SANTA CATAR");
        gravarBanco(28, "BANEB");
        gravarBanco(29, "BANCO BANERJ S.A.");
        gravarBanco(30, "PARAIBAN BANCO DO ESTADO DA PA");
        gravarBanco(31, "BANCO DO ESTADO DE GOIAS S.A.");
        gravarBanco(32, "BANCO DO ESTADO DO MATO GROSSO");
        gravarBanco(33, "BANCO SANTANDER (BRASIL) S.A.");
        gravarBanco(34, "BANCO DO ESTADO DO AMAZONAS S.");
        gravarBanco(35, "BANCO DO ESTADO DO CEARA S.A.");
        gravarBanco(36, "BANCO DO ESTADO DO MARANHAO S.");
        gravarBanco(37, "BANCO DO ESTADO DO PARA S.A.");
        gravarBanco(38, "BANCO BANESTADO S.A.");
        gravarBanco(39, "BANCO DO ESTADO DO PIAUI S.A.");
        gravarBanco(40, "BANCO CARGILL S.A");
        gravarBanco(41, "BANCO DO ESTADO DO RIO GRANDE");
        gravarBanco(42, "BANCO J. SAFRA S.A");
        gravarBanco(44, "BANCO BVA SA");
        gravarBanco(45, "BANCO OPPORTUNITY S.A.");
        gravarBanco(47, "BANCO DO ESTADO DE SERGIPE S.A");
        gravarBanco(48, "BANCO BEMGE S.A.");
        gravarBanco(59, "BANCO DO ESTADO DE RONDONIA S.");
        gravarBanco(61, "BANCO ABB SA");
        gravarBanco(62, "BANCO1.NET S.A");
        gravarBanco(63, "IBIBANK S.A BANCO MULTIPLO");
        gravarBanco(64, "GOLDMAN SACHS DO BRASIL-BANCO");
        gravarBanco(65, "BANCO PATAGON S.A");
        gravarBanco(66, "BANCO MORGAN STANLEY DEAN WITT");
        gravarBanco(67, "BANCO BANEB SA");
        gravarBanco(70, "BRB - BANCO DE BRASILIA S.A.");
        gravarBanco(104, "CAIXA ECONOMICA FEDERAL");
        gravarBanco(106, "BANCO ITABANCO S.A.");
        gravarBanco(107, "BANCO BBM S.A");
        gravarBanco(109, "CREDIBANCO S.A.");
        gravarBanco(116, "BANCO BNL DO BRASIL S.A.");
        gravarBanco(148, "BANK OF AMERICA - BRASIL S.A.");
        gravarBanco(151, "BANCO NOSSA CAIXA S.A");
        gravarBanco(153, "CAIXA ECONOMICA ESTADUAL DO RI");
        gravarBanco(165, "BANCO NORCHEM S.A.");
        gravarBanco(166, "BANCO INTER-ATLANTICO S.A.");
        gravarBanco(168, "HSBC INVESTMENT BANK BRASIL S.");
        gravarBanco(175, "CONTINENTAL BANCO S.A.");
        gravarBanco(184, "BANCO BBA - CREDITANSTALT S.A.");
        gravarBanco(199, "BANCO FINANCIAL PORTUGUES");
        gravarBanco(200, "BANCO FICRISA AXELRUD S.A.");
        gravarBanco(201, "BANCO AXIAL S.A.");
        gravarBanco(204, "BANCO INTER AMERICAN EXPRESS S");
        gravarBanco(205, "BANCO SUL AMERICA S.A.");
        gravarBanco(206, "BANCO MARTINELLI S.A.");
        gravarBanco(208, "BANCO PACTUAL S.A.");
        gravarBanco(210, "DRESDNER BANK LATEINAMERIKA AK");
        gravarBanco(211, "BANCO SISTEMA S.A.");
        gravarBanco(212, "BANCO MATONE S.A.");
        gravarBanco(213, "BANCO ARBI S.A.");
        gravarBanco(214, "BANCO DIBENS S.A.");
        gravarBanco(215, "BANCO AMERICA DO SUL S.A.");
        gravarBanco(216, "BANCO REGIONAL MALCON S.A.");
        gravarBanco(217, "BANCO JOHN DEERE S.A.");
        gravarBanco(218, "BANCO BONSUCESSO S.A.");
        gravarBanco(219, "BANCO ZOGBI");
        gravarBanco(220, "BANCO CREFISUL S.A.");
        gravarBanco(221, "BANCO CHASE FLEMING S.A.");
        gravarBanco(222, "BANCO CREDIT LYONNAIS BRASIL S");
        gravarBanco(224, "BANCO FIBRA S.A.");
        gravarBanco(225, "BANCO BRASCAN S.A.");
        gravarBanco(228, "BANCO ICATU S.A.");
        gravarBanco(229, "BANCO CRUZEIRO DO SUL S.A.");
        gravarBanco(230, "BANCO BANDEIRANTES S.A.");
        gravarBanco(231, "BANCO BOAVISTA INTERATLANTICO");
        gravarBanco(232, "BANCO INTERPART S.A.");
        gravarBanco(233, "BANCO GE CAPITAL S.A");
        gravarBanco(234, "BANCO LAVRA S.A.");
        gravarBanco(235, "BANCO LIBERAL S.A.");
        gravarBanco(236, "BANCO CAMBIAL SA");
        gravarBanco(237, "BANCO BRADESCO S.A.");
        gravarBanco(239, "BANCO BANCRED S.A.");
        gravarBanco(240, "BANCO DE CREDITO REAL DE MINAS");
        gravarBanco(241, "BANCO CLASSICO S.A.");
        gravarBanco(242, "BANCO EUROINVEST S.A. EUROBANC");
        gravarBanco(243, "BANCO STOCK MAXIMA S.A.");
        gravarBanco(244, "BANCO CIDADE S.A.");
        gravarBanco(245, "BANCO EMPRESARIAL S.A.");
        gravarBanco(246, "BANCO ABC-BRASIL S.A.");
        gravarBanco(247, "UBS WARBURG S.A.");
        gravarBanco(249, "BANCO INVESTCRED S.A.");
        gravarBanco(250, "BANCO SCHAHIN");
        gravarBanco(252, "BANCO FININVEST S.A.");
        gravarBanco(254, "PARANA BANCO S.A.");
        gravarBanco(255, "MILBANCO S.A.");
        gravarBanco(256, "BANCO GULFINVEST S.A.");
        gravarBanco(258, "BANCO INDUSCRED S.A.");
        gravarBanco(262, "BANCO BOREAL S.A.");
        gravarBanco(263, "BANCO CACIQUE S.A.");
        gravarBanco(265, "BANCO FATOR S.A.");
        gravarBanco(266, "BANCO CEDULA S.A.");
        gravarBanco(267, "BANCO BBM-COM.C.IMOB.CFI S.A.");
        gravarBanco(275, "BANCO ABN AMRO REAL S.A.");
        gravarBanco(277, "BANCO PLANIBANC S.A.");
        gravarBanco(282, "BANCO BRASILEIRO COMERCIAL S.A");
        gravarBanco(291, "BANCO DE CREDITO NACIONAL S.A.");
        gravarBanco(294, "BCR");
        gravarBanco(300, "BANCO DE LA NACION ARGENTINA");
        gravarBanco(302, "BANCO DO PROGRESSO S.A.");
        gravarBanco(303, "BANCO HNF S.A.");
        gravarBanco(304, "BANCO PONTUAL S.A.");
        gravarBanco(318, "BANCO BMG S.A.");
        gravarBanco(320, "BANCO INDUSTRIAL E COMERCIAL S");
        gravarBanco(341, "BANCO ITAU S.A.");
        gravarBanco(346, "BANCO BFB");
        gravarBanco(347, "BANCO SUDAMERIS BRASIL S.A.");
        gravarBanco(351, "BANCO BOZANO, SIMONSEN S.A.");
        gravarBanco(353, "BANCO SANTANDER BRASIL S.A.");
        gravarBanco(356, "BANCO ABN AMRO S.A.");
        gravarBanco(366, "BANCO SOCIETE GENERALE BRASIL");
        gravarBanco(369, "BANCO DIGIBANCO S.A.");
        gravarBanco(370, "BANCO EUROPEU PARA AMERICA LAT");
        gravarBanco(372, "BANCO ITAMARATI S.A.");
        gravarBanco(375, "BANCO FENICIA S.A.");
        gravarBanco(376, "BANCO CHASE MANHATTAN S.A.");
        gravarBanco(388, "BANCO BMD S.A.");
        gravarBanco(389, "BANCO MERCANTIL DO BRASIL S.A.");
        gravarBanco(392, "BANCO MERCANTIL DE SAO PAULO S");
        gravarBanco(394, "BANCO BMC S.A.");
        gravarBanco(399, "HSBC BANK BRASIL S.A.-BANCO MU");
        gravarBanco(409, "UNIBANCO - UNIAO DE BANCOS BRA");
        gravarBanco(412, "BANCO CAPITAL S.A.");
        gravarBanco(415, "BANCO NACIONAL S.A.");
        gravarBanco(420, "BANORTE - BANCO NACIONAL DO NO");
        gravarBanco(422, "BANCO SAFRA S.A.");
        gravarBanco(424, "BANCO SANTANDER NOROESTE S.A.");
        gravarBanco(434, "BANFORT - BANCO FORTALEZA S.A.");
        gravarBanco(453, "BANCO RURAL S.A.");
        gravarBanco(456, "BANCO DE TOKYO MITSUBISHI BRAS");
        gravarBanco(464, "BANCO SUMITOMO MITSUI BRASILEI");
        gravarBanco(472, "LLOYDS BANK PLC");
        gravarBanco(473, "BANCO FINANCIAL PORTUGUES");
        gravarBanco(479, "BANKBOSTON BANCO MULTIPLO S.A.");
        gravarBanco(480, "BANCO WACHOVIA S.A.");
        gravarBanco(487, "DEUTSCHE BANK S. A. - BANCO AL");
        gravarBanco(488, "MORGAN GUARANTY TRUST COMPANY");
        gravarBanco(489, "BANCO FRANCES INTERNACIONAL-BR");
        gravarBanco(492, "ING BANK N.V.");
        gravarBanco(493, "BANCO UNION - BRASIL S.A");
        gravarBanco(494, "BANCO DE LA REPUBLICA ORIENTAL");
        gravarBanco(495, "BANCO DE LA PROVINCIA DE BUENO");
        gravarBanco(496, "BANCO UNO-E BRASIL S.A");
        gravarBanco(498, "CENTRO HISPANO BANCO");
        gravarBanco(499, "BANCO IOCHPE S.A.");
        gravarBanco(501, "BANCO BRASILEIRO IRAQUIANO S.A");
        gravarBanco(502, "BANCO SANTANDER DE NEGOCIOS S.");
        gravarBanco(504, "BANCO MULTIPLIC S.A.");
        gravarBanco(505, "BANCO CREDIT SUISSE FIRST BOST");
        gravarBanco(600, "BANCO LUSO BRASILEIRO S.A.");
        gravarBanco(602, "BANCO PATENTE S.A.");
        gravarBanco(604, "BANCO INDUSTRIAL DO BRASIL S.");
        gravarBanco(607, "BANCO SANTOS NEVES S.A.");
        gravarBanco(610, "BANCO VR S.A.");
        gravarBanco(611, "BANCO PAULISTA S.A.");
        gravarBanco(612, "BANCO GUANABARA S.A.");
        gravarBanco(613, "BANCO PECUNIA S.A.");
        gravarBanco(618, "BANCO TENDENCIA S.A.");
        gravarBanco(621, "BANCO APLICAP S.A.");
        gravarBanco(623, "BANCO PANAMERICANO S.A.");
        gravarBanco(624, "BANCO GENERAL MOTORS S.A");
        gravarBanco(625, "BANCO ARAUCARIA S.A.");
        gravarBanco(626, "BANCO FICSA S.A.");
        gravarBanco(627, "BANCO DESTAK S.A.");
        gravarBanco(628, "BANCO CRITERIUM S. A.");
        gravarBanco(630, "BANCO INTERCAP S.A.");
        gravarBanco(633, "BANCO RENDIMENTO S.A.");
        gravarBanco(634, "BANCO TRIANGULO S.A.");
        gravarBanco(635, "BANCO DO ESTADO AMAPA S.A.");
        gravarBanco(637, "BANCO SOFISA S.A.");
        gravarBanco(638, "BANCO PROSPER S.A.");
        gravarBanco(640, "BANCO CREDITO METROPOLITANO S/");
        gravarBanco(641, "BANCO BILBAO VIZCAYA ARGENTARI");
        gravarBanco(643, "BANCO PINE S.A.");
        gravarBanco(645, "BANCO DO ESTADO DE RORAIMA S.A");
        gravarBanco(647, "BANCO MARKA S.A.");
        gravarBanco(649, "BANCO DIMENSAO S.A.");
        gravarBanco(650, "BANCO PEBB S.A.");
        gravarBanco(652, "BANCO FRANCES E BRASILEIRO SA");
        gravarBanco(653, "BANCO INDUSVAL S.A.");
        gravarBanco(654, "BANCO A.J. RENNER S.A.");
        gravarBanco(655, "BANCO VOTORANTIM S.A.");
        gravarBanco(656, "BANCO MATRIX S.A.");
        gravarBanco(657, "BANCO TECNICORP S.A.");
        gravarBanco(658, "BANCO PORTO REAL S.A.");
        gravarBanco(702, "BANCO SANTOS S. A.");
        gravarBanco(707, "BANCO DAYCOVAL S.A.");
        gravarBanco(711, "BANCO VETOR S.A.");
        gravarBanco(715, "BANCO VEGA S.A.");
        gravarBanco(718, "BANCO OPERADOR S.A.");
        gravarBanco(719, "BANCO BANIF PRIMUS S.A.");
        gravarBanco(720, "BANCO MAXINVEST S.A.");
        gravarBanco(721, "BANCO CREDIBEL S.A.");
        gravarBanco(722, "BANCO INTERIOR DE SAO PAULO S.");
        gravarBanco(725, "BANCO FINANSINOS S. A.");
        gravarBanco(728, "BANCO FITAL S.A.");
        gravarBanco(729, "BANCO FONTE CINDAM S.A.");
        gravarBanco(732, "BANCO MINAS S.A.");
        gravarBanco(733, "BANCO DAS NACOES S.A.");
        gravarBanco(734, "BANCO GERDAU S.A.");
        gravarBanco(735, "BANCO POTTENCIAL S.A.");
        gravarBanco(737, "BANCO THECA S.A.");
        gravarBanco(738, "BANCO MORADA S.A.");
        gravarBanco(739, "BANCO BGN S.A.");
        gravarBanco(740, "BANCO BARCLAYS E GALICIA S.A.");
        gravarBanco(741, "BANCO RIBEIRAO PRETO S.A.");
        gravarBanco(742, "BANCO EQUATORIAL S.A.");
        gravarBanco(743, "BANCO EMBLEMA S.A.");
        gravarBanco(744, "BANKBOSTON N.A.");
        gravarBanco(745, "BANCO CITIBANK S.A.");
        gravarBanco(746, "BANCO MODAL S.A.");
        gravarBanco(747, "BANCO RABOBANK INTERNATIONAL B");
        gravarBanco(748, "BANCO COOPERATIVO SICREDI S.A.");
        gravarBanco(749, "BR BANCO MERCANTIL S.A.");
        gravarBanco(750, "HSBC REPUBLIC BANK BRASIL S.A-");
        gravarBanco(751, "DRESDNER BANK BRASIL S.A. BANC");
        gravarBanco(752, "BANCO BANQUE NATIONALE DE PARI");
        gravarBanco(753, "BANCO COMERCIAL URUGUAI S.A.");
        gravarBanco(755, "BANCO MERRILL LYNCH S.A.");
        gravarBanco(756, "BANCO COOPERATIVO DO BRASIL S.");
        gravarBanco(757, "BANCO KEB DO BRASIL S.A.");
        gravarBanco(800, "BCR BANCO DE CREDITO REAL S.A");
    }

    public void gravarBanco(int codigoBanco, String nome) throws Exception {
        List<BancoVO> listaBancos = getFacade().getBanco().consultarPorNome(nome, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaBancos.isEmpty()) {
            BancoVO bancoVO = new BancoVO();
            bancoVO.setCodigoBanco(codigoBanco);
            bancoVO.setNome(nome);
            getFacade().getBanco().incluir(bancoVO);
        }
    }

    public void gravarClientes() throws Exception {
        listaQuestionarioCliente.add(incluirQuestionario(Uteis.getDate("03/02/2011"), listaColaboradores.get(0).getCodigo(), questionario1, ""));

        CidadeVO cidadeVO = new CidadeVO();
        cidadeVO.setCodigo(1);

        EstadoVO estadoVO = new EstadoVO();
        estadoVO.setCodigo(1);

        PessoaVO pessoaVO = povoarPessoa("", null, 4, "M", "GOIÂNIA", "BRASILEIRA", "S", new PaisVO(), estadoVO, cidadeVO,
                "GO", "DGPC", "5000609", "", "", "", Uteis.getDate("02/11/1987"), "AA_PRIMEIRO CLIENTE", Uteis.getDate("01/01/2011"),
                getFacade().getProfissao().consultarCodigoPorDescricao("ADMINISTRADOR"));

        gravarCliente(new ProdutoVO(), 0, 1, "", "", "", "", "", "", "1000010007", "", null, "000001", "VI",
                empresaVO.getCodigo(), listaQuestionarioCliente.get(0), pessoaVO);

        gravarEndereco(pessoaVO, "CO", "74.270-170", "JARDIM AMÉRICA", "246", "", "RUA C 200", true);
        gravarEmail(pessoaVO, true, "<EMAIL>");
        gravarTelefone(pessoaVO.getCodigo(), "CO", "(62)32515820");
        gravarUsuarioMovel("pacto", "1abc.", new ColaboradorVO(), clienteVO, null);
    }

    public Integer consultaNumeroMatricula() throws Exception {
        consultar(getIdEntidade(), false);
        String sqlStr = "select * from numeromatricula";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("matricula");
        }
        return null;
    }

    public void gravarCliente(ProdutoVO freepass, int responsavelfreepass,
            int codigomatricula, String identificadorparacobranca,
            String contadigito, String conta, String agenciadigito,
            String agencia, String banco, String codacesso,
            String codacessoalternativo, CategoriaVO categoria,
            String matricula, String situacao, int empresa,
            QuestionarioClienteVO questionarioClienteVO, PessoaVO pessoa)
            throws Exception {
        List<ClienteVO> listaClienteVOs = getFacade().getCliente().consultarPorNomePessoa(pessoa.getNome(), empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null);
        if (listaClienteVOs.isEmpty()) {
            clienteVO = new ClienteVO();
            clienteVO.setFreePass(freepass);
            clienteVO.setResponsavelFreePass(responsavelfreepass);
            clienteVO.setCodigoMatricula(codigomatricula);
            clienteVO.setIdentificadorParaCobranca(identificadorparacobranca);
            clienteVO.setContaDigito(contadigito);
            clienteVO.setConta(conta);
            clienteVO.setAgenciaDigito(agenciadigito);
            clienteVO.setAgencia(agencia);
            clienteVO.setBanco(banco);
            clienteVO.setCodAcesso(codacesso);
            clienteVO.setCodAcessoAlternativo(codacessoalternativo);
            clienteVO.setCategoria(categoria);
            clienteVO.setMatricula(matricula);
            clienteVO.setSituacao(situacao);
            clienteVO.setPessoa(pessoa);
            clienteVO.setEmpresa(new EmpresaVO());
            clienteVO.getEmpresa().setCodigo(empresa);
            clienteVO.setUsuarioVO(new UsuarioVO());
            clienteVO.getUsuarioVO().setCodigo(1);
            getFacade().getCliente().incluirSemCommit(clienteVO, questionarioClienteVO, configuracaoSistemaVO, true);
        }
    }

    public QuestionarioClienteVO incluirQuestionario(Date data, int consultor, QuestionarioVO questionario, String observacao) throws Exception {
        QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
        questionarioClienteVO.setData(data);
        questionarioClienteVO.setConsultor(new ColaboradorVO());
        questionarioClienteVO.getConsultor().setCodigo(consultor);
        questionarioClienteVO.setQuestionario(questionario);
        questionarioClienteVO.setObservacao(observacao);
        return questionarioClienteVO;
    }

    public void gravarEndereco(PessoaVO pessoa, String tipoEndereco, String cep,
            String bairro, String numero, String complemento, String endereco,
            boolean enderecoCorrespondencia) throws Exception {
        List<EnderecoVO> listaEnderecoVOs = getFacade().getEndereco().consultarPorNomePessoa(pessoa.getNome(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaEnderecoVOs.isEmpty()) {
            EnderecoVO enderecoVO = new EnderecoVO();
            enderecoVO.setPessoa(pessoa.getCodigo());
            enderecoVO.setTipoEndereco(tipoEndereco);
            enderecoVO.setCep(cep);
            enderecoVO.setBairro(bairro);
            enderecoVO.setNumero(numero);
            enderecoVO.setComplemento(complemento);
            enderecoVO.setEndereco(endereco);
            enderecoVO.setEnderecoCorrespondencia(enderecoCorrespondencia);
            getFacade().getEndereco().incluir(enderecoVO);
        }
    }

    public void gravarEmail(PessoaVO pessoa, boolean emailcorrespondencia, String email) throws Exception {
        EmailVO emailVO = new EmailVO();
        emailVO.setPessoa(pessoa.getCodigo());
        emailVO.setEmailCorrespondencia(emailcorrespondencia);
        emailVO.setEmail(email);
        getFacade().getEmail().incluir(emailVO);
    }

    public void gravarTelefone(int pessoa, String tipoTelefone, String numero)
            throws Exception {
        List<TelefoneVO> listaTelefoneVOs = getFacade().getTelefone().consultarPorNumero(numero, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaTelefoneVOs.isEmpty()) {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setPessoa(pessoa);
            telefoneVO.setTipoTelefone(tipoTelefone);
            telefoneVO.setNumero(numero);
            getFacade().getTelefone().incluir(telefoneVO);
        }
    }

    public void gravarPerguntaCliente(boolean multipla, boolean simples,
            boolean textual, String tipoPergunta, String descricao)
            throws Exception {
        List<PerguntaClienteVO> listaClienteVOs = getFacade().getPerguntaCliente().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaClienteVOs.isEmpty()) {
            PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
            perguntaClienteVO.setMultipla(multipla);
            perguntaClienteVO.setSimples(simples);
            perguntaClienteVO.setTextual(textual);
            perguntaClienteVO.setDescricao(descricao);
            perguntaClienteVO.setTipoPergunta(tipoPergunta);
            getFacade().getPerguntaCliente().incluirPerguntaCliente(perguntaClienteVO, false, true);
            listaPerguntaCliente.add(perguntaClienteVO);
        }
    }

    public void gravarPerguntasCliente() throws Exception {
        gravarPerguntaCliente(true, false, false, "ME", "QUAIS AS ATIVIDADES DE INTERESSE?");
        gravarPerguntaCliente(true, false, false, "ME", "QUAIS SÃO SEUS OBJETIVOS?");
        gravarPerguntaCliente(false, true, false, "SE", "EM QUANTO TEMPO DESEJA ALCANÇAR SEUS OBJETIVOS?");
        gravarPerguntaCliente(false, true, false, "SE", "HÁ QUANTO TEMPO ESTÁ PENSANDO EM COMEÇAR ATIVIDADE FÍSICA?");
        gravarPerguntaCliente(true, false, false, "ME", "O QUE TE IMPEDIU DE COMEÇAR ANTES?");
        gravarPerguntaCliente(true, false, false, "ME", "COMO CONHECEU A ACADEMIA?");
    }

    public void gravarOperadorasCartao() throws Exception {
        gravarOperadoraCartao("VISA ELECTRON (DÉBITO)", 901, 1, false, 1);
        gravarOperadoraCartao("VISA (CRÉDITO)", 902, 0, true, 12);
        gravarOperadoraCartao("MASTERCARD (CRÉDITO)", 904, 0, true, 12);
        gravarOperadoraCartao("AMERICAN EXPRESS (CREDITO)", 905, 0, true, 12);
        gravarOperadoraCartao("DINNERS CLUB (CREDITO)", 906, 0, true, 12);
        gravarOperadoraCartao("MAESTRO (DEBITO)", 907, 2, false, 1);
        gravarOperadoraCartao("ELO (DEBITO)", 907, 0, false, 1);
        gravarOperadoraCartao("ELO (CREDITO)", 908, 0, true, 12);
    }

    public void gravarOperadoraCartao(String descricao, int codigoOperadora, int codIntegracao, boolean credito, int qtdMaximaParcelas) throws Exception {
        List<OperadoraCartaoVO> listaOperadoraCartaoVOs = getFacade().getOperadoraCartao().consultarPorDescricao(descricao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaOperadoraCartaoVOs.isEmpty()) {
            OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
            operadoraCartaoVO.setDescricao(descricao);
            operadoraCartaoVO.setCodigoOperadora(codigoOperadora);
            operadoraCartaoVO.setCodigoIntegracao(OperadorasExternasPagamentoDigitalEnum.valueOf(codIntegracao));
            operadoraCartaoVO.setCredito(credito);
            operadoraCartaoVO.setQtdeMaxParcelas(qtdMaximaParcelas);
            getFacade().getOperadoraCartao().incluir(operadoraCartaoVO);
        }
    }

    public void gravarQuestionarioCliente(PerguntaClienteVO perguntaCliente,
            int questionarioCliente) throws Exception {
        List<QuestionarioPerguntaClienteVO> listaQuestionarioPerguntaClienteVOs =
                getFacade().getQuestionarioPerguntaCliente().
                consultarPorDescricaoPerguntaCliente(perguntaCliente.getDescricao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaQuestionarioPerguntaClienteVOs.isEmpty()) {
            QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
            questionarioPerguntaClienteVO.setPerguntaCliente(perguntaCliente);
            questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioCliente);
            getFacade().getQuestionarioPerguntaCliente().incluir(questionarioPerguntaClienteVO, true, true);
        }
    }

    public void gravarRespostasQuestionarioCliente() throws Exception {
        gravarRespostaQuestionarioCliente(true, "MUSCULAÇÃO", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "CORRIDA", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "STEP", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "ABDOMINAL", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "GLÚTEO", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "PILATES", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "PUMP", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "BODY COMBAT", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "SPINNIG", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "JUMP", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "LUTAS", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "RITMOS", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "ALONGAMENTO", listaPerguntaCliente.get(0).getCodigo());
        gravarRespostaQuestionarioCliente(false, "TREIN. FUNCIONAL", listaPerguntaCliente.get(0).getCodigo());

        gravarRespostaQuestionarioCliente(true, "EMAGRECIMENTO", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(false, "AUMENTO DE MASSA", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(true, "DEFINIÇÃO MUSCULAR", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(false, "QUALIDADE DE VIDA", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(true, "CONDICIONAMENTO", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(true, "ALÍVIO DE STRESS", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(true, "REABILITAÇÃO", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(true, "LAZER", listaPerguntaCliente.get(1).getCodigo());
        gravarRespostaQuestionarioCliente(true, "FAZER AMIGOS", listaPerguntaCliente.get(1).getCodigo());

        gravarRespostaQuestionarioCliente(true, "3 MESES", listaPerguntaCliente.get(2).getCodigo());
        gravarRespostaQuestionarioCliente(false, "6 MESES", listaPerguntaCliente.get(2).getCodigo());
        gravarRespostaQuestionarioCliente(false, "9 MESES", listaPerguntaCliente.get(2).getCodigo());
        gravarRespostaQuestionarioCliente(false, "12 MESES", listaPerguntaCliente.get(2).getCodigo());
        gravarRespostaQuestionarioCliente(false, "MAIS", listaPerguntaCliente.get(2).getCodigo());

        gravarRespostaQuestionarioCliente(true, "MENOS DE UM MÊS", listaPerguntaCliente.get(3).getCodigo());
        gravarRespostaQuestionarioCliente(false, "MAIS OU MENOS 3 MESES", listaPerguntaCliente.get(3).getCodigo());
        gravarRespostaQuestionarioCliente(false, "MAIS OU MENOS 6 MESES", listaPerguntaCliente.get(3).getCodigo());
        gravarRespostaQuestionarioCliente(false, "MAIS OU MENOS 1 ANO", listaPerguntaCliente.get(3).getCodigo());
        gravarRespostaQuestionarioCliente(false, "A MAIS DE UM ANO", listaPerguntaCliente.get(3).getCodigo());

        gravarRespostaQuestionarioCliente(true, "TEMPO", listaPerguntaCliente.get(4).getCodigo());
        gravarRespostaQuestionarioCliente(false, "FINANCEIRO", listaPerguntaCliente.get(4).getCodigo());
        gravarRespostaQuestionarioCliente(true, "DISPOSIÇÃO", listaPerguntaCliente.get(4).getCodigo());

        gravarRespostaQuestionarioCliente(true, "AMIGOS", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "PROPAGANDA", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "OUTDOOR", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "EX-ALUNO", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "PASSANDO NA FRENTE", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "INTERNET", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "MALA DIRETA", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "LISTA TELEFÔNICA", listaPerguntaCliente.get(5).getCodigo());
        gravarRespostaQuestionarioCliente(false, "OUTROS", listaPerguntaCliente.get(5).getCodigo());

    }

    public void gravarRespostaQuestionarioCliente(Boolean respostaOpcao,
            String descricaoResposta, int perguntaCliente) throws Exception {
        List<RespostaPergClienteVO> listaPergClienteVOs =
                getFacade().getRespostaPergCliente().consultarPorDescricaoRespota(descricaoResposta, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaPergClienteVOs.isEmpty()) {
            RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
            respostaPergClienteVO.setDescricaoRespota(descricaoResposta);
            respostaPergClienteVO.setRespostaOpcao(respostaOpcao);
            respostaPergClienteVO.setPerguntaCliente(perguntaCliente);
            getFacade().getRespostaPergCliente().incluir(respostaPergClienteVO,
                    false);
        }
    }

    private void gravarTipoDocumentos() throws SQLException {
        gravarTipoDocumento("RECIBO");
        gravarTipoDocumento("CUPOM FISCAL");
        gravarTipoDocumento("NOTA FISCAL");
        gravarTipoDocumento("CHEQUE");
        gravarTipoDocumento("BOLETO");
        gravarTipoDocumento("SEM COMPROVANTE");
    }

    private void gravarTipoDocumento(String descricao) throws SQLException {
        String sql = "INSERT INTO tipodocumento(descricao) VALUES(?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, descricao);
        sqlInserir.execute();
    }

    public void gravarSituacoes() throws Exception {
        gravarSituacaoCliente(Calendario.hoje(), clienteVO.getCodigo(), Integer.parseInt(clienteVO.getMatricula()),
                "AA_PRIMEIRO CLIENTE", Uteis.getDate("02/11/1987"), 27, "ADMINISTRADOR(A)", "CO-PACTO - MÉTODO DE GESTÃO/",
                0, "VI", 0, "", "", 0.0, 0.0, 0.0, 0.0, null, null, null, null, null, null, Uteis.getDate("15/12/2014"), null,
                null, 0, 0, Calendario.hoje(), "", null, "", 0, "", "", null, null, 0, 0, 0, clienteVO.getPessoa().getCodigo());
    }

    public void gravarSituacaoCliente(Date dia, int codCliente, int matricula,
            String nomeCliente, Date dataNascimento, int idade,
            String profissao, String colaboradores, int codContrato,
            String situacao, int duracaoContratoMeses,
            String mnemonicoContrato, String nomePlano,
            Double valorFaturaContrato, Double valorPagoContrato,
            Double valorParcelaAbertoContrato,
            Double saldoContaCorrenteCliente, Date dataVigenciaDe,
            Date dataVigenciaAte, Date dataVigenciaAteAjustada,
            Date dataLancamentoContrato, Date dataRenovacaoContrato,
            Date dataRematriculaContrato, Date dataUltimoBV,
            Date dataMatricula, Date dataUltimaRematricula,
            int diasAssiduidadeUltRematriculaAteHoje,
            int diasAcessoSemanaPassada, Date dataUltimoAcesso, String faseAtualCrm,
            Date dataUltimoContatoCrm, String responsavelUltimoContatoCrm,
            int codigoUltimoContatoCrm, String situacaoContrato,
            String tipoPeriodoAcesso, Date dataInicioPeriodoAcesso,
            Date dataFimPeriodoAcesso, int diasacessosemana2,
            int diasAcessoSemana3, int diasAcessoSemana4, int codigoPessoa) throws Exception {
        SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVOs =
                getFacade().getSituacaoClienteSinteticoDW().consultarCliente(codCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (situacaoClienteSinteticoDWVOs.getCodigo() == 0) {
            SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = new SituacaoClienteSinteticoDWVO();
            situacaoClienteSinteticoDWVO.setDia(dia);
            situacaoClienteSinteticoDWVO.setCodigoCliente(codCliente);
            situacaoClienteSinteticoDWVO.setMatricula(matricula);
            situacaoClienteSinteticoDWVO.setNomeCliente(nomeCliente);
            situacaoClienteSinteticoDWVO.setDataNascimento(dataNascimento);
            situacaoClienteSinteticoDWVO.setIdade(idade);
            situacaoClienteSinteticoDWVO.setProfissao(profissao);
            situacaoClienteSinteticoDWVO.setColaboradores(colaboradores);
            situacaoClienteSinteticoDWVO.setCodigoContrato(codContrato);
            situacaoClienteSinteticoDWVO.setSituacao(situacao);
            situacaoClienteSinteticoDWVO.setDuracaoContratoMeses(duracaoContratoMeses);
            situacaoClienteSinteticoDWVO.setMnemonicoContrato(mnemonicoContrato);
            situacaoClienteSinteticoDWVO.setNomePlano(nomePlano);
            situacaoClienteSinteticoDWVO.setValorFaturadoContrato(valorFaturaContrato);
            situacaoClienteSinteticoDWVO.setValorPagoContrato(valorPagoContrato);
            situacaoClienteSinteticoDWVO.setValorParcAbertoContrato(valorParcelaAbertoContrato);
            situacaoClienteSinteticoDWVO.setSaldoContaCorrenteCliente(saldoContaCorrenteCliente);
            situacaoClienteSinteticoDWVO.setDataVigenciaDe(dataVigenciaDe);
            situacaoClienteSinteticoDWVO.setDataVigenciaAte(dataVigenciaAte);
            situacaoClienteSinteticoDWVO.setDataVigenciaAteAjustada(dataVigenciaAteAjustada);
            situacaoClienteSinteticoDWVO.setDataLancamentoContrato(dataLancamentoContrato);
            situacaoClienteSinteticoDWVO.setDataRenovacaoContrato(dataRenovacaoContrato);
            situacaoClienteSinteticoDWVO.setDataRematriculaContrato(dataRematriculaContrato);
            situacaoClienteSinteticoDWVO.setDataUltimoBV(dataUltimoBV);
            situacaoClienteSinteticoDWVO.setDataMatricula(dataMatricula);
            if (dataUltimaRematricula != Calendario.hoje()) {
                situacaoClienteSinteticoDWVO.setDataUltimaRematricula(dataUltimaRematricula);
            }
            situacaoClienteSinteticoDWVO.setDiasAssiduidadeUltRematriculaAteHoje(diasAssiduidadeUltRematriculaAteHoje);
            situacaoClienteSinteticoDWVO.setDiasAcessoSemanaPassada(diasAcessoSemanaPassada);
            if (dataUltimoAcesso != Calendario.hoje()) {
                situacaoClienteSinteticoDWVO.setDataUltimoAcesso(dataUltimoAcesso);
            }
            situacaoClienteSinteticoDWVO.setFaseAtualCRM(faseAtualCrm);
            if (dataUltimoContatoCrm != Calendario.hoje()) {
                situacaoClienteSinteticoDWVO.setDataUltimoContatoCRM(dataUltimoContatoCrm);
            }
            situacaoClienteSinteticoDWVO.setResponsavelUltimoContatoCRM(responsavelUltimoContatoCrm);
            situacaoClienteSinteticoDWVO.setCodigoUltimoContatoCRM(codigoUltimoContatoCrm);
            situacaoClienteSinteticoDWVO.setSituacaoContrato(situacaoContrato);
            situacaoClienteSinteticoDWVO.setTipoPeriodoAcesso(tipoPeriodoAcesso);
            situacaoClienteSinteticoDWVO.setDataInicioPeriodoAcesso(dataInicioPeriodoAcesso);
            situacaoClienteSinteticoDWVO.setDataFimPeriodoAcesso(dataFimPeriodoAcesso);
            situacaoClienteSinteticoDWVO.setDiasAcessoSemana2(diasacessosemana2);
            situacaoClienteSinteticoDWVO.setDiasAcessoSemana3(diasAcessoSemana3);
            situacaoClienteSinteticoDWVO.setDiasAcessoSemana4(diasAcessoSemana4);
            situacaoClienteSinteticoDWVO.setCodigoPessoa(codigoPessoa);
            getFacade().getSituacaoClienteSinteticoDW().incluirSemCommit(situacaoClienteSinteticoDWVO, null);
        }
    }

    private void gravarUsuarioMovel(String nome, String senha, ColaboradorVO colaborador, ClienteVO cliente, Integer usuarioZW) throws Exception {
        UsuarioMovelVO usuarioMovelVO = new UsuarioMovelVO();
        usuarioMovelVO.setNome(nome);
        usuarioMovelVO.setSenha(senha);
        usuarioMovelVO.setColaborador(colaborador);
        usuarioMovelVO.setCliente(cliente);
        usuarioMovelVO.setAtivo(true);
        usuarioMovelVO.setUsuarioZW(usuarioZW);
        usuarioMovelVO.setEmpresa(empresaVO.getCodigo());
        getFacade().getUsuarioMovel().incluir(usuarioMovelVO);
    }

    public void setarFalseCampoRodarSqlsBancoInicial() throws Exception {
        configuracaoSistemaVO.setRodarSqlsBancoInicial(false);
        getFacade().getConfiguracaoSistema().alterarSemCommit(configuracaoSistemaVO);
    }

    public List<MovProdutoVO> getListaMovProduto() {
        return listaMovProduto;
    }

    public void setListaMovProduto(List<MovProdutoVO> listaMovProduto) {
        this.listaMovProduto = listaMovProduto;
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
        return configuracaoSistemaCRMVO;
    }

    public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
    }

    private List<TabelaZW> retornarListaTabelasZW(JSONArray jsonBDZillyon) throws Exception{
        List<TabelaZW> listaTabelaZW = new ArrayList<TabelaZW>();
        for (int i=0; i<jsonBDZillyon.length(); i++){
            JSONObject jsonObject = (JSONObject)jsonBDZillyon.get(i);
            Object object = jsonObject.get("nomeTabela");
            TabelaZWEnum tabelaZWEnum = TabelaZWEnum.getPorNomeTabela((String) object);
            if (tabelaZWEnum != null){
                TabelaZW tabelaZW = new TabelaZW();
                tabelaZW.setTabelaZWEnum(tabelaZWEnum);
                tabelaZW.setJsonArray(new JSONArray(jsonObject.get("dadosJson").toString()));
                listaTabelaZW.add(tabelaZW);
            }
        }
        Collections.sort(listaTabelaZW, new Comparator() {

            @Override
            public int compare(Object o1, Object o2) {
                TabelaZW obj1 = (TabelaZW) o1;
                TabelaZW obj2 = (TabelaZW) o2;
                if (obj1.getTabelaZWEnum().getOrdem() > obj2.getTabelaZWEnum().getOrdem()) {
                    return +1;
                } else {
                    return -1;
                }
            }
        });

        return listaTabelaZW;
    }

    private void incluirMapaInclusaoTabelasAux(Object VO)throws Exception{
        // Atualizar o mapa de inclusão com as tabelas auxiliares que podem ser utilizadas por outras entidades.
        if (VO instanceof LocalAcessoVO){
            for (ColetorVO coletorVO:  ((LocalAcessoVO)VO).getListaColetores()){
                // O coletor é utilizado ao alterar o cadastro de empresa.
                Integer codigoPesquisado = connsultarColetor(coletorVO.getDescricao());
                Integer novoCodigo = coletorVO.getCodigo();
                if (codigoPesquisado != null){
                    novoCodigo = codigoPesquisado;
                }
                incluirMapaInclusao(coletorVO, coletorVO.getCodigoAux(), novoCodigo);
            }
        }else if (VO instanceof PaisVO){
            for (Object object: ((PaisVO) VO).getEstadoVOs()){
                EstadoVO estadoVO = (EstadoVO)object;
                incluirMapaInclusao(estadoVO, estadoVO.getCodigoAux(), estadoVO.getCodigo());
            }
        }
    }

    private Integer connsultarColetor(String descricao)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from coletor where descricao = ?");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setString(1, descricao.toUpperCase());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return rs.getInt("codigo");
        }
        return null;
    }

    private void quardarCodigoAntigoTabelasAux(Object VO)throws Exception{
        /*
           Quardar o codigo original das tabelas, pois ao realizar a inclusão os codigos são perdidos.
         */
        if (VO instanceof LocalAcessoVO){
            for (ColetorVO coletorVO:  ((LocalAcessoVO)VO).getListaColetores()){
                // A inclusão do coletor é feita no metodo de incluir LocalAcesso.
                coletorVO.setCodigoAux(coletorVO.getCodigo());
            }
        }else if (VO instanceof PaisVO){
            for (Object object: ((PaisVO) VO).getEstadoVOs()){
                EstadoVO estadoVO = (EstadoVO)object;
                estadoVO.setCodigoAux(estadoVO.getCodigo());
            }
        }
    }

    private boolean verificaIncluirVO(Object VO)throws Exception{
        if (VO instanceof UsuarioVO){
            UsuarioVO usuarioVO = (UsuarioVO)VO;
            if (usuarioVO.getUsername().toUpperCase().equals("ADMIN")){
                return false;
            }
        }else if ((VO instanceof ConfiguracaoSistemaVO) || (VO instanceof ConfiguracaoSistemaCRMVO) ){
            return false;
        }
        return true;
    }

    private void povoarTabelasZW(TabelaZW tabelaZW, boolean modoAlteracao) throws Exception{
        String tabela = "";
        try{
            tabela = tabelaZW.getTabelaZWEnum().getNomeTabela();
            for (int i = 0; i < tabelaZW.getJsonArray().length(); i++) {
                Object dao = UtilReflection.invoke(getFacade(), tabelaZW.getTabelaZWEnum().getNomeMetodoDaoFacadeFactory());
                Object VO = UteisJSON.jsonToObject(tabelaZW.getTabelaZWEnum().getClazzVO(), (JSONObject) tabelaZW.getJsonArray().get(i));
                atualizarFKs(VO, tabelaZW, modoAlteracao);
                UtilReflection.setValor(VO,false,"validarDados", false);
                Integer codigoAntigo = (Integer)  UtilReflection.invoke(VO, "getCodigo");
                Integer codigoPesquisado = consultarRegistroBD(tabelaZW, VO);

                quardarCodigoAntigoTabelasAux(VO);
                if ((codigoPesquisado == null) || (codigoPesquisado <=0)){
                    if (tabela.toUpperCase().equals("CLIENTE")){
                        ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS);
                        ClienteVO clienteVO = (ClienteVO)VO;
                        for (VinculoVO vinculoVO: clienteVO.getVinculoVOs()){
                            vinculoVO.setCodigo(0);
                        }
                        clienteVO.setUsuarioVO(new UsuarioVO());
                        clienteVO.getUsuarioVO().setCodigo(1);
                        EmpresaVO empresaVO = new EmpresaVO();
                        empresaVO.setCodigo(this.mapaInclusao.get("EmpresaVO").get(this.mapaInclusao.get("EmpresaVO").keySet().iterator().next()));
                        clienteVO.setMatricula("");
                        ((Cliente) dao).gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO);
                        UtilReflection.invoke(dao, tabelaZW.getTabelaZWEnum().getNomeMetodoIncluir(), new Class[]{ClienteVO.class, ConfiguracaoSistemaVO.class}, new Object[]{VO, configuracaoSistemaVO});
                        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                        zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                        zwFacade = null;
                    }else{
                        if (tabela.toUpperCase().equals("USUARIO")){
                            ((UsuarioVO)VO).setEncriptarSenha(false);
                        }
                        if (verificaIncluirVO(VO)){
                            UtilReflection.invoke(dao, tabelaZW.getTabelaZWEnum().getNomeMetodoIncluir(), new Class[]{tabelaZW.getTabelaZWEnum().getClazzVO()}, new Object[]{VO});
                        }
                    }
                }else{
                    if ((modoAlteracao) &&  ((tabelaZW.getTabelaZWEnum().getNomeMetodoAlterar() != null) && (!tabelaZW.getTabelaZWEnum().getNomeMetodoAlterar().equals("")))){
                        String nomeMetodoAlterar = tabelaZW.getTabelaZWEnum().getNomeMetodoAlterar();
                        if ((nomeMetodoAlterar != null) && (!(nomeMetodoAlterar.trim().equals("")))){
                            UtilReflection.setValor(VO, codigoPesquisado, "codigo", false);
                            UtilReflection.invoke(dao, nomeMetodoAlterar, new Class[]{tabelaZW.getTabelaZWEnum().getClazzVO()}, new Object[]{VO});
                        }
                    }
                }
                Integer codigoNovo = (Integer)  UtilReflection.invoke(VO, "getCodigo");
                if ((codigoPesquisado != null) && (codigoPesquisado >0)){
                    codigoNovo =  codigoPesquisado;
                }
                incluirMapaInclusao(VO, codigoAntigo, codigoNovo);
                incluirMapaInclusaoTabelasAux(VO);

            }

        }catch (Exception e){
            String erro = "ERRO AO IMPORTAR TABELA: " + tabela + " Erro:" + e.getMessage() + e.getCause();
            Uteis.logar(null, erro);
            throw new ConsistirException(erro);
        }

    }
    
    private String consultarDescricaoVO(Object VO, TabelaZW tabelaZW)throws Exception{
        if ((tabelaZW.getTabelaZWEnum().getNomeCampoDescricaoVO() == null) || (tabelaZW.getTabelaZWEnum().getNomeCampoDescricaoVO().trim().equals(""))){
            return "";
        }
        String nomeCampo = tabelaZW.getTabelaZWEnum().getNomeCampoDescricaoVO().substring(0, 1).toUpperCase() + tabelaZW.getTabelaZWEnum().getNomeCampoDescricaoVO().substring(1);
        return (String)  UtilReflection.invoke(VO, "get" + nomeCampo);
    }

    public void atualizarFKs(Object VO, TabelaZW tabelaZW, boolean modoAlteracao) throws Exception{
       if (modoAlteracao){
           tabelaZW.setAtualizarFKAposInclusao(false);
       }else{
           tabelaZW.setAtualizarFKAposInclusao(atualizarFKAposIncluir(VO));
       }
       if (VO instanceof QuestionarioVO){
            QuestionarioVO questionarioVO = (QuestionarioVO) VO;
            for (QuestionarioPerguntaVO questionarioPerguntaVO:  questionarioVO.getQuestionarioPerguntaVOs()){
                Integer codigoNovo = consultarNovoCodigoMapaInclusao("PerguntaVO", questionarioPerguntaVO.getPergunta().getCodigo()) ;
                // atualizar a FK para Pergunta, pois a tabela de Pergunta foi incluida antes da tabela Questionario
                questionarioPerguntaVO.getPergunta().setCodigo(codigoNovo);
            }
            return;
        }else if (VO instanceof ModalidadeEmpresaVO){
           ModalidadeEmpresaVO modalidadeEmpresaVO = (ModalidadeEmpresaVO)VO;
           Integer codigoNovoModalidade = consultarNovoCodigoMapaInclusao("ModalidadeVO", modalidadeEmpresaVO.getModalidade());
           modalidadeEmpresaVO.setModalidade(codigoNovoModalidade);
           Integer codigoNovoEmpresa = consultarNovoCodigoMapaInclusao("EmpresaVO", modalidadeEmpresaVO.getEmpresa().getCodigo());
           modalidadeEmpresaVO.getEmpresa().setCodigo(codigoNovoEmpresa);
           return;
       }else if (VO instanceof HorarioTurmaVO){
           HorarioTurmaVO horarioTurmaVO = (HorarioTurmaVO)VO;
           Integer codigoNovoTurma = consultarNovoCodigoMapaInclusao("TurmaVO", horarioTurmaVO.getTurma());
           horarioTurmaVO.setTurma(codigoNovoTurma);
       }else if (VO instanceof RateioIntegracaoTO){
           RateioIntegracaoTO rateioIntegracaoTO = (RateioIntegracaoTO)VO;
           Integer codigoNovo = consultarNovoCodigoMapaInclusao("PlanoContaTO", rateioIntegracaoTO.getCodigoPlanoContas());
           rateioIntegracaoTO.setCodigoPlanoContas(codigoNovo);
           codigoNovo = consultarNovoCodigoMapaInclusao("CategoriaProdutoVO", rateioIntegracaoTO.getCodigoCategoria());
           rateioIntegracaoTO.setCodigoCategoria(codigoNovo);
           codigoNovo = consultarNovoCodigoMapaInclusao("ModalidadeVO", rateioIntegracaoTO.getCodigoModalidade());
           rateioIntegracaoTO.setCodigoModalidade(codigoNovo);
           codigoNovo = consultarNovoCodigoMapaInclusao("ProdutoVO", rateioIntegracaoTO.getCodigoProduto());
           rateioIntegracaoTO.setCodigoProduto(codigoNovo);
           codigoNovo = consultarNovoCodigoMapaInclusao("CentroCustoTO", rateioIntegracaoTO.getCodigoCentroCustos());
           rateioIntegracaoTO.setCodigoCentroCustos(codigoNovo);
       }

        UteisJSON.alterarValorFKs(VO, tabelaZW.isAtualizarFKAposInclusao(), this.mapaInclusao);
        // alterar valor das FKS das Listas.
        UteisJSON.alterarValorFKsLista(VO, this.mapaInclusao);
    }

    private boolean atualizarFKAposIncluir(Object VO)throws Exception{
       return (VO.getClass().getSimpleName().toUpperCase() .equals("EMPRESAVO")) ||
               (VO.getClass().getSimpleName().toUpperCase() .equals("QUESTIONARIOVO")) ||
               (VO.getClass().getSimpleName().toUpperCase() .equals("CONFIGURACAOSISTEMACADASTROCLIENTEVO"));
    }

    private Integer consultarRegistroBD(TabelaZW tabelaZW, Object VO)throws Exception{
        if  ((VO instanceof ConfiguracaoSistemaVO) || (VO instanceof ConfiguracaoSistemaCRMVO)) {
            return 1;
        }
        /*if (!(VO instanceof EmpresaVO)){
            return null;
        }*/
        String descricao = consultarDescricaoVO(VO, tabelaZW);
        String nomeColunaPesquisar = tabelaZW.getTabelaZWEnum().getNomeCampoDescricaoVO();

        //Casos onde o nome da coluna no BD é diferente do atributo do VO.
        if ((tabelaZW.getTabelaZWEnum().getNomeTabela().equals("CENTROCUSTO")) || (tabelaZW.getTabelaZWEnum().getNomeTabela().equals("PLANOCONTA"))){
            nomeColunaPesquisar = "nome";
        }else if (tabelaZW.getTabelaZWEnum().getNomeTabela().toUpperCase().equals("CONFIGURACAOSISTEMACADASTROCLIENTE")){
            // esta tabela não tem a coluna codigo.
            return 99;
        }
        if ((nomeColunaPesquisar == null) || (nomeColunaPesquisar.trim().equals(""))){
            return null;
        }
        if ((descricao == null) || (descricao.trim().equals(""))){
            return null;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select * from ").append(tabelaZW.getTabelaZWEnum().getNomeTabela()).append(" where upper( ").append(nomeColunaPesquisar).append(")");
        sql.append(" = ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setString(1, descricao.toUpperCase());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return rs.getInt("codigo");
        }
        return null;
    }

    private void incluirMapaInclusao(Object VO, Integer codigoAntigo, Integer codigoNovo){
        String chave = VO.getClass().getSimpleName();
        Map<Integer, Integer> mapa = this.mapaInclusao.get(chave);
        if (mapa == null){
            mapa = new HashMap<Integer, Integer>();
        }
        mapa.put(codigoAntigo, codigoNovo);
        this.mapaInclusao.put(chave, mapa);
    }

    private Integer consultarNovoCodigoMapaInclusao(String chave, Integer codigoAntigo){
        Map<Integer, Integer> mapa = this.mapaInclusao.get(chave);
        if (mapa == null){
            return null;
        }
        return mapa.get(codigoAntigo);
    }

    public String definirBancoAtualComoBancoPadraoParaImportacao(String nomeUsuario)throws Exception{
        JSONArray jsonArrayBDZillyon = new JSONArray();
        for (TabelaZWEnum tabelaSistemaEnum: TabelaZWEnum.values()){
            try{
                JSONObject jsonObjectTabela = new JSONObject();
                jsonObjectTabela.put("nomeTabela", tabelaSistemaEnum.getNomeTabela());
                JSONArray jsonArrayDados = new JSONArray();
                Object dao = UtilReflection.invoke(getFacade(), tabelaSistemaEnum.getNomeMetodoDaoFacadeFactory());
                List lista = (List) UtilReflection.invoke(dao,tabelaSistemaEnum.getNomeMetodoPesquisarTodos(),tabelaSistemaEnum.getParameterTypesConsultarTodos(), tabelaSistemaEnum.getParameterValuesConsultarTodos());
                if ((lista != null) && (lista.size() > 0)){
                    for (Object obj: lista){
                        jsonArrayDados.put(UteisJSON.objectToJSON(obj, false));
                    }
                    jsonObjectTabela.put("dadosJson", jsonArrayDados);
                    jsonArrayBDZillyon.put(jsonObjectTabela);
                }
            }catch (Exception ex){
                String erro = "PARSE DA TABELA QUE OCORREU ERRO: " + tabelaSistemaEnum.getNomeTabela() + "Erro:" + ex.getMessage() + ex.getCause();
                System.out.println(erro);
                throw new ConsistirException(erro);
            }
        }
        Map<String, String> params = new HashMap<String, String>();
        params.put("nomeUsuario", nomeUsuario.toString());
        params.put("tipoTabela", "0");
        params.put("jsonArrayTabelasZW", jsonArrayBDZillyon.toString());
        return UtilWS.executeRequestOAMDRestFul("tabelaZwJSONControle/gravarTabelaZwJSON", params);
    }


    public void povoarBancoInicialViaOAMD() throws Exception{
        Logger.getLogger(this.getClass().getSimpleName()).log(Level.INFO, "#### INICIOU POVOARBANCOINICIALVIAOAMD ####" );
        Conexao.storeOnSession(con);
        Map<String, String> params = new HashMap<String, String>();
        params.put("nomeTabela", "");
        params.put("tipoTabela", "0");
        String json = UtilWS.executeRequestOAMDRestFul("tabelaZwJSONControle/consultarTabelaZwJSON", params);
        JSONObject jsonObjectBDZillyon = new JSONObject(json);
        JSONArray jsonBDZillyon = new JSONArray(jsonObjectBDZillyon.getJSONArray("return").toString());
        con.setAutoCommit(false);
        try{
            gravarNumeroMatricula(0);
            gravarRegistroRobo(Uteis.getDataJDBCTimestamp(Calendario.hoje()),
                    Uteis.getDataJDBCTimestamp(Calendario.hoje()), Uteis.getDataJDBCTimestamp(Calendario.hoje()),
                    "INÍCIO DO SISTEMA");

            List<TabelaZW> listaTabelasZW = retornarListaTabelasZW(jsonBDZillyon);
            for (TabelaZW tabelaZW: listaTabelasZW){
                povoarTabelasZW(tabelaZW, false);
            }
            // Atualizar as FKs das tabelas
            for (TabelaZW tabelaZW: listaTabelasZW){
                // No caso da tabela de empresa por exemplo, ao incluir o registro ainda não foi incluido as FKS, desta forma, incluir a tabela no primeiro momento e depois atualiza as FKs.
                if (tabelaZW.isAtualizarFKAposInclusao()){
                    povoarTabelasZW(tabelaZW, true);
                }
            }
            zerarConfiguracoesImpactantes();
            con.commit();
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.INFO, "#### FINALIZOU POVOARBANCOINICIALVIAOAMD COM SUCESSO ####" );
        }catch (Exception e){
            con.rollback();
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.INFO, "#### ERRO AO RODAR POVOARBANCOINICIALVIAOAMD. ERRO:" + e.getMessage());
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    private void zerarConfiguracoesImpactantes()throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("update configuracaosistemacrm \n");
        sql.append("set login = null, senha=null,remetentePadrao=null, emailPadrao=null,mailServer=null, portaServer=null, \n");
        sql.append("conexaoSegura=false, iniciarTLS=false, enviarEmailIndividualmente=false");
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    public Map<String, Map<Integer, Integer>> getMapaInclusao() {
        return mapaInclusao;
    }

    public void setMapaInclusao(Map<String, Map<Integer, Integer>> mapaInclusao) {
        this.mapaInclusao = mapaInclusao;
    }

    public class TabelaZW {
        private TabelaZWEnum tabelaZWEnum;
        private JSONArray jsonArray;
        private boolean atualizarFKAposInclusao;

        public TabelaZWEnum getTabelaZWEnum() {
            return tabelaZWEnum;
        }

        public void setTabelaZWEnum(TabelaZWEnum tabelaZWEnum) {
            this.tabelaZWEnum = tabelaZWEnum;
        }

        public JSONArray getJsonArray() {
            return jsonArray;
        }

        public void setJsonArray(JSONArray jsonArray) {
            this.jsonArray = jsonArray;
        }

        public boolean isAtualizarFKAposInclusao() {
            return atualizarFKAposInclusao;
        }

        public void setAtualizarFKAposInclusao(boolean atualizarFKAposInclusao) {
            this.atualizarFKAposInclusao = atualizarFKAposInclusao;
        }
    }
}
