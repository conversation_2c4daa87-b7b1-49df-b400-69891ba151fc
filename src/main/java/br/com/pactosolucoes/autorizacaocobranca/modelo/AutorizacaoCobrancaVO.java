/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.modelo;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;

import java.sql.Connection;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AutorizacaoCobrancaVO extends SuperVO {

    @NaoControlarLogAlteracao
    public static final String[] TiposProdutoPlano = new String[]{"MA", "RN", "RE", "PM"};
    @NaoControlarLogAlteracao
    public static final String[] TiposProdutoContrato = new String[]{"PM", "MA", "RN", "RE", "SE", "TD", "TA", "PE", "TP", "TN", "MM"};
    private TipoAutorizacaoCobrancaEnum tipoAutorizacao;
    private String validadeCartao;
    private int mesValidade;
    private int anoValidade;
    @ChaveEstrangeira
    private BancoVO banco = new BancoVO();
    private Integer agencia = 0;
    private String agenciaDV = "";
    private Long contaCorrente = 0L;
    private String contaCorrenteDV = "";
    private TipoObjetosCobrarEnum tipoACobrar;
    private String listaObjetosACobrar;
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenio = new ConvenioCobrancaVO();
    private OperadorasExternasAprovaFacilEnum operadoraCartao;
    private String cartaoMascarado;
    @NaoControlarLogAlteracao
    private String numeroCartao;
    @NaoControlarLogAlteracao
    private boolean edit = false;
    @NaoControlarLogAlteracao
    private boolean desabilitarValidacaoValidade = false;
    private String codigoOperacao = "";
    private String cpfTitular = "";
    //Usado em caso de DCO com layout FEBRABAN
    private String identificacaoClienteBanco = "";
    private String nomeTitularCartao;
    private AdquirenteMaxiPagoEnum adquirenteMaxiPago = AdquirenteMaxiPagoEnum.NENHUM;
    private String tokenExterno;
    private boolean ativa = true;
    private boolean renovadoAutomatico = false;
    private String codigoExterno;
    private String assinaturaDigitalBiometria;
    private boolean autorizarClienteDebito = false;
    private String tokenAragorn;
    @NaoControlarLogAlteracao
    private NazgDTO nazgDTO;
    @NaoControlarLogAlteracao
    private boolean alterouNumeroCartao = false;
    @NaoControlarLogAlteracao
    private String cartaoMascaradoAnterior;
    @NaoControlarLogAlteracao
    private boolean validarBinCartao = true;
    private String idCardMundiPagg;
    private String idCardPagarMe;
    @NaoControlarLogAlteracao
    private boolean usarIdVindiPessoa = false;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;
    private boolean clienteTitularCartao = false;
    @NaoControlarLogAlteracao
    private boolean cartaoVerificado = false;
    @NaoControlarLogAlteracao
    private boolean validarClienteTitularCartao = true;
    @NaoControlarLogAlteracao
    private boolean gravarCodigoExterno = false;
    private int vencimentoFatura = 0;
    @NaoControlarLogAlteracao
    private String cvv;
    private OrigemCobrancaEnum origemCobrancaEnum;
    @NaoControlarLogAlteracao
    private boolean validarQtdCartoes = true;
    private String idPinBank;
    private Integer ordem;
    @NaoControlarLogAlteracao
    private String observacao;
    private String tokenCielo;
    private String tokenPagoLivre;
    @NaoControlarLogAlteracao
    private boolean processoImportacao = false;
    @NaoControlarLogAlteracao
    private String importacaoCliente;
    @NaoControlarLogAlteracao
    private String importacaoCPF;
    @NaoControlarLogAlteracao
    private String importacaoTitular;
    @NaoControlarLogAlteracao
    private boolean importacaoTokenCartao = false;
    @NaoControlarLogAlteracao
    private boolean realizandoImportacao = false;

    public AutorizacaoCobrancaVO(boolean iniciarLog) {
        if (iniciarLog) {
            registrarObjetoVOAntesDaAlteracao();
        }
    }

    public AutorizacaoCobrancaVO() {
    }

    public static void validarDados(AutorizacaoCobrancaVO obj) throws Exception {
        if (obj.isProcessoImportacao()) {
            return;
        }

        AutorizacaoCobrancaVO.limparDadosDeOutroTipo(obj);
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.NENHUM)) {
            throw new ConsistirException("O Tipo de Autorização deve ser informado!");
        }

        if (UteisValidacao.emptyNumber(obj.getConvenio().getCodigo())) {
            throw new ConsistirException("O Convênio de Cobrança deve ser informado!");
        }

        if ((obj.getTipoACobrar() == null || obj.getTipoACobrar().equals(TipoObjetosCobrarEnum.NENHUM)) && obj.getTipoAutorizacao().getId() != 3) {
            throw new ConsistirException("Selecione algum tipo de parcela a cobrar!");
        } else if (obj.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS) && UteisValidacao.emptyString(obj.getListaObjetosACobrar())) {
            throw new ConsistirException("Informe os tipos de produtos específicos a cobrar no campo 'Tipos de produtos' !");
        }

        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) && !obj.usarIdVindiPessoa) {

            if(obj.getConvenio() != null
                    && obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)
                    && obj.getConvenio().getAdquirenteMaxiPago().equals(AdquirenteMaxiPagoEnum.NENHUM)){
                throw new ConsistirException("Informe a adquirente que será utilizado na MaxiPago!");
            }

//            if (obj.getOperadoraCartao() == null) {
//                throw new ConsistirException("Informe uma Operadora para o Cartão de Crédito (Autorização Cobrança)");
//            }

            boolean validarCVVObrigatorio = obj.getConvenio() != null && obj.getConvenio().getTipo() != null
                    && !obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)
                    && !obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)
                    && !obj.isRealizandoImportacao();
            if (validarCVVObrigatorio && UteisValidacao.emptyString(obj.getCvv())) {
                throw new ConsistirException("Informe o Código de Segurança do cartão (CVV)!");
            }

            if (obj.getConvenio() != null && UteisValidacao.emptyString(obj.getNomeTitularCartao())) {
                throw new ConsistirException("Informe o NOME DO TITULAR do cartão!");
            }

            if (!obj.isImportacaoTokenCartao() && UteisValidacao.emptyString(obj.getNumeroCartao())) {
                throw new ConsistirException("Informe o NÚMERO DO CARTÃO de Crédito!");
            }

            if (obj.getOperadoraCartao() == null) {
                throw new ConsistirException("Informe um número válido para o Cartão de Crédito!");
            }

            try {
                obj.getMesValidade();
                obj.getAnoValidade();
            } catch (Exception e) {
                throw new ConsistirException("Data de validade do cartão inválida. Experimente informar a validade do Cartão no formato mm/aa!");
            }
            //
            if (UteisValidacao.emptyNumber(obj.getMesValidade()) || UteisValidacao.emptyNumber(obj.getAnoValidade())) {
                throw new ConsistirException("A DATA DE VENCIMENTO do cartão deve ser informada!");
            }

            if (obj.getMesValidade() == 0 || obj.getMesValidade() > 12) {
                throw new ConsistirException("O mês informado na VALIDADE do cartão é inválido. Valor deve ser entre 01 e 12!");
            }
            Calendar cal = Calendario.getInstance();
            if (obj.getAnoValidade() > (cal.get(Calendar.YEAR) + 30)) {
                throw new ConsistirException("O ano informado na VALIDADE do cartão é inválido. Valor deve ser no máximo " + (cal.get(Calendar.YEAR) + 30) + "!");
            }

            if (obj.isValidarBinCartao()) {
                if (obj.getOperadoraCartao().equals(OperadorasExternasAprovaFacilEnum.ELO)) { // cartoes elo devem ser validados de forma diferente.
                    boolean valido = ValidaBandeira.numeroCartaoValido(obj.getNumeroCartao().replace(" ", ""));
                    if (!valido) {
                        throw new ConsistirException("Número de Cartão inválido ELO!");
                    }
                    ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(obj.getNumeroCartao().replace(" ", ""));
                    OperadorasExternasAprovaFacilEnum band = Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, String.valueOf(bandeiraCard).toUpperCase());
                    if (!band.equals(OperadorasExternasAprovaFacilEnum.ELO)) {
                        throw new ConsistirException("Número de Cartão inválido ELO!");
                    }
                    if (obj.getNumeroCartao().replace(" ", "").length() < 14) {
                        throw new ConsistirException("Número de Cartão inválido ELO!");
                    }
                } else if (obj.getOperadoraCartao().equals(OperadorasExternasAprovaFacilEnum.BANESCARD)) {
                    if (obj.getNumeroCartao() == null || obj.getNumeroCartao().replace(" ", "").length() == 0) {
                        throw new ConsistirException("Número de Cartão inválido!");
                    }
                } else {
                    UteisValidacao.validarNumeroCartaoCredito(obj.getNumeroCartao().replaceAll(" ", ""));
                }
            }

            if (!obj.desabilitarValidacaoValidade) {
                UteisValidacao.validarVencimentoCartao(getValidadeMMYYYY(true, obj.getMesValidade(), obj.getAnoValidade()));
            }

           if (obj.isValidarBinCartao() &&
                   (obj.getOperadoraCartao().getBinStart()[0] != 0)
                    && !APF.isNumCardIntoBINS(obj.getNumeroCartao().replace(" ", ""), obj.getOperadoraCartao().getBinStart())) {
                throw new ConsistirException("A Bandeira informada não corresponde com o Número de Cartão digitado!");
            }

        } else if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA) && !obj.getConvenio().isBoleto()) {
            if (UteisValidacao.emptyNumber(obj.getAgencia())) {
                throw new ConsistirException("A Agência informada é inválida!");
            }
            if (UteisValidacao.emptyNumber(obj.getContaCorrente()) && !obj.getConvenio().isLayoutFebrabanDCO()) {
                throw new ConsistirException("A Conta Corrente informada inválida!");
            }
            if (UteisValidacao.emptyNumber(obj.getBanco().getCodigo())) {
                throw new ConsistirException("O Banco informado é inválido!");
            }
            if (obj.getContaCorrenteDV().trim().equals("")) {
                throw new ConsistirException("Informe o dígito verificador da Conta Corrente, cliente: " +obterNomePessoaAbreviadoAutorizacao(obj));
            }
        }
    }

    static public String getValidadeMMYYYY(boolean comBarra, int mes, int ano) {
        String barra = comBarra ? "/" : "";
        String v = Formatador.formatarValorNumerico(Double.valueOf(mes), "00")
                + barra
                + Formatador.formatarValorNumerico(Double.valueOf(ano), "0000");

        return v;
    }

    public static String obterNomePessoaAbreviadoAutorizacao(AutorizacaoCobrancaVO obj){
        String nomePessoaAbreviado = "";
        try{
            nomePessoaAbreviado = ((AutorizacaoCobrancaClienteVO) obj).getCliente().getPessoa().getNomeAbreviado();

        }catch(Exception ignored){
            nomePessoaAbreviado = ((AutorizacaoCobrancaColaboradorVO) obj).getColaborador().getPessoa().getNomeAbreviado();
        }

        return nomePessoaAbreviado;
    }

    public static void limparDadosDeOutroTipo(AutorizacaoCobrancaVO obj) {
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            obj.setAgencia(0);
            obj.setAgenciaDV("0");
            obj.setBanco(new BancoVO());
            obj.setContaCorrente(0L);
            obj.setContaCorrenteDV("0");
        } else if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
            obj.setCartaoMascarado(null);
            obj.setNumeroCartao(null);
            obj.setValidadeCartao(null);
        }
        if (obj.getTipoACobrar() != null && !obj.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
            obj.setListaObjetosACobrar(null);
        }

    }

    public Integer getAgencia() {
        return agencia;
    }

    public void setAgencia(Integer agencia) {
        this.agencia = agencia;
    }

    public String getAgenciaDV() {
        return agenciaDV;
    }

    public void setAgenciaDV(String agenciaDV) {
        this.agenciaDV = agenciaDV;
    }

    public String getAgenciaApresentar() {
        if (getAgenciaDV().length() > 0) {
            return getAgencia().toString() + "-" + getAgenciaDV();
        } else {
            return getAgencia().toString();
        }
    }

    public String getContaCorrenteApresentarSemBanco() {
        if (getContaCorrenteDV().length() > 0) {
            return getContaCorrente().toString() + "-" + getContaCorrenteDV();
        } else {
            return getContaCorrente().toString();
        }
    }

    public BancoVO getBanco() {
        return banco;
    }

    public void setBanco(BancoVO banco) {
        this.banco = banco;
    }

    public String getContaCorrenteApresentar(){
        String contaBanco = "";
        if(getContaCorrenteDV().length() > 0){
            contaBanco = getContaCorrente().toString() + "-" + getContaCorrenteDV();
        }else{
            contaBanco = getContaCorrente().toString();
        }

        contaBanco += "-"+getBanco().getNome();

        return contaBanco;
    }
    public Long getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(Long contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public String getContaCorrenteDV() {
        return contaCorrenteDV;
    }

    public void setContaCorrenteDV(String contaCorrenteDV) {
        this.contaCorrenteDV = contaCorrenteDV;
    }

    public ConvenioCobrancaVO getConvenio() {
        if (convenio == null) {
            convenio = new ConvenioCobrancaVO();
        }
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public boolean isTipoCartao(){
        return getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
    }

    public boolean isTipoContaCorrente(){
        return getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
    }

    public boolean isTipoBoleto(){
        return getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacao() {
        if (tipoAutorizacao == null) {
            tipoAutorizacao = TipoAutorizacaoCobrancaEnum.NENHUM;
        }
        return tipoAutorizacao;
    }

    public void setTipoAutorizacao(TipoAutorizacaoCobrancaEnum tipoAutorizacao) {
        this.tipoAutorizacao = tipoAutorizacao;
    }

    public String getListaObjetosACobrar() {
        return listaObjetosACobrar;
    }

    public String getListaObjetosACobrar_Title() {
        try {
            StringBuilder msg = new StringBuilder();

            if (UteisValidacao.emptyString(getListaObjetosACobrar())) {
                return "<b>Clique para adicionar produtos</b>";
            }

            if (!UteisValidacao.emptyString(getListaObjetosACobrar())) {
                String[] tipos = getListaObjetosACobrar().split(",");
                for (String tipo : tipos) {
                    if (!UteisValidacao.emptyString(tipo)) {
                        TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(tipo);
                        if (tipoProduto != null) {
                            if (msg.length() ==0) {
                                msg.append("Irá cobrar parcelas em aberto de produtos do tipo:<br/>");
                            }
                            msg.append("<br/><b>").append(tipoProduto.getDescricao()).append("</b>");
                        }
                    }
                }
            }

            if (msg.length() > 0) {
                msg.append("<br/><br/>");
            }
            msg.append("<b>Clique para alterar os produtos</b>");

            return msg.toString();
        } catch (Exception ex) {
            return "Clique para alterar os produtos";
        }
    }

    public void setListaObjetosACobrar(String listaObjetosACobrar) {
        this.listaObjetosACobrar = listaObjetosACobrar;
    }

    public TipoObjetosCobrarEnum getTipoACobrar() {
        if (tipoACobrar == null) {
            tipoACobrar = TipoObjetosCobrarEnum.NENHUM;
        }
        return tipoACobrar;
    }

    public void setTipoACobrar(TipoObjetosCobrarEnum tipoACobrar) {
        this.tipoACobrar = tipoACobrar;
    }

    public String getTipoACobrar_Title() {
        try {

            if (getTipoACobrar().equals(TipoObjetosCobrarEnum.TUDO)) {
                return "Irá cobrar todas as parcelas em aberto, independente do tipo do produto.";
            }

            if (getTipoACobrar().equals(TipoObjetosCobrarEnum.APENAS_PLANOS)) {
                StringBuilder msg = new StringBuilder();
                for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoPlano) {
                    if (!UteisValidacao.emptyString(tipo)) {
                        TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(tipo);
                        if (tipoProduto != null) {
                            if (msg.length() == 0) {
                                msg.append("Irá cobrar as parcelas em aberto de produtos do tipo:<br/>");
                            }
                            msg.append("<br/><b>").append(tipoProduto.getDescricao()).append("</b>");
                        }
                    }
                }
                return msg.toString();
            }

            if (getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                StringBuilder msg = new StringBuilder();
                msg.append("Irá cobrar as parcelas em aberto de produtos que foram especificados<br/>");
                msg.append("no campo <b>Tipos de produtos:</b><br/>");
                if (!UteisValidacao.emptyString(getListaObjetosACobrar())) {
                    String[] tipos = getListaObjetosACobrar().split(",");
                    for (String tipo : tipos) {
                        if (!UteisValidacao.emptyString(tipo)) {
                            TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(tipo);
                            if (tipoProduto != null) {
                                msg.append("<br/><b>").append(tipoProduto.getDescricao()).append("</b>");
                            }
                        }
                    }
                } else {
                    msg.append("<br/><b>Nenhum produto especificado</b>");
                }
                return msg.toString();
            }

            if (getTipoACobrar().equals(TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO)) {
                StringBuilder msg = new StringBuilder();
                for (String tipo : AutorizacaoCobrancaClienteVO.TiposProdutoContrato) {
                    if (!UteisValidacao.emptyString(tipo)) {
                        TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(tipo);
                        if (tipoProduto != null) {
                            if (msg.length() == 0) {
                                msg.append("Irá cobrar as parcelas em aberto de contratos que sejam <b>renováveis automaticamente</b><br/>e que seja de produtos do tipo:<br/>");
                            }
                            msg.append("<br/><b>").append(tipoProduto.getDescricao()).append("</b>");
                        }
                    }
                }
                return msg.toString();
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getValidadeCartao() {
        return validadeCartao;
    }

    public void setValidadeCartao(String validadeCartao) {
        this.validadeCartao = validadeCartao;
        inicializarAnoMesValidade();
    }

    public OperadorasExternasAprovaFacilEnum getOperadoraCartao() {
        return operadoraCartao;
    }
    public String getImagemBandeira(){
        if(UteisValidacao.emptyString(getNumeroCartao())){
            return "";
        }
        return getOperadoraCartao() != null ? getOperadoraCartao().getDescricaoMinusculo() : " ";
    }
    public void setOperadoraCartao(OperadorasExternasAprovaFacilEnum operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public String getCartaoMascarado() {
        return cartaoMascarado;
    }

    public void setCartaoMascarado(String cartaoMascarado) {
        this.cartaoMascarado = cartaoMascarado;
    }

    public boolean isEdit() {
        return edit;
    }

    public void setEdit(boolean edit) {
        this.edit = edit;
    }

    public boolean isDesabilitarValidacaoValidade() {
        return desabilitarValidacaoValidade;
    }

    public void setDesabilitarValidacaoValidade(boolean ignorarCartaoVencido) {
        this.desabilitarValidacaoValidade = ignorarCartaoVencido;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AutorizacaoCobrancaVO other = (AutorizacaoCobrancaVO) obj;
        if (this.tipoAutorizacao != other.tipoAutorizacao) {
            return false;
        }
        if ((this.numeroCartao == null) ? (other.numeroCartao != null) : !this.numeroCartao.equals(other.numeroCartao)) {
            return false;
        }
        if ((this.validadeCartao == null) ? (other.validadeCartao != null) : !this.validadeCartao.equals(other.validadeCartao)) {
            return false;
        }
        if (this.banco != other.banco && (this.banco == null || !(this.banco.getCodigo().intValue() == other.banco.getCodigo().intValue()))) {
            return false;
        }
        if (this.agencia != other.agencia && (this.agencia == null || !this.agencia.equals(other.agencia))) {
            return false;
        }
        if (this.agenciaDV != other.agenciaDV && (this.agenciaDV == null || !this.agenciaDV.equals(other.agenciaDV))) {
            return false;
        }
        if (this.contaCorrente != other.contaCorrente && (this.contaCorrente == null || !this.contaCorrente.equals(other.contaCorrente))) {
            return false;
        }
        if (this.contaCorrenteDV != other.contaCorrenteDV && (this.contaCorrenteDV == null || !this.contaCorrenteDV.equals(other.contaCorrenteDV))) {
            return false;
        }
        if (this.tipoACobrar != other.tipoACobrar) {
            return false;
        }
        if ((this.listaObjetosACobrar == null) ? (other.listaObjetosACobrar != null) : !this.listaObjetosACobrar.equals(other.listaObjetosACobrar)) {
            return false;
        }
        if (this.convenio != other.convenio && (this.convenio == null || !(this.convenio.getCodigo().intValue() == other.convenio.getCodigo().intValue()))) {
            return false;
        }
        if (this.operadoraCartao != other.operadoraCartao) {
            return false;
        }
        return true;
    }

    public boolean isCompativelComTiposProduto(final String[] tiposTestar) {

        switch (this.tipoACobrar) {
            case APENAS_PLANOS: {
                for (int i = 0; i < tiposTestar.length; i++) {
                    String tipoTeste = tiposTestar[i];
                    for (int j = 0; j < TiposProdutoPlano.length; j++) {
                        if (tipoTeste.equals(TiposProdutoPlano[j])) {
                            return true;
                        }
                    }
                }
                break;
            }
            case CONTRATOS_RENOVAVEIS_AUTO: {
                return true;
            }
            case TIPOS_PRODUTOS: {
                if (UteisValidacao.emptyString(this.getListaObjetosACobrar())){
                    return false;
                }
                String[] tipos = this.getListaObjetosACobrar().split(",");
                for (int i = 0; i < tipos.length; i++) {
                    String tipo = tipos[i].trim();
                    for (String tipoTestar : tiposTestar) {
                        if (tipo.equals(tipoTestar)) {
                            return true;
                        }
                    }
                }
                break;
            }
            case TUDO: {
                return true;
            }
        }
        return false;
    }

    public boolean isParcelaCompativel(MovParcelaVO parcela, boolean gerarRemessaContratoCancelado, Connection con) throws Exception {
        if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
            return true;
        }
        switch (this.tipoACobrar) {
            case APENAS_PLANOS: {
                List<String> lista = parcela.getTipoProdutos();
                for (int i = 0; i < TiposProdutoPlano.length; i++) {
                    String tipo = TiposProdutoPlano[i];
                    for (String tipoNaParcela : lista) {
                        if (tipo.equals(tipoNaParcela) || (tipoNaParcela.equals(TipoProduto.QUITACAO_DE_DINHEIRO.getCodigo()) && gerarRemessaContratoCancelado)) {
                            return true;
                        }
                    }
                }

                break;
            }
            case CONTRATOS_RENOVAVEIS_AUTO: {

                List<String> lista = parcela.getTipoProdutos();
                boolean tipoProdutoCompativel = false;
                FOR:
                for (int i = 0; i < TiposProdutoContrato.length; i++) {
                    String tipo = TiposProdutoContrato[i];
                    for (String tipoNaParcela : lista) {
                        if (tipo.equals(tipoNaParcela) || (tipoNaParcela.equals(TipoProduto.QUITACAO_DE_DINHEIRO.getCodigo()) && gerarRemessaContratoCancelado)) {
                            tipoProdutoCompativel = true;
                            break FOR;
                        }
                    }
                }

                return tipoProdutoCompativel && (SuperFacadeJDBC.existe("select codigo from contratorecorrencia  "
                                + "where renovavelautomaticamente = 't' and contrato = "
                                + parcela.getContrato().getCodigo(),
                        con) || SuperFacadeJDBC.existe("select codigo from contrato where renovavelautomaticamente = 't' and   "
                                + " codigo = "
                                + parcela.getContrato().getCodigo(),
                        con));
            }
            case TIPOS_PRODUTOS: {
                String[] tipos = this.getListaObjetosACobrar().split(",");
                List<String> lista = parcela.getTipoProdutos();
                for (int i = 0; i < tipos.length; i++) {
                    String tipo = tipos[i].trim();
                    for (String tipoNaParcela : lista) {
                        if (tipo.equals(tipoNaParcela) || (tipoNaParcela.equals(TipoProduto.QUITACAO_DE_DINHEIRO.getCodigo()) && gerarRemessaContratoCancelado)) {
                            return true;
                        }
                    }
                }

                break;
            }
            case TUDO: {
                return true;
            }
        }
        return false;
    }

    public String getDescricaoObjeto() {

        if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

            StringBuilder cartaoCredito = new StringBuilder();
            cartaoCredito.append("<div style=\"width:100%;font-size:12px;font-family:Arial;font-weight:bold;padding:5px;border-style:dotted;border-width:2px;\">");
            cartaoCredito.append("								<table>");
            cartaoCredito.append("									<tr>");
            cartaoCredito.append("										<td>Tipo Cobrança</td>");
            cartaoCredito.append("										<td>Número Cartão</td>");
            cartaoCredito.append("										<td>Validade Cartão</td>");
            cartaoCredito.append("										<td>Operadora</td>");
            cartaoCredito.append("										<td>CPF do Titular</td>");
            cartaoCredito.append("										<td>Nome do Titular</td>");
            cartaoCredito.append("										<td>Permissão Cobrança</td>");
            cartaoCredito.append("									</tr>");
            cartaoCredito.append("									<tr>");
            cartaoCredito.append("										<td>").append(this.getTipoAutorizacao().getDescricao()).append("</td>");
            cartaoCredito.append("										<td>").append(APF.getCartaoMascarado(this.getNumeroCartao())).append("</td>");
            cartaoCredito.append("										<td>").append(this.getValidadeCartao()).append("</td>");
            cartaoCredito.append("										<td>").append(this.getOperadoraCartao().getDescricao()).append("</td>");
            cartaoCredito.append("										<td>").append(this.getCpfTitular()).append("</td>");
            cartaoCredito.append("										<td>").append(this.getNomeTitularCartao()).append("</td>");
            cartaoCredito.append("										<td>").append(this.getTipoACobrar().getDescricao()).append("</td>");
            cartaoCredito.append("									</tr>");
            cartaoCredito.append("								</table>");
            if (!UteisValidacao.emptyString(this.getAssinaturaDigitalBiometria())) {
                cartaoCredito.append("								<table style=\"width:100%;font-size:12px;font-family:Arial;font-weight:bold;padding-top:5px;border-top-style:dotted;border-width:2px;\">");
                cartaoCredito.append("									<tr>");
                cartaoCredito.append("										<td>Assinatura Biometria - ").append(this.getTipoAutorizacao().getDescricao()).append("</td>");
                cartaoCredito.append("									</tr>");
                cartaoCredito.append("									<tr>");
                cartaoCredito.append("										<td>");
                cartaoCredito.append("											<div style='word-break: break-all;'>").append(this.getAssinaturaDigitalBiometria()).append("</div>");
                cartaoCredito.append("										</td>");
                cartaoCredito.append("									</tr>");
                cartaoCredito.append("								</table>");
            }
            cartaoCredito.append("							</div>");
            return cartaoCredito.toString();

        } else if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {

            StringBuilder debitoConta = new StringBuilder();
            debitoConta.append("<div style=\"width:100%;font-size:12px;font-family:Arial;font-weight:bold;padding:5px;border-style:dotted;border-width:2px;\">");
            debitoConta.append("								<table>");
            debitoConta.append("									<tr>");
            debitoConta.append("										<td>Tipo Cobrança</td>");
            debitoConta.append("										<td>Banco</td>");
            debitoConta.append("										<td>Agência</td>");
            debitoConta.append("										<td>Conta corrente</td>");
            debitoConta.append("										<td>CPF do Titular</td>");
            debitoConta.append("										<td>Nome do Titular</td>");
            debitoConta.append("										<td>Permissão Cobrança</td>");
            debitoConta.append("									</tr>");
            debitoConta.append("									<tr>");
            debitoConta.append("										<td>").append(this.getTipoAutorizacao().getDescricao()).append("</td>");
            debitoConta.append("										<td>").append(this.getBanco().getNome()).append("</td>");
            debitoConta.append("										<td>").append(this.getAgencia()).append('-').append(this.getAgenciaDV()).append("</td>");
            debitoConta.append("										<td>").append(this.getContaCorrente()).append("-").append(this.getContaCorrenteDV()).append("</td>");
            debitoConta.append("										<td>").append(this.getCpfTitular()).append("</td>");
            debitoConta.append("										<td>").append(this.getNomeTitularCartao()).append("</td>");
            debitoConta.append("										<td>").append(this.getTipoACobrar().getDescricao()).append("</td>");
            debitoConta.append("									</tr>");
            debitoConta.append("								</table>");
            if (!UteisValidacao.emptyString(this.getAssinaturaDigitalBiometria())) {
               debitoConta.append("								<table style=\"width:100%;font-size:12px;font-family:Arial;font-weight:bold;padding-top:5px;border-top-style:dotted;border-width:2px;\">");
               debitoConta.append("									<tr>");
               debitoConta.append("										<td>Assinatura Biometria - ").append(this.getTipoAutorizacao().getDescricao()).append("</td>");
               debitoConta.append("									</tr>");
               debitoConta.append("									<tr>");
               debitoConta.append("										<td>");
               debitoConta.append("											<div style='word-break: break-all;'>").append(this.getAssinaturaDigitalBiometria()).append("</div>");
               debitoConta.append("										</td>");
               debitoConta.append("									</tr>");
               debitoConta.append("								</table>");
            }
            debitoConta.append("							</div>");
            return debitoConta.toString();

        } else if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {

            StringBuilder boleto = new StringBuilder();
            boleto.append("<div style=\"width:100%;font-size:12px;font-family:Arial;font-weight:bold;padding:5px;border-style:dotted;border-width:2px;\">");
            boleto.append("								<table>");
            boleto.append("									<tr>");
            boleto.append("										<td>Tipo Cobrança</td>");
            boleto.append("										<td>Convênio</td>");
            boleto.append("										<td>CPF do Titular</td>");
            boleto.append("										<td>Nome do Titular</td>");
            boleto.append("										<td>Permissão Cobrança</td>");
            boleto.append("									</tr>");
            boleto.append("									<tr>");
            boleto.append("										<td>").append(this.getTipoAutorizacao().getDescricao()).append("</td>");
            boleto.append("										<td>").append(this.getConvenio().getDescricao()).append("</td>");
            boleto.append("										<td>").append(this.getCpfTitular()).append("</td>");
            boleto.append("										<td>").append(this.getNomeTitularCartao()).append("</td>");
            boleto.append("										<td>").append(this.getTipoACobrar().getDescricao()).append("</td>");
            boleto.append("									</tr>");
            boleto.append("								</table>");
            if (!UteisValidacao.emptyString(this.getAssinaturaDigitalBiometria())) {
                boleto.append("								<table style=\"width:100%;font-size:12px;font-family:Arial;font-weight:bold;padding-top:5px;border-top-style:dotted;border-width:2px;\">");
                boleto.append("									<tr>");
                boleto.append("										<td>Assinatura Biometria - ").append(this.getTipoAutorizacao().getDescricao()).append("</td>");
                boleto.append("									</tr>");
                boleto.append("									<tr>");
                boleto.append("										<td>");
                boleto.append("											<div style='word-break: break-all;'>").append(this.getAssinaturaDigitalBiometria()).append("</div>");
                boleto.append("										</td>");
                boleto.append("									</tr>");
                boleto.append("								</table>");
            }
            boleto.append("							</div>");
            return boleto.toString();
        }
        return "";
    }

    public void preencherRemessaItemSegundoAutorizacao(RemessaItemVO item, Connection con) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        ConfiguracaoSistema configuracaoSistemaDAO = null;
        Cliente clienteDAO = null;
        Empresa empresaDAO = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            clienteDAO = new Cliente(con);
            empresaDAO = new Empresa(con);

            if (item.getRemessa().getConvenioCobranca().getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                item.put(APF.CartaoMascarado, APF.getCartaoMascarado(this.getNumeroCartao()));
                item.put(APF.AnoValidade, this.getValidadeCartao().substring(3));
                item.put(APF.MesValidade, this.getValidadeCartao().substring(0, 2));
                item.put(APF.TokenAragorn, this.getTokenAragorn());
                this.preencherOperadoraCartao();
                item.put(APF.Bandeira, this.getOperadoraCartao().name());

                try {
                    Boolean parceladoLojista;
                    Integer numeroParcelasOperadora;
                    if (item.getRemessa().isNovoFormato()) {
                        parceladoLojista = item.getMovParcelas().get(0).getMovParcelaVO().isParceladoLojista();
                        numeroParcelasOperadora = item.getMovParcelas().get(0).getMovParcelaVO().getNumeroParcelasOperadora();
                    } else {
                        parceladoLojista = item.getMovParcela().isParceladoLojista();
                        numeroParcelasOperadora = item.getMovParcela().getNumeroParcelasOperadora();
                    }
                    item.put(APF.NrVezes, numeroParcelasOperadora == null ? "1" : numeroParcelasOperadora.toString());
                    item.put(APF.ParceladoLojista, parceladoLojista.toString());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else if (item.isLayoutFebrabanDCO() || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_HSBC)) {
                if (!UteisValidacao.emptyString(this.getIdentificacaoClienteBanco().trim())) {
                    item.put(DCCAttEnum.IdentificadorClienteBanco.name(), this.getIdentificacaoClienteBanco());
                }
                item.put(DCCAttEnum.IdentificadorClienteEmpresa.name(), StringUtilities.formatarCpfCnjp(item.getMovParcela().getPessoa().getCfp(), 11));
                item.put(DCCAttEnum.AgenciaDebito.name(), this.getAgencia().toString());
                item.put(DCCAttEnum.ContaCorrente.name(), this.getContaCorrente().toString());
                item.put(DCCAttEnum.ContaCorrenteDigito.name(), this.getContaCorrenteDV());
                item.put(DCCAttEnum.CpfOuCnpj.name(), this.getCpfTitular());
                item.put(DCCAttEnum.NomePagador.name(), this.getNomeTitularCartao());
                if (this.getApresentarCodigoOperacao()) {
                    item.put(DCCAttEnum.CodigoOperacao.name(), this.getCodigoOperacao());
                }
            } else if (item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)
                    || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) {
                item.put(DCCAttEnum.IdentificadorClienteEmpresa.name(), item.getMovParcela().getPessoa().getCodigo().toString());
                item.put(DCCAttEnum.AgenciaDebito.name(), this.getAgencia().toString());
                item.put(DCCAttEnum.AgenciaDebitoDigito.name(), this.getAgenciaDV());
                item.put(DCCAttEnum.ContaCorrenteDebito.name(), this.getContaCorrente().toString());
                item.put(DCCAttEnum.ContaCorrenteDebitoDigito.name(), this.getContaCorrenteDV());
                if (item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                    item.put(DCCAttEnum.CodigoOperacao.name(), this.getCodigoOperacao());
                    item.put(DCCAttEnum.ContaCorrenteDebito.name(), this.getContaCorrente().toString());
                }
                item.put(DCCAttEnum.CpfCliente.name(), this.getCpfTitular());
                item.put(DCCAttEnum.CpfOuCnpj.name(), this.getCpfTitular());
            } else if (item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)
                    || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)) {
                if (item.getRemessa().getConvenioCobranca().getUsarSequencialUnico()) {
                    item.setIdentificador(configuracaoSistemaDAO.incrementarSequencialItem());
//                item.setSequencialArquivo(getFacade().getConfiguracaoSistema().incrementarSequencialArquivo());
                } else {
                    item.setIdentificador(convenioCobrancaDAO.incrementarSequencialItem(item.getRemessa().getConvenioCobranca().getCodigo()));
                }
            }

            if (item.getRemessa().getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO)) {
                String cpfCNPJ = "";
                String nome = "";

                PessoaVO pessoaVO = item.getPessoa();
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(item.getRemessa().getEmpresa(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

                if (empresaVO.isUtilizarNomeResponsavelNoBoleto() &&
                        !pessoaVO.getCategoriaPessoa().equals(TipoPessoa.JURIDICA) &&
                        pessoaVO.getIdade() < 18) {

                    PessoaVO pessoaResponsavel = clienteDAO.obterPessoaResponsavelCliente(null, pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    if (pessoaResponsavel != null && !UteisValidacao.emptyNumber(pessoaResponsavel.getCodigo())) {
                        nome =  pessoaResponsavel.getNome();
                        cpfCNPJ = pessoaResponsavel.getCfp().replaceAll("[^0-9]", "");
                    } else if (!pessoaVO.getCpfMae().equals("")) {
                        nome = pessoaVO.getNomeMae();
                        cpfCNPJ = pessoaVO.getCpfMae().replaceAll("[^0-9]", "");
                    } else if (!pessoaVO.getCpfPai().equals("")) {
                        nome = pessoaVO.getNomePai();
                        cpfCNPJ = pessoaVO.getCpfPai().replaceAll("[^0-9]", "");
                    } else {
                        if (!UteisValidacao.emptyString(pessoaVO.getNome())) {
                            throw new Exception("Responsável pelo cliente \"" +pessoaVO.getNome()+ "\" não cadastrado.");
                        } else {
                            throw new Exception("Responsável pelo cliente não cadastrado.");
                        }
                    }

                    if (UteisValidacao.emptyString(nome) ||
                            UteisValidacao.emptyString(cpfCNPJ.replaceAll("[^0-9]", ""))) {
                        if (!UteisValidacao.emptyString(pessoaVO.getNome())) {
                            throw new Exception("Marcado configuração de \"Utilizar nome responsável no boleto\", nome ou CPF do responsável não está preenchido. Cliente: " + pessoaVO.getNome());
                        } else {
                            throw new Exception("Marcado configuração de \"Utilizar nome responsável no boleto\", nome ou CPF do responsável não está preenchido.");
                        }
                    }

                } else {
                    nome = pessoaVO.getNome();

                    if (pessoaVO.getCategoriaPessoa().equals(TipoPessoa.JURIDICA)) {
                        cpfCNPJ = pessoaVO.getCnpj().replaceAll("[^0-9]", "");
                    } else {
                        cpfCNPJ = pessoaVO.getCfp().replaceAll("[^0-9]", "");
                    }

                    if (UteisValidacao.emptyString(nome)) {
                        throw new Exception("Nome não informado");
                    }

                    if (UteisValidacao.emptyString(cpfCNPJ.replaceAll("[^0-9]", ""))) {
                        if (!UteisValidacao.emptyString(pessoaVO.getNome())) {
                            throw new Exception("CPF não cadastrado. Cliente: " + pessoaVO.getNome());
                        } else {
                            throw new Exception("CPF não cadastrado");
                        }
                    }
                }

                if (UteisValidacao.emptyString(nome)) {
                    throw new Exception("Nome não informado");
                }

                if (UteisValidacao.emptyString(Uteis.formatarCpfCnpj(cpfCNPJ, true))) {
                    if (!UteisValidacao.emptyString(pessoaVO.getNome())) {
                        throw new Exception("CPF/CNPJ não cadastrado. Cliente: " + pessoaVO.getNome());
                    } else {
                        throw new Exception("CPF/CNPJ não cadastrado");
                    }
                }

                item.put(DCCAttEnum.NomePessoa.name(), pessoaVO.getNome());
                item.put(DCCAttEnum.CpfCliente.name(), Uteis.formatarCpfCnpj(pessoaVO.getCfp(), true));
                item.put(DCCAttEnum.CnpjCliente.name(), Uteis.formatarCpfCnpj(pessoaVO.getCnpj(), true));

                item.put(DCCAttEnum.NomePagador.name(), nome);
                item.put(DCCAttEnum.CpfCnpjPagador.name(), Uteis.formatarCpfCnpj(cpfCNPJ, true));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            configuracaoSistemaDAO = null;
            clienteDAO = null;
            empresaDAO = null;
        }
    }

    public int getMesValidade() {
        if (!UteisValidacao.emptyString(this.getValidadeCartao())) {
            mesValidade = Integer.parseInt(this.getValidadeCartao().split("/")[0]);
        }
        return mesValidade;
    }

    public void setMesValidade(int mesValidade) {
        this.mesValidade = mesValidade;
    }

    public int getAnoValidade() {
        if (!UteisValidacao.emptyString(this.getValidadeCartao())) {
            if (this.validadeCartao.length() == 7) {
                anoValidade = Integer.valueOf(this.getValidadeCartao().substring(3, 7));
            }else if (this.validadeCartao.length() == 5) {
                anoValidade = Integer.valueOf("20" + this.getValidadeCartao().substring(3, 5));
            }else {
                return Integer.parseInt(this.getValidadeCartao().split("/")[1]);
            }
        }
        return anoValidade;
    }

    public void setAnoValidade(int anoValidade) {
        this.anoValidade = anoValidade;
    }

    public String getCodigoOperacao() {
        if (codigoOperacao == null) {
            codigoOperacao = "";
        }
        return codigoOperacao;
    }

    public void setCodigoOperacao(String codigoOperacao) {
        this.codigoOperacao = codigoOperacao;
    }

    public boolean getApresentarCodigoOperacao() {
        try {
            return getBanco().getCodigoBanco() == 104;
        } catch (Exception e) {
            return false;
        }

    }

    private void inicializarAnoMesValidade() {
        try {
            this.getAnoValidade();
            this.getMesValidade();
        } catch (Exception e) {

        }
    }

    public String getCpfTitularComMascara() {
        return Uteis.formatarCpfCnpj(getCpfTitular(), false);
    }

    public String getCpfTitular() {
        if (!UteisValidacao.emptyString(cpfTitular)) {
            cpfTitular = Uteis.formatarCpfCnpj(cpfTitular, true);
        }
        return cpfTitular;
    }

    public void setCpfTitular(String cpfTitular) {
        this.cpfTitular = cpfTitular;
    }

    public String getCartaoMascarado_Apresentar() {
        if(UteisValidacao.emptyString(numeroCartao)){
            return "";
        }
        return APF.getCartaoMascarado(numeroCartao);
    }

    public String getIdentificacaoClienteBanco() {
        if (identificacaoClienteBanco == null) {
            identificacaoClienteBanco = "";
        }
        return identificacaoClienteBanco;
    }

    public void setIdentificacaoClienteBanco(String identificacaoClienteBanco) {
        this.identificacaoClienteBanco = identificacaoClienteBanco;
    }

    public String getNomeTitularCartao() {
        if (nomeTitularCartao == null) {
            nomeTitularCartao = "";
        }
        return nomeTitularCartao;
    }

    public void setNomeTitularCartao(String nomeTitularCartao) {
        this.nomeTitularCartao = nomeTitularCartao;
    }

    public AdquirenteMaxiPagoEnum getAdquirenteMaxiPago() {
        if (adquirenteMaxiPago == null){
            adquirenteMaxiPago =  AdquirenteMaxiPagoEnum.NENHUM;
        }
        return adquirenteMaxiPago;
    }

    public void setAdquirenteMaxiPago(AdquirenteMaxiPagoEnum adquirenteMaxiPago) {
        this.adquirenteMaxiPago = adquirenteMaxiPago;
    }

    public String getTokenExterno() {
        if (tokenExterno == null) {
            tokenExterno = "";
        }
        return tokenExterno;
    }

    public void setTokenExterno(String tokenExterno) {
        this.tokenExterno = tokenExterno;
    }
    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public boolean isBandeiraValidaParaTipoConvenioCobranca(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        List<OperadorasExternasAprovaFacilEnum> operadoras = OperadorasExternasAprovaFacilEnum.operadorasConvenio(tipoConvenioCobrancaEnum);
        for (OperadorasExternasAprovaFacilEnum ope: operadoras) {
            if (this.getOperadoraCartao() != null &&
                    ope.getId().equals(this.getOperadoraCartao().getId())) {
                return true;
            }
        }
        return false;
    }

    public boolean isRenovadoAutomatico() {
        return renovadoAutomatico;
    }

    public void setRenovadoAutomatico(boolean renovadoAutomatico) {
        this.renovadoAutomatico = renovadoAutomatico;
    }

    public String getCodigoExterno() {
        if (codigoExterno == null) {
            codigoExterno = "";
        }
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public String getAssinaturaDigitalBiometria() {
        if (assinaturaDigitalBiometria == null) {
            assinaturaDigitalBiometria = "";
        }
        return assinaturaDigitalBiometria;
    }

    public void setAssinaturaDigitalBiometria(String assinaturaDigitalBiometria) {
        this.assinaturaDigitalBiometria = assinaturaDigitalBiometria;
    }

    public boolean isAutorizarClienteDebito() {
        return autorizarClienteDebito;
    }

    public void setAutorizarClienteDebito(boolean autorizarClienteDebito) {
        this.autorizarClienteDebito = autorizarClienteDebito;
    }

    public boolean isConvenioEmpresaCliente() {
        try {
            AutorizacaoCobrancaClienteVO autoClienteVO = ((AutorizacaoCobrancaClienteVO) this);
            if (autoClienteVO != null && autoClienteVO.getConvenio() != null && autoClienteVO.getCliente() != null) {
                for (ConvenioCobrancaEmpresaVO convVO : autoClienteVO.getConvenio().getConfiguracoesEmpresa()) {
                    if (convVO.getEmpresa().getCodigo().equals(autoClienteVO.getCliente().getEmpresa().getCodigo())) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception ex) {
            return true;
        }
    }

    public String getTokenAragorn() {
        if (tokenAragorn == null) {
            tokenAragorn = "";
        }
        return tokenAragorn;
    }

    public void setTokenAragorn(String tokenAragorn) {
        this.tokenAragorn = tokenAragorn;
    }

    public NazgDTO getNazgDTO() {
        if (nazgDTO == null) {
            nazgDTO = new NazgDTO();
        }
        return nazgDTO;
    }

    public void setNazgDTO(NazgDTO nazgDTO) {
        this.nazgDTO = nazgDTO;
    }

    public void verificarDadosParaEnviarAragorn() throws Exception {
        //verificar se houve alterações nos dados que são enviados para Aragorn
        //by Luiz Felipe
        if (getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

            if (getNumeroCartao() == null) {
                throw new Exception("O número do cartão não pode ser nulo!");
            }

            //verificar alterações no número do cartão
            if (getNumeroCartao().contains("*")) {
                //não alterou o número do cartão
                //setar no numero do cartão o numero do cartão sem mascara
                setNumeroCartao(getNazgDTO().getCard());
            } else {
                setCartaoMascarado(APF.getCartaoMascarado(getNumeroCartao()));
            }
        }
    }

    public boolean isAlterouNumeroCartao() {
        return alterouNumeroCartao;
    }

    public void setAlterouNumeroCartao(boolean alterouNumeroCartao) {
        this.alterouNumeroCartao = alterouNumeroCartao;
    }

    public String getCartaoMascaradoAnterior() {
        return cartaoMascaradoAnterior;
    }

    public void setCartaoMascaradoAnterior(String cartaoMascaradoAnterior) {
        this.cartaoMascaradoAnterior = cartaoMascaradoAnterior;
    }

    public String getIntegracaoTitle() {
        StringBuilder title = new StringBuilder();
        if (!UteisValidacao.emptyString(this.getCodigoExterno())) {
            title.append("Integrado com Vindi.");
        } else {
            title.append("Convênio em que serão realizadas as cobranças: " + this.convenio.getDescricao());
        }

//        if (!UteisValidacao.emptyString(this.getCodigoExterno())) {
//            if (title.length() > 0) {
//                title.append("<br/>");
//            }
//            title.append("Integrado com Mundipagg.");
//        }
        return title.toString();
    }

    public boolean isValidarBinCartao() {
        return validarBinCartao;
    }

    public void setValidarBinCartao(boolean validarBinCartao) {
        this.validarBinCartao = validarBinCartao;
    }

    public String getIdCardMundiPagg() {
        if (idCardMundiPagg == null) {
            idCardMundiPagg = "";
        }
        return idCardMundiPagg;
    }

    public void setIdCardMundiPagg(String idCardMundiPagg) {
        this.idCardMundiPagg = idCardMundiPagg;
    }

    public String getIdCardPagarMe() {
        if (idCardPagarMe == null) {
            idCardPagarMe = "";
        }
        return idCardPagarMe;
    }

    public void setIdCardPagarMe(String idCardPagarMe) {
        this.idCardPagarMe = idCardPagarMe;
    }

    public int getVencimentoFatura() {
        return vencimentoFatura;
    }

    public void setVencimentoFatura(int vencimentoFatura) {
        this.vencimentoFatura = vencimentoFatura;
    }

    public boolean isAutorizacaoUtilizandoIdVindiPessoa() {
        Integer idVindiPessoa = 0;
        try {
            AutorizacaoCobrancaClienteVO autoClienteVO = ((AutorizacaoCobrancaClienteVO) this);
            idVindiPessoa = autoClienteVO.getCliente().getPessoa().getIdVindi();
        } catch (Exception ignored) {
        }

        try {
            AutorizacaoCobrancaColaboradorVO autoColaboradorVO = ((AutorizacaoCobrancaColaboradorVO) this);
            idVindiPessoa = autoColaboradorVO.getColaborador().getPessoa().getIdVindi();
        } catch (Exception ignored) {
        }
        return getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                UteisValidacao.emptyString(getTokenAragorn()) &&
                !UteisValidacao.emptyNumber(idVindiPessoa);
    }

    public boolean isAutorizacaoUtilizandotokenCielo() {
        String tokenCielo = "";
        try {
            AutorizacaoCobrancaClienteVO autoClienteVO = ((AutorizacaoCobrancaClienteVO) this);
            tokenCielo = autoClienteVO.getTokenCielo();
        } catch (Exception ignored) {
        }

        try {
            AutorizacaoCobrancaColaboradorVO autoColaboradorVO = ((AutorizacaoCobrancaColaboradorVO) this);
            tokenCielo = autoColaboradorVO.getTokenCielo();
        } catch (Exception ignored) {
        }
        return getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) &&
                UteisValidacao.emptyString(getTokenAragorn()) &&
                !UteisValidacao.emptyString(tokenCielo);
    }

    public boolean isUsarIdVindiPessoa() {
        return usarIdVindiPessoa;
    }

    public void setUsarIdVindiPessoa(boolean usarIdVindiPessoa) {
        this.usarIdVindiPessoa = usarIdVindiPessoa;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public boolean isClienteTitularCartao() {
        return clienteTitularCartao;
    }

    public void setClienteTitularCartao(boolean clienteTitularCartao) {
        this.clienteTitularCartao = clienteTitularCartao;
    }

    public boolean isValidarClienteTitularCartao() {
        return validarClienteTitularCartao;
    }

    public void setValidarClienteTitularCartao(boolean validarClienteTitularCartao) {
        this.validarClienteTitularCartao = validarClienteTitularCartao;
    }

    public boolean isGravarCodigoExterno() {
        return gravarCodigoExterno;
    }

    public void setGravarCodigoExterno(boolean gravarCodigoExterno) {
        this.gravarCodigoExterno = gravarCodigoExterno;
    }

    public String getCvv() {
        if (cvv == null) {
            cvv = "";
        }
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public boolean isCartaoVerificado() {
        return cartaoVerificado;
    }

    public void setCartaoVerificado(boolean cartaoVerificado) {
        this.cartaoVerificado = cartaoVerificado;
    }

    public String getIdPinBank() {
        if (idPinBank == null){
            idPinBank = "";
        }
        return idPinBank;
    }

    public void setIdPinBank(String idPinBank) {
        this.idPinBank = idPinBank;
    }

    public StringBuilder getDescricaoParaLog() {
        StringBuilder msg = new StringBuilder();
        msg.append("Tipo autorização cobrança: ").append(this.getTipoAutorizacao().getDescricao()).append(". \n");
        msg.append("Convênio de cobrança: ").append(this.getConvenio().getCodigo()).append(". \n");
        if (!UteisValidacao.emptyString(this.getConvenio().getDescricao())) {
            msg.append("Convênio de cobrança descricao: ").append(this.getConvenio().getDescricao()).append(". \n");
        }
        if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            msg.append("CPF titular: ").append(this.getCpfTitular()).append(". \n");
            msg.append("Nome titular Cartão: ").append(this.getNomeTitularCartao()).append(". \n");
            msg.append("Número do cartão: ").append(this.getCartaoMascarado_Apresentar()).append(". \n");
            msg.append("Validade do cartão: ").append(this.getValidadeCartao()).append(". \n");
            msg.append("Operadora Cartão: ").append(this.getOperadoraCartao().getDescricao()).append(". \n");
        } else if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
            msg.append("CPF titular: ").append(this.getCpfTitular()).append(". \n");
            msg.append("Nome titular: ").append(this.getNomeTitularCartao()).append(". \n");
            msg.append("Agência: ").append(this.getAgenciaApresentar()).append(". \n");
            msg.append("Conta-Corrente: ").append(this.getContaCorrenteApresentarSemBanco()).append(". \n");
            msg.append("Banco: ").append(this.getBanco().getNome()).append(". \n");
        }
        return msg;
    }

    public void preencherOperadoraCartao() {
        try {
            if (this.getOperadoraCartao() == null) {
                String numeroCard = this.getNumeroCartao();
                if (UteisValidacao.emptyString(numeroCard) ||
                        (numeroCard.contains("*") && !UteisValidacao.emptyString(this.getNazgDTO().getCard()))) {
                    numeroCard = this.getNazgDTO().getCard();
                }
                this.setOperadoraCartao(Uteis.obterOperadoraComException(numeroCard));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getOrdemApresentar() {
        if (this.getTipoAutorizacao() != null &&
                this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            return getOrdem().toString();
        } else {
            return "";
        }
    }

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public boolean isValidarQtdCartoes() {
        return validarQtdCartoes;
    }

    public void setValidarQtdCartoes(boolean validarQtdCartoes) {
        this.validarQtdCartoes = validarQtdCartoes;
    }

    public String getUltimos4Digitos() {
        try {
            return this.getCartaoMascarado().substring(this.getCartaoMascarado().length() - 4);
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCartaoMascaradoUltimos4Digitos() {
        try {
            return (this.getCartaoMascarado().substring(0, this.getCartaoMascarado().length() - 4).replaceAll("[0-9]", "\\*") + this.getCartaoMascarado().substring(this.getCartaoMascarado().length() - 4));
        } catch (Exception ex) {
            return "";
        }
    }

    public String getOperadoraCartaoApresentar() {
        return this.getOperadoraCartao() != null ? this.getOperadoraCartao().getDescricao() : "";
    }

    public boolean isCartaoVencido() {
        try {
            return !Calendario.maiorOuIgual(Uteis.getDate(this.getValidadeCartao(), "MM/yyyy"), Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
        } catch (Exception ex) {
            return false;
        }
    }

    public OrigemCobrancaEnum getOrigemCobrancaEnum() {
        if (origemCobrancaEnum == null) {
            origemCobrancaEnum = OrigemCobrancaEnum.NENHUM;
        }
        return origemCobrancaEnum;
    }

    public void setOrigemCobrancaEnum(OrigemCobrancaEnum origemCobrancaEnum) {
        this.origemCobrancaEnum = origemCobrancaEnum;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTokenCielo() {
        if (tokenCielo == null) {
            tokenCielo = "";
        }
        return tokenCielo;
    }

    public void setTokenCielo(String tokenCielo) {
        this.tokenCielo = tokenCielo;
    }

    public boolean isProcessoImportacao() {
        return processoImportacao;
    }

    public void setProcessoImportacao(boolean processoImportacao) {
        this.processoImportacao = processoImportacao;
    }

    public String getImportacaoCliente() {
        if (importacaoCliente == null) {
            importacaoCliente = "";
        }
        return importacaoCliente;
    }

    public void setImportacaoCliente(String importacaoCliente) {
        this.importacaoCliente = importacaoCliente;
    }

    public String getImportacaoCPF() {
        if (importacaoCPF == null) {
            importacaoCPF = "";
        }
        return importacaoCPF;
    }

    public void setImportacaoCPF(String importacaoCPF) {
        this.importacaoCPF = importacaoCPF;
    }

    public String getImportacaoTitular() {
        if (importacaoTitular == null) {
            importacaoTitular = "";
        }
        return importacaoTitular;
    }

    public void setImportacaoTitular(String importacaoTitular) {
        this.importacaoTitular = importacaoTitular;
    }


    public String getTokenPagoLivre() {
        if (tokenPagoLivre == null) {
            tokenPagoLivre = "";
        }
        return tokenPagoLivre;
    }

    public void setTokenPagoLivre(String tokenPagoLivre) {
        this.tokenPagoLivre = tokenPagoLivre;
    }

    public boolean isImportacaoTokenCartao() {
        return importacaoTokenCartao;
    }

    public void setImportacaoTokenCartao(boolean importacaoTokenCartao) {
        this.importacaoTokenCartao = importacaoTokenCartao;
    }

    public boolean isRealizandoImportacao() {
        return realizandoImportacao;
    }

    public void setRealizandoImportacao(boolean realizandoImportacao) {
        this.realizandoImportacao = realizandoImportacao;
    }
}
