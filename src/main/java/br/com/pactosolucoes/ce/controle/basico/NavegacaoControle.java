package br.com.pactosolucoes.ce.controle.basico;

import br.com.pactosolucoes.ce.comuns.to.CadastroInicialTO;
import br.com.pactosolucoes.ce.comuns.to.ConversaTO;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.ce.controle.CadastroInicialControle;
import br.com.pactosolucoes.ce.controle.ConversaControle;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.UsuarioControle;
import controle.basico.ClienteControle;
import controle.crm.MetaCRMControle;
import controle.financeiro.GestaoBoletosOnlineControle;
import controle.financeiro.GestaoRemessasControle;
import controle.financeiro.GestaoTransacoesControle;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe que contém os metodos para abrir as telas de navegação
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
public class NavegacaoControle extends SuperControle {

    /**
     * Abre a tela CE
     * @return loginCE
     */
    public String abrirCE() {
        return "loginCE";
    }

    /**
     * Abre a tela pesquisaGeral
     * @return pesquisaGeral
     */
    public String abrirPesquisaGeral() {
        return "pesquisaGeral";
    }

    /**
     * Abre a tela fornecedor
     * @return fornecedor
     */
    public String abrirFornecedor() {
        return "fornecedor";
    }

    /**
     * Abre a tela fornecedor
     * @return fornecedor
     */
    public String abrirPerfilEvento() {
        return "perfilEvento";
    }

    /**
     * Abre a tela provaConceito
     * @return provaConceito
     */
    public String abrirTelaProvaConceito() {
        return "provaConceito";
    }

    /**
     * Abre a tela provaConceito
     * @return provaConceito
     */
    public String abrirTelaProvaConceitoPedro() {
        return "provaConceitoPedro";
    }

    /**
     * Abre a tela servico
     * @return servico
     */
    public String abrirTelaServico() {
        return "servico";
    }

    /**
     * Abre a tela provaConceitoNovo
     * @return provaConceitoNovo
     */
    public String abrirTelaProvaConceitoNovo() {
        return "provaConceitoNovo";
    }

    /**
     * Abre a tela tipoAmbiente
     * @return tipoAmbiente
     */
    public String abrirTelaTipoAmbiente() {
        return "tipoAmbiente";
    }

    /**
     * Abre a tela tipoAmbienteNovo
     * @return tipoAmbienteNovo
     */
    public String abrirTelaTipoAmbienteNovo() {
        return "tipoAmbienteNovo";
    }

    /**
     * Abre a tela loginCE
     * @return loginCE
     */
    public String abrirTelaInicial() {
        //redireciona para pagina [/pages/ce/telaInicialCE.jsp]
        return "loginCE";
    }

    public String abrirTelaInicialZW() {
        UsuarioControle userControl = (UsuarioControle) getControlador(UsuarioControle.class.getSimpleName());
        try {
            userControl.atualizarNrMsgNaoLidas(true);
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return "login";
    }
    
    public String abrirTelaCadastros() {
        return "cadastros";
    }

    /**
     * Abre a tela telaCadastro
     * @return telaCadastro
     */
    public String abrirTelaCadastro() {
        //redireciona para pagina [/pages/ce/cadastros/telaCadastro.jsp]
        return "telaCadastro";
    }

    /**
     * Abre a tela preReserva
     * @return preReserva
     */
    public String abrirTelaPreReserva() {
        return "preReserva";
    }

    /**
     * Abre a tela cancelarEvento
     * @return cancelarEvento
     */
    public String abrirTelaCancelamentoEvento() {
        return "cancelarEvento";
    }

    /**
     * Abre a tela caixa em aberto
     * @return pagamento
     */
    public String abrirTelaCaixaEmAberto() {
        return "caixaCE";
    }

    /**
     * Abre a tela prospects
     * @return prospects
     */
    public String abrirTelaProspects() {
        return "prospects";
    }

    /**
     * Abre a tela exibeOrcamento
     * @return exibeOrcamento
     */
    public String abrirTelaExibeOrcamento() {
        return "exibeOrcamento";
    }

    /**
     * Abre a tela detalhamentoEvento
     * @return detalhamentoEvento
     */
    public String abrirTelaExibeDetalhamentoEvento() {
        return "detalhamentoEvento";
    }

    /**
     * Abre a tela cadastroInicial
     * @return cadastroInicial
     */
    public String abrirTelaCadastroInicial() {
        final CadastroInicialControle c = (CadastroInicialControle) JSFUtilities.getManagedBean("CadastroInicialControle");
        c.showModal();
        c.setPessoa(new PessoaTO());
        c.setCadastro(new CadastroInicialTO());
        c.setOrcado(false);
        return "cadastroInicial";
    }

    /**
     * Abre a tela consultaConversas
     * @return consultaConversas
     */
    public String abrirTelaConsultaConversas() {
        final ConversaControle c = (ConversaControle) JSFUtilities.getManagedBean("ConversaControle");
        c.showModal();
        c.setPessoa(new PessoaTO());
        c.setConsultaConversa(new ConversaTO());
        return "consultaConversas";
    }

    /**
     * Abre a tela orcamentoDetalhado
     * @return orcamentoDetalhado
     */
    public String abrirTelaOrcamentoDetalhado() {
        //redireciona para a tela [/pages/ce/cadastros/orcamentoDetalhado.jsp]
        return "orcamentoDetalhado";
    }

    /**
     * Abre a tela auxiliares
     * @return auxiliares
     */
    public String abrirTelaCadastrosAuxiliares() {
        return "auxiliares";
    }

    /**
     * Abre a tela financeira
     * @return financeira
     */
    public String abrirTelaConfigFinanceiras() {
        return "financeira";
    }

    /**
     * Abre a tela acesso
     * @return acesso
     */
    public String abrirTelaAcessoSistema() {
        return "acesso";
    }

    /**
     * Abre a tela contrato
     * @return contrato
     */
    public String abrirTelaConfigContrato() {
        return "contrato";
    }

    /**
     * Abre a tela produtosperfis
     * @return produtosperfis
     */
    public String abrirTelaProdutosPerfis() {
        return "produtosperfis";
    }

    /**
     * Abre a tela consulta
     * @return consulta
     */
    public String abrirTelaCadastroProdutoLocacao() {
        return "consulta";
    }

    /**
     * Abre a tela documentos
     * @return documentos
     */
    public String abrirTelaEmissaoDocumentos() {
        return "documentos";
    }

    /**
     * Abre a tela agenda visita [agendaVisita.jsp]
     * @return agendaVisita
     */
    public String abrirAgendaVisita() {
        return "agendaVisita";
    }

    public String abrirRelatorios() {
        //redireciona para a pagina [/pages/ce/relatorio/telaRelatorio.jsp]
        return "telaRelatorios";
    }

    public String abrirFinanceiro() {
        //redireciona para a pagina [/pages/ce/cadastro/telaFinanceiro.jsp]
        return "telaFinanceiro";
    }

    public String abrirRelatorioClientes() {
        //redireciona para a pagina [/relatorioClientes.jsp]
        return "relatorioClientes";
    }

    public String abrirGestaoTransacoes() {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.GESTAO_DE_TRANSACOES.toString()));
        GestaoTransacoesControle control = (GestaoTransacoesControle) JSFUtilities.getFromSession(GestaoTransacoesControle.class);
        if (control != null) {
            control.novo();
        }
        return "gestaoTransacoes";
    }

    public String abrirGestaoRemessas() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.GESTAO_DE_REMESSAS.toString()));
        GestaoRemessasControle control = (GestaoRemessasControle) JSFUtilities.getFromSession(GestaoRemessasControle.class);
        if (control != null) {
            control.fecharPanelDadosParametros();
        } else {
            control = getControlador(GestaoRemessasControle.class);
        }

        if (control.isInicializarEmpresa()) {
            control.inicializarEmpresa();
        }
        return "gestaoRemessas";
    }

    public String abrirGestaoBoletosOnline() throws Exception {
        GestaoBoletosOnlineControle control = (GestaoBoletosOnlineControle) JSFUtilities.getFromSession(GestaoBoletosOnlineControle.class);
        control = getControlador(GestaoBoletosOnlineControle.class);
        return "gestaoBoletosOnline";
    }

    public String abrirGestaoComissao() {
        return "gestaoComissao";
    }
    
    public String abrirGestaoArmario() {
        return "gestaoArmario";
    }

    public String abrirListaClientesSimplificada() {
        ClienteControle control = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class);
        if (control != null) {
            control.gerarListaClientes();
        }
        return "listaClientesDadosBasicos";
    }

    public String abrirMenuAnteriorFinan() {
        return "cadastrosFinan";
    }

    public String abrirSocialMail() {
        return "socialMailing";
    }

    public String abrirRealizarContato() throws Exception {
        MetaCRMControle control = (MetaCRMControle) getControlador(MetaCRMControle.class.getSimpleName());
        control.inicializarContatoAvulso();
        return "newRealizarContato";
    }
    
    public String abrirListasRelatorios() throws Exception {
        return "listasRelatorios";
    }

    public String abrirGestaoCredito() {
        return "gestaoCredito";
    }
}
