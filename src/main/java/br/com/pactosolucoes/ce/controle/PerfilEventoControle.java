package br.com.pactosolucoes.ce.controle;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.UteisValidacao;

import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.ce.comuns.enumerador.FormaCalculo;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoOperacao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.ex.NegocioException;
import br.com.pactosolucoes.ce.comuns.to.ModeloContratoTO;
import br.com.pactosolucoes.ce.comuns.to.ModeloImagemTO;
import br.com.pactosolucoes.ce.comuns.to.ModeloOrcamentoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteLayoutTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoSazonalidadeTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoServicoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoTO;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.ServicoTO;
import br.com.pactosolucoes.ce.negocio.perfil.PerfilEventoServicoVO;
import br.com.pactosolucoes.comuns.util.FaixaQuantidade;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF da página perfilEvento.jsp.
 * 
 * 
 * @see SuperControle
 * 
 * <AUTHOR>
 */
public class PerfilEventoControle extends CEControle {

	/* ------------------- INÍCIO - ATRIBUTOS --------------------- */

	private PerfilEventoTO perfilEventoTO;
	private List<PerfilEventoTO> resultadoConsulta;

	private ProdutoVO filtroProdutos;
	private List<ProdutoVO> produtos;

	/* -- INÍCIO - ABA AMBIENTES -- */
	private PerfilEventoAmbienteTO perfilEventoAmbienteTO;
	private PerfilEventoAmbienteTO ambienteAAlterar;
	private List<Integer> ambientesAExcluir;
	private PerfilEventoSazonalidadeTO perfilEventoSazonalidadeTO;
	private PerfilEventoSazonalidadeTO sazonalidadeAAlterar;

	private Integer codigoAmbiente;
	private String descricaoAmbiente;
	private Double valorAmbiente;
	private String exibeCalculo;
	private List<PerfilEventoSazonalidadeTO> perfilEventoSazonalidadeTOs;
	private List<Integer> sazonalidadesAExcluir;
	private Map<Integer, List<PerfilEventoSazonalidadeTO>> mapPerfilEventoSazonalidadeTO;

	private Boolean edicaoPerfilEventoAmbiente;
	private Boolean edicaoPerfilEventoSazonalidade;

	// guiaVisualizada == 1 -> visualiza a guia de ambientes
	// guiaVisualizada == 2 -> visualiza a guia de sazonalidades
	// guiaVisualizada == 3 -> visualiza a guia de layouts
	private Integer guiaVisualizada;
	/* -- FIM - ABA AMBIENTES -- */

	/* -- INÍCIO - ABA SERVIÇOS -- */
	private ServicoTO filtroServicos;
	private PerfilEventoServicoTO servico;
	private PerfilEventoServicoTO servicoAAlterar;
	private List<Integer> servicosAExcluir;

	private List<ServicoTO> servicos;
	private Boolean temServicoAnterior;
	private Boolean edicaoPerfilEventoServico;
	/* -- FIM - ABA SERVIÇOS -- */

	// Bens de consumo, utensilios e brinquedos a excluir do perfil de evento.
	private List<Integer> produtosLocacaoAExcluir;

	/* -- INÍCIO - ABA BENS DE CONSUMO -- */
	private ProdutoLocacaoTO filtroBensConsumo;
	private PerfilEventoProdutoLocacaoTO bemConsumo;
	private PerfilEventoProdutoLocacaoTO bemConsumoAAlterar;

	private List<ProdutoLocacaoTO> bensConsumo;

	private Boolean temBemConsumoAnterior;
	private Boolean edicaoPerfilEventoBemConsumo;
	/* -- FIM - ABA BENS DE CONSUMO -- */

	/* -- INÍCIO - ABA UTENSÍLIOS -- */
	private ProdutoLocacaoTO filtroUtensilios;
	private PerfilEventoProdutoLocacaoTO utensilio;
	private PerfilEventoProdutoLocacaoTO utensilioAAlterar;

	private List<ProdutoLocacaoTO> utensilios;

	private Boolean temUtensilioAnterior;
	private Boolean edicaoPerfilEventoUtensilio;
	/* -- FIM - ABA UTENSÍLIOS -- */

	/* -- INÍCIO - ABA BRINQUEDOS -- */
	private ProdutoLocacaoTO filtroBrinquedos;
	private PerfilEventoProdutoLocacaoTO brinquedo;
	private PerfilEventoProdutoLocacaoTO brinquedoAAlterar;

	private List<ProdutoLocacaoTO> brinquedos;

	private Boolean temBrinquedoAnterior;
	private Boolean edicaoPerfilEventoBrinquedo;
	private String msgAlert;

	/* -- FIM - ABA BRINQUEDOS -- */
	
	/* -- OBJETO PARA LOG -- */
	private PerfilEventoTO objetoLog;

	/* ------------------- FIM - ATRIBUTOS --------------------- */

	/* ------------------- INÍCIO - CONSTRUTORES --------------------- */

	/**
	 * Construtor padrão.
	 */
	public PerfilEventoControle() throws Exception {
		// Inicializar controle de paginação de consulta
		this.setControleConsulta(new ControleConsulta());
		// Inicializar lista que guarda o resultado da consulta
		this.resultadoConsulta = new ArrayList<PerfilEventoTO>();
		// Set mensagem informar parâmetros
		this.setMensagemID("parametros.informar");
	}

	/* ------------------- FIM - CONSTRUTORES --------------------- */

	/* ------------------- INÍCIO - GETTERS AND SETTERS --------------------- */
	/**
	 * @return the valorAmbiente
	 */
	public Double getValorAmbiente() {
		return this.valorAmbiente;
	}

	/**
	 * @param valorAmbiente
	 *            the valorAmbiente to set
	 */
	public void setValorAmbiente(final Double valorAmbiente) {
		this.valorAmbiente = valorAmbiente;
	}

	/**
	 * @return the exibeCalculo
	 */
	public String getExibeCalculo() {
		return this.exibeCalculo;
	}

	/**
	 * @param exibeCalculo
	 *            the exibeCalculo to set
	 */
	public void setExibeCalculo(final String exibeCalculo) {
		this.exibeCalculo = exibeCalculo;
	}

	/**
	 * @return O campo perfilEventoAmbienteTO.
	 */
	public PerfilEventoTO getPerfilEventoTO() {
		if (this.perfilEventoTO == null) {
			this.perfilEventoTO = new PerfilEventoTO();
		}
		return this.perfilEventoTO;
	}

	/**
	 * @param perfilEventoTO
	 *            O novo valor de perfilEventoAmbienteTO.
	 */
	public void setPerfilEventoTO(final PerfilEventoTO perfilEventoTO) {
		this.perfilEventoTO = perfilEventoTO;
	}

	/**
	 * @return O campo resultadoConsulta.
	 */
	public List<PerfilEventoTO> getResultadoConsulta() {
		if (this.resultadoConsulta == null) {
			this.resultadoConsulta = new ArrayList<PerfilEventoTO>();
		}
		return this.resultadoConsulta;
	}

	/**
	 * @param resultadoConsulta
	 *            O novo valor de resultadoConsulta.
	 */
	public void setResultadoConsulta(final List<PerfilEventoTO> resultadoConsulta) {
		this.resultadoConsulta = resultadoConsulta;
	}

	/**
	 * @return O campo filtroProdutos.
	 */
	public ProdutoVO getFiltroProdutos() {
		if (this.filtroProdutos == null) {
			this.filtroProdutos = new ProdutoVO();
		}
		return this.filtroProdutos;
	}

	/**
	 * @param filtroProdutos
	 *            O novo valor de filtroProdutos.
	 */
	public void setFiltroProdutos(final ProdutoVO filtroProdutos) {
		this.filtroProdutos = filtroProdutos;
	}

	/**
	 * @return O campo produtos.
	 */
	public List<ProdutoVO> getProdutos() {
		return this.produtos;
	}

	/**
	 * @param produtos
	 *            O novo valor de produtos.
	 */
	public void setProdutos(final List<ProdutoVO> produtos) {
		this.produtos = produtos;
	}

	/**
	 * @return O campo perfilEventoAmbienteTO.
	 */
	public PerfilEventoAmbienteTO getPerfilEventoAmbienteTO() {
		if (this.perfilEventoAmbienteTO == null) {
			this.perfilEventoAmbienteTO = new PerfilEventoAmbienteTO();
		}

		return this.perfilEventoAmbienteTO;
	}

	/**
	 * @param perfilEventoAmbienteTO
	 *            O novo valor de perfilEventoAmbienteTO.
	 */
	public void setPerfilEventoAmbienteTO(final PerfilEventoAmbienteTO perfilEventoAmbienteTO) {
		this.perfilEventoAmbienteTO = perfilEventoAmbienteTO;
	}

	/**
	 * @return O campo ambienteAAlterar.
	 */
	public PerfilEventoAmbienteTO getAmbienteAAlterar() {
		if (this.ambienteAAlterar == null) {
			this.ambienteAAlterar = new PerfilEventoAmbienteTO();
		}
		return this.ambienteAAlterar;
	}

	/**
	 * @param ambienteAAlterar
	 *            O novo valor de ambienteAAlterar.
	 */
	public void setAmbienteAAlterar(final PerfilEventoAmbienteTO ambienteAAlterar) {
		this.ambienteAAlterar = ambienteAAlterar;
	}

	/**
	 * @return O campo ambientesAExcluir.
	 */
	public List<Integer> getAmbientesAExcluir() {
		if (this.ambientesAExcluir == null) {
			this.ambientesAExcluir = new ArrayList<Integer>();
		}
		return this.ambientesAExcluir;
	}

	/**
	 * @param ambientesAExcluir
	 *            O novo valor de ambientesAExcluir.
	 */
	public void setAmbientesAExcluir(final List<Integer> ambientesAExcluir) {
		this.ambientesAExcluir = ambientesAExcluir;
	}

	/**
	 * @return O campo perfilEventoSazonalidadeTO.
	 */
	public PerfilEventoSazonalidadeTO getPerfilEventoSazonalidadeTO() {
		if (this.perfilEventoSazonalidadeTO == null) {
			this.perfilEventoSazonalidadeTO = new PerfilEventoSazonalidadeTO();
		}
		return this.perfilEventoSazonalidadeTO;
	}

	/**
	 * @param perfilEventoSazonalidadeTO
	 *            O novo valor de perfilEventoSazonalidadeTO.
	 */
	public void setPerfilEventoSazonalidadeTO(final PerfilEventoSazonalidadeTO perfilEventoSazonalidadeTO) {
		this.perfilEventoSazonalidadeTO = perfilEventoSazonalidadeTO;
	}

	/**
	 * @return O campo sazonalidadeAAlterar.
	 */
	public PerfilEventoSazonalidadeTO getSazonalidadeAAlterar() {
		if (this.sazonalidadeAAlterar == null) {
			this.sazonalidadeAAlterar = new PerfilEventoSazonalidadeTO();
		}
		return this.sazonalidadeAAlterar;
	}

	/**
	 * @param sazonalidadeAAlterar
	 *            O novo valor de sazonalidadeAAlterar.
	 */
	public void setSazonalidadeAAlterar(final PerfilEventoSazonalidadeTO sazonalidadeAAlterar) {
		this.sazonalidadeAAlterar = sazonalidadeAAlterar;
	}

	/**
	 * @return O campo perfilEventoSazonalidadeTOs.
	 */
	public List<PerfilEventoSazonalidadeTO> getPerfilEventoSazonalidadeTOs() {
		if (this.perfilEventoSazonalidadeTOs == null) {
			this.perfilEventoSazonalidadeTOs = new ArrayList<PerfilEventoSazonalidadeTO>();
		}
		return this.perfilEventoSazonalidadeTOs;
	}

	/**
	 * @param perfilEventoSazonalidadeTOs
	 *            O novo valor de perfilEventoSazonalidadeTOs.
	 */
	public void setPerfilEventoSazonalidadeTOs(final List<PerfilEventoSazonalidadeTO> perfilEventoSazonalidadeTOs) {
		this.perfilEventoSazonalidadeTOs = perfilEventoSazonalidadeTOs;
	}

	/**
	 * @return O campo sazonalidadesAExcluir.
	 */
	public List<Integer> getSazonalidadesAExcluir() {
		if (this.sazonalidadesAExcluir == null) {
			this.sazonalidadesAExcluir = new ArrayList<Integer>();
		}
		return this.sazonalidadesAExcluir;
	}

	/**
	 * @param sazonalidadesAExcluir
	 *            O novo valor de sazonalidadesAExcluir.
	 */
	public void setSazonalidadesAExcluir(final List<Integer> sazonalidadesAExcluir) {
		this.sazonalidadesAExcluir = sazonalidadesAExcluir;
	}

	/**
	 * @return O campo mapPerfilEventoSazonalidadeTO.
	 */
	public Map<Integer, List<PerfilEventoSazonalidadeTO>> getMapPerfilEventoSazonalidadeTO() {
		if (this.mapPerfilEventoSazonalidadeTO == null) {
			this.mapPerfilEventoSazonalidadeTO = new HashMap<Integer, List<PerfilEventoSazonalidadeTO>>();
		}
		return this.mapPerfilEventoSazonalidadeTO;
	}

	/**
	 * @param mapPerfilEventoSazonalidadeTO
	 *            O novo valor de mapPerfilEventoSazonalidadeTO.
	 */
	public void setMapPerfilEventoSazonalidadeTO(final Map<Integer, List<PerfilEventoSazonalidadeTO>> mapPerfilEventoSazonalidadeTO) {
		this.mapPerfilEventoSazonalidadeTO = mapPerfilEventoSazonalidadeTO;
	}

	/**
	 * @return O campo codigoAmbiente.
	 */
	public Integer getCodigoAmbiente() {
		return this.codigoAmbiente;
	}

	/**
	 * @param codigoAmbiente
	 *            O novo valor de codigoAmbiente.
	 */
	public void setCodigoAmbiente(final Integer codigoAmbiente) {
		this.codigoAmbiente = codigoAmbiente;
	}

	/**
	 * @return O campo descricaoAmbiente.
	 */
	public String getDescricaoAmbiente() {
		return this.descricaoAmbiente;
	}

	/**
	 * @param descricaoAmbiente
	 *            O novo valor de descricaoAmbiente.
	 */
	public void setDescricaoAmbiente(final String descricaoAmbiente) {
		this.descricaoAmbiente = descricaoAmbiente;
	}

	/**
	 * @return O campo edicaoPerfilEventoAmbiente.
	 */
	public Boolean getEdicaoPerfilEventoAmbiente() {
		if (this.edicaoPerfilEventoAmbiente == null) {
			this.edicaoPerfilEventoAmbiente = false;
		}
		return this.edicaoPerfilEventoAmbiente;
	}

	/**
	 * @param edicaoPerfilEventoAmbiente
	 *            O novo valor de edicaoPerfilEventoAmbiente.
	 */
	public void setEdicaoPerfilEventoAmbiente(final Boolean edicaoPerfilEventoAmbiente) {
		this.edicaoPerfilEventoAmbiente = edicaoPerfilEventoAmbiente;
	}

	/**
	 * @return O campo edicaoPerfilEventoSazonalidade.
	 */
	public Boolean getEdicaoPerfilEventoSazonalidade() {
		if (this.edicaoPerfilEventoSazonalidade == null) {
			this.edicaoPerfilEventoSazonalidade = false;
		}
		return this.edicaoPerfilEventoSazonalidade;
	}

	/**
	 * @param edicaoPerfilEventoSazonalidade
	 *            O novo valor de edicaoPerfilEventoSazonalidade.
	 */
	public void setEdicaoPerfilEventoSazonalidade(final Boolean edicaoPerfilEventoSazonalidade) {
		this.edicaoPerfilEventoSazonalidade = edicaoPerfilEventoSazonalidade;
	}

	/**
	 * @return O campo guiaVisualizada.
	 */
	public Integer getGuiaVisualizada() {
		if (this.guiaVisualizada == null) {
			this.guiaVisualizada = 1;
		}
		return this.guiaVisualizada;
	}

	/**
	 * @param guiaVisualizada
	 *            O novo valor de guiaVisualizada.
	 */
	public void setGuiaVisualizada(final Integer guiaVisualizada) {
		this.guiaVisualizada = guiaVisualizada;
	}

	/**
	 * @return O campo filtroServicos.
	 */
	public ServicoTO getFiltroServicos() {
		if (this.filtroServicos == null) {
			this.filtroServicos = new ServicoTO();
		}
		return this.filtroServicos;
	}

	/**
	 * @param filtroServicos
	 *            O novo valor de filtroServicos.
	 */
	public void setFiltroServicos(final ServicoTO filtroServicos) {
		this.filtroServicos = filtroServicos;
	}

	/**
	 * @return O campo servico.
	 */
	public PerfilEventoServicoTO getServico() {
		if (this.servico == null) {
			this.servico = new PerfilEventoServicoTO();
		}
		return this.servico;
	}

	/**
	 * @param servico
	 *            O novo valor de servico.
	 */
	public void setServico(final PerfilEventoServicoTO servico) {
		this.servico = servico;
	}

	/**
	 * @return O campo servicoAAlterar.
	 */
	public PerfilEventoServicoTO getServicoAAlterar() {
		if (this.servicoAAlterar == null) {
			this.servicoAAlterar = new PerfilEventoServicoTO();
		}
		return this.servicoAAlterar;
	}

	/**
	 * @param servicoAAlterar
	 *            O novo valor de servicoAAlterar.
	 */
	public void setServicoAAlterar(final PerfilEventoServicoTO servicoAAlterar) {
		this.servicoAAlterar = servicoAAlterar;
	}

	/**
	 * @return O campo servicosAExcluir.
	 */
	public List<Integer> getServicosAExcluir() {
		if (this.servicosAExcluir == null) {
			this.servicosAExcluir = new ArrayList<Integer>();
		}
		return this.servicosAExcluir;
	}

	/**
	 * @param servicosAExcluir
	 *            O novo valor de servicosAExcluir.
	 */
	public void setServicosAExcluir(final List<Integer> servicosAExcluir) {
		this.servicosAExcluir = servicosAExcluir;
	}

	/**
	 * @return O campo servicos.
	 */
	public List<ServicoTO> getServicos() {
		if (this.servicos == null) {
			this.servicos = new ArrayList<ServicoTO>();
		}
		return this.servicos;
	}

	/**
	 * @param servicos
	 *            O novo valor de servicos.
	 */
	public void setServicos(final List<ServicoTO> servicos) {
		this.servicos = servicos;
	}

	/**
	 * @return O campo edicaoPerfilEventoServico.
	 */
	public Boolean getEdicaoPerfilEventoServico() {
		if (this.edicaoPerfilEventoServico == null) {
			this.edicaoPerfilEventoServico = false;
		}
		return this.edicaoPerfilEventoServico;
	}

	/**
	 * @param edicaoPerfilEventoServico
	 *            O novo valor de edicaoPerfilEventoServico.
	 */
	public void setEdicaoPerfilEventoServico(final Boolean edicaoPerfilEventoServico) {
		this.edicaoPerfilEventoServico = edicaoPerfilEventoServico;
	}

	/**
	 * @return O campo produtosLocacaoAExcluir.
	 */
	public List<Integer> getProdutosLocacaoAExcluir() {
		if (this.produtosLocacaoAExcluir == null) {
			this.produtosLocacaoAExcluir = new ArrayList<Integer>();
		}
		return this.produtosLocacaoAExcluir;
	}

	/**
	 * @param produtosLocacaoAExcluir
	 *            O novo valor de produtosLocacaoAExcluir.
	 */
	public void setProdutosLocacaoAExcluir(final List<Integer> produtosLocacaoAExcluir) {
		this.produtosLocacaoAExcluir = produtosLocacaoAExcluir;
	}

	/**
	 * @return O campo filtroBensConsumo.
	 */
	public ProdutoLocacaoTO getFiltroBensConsumo() {
		if (this.filtroBensConsumo == null) {
			this.filtroBensConsumo = new ProdutoLocacaoTO();
			this.filtroBensConsumo.setTipo(TipoProdutoLocacao.BENS_DE_CONSUMO);
		}
		return this.filtroBensConsumo;
	}

	/**
	 * @param filtroBensConsumo
	 *            O novo valor de filtroBensConsumo.
	 */
	public void setFiltroBensConsumo(final ProdutoLocacaoTO filtroBensConsumo) {
		this.filtroBensConsumo = filtroBensConsumo;
	}

	/**
	 * @return O campo bemConsumo.
	 */
	public PerfilEventoProdutoLocacaoTO getBemConsumo() {
		if (this.bemConsumo == null) {
			this.bemConsumo = new PerfilEventoProdutoLocacaoTO();
		}
		return this.bemConsumo;
	}

	/**
	 * @param bemConsumo
	 *            O novo valor de bemConsumo.
	 */
	public void setBemConsumo(final PerfilEventoProdutoLocacaoTO bemConsumo) {
		this.bemConsumo = bemConsumo;
	}

	/**
	 * @return O campo bemConsumoAAlterar.
	 */
	public PerfilEventoProdutoLocacaoTO getBemConsumoAAlterar() {
		if (this.bemConsumoAAlterar == null) {
			this.bemConsumoAAlterar = new PerfilEventoProdutoLocacaoTO();
		}
		return this.bemConsumoAAlterar;
	}

	/**
	 * @param bemConsumoAAlterar
	 *            O novo valor de bemConsumoAAlterar.
	 */
	public void setBemConsumoAAlterar(final PerfilEventoProdutoLocacaoTO bemConsumoAAlterar) {
		this.bemConsumoAAlterar = bemConsumoAAlterar;
	}

	/**
	 * @return O campo bensConsumo.
	 */
	public List<ProdutoLocacaoTO> getBensConsumo() {
		if (this.bensConsumo == null) {
			this.bensConsumo = new ArrayList<ProdutoLocacaoTO>();
		}
		return this.bensConsumo;
	}

	/**
	 * @param bensConsumo
	 *            O novo valor de bensConsumo.
	 */
	public void setBensConsumo(final List<ProdutoLocacaoTO> bensConsumo) {
		this.bensConsumo = bensConsumo;
	}

	/**
	 * @return O campo temBemConsumoAnterior.
	 */
	public Boolean getTemBemConsumoAnterior() {
		if (this.temBemConsumoAnterior == null) {
			this.temBemConsumoAnterior = Boolean.FALSE;
		}
		return this.temBemConsumoAnterior;
	}

	/**
	 * @param temBemConsumoAnterior
	 *            O novo valor de temBemConsumoAnterior.
	 */
	public void setTemBemConsumoAnterior(final Boolean temBemConsumoAnterior) {
		this.temBemConsumoAnterior = temBemConsumoAnterior;
	}

	/**
	 * @return O campo edicaoPerfilEventoBemConsumo.
	 */
	public Boolean getEdicaoPerfilEventoBemConsumo() {
		if (this.edicaoPerfilEventoBemConsumo == null) {
			this.edicaoPerfilEventoBemConsumo = false;
		}
		return this.edicaoPerfilEventoBemConsumo;
	}

	/**
	 * @param edicaoPerfilEventoBemConsumo
	 *            O novo valor de edicaoPerfilEventoBemConsumo.
	 */
	public void setEdicaoPerfilEventoBemConsumo(final Boolean edicaoPerfilEventoBemConsumo) {
		this.edicaoPerfilEventoBemConsumo = edicaoPerfilEventoBemConsumo;
	}

	/**
	 * @return O campo filtroUtensilios.
	 */
	public ProdutoLocacaoTO getFiltroUtensilios() {
		if (this.filtroUtensilios == null) {
			this.filtroUtensilios = new ProdutoLocacaoTO();
			this.filtroUtensilios.setTipo(TipoProdutoLocacao.UTENSILIOS);
		}
		return this.filtroUtensilios;
	}

	/**
	 * @param filtroUtensilios
	 *            O novo valor de filtroUtensilios.
	 */
	public void setFiltroUtensilios(final ProdutoLocacaoTO filtroUtensilios) {
		this.filtroUtensilios = filtroUtensilios;
	}

	/**
	 * @return O campo utensilio.
	 */
	public PerfilEventoProdutoLocacaoTO getUtensilio() {
		if (this.utensilio == null) {
			this.utensilio = new PerfilEventoProdutoLocacaoTO();
		}
		return this.utensilio;
	}

	/**
	 * @param utensilio
	 *            O novo valor de utensilio.
	 */
	public void setUtensilio(final PerfilEventoProdutoLocacaoTO utensilio) {
		this.utensilio = utensilio;
	}

	/**
	 * @return O campo utensilioAAlterar.
	 */
	public PerfilEventoProdutoLocacaoTO getUtensilioAAlterar() {
		if (this.utensilioAAlterar == null) {
			this.utensilioAAlterar = new PerfilEventoProdutoLocacaoTO();
		}
		return this.utensilioAAlterar;
	}

	/**
	 * @param utensilioAAlterar
	 *            O novo valor de utensilioAAlterar.
	 */
	public void setUtensilioAAlterar(final PerfilEventoProdutoLocacaoTO utensilioAAlterar) {
		this.utensilioAAlterar = utensilioAAlterar;
	}

	/**
	 * @return O campo utensilios.
	 */
	public List<ProdutoLocacaoTO> getUtensilios() {
		if (this.utensilios == null) {
			this.utensilios = new ArrayList<ProdutoLocacaoTO>();
		}
		return this.utensilios;
	}

	/**
	 * @param utensilios
	 *            O novo valor de utensilios.
	 */
	public void setUtensilios(final List<ProdutoLocacaoTO> utensilios) {
		this.utensilios = utensilios;
	}

	/**
	 * @return O campo temUtensilioAnterior.
	 */
	public Boolean getTemUtensilioAnterior() {
		if (this.temUtensilioAnterior == null) {
			this.temUtensilioAnterior = Boolean.FALSE;
		}
		return this.temUtensilioAnterior;
	}

	/**
	 * @param temUtensilioAnterior
	 *            O novo valor de temUtensilioAnterior.
	 */
	public void setTemUtensilioAnterior(final Boolean temUtensilioAnterior) {
		this.temUtensilioAnterior = temUtensilioAnterior;
	}

	/**
	 * @return O campo edicaoPerfilEventoUtensilio.
	 */
	public Boolean getEdicaoPerfilEventoUtensilio() {
		if (this.edicaoPerfilEventoUtensilio == null) {
			this.edicaoPerfilEventoUtensilio = false;
		}
		return this.edicaoPerfilEventoUtensilio;
	}

	/**
	 * @param edicaoPerfilEventoUtensilio
	 *            O novo valor de edicaoPerfilEventoUtensilio.
	 */
	public void setEdicaoPerfilEventoUtensilio(final Boolean edicaoPerfilEventoUtensilio) {
		this.edicaoPerfilEventoUtensilio = edicaoPerfilEventoUtensilio;
	}

	/**
	 * @return O campo filtroBrinquedos.
	 */
	public ProdutoLocacaoTO getFiltroBrinquedos() {
		if (this.filtroBrinquedos == null) {
			this.filtroBrinquedos = new ProdutoLocacaoTO();
			this.filtroBrinquedos.setTipo(TipoProdutoLocacao.BRINQUEDOS);
		}
		return this.filtroBrinquedos;
	}

	/**
	 * @param filtroBrinquedos
	 *            O novo valor de filtroBrinquedos.
	 */
	public void setFiltroBrinquedos(final ProdutoLocacaoTO filtroBrinquedos) {
		this.filtroBrinquedos = filtroBrinquedos;
	}

	/**
	 * @return O campo brinquedo.
	 */
	public PerfilEventoProdutoLocacaoTO getBrinquedo() {
		if (this.brinquedo == null) {
			this.brinquedo = new PerfilEventoProdutoLocacaoTO();
		}
		return this.brinquedo;
	}

	/**
	 * @param brinquedo
	 *            O novo valor de brinquedo.
	 */
	public void setBrinquedo(final PerfilEventoProdutoLocacaoTO brinquedo) {
		this.brinquedo = brinquedo;
	}

	/**
	 * @return O campo brinquedoAAlterar.
	 */
	public PerfilEventoProdutoLocacaoTO getBrinquedoAAlterar() {
		if (this.brinquedoAAlterar == null) {
			this.brinquedoAAlterar = new PerfilEventoProdutoLocacaoTO();
		}
		return this.brinquedoAAlterar;
	}

	/**
	 * @param brinquedoAAlterar
	 *            O novo valor de brinquedoAAlterar.
	 */
	public void setBrinquedoAAlterar(final PerfilEventoProdutoLocacaoTO brinquedoAAlterar) {
		this.brinquedoAAlterar = brinquedoAAlterar;
	}

	/**
	 * @return O campo brinquedos.
	 */
	public List<ProdutoLocacaoTO> getBrinquedos() {
		if (this.brinquedos == null) {
			this.brinquedos = new ArrayList<ProdutoLocacaoTO>();
		}
		return this.brinquedos;
	}

	/**
	 * @param brinquedos
	 *            O novo valor de brinquedos.
	 */
	public void setBrinquedos(final List<ProdutoLocacaoTO> brinquedos) {
		this.brinquedos = brinquedos;
	}

	/**
	 * @return O campo temBrinquedoAnterior.
	 */
	public Boolean getTemBrinquedoAnterior() {
		if (this.temBrinquedoAnterior == null) {
			this.temBrinquedoAnterior = Boolean.FALSE;
		}
		return this.temBrinquedoAnterior;
	}

	/**
	 * @param temBrinquedoAnterior
	 *            O novo valor de temBrinquedoAnterior.
	 */
	public void setTemBrinquedoAnterior(final Boolean brinquedoAnteriorObrig) {
		this.temBrinquedoAnterior = brinquedoAnteriorObrig;
	}

	/**
	 * @return O campo edicaoPerfilEventoBrinquedo.
	 */
	public Boolean getEdicaoPerfilEventoBrinquedo() {
		if (this.edicaoPerfilEventoBrinquedo == null) {
			this.edicaoPerfilEventoBrinquedo = false;
		}
		return this.edicaoPerfilEventoBrinquedo;
	}

	/**
	 * @param edicaoPerfilEventoBrinquedo
	 *            O novo valor de edicaoPerfilEventoBrinquedo.
	 */
	public void setEdicaoPerfilEventoBrinquedo(final Boolean edicaoPerfilEventoBrinquedo) {
		this.edicaoPerfilEventoBrinquedo = edicaoPerfilEventoBrinquedo;
	}

	/* ------------------- FIM - GETTERS AND SETTERS --------------------- */

	/* ------------------- INÍCIO - DEMAIS MÉTODOS --------------------- */

	/* -- INÍCIO - ABA MODELO DE CONTRATO -- */
	private ModeloContratoTO modeloContrato;
	private Integer numeroArquivos;
	private ModeloContratoTO modeloAddImagens;
	private ModeloImagemTO imagem;
	private Boolean inclusaoImagemModelo;
	private Boolean arquivoCarregado;

	private List<Integer> modelosContratoAExcluir;
	private List<Integer> imagensAExcluir;

	/**
	 * @return O campo modeloContrato.
	 */
	public ModeloContratoTO getModeloContrato() {
		if (this.modeloContrato == null) {
			this.modeloContrato = new ModeloContratoTO();
		}
		return this.modeloContrato;
	}

	/**
	 * @param modeloContrato
	 *            O novo valor de modeloContrato.
	 */
	public void setModeloContrato(final ModeloContratoTO modeloContrato) {
		this.modeloContrato = modeloContrato;
	}

	/**
	 * @return O campo numeroArquivos.
	 */
	public Integer getNumeroArquivos() {
		if (this.numeroArquivos == null) {
			this.iniciarNumeroArquivos();
		}
		return this.numeroArquivos;
	}

	/**
	 * @param numeroArquivos
	 *            O novo valor de numeroArquivos.
	 */
	public void setNumeroArquivos(final Integer numeroArquivos) {
		this.numeroArquivos = numeroArquivos;
	}

	/**
	 * @return O campo modeloAddImagens.
	 */
	public ModeloContratoTO getModeloAddImagens() {
		return this.modeloAddImagens;
	}

	/**
	 * @param modeloAddImagens
	 *            O novo valor de modeloAddImagens.
	 */
	public void setModeloAddImagens(final ModeloContratoTO modeloAddImagens) {
		this.modeloAddImagens = modeloAddImagens;
	}

	/**
	 * @return O campo imagem.
	 */
	public ModeloImagemTO getImagem() {
		if (this.imagem == null) {
			this.imagem = new ModeloImagemTO();
		}
		return this.imagem;
	}

	/**
	 * @param imagem
	 *            O novo valor de imagem.
	 */
	public void setImagem(final ModeloImagemTO imagem) {
		this.imagem = imagem;
	}

	/**
	 * @return O campo inclusaoImagemModelo.
	 */
	public Boolean getInclusaoImagemModelo() {
		if (this.inclusaoImagemModelo == null) {
			this.inclusaoImagemModelo = Boolean.FALSE;
		}
		return this.inclusaoImagemModelo;
	}

	/**
	 * @param inclusaoImagemModelo
	 *            O novo valor de inclusaoImagemModelo.
	 */
	public void setInclusaoImagemModelo(final Boolean inclusaoImagemModelo) {
		this.inclusaoImagemModelo = inclusaoImagemModelo;
	}

	/**
	 * @return O campo arquivoCarregado.
	 */
	public Boolean getArquivoCarregado() {
		if (this.arquivoCarregado == null) {
			this.arquivoCarregado = Boolean.FALSE;
		}
		return this.arquivoCarregado;
	}

	/**
	 * @param arquivoCarregado
	 *            O novo valor de arquivoCarregado.
	 */
	public void setArquivoCarregado(final Boolean arquivoCarregado) {
		this.arquivoCarregado = arquivoCarregado;
	}

	/**
	 * @return O campo modelosContratoAExcluir.
	 */
	public List<Integer> getModelosContratoAExcluir() {
		if (this.modelosContratoAExcluir == null) {
			this.modelosContratoAExcluir = new ArrayList<Integer>();
		}
		return this.modelosContratoAExcluir;
	}

	/**
	 * @param modelosContratoAExcluir
	 *            O novo valor de modelosContratoAExcluir.
	 */
	public void setModelosContratoAExcluir(final List<Integer> modelosContratoAExcluir) {
		this.modelosContratoAExcluir = modelosContratoAExcluir;
	}

	/**
	 * @return O campo imagensAExcluir.
	 */
	public List<Integer> getImagensAExcluir() {
		if (this.imagensAExcluir == null) {
			this.imagensAExcluir = new ArrayList<Integer>();
		}
		return this.imagensAExcluir;
	}

	/**
	 * @param imagensAExcluir
	 *            O novo valor de imagensAExcluir.
	 */
	public void setImagensAExcluir(final List<Integer> imagensAExcluir) {
		this.imagensAExcluir = imagensAExcluir;
	}

	public void iniciarNumeroArquivos() {
		this.setNumeroArquivos(1);
	}

	public void incrementarNumeroArquivos() {
		this.numeroArquivos++;
	}

	public void decrementarNumeroArquivos() {
		this.numeroArquivos--;
	}

	/**
	 * Realiza o upload do arquivo de modelo de contrato.
	 * 
	 * @param event
	 * @throws IOException
	 */
	public void uploadModeloListener(final UploadEvent event) throws IOException {
		// Obter o arquivo a partir do evento
		final UploadItem item = event.getUploadItem();
		final File modelUploaded = item.getFile();
		// cria um novo arquivo
		final File arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
		// caso esse arquivo já exista ele é deletado
		arquivo.delete();
		final FileOutputStream out = new FileOutputStream(arquivo);
		out.write(FileUtilities.obterBytesArquivo(modelUploaded));
		// limpa a memoria assim que esse arquivo é carregado na memória
		out.flush();
		out.close();

		this.getModeloContrato().setArquivo(arquivo);
		this.getModeloContrato().setNomeArquivo(arquivo.getName());

		this.setArquivoCarregado(Boolean.TRUE);
	}

	/**
	 * Remove o arquivo de modelo de contrato do perfil de evento.
	 */
	public void removerArquivoModeloContrato() {
		this.getModeloContrato().setArquivo(null);
		this.getModeloContrato().setNomeArquivo(null);
		this.incrementarNumeroArquivos();

		this.setArquivoCarregado(Boolean.FALSE);
	}

	/**
	 * Adiciona um modelo de contrato
	 */
	public void adicionarModelo() {
		this.getPerfilEventoTO().getModelosContrato().add(this.getModeloContrato());
		this.setModeloContrato(null);
		this.incrementarNumeroArquivos();

		this.setArquivoCarregado(Boolean.FALSE);
	}

	/**
	 * Remove um modelo de contrato
	 */
	public void removerModeloContrato() {
		final ModeloContratoTO modeloContrato = (ModeloContratoTO) JSFUtilities.getRequestAttribute("modeloContrato");
		this.getPerfilEventoTO().getModelosContrato().remove(modeloContrato);

		// Caso o modelo de contrato seja persistido, marcá-lo para exclusão
		if ((modeloContrato.getCodigo() != null) && !modeloContrato.getCodigo().equals(0)) {
			this.getModelosContratoAExcluir().add(modeloContrato.getCodigo());
		}
	}

	/**
	 * Abrir para dicionar imagens
	 */
	public void abrirAdicionarImagens() {
		this.setInclusaoImagemModelo(Boolean.TRUE);
		this.setModeloAddImagens((ModeloContratoTO) JSFUtilities.getRequestAttribute("modeloContrato"));
		this.iniciarNumeroArquivos();

		this.setArquivoCarregado(Boolean.FALSE);
	}

	/**
	 * Realiza o upload de uma imagem de um modelo de contrato.
	 * 
	 * @param event
	 * @throws IOException
	 */
	public void uploadImagemListener(final UploadEvent event) throws IOException {
		// Obter o arquivo a partir do evento
		final UploadItem item = event.getUploadItem();
		final File imageUploaded = item.getFile();
		// cria um novo arquivo 
		final File arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
		// caso exista o arquivo ele é deletado
		arquivo.delete();
		final FileOutputStream out = new FileOutputStream(arquivo);
		out.write(FileUtilities.obterBytesArquivo(imageUploaded));
		// Limpa a memória assim que o arquivo e carregado
		out.flush();
		out.close();

		this.getImagem().setArquivo(arquivo);
		this.getImagem().setNomeArquivo(arquivo.getName());

		this.setArquivoCarregado(Boolean.TRUE);
	}

	/**
	 * Remove um arquivo de imagem
	 */
	public void removerArquivoImagem() {
		this.getImagem().setArquivo(null);
		this.getImagem().setNomeArquivo(null);
		this.incrementarNumeroArquivos();

		this.setArquivoCarregado(Boolean.FALSE);
	}

	/**
	 * Adiciona um arquivo de imagem
	 */
	public void adicionarImagem() {
		this.getModeloAddImagens().getImagens().add(this.getImagem());
		this.setImagem(null);
		this.incrementarNumeroArquivos();

		this.setArquivoCarregado(Boolean.FALSE);
	}

	/**
	 * Remove uma imagem
	 */
	public void removerImagem() {
		final ModeloImagemTO imagem = (ModeloImagemTO) JSFUtilities.getRequestAttribute("imagem");
		this.getModeloAddImagens().getImagens().remove(imagem);

		// Caso a imagem seja persistida, marcá-la para exclusão
		if ((imagem.getCodigo() != null) && !imagem.getCodigo().equals(0)) {
			this.getImagensAExcluir().add(imagem.getCodigo());
		}
	}

	/**
	 * Visualiza os modelos de contratos
	 */
	public void visualizarModelos() {
		this.setInclusaoImagemModelo(Boolean.FALSE);
		this.setModeloAddImagens(null);
		this.iniciarNumeroArquivos();

		this.setArquivoCarregado(Boolean.FALSE);
	}

	/* -- FIM - ABA MODELO DE CONTRATO -- */

	/* -- INÍCIO - ABA MODELO DE ORÇAMENTO -- */
	private ModeloOrcamentoTO modeloOrcamento;
	private Integer numeroArquivosOrcamento;
	private ModeloOrcamentoTO modeloOrcamentoAddImagens;
	private ModeloImagemTO imagemModeloOrcamento;
	private Boolean inclusaoImagemModeloOrcamento;
	private Boolean arquivoOrcamentoCarregado;

	private List<Integer> modelosOrcamentoAExcluir;
	private List<Integer> imagensModeloOrcamentoAExcluir;

	/**
	 * @return O campo modeloOrcamento.
	 */
	public ModeloOrcamentoTO getModeloOrcamento() {
		if (this.modeloOrcamento == null) {
			this.modeloOrcamento = new ModeloOrcamentoTO();
		}
		return this.modeloOrcamento;
	}

	/**
	 * @param modeloOrcamento
	 *            O novo valor de modeloOrcamento.
	 */
	public void setModeloOrcamento(final ModeloOrcamentoTO modeloOrcamento) {
		this.modeloOrcamento = modeloOrcamento;
	}

	/**
	 * @return O campo numeroArquivosOrcamento.
	 */
	public Integer getNumeroArquivosOrcamento() {
		if (this.numeroArquivosOrcamento == null) {
			this.iniciarNumeroArquivosOrcamento();
		}
		return this.numeroArquivosOrcamento;
	}

	/**
	 * @param numeroArquivosOrcamento
	 *            O novo valor de numeroArquivosOrcamento.
	 */
	public void setNumeroArquivosOrcamento(final Integer numeroArquivosOrcamento) {
		this.numeroArquivosOrcamento = numeroArquivosOrcamento;
	}

	/**
	 * @return the modeloOrcamentoAddImagens
	 */
	public ModeloOrcamentoTO getModeloOrcamentoAddImagens() {
		return this.modeloOrcamentoAddImagens;
	}

	/**
	 * @param modeloOrcamentoAddImagens
	 *            the modeloOrcamentoAddImagens to set
	 */
	public void setModeloOrcamentoAddImagens(final ModeloOrcamentoTO modeloOrcamentoAddImagens) {
		this.modeloOrcamentoAddImagens = modeloOrcamentoAddImagens;
	}

	/**
	 * @return the imagemModeloOrcamento
	 */
	public ModeloImagemTO getImagemModeloOrcamento() {
		if (this.imagemModeloOrcamento == null) {
			this.imagemModeloOrcamento = new ModeloImagemTO();
		}
		return this.imagemModeloOrcamento;
	}

	/**
	 * @param imagemModeloOrcamento
	 *            the imagemModeloOrcamento to set
	 */
	public void setImagemModeloOrcamento(final ModeloImagemTO imagemModeloOrcamento) {
		this.imagemModeloOrcamento = imagemModeloOrcamento;
	}

	/**
	 * @return the inclusaoImagemModeloOrcamento
	 */
	public Boolean getInclusaoImagemModeloOrcamento() {
		if (this.inclusaoImagemModeloOrcamento == null) {
			this.inclusaoImagemModeloOrcamento = Boolean.FALSE;
		}
		return this.inclusaoImagemModeloOrcamento;
	}

	/**
	 * @param inclusaoImagemModeloOrcamento
	 *            the inclusaoImagemModeloOrcamento to set
	 */
	public void setInclusaoImagemModeloOrcamento(final Boolean inclusaoImagemModeloOrcamento) {
		this.inclusaoImagemModeloOrcamento = inclusaoImagemModeloOrcamento;
	}

	/**
	 * @return O campo arquivoOrcamentoCarregado.
	 */
	public Boolean getArquivoOrcamentoCarregado() {
		if (this.arquivoOrcamentoCarregado == null) {
			this.arquivoOrcamentoCarregado = Boolean.FALSE;
		}
		return this.arquivoOrcamentoCarregado;
	}

	/**
	 * @param arquivoOrcamentoCarregado
	 *            O novo valor de arquivoOrcamentoCarregado.
	 */
	public void setArquivoOrcamentoCarregado(final Boolean arquivoOrcamentoCarregado) {
		this.arquivoOrcamentoCarregado = arquivoOrcamentoCarregado;
	}

	/**
	 * @return O campo modelosContratoAExcluir.
	 */
	public List<Integer> getModelosOrcamentoAExcluir() {
		if (this.modelosOrcamentoAExcluir == null) {
			this.modelosOrcamentoAExcluir = new ArrayList<Integer>();
		}
		return this.modelosOrcamentoAExcluir;
	}

	/**
	 * @param modelosOrcamentoAExcluir
	 *            O novo valor de modelosOrcamentoAExcluir.
	 */
	public void setModelosOrcamentoAExcluir(final List<Integer> modelosOrcamentoAExcluir) {
		this.modelosOrcamentoAExcluir = modelosOrcamentoAExcluir;
	}

	/**
	 * @return the imagensModeloOrcamentoAExcluir
	 */
	public List<Integer> getImagensModeloOrcamentoAExcluir() {
		if (this.imagensModeloOrcamentoAExcluir == null) {
			this.imagensModeloOrcamentoAExcluir = new ArrayList<Integer>();
		}
		return this.imagensModeloOrcamentoAExcluir;
	}

	/**
	 * @param imagensModeloOrcamentoAExcluir
	 *            the imagensModeloOrcamentoAExcluir to set
	 */
	public void setImagensModeloOrcamentoAExcluir(final List<Integer> imagensModeloOrcamentoAExcluir) {
		this.imagensModeloOrcamentoAExcluir = imagensModeloOrcamentoAExcluir;
	}

	public void iniciarNumeroArquivosOrcamento() {
		this.setNumeroArquivosOrcamento(1);
	}

	public void incrementarNumeroArquivosOrcamento() {
		this.numeroArquivosOrcamento++;
	}

	public void decrementarNumeroArquivosOrcamento() {
		this.numeroArquivosOrcamento--;
	}

	/**
	 * Realiza o upload do arquivo de modelo de orçamento.
	 * 
	 * @param event
	 * @throws IOException
	 */
	public void uploadModeloOrcamentoListener(final UploadEvent event) throws IOException {
		// Obter o arquivo a partir do evento
		final UploadItem item = event.getUploadItem();
		final File modelUploaded = item.getFile();
		// Cria um novo arquivo
		final File arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
		// caso o arquivo exista ele é deletado
		arquivo.delete();
		FileOutputStream out = new FileOutputStream(arquivo);
		out.write(FileUtilities.obterBytesArquivo(modelUploaded));
		// limpa a memoria após que o arquivo e carregado
		out.flush();
		out.close();

		this.getModeloOrcamento().setArquivo(arquivo);
		this.getModeloOrcamento().setNomeArquivo(arquivo.getName());

		this.setArquivoOrcamentoCarregado(Boolean.TRUE);
	}

	/**
	 * Remove o arquivo de modelo de orçamento do perfil de evento.
	 */
	public void removerArquivoModeloOrcamento() {
		this.getModeloOrcamento().setArquivo(null);
		this.getModeloOrcamento().setNomeArquivo(null);
		this.incrementarNumeroArquivosOrcamento();

		this.setArquivoOrcamentoCarregado(Boolean.FALSE);
	}

	/**
	 * Adiciona um modelo de orçamento
	 */
	public void adicionarModeloOrcamento() {
		this.getPerfilEventoTO().getModelosOrcamento().add(this.getModeloOrcamento());
		this.setModeloOrcamento(null);
		this.incrementarNumeroArquivosOrcamento();

		this.setArquivoOrcamentoCarregado(Boolean.FALSE);
	}

	/**
	 * Remove um modelo de orçamento
	 */
	public void removerModeloOrcamento() {
		ModeloOrcamentoTO modeloOrcamento = (ModeloOrcamentoTO) JSFUtilities.getRequestAttribute("modeloOrcamento");
		this.getPerfilEventoTO().getModelosOrcamento().remove(modeloOrcamento);

		// Caso o modelo de contrato seja persistido, marcá-lo para exclusão
		if ((modeloOrcamento.getCodigo() != null) && !modeloOrcamento.getCodigo().equals(0)) {
			this.getModelosOrcamentoAExcluir().add(modeloOrcamento.getCodigo());
		}
	}

	/**
	 * Abre Adiciona um modelo de orçamento
	 */
	public void abrirAdicionarImagensModeloOrcamento() {
		this.setInclusaoImagemModeloOrcamento(Boolean.TRUE);
		this.setModeloOrcamentoAddImagens((ModeloOrcamentoTO) JSFUtilities.getRequestAttribute("modeloOrcamento"));
		this.iniciarNumeroArquivosOrcamento();

		this.setArquivoOrcamentoCarregado(Boolean.FALSE);
	}

	/**
	 * Realiza o upload de uma imagem de um modelo de orçamento.
	 * 
	 * @param event
	 * @throws IOException
	 */
	public void uploadImagemModeloOrcamentoListener(final UploadEvent event) throws IOException {
		// Obter o arquivo a partir do evento
		UploadItem item = event.getUploadItem();
		File imageUploaded = item.getFile();

		// cria um novo arquivo
		File arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
		// deleta o arquivo caso ele exista
		arquivo.delete();
		FileOutputStream out = new FileOutputStream(arquivo);
		out.write(FileUtilities.obterBytesArquivo(imageUploaded));
		// limpa a memoria após que o arquivo e carregado
		out.flush();
		out.close();

		this.getImagemModeloOrcamento().setArquivo(arquivo);
		this.getImagemModeloOrcamento().setNomeArquivo(arquivo.getName());

		this.setArquivoOrcamentoCarregado(Boolean.TRUE);
	}

	/**
	 * Remove um arquivo de imagem
	 */
	public void removerArquivoImagemModeloOrcamento() {
		this.getImagemModeloOrcamento().setArquivo(null);
		this.getImagemModeloOrcamento().setNomeArquivo(null);
		this.incrementarNumeroArquivosOrcamento();

		this.setArquivoOrcamentoCarregado(Boolean.FALSE);
	}

	/**
	 * Adicioa uma imagem
	 */
	public void adicionarImagemModeloOrcamento() {
		this.getModeloOrcamentoAddImagens().getImagens().add(this.getImagemModeloOrcamento());
		this.setImagemModeloOrcamento(null);
		this.incrementarNumeroArquivosOrcamento();

		this.setArquivoOrcamentoCarregado(Boolean.FALSE);
	}

	/**
	 * Remove uma imagem
	 */
	public void removerImagemModeloOrcamento() {
		ModeloImagemTO imagem = (ModeloImagemTO) JSFUtilities.getRequestAttribute("imagemModeloOrcamento");
		this.getModeloOrcamentoAddImagens().getImagens().remove(imagem);

		// Caso a imagem seja persistida, marcá-la para exclusão
		if ((imagem.getCodigo() != null) && !imagem.getCodigo().equals(0)) {
			this.getImagensModeloOrcamentoAExcluir().add(imagem.getCodigo());
		}
	}

	/**
	 * Visualiza os modelos de orçamento
	 */
	public void visualizarModelosOrcamento() {
		this.setInclusaoImagemModeloOrcamento(Boolean.FALSE);
		this.setModeloOrcamentoAddImagens(null);
		this.iniciarNumeroArquivosOrcamento();

		this.setArquivoOrcamentoCarregado(Boolean.FALSE);
	}

	/* -- FIM - ABA MODELO DE ORÇAMENTO -- */

	/* -- INÍCIO - ABA SERVIÇOS -- */
	/**
	 * Responsável pela adição de novos serviços ao perfil de evento.
	 */
	public void adicionarPerfilEventoServico() {
		try {
			// Verificar se algum serviço foi selecionado
			if (this.getServico().getCodigoServico() == null) {
				throw new NegocioException("operacoes.adicao.erro.servicoObrigatorio");
			}

			// Verificar se o utensílio já foi adicionado
			for (PerfilEventoServicoTO perfEvServ : this.getPerfilEventoTO().getPerfilEventoServicoTOs()) {
				if (this.getServico().getCodigoServico().equals(perfEvServ.getCodigoServico())) 
						throw new NegocioException("operacoes.adicao.erro.servicoJaRelacionado");
			}

			// Adicionar o serviço à lista de serviços relacionados ao perfil do
			// evento
			if (!this.getPerfilEventoTO().getPerfilEventoServicoTOs().contains(this.getServico())) {
				this.getPerfilEventoTO().getPerfilEventoServicoTOs().add(this.getServico());
			}

			// Limpar campos de preenchimento de novo serviço
			this.setServico(new PerfilEventoServicoTO());

			// Mensagem sucesso
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Responsável pela edição de serviços relacionados ao perfil de evento.
	 */
	public void editarPerfilEventoServico() {
		// Marcar edição de serviço
		this.setEdicaoPerfilEventoServico(true);

		// Obter serviço a alterar
		this.setServicoAAlterar((PerfilEventoServicoTO) JSFUtilities.getRequestAttribute("perfilEventoServico"));

		// Preencher campos de edição
		this.copiarPerfilEventoServicoTO(this.getServicoAAlterar(), this.getServico());
		

		this.setTemServicoAnterior(Boolean.FALSE);

		for (PerfilEventoServicoTO servico : this.getPerfilEventoTO().getPerfilEventoServicoTOs()) {
			if (servico.getCodigoServico().equals(this.getServicoAAlterar().getCodigoServico())
					&& !servico.equals(this.getServicoAAlterar())) {
				this.setTemServicoAnterior(Boolean.TRUE);
				break;
			}
		}		
		
		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em algum serviço relacionado ao perfil de evento.
	 */
	public void confirmarAlteracaoPerfilEventoServico() {
		try {
			// Verificar se algum serviço foi selecionado
			if (this.getServico().getCodigoServico() == null) {
				throw new NegocioException("operacoes.adicao.erro.servicoObrigatorio");
			}

			// Verificar se o serviço já foi adicionado
			for (PerfilEventoServicoTO perfEvServ : this.getPerfilEventoTO().getPerfilEventoServicoTOs()) {
				if (this.getServico().getCodigoServico().equals(perfEvServ.getCodigoServico())
						&& !this.getServicoAAlterar().equals(perfEvServ)) 
						throw new NegocioException("operacoes.adicao.erro.servicoJaRelacionado");
				
			}


			// Efetivar alteração
			this.copiarPerfilEventoServicoTO(this.getServico(), this.getServicoAAlterar());

			// Desmarcar edição de serviço
			this.setEdicaoPerfilEventoServico(false);

			this.setServicoAAlterar(null);
			this.setServico(null);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover serviço dentre os relacionados ao perfil de evento
	 */
	public void removerPerfilEventoServico() {
		// Obter serviço a ser excluído
		PerfilEventoServicoTO obj = (PerfilEventoServicoTO) JSFUtilities.getRequestAttribute("perfilEventoServico");

		// Remover serviço
		this.getPerfilEventoTO().getPerfilEventoServicoTOs().remove(obj);


		// Caso o serviço seja persistido, marcá-lo para exclusão
		if ((obj.getCodigo() != null) && !obj.getCodigo().equals(0) && (this.getPerfilEventoTO().getCodigo() != null)
				&& (this.getPerfilEventoTO().getCodigo() != 0)) {
			this.getServicosAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Selecionar serviço dentre os consultados.
	 */
	public void selecionarServico() {
		try {
			// Limpar campos edição de serviço
			this.setServico(new PerfilEventoServicoTO());

			// Obter serviço selecionado
			ServicoTO obj = (ServicoTO) JSFUtilities.getRequestAttribute("servico");

			// Preencher campos de edição de serviço com o serviço selecionado
			this.getServico().setCodigoServico(obj.getCodigo());
			this.getServico().setDescricaoServico(obj.getDescricao());
			this.getServico().setValor(obj.getValor());
			
			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());

		} finally {
			// Limpar lista de serviços consultados
			this.getServicos().clear();

			// Limpar filtros de consulta a serviços
			this.setFiltroServicos(null);
			this.setAtencao(false);
		}
	}

	/**
	 * Responsável pela consulta a serviços.
	 */
	public void consultarServicos() {
		try {
			// Consultar serviços segundo filtro
			List<ServicoTO> objs = CEControle.getCEFacade().consultarServico(this.getFiltroServicos());
			// Set lista de resultado da consulta
			this.setServicos(objs);
			// Exibir mensagem de sucesso
			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			// Limpar lista de resultado da consulta
			this.getServicos().clear();
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		}
	}

	/* -- FIM - ABA SERVIÇOS -- */

	/* -- INÍCIO - ABA BRINQUEDOS -- */
	/**
	 * Responsável pela adição de novos brinquedos ao perfil de evento.
	 */
	public void adicionarPerfilEventoBrinquedo() {
		try {
			// Verificar se algum brinquedo foi selecionado
			if (this.getBrinquedo().getCodigoProdutoLocacao() == null) {
				throw new NegocioException("operacoes.adicao.erro.brinquedoObrigatorio");
			}

			// Verificar se o brinquedo já foi adicionado
			for (PerfilEventoProdutoLocacaoTO perfEvProd : this.getPerfilEventoTO().getPerfilEventoBrinquedos()) {
				if (this.getBrinquedo().getCodigoProdutoLocacao().equals(perfEvProd.getCodigoProdutoLocacao())) {
						throw new NegocioException("operacoes.adicao.erro.brinquedoJaRelacionado");
				
				}
			}

			// Adicionar o brinquedo à lista de brinquedos relacionados ao
			// perfil do evento
			if (!this.getPerfilEventoTO().getPerfilEventoBrinquedos().contains(this.getBrinquedo())) {
				this.getPerfilEventoTO().getPerfilEventoBrinquedos().add(this.getBrinquedo());
			}

			// Limpar campos de preenchimento de novo brinquedo
			this.setBrinquedo(null);
			this.setTemBrinquedoAnterior(Boolean.FALSE);

			// Mensagem sucesso
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Responsável pela edição de brinquedos relacionados ao perfil de evento.
	 */
	public void editarPerfilEventoBrinquedo() {
		// Marcar edição de brinquedo
		this.setEdicaoPerfilEventoBrinquedo(true);

		// Obter brinquedo a alterar
		this.setBrinquedoAAlterar((PerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("perfilEventoBrinquedo"));

		// Preencher campos de edição
		this.copiarPerfilEventoProdutoLocacaoTO(this.getBrinquedoAAlterar(), this.getBrinquedo());

		this.setTemBrinquedoAnterior(Boolean.FALSE);

		for (PerfilEventoProdutoLocacaoTO brinquedo : this.getPerfilEventoTO().getPerfilEventoBrinquedos()) {
			if (brinquedo.getCodigoProdutoLocacao().equals(this.getBrinquedoAAlterar().getCodigoProdutoLocacao())
					&& !brinquedo.equals(this.getBrinquedoAAlterar())) {
				this.setTemBrinquedoAnterior(Boolean.TRUE);
				break;
			}
		}

		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em algum brinquedo relacionado ao perfil de evento.
	 */
	public void confirmarAlteracaoPerfilEventoBrinquedo() {
		try {
			// Verificar se algum brinquedo foi selecionado
			if (this.getBrinquedo().getCodigoProdutoLocacao() == null) {
				throw new NegocioException("operacoes.adicao.erro.brinquedoObrigatorio");
			}

			// Verificar se o brinquedo já foi adicionado
			for (PerfilEventoProdutoLocacaoTO perfEvProd : this.getPerfilEventoTO().getPerfilEventoBrinquedos()) {
				if (this.getBrinquedo().getCodigoProdutoLocacao().equals(perfEvProd.getCodigoProdutoLocacao())
						&& !this.getBrinquedoAAlterar().equals(perfEvProd)) 
						throw new NegocioException("operacoes.adicao.erro.brinquedoJaRelacionado");
			}


			// Efetivar alteração
			this.copiarPerfilEventoProdutoLocacaoTO(this.getBrinquedo(), this.getBrinquedoAAlterar());

			// Desmarcar edição de brinquedo
			this.setEdicaoPerfilEventoBrinquedo(false);

			this.setBrinquedoAAlterar(null);
			this.setBrinquedo(null);
			this.setTemBrinquedoAnterior(Boolean.FALSE);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover brinquedo dentre os relacionados ao perfil de evento
	 */
	public void removerPerfilEventoBrinquedo() {
		// Obter brinquedo a ser excluído
		PerfilEventoProdutoLocacaoTO obj = (PerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("perfilEventoBrinquedo");

		// Remover brinquedo
		this.getPerfilEventoTO().getPerfilEventoBrinquedos().remove(obj);

		// Caso o brinquedo seja persistido, marcá-lo para exclusão
		if ((obj.getCodigo() != null) && !obj.getCodigo().equals(0) && (this.getPerfilEventoTO().getCodigo() != null)
				&& (this.getPerfilEventoTO().getCodigo() != 0)) {
			this.getProdutosLocacaoAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Selecionar brinquedo dentre os consultados.
	 */
	public void selecionarBrinquedo() {
		try {
			// Limpar campos edição de brinquedo
			this.setBrinquedo(new PerfilEventoProdutoLocacaoTO());

			// Obter brinquedo selecionado
			ProdutoLocacaoTO obj = (ProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo");

			// Preencher campos de edição de brinquedo com o brinquedo
			// selecionado
			this.getBrinquedo().setCodigoProdutoLocacao(obj.getCodigo());
			this.getBrinquedo().setDescricaoProdutoLocacao(obj.getDescricao());
			this.getBrinquedo().setValor(obj.getValor());
			

			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		} finally {
			// Limpar lista de brinquedos consultados
			this.getBrinquedos().clear();

			// Limpar filtros de consulta a brinquedos
			this.setFiltroBrinquedos(null);
			this.setAtencao(false);
		}
	}

	/**
	 * Responsável pela consulta a brinquedos.
	 */
	public void consultarBrinquedos() {
		try {
			// Consultar brinquedos segundo filtro
			List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getFiltroBrinquedos());
			// Set lista de resultado da consulta
			this.setBrinquedos(objs);
			// Exibir mensagem de sucesso
			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			// Limpar lista de resultado da consulta
			this.getBrinquedos().clear();
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		}
	}

	/* -- FIM - ABA BRINQUEDOS -- */

	/* -- INÍCIO - ABA UTENSÍLIOS -- */
	/**
	 * Responsável pela adição de novos utensílios ao perfil de evento.
	 */
	public void adicionarPerfilEventoUtensilio() {
		try {
			// Verificar se algum utensílio foi selecionado
			if (this.getUtensilio().getCodigoProdutoLocacao() == null) {
				throw new NegocioException("operacoes.adicao.erro.utensilioObrigatorio");
			}

			// Verificar se o utensílio já foi adicionado
			for (PerfilEventoProdutoLocacaoTO perfEvProd : this.getPerfilEventoTO().getPerfilEventoUtensilios()) {
				if (this.getUtensilio().getCodigoProdutoLocacao().equals(perfEvProd.getCodigoProdutoLocacao())) 
						throw new NegocioException("operacoes.adicao.erro.utensilioJaRelacionado");
			}

			// Adicionar o utensílio à lista de utensílios relacionados ao
			// perfil do evento
			if (!this.getPerfilEventoTO().getPerfilEventoUtensilios().contains(this.getUtensilio())) {
				this.getPerfilEventoTO().getPerfilEventoUtensilios().add(this.getUtensilio());
			}

			// Limpar campos de preenchimento de novo utensílio
			this.setUtensilio(null);
			this.setTemUtensilioAnterior(Boolean.FALSE);

			// Mensagem sucesso
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Responsável pela edição de utensílios relacionados ao perfil de evento.
	 */
	public void editarPerfilEventoUtensilio() {
		// Marcar edição de utensílio
		this.setEdicaoPerfilEventoUtensilio(true);

		// Obter utensílio a alterar
		this.setUtensilioAAlterar((PerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("perfilEventoUtensilio"));

		// Preencher campos de edição
		this.copiarPerfilEventoProdutoLocacaoTO(this.getUtensilioAAlterar(), this.getUtensilio());

		this.setTemUtensilioAnterior(Boolean.FALSE);

		for (PerfilEventoProdutoLocacaoTO utensilio : this.getPerfilEventoTO().getPerfilEventoUtensilios()) {
			if (utensilio.getCodigoProdutoLocacao().equals(this.getUtensilioAAlterar().getCodigoProdutoLocacao())
					&& !utensilio.equals(this.getUtensilioAAlterar())) {
				this.setTemUtensilioAnterior(Boolean.TRUE);
				break;
			}
		}

		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em algum utensílio relacionado ao perfil de evento.
	 */
	public void confirmarAlteracaoPerfilEventoUtensilio() {
		try {
			// Verificar se algum utensílio foi selecionado
			if (this.getUtensilio().getCodigoProdutoLocacao() == null) {
				throw new NegocioException("operacoes.adicao.erro.utensilioObrigatorio");
			}

			// Verificar se o utensílio já foi adicionado
			for (PerfilEventoProdutoLocacaoTO perfEvProd : this.getPerfilEventoTO().getPerfilEventoUtensilios()) {
				if (this.getUtensilio().getCodigoProdutoLocacao().equals(perfEvProd.getCodigoProdutoLocacao())
						&& !this.getUtensilioAAlterar().equals(perfEvProd)) 
						throw new NegocioException("operacoes.adicao.erro.utensilioJaRelacionado");
			}


			// Efetivar alteração
			this.copiarPerfilEventoProdutoLocacaoTO(this.getUtensilio(), this.getUtensilioAAlterar());

			// Desmarcar edição de utensílio
			this.setEdicaoPerfilEventoUtensilio(false);

			this.setUtensilioAAlterar(null);
			this.setUtensilio(null);
			this.setTemUtensilioAnterior(Boolean.FALSE);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover utensílio dentre os relacionados ao perfil de evento
	 */
	public void removerPerfilEventoUtensilio() {
		// Obter utensílio a ser excluído
		PerfilEventoProdutoLocacaoTO obj = (PerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("perfilEventoUtensilio");

		// Remover utensílio
		this.getPerfilEventoTO().getPerfilEventoUtensilios().remove(obj);
		

		// Caso o utensílio seja persistido, marcá-lo para exclusão
		if ((obj.getCodigo() != null) && !obj.getCodigo().equals(0) && (this.getPerfilEventoTO().getCodigo() != null)
				&& (this.getPerfilEventoTO().getCodigo() != 0)) {
			this.getProdutosLocacaoAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Selecionar utensílio dentre os consultados.
	 */
	public void selecionarUtensilio() {
		try {
			// Limpar campos edição de utensílio
			this.setUtensilio(new PerfilEventoProdutoLocacaoTO());

			// Obter utensílio selecionado
			ProdutoLocacaoTO obj = (ProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio");

			// Preencher campos de edição de utensílio com o utensílio
			// selecionado
			this.getUtensilio().setCodigoProdutoLocacao(obj.getCodigo());
			this.getUtensilio().setDescricaoProdutoLocacao(obj.getDescricao());
			this.getUtensilio().setValor(obj.getValor());

			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());

		} finally {
			// Limpar lista de utensílios consultados
			this.getUtensilios().clear();

			// Limpar filtros de consulta a utensílios
			this.setFiltroUtensilios(null);
			this.setAtencao(false);
		}
	}

	/**
	 * Responsável pela consulta a utensílios.
	 */
	public void consultarUtensilios() {
		try {
			// Consultar utensílios segundo filtro
			List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getFiltroUtensilios());
			// Set lista de resultado da consulta
			this.setUtensilios(objs);
			// Exibir mensagem de sucesso
			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			// Limpar lista de resultado da consulta
			this.getUtensilios().clear();
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		}
	}

	/* -- FIM - ABA UTENSÍLIOS -- */

	/* -- INÍCIO - ABA BENS CONSUMO -- */
	/**
	 * Responsável pela adição de novos bens de consumo ao perfil de evento.
	 */
	public void adicionarPerfilEventoBemConsumo() {
		try {
			// Verificar se algum bem de consumo foi selecionado
			if (this.getBemConsumo().getCodigoProdutoLocacao() == null) {
				throw new NegocioException("operacoes.adicao.erro.bemConsumoObrigatorio");
			}
			
			// Verificar se o bem de consumo já foi adicionado
			for (PerfilEventoProdutoLocacaoTO perfEvProd : this.getPerfilEventoTO().getPerfilEventoBensConsumo()) {
				if (this.getBemConsumo().getCodigoProdutoLocacao().equals(perfEvProd.getCodigoProdutoLocacao())) 
						throw new NegocioException("operacoes.adicao.erro.bemConsumoJaRelacionado");
			}

			// Adicionar o bem de consumo à lista de bens de consumo
			// relacionados ao perfil do evento
			if (!this.getPerfilEventoTO().getPerfilEventoBensConsumo().contains(this.getBemConsumo())) {
				this.getPerfilEventoTO().getPerfilEventoBensConsumo().add(this.getBemConsumo());
			}

			// Limpar campos de preenchimento de novo bem de consumo
			this.setBemConsumo(null);
			this.setTemBemConsumoAnterior(Boolean.FALSE);

			// Mensagem sucesso
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Responsável pela edição de bens de consumo relacionados ao perfil de evento.
	 */
	public void editarPerfilEventoBemConsumo() {
		// Marcar edição de bem de consumo
		this.setEdicaoPerfilEventoBemConsumo(true);

		// Obter bem de consumo a alterar
		this.setBemConsumoAAlterar((PerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("perfilEventoBemConsumo"));

		// Preencher campos de edição
		this.copiarPerfilEventoProdutoLocacaoTO(this.getBemConsumoAAlterar(), this.getBemConsumo());

		this.setTemBemConsumoAnterior(Boolean.FALSE);

		for (PerfilEventoProdutoLocacaoTO bemConsumo : this.getPerfilEventoTO().getPerfilEventoBensConsumo()) {
			if (bemConsumo.getCodigoProdutoLocacao().equals(this.getBemConsumoAAlterar().getCodigoProdutoLocacao())
					&& !bemConsumo.equals(this.getBemConsumoAAlterar())) {
				this.setTemBemConsumoAnterior(Boolean.TRUE);
				break;
			}
		}

		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em algum bem de consumo relacionado ao perfil de evento.
	 */
	public void confirmarAlteracaoPerfilEventoBemConsumo() {
		try {
			// Verificar se algum bem de consumo foi selecionado
			if (this.getBemConsumo().getCodigoProdutoLocacao() == null) {
				throw new NegocioException("operacoes.adicao.erro.bemConsumoObrigatorio");
			}


			// Verificar se o bem de consumo já foi adicionado
			for (PerfilEventoProdutoLocacaoTO perfEvProd : this.getPerfilEventoTO().getPerfilEventoBensConsumo()) {
				if (this.getBemConsumo().getCodigoProdutoLocacao().equals(perfEvProd.getCodigoProdutoLocacao())
						&& !this.getBemConsumoAAlterar().equals(perfEvProd)) 
						throw new NegocioException("operacoes.adicao.erro.bemConsumoJaRelacionado");
			}


			// Efetivar alteração
			this.copiarPerfilEventoProdutoLocacaoTO(this.getBemConsumo(), this.getBemConsumoAAlterar());

			// Desmarcar edição de bem de consumo
			this.setEdicaoPerfilEventoBemConsumo(false);

			this.setBemConsumoAAlterar(null);
			this.setBemConsumo(null);
			this.setTemBemConsumoAnterior(Boolean.FALSE);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover bem de consumo dentre os relacionados ao perfil de evento
	 */
	public void removerPerfilEventoBemConsumo() {
		// Obter bem de consumo a ser excluído
		PerfilEventoProdutoLocacaoTO obj = (PerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("perfilEventoBemConsumo");

		// Remover bem de consumo
		this.getPerfilEventoTO().getPerfilEventoBensConsumo().remove(obj);

		
		// Caso o bem de consumo seja persistido, marcá-lo para exclusão
		if ((obj.getCodigo() != null) && !obj.getCodigo().equals(0) && (this.getPerfilEventoTO().getCodigo() != null)
				&& (this.getPerfilEventoTO().getCodigo() != 0)) {
			this.getProdutosLocacaoAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Selecionar bem de consumo dentre os consultados.
	 */
	public void selecionarBemConsumo() {
		try {
			// Limpar campos de edição de bem de consumo
			this.setBemConsumo(new PerfilEventoProdutoLocacaoTO());

			// Obter bem de consumo selecionado
			ProdutoLocacaoTO obj = (ProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo");

			// Preencher campos de edição de bem de consumo com o bem de consumo
			// selecionado
			this.getBemConsumo().setCodigoProdutoLocacao(obj.getCodigo());
			this.getBemConsumo().setDescricaoProdutoLocacao(obj.getDescricao());
			this.getBemConsumo().setValor(obj.getValor());

			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		} finally {
			// Limpar lista de bens de consumo consultados
			this.getBensConsumo().clear();

			// Limpar filtros de consulta a bens de consumo
			this.setFiltroBensConsumo(null);
			this.setAtencao(false);
		}
	}

	/**
	 * Responsável pela consulta a bens de consumo.
	 */
	public void consultarBensConsumo() {
		try {
			// Consultar bens de consumo segundo filtro
			List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getFiltroBensConsumo());
			// Set lista de resultado da consulta
			this.setBensConsumo(objs);
			// Exibir mensagem de sucesso
			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			// Limpar lista de resultado da consulta
			this.getBensConsumo().clear();
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		}
	}

	/* -- FIM - ABA BENS CONSUMO -- */

	/* -- INÍCIO - ABA AMBIENTES -- */

	/**
	 * Abre a visualização de layouts para um determinado ambiente.
	 */
	public void visualizarLayouts() {
		// Obter o ambiente selecionado
		PerfilEventoAmbienteTO perfEvAmb = (PerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("perfilEventoAmbiente");
		this.setCodigoAmbiente(perfEvAmb.getCodigoAmbiente());
		this.setDescricaoAmbiente(perfEvAmb.getDescricaoAmbiente());
		// Obter os layouts relacionados ao ambiente selecionado
		this.setLayouts(this.getMapLayouts().get(this.getCodigoAmbiente()));
		// Limpar campos de edição de layout
		this.setLayout(null);

		this.setGuiaVisualizada(3);
	}

	/**
	 * Fecha a visualização de layouts para um determinado ambiente.
	 */
	public void fecharVisualizacaoLayouts() {
		// Limpar a lista de layouts exibidos
		this.setLayouts(null);
		// Limpar os campos de edição de layout
		this.setCodigoAmbiente(null);
		this.setDescricaoAmbiente(null);
		this.setLayout(null);

		this.setGuiaVisualizada(1);
	}

	private PerfilEventoAmbienteLayoutTO layout;
	private Integer numeroArquivosLayout;
	private Boolean arquivoLayoutCarregado;

	private List<PerfilEventoAmbienteLayoutTO> layouts;
	private Map<Integer, List<PerfilEventoAmbienteLayoutTO>> mapLayouts;
	private List<Integer> layoutsAExcluir;

	/**
	 * @return O campo modeloOrcamento.
	 */
	public PerfilEventoAmbienteLayoutTO getLayout() {
		if (this.layout == null) {
			this.layout = new PerfilEventoAmbienteLayoutTO();
		}
		return this.layout;
	}

	/**
	 * @param layout
	 *            O novo valor de layout.
	 */
	public void setLayout(final PerfilEventoAmbienteLayoutTO layout) {
		this.layout = layout;
	}

	/**
	 * @return O campo numeroArquivosLayout.
	 */
	public Integer getNumeroArquivosLayout() {
		if (this.numeroArquivosLayout == null) {
			this.iniciarNumeroArquivosLayout();
		}
		return this.numeroArquivosLayout;
	}

	/**
	 * @param numeroArquivosLayout
	 *            O novo valor de numeroArquivosLayout.
	 */
	public void setNumeroArquivosLayout(final Integer numeroArquivosLayout) {
		this.numeroArquivosLayout = numeroArquivosLayout;
	}

	/**
	 * @return O campo arquivoLayoutCarregado.
	 */
	public Boolean getArquivoLayoutCarregado() {
		if (this.arquivoLayoutCarregado == null) {
			this.arquivoLayoutCarregado = Boolean.FALSE;
		}
		return this.arquivoLayoutCarregado;
	}

	/**
	 * @param arquivoLayoutCarregado
	 *            O novo valor de arquivoLayoutCarregado.
	 */
	public void setArquivoLayoutCarregado(final Boolean arquivoLayoutCarregado) {
		this.arquivoLayoutCarregado = arquivoLayoutCarregado;
	}

	/**
	 * @return O campo layouts.
	 */
	public List<PerfilEventoAmbienteLayoutTO> getLayouts() {
		if (this.layouts == null) {
			this.layouts = new ArrayList<PerfilEventoAmbienteLayoutTO>();
		}
		return this.layouts;
	}

	/**
	 * @param layouts
	 *            O novo valor de layouts.
	 */
	public void setLayouts(final List<PerfilEventoAmbienteLayoutTO> layouts) {
		this.layouts = layouts;
	}

	/**
	 * @return O campo mapLayoutsTO.
	 */
	public Map<Integer, List<PerfilEventoAmbienteLayoutTO>> getMapLayouts() {
		if (this.mapLayouts == null) {
			this.mapLayouts = new HashMap<Integer, List<PerfilEventoAmbienteLayoutTO>>();
		}
		return this.mapLayouts;
	}

	/**
	 * @param mapLayouts
	 *            O novo valor de mapLayoutsTO.
	 */
	public void setMapLayouts(final Map<Integer, List<PerfilEventoAmbienteLayoutTO>> mapLayouts) {
		this.mapLayouts = mapLayouts;
	}

	/**
	 * @return O campo layoutsAExcluir.
	 */
	public List<Integer> getLayoutsAExcluir() {
		if (this.layoutsAExcluir == null) {
			this.layoutsAExcluir = new ArrayList<Integer>();
		}
		return this.layoutsAExcluir;
	}

	/**
	 * @param layoutsAExcluir
	 *            O novo valor de layoutsAExcluir.
	 */
	public void setLayoutsAExcluir(final List<Integer> layoutsAExcluir) {
		this.layoutsAExcluir = layoutsAExcluir;
	}

	public void iniciarNumeroArquivosLayout() {
		this.setNumeroArquivosLayout(1);
	}

	public void incrementarNumeroArquivosLayout() {
		this.numeroArquivosLayout++;
	}

	public void decrementarNumeroArquivosLayout() {
		this.numeroArquivosLayout--;
	}

	/**
	 * Realiza o upload do arquivo de modelo de orçamento.
	 * 
	 * @param event
	 * @throws IOException
	 */
	public void uploadLayoutListener(final UploadEvent event) throws IOException {
		// Obter o arquivo a partir do evento
		UploadItem item = event.getUploadItem();
		File modelUploaded = item.getFile();

		// cria um novo arquivo
		File arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
		// deleta o arquivo caso ele exista
		arquivo.delete();
		FileOutputStream out = new FileOutputStream(arquivo);
		out.write(FileUtilities.obterBytesArquivo(modelUploaded));
		// limpa a memoria após que o arquivo é carregado
		out.flush();
		out.close();

		this.getLayout().setArquivo(arquivo);
		this.getLayout().setNomeArquivo(arquivo.getName());

		this.setArquivoLayoutCarregado(Boolean.TRUE);
	}

	/**
	 * Remove o arquivo de modelo de orçamento do perfil de evento.
	 */
	public void removerArquivoLayout() {
		this.getLayout().setArquivo(null);
		this.getLayout().setNomeArquivo(null);
		this.incrementarNumeroArquivosLayout();

		this.setArquivoLayoutCarregado(Boolean.FALSE);
	}

	/**
	 * Adiciona um layout
	 */
	public void adicionarLayout() {
		this.setLayouts(this.getMapLayouts().get(this.getCodigoAmbiente()));
		// Verificar se a lista de layouts para um ambiente está vazia
		if (this.getLayouts().isEmpty()) {
			// Caso sim: inicializar a lista de layouts para o ambiente
			// selecionado
			this.getMapLayouts().put(this.getCodigoAmbiente(), this.getLayouts());
		}

		// Adicionar o layout à lista de layouts relacionados ao ambiente
		this.getLayouts().add(this.getLayout());

		// Limpar campos de preenchimento de novo layout
		this.setLayout(null);

		this.incrementarNumeroArquivosLayout();

		this.setArquivoLayoutCarregado(Boolean.FALSE);
	}

	/**
	 * Remove um layout
	 */
	public void removerLayout() {
		// Obter o layout a ser excluído
		PerfilEventoAmbienteLayoutTO layout = (PerfilEventoAmbienteLayoutTO) JSFUtilities.getRequestAttribute("layout");

		// Remover layout
		this.getMapLayouts().get(this.getCodigoAmbiente()).remove(layout);

		// Caso o layout seja persistido, marcá-lo para exclusão
		if ((layout.getCodigo() != null) && !layout.getCodigo().equals(0)) {
			this.getLayoutsAExcluir().add(layout.getCodigo());
		}
	}

	private File imagemLayout;

	/**
	 * @return the imagemLayout
	 */
	public File getImagemLayout() {
		return this.imagemLayout;
	}

	/**
	 * @param imagemLayout
	 *            the imagemLayout to set
	 */
	public void setImagemLayout(final File imagemLayout) {
		this.imagemLayout = imagemLayout;
	}

	/**
	 * Exibe a imagem do layout
	 */
	public void exibirImagemLayout() {
		// Obter o layout a ser excluído
		PerfilEventoAmbienteLayoutTO layout = (PerfilEventoAmbienteLayoutTO) JSFUtilities.getRequestAttribute("layout");
		JSFUtilities.storeOnSession("imagem", layout.getArquivo());
		this.exibicao("modal", true);

	}

	/**
	 * Abre a visualização de sazonalidades para um determinado ambiente.
	 */
	public void visualizarPerfilEventoSazonalidade() {
		// Obter o ambiente selecionado
		PerfilEventoAmbienteTO perfEvAmb = (PerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("perfilEventoAmbiente");
		this.setCodigoAmbiente(perfEvAmb.getCodigoAmbiente());
		this.setDescricaoAmbiente(perfEvAmb.getDescricaoAmbiente());
		this.setValorAmbiente(perfEvAmb.getValor());
		// Obter as sazonalidades relacionadas ao ambiente selecionado
		this.setPerfilEventoSazonalidadeTOs(this.getMapPerfilEventoSazonalidadeTO().get(this.getCodigoAmbiente()));
		// Limpar campos de edição de sazonalidade
		this.setPerfilEventoSazonalidadeTO(new PerfilEventoSazonalidadeTO());

		this.setGuiaVisualizada(2);
	}

	/**
	 * Fecha a visualização de sazonalidades para um determinado ambiente.
	 */
	public void fecharVisualizacaoPerfilEventoSazonalidade() {
		// Limpar a lista de sazonalidades exibidas
		this.setPerfilEventoSazonalidadeTOs(null);
		// Limpar os campos de edição de sazonalidade
		this.setCodigoAmbiente(null);
		this.setDescricaoAmbiente(null);
		this.setPerfilEventoSazonalidadeTO(new PerfilEventoSazonalidadeTO());
		this.setExibeCalculo("");
		this.setGuiaVisualizada(1);
		limparMsg();
		setErro(false);
		setSucesso(false);
	}

	/**
	 * Responsável pela adição de novas sazonalidades ao ambiente.
	 */
	public void adicionarPerfilEventoSazonalidade() {

		try {
			if (!CEControle.getCEFacade().validarValores(this.getPerfilEventoSazonalidadeTO(), this.getValorAmbiente())) {
				throw new NegocioException("operacoes.adicao.erro.valorSazonalidade");
			}
			this.setPerfilEventoSazonalidadeTOs(this.getMapPerfilEventoSazonalidadeTO().get(this.getCodigoAmbiente()));
			// Verificar se a lista de sazonalidades para um ambiente está vazia
			if (this.getPerfilEventoSazonalidadeTOs().isEmpty()) {
				// Caso sim: inicializar a lista de sazonalidades para o
				// ambiente selecionado
				this.getMapPerfilEventoSazonalidadeTO().put(this.getCodigoAmbiente(), this.getPerfilEventoSazonalidadeTOs());
			} else {
				// Caso não: verificar se a sazonalidade já foi adicionada
				for (PerfilEventoSazonalidadeTO perfEvSaz : this.getPerfilEventoSazonalidadeTOs()) {
					CEControle.getCEFacade().validarDatas(this.getPerfilEventoSazonalidadeTO(), perfEvSaz);
				}
			}

			// Adicionar a sazonalidade à lista de sazonalidades relacionadas ao
			// ambiente			
			if (!this.getPerfilEventoSazonalidadeTOs().contains(this.getPerfilEventoSazonalidadeTO())) {
				if(this.getPerfilEventoSazonalidadeTO().getSeg()){
					inserirSazonalidade(DiaSemana.SEGUNDA_FEIRA);	
				}
				if(this.getPerfilEventoSazonalidadeTO().getTer()){
					inserirSazonalidade(DiaSemana.TERCA_FEIRA);	
				}
				if(this.getPerfilEventoSazonalidadeTO().getQua()){
					inserirSazonalidade(DiaSemana.QUARTA_FEIRA);	
				}
				if(this.getPerfilEventoSazonalidadeTO().getQui()){
					inserirSazonalidade(DiaSemana.QUINTA_FEIRA);	
				}
				if(this.getPerfilEventoSazonalidadeTO().getSex()){
					inserirSazonalidade(DiaSemana.SEXTA_FEIRA);	
				}
				if(this.getPerfilEventoSazonalidadeTO().getSab()){
					inserirSazonalidade(DiaSemana.SABADO);	
				}
				if(this.getPerfilEventoSazonalidadeTO().getDom()){
					inserirSazonalidade(DiaSemana.DOMINGO);	
				}
			}

			// Limpar campos de preenchimento de nova sazonalidade
			this.setPerfilEventoSazonalidadeTO(new PerfilEventoSazonalidadeTO());
			this.setExibeCalculo("");
			// Mensagem sucesso
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	private void inserirSazonalidade(DiaSemana dia)
			throws CloneNotSupportedException {
		PerfilEventoSazonalidadeTO obj = this.getPerfilEventoSazonalidadeTO().clone();
		obj.setDiaSemana(dia.getCodigo());
		this.getPerfilEventoSazonalidadeTOs().add(obj);
	}

	/**
	 * Responsável pela edição de sazonalidades relacionadas ao ambiente.
	 */
	public void editarPerfilEventoSazonalidade() {
		// Marcar edição de sazonalidade
		this.setEdicaoPerfilEventoSazonalidade(true);

		// Obter sazonalidade a alterar
		this.setSazonalidadeAAlterar((PerfilEventoSazonalidadeTO) JSFUtilities.getRequestAttribute("perfilEventoSazonalidade"));

		// Preencher campos de edição
		this.copiarPerfilEventoSazonalidadeTO(this.getSazonalidadeAAlterar(), this.getPerfilEventoSazonalidadeTO());

		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em alguma sazonalidade relacionada ao ambiente.
	 */
	public void confirmarAlteracaoPerfilEventoSazonalidade() {
		try {
			this.setPerfilEventoSazonalidadeTOs(this.getMapPerfilEventoSazonalidadeTO().get(this.getCodigoAmbiente()));

			// Verificar se a sazonalidade já foi adicionada
			for (PerfilEventoSazonalidadeTO perfEvSaz : this.getPerfilEventoSazonalidadeTOs()) {
				if (this.getPerfilEventoSazonalidadeTO().getDataInicio().equals(perfEvSaz.getDataInicio())
						&& this.getPerfilEventoSazonalidadeTO().getDataFim().equals(perfEvSaz.getDataFim())
						&& this.getPerfilEventoSazonalidadeTO().getDiaSemana().equals(perfEvSaz.getDiaSemana())
						&& (UteisValidacao.emptyNumber(this.getPerfilEventoSazonalidadeTO().getCodigo())
								|| 
						!this.getPerfilEventoSazonalidadeTO().getCodigo().equals(perfEvSaz.getCodigo()))) {
					throw new NegocioException("operacoes.adicao.erro.sazonalidadeJaRelacionada");
				}
			}

			// Efetivar alteração
			this.copiarPerfilEventoSazonalidadeTO(this.getPerfilEventoSazonalidadeTO(), this.getSazonalidadeAAlterar());

			// Desmarcar edição de sazonalidade
			this.setEdicaoPerfilEventoSazonalidade(false);

			this.setSazonalidadeAAlterar(null);
			this.setPerfilEventoSazonalidadeTO(null);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover sazonalidade dentre as relacionadas ao ambiente.
	 */
	public void removerPerfilEventoSazonalidade() {
		// Obter a sazonalidade a ser excluída
		PerfilEventoSazonalidadeTO obj = (PerfilEventoSazonalidadeTO) JSFUtilities.getRequestAttribute("perfilEventoSazonalidade");

		// Remover sazonalidade
		this.getMapPerfilEventoSazonalidadeTO().get(this.getCodigoAmbiente()).remove(obj);

		// Caso a sazonalidade seja persistida, marcá-la para exclusão
		if ((obj.getCodigo() != null) && !obj.getCodigo().equals(0) && (this.getPerfilEventoTO().getCodigo() != null)
				&& (this.getPerfilEventoTO().getCodigo() != 0)) {
			this.getSazonalidadesAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Responsável pela adição de novos ambientes ao perfil de evento.
	 */
	public void adicionarPerfilEventoAmbiente() {
		try {
			// Obter ambiente selecionado
			AmbienteVO amb = CEControle.getCEFacade().consultarAmbientePorCodigo(this.getPerfilEventoAmbienteTO().getCodigoAmbiente());

			this.getPerfilEventoAmbienteTO().setDescricaoAmbiente(amb.getDescricao());

			// Verificar se o ambiente já foi adicionado
			for (PerfilEventoAmbienteTO perfEvAmb : this.getPerfilEventoTO().getPerfilEventoAmbienteTOs()) {
				if (this.getPerfilEventoAmbienteTO().getCodigoAmbiente().equals(perfEvAmb.getCodigoAmbiente())
						&& this.getPerfilEventoAmbienteTO().getNrMaximoConvidados().equals(perfEvAmb.getNrMaximoConvidados())) {
					throw new NegocioException("operacoes.adicao.erro.ambienteJaRelacionado");
				}
			}

			// Adicionar o ambiente à lista de ambientes relacionados ao perfil
			// do evento
			if (!this.getPerfilEventoTO().getPerfilEventoAmbienteTOs().contains(this.getPerfilEventoAmbienteTO())) {
				this.getPerfilEventoTO().getPerfilEventoAmbienteTOs().add(this.getPerfilEventoAmbienteTO());
			}

			// Limpar campos de preenchimento de novo ambiente
			this.setPerfilEventoAmbienteTO(new PerfilEventoAmbienteTO());

			// Mensagem sucesso
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID(e.getMessage());
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Responsável pela edição ambientes relacionados ao perfil de evento.
	 */
	public void editarPerfilEventoAmbiente() {
		// Marcar edição de ambiente
		this.setEdicaoPerfilEventoAmbiente(true);

		// Obter ambiente a alterar
		this.setAmbienteAAlterar((PerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("perfilEventoAmbiente"));

		// Preencher campos de edição
		this.copiarPerfilEventoAmbienteTO(this.getAmbienteAAlterar(), this.getPerfilEventoAmbienteTO());

		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em algum ambiente relacionado ao perfil de evento.
	 */
	public void confirmarAlteracaoPerfilEventoAmbiente() {
		try {
			// Verificar se o ambiente já foi adicionado
			for (PerfilEventoAmbienteTO perfEvAmb : this.getPerfilEventoTO().getPerfilEventoAmbienteTOs()) {
				if (this.getPerfilEventoAmbienteTO().getCodigoAmbiente().equals(perfEvAmb.getCodigoAmbiente())
						&& !this.getAmbienteAAlterar().equals(perfEvAmb)) {
					throw new NegocioException("operacoes.adicao.erro.ambienteJaRelacionado");
				}
			}

			// Efetivar alteração
			this.copiarPerfilEventoAmbienteTO(this.getPerfilEventoAmbienteTO(), this.getAmbienteAAlterar());

			// Desmarcar edição de ambiente
			this.setEdicaoPerfilEventoAmbiente(false);

			this.setAmbienteAAlterar(null);
			this.setPerfilEventoAmbienteTO(null);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover ambiente dentre os relacionados ao perfil de evento
	 */
	public void removerPerfilEventoAmbiente() {
		// Obter ambiente a ser excluído
		PerfilEventoAmbienteTO obj = (PerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("perfilEventoAmbiente");

		// Remover ambiente
		this.getPerfilEventoTO().getPerfilEventoAmbienteTOs().remove(obj);

		// Caso o ambiente seja persistido, marcá-lo para exclusão
		if ((obj.getCodigo() != null) && !obj.getCodigo().equals(0) && (this.getPerfilEventoTO().getCodigo() != null)
				&& (this.getPerfilEventoTO().getCodigo() != 0)) {
			this.getAmbientesAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/* -- FIM - ABA AMBIENTES -- */

	/* -- INÍCIO - ABA DADOS BÁSICOS -- */

	/**
	 * Selecionar produto dentre os consultados.
	 */
	public void selecionarProduto() {
		try {
			ProdutoVO obj = (ProdutoVO) JSFUtilities.getRequestAttribute("produto");

			this.getPerfilEventoTO().getProduto().setCodigoProduto(obj.getCodigo());
			this.getPerfilEventoTO().getProduto().setDescricaoProduto(obj.getDescricao());

			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());

		} finally {
			// Limpar lista de produtos consultados
			this.getProdutos().clear();

			// Limpar filtros de consulta a produtos
			this.setFiltroProdutos(null);
			this.setAtencao(false);
		}
	}

	/**
	 * Responsável pela consulta a serviços.
	 */
	public void consultarProdutos() {
		try {
			// Consultar produtos segundo filtro
			List<ProdutoVO> objs = CEControle.getCEFacade().consultarProdutoPorDescricao(this.getFiltroProdutos().getDescricao());
			// Set lista de resultado da consulta
			this.setProdutos(objs);
			// Exibir mensagem de sucesso
			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);

		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			// Limpar lista de resultado da consulta
			this.getProdutos().clear();
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
		}
	}

	/* -- FIM - ABA DADOS BÁSICOS -- */

	/**
	 * Consulta os perfis de evento existentes de acordo com o filtro preenchido.
	 */
	@SuppressWarnings("unchecked")
	public String consultar() {
		try {
			this.verificarAutorizacao();
			super.consultar();
			// Obter o filtro preenchido pelo usuário
			List<PerfilEventoTO> objs = CEControle.getCEFacade().consultarPerfilEvento(this.getPerfilEventoTO());
                        // Exibir resultado
			this.setResultadoConsulta(objs);

			// Mensagem de sucesso
			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);
			return "resultadoConsulta";
		} catch (Exception e) {
			// Limpar resultado da consulta
			this.getResultadoConsulta().clear();

			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setErro(true);
			return "resultadoConsulta";
		}
	}

	/**
	 * Copia um objeto <code>PerfilEventoAmbienteTO</code>.
	 * 
	 * @param original
	 *            Objeto cujas propriedades serão copiadas para um novo objeto.
	 * @param clone
	 *            Novo objeto com as propriedades do objeto <code>original</code>.
	 */
	private void copiarPerfilEventoAmbienteTO(final PerfilEventoAmbienteTO original, final PerfilEventoAmbienteTO clone) {
		clone.setCodigo(original.getCodigo());
		clone.setNrMaximoConvidados(original.getNrMaximoConvidados());
		clone.setObservacao(original.getObservacao());
		clone.setValor(original.getValor());
		clone.setCodigoAmbiente(original.getCodigoAmbiente());
		clone.setDescricaoAmbiente(original.getDescricaoAmbiente());
	}

	/**
	 * Copia um objeto <code>PerfilEventoSazonalidadeTO</code>.
	 * 
	 * @param original
	 *            Objeto cujas propriedades serão copiadas para um novo objeto.
	 * @param clone
	 *            Novo objeto com as propriedades do objeto <code>original</code>.
	 */
	private void copiarPerfilEventoSazonalidadeTO(final PerfilEventoSazonalidadeTO original, final PerfilEventoSazonalidadeTO clone) {
		clone.setCodigo(original.getCodigo());
		clone.setDataInicio(original.getDataInicio());
		clone.setDataFim(original.getDataFim());
		clone.setDiaSemana(original.getDiaSemana());
		clone.setFormaCalculo(original.getFormaCalculo());
		clone.setTipoOperacao(original.getTipoOperacao());
		clone.setValor(original.getValor());
		clone.setarDiaSemana();
	}

	/**
	 * Copia um objeto <code>PerfilEventoServicoTO</code>.
	 * 
	 * @param original
	 *            Objeto cujas propriedades serão copiadas para um novo objeto.
	 * @param clone
	 *            Novo objeto com as propriedades do objeto <code>original</code>.
	 */
	private void copiarPerfilEventoServicoTO(final PerfilEventoServicoTO original, final PerfilEventoServicoTO clone) {
		clone.setCodigo(original.getCodigo());
		clone.setCodigoServico(original.getCodigoServico());
		clone.setDescricaoServico(original.getDescricaoServico());
		clone.setValor(original.getValor());
		clone.setTextoLivre(original.getTextoLivre());
		clone.setMaiorFaixaQtd(original.getMaiorFaixaQtd());
		clone.setObrigatorio(original.getObrigatorio());
        clone.setCodigoFornecedor(original.getCodigoFornecedor());
        clone.setDescricaoFornecedor(original.getDescricaoFornecedor());
	}

	/**
	 * Copia um objeto <code>PerfilEventoProdutoLocacaoTO</code>.
	 * 
	 * @param original
	 *            Objeto cujas propriedades serão copiadas para um novo objeto.
	 * @param clone
	 *            Novo objeto com as propriedades do objeto <code>original</code>.
	 */
	private void copiarPerfilEventoProdutoLocacaoTO(final PerfilEventoProdutoLocacaoTO original, final PerfilEventoProdutoLocacaoTO clone) {
		clone.setCodigo(original.getCodigo());
		clone.setCodigoProdutoLocacao(original.getCodigoProdutoLocacao());
		clone.setDescricaoProdutoLocacao(original.getDescricaoProdutoLocacao());
		clone.setObrigatorio(original.getObrigatorio());
		clone.setValor(original.getValor());
		clone.setTextoLivre(original.getTextoLivre());
	}

	/**
	 * Preenche a combo de dia da semana.
	 * 
	 * @return Lista de <code>SelectItem</code> referente a Dias da semana.
	 */
	public List<SelectItem> getDiasSemana() {
		List<SelectItem> itens = new ArrayList<SelectItem>();

		for (DiaSemana diaSemana : DiaSemana.values()) {
			itens.add(new SelectItem(diaSemana.getCodigo(), diaSemana.getDescricao()));
		}
		
		return itens;
	}

	/**
	 * Preenche a combo de tipos de operação.
	 * 
	 * @return Lista de <code>SelectItem</code> referente a Tipos de Operação.
	 */
	public List<SelectItem> getTiposOperacao() {
		List<SelectItem> itens = new ArrayList<SelectItem>();

		for (TipoOperacao tipoOperacao : TipoOperacao.values()) {
			itens.add(new SelectItem(tipoOperacao.getCodigo(), tipoOperacao.getDescricao()));
		}

		return itens;
	}

	/**
	 * Preenche a combo de formas de cálculo.
	 * 
	 * @return Lista de <code>SelectItem</code> referente a Formas de Cálculo.
	 */
	public List<SelectItem> getFormasCalculo() {
		List<SelectItem> itens = new ArrayList<SelectItem>();

		for (FormaCalculo formaCalculo : FormaCalculo.values()) {
			itens.add(new SelectItem(formaCalculo.getCodigo(), formaCalculo.getDescricao()));
		}

		return itens;
	}

	/**
	 * Preenche a combo de Ambientes.
	 * 
	 * @return Lista de <code>SelectItem</code> referente aos Ambientes.
	 */
	public List<SelectItem> getItensAmbiente() throws Exception {
		List<AmbienteVO> ambientes = CEControle.getCEFacade().consultarTodosAmbientes();

		List<SelectItem> itens = new ArrayList<SelectItem>();

		for (AmbienteVO ambiente : ambientes) {
			itens.add(new SelectItem(ambiente.getCodigo(), ambiente.getDescricao()));
		}

		return itens;
	}

	/**
	 * Prepara a view para a edição de um novo perfil de evento.
	 * 
	 * @return <from-outcome>
	 * @throws Exception
	 */
	public String novo() throws Exception {
		// Inicializa um novo perfil de evento
		this.setPerfilEventoTO(new PerfilEventoTO());

		this.setMensagemID("dados.informar");
		return "novo";
	}

	/**
	 * Prepara a view para a edição de algum perfil de evento.
	 * 
	 * @return <from-outcome>
	 * @throws Exception
	 */
	public String editar() throws Exception {
		Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
		// Carrega o perfil de evento com todas as suas características
		this.setPerfilEventoTO(CEControle.getCEFacade().carregarPerfilEvento(codigoConsulta));
		//carrega objeto para log
		this.setObjetoLog(this.getPerfilEventoTO().clone());
		// Prepara os mapas que guardam as sazonalidades e os layouts por
		// ambiente
		for (PerfilEventoAmbienteTO ambiente : this.getPerfilEventoTO().getPerfilEventoAmbienteTOs()) {
			this.getMapPerfilEventoSazonalidadeTO().put(ambiente.getCodigoAmbiente(), ambiente.getPerfilEventoSazonalidadeTOs());
			this.getMapLayouts().put(ambiente.getCodigoAmbiente(), ambiente.getLayouts());
		}

		this.setMensagemID("dados.informar");
		//redireciona para pagina [/pages/ce/cadastros/perfilEventoForm.jsp]
		return "alterar";
	}

	/**
	 * Prepara a view para uma nova consulta a perfis de evento.
	 * 
	 * @return <from-outcome>
	 */
	public String inicializarConsultar() {
		return "perfilEvento";
	}

	/**
	 * Salva um perfil de evento.
	 * 
	 * @return <from-outcome>
	 */
	public void salvar() {
		try {
			this.verificarAutorizacao();
			// Relaciona as sazonalidades e os layouts guardados nos mapas no
			// ambiente correspondente
			for (PerfilEventoAmbienteTO ambiente : this.getPerfilEventoTO().getPerfilEventoAmbienteTOs()) {
				ambiente.setPerfilEventoSazonalidadeTOs(this.getMapPerfilEventoSazonalidadeTO().get(ambiente.getCodigoAmbiente()));
				ambiente.setLayouts(this.getMapLayouts().get(ambiente.getCodigoAmbiente()));
			}

			// Caso o perfil de evento não seja persistido, incluí-lo
			if ((this.getPerfilEventoTO().getCodigo() == null) || this.getPerfilEventoTO().getCodigo().equals(0)) {
				CEControle.getCEFacade().incluirPerfilEvento(this.getPerfilEventoTO());
				 //LOG
                 registrarLogCE(this.getPerfilEventoTO(), new PerfilEventoTO(), this.getPerfilEventoTO().getCodigo(),"PERFIL EVENTO",true);
                 
				
			} else {
				
				// Caso seja persistido, alterá-lo

				CEControle.getCEFacade().alterarPerfilEvento(this.getPerfilEventoTO(), this.getAmbientesAExcluir(),
						this.getSazonalidadesAExcluir(), this.getLayoutsAExcluir(), this.getServicosAExcluir(),
						this.getProdutosLocacaoAExcluir(), this.getModelosContratoAExcluir(), this.getImagensAExcluir(),
						this.getModelosOrcamentoAExcluir(), this.getImagensModeloOrcamentoAExcluir());
				
				//LOG
                registrarLogCE(this.getPerfilEventoTO(), this.getObjetoLog(), this.getPerfilEventoTO().getCodigo(),"PERFIL EVENTO",false);
                
			}
			
			
			
			// Exibir mensagem de sucesso
			this.setMensagemID("operacoes.salvar.sucesso");
			this.setSucesso(true);
			this.setAtencao(false);
			this.setErro(false);
		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de
			// mensagens segundo chave contida na exceção
			Exception exTratada = this.tratarMensagemExcecao(e);

			
			this.setMensagemDetalhada("msg_erro",tratarMensagemErroDetalhadaReferenciaChaveEstrangeira(exTratada.getMessage()));
			
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Operação responsável por processar a exclusão um objeto da classe <code>PlanoVO</code> Após a exclusão ela automaticamente aciona a
	 * rotina para uma nova inclusão.
	 */

	public String excluir() {
		try {
			limparMsg();
			this.verificarAutorizacao();
			CEControle.getCEFacade().excluirPerfilEvento(this.getPerfilEventoTO());
			registrarLogExclusaoObjetoCE(this.getPerfilEventoTO(), this.getPerfilEventoTO().getCodigo(), "PERFIL EVENTO");
			this.setPerfilEventoTO(new PerfilEventoTO());
			this.setMensagemID("operacoes.exclusao.sucesso");
			this.setSucesso(true);
			this.setAtencao(false);
			this.setErro(false);
			novo();
			montarSucessoGrowl("Dados excluídos com sucesso!");
			redirect("/faces/pages/ce/cadastros/perfilEvento.jsp?modulo=centralEventos");
			return "consultar";
		} catch (Exception e) {
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
			return "editar";
		}
	}

	/**
	 * Apresenta o resultado da consulta
	 */
	@Override
	public boolean getApresentarResultadoConsulta() {
		return !(this.getResultadoConsulta() == null);
	}
	
	/**
	 * 
	 * @return usuario administrador
	 * @throws Exception
	 */
	public Boolean getAdministrador() throws Exception {
		UsuarioVO usuarioVO = this.getUsuarioLogado();
		return usuarioVO.getAdministrador();
	}

	/**
	 * Método que preenche a String exibeCalculo com os valores referentes a operação que será realizada na adição de uma nova sazonalidade
	 * para o ambiente do perfil evento
	 */
	public void mostraCalculo() {
		// criar uma variavel string local
		String valorFormatado;
		
		if (this.getPerfilEventoSazonalidadeTO().getValor() == null){
			this.getPerfilEventoSazonalidadeTO().setValor(0.0);
		}
		
		// se a forma de calculo não for nula e o valor também não
		if ((this.getPerfilEventoSazonalidadeTO().getFormaCalculo() != null) && (this.getPerfilEventoSazonalidadeTO().getValor() != null)) {
			// se a entrada de valor for percentual
			if (this.getPerfilEventoSazonalidadeTO().getFormaCalculo().equals(FormaCalculo.PERCENTUAL.getCodigo())) {
				// realizar operação matemática para obter o valor da
				// porcentagem referente ao valor do ambiente
				Double valor = (this.getPerfilEventoSazonalidadeTO().getValor() * this.getValorAmbiente() / 100);
				// formatar a string para apresentação
				valorFormatado = Formatador.formatarValorMonetario(valor);
				// setar na string do bean
				this.setExibeCalculo("Valor correspondente: " + valorFormatado);
				// se a entrada de valor for percentual
			} else {
				// realizar operação matemática para obter a porcentagem
				// referente ao valor do ambiente
				Double valor = (100 * this.getPerfilEventoSazonalidadeTO().getValor() / this.getValorAmbiente());
				// formatar a string para apresentação
				valorFormatado = Formatador.formatarValorPercentual(valor / 100);

				// setar na string do bean
				this.setExibeCalculo("Percentual correspondente: " + valorFormatado);
			}
		}
	}

	/* ------------------- FIM - DEMAIS MÉTODOS --------------------- */

	/**
	 * Exibição da imagem Faz o download da imagem
	 * 
	 * <AUTHOR>
	 */
	public String exibir() {
		try {
			File file;
			// obtem o layout
			PerfilEventoAmbienteLayoutTO layout = (PerfilEventoAmbienteLayoutTO) JSFUtilities.getRequestAttribute("layout");
			// obtem o arquivo java.io.File
			file = layout.getArquivo();
			URL url = new URL("file://" + file.getAbsolutePath());
			// Response
			HttpServletResponse res = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
			// Saida
			ServletOutputStream out = res.getOutputStream();
			// Bytes
			byte[] b = FileUtilities.obterBytesArquivo(file);
			// Adiciona o arquivo no header da resposta
			res.setHeader("Content-disposition", "attachment;filename=\"" + file.getName() + "\"");
			res.setContentLength(b.length);
			res.setContentType(url.openConnection().getContentType());

			out.write(b);
			out.flush();
			out.close();

			FacesContext.getCurrentInstance().responseComplete();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return "downloadLayout";
	}
	/**
	 * Preenche a combo de Tipos de Desconto.
	 * 
	 * @return Lista de <code>SelectItem</code> referente aos Tipos de Desconto.
	 */
	public List<SelectItem> getTiposDesconto() {
		List<SelectItem> itens = new ArrayList<SelectItem>();

		for (FormaCalculo tipoDesconto : FormaCalculo.values()) {
			itens.add(new SelectItem(tipoDesconto.getCodigo(), tipoDesconto.getDescricao()));
		}

		return itens;
	}

    public List<SelectItem> getListaFornecedor() throws Exception {
        List<SelectItem> result;
        try {
            List<SelectItem> itens = new ArrayList<SelectItem>();
            List<FornecedorVO> fornecedors = getFacade().getFornecedor().consultarFornecedor(false);
            for (FornecedorVO fornecedor : fornecedors) {
                itens.add(new SelectItem(fornecedor.getCodigo(), fornecedor.getPessoa().getNome()));
            }
            result = itens;
        } catch (Exception e) {
            result = new ArrayList<SelectItem>();
        }
        return result;
    }

	/**
	 * @param objetoLog the objetoLog to set
	 */
	public void setObjetoLog(PerfilEventoTO objetoLog) {
		this.objetoLog = objetoLog;
	}

	/**
	 * @return the objetoLog
	 */
	public PerfilEventoTO getObjetoLog() {
		if(objetoLog == null){
			objetoLog = new PerfilEventoTO();
		}
		return objetoLog;
	}

	/**
	 * @param temServicoAnterior the temServicoAnterior to set
	 */
	public void setTemServicoAnterior(Boolean temServicoAnterior) {
		this.temServicoAnterior = temServicoAnterior;
	}

	/**
	 * @return the temServicoAnterior
	 */
	public Boolean getTemServicoAnterior() {
		if(temServicoAnterior == null){
			temServicoAnterior = Boolean.FALSE;
		}
		return temServicoAnterior;
	}

    public void fornecedorListener(ValueChangeEvent valueChangeEvent) throws Exception {
        FornecedorVO fornecedorVO = getFacade().getFornecedor().obter( (Integer) valueChangeEvent.getNewValue());
        if (fornecedorVO != null) {
            servico.setDescricaoFornecedor(fornecedorVO.getDescricao());
        }

    }
	public void setMsgAlert(String msgAlert) {
		this.msgAlert = msgAlert;
	}

	public String getMsgAlert() {
		if (msgAlert == null) {
			return "";
		}
		return msgAlert;
	}

	public void confirmarExcluir(){
		MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
		control.setMensagemDetalhada("", "");
		setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
		control.init("Exclusão de Perfil de Evento",
				"Deseja excluir o Perfil de Evento?",
				this, "excluir", "", "", "", "grupoBtnExcluir,mensagens");
	}

}
