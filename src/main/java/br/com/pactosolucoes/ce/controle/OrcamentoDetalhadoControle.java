/**
 *
 */
package br.com.pactosolucoes.ce.controle;

import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.Usuario;
import br.com.pactosolucoes.ce.comuns.enumerador.EstadoAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.FormaCalculo;
import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoCaucaoCredito;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoContato;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoQuitacao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoReserva;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.ce.comuns.to.AlteraValoresTO;
import br.com.pactosolucoes.ce.comuns.to.AmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.FornecedorServicoTO;
import br.com.pactosolucoes.ce.comuns.to.ModeloContratoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvCaucaoCreditoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvCondicaoPagamentoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoServicoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvServicoTerceirizadoTO;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteLayoutTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoSazonalidadeTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoServicoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoTO;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.ReservaTO;
import br.com.pactosolucoes.ce.comuns.to.ServicoTO;
import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.TipoLayoutTO;
import br.com.pactosolucoes.ce.negocio.evento.EventoEncerrarVO;
import br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.evento.EventoInteresse;
import br.com.pactosolucoes.ce.negocio.interessado.AgendaVisitaVO;
import br.com.pactosolucoes.ce.negocio.interessado.InteressadoTO;
import br.com.pactosolucoes.comuns.util.FaixaQuantidade;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF da
 * página orcamentoDetalhado.jsp com as funcionalidades da classe.
 *
 * @see SuperControle
 * <AUTHOR>
 * <AUTHOR>
 */
public class OrcamentoDetalhadoControle extends CEControle {

    private static final int GERAR_NOVAS_PARCELAS = 1;
    private static final int DEVOLVER_CREDITO = 1;
    private static final int NAO_DEVOLVER_CREDITO = 2;
    private static final long serialVersionUID = -3776169001659056017L;
    private NegociacaoEventoTO negociacaoEvento;
    private NegociacaoEventoTO negociacaoEventoLog;
    private String textoLivre;
    private List<SelectItem> itensPerfisEvento;
    private List<NegEvPerfilEventoAmbienteTO> ambientes;
    private List<TipoLayoutTO> tiposLayout;
    private NegociacaoEventoContratoTO negEvContrato;
    private List<NegEvPerfilEventoProdutoLocacaoTO> bensConsumo;
    private List<NegEvPerfilEventoProdutoLocacaoTO> utensilios;
    private List<NegEvPerfilEventoProdutoLocacaoTO> brinquedos;
    private List<Integer> produtosLocacaoAExcluir;
    private ProdutoLocacaoTO filtroBensConsumo;
    private List<ProdutoLocacaoTO> bensConsumoConsulta;
    private EstadoAlteracao estadoAlteracao;
    private ProdutoLocacaoTO filtroUtensilios;
    private List<ProdutoLocacaoTO> utensiliosConsulta;
    private ProdutoLocacaoTO filtroBrinquedos;
    private List<ProdutoLocacaoTO> brinquedosConsulta;
    private ServicoTO filtroServicos;
    private List<ServicoTO> servicosConsulta;
    private FornecedorServicoTO filtroServicosTercerizados;
    private List<ServicoTO> servicoTercerizadoConsulta;
    private List<NegEvPerfilEventoServicoTO> servicos;
    private List<Integer> servicosAExcluir;
    private List<NegEvCondicaoPagamentoTO> condicoesPagamento;
    private Integer condicaoPagamentoAExcluir;
    private Boolean orcamentoFicticio;
    private Boolean alterarValores;
    private Boolean fecharNegociacaoAutorizada;
    private Boolean clienteInexistente;
    private Boolean ambienteSelecionado;
    private Boolean condicaoPagamentoSelecionada;
    private Boolean qtdAlteradaUtensilio, qtdAlteradaServico, qtdAlteradaBrinquedo, qtdAlteradaBensConsumo;
    private EventoInteresseVO evento;
    private MovProdutoVO movProdutoVO;
    private MovParcelaVO movParcelaVO;
    private Boolean origemDetalhamentoEvento;
    private String faixasServico, faixasProdutos;
    private List<String> perfisComAmbiente;
    private AmbienteVO ambienteInteresse;
    private Boolean novoOrcamento;
    private Boolean descontoLiberado;
    private Boolean perfilSelecionado = false;
    private List<SelectItem> itensAmbientes;
    private Map<Integer, List<SelectItem>> itensLayouts;
    private AlteraValoresTO alteraNegociacao;
    private Boolean alteracaoPermitida;
    private boolean dataEventoMenorAtual;
    private UsuarioVO responsavelEncerramento;
    private String textoEncerramento;
    private NegEvCaucaoCreditoTO caucaoECredito;
    private NegEvPerfilEventoProdutoLocacaoTO produtoAlteracao;
    private NegEvPerfilEventoServicoTO servicoAlteracao;
    private List<MovParcelaVO> parcelas;
    private Integer codigoCondicao;
    private Double valorEventoAntesAlteracao;
    private List<SelectItem> listaSelectItemEmpresa;
    private Integer escolhaDevolucaoCredito = NAO_DEVOLVER_CREDITO;
    private String msgAlert;

    /**
     * @return the servicoTercerizadoConsulta
     */
    public List<ServicoTO> getServicoTercerizadoConsulta() {
        if (servicoTercerizadoConsulta == null) {
            servicoTercerizadoConsulta = new ArrayList<ServicoTO>();
        }
        return servicoTercerizadoConsulta;
    }

    /**
     * @param servicoTercerizadoConsulta the servicoTercerizadoConsulta to set
     */
    public void setServicoTercerizadoConsulta(
            List<ServicoTO> servicoTercerizadoConsulta) {
        this.servicoTercerizadoConsulta = servicoTercerizadoConsulta;
    }

    /**
     * @return the filtroServicosTercerizados
     */
    public FornecedorServicoTO getFiltroServicosTercerizados() {
        if (filtroServicosTercerizados == null) {
            filtroServicosTercerizados = new FornecedorServicoTO();
        }
        return filtroServicosTercerizados;
    }

    /**
     * @param filtroServicosTercerizados the filtroServicosTercerizados to set
     */
    public void setFiltroServicosTercerizados(
            FornecedorServicoTO filtroServicosTercerizados) {
        this.filtroServicosTercerizados = filtroServicosTercerizados;
    }

    public OrcamentoDetalhadoControle() throws Exception {
        this.setMensagemID("dados.informar");
        // usuario logado no sistema
        this.getNegEvContrato().setResponsavelContrato(this.getUsuarioLogado());
    }

    /**
     * @param servicosConsulta the servicosConsulta to set
     */
    public void setServicosConsulta(List<ServicoTO> servicosConsulta) {
        this.servicosConsulta = servicosConsulta;
    }

    /**
     * @return the servicosConsulta
     */
    public List<ServicoTO> getServicosConsulta() {
        if (servicosConsulta == null) {
            servicosConsulta = new ArrayList<ServicoTO>();
        }
        return servicosConsulta;
    }

    /**
     * @return the filtroServicos
     */
    public ServicoTO getFiltroServicos() {
        if (filtroServicos == null) {
            filtroServicos = new ServicoTO();
        }
        return filtroServicos;
    }

    /**
     * @param filtroServicos the filtroServicos to set
     */
    public void setFiltroServicos(ServicoTO filtroServicos) {
        this.filtroServicos = filtroServicos;
    }

    /**
     * @return O campo origemDetalhamentoEvento.
     */
    public Boolean getOrigemDetalhamentoEvento() {
        if (this.origemDetalhamentoEvento == null) {
            this.origemDetalhamentoEvento = Boolean.FALSE;
        }
        return this.origemDetalhamentoEvento;
    }

    /**
     * @param origemDetalhamentoEvento O novo valor de origemDetalhamentoEvento.
     */
    public void setOrigemDetalhamentoEvento(final Boolean origemDetalhamentoEvento) {
        this.origemDetalhamentoEvento = origemDetalhamentoEvento;
    }

    /**
     * @return the movParcelaVO
     */
    public MovParcelaVO getMovParcelaVO() {
        return this.movParcelaVO;
    }

    /**
     * @param movParcelaVO the movParcelaVO to set
     */
    public void setMovParcelaVO(final MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public AmbienteVO getAmbienteInteresse() {
        return ambienteInteresse;
    }

    /**
     * @return the movProdutoVO
     */
    public MovProdutoVO getMovProdutoVO() {
        return this.movProdutoVO;
    }

    /**
     * @param movProdutoVO the movProdutoVO to set
     */
    public void setMovProdutoVO(final MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    /**
     * @return the qtdAlteradaUtensilio
     */
    public Boolean getQtdAlteradaUtensilio() {
        return this.qtdAlteradaUtensilio;
    }

    /**
     * @param qtdAlteradaUtensilio the qtdAlteradaUtensilio to set
     */
    public void setQtdAlteradaUtensilio(final Boolean qtdAlteradaUtensilio) {
        this.qtdAlteradaUtensilio = qtdAlteradaUtensilio;
    }

    /**
     * @return the qtdAlteradaServico
     */
    public Boolean getQtdAlteradaServico() {
        return this.qtdAlteradaServico;
    }

    /**
     * @param qtdAlteradaServico the qtdAlteradaServico to set
     */
    public void setQtdAlteradaServico(final Boolean qtdAlteradaServico) {
        this.qtdAlteradaServico = qtdAlteradaServico;
    }

    /**
     * @return the qtdAlteradaBrinquedo
     */
    public Boolean getQtdAlteradaBrinquedo() {
        return this.qtdAlteradaBrinquedo;
    }

    /**
     * @param qtdAlteradaBrinquedo the qtdAlteradaBrinquedo to set
     */
    public void setQtdAlteradaBrinquedo(final Boolean qtdAlteradaBrinquedo) {
        this.qtdAlteradaBrinquedo = qtdAlteradaBrinquedo;
    }

    /**
     * @return the qtdAlteradaBensConsumo
     */
    public Boolean getQtdAlteradaBensConsumo() {
        return this.qtdAlteradaBensConsumo;
    }

    /**
     * @param qtdAlteradaBensConsumo the qtdAlteradaBensConsumo to set
     */
    public void setQtdAlteradaBensConsumo(final Boolean qtdAlteradaBensConsumo) {
        this.qtdAlteradaBensConsumo = qtdAlteradaBensConsumo;
    }

    /**
     * @return O campo negociacaoEvento.
     */
    public NegociacaoEventoTO getNegociacaoEvento() {
        if (this.negociacaoEvento == null) {
            this.negociacaoEvento = new NegociacaoEventoTO();
        }
        return this.negociacaoEvento;
    }

    /**
     * @param ambienteInteresse the ambienteInteresse to set
     */
    public void setAmbienteInteresse(AmbienteVO ambienteInteresse) {
        this.ambienteInteresse = ambienteInteresse;
    }

    /**
     * @param negociacaoEvento O novo valor de negociacaoEvento.
     */
    public void setNegociacaoEvento(final NegociacaoEventoTO negociacaoEvento) {
        this.negociacaoEvento = negociacaoEvento;
    }

    /**
     * @return O campo textoLivre.
     */
    public String getTextoLivre() {
        return this.textoLivre;
    }

    /**
     * @param textoLivre O novo valor de textoLivre.
     */
    public void setTextoLivre(final String textoLivre) {
        this.textoLivre = textoLivre;
    }

    /**
     * @return O campo ambientes.
     */
    public List<NegEvPerfilEventoAmbienteTO> getAmbientes() {
        if (this.ambientes == null) {
            this.ambientes = new ArrayList<NegEvPerfilEventoAmbienteTO>();
        }
        return this.ambientes;
    }

    /**
     * @return the negEvContrato
     */
    public NegociacaoEventoContratoTO getNegEvContrato() {
        if (this.negEvContrato == null) {
            this.negEvContrato = new NegociacaoEventoContratoTO();
        }
        return this.negEvContrato;
    }

    /**
     * @param negEvContrato the negEvContrato to set
     */
    public void setNegEvContrato(final NegociacaoEventoContratoTO negEvContrato) {
        this.negEvContrato = negEvContrato;
    }

    /**
     * @param ambientes O novo valor de ambientes.
     */
    public void setAmbientes(final List<NegEvPerfilEventoAmbienteTO> ambientes) {
        this.ambientes = ambientes;
    }

    /**
     * @return O campo tiposLayout.
     */
    public List<TipoLayoutTO> getTiposLayout() {
        if (this.tiposLayout == null) {
            this.tiposLayout = new ArrayList<TipoLayoutTO>();
        }
        return this.tiposLayout;
    }

    /**
     * @param tiposLayout O novo valor de tiposLayout.
     */
    public void setTiposLayout(final List<TipoLayoutTO> tiposLayout) {
        this.tiposLayout = tiposLayout;
    }

    /**
     * @return O campo bensConsumo.
     */
    public List<NegEvPerfilEventoProdutoLocacaoTO> getBensConsumo() {
        if (this.bensConsumo == null) {
            this.bensConsumo = new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>();
        }
        return this.bensConsumo;
    }

    /**
     * @param bensConsumo O novo valor de bensConsumo.
     */
    public void setBensConsumo(final List<NegEvPerfilEventoProdutoLocacaoTO> bensConsumo) {
        this.bensConsumo = bensConsumo;
    }

    /**
     * @return O campo utensilios.
     */
    public List<NegEvPerfilEventoProdutoLocacaoTO> getUtensilios() {
        if (this.utensilios == null) {
            this.utensilios = new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>();
        }
        return this.utensilios;
    }

    /**
     * @param utensilios O novo valor de utensilios.
     */
    public void setUtensilios(final List<NegEvPerfilEventoProdutoLocacaoTO> utensilios) {
        this.utensilios = utensilios;
    }

    /**
     * @return O campo brinquedos.
     */
    public List<NegEvPerfilEventoProdutoLocacaoTO> getBrinquedos() {
        if (this.brinquedos == null) {
            this.brinquedos = new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>();
        }
        return this.brinquedos;
    }

    /**
     * @param brinquedos O novo valor de brinquedos.
     */
    public void setBrinquedos(final List<NegEvPerfilEventoProdutoLocacaoTO> brinquedos) {
        this.brinquedos = brinquedos;
    }

    /**
     * @return O campo produtosLocacaoAExcluir.
     */
    public List<Integer> getProdutosLocacaoAExcluir() {
        if (this.produtosLocacaoAExcluir == null) {
            this.produtosLocacaoAExcluir = new ArrayList<Integer>();
        }
        return this.produtosLocacaoAExcluir;
    }

    /**
     * @param produtosLocacaoAExcluir O novo valor de produtosLocacaoAExcluir.
     */
    public void setProdutosLocacaoAExcluir(final List<Integer> produtosLocacaoAExcluir) {
        this.produtosLocacaoAExcluir = produtosLocacaoAExcluir;
    }

    /**
     * @return O campo filtroBensConsumo.
     */
    public ProdutoLocacaoTO getFiltroBensConsumo() {
        if (this.filtroBensConsumo == null) {
            this.filtroBensConsumo = new ProdutoLocacaoTO();
            this.filtroBensConsumo.setTipo(TipoProdutoLocacao.BENS_DE_CONSUMO);
        }
        return this.filtroBensConsumo;
    }

    /**
     * @param filtroBensConsumo O novo valor de filtroBensConsumo.
     */
    public void setFiltroBensConsumo(final ProdutoLocacaoTO filtroBensConsumo) {
        this.filtroBensConsumo = filtroBensConsumo;
    }

    /**
     * @return O campo bensConsumoConsulta.
     */
    public List<ProdutoLocacaoTO> getBensConsumoConsulta() {
        if (this.bensConsumoConsulta == null) {
            this.bensConsumoConsulta = new ArrayList<ProdutoLocacaoTO>();
        }
        return this.bensConsumoConsulta;
    }

    /**
     * @param bensConsumoConsulta O novo valor de bensConsumoConsulta.
     */
    public void setBensConsumoConsulta(final List<ProdutoLocacaoTO> bensConsumoConsulta) {
        this.bensConsumoConsulta = bensConsumoConsulta;
    }

    /**
     * @return O campo filtroUtensilios.
     */
    public ProdutoLocacaoTO getFiltroUtensilios() {
        if (this.filtroUtensilios == null) {
            this.filtroUtensilios = new ProdutoLocacaoTO();
            this.filtroUtensilios.setTipo(TipoProdutoLocacao.UTENSILIOS);
        }
        return this.filtroUtensilios;
    }

    /**
     * @param filtroUtensilios O novo valor de filtroUtensilios.
     */
    public void setFiltroUtensilios(final ProdutoLocacaoTO filtroUtensilios) {
        this.filtroUtensilios = filtroUtensilios;
    }

    /**
     * @return O campo utensiliosConsulta.
     */
    public List<ProdutoLocacaoTO> getUtensiliosConsulta() {
        if (this.utensiliosConsulta == null) {
            this.utensiliosConsulta = new ArrayList<ProdutoLocacaoTO>();
        }
        return this.utensiliosConsulta;
    }

    /**
     * @param utensiliosConsulta O novo valor de utensiliosConsulta.
     */
    public void setUtensiliosConsulta(final List<ProdutoLocacaoTO> utensiliosConsulta) {
        this.utensiliosConsulta = utensiliosConsulta;
    }

    /**
     * @return O campo filtroBrinquedos.
     */
    public ProdutoLocacaoTO getFiltroBrinquedos() {
        if (this.filtroBrinquedos == null) {
            this.filtroBrinquedos = new ProdutoLocacaoTO();
            this.filtroBrinquedos.setTipo(TipoProdutoLocacao.BRINQUEDOS);
        }
        return this.filtroBrinquedos;
    }

    /**
     * @param filtroBrinquedos O novo valor de filtroBrinquedos.
     */
    public void setFiltroBrinquedos(final ProdutoLocacaoTO filtroBrinquedos) {
        this.filtroBrinquedos = filtroBrinquedos;
    }

    /**
     * @return O campo brinquedosConsulta.
     */
    public List<ProdutoLocacaoTO> getBrinquedosConsulta() {
        if (this.brinquedosConsulta == null) {
            this.brinquedosConsulta = new ArrayList<ProdutoLocacaoTO>();
        }
        return this.brinquedosConsulta;
    }

    /**
     * @param brinquedosConsulta O novo valor de brinquedosConsulta.
     */
    public void setBrinquedosConsulta(final List<ProdutoLocacaoTO> brinquedosConsulta) {
        this.brinquedosConsulta = brinquedosConsulta;
    }

    /**
     * @return O campo servicos.
     */
    public List<NegEvPerfilEventoServicoTO> getServicos() {
        if (this.servicos == null) {
            this.servicos = new ArrayList<NegEvPerfilEventoServicoTO>();
        }
        return this.servicos;
    }

    public void setParcelas(List<MovParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public List<MovParcelaVO> getParcelas() throws Exception {
        if (parcelas == null) {
            this.setParcelas(getCEFacade().consultarParcelasPorEvento(this.getNegociacaoEvento().getCodigoEventoInteresse()));
            Ordenacao.ordenarLista(parcelas, "codigo");
        }
        return parcelas;
    }

    /**
     * @param servicos O novo valor de servicos.
     */
    public void setServicos(final List<NegEvPerfilEventoServicoTO> servicos) {
        this.servicos = servicos;
    }

    /**
     * @return O campo servicosAExcluir.
     */
    public List<Integer> getServicosAExcluir() {
        if (this.servicosAExcluir == null) {
            this.servicosAExcluir = new ArrayList<Integer>();
        }
        return this.servicosAExcluir;
    }

    /**
     * @param servicosAExcluir O novo valor de servicosAExcluir.
     */
    public void setServicosAExcluir(final List<Integer> servicosAExcluir) {
        this.servicosAExcluir = servicosAExcluir;
    }

    /**
     * Defini o tipo como caucao
     *
     * Autor: Pedro Y. Saito Criado em 16/02/2011
     */
    public void setarTipoCaucao() {
        this.setCaucaoECredito(new NegEvCaucaoCreditoTO());
        this.getCaucaoECredito().setTipoCaucaoECredito(TipoCaucaoCredito.CHEQUE_CAUCAO);
    }

    /**
     * Defini o tipo como credito
     *
     * Autor: Pedro Y. Saito Criado em 16/02/2011
     */
    public void setarTipoCredito() {
        this.setCaucaoECredito(new NegEvCaucaoCreditoTO());
        this.getCaucaoECredito().setTipoCaucaoECredito(TipoCaucaoCredito.CREDITO);
    }

    /**
     * Adiciona caucao ou credito da lista e faz os calculos para ser exibido
     * nos detalhes da negociacao
     *
     * Autor: Pedro Y. Saito Criado em 16/02/2011
     */
    public void adicionarCaucaoECredito() throws Exception {
        this.getCaucaoECredito().setPodeQuitar(false);
        if ((this.getCaucaoECredito().getDataQuitacao() == null)
                && (this.evento != null && this.evento.getSituacao() != null
                && (this.evento.getSituacao().equals(Situacao.CONFIRMADO)
                || this.evento.getSituacao().equals(Situacao.AUTORIZADO)))) {
            this.getCaucaoECredito().setPodeQuitar(true);
        }
        this.getCaucaoECredito().setDataLancamento(Calendario.hoje());
        this.getNegociacaoEvento().getListaNegEvCaucaoECreditoTO().add(this.getCaucaoECredito());

        if (this.getCaucaoECredito().getTipoCaucaoECredito().equals(TipoCaucaoCredito.CREDITO)) {
            // Calcular a negociação
            this.calcular();
        }

        this.setCaucaoECredito(new NegEvCaucaoCreditoTO());
    }

    /**
     * Remove caucao ou credito da lista e faz os calculos para ser exibido nos
     * detalhes da negociacao
     *
     * Autor: Pedro Y. Saito Criado em 16/02/2011
     */
    public void removerCaucaoECredito(final ActionEvent event) throws Exception {
        NegEvCaucaoCreditoTO necc = (NegEvCaucaoCreditoTO) JSFUtilities.getRequestAttribute("caucaoCredito");

        this.negociacaoEvento.getListaNegEvCaucaoECreditoTO().remove(necc);

        // Calcular a negociação
        this.calcular();

        // Gerando novamente as parcelas;
        selecionarCondicaoPagamento();

    }

    public void quitarCaucaoECredito() throws Exception {
        //Interacao para atualizar o tipo de quitacao
        NegEvCaucaoCreditoTO necc = null;
        for (int i = 0; i < this.negociacaoEvento.getListaNegEvCaucaoECreditoTO().size(); i++) {
            necc = this.negociacaoEvento.getListaNegEvCaucaoECreditoTO().get(i);
            if (necc.getDataLancamento().equals(this.getCaucaoECredito().getDataLancamento())) {
                //Removendo o obj caucao e credito antigo
                this.negociacaoEvento.getListaNegEvCaucaoECreditoTO().remove(i);
                //Atribuindo novos valores aos atributos do obj caucao e credito
                necc.setTpQuitacao(TipoQuitacao.getTipoQuitacao(this.getCaucaoECredito().getTipoQuitacao()));
                necc.setDataQuitacao(Calendario.hoje());

                if (this.getCaucaoECredito().getTipoQuitacao() == TipoQuitacao.QUITACAO_DEPOSITO_CREDITO_CLIENTE.getCodigo()) {
                    InteressadoTO interessadoVO = CEControle.getCEFacade().obterInteressado(this.evento.getInteressado().getCodigo());
                    double valorAnterior = CEControle.getCEFacade().consultarSaldoAtual(interessadoVO.getCodigo());

//					valor anterior mais o valor de credito
                    necc.getMovCCClienteVO().setSaldoAtual(this.getCaucaoECredito().getValor() + valorAnterior);
                    //necc.getMovCCClienteVO().setSaldoAtual(Formatador.obterValorNumerico(necc.getMovCCClienteVO().getSaldoAtual().toString()));
//					valor de credito *modificar o código *getXX dessa propriedado no VO para retornar 2 casas decimais, arredondando
                    necc.getMovCCClienteVO().setValor(this.getCaucaoECredito().getValor());
//					
//					Caução de Evento( ou credito de bebidas) n. xxx (id_evento) depositado em credito para o cliente
                    if (this.getCaucaoECredito().getTipoCaucaoECredito().equals(TipoCaucaoCredito.CHEQUE_CAUCAO)) {
                        necc.getMovCCClienteVO().setDescricao("Caução de Evento n. " + this.negociacaoEvento.getCodigoEventoInteresse() + " depositado em credito para o cliente");
                    } else if (this.getCaucaoECredito().getTipoCaucaoECredito().equals(TipoCaucaoCredito.CREDITO)) {
                        necc.getMovCCClienteVO().setDescricao("Crédito de bebidas n. " + this.negociacaoEvento.getCodigoEventoInteresse() + " depositado em credito para o cliente");
                    }
//					CR
                    necc.getMovCCClienteVO().setTipoMovimentacao("CR");
//					data atual (a data devera ser completa ou seja XXXX-XX-XX XX:XX:XX)
                    DateFormat df = DateFormat.getDateInstance(DateFormat.LONG);
                    necc.getMovCCClienteVO().setDataRegistro(df.parse(df.format(Calendario.hoje())));
//					cliente que esta recebendo o credito
                    necc.getMovCCClienteVO().setPessoa(interessadoVO.getPessoa());
//					responsavel por liberar o credito
                    necc.getMovCCClienteVO().setResponsavelAutorizacao(this.getUsuarioLogado());
//				    setar como null
                    necc.getMovCCClienteVO().setReciboPagamentoVO(null);
//				    setar como null
                    necc.getMovCCClienteVO().setContratoOperacaoVO(null);
                }

                necc.setPodeQuitar(false);

                //Adicionando o obj caucao e credito com novos valores a lista
                this.negociacaoEvento.getListaNegEvCaucaoECreditoTO().add(necc);

                break;
            }
        }

        this.setCaucaoECredito(new NegEvCaucaoCreditoTO());
    }

    public void selCaucaoECredito(final ActionEvent event) throws Exception {
        NegEvCaucaoCreditoTO necc = (NegEvCaucaoCreditoTO) JSFUtilities.getRequestAttribute("caucaoCredito");
        this.caucaoECredito = necc;
    }

    public List<SelectItem> getTiposQuitacaoCaucao() throws Exception {
        // criar lista para retorno
        final List<SelectItem> itens = new ArrayList<SelectItem>();
        // percorrer os valores do enumerador TipoFiltroData
        for (TipoQuitacao tipoQuitacao : TipoQuitacao.values()) {
            // adicionar o tipo de filtro na lista de retorno
            itens.add(new SelectItem(tipoQuitacao.getCodigo(), tipoQuitacao.getDescricao()));
        }
        // retornar a lista
        return itens;
    }

    public List<SelectItem> getTiposQuitacaoCredito() throws Exception {
        // criar lista para retorno
        final List<SelectItem> itens = new ArrayList<SelectItem>();

        TipoQuitacao tipoQuitacao = TipoQuitacao.getTipoQuitacao(TipoQuitacao.QUITACAO_DEPOSITO_CREDITO_CLIENTE.getCodigo());
        // adicionar o tipo de filtro na lista de retorno
        itens.add(new SelectItem(tipoQuitacao.getCodigo(), tipoQuitacao.getDescricao()));

        // retornar a lista
        return itens;
    }

    /**
     * @return O campo condicoesPagamento.
     * @throws Exception
     */
    public List<NegEvCondicaoPagamentoTO> getCondicoesPagamento() throws Exception {
        // Caso a lista de condições de pagamento seja nula, iniciá-la
        if (this.condicoesPagamento == null) {
            // Consultar condições de pagamento existentes
            this.condicoesPagamento = CEControle.getCEFacade().consultarCondicoesPagamento();

            // Caso haja alguma condição de pagamento existenta para a negociação, selecioná-la
            if ((this.getNegociacaoEvento().getCondicaoPagamento().getCodigo() != null)
                    && !this.getNegociacaoEvento().getCondicaoPagamento().getCodigo().equals(0)) {
                for (int i = 0; i < this.condicoesPagamento.size(); i++) {
                    if (this.condicoesPagamento.get(i).getCodigoCondicaoPagamento().equals(
                            this.getNegociacaoEvento().getCondicaoPagamento().getCodigoCondicaoPagamento())) {
                        this.condicoesPagamento.remove(i);
                        this.condicoesPagamento.add(i, this.getNegociacaoEvento().getCondicaoPagamento());
                        break;
                    }
                }
            }
        }
        return this.condicoesPagamento;
    }

    /**
     * @param condicoesPagamento O novo valor de condicoesPagamento.
     */
    public void setCondicoesPagamento(final List<NegEvCondicaoPagamentoTO> condicoesPagamento) {
        this.condicoesPagamento = condicoesPagamento;
    }

    /**
     * @return O campo condicaoPagamentoAExcluir.
     */
    public Integer getCondicaoPagamentoAExcluir() {
        return this.condicaoPagamentoAExcluir;
    }

    /**
     * @param evento the evento to set
     */
    public void setEvento(final EventoInteresseVO evento) {
        this.evento = evento;
    }

    /**
     * @return the evento
     */
    public EventoInteresseVO getEvento() {
        return this.evento;
    }

    /**
     * @param condicaoPagamentoAExcluir O novo valor de
     * condicaoPagamentoAExcluir.
     */
    public void setCondicaoPagamentoAExcluir(final Integer condicaoPagamentoAExcluir) {
        this.condicaoPagamentoAExcluir = condicaoPagamentoAExcluir;
    }

    /**
     * @return O campo orcamentoFicticio.
     */
    public Boolean getOrcamentoFicticio() {
        if (this.orcamentoFicticio == null) {
            this.orcamentoFicticio = false;
        }

        return this.orcamentoFicticio;
    }

    /**
     * @param orcamentoFicticio O novo valor de orcamentoFicticio.
     */
    public void setOrcamentoFicticio(final Boolean orcamentoFicticio) {
        this.orcamentoFicticio = orcamentoFicticio;
    }

    public Boolean getBtnSalvarReserva() {
        if (this.evento != null && this.evento.getSituacao() != null
                && ((this.evento.getSituacao().equals(Situacao.ORCADO))
                || (this.evento.getSituacao().equals(Situacao.PRE_RESERVADO))
                || (this.evento.getSituacao().equals(Situacao.NAO_RESERVADO)))) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getBtnSalvarSemReserva() {
        if (this.evento != null && this.evento.getSituacao() != null
                && ((this.evento.getSituacao().equals(Situacao.ORCADO))
                || (this.evento.getSituacao().equals(Situacao.PRE_RESERVADO))
                || (this.evento.getSituacao().equals(Situacao.NAO_RESERVADO)))) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getBtnEditar() {
        if (this.evento != null && this.evento.getSituacao() != null
                && ((!this.evento.getSituacao().equals(Situacao.CANCELADO))
                && !this.evento.getSituacao().equals(Situacao.ENCERRADO))) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getBtnRemoverCredito() {
        if (this.evento != null && this.evento.getSituacao() != null
                && ((!this.evento.getSituacao().equals(Situacao.AUTORIZADO))
                && !this.evento.getSituacao().equals(Situacao.CONFIRMADO))) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getBtnFecharNegociacao() {
        if (this.evento != null && this.evento.getSituacao() != null
                && this.evento.getSituacao().equals(Situacao.PRE_RESERVADO)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @return O campo ambienteSelecionado.
     */
    public Boolean getAmbienteSelecionado() {
//		// true caso algum ambiente esteja selecionado.
//		this.ambienteSelecionado = ((this.getNegociacaoEvento().getAmbiente().getCodigoAmbiente() != null) && !this.getNegociacaoEvento()
//				.getAmbiente().getCodigoAmbiente().equals(0));
//		return this.ambienteSelecionado;

        if (this.getAmbientes().size() > 0) {
            this.ambienteSelecionado = true;
        } else {
            this.ambienteSelecionado = false;
        }
        return this.ambienteSelecionado;
    }

    /**
     * @param ambienteSelecionado O novo valor de ambienteSelecionado.
     */
    public void setAmbienteSelecionado(final Boolean ambienteSelecionado) {
        this.ambienteSelecionado = ambienteSelecionado;
    }

    /**
     * @return O campo condicaoPagamentoSelecionada.
     */
    public Boolean getCondicaoPagamentoSelecionada() {
        // true caso alguma condição de pagamento esteja selecionada.
        this.condicaoPagamentoSelecionada = ((this.getNegociacaoEvento().getCondicaoPagamento().getCodigoCondicaoPagamento() != null) && !this.getNegociacaoEvento().getCondicaoPagamento().getCodigoCondicaoPagamento().equals(0));
        return this.condicaoPagamentoSelecionada;
    }

    /**
     * @param condicaoPagamentoSelecionada O novo valor de
     * condicaoPagamentoSelecionada.
     */
    public void setCondicaoPagamentoSelecionada(final Boolean condicaoPagamentoSelecionada) {
        this.condicaoPagamentoSelecionada = condicaoPagamentoSelecionada;
    }

    /**
     * Recebe o parâmetro de requisição "codigoEventoInteresse" que contem o
     * identificador do evento interesse, carrega a negociação associada e abre
     * a página de orçamento.
     *
     * @return <from-outcome>
     * @throws Exception
     */
    public String abrirOrcamentoFicticio() throws Exception {
        setNegociacaoEvento(new NegociacaoEventoTO());
        this.setPerfilSelecionado(false);
        montarListaSelectItemEmpresa();
        this.setValorEventoAntesAlteracao(0.0);
        this.setNovoOrcamento(Boolean.FALSE);
        // A negociação será fictícia, portanto, não persistida
        this.setOrcamentoFicticio(true);
        //redireciona para a pagina [/pages/ce/cadastros/orcamentoDetalhado.jsp]
        if (!getUsuarioLogado().getAdministrador()) {
            this.getNegociacaoEvento().setEmpresa(getEmpresaLogado());
        }
        return "orcamentoDetalhado";
    }

    public void selOrigemDetalhamentoEvento(final ActionEvent event) {
        this.setOrigemDetalhamentoEvento(Boolean.TRUE);
    }

    /**
     * Responsável por limpar a negociação quando um novo perfil é selecionado
     *
     * <AUTHOR> 14/02/2011
     * @param event
     * @throws Exception
     */
    public void selPerfilEvento(final ActionEvent event) throws Exception {
        limparNegociacao();
    }

    /**
     * Obtem o parâmetro
     * <code>dataInteresse</code> do evento
     * <code>ActionEvent</code> disparado.
     *
     * @param event Evento disparado que contem o parâmetro.
     * @throws Exception
     */
    public void selDataInteresseListener(final ActionEvent event) throws Exception {
        this.getNegociacaoEvento().setDataEvento((Date) event.getComponent().getAttributes().get("dataInteresse"));
        this.selecionarDataEvento();
    }

    /**
     * Obtem o parâmetro
     * <code>codigoEventoInteresse</code> do evento
     * <code>ActionEvent</code> disparado.
     *
     * @param event Evento disparado que contem o parâmetro.
     * @throws Exception
     */
    public void selEventoInteresseListener(final ActionEvent event) throws Exception {
        //pegar código do evento
        Object component = event.getComponent().getAttributes().get("codigoEventoInteresse");
        this.getNegociacaoEvento().setCodigoEventoInteresse(Integer.valueOf(component.toString()));
        //preencher o ambiente de interesse
        Object component2 = event.getComponent().getAttributes().get("ambienteInteresse");
        //preencher o perfil de interesse
        Object component3 = event.getComponent().getAttributes().get("perfilInteresse");
        if (component3 != null) {
            this.getNegociacaoEvento().getPerfilEventoTO().setCodigo(Integer.valueOf(component3.toString()));
            this.carregarPerfilEvento();
        }
        this.setAmbienteInteresse(CEControle.getCEFacade().obterAmbiente(Integer.valueOf(component2.toString())));
        this.getNegociacaoEvento().setDataEvento((Date) event.getComponent().getAttributes().get("dataInteresse"));
        this.setNovoOrcamento(Boolean.TRUE);
        this.selecionarDataEvento();


    }

    /**
     * Obtem o parâmetro
     * <code>codigoEventoInteresse</code> do evento
     * <code>ActionEvent</code> disparado.
     *
     * @param event Evento disparado que contem o parâmetro.
     * @throws Exception
     */
    public void selEventoExibeListener(final ActionEvent event) throws Exception {
        Object component = event.getComponent().getAttributes().get("codigoEventoInteresse");
        this.getNegociacaoEvento().setCodigoEventoInteresse(Integer.valueOf(component.toString()));

        this.evento = new EventoInteresse().obter(this.getNegociacaoEvento().getCodigoEventoInteresse());

        limparMsg();
    }

    /**
     * Obtem o parâmetro
     * <code>codigoEventoInteresse</code> do evento
     * <code>ActionEvent</code> disparado.
     *
     * @param event Evento disparado que contem o parâmetro.
     * @throws Exception
     *
     */
    public void selNegociacaoEventoListener(final ActionEvent event) throws Exception {
        Object component = event.getComponent().getAttributes().get("codigoReserva");
        this.getNegociacaoEvento().setCodigoEventoInteresse(
                CEControle.getCEFacade().carregarCodigoEventoInteresse(Integer.valueOf(component.toString())));
    }

    /**
     * Recebe o parâmetro de requisição "codigoEventoInteresse" que contem o
     * identificador do evento interesse, carrega a negociação associada e abre
     * a página de orçamento.
     *
     * @return <from-outcome>
     * @throws Exception
     */
    public String abrirOrcamentoDetalhado() throws Exception {
        montarListaSelectItemEmpresa();
        this.setValorEventoAntesAlteracao(0.0);
        // Carregar a negociação
        NegociacaoEventoTO negociacao = CEControle.getCEFacade().carregarNegociacaoEventoPorEventoInteresse(
                this.getNegociacaoEvento().getCodigoEventoInteresse());

        // Verificar se o evento interesse possui orçamento
        if (negociacao != null) {
            this.setNegociacaoEvento(negociacao);
            this.exibirDetalhesEvento();
            // Carregar tela:

            // Selecionar itens existentes na negociação
            this.getNegociacaoEvento().getAmbiente().setSelecionado(true);
            for (NegEvPerfilEventoServicoTO servico : this.getNegociacaoEvento().getServicos()) {
                servico.setSelecionado(true);
            }
            for (NegEvPerfilEventoProdutoLocacaoTO bemConsumo : this.getNegociacaoEvento().getBensConsumo()) {
                bemConsumo.setSelecionado(true);
            }
            for (NegEvPerfilEventoProdutoLocacaoTO utensilio : this.getNegociacaoEvento().getUtensilios()) {
                utensilio.setSelecionado(true);
            }
            for (NegEvPerfilEventoProdutoLocacaoTO brinquedo : this.getNegociacaoEvento().getBrinquedos()) {
                brinquedo.setSelecionado(true);
            }
            for (NegEvServicoTerceirizadoTO servTercerizado : this.getNegociacaoEvento().getServicosTerceirizados()) {
                servTercerizado.setSelecionado(true);
                servTercerizado.montarFornecedores(
                        getCEFacade().consultaFornecedorPorServico(servTercerizado.getFornecedorServico().getCodServico()));
            }
            this.getNegociacaoEvento().getCondicaoPagamento().setSelecionado(true);

            // Carregar perfil do evento, possíveis ambientes, serviços, bens de
            // consumo, utensílios e brinquedos
            this.carregarPerfilEvento();

            // Carregar os tipos de layout
            AmbienteTO ambiente = new AmbienteTO();
            ambiente.setCodigo(this.getNegociacaoEvento().getAmbiente().getCodigoAmbiente());
            if (CEControle.getCEFacade().consultarTipoLayoutPorAmbiente(ambiente) != null) {
                this.setTiposLayout(CEControle.getCEFacade().consultarTipoLayoutPorAmbiente(ambiente));
            }

            this.getNegociacaoEvento().getAmbiente().getTipoLayout().setSelecionado(true);
            for (int i = 0; i < this.getTiposLayout().size(); i++) {
                if (this.getTiposLayout().get(i).getCodigo().equals(this.getNegociacaoEvento().getAmbiente().getTipoLayout().getCodigo())) {
                    this.getTiposLayout().remove(i);
                    this.getTiposLayout().add(i, this.getNegociacaoEvento().getAmbiente().getTipoLayout());
                    break;
                }
            }
            this.getNegociacaoEvento().setAmbientes(new ArrayList<NegEvPerfilEventoAmbienteTO>());
            this.getNegociacaoEvento().setAmbientes(getCEFacade().consultarAmbienteNegociacao(this.getNegociacaoEvento().getCodigo()));
            for (NegEvPerfilEventoAmbienteTO negAmbiente : this.getNegociacaoEvento().getAmbientes()) {
                negAmbiente.setNrMaximoConvidadoExibicao(this.obterNrMaximoConvidadosAmbiente(negAmbiente.getCodigoAmbiente()));

                for (PerfilEventoAmbienteTO pea : this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoAmbienteTOs()) {
                    if (negAmbiente.getCodigoAmbiente().equals(pea.getCodigoAmbiente())) {
                        negAmbiente.setPerfilEventoSazonalidadeTOs(CEControle.getCEFacade().consultarPorPerfilEventoAmbiente(pea.getCodigo()));
                    }
                }
            }
            this.arranjarLayouts();

            CEControle.getCEFacade().verificarSazonalidade(this.getNegociacaoEvento());
            this.calcular();
            //redireciona para a pagina de exibicao de orcamento (/pages/ce/eventos/exibeOrcamento.jsp)
            return "exibeOrcamento";
        } else {
            this.setarHorarios();
            if (this.validaDisponibilidadeHorarioEvento()) {
                return "exibeOrcamento";
            }
        }

        //redireciona para a pagina de detalhe do orcamento (/pages/ce/cadastros/orcamentoDetalhado.jsp)
        return "orcamentoDetalhado";
    }

    /**
     * Exibe o orcamento se true
     *
     * @return exibeOrcamento se não
     * @throws Exception
     */
    public String exibirOrcamento() throws Exception {
        try {

            if (!getUsuarioLogado().getAdministrador()) {
                this.getNegociacaoEvento().setEmpresa(getEmpresaLogado());
            }

            this.setValorEventoAntesAlteracao(0.0);
            this.setQtdAlteradaBensConsumo(false);
            this.setQtdAlteradaBrinquedo(false);
            this.setQtdAlteradaServico(false);
            this.setQtdAlteradaUtensilio(false);
            this.setDataEventoMenorAtual(false);
            this.setarHorarios();
            this.arranjarFornecedores();
            this.arranjarLayouts();

            if (UteisValidacao.emptyNumber(this.getNegociacaoEvento().getPerfilEventoTO().getCodigo())) {
                this.setMensagemID("campoObrigatorio.perfilEvento");
                return "orcamentoDetalhado";
            }
            if (UteisValidacao.emptyNumber(this.getNegociacaoEvento().getEmpresa().getCodigo())) {
                this.setMensagemID("campoObrigatorio.empresa");
                return "orcamentoDetalhado";
            }

            //validando ambientes
            if (!validaAmbientesEvento()) {
                return "orcamentoDetalhado";
            }
            if (this.validaDisponibilidadeHorarioEvento()) {
                //setar o evento como não reserva
                this.negociacaoEvento.setEventoReserva(Boolean.FALSE);
                //setando a situacao para o evento negociado
                this.getNegociacaoEvento().setSituacao(Situacao.ORCADO);
                //salvar negociacao
                this.salvar();
                this.evento = CEControle.getCEFacade().obterEventoInteresse(this.getNegociacaoEvento().getCodigoEventoInteresse());

                //redireciona para pagina [/pages/ce/eventos/exibeOrcamento.jsp]
                return "exibeOrcamento";
            }
        } catch (Exception e) {
            return "orcamentoDetalhado";
        }
        return "orcamentoDetalhado";
    }

    public String abrirCadastroEventoOrcado() {
        try {
            if (!validaDisponibilidadeHorarioEvento()) {
                return "orcamentoDetalhado";
            }
            CadastroInicialControle cadControle = (CadastroInicialControle) getControlador(CadastroInicialControle.class);
            return cadControle.abrirCadastroEventoOrcado();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            return "orcamentoDetalhado";
        }
    }

    /**
     * Preenche os nomes dos fornecedores para exibição na view, retirando os
     * que não estão selecionados
     */
    private void arranjarFornecedores() {
        //criar lista para guardar os não selecionados
        List<NegEvServicoTerceirizadoTO> retirar = new ArrayList<NegEvServicoTerceirizadoTO>();
        //percorrer a lista dos serviços da negociação
        for (NegEvServicoTerceirizadoTO nest : negociacaoEvento.getServicosTerceirizados()) {
            //se selecionado, setar o nome do fornecedor para exibição
            if (nest.getSelecionado()) {
                nest.setarNomeFornecedor();
            } else {
                //senão adicionar a lista dos que ser removidos
                retirar.add(nest);
            }
        }
        //retirar da lista os que não estão selecionados
        negociacaoEvento.getServicosTerceirizados().removeAll(retirar);

    }

    /**
     * Preenche os nomes dos fornecedores para exibição na view, retirando os
     * que não estão selecionados
     */
    private void arranjarLayouts() {
        // limpar lista de layouts para exibição
        this.setTiposLayout(new ArrayList<TipoLayoutTO>());
        // percorrer ambientes
        for (NegEvPerfilEventoAmbienteTO ambiente : negociacaoEvento.getAmbientes()) {
            //verificar se o ambiente possui layout
            if (!UteisValidacao.emptyNumber(ambiente.getTipoLayout().getCodigo())) {
                // percorrer mapa de todos os layouts
                for (SelectItem item : this.getItensLayouts().get(ambiente.getCodigoAmbiente())) {
                    // verificar se o layout é o mesmo do ambiente
                    if (Integer.parseInt(item.getValue().toString()) == ambiente.getTipoLayout().getCodigo()) {
                        TipoLayoutTO tipo = new TipoLayoutTO();
                        // adicionar na lista para exibição
                        tipo.setDescricao(item.getLabel());
                        //adicionar o layout na lista
                        this.getTiposLayout().add(tipo);
                    }
                }
            }
        }
    }

    /**
     * Calcula o valor da negociação.
     *
     * @throws Exception
     */
    public void calcular() throws Exception {
        limparMsg();
        CEControle.getCEFacade().calcularValorFinalNegociacao(this.getNegociacaoEvento());
    }

    /**
     * Aplica o desconto validando a permissão
     *
     * @throws Exception
     */
    public void aplicarDesconto() throws Exception {
        limparMsg();
        //Verificando se os objetos sao nullos
        if (this.getNegociacaoEvento().getTipoDesconto() == null) {
            this.setMensagemID("operacoes.desconto.sem.tipo.desconto.definido");
        } else {
            if ( //verificar se o desconto é valido
                    CEControle.getCEFacade().permiteDesconto(this.getNegociacaoEvento().getValorTotal(),
                    this.getNegociacaoEvento().getDesconto(), this.getNegociacaoEvento().getTipoDesconto())) {
                //calcular negociacao
                this.calcular();
            } else {
                // setar mensagem
                this.setMensagemID("operacoes.desconto.valorNaoPermitido");
            }
        }
    }

    public void removerDesconto() throws Exception {
        limparMsg();
        this.getNegociacaoEvento().setDesconto(0.0);
        this.getNegociacaoEvento().setTipoDesconto(0);
        this.calcular();

    }

    public void salvarQuitacaoCredito() {
        try {
            if (UteisValidacao.emptyNumber(caucaoECredito.getCodigo())) {
                getCEFacade().getNegEvCaucaoCredito().incluir(caucaoECredito, negociacaoEvento.getCodigo());
            }
            caucaoECredito.setTpQuitacao(TipoQuitacao.getTipoQuitacao(this.getCaucaoECredito().getTipoQuitacao()));
            caucaoECredito.setDataQuitacao(Calendario.hoje());

            if (this.getCaucaoECredito().getTipoQuitacao() == TipoQuitacao.QUITACAO_DEPOSITO_CREDITO_CLIENTE.getCodigo()) {
                getCEFacade().incluirCredito(negociacaoEvento, getUsuario(), caucaoECredito);
            }
            getCEFacade().getNegEvCaucaoCredito().alterar(caucaoECredito, negociacaoEvento.getCodigo());
            setMsgAlert("Richfaces.hideModalPanel('panelCacaoECreditoQuitacao');alert('Quitação salva com sucesso!');");
            caucaoECredito.setPodeQuitar(false);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    /**
     * Fecha a negociação.
     *
     * @return <from-outcome>
     */
    public String fecharNegociacao() {
        try {
            this.verificarAutorizacao();
            limparMsg();
            if (this.getEvento() == null) {
                exibirDetalhesEvento();
            }
            this.checarCredenciais();
            if (!verificaDispEventoSemReserva()) {
                return "detalhamentoEvento";
            }
            // incluir contrato da negociacao
            CEControle.getCEFacade().fecharNegociacao(this.negociacaoEvento, this.negEvContrato, getUsuarioLogado());
            // Alterar a situação do Evento
            CEControle.getCEFacade().alterarSituacaoEvento(this.getNegociacaoEvento().getCodigoEventoInteresse(), Situacao.CONFIRMADO);
            this.colocarCodigoContratoNaSessao();
            JSFUtilities.removeFromSession("MovParcelaControle");

            //registrar contato
            getCEFacade().salvarContato(TipoContato.CONFIRMAR_EVENTO, this.getNegociacaoEvento().getCodigoEventoInteresse(),
                    this.getMensagemInternalizacao(TipoContato.CONFIRMAR_EVENTO.getTextoID()),
                    this.getUsuarioLogado().getCodigo(), this.getEvento().getInteressado().getCodigo(),
                    this.getNegociacaoEvento(), null);

            getCEFacade().registrarMudancaSituacao(this.getEvento().getSituacao(), Situacao.CONFIRMADO,
                    this.getNegociacaoEvento().getCodigoEventoInteresse(), getUsuarioLogado().getNome());
            return "pagamento";

        } catch (Exception e) {
            CadastroInicialControle cadastro = (CadastroInicialControle) JSFUtilities.getFromSession(CadastroInicialControle.class.getSimpleName());
            if (cadastro == null) {
                cadastro = new CadastroInicialControle();
            }
            cadastro.setMensagemDetalhada(e);
            return "detalhamentoEvento";
        }
    }

    /**
     * Responsável por indicar a existencia de parcelas na negociação (sem
     * consultar em banco, levando em conta apenas a situação do evento)
     *
     * <AUTHOR> Alcides 03/03/2011
     * @return flag
     * @throws Exception
     */
    public Boolean getParcelasExistentes() {
        try {
            Boolean retorno = Boolean.FALSE;
            if (!this.getOrcamentoFicticio()) {
                if (this.getEvento() == null) {
                    this.exibirDetalhesEvento();
                }
                //se a situacao for confirmado ou alguma situação acima de confirmado
                if (this.getEvento().getSituacao().getCodigo() >= Situacao.CONFIRMADO.getCodigo()) {
                    retorno = Boolean.TRUE;
                }
            }
            return retorno;
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * Verifica se o evento não possui reserva, para criar uma reserva nova
     *
     * <AUTHOR>
     * @throws Exception
     */
    private Boolean verificaDispEventoSemReserva() throws Exception {
        Boolean retorno = Boolean.TRUE;
        ;
        if (!negociacaoEvento.getEventoReserva()) {
            if (!validaDisponibilidadeHorarioEvento()) {
                retorno = Boolean.FALSE;
            }
        }
        return retorno;
    }

    private void checarCredenciais() throws Exception {
        // verificar usuario e senha
        this.getNegEvContrato().setResponsavelContrato(
                getFacade().getControleAcesso().verificarLoginUsuario(this.getNegEvContrato().getResponsavelContrato().getCodigo().intValue(), this.getNegEvContrato().getResponsavelContrato().getSenha().toUpperCase()));
    }

    // TODO: implementar regras
    public String receber() {
        return "receber";
    }

    /**
     * Salva a negociação.
     */
    public void salvar() {
        try {
            limparMsg();
            this.verificarAutorizacao();

            if (this.evento == null) {
                this.exibirDetalhesEvento();
            }

            // TODO: verificar regras de validade e situação da negociação
//			this.getNegociacaoEvento().setSituacao(Situacao.PENDENTE);

            this.setarHorarios();

            // Caso a negociação ainda não esteja no banco: incluí-la
            if ((this.getNegociacaoEvento().getCodigo() == null) || this.getNegociacaoEvento().getCodigo().equals(0)) {
//				JSFUtilities.storeOnSession("perfil", carregarPerfilEvento.getCodigo());
                this.getNegociacaoEvento().getPerfilEventoTO().setCodigo((Integer) JSFUtilities.getFromSession("perfil"));
                this.getNegociacaoEvento().setDataCadastro(Calendario.hoje());
                this.getNegociacaoEvento().setCodigo(CEControle.getCEFacade().incluirNegociacaoEvento(this.getNegociacaoEvento(), this.getUsuarioLogado().getCodigo()));
                //LOG
                registrarLogCE(this.getNegociacaoEvento(), new NegociacaoEventoTO(), this.getNegociacaoEvento().getCodigo(), "NEGOCIACAO", true);
                // Senão: alterá-la
            } else {
                CEControle.getCEFacade().alterarNegociacaoEvento(this.getNegociacaoEvento(), this.getUsuarioLogado().getCodigo(),
                        this.getProdutosLocacaoAExcluir(), this.getServicosAExcluir(),
                        this.getCondicaoPagamentoAExcluir());
                //LOG
                registrarLogCE(this.getNegociacaoEvento(), this.getNegociacaoEventoLog(), this.getNegociacaoEvento().getCodigo(), "NEGOCIACAO", false);
            }

        } catch (Exception e) {
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
            // return "orcamentoDetalhado";
        }
    }

    /**
     * Cancela a negociação.
     *
     * @throws Exception
     */
    public void cancelarNegociacao() throws Exception {
        // Caso o registro esteja no banco: excluí-lo
        if ((this.getNegociacaoEvento().getCodigo() != null) && !this.getNegociacaoEvento().getCodigo().equals(0)) {
            CEControle.getCEFacade().excluirNegociacaoEvento(this.getNegociacaoEvento());
        }

        // Guardar o código do Evento
        int eventoInteresse = this.getNegociacaoEvento().getCodigoEventoInteresse();
        this.setNegociacaoEvento(new NegociacaoEventoTO());
        this.getNegociacaoEvento().setCodigoEventoInteresse(eventoInteresse);

        // Limpar tela
        this.limparNegociacao();
        for (NegEvCondicaoPagamentoTO cond : this.condicoesPagamento) {
            cond.setSelecionado(false);
        }
    }

    /**
     * Responsável pela consulta a bens de consumo.
     */
    public void consultarBensConsumo() {
        try {
            limparMsg();
            List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getFiltroBensConsumo());
            this.setBensConsumoConsulta(objs);
            this.setMensagemID("operacoes.consulta.sucesso");
        } catch (Exception e) {
            this.setBensConsumoConsulta(null);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Responsável pela consulta a serviços.
     */
    public void consultarServico() {
        try {
            limparMsg();
            List<ServicoTO> objs = CEControle.getCEFacade().consultarServico(filtroServicos);
            this.setServicosConsulta(objs);
            this.setMensagemID("operacoes.consulta.sucesso");
        } catch (Exception e) {
            this.setBensConsumoConsulta(null);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Responsável pela consulta a Fornecedor relacionado com os serviços.
     */
    public void consultarServicoTercerizado() {
        try {
            limparMsg();
            List<ServicoTO> objs = CEControle.getCEFacade().consultarFornServ(filtroServicosTercerizados);
            this.setServicoTercerizadoConsulta(objs);
            this.setMensagemID("operacoes.consulta.sucesso");
        } catch (Exception e) {
            this.setBensConsumoConsulta(null);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Selecionar bem de consumo dentre os consultados.
     */
    public void selecionarBemConsumoConsulta() {
        try {
            limparMsg();
            // Obter bem de consumo selecionado
            ProdutoLocacaoTO obj = (ProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumoConsulta");

            // // Verificar se o bem de consumo já foi adicionado
            // for (NegEvPerfilEventoProdutoLocacaoTO produtoLoc : getBensConsumo()) {
            // if (produtoLoc.getCodigoProdutoLocacao().equals(obj.getCodigo())) {
            // throw new NegocioException("operacoes.adicao.erro.bemConsumoJaRelacionado");
            // }
            // }

            // Inicializar campos de edição do bem de consumo
            NegEvPerfilEventoProdutoLocacaoTO bemConsumo = new NegEvPerfilEventoProdutoLocacaoTO();

            bemConsumo.setCodigoProdutoLocacao(obj.getCodigo());
            bemConsumo.setDescricaoProdutoLocacao(obj.getDescricao());
            bemConsumo.setValorUnitario(obj.getValor());
            bemConsumo.setQuantidade(0);
            bemConsumo.setSelecionado(Boolean.TRUE);
            bemConsumo.setExtra(Boolean.TRUE);

            this.getBensConsumo().add(bemConsumo);

            // Realizar as operações necessárias
            this.arranjarBemConsumoSelecionado(bemConsumo);

        } catch (Exception e) {
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
        } finally {
            this.getBensConsumoConsulta().clear();
            this.setFiltroUtensilios(null);
        }
    }

    /**
     * Responsável pela consulta a utensílios.
     */
    public void consultarUtensilios() {
        try {
            limparMsg();
            List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getFiltroUtensilios());
            this.setUtensiliosConsulta(objs);
            this.setMensagemID("operacoes.consulta.sucesso");
        } catch (Exception e) {
            this.setUtensiliosConsulta(null);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Selecionar utensilio dentre os consultados.
     */
    public void selecionarUtensilioConsulta() {
        try {
            limparMsg();

            // Obter utensílio selecionado
            ProdutoLocacaoTO obj = (ProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilioConsulta");

            // // Verificar se o utensílio já foi adicionado
            // for (NegEvPerfilEventoProdutoLocacaoTO produtoLoc : getUtensilios()) {
            // if (produtoLoc.getCodigoProdutoLocacao().equals(obj.getCodigo())) {
            // throw new NegocioException("operacoes.adicao.erro.utensilioJaRelacionado");
            // }
            // }

            // Inicializar campos de edição do utensílio
            NegEvPerfilEventoProdutoLocacaoTO utensilio = new NegEvPerfilEventoProdutoLocacaoTO();

            utensilio.setCodigoProdutoLocacao(obj.getCodigo());
            utensilio.setDescricaoProdutoLocacao(obj.getDescricao());
            utensilio.setValorUnitario(obj.getValor());
            utensilio.setQuantidade(0);
            utensilio.setSelecionado(true);
            utensilio.setExtra(true);

            this.getUtensilios().add(utensilio);

            // Realizar as operações necessárias
            this.arranjarUtensilioSelecionado(utensilio);

        } catch (Exception e) {
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
        } finally {
            this.getUtensiliosConsulta().clear();
            this.setFiltroUtensilios(null);
        }
    }

    /**
     * Responsável pela consulta a brinquedos.
     */
    public void consultarBrinquedos() {
        try {
            limparMsg();
            List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getFiltroBrinquedos());
            this.setBrinquedosConsulta(objs);
            this.setMensagemID("operacoes.consulta.sucesso");
        } catch (Exception e) {
            this.setBrinquedosConsulta(null);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Selecionar brinquedo dentre os consultados.
     */
    public void selecionarBrinquedoConsulta() {
        try {
            limparMsg();
            // Obter brinquedo selecionado
            ProdutoLocacaoTO obj = (ProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedoConsulta");

            // // Verificar se o brinquedo já foi adicionado
            // for (NegEvPerfilEventoProdutoLocacaoTO produtoLoc : getBrinquedos()) {
            // if (produtoLoc.getCodigoProdutoLocacao().equals(obj.getCodigo())) {
            // throw new NegocioException("operacoes.adicao.erro.brinquedoJaRelacionado");
            // }
            // }

            // Inicializar campos de edição do brinquedo
            NegEvPerfilEventoProdutoLocacaoTO brinquedo = new NegEvPerfilEventoProdutoLocacaoTO();

            brinquedo.setCodigoProdutoLocacao(obj.getCodigo());
            brinquedo.setDescricaoProdutoLocacao(obj.getDescricao());
            brinquedo.setValorUnitario(obj.getValor());
            brinquedo.setQuantidade(0);
            brinquedo.setSelecionado(true);
            brinquedo.setExtra(true);

            this.getBrinquedos().add(brinquedo);

            // Realizar as operações necessárias
            this.arranjarBrinquedoSelecionado(brinquedo);

        } catch (Exception e) {
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
        } finally {
            this.getBrinquedosConsulta().clear();
            this.setFiltroBrinquedos(null);
        }
    }

    /**
     * Selecionar servico dentre os consultados.
     */
    public void selecionarServicoConsulta() {
        try {
            limparMsg();
            // Obter servico selecionado
            ServicoTO obj = (ServicoTO) JSFUtilities.getRequestAttribute("servico");


            // Inicializar campos de edição do servico
            NegEvPerfilEventoServicoTO servico = new NegEvPerfilEventoServicoTO();

            servico.setCodigoServico(obj.getCodigo());
            servico.setDescricaoServico(obj.getDescricao());
            servico.setValorUnitario(obj.getValor());
            servico.setQuantidade(0);
            servico.setSelecionado(true);
            servico.setExtra(true);

            this.getServicos().add(servico);

            // Realizar as operações necessárias
            this.arranjarServicoSelecionado(servico);

        } catch (Exception e) {
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
        } finally {
            this.getServicosConsulta().clear();
            this.setFiltroServicos(null);
        }
    }

    /**
     * Selecionar servico tercerizados dentre os consultados.
     */
    public void selecionarServTerceConsulta() {
        try {
            limparMsg();
            // Obter servico selecionado
            ServicoTO obj = (ServicoTO) JSFUtilities.getRequestAttribute("servicoTercerizado");


            // Inicializar campos de edição do servico
            NegEvServicoTerceirizadoTO servico = new NegEvServicoTerceirizadoTO();
            servico.setDescricao(obj.getDescricao());
            servico.setSelecionado(true);
            servico.getFornecedorServico().setCodServico(obj.getCodigo());
            servico.montarFornecedores(getCEFacade().consultaFornecedorPorServico(obj.getCodigo()));
            servico.setValor(0.0);
            this.getNegociacaoEvento().getServicosTerceirizados().add(servico);

        } catch (Exception e) {
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
        }
    }

    /**
     * Carrega as informações do perfil de evento selecionado; alterando esse
     * perfil caso seja editado
     *
     * @throws Exception
     */
    public void carregarPerfilEvento() throws Exception {
//		limparNegociacao();
        //Seta a variavel que ira mostrar os outros campos do orcamento
        this.setPerfilSelecionado(true);

        Integer codigoPerfil = this.getNegociacaoEvento().getPerfilEventoTO().getCodigo();

        // se o coidigo de negociação do evento diferente de nulo e codigo de negociação de vento igual a zero
        if ((this.getNegociacaoEvento().getCodigo() != null) && this.getNegociacaoEvento().getCodigo().equals(0)) {
            // exclui todos os atributos relacionado a esse evento
            for (NegEvPerfilEventoServicoTO servico : this.getNegociacaoEvento().getServicos()) {
                this.getServicosAExcluir().add(servico.getCodigo());
            }
            for (NegEvPerfilEventoProdutoLocacaoTO bemConsumo : this.getNegociacaoEvento().getBensConsumo()) {
                this.getProdutosLocacaoAExcluir().add(bemConsumo.getCodigo());
            }
            for (NegEvPerfilEventoProdutoLocacaoTO utensilio : this.getNegociacaoEvento().getUtensilios()) {
                this.getProdutosLocacaoAExcluir().add(utensilio.getCodigo());
            }
            for (NegEvPerfilEventoProdutoLocacaoTO brinquedo : this.getNegociacaoEvento().getBrinquedos()) {
                this.getProdutosLocacaoAExcluir().add(brinquedo.getCodigo());
            }
        }

        // Caso não tenha sido selecionado algum perfil de evento, limpar toda a negociação
        if ((codigoPerfil == null) || codigoPerfil.equals(0)) {
            this.limparNegociacao();
            //Seta a variavel que ira mostrar os outros campos do orcamento
            this.setPerfilSelecionado(false);
        } else {
            // Caso algum perfil de evento tenha sido selecionado:
            // Carregá-lo
            PerfilEventoTO carregarPerfilEvento = CEControle.getCEFacade().carregarPerfilEvento(codigoPerfil);
            this.getNegociacaoEvento().setPerfilEventoTO(carregarPerfilEvento);
            JSFUtilities.storeOnSession("perfil", carregarPerfilEvento.getCodigo());
            // Relacionar os ambientes envolvidos ao perfil na negocia
            this.relacionarAmbientesNaNegociacao();
            // Relacionar os bens de consumo envolvidos ao perfil na negociação
            this.relacionarBensConsumoNaNegociacao();
            // Relacionar os utensílios envolvidos ao perfil na negociação
            this.relacionarUtensiliosNaNegociacao();
            // Relacionar os brinquedos envolvidos ao perfil na negociação
            this.relacionarBrinquedosNaNegociacao();
            // Relacionar os serviços envolvidos ao perfil na negociação
            this.relacionarServicosNaNegociacao();

            // Calcular a negociação
            this.calcular();
            limparMensagens();

        }
    }

    /**
     * Limpar as mensagens de quantidade
     */
    public void limparMensagens() {
        qtdAlteradaBensConsumo = false;
        qtdAlteradaBrinquedo = false;
        qtdAlteradaServico = false;
        qtdAlteradaUtensilio = false;
        this.setDataEventoMenorAtual(false);
    }

    /**
     * Limpa toda a negociação, removendo tudo o que havia sido
     * incluso/selecionado.
     *
     * @throws Exception
     */
    private void limparNegociacao() throws Exception {
        this.getNegociacaoEvento().setAmbiente(null);
        this.getNegociacaoEvento().setBensConsumo(null);
        this.getNegociacaoEvento().setBrinquedos(null);
        this.getNegociacaoEvento().setUtensilios(null);
        this.getNegociacaoEvento().setValorTotal(null);
        this.getNegociacaoEvento().setValorFinal(null);
        this.getNegociacaoEvento().setAmbientes(null);
        this.setAmbientes(null);
        this.setTiposLayout(null);
        this.setServicos(null);
        this.setBensConsumo(null);
        this.setUtensilios(null);
        this.setBrinquedos(null);
        this.setCondicoesPagamento(null);
        this.setCondicaoPagamentoSelecionada(false);
    }

    /**
     * Responsável por obter o numero máximo aceito de convidados para um
     * ambiente
     *
     * <AUTHOR> 16/03/2011
     * @param ambiente
     * @return
     */
    private Integer obterNrMaximoConvidadosAmbiente(Integer ambiente) {
        Integer maximoConv = new Integer(0);
        // obter lista de ambientes
        List<PerfilEventoAmbienteTO> perfilEventoAmbientes = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoAmbienteTOs();
        // Percorrer os possíveis ambientes
        for (PerfilEventoAmbienteTO ambienteNeg : perfilEventoAmbientes) {
            if (ambienteNeg.getCodigoAmbiente().equals(ambiente) && ambienteNeg.getNrMaximoConvidados() > maximoConv) {
                maximoConv = ambienteNeg.getNrMaximoConvidados();
            }
        }
        return maximoConv;
    }

    /**
     * Realiza ou desfaz a seleção de um ambiente na negociação.
     *
     * @throws Exception
     */
    public void selecionarAmbiente() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();

        if (this.getNegociacaoEvento().getAmbiente() != null) {
            this.getNegociacaoEvento().getAmbiente().setValor(this.getNegociacaoEvento().getAmbiente().getValorReal());
        }
        // Obter o ambiente selecionado ou cuja seleção foi desfeita
        NegEvPerfilEventoAmbienteTO negAmbiente = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");

        //OBTENDO O CODIGO QUE FOI SELECIONADO NO COMBO
        if (negAmbiente != null && negAmbiente.getCodigoAmbiente() != null) {

            Integer codigo = negAmbiente.getCodigoAmbiente();
            if (codigo != null && codigo > 0) {

                for (int i = 0; i < this.getAmbientes().size() - 1; i++) {
                    NegEvPerfilEventoAmbienteTO ambiente = this.getAmbientes().get(i);
                    if (ambiente != null && ambiente.getCodigoAmbiente() != null) {
                        if (ambiente.getCodigoAmbiente() == codigo) {
                            this.getAmbientes().remove(this.getAmbientes().size() - 1);
                            this.getAmbientes().add(new NegEvPerfilEventoAmbienteTO());
                            // setar mensagem
                            this.setMensagemID("operacoes.selecionado.ambiente");
                            return;
                        }
                    }
                }

                AmbienteVO ambienteVO = null;
                ambienteVO = CEControle.getCEFacade().obterAmbiente(codigo);
                if (ambienteVO != null && ambienteVO.getDescricao() != null) {
                    negAmbiente.setMsg("");
                    negAmbiente.setTipoLayout(null);
                    negAmbiente.setDescricaoAmbiente(ambienteVO.getDescricao());
                    if (negAmbiente.getTpAmbienteTO() == null || negAmbiente.getTpAmbienteTO().getCodigo() == null
                            || negAmbiente.getTpAmbienteTO().getCodigo().intValue() == 0) {
                        TipoAmbienteTO tpAmbienteTO = CEControle.getCEFacade().obterTipoDoAmbiente(ambienteVO.getCodigo());
                        negAmbiente.setTpAmbienteTO(tpAmbienteTO);
                    }
                    List<PerfilEventoAmbienteTO> perfilEventoAmbientes = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoAmbienteTOs();

                    // Percorrer os possíveis ambientes
                    for (PerfilEventoAmbienteTO perfilEventoAmbiente : perfilEventoAmbientes) {
                        if (codigo == perfilEventoAmbiente.getCodigoAmbiente()) {
                            negAmbiente.setCodigoAmbiente(perfilEventoAmbiente.getCodigoAmbiente());
                            negAmbiente.setNrMaximoConvidado(0);
                            negAmbiente.setDescricaoAmbiente(perfilEventoAmbiente.getDescricaoAmbiente());
                            negAmbiente.setHorarioInicial(null);
                            negAmbiente.setHorarioFinal(null);
                            negAmbiente.setHorarioFinalExibicao(null);
                            negAmbiente.setPerfilEventoSazonalidadeTOs(CEControle.getCEFacade().consultarPorPerfilEventoAmbiente(perfilEventoAmbiente.getCodigo()));
                            negAmbiente.setVlrSazonalidade(0.0);
                            break;
                        }
                    }
                }
                negAmbiente.setNrMaximoConvidadoExibicao(this.obterNrMaximoConvidadosAmbiente(negAmbiente.getCodigoAmbiente()));
                negAmbiente.setLayouts(this.getItensLayouts().get(negAmbiente.getCodigoAmbiente()));
                negAmbiente.setSelecionado(true);

                for (TipoLayoutTO tipoLayoutTO : this.getTiposLayout()) {
                    //CRIANDO LISTA DE AMBIENTES POSSIVEIS PARA O PERFIL SELECIONADO
                    itens.add(new SelectItem(tipoLayoutTO.getCodigo(), tipoLayoutTO.getDescricao()));
                    //this.setItensLayouts(itens);
                }
            }

            //Relacionar o ambiente à negociação
            this.getNegociacaoEvento().setAmbiente(negAmbiente);
            this.getNegociacaoEvento().getAmbiente().setValorReal(negAmbiente.getValor());
        } else {
            this.getNegociacaoEvento().setValorTotal(this.getNegociacaoEvento().getValorTotal() - negAmbiente.getValor());

            this.getAmbientes().remove(negAmbiente);

            if (this.getAmbientes().size() <= 0) {
                this.relacionarAmbientesNaNegociacao();
            }
        }

        // Adicionando a lista de ambientes selecionados a lista de ambientes da negociacao
        this.getNegociacaoEvento().setAmbientes(this.getAmbientes());

        //verificar sazonalidade
        CEControle.getCEFacade().verificarSazonalidade(this.getNegociacaoEvento());

        // Calcular a negociação
        this.calcular();
    }

    /**
     * Responsável por
     *
     * <AUTHOR> 17/02/2011
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void escolheNrConvidados() throws Exception {
        limparMsg();
        // Obter o ambiente modificado
        NegEvPerfilEventoAmbienteTO negAmbiente = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");
        negAmbiente.setValorSazonalidadeEmBanco(0.0);
        //verificar ambiente escolhido
        if (UteisValidacao.emptyNumber(negAmbiente.getCodigoAmbiente())) {
            limparAmbiente(negAmbiente);
            this.setMensagemID("campoObrigatorio.definir.ambiente.nrConvidados");
        } else {
            if (UteisValidacao.emptyNumber(negAmbiente.getNrMaximoConvidado())) {
                negAmbiente.setNrMaximoConvidado(new Integer(0));
            }
            // procurar em qual dos ambientes o nr de convidados se encaixa
            List<PerfilEventoAmbienteTO> perfilEventoAmbientes = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoAmbienteTOs();
            // ordenar a lista
            Collections.sort(perfilEventoAmbientes, new ComparadorAmbientes());
            // Percorrer os possíveis ambientes
            int nrConAnterior = 0;
            int maiorNrConvidados = 0;
            boolean escolhido = false;
            for (PerfilEventoAmbienteTO perfilEventoAmbiente : perfilEventoAmbientes) {
                // se o ambiente do perfil for o mesmo do selecionado
                if (perfilEventoAmbiente.getCodigoAmbiente().equals(negAmbiente.getCodigoAmbiente())) {
                    // o nr de convidados escolhido for maior do que o do ambiente percorrido anteriormente
                    if (negAmbiente.getNrMaximoConvidado() > nrConAnterior
                            // o nr de convidados for menor do que o do ambiente percorrido agora
                            && negAmbiente.getNrMaximoConvidado() <= perfilEventoAmbiente.getNrMaximoConvidados()) {
                        // setar valor e sazonalidades no ambiente
                        negAmbiente.setValor(perfilEventoAmbiente.getValor());
                        negAmbiente.setPerfilEventoSazonalidadeTOs(perfilEventoAmbiente.getPerfilEventoSazonalidadeTOs());
                        // informar ambiente encontrado
                        escolhido = true;
                    }
                    // setar o maior nr de convidados possível
                    if (nrConAnterior < perfilEventoAmbiente.getNrMaximoConvidados()) {
                        maiorNrConvidados = perfilEventoAmbiente.getNrMaximoConvidados();
                    }
                    // guardar o nr percorrido
                    nrConAnterior = perfilEventoAmbiente.getNrMaximoConvidados();
                }
            }
            // verificar se o nr de convidados foi setado
            if (escolhido || negAmbiente.getNrMaximoConvidado().equals(0)) {
                this.calcular();
            } else {
                limparAmbiente(negAmbiente);
                this.calcular();
                this.setMensagem(this.getMensagemInternalizacao("operacoes.nrConvidados") + maiorNrConvidados + ".");
            }
        }

    }

    /**
     * Responsável por limpar um Ambiente da negociacao
     *
     * <AUTHOR> 17/02/2011
     * @param negAmbiente
     */
    private void limparAmbiente(NegEvPerfilEventoAmbienteTO negAmbiente) {
        negAmbiente.setNrMaximoConvidado(0);
        negAmbiente.setValor(0.0);
        negAmbiente.setPerfilEventoSazonalidadeTOs(new ArrayList<PerfilEventoSazonalidadeTO>());
    }

    public void fornecedorListener(ValueChangeEvent valueChangeEvent) throws Exception {
        NegEvPerfilEventoServicoTO servicoParaAlterar = (NegEvPerfilEventoServicoTO) JSFUtilities.getRequestAttribute("servico");
        FornecedorVO fornecedor = getFacade().getFornecedor().obter(Integer.parseInt(valueChangeEvent.getNewValue().toString()));
        servicos.get(servicos.indexOf(servicoParaAlterar)).setDescricaoFornecedor(fornecedor.getDescricao());
    }

    /**
     * Comparator para ordenação de lista de ambientes, ordenando por nr de
     * convidados
     *
     * <AUTHOR>
     *
     */
    @SuppressWarnings("unchecked")
    public class ComparadorAmbientes implements Comparator {

        public int compare(Object o1, Object o2) {
            PerfilEventoAmbienteTO p1 = (PerfilEventoAmbienteTO) o1;
            PerfilEventoAmbienteTO p2 = (PerfilEventoAmbienteTO) o2;
            return p1.getNrMaximoConvidados() - p2.getNrMaximoConvidados();
        }
    }

    /**
     * Responsavel por remover um ambiente do evento
     *
     * @throws Exception
     */
    public void removerAmbiente() throws Exception {
        limparMsg();
        // Obter o ambiente
        NegEvPerfilEventoAmbienteTO negAmbiente = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");
        //remover o ambiente
        this.getAmbientes().remove(negAmbiente);
        this.getNegociacaoEvento().getAmbientes().remove(negAmbiente);
        //se a lista ficou vazia, adicionar um objeto vazio
        if (this.getAmbientes().isEmpty()) {
            this.getAmbientes().add(new NegEvPerfilEventoAmbienteTO());
            // setar os horarios da negociacao
            this.getNegociacaoEvento().setHorarioInicial(null);
            this.getNegociacaoEvento().setHorarioFinal(null);
            this.getNegociacaoEvento().setHorarioFinalExibicao(null);
        } else //setar os horários do evento
        {
            percorrerHorarios();
        }
        calcular();
    }

    /**
     * Método que verificará a disponibilidade de um Dia, usado na tela de
     * orçamento detalhado
     *
     * @throws Exception
     */
    public void verificarDisponibilidadeAmbienteOrcDet(ActionEvent event) throws Exception {
        // Obter o ambiente selecionado ou cuja seleção foi desfeita
        NegEvPerfilEventoAmbienteTO negAmbiente = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");

        this.getNegociacaoEvento().setAmbiente(negAmbiente);
    }

    public void novoAmbiente() throws Exception {
        limparMsg();
        NegEvPerfilEventoAmbienteTO nepeaTO = new NegEvPerfilEventoAmbienteTO();
        nepeaTO.setValor(0.0);
        this.getAmbientes().add(nepeaTO);
        this.getNegociacaoEvento().setAmbiente(nepeaTO);

    }

    /**
     * @param event
     * @throws Exception
     */
    public void selNovoAmbiente(ActionEvent event) throws Exception {
        limparMsg();
        NegEvPerfilEventoAmbienteTO negAmbiente = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");
        validarAmbiente(negAmbiente);

    }

    public void setarHorarioParaAmbiente(String horaInicial, String horaFinal) throws Exception {
        setarHorariosNegociacao(Formatador.obterHorario(horaInicial.trim()), Formatador.obterHorario(horaFinal.trim()));
    }

    public void validarAmbientes() throws Exception {
        for (NegEvPerfilEventoAmbienteTO negAmbiente : this.getAmbientes()) {
            validarAmbiente(negAmbiente);
        }
    }

    private void validarAmbiente(NegEvPerfilEventoAmbienteTO negAmbiente) throws Exception {
        if (negAmbiente != null) {
            negAmbiente.setMsg("");
        }
        validarHorarioAmbientes(negAmbiente);
        //setar os horários do evento
        percorrerHorarios();
        calcular();
    }

    /**
     * Setar nos horarios da negociacao os horarios do ambiente selecionado
     *
     * @param horaInicial
     * @param horaFinal
     * @throws Exception
     */
    private void setarHorariosNegociacao(Date horaInicial, Date horaFinal) throws Exception {
        this.getNegociacaoEvento().getAmbiente().setHorarioInicial(horaInicial);
        this.getNegociacaoEvento().getAmbiente().setHorarioFinalExibicao(horaFinal);
        this.getNegociacaoEvento().getAmbiente().setHorarioFinal(horaFinal);
        //achar o ambiente correspondente ao que chamou a modal
        for (NegEvPerfilEventoAmbienteTO negEvPerfilEventoAmbienteTO : this.getAmbientes()) {
            //verificar o ambiente
            if (negEvPerfilEventoAmbienteTO.getCodigoAmbiente() != null
                    && this.getNegociacaoEvento().getAmbiente().getCodigoAmbiente() != null
                    && negEvPerfilEventoAmbienteTO.getCodigoAmbiente() == this.getNegociacaoEvento().getAmbiente().getCodigoAmbiente()) {
                //setar o ambiente caso tenha o mesmo código
                negEvPerfilEventoAmbienteTO = this.getNegociacaoEvento().getAmbiente();
            }
        }
        //setar os horários do evento
        percorrerHorarios();
    }

    /**
     * Método responsavel por percorrer todos os ambientes selecionados,
     * buscando identificar qual o horario inicial e qual o final do evento
     *
     * @throws Exception
     */
    private void percorrerHorarios() throws Exception {
        boolean diaPosterior = false;
        // setar os horarios da negociacao (apenas para fim de comparação
        this.getNegociacaoEvento().setHorarioInicial(this.getAmbientes().get(0).getHorarioInicial());
        this.getNegociacaoEvento().setHorarioFinal(this.getAmbientes().get(0).getHorarioFinal());
        this.getNegociacaoEvento().setHorarioFinalExibicao(this.getAmbientes().get(0).getHorarioFinalExibicao());
        // verificar se o evento termina em dia posterior
        if (UteisValidacao.nenhumNulo(this.getNegociacaoEvento().getHorarioFinal(),
                this.getNegociacaoEvento().getHorarioFinalExibicao(),
                this.getNegociacaoEvento().getHorarioInicial())) {

            if (this.getNegociacaoEvento().getHorarioFinalExibicao().before(this.getNegociacaoEvento().getHorarioInicial())) {
                diaPosterior = true;
            }
            for (NegEvPerfilEventoAmbienteTO ambiente : this.getAmbientes()) {
                // verificar datas válidas
                if (UteisValidacao.nenhumNulo(ambiente.getHorarioInicial(), ambiente.getHorarioFinalExibicao())) {
                    // verificar se a data inicial é menor do que a inicial do evento
                    if (this.getNegociacaoEvento().getHorarioInicial().after(ambiente.getHorarioInicial())) {
                        this.getNegociacaoEvento().setHorarioInicial(ambiente.getHorarioInicial());
                    }
                    // verificar dia posterior
                    if ((ambiente.getHorarioFinal() == null && ambiente.getHorarioFinalExibicao() != null)
                            && ambiente.getHorarioInicial().after(ambiente.getHorarioFinalExibicao())) {
                        ambiente.setHorarioFinal(Uteis.getDateTime(ambiente.getHorarioInicial(), 23, 59, 59));
                    }

                    if ((ambiente.getHorarioFinal() == null && ambiente.getHorarioFinalExibicao() != null)
                            && ambiente.getHorarioInicial().before(ambiente.getHorarioFinalExibicao())) {
                        ambiente.setHorarioFinal(ambiente.getHorarioFinalExibicao());
                    }

                    if (!ambiente.getHorarioFinal().equals(ambiente.getHorarioFinalExibicao())) {
                        ambiente.setDiaPosterior(true);
                    }
                    // se o evento termina no dia posterior e o ambiente tambem, o horario final é mudado se
                    // o horario do evento for menor que o do ambiente
                    if ((ambiente.isDiaPosterior() && diaPosterior) || (!ambiente.isDiaPosterior() && !diaPosterior)) {
                        if (this.getNegociacaoEvento().getHorarioFinalExibicao().before(ambiente.getHorarioFinalExibicao())) {
                            this.getNegociacaoEvento().setHorarioFinal(ambiente.getHorarioFinal());
                            this.getNegociacaoEvento().setHorarioFinalExibicao(ambiente.getHorarioFinalExibicao());
                        }
                    } else // se o ambiente termina no dia posterior e o evento não, o horario final é mudado
                    if (ambiente.isDiaPosterior() && !diaPosterior) {
                        this.getNegociacaoEvento().setHorarioFinal(ambiente.getHorarioFinal());
                        this.getNegociacaoEvento().setHorarioFinalExibicao(ambiente.getHorarioFinalExibicao());
                    }
                }
            }
        }
    }

    /**
     * Realiza ou desfaz a seleção de um tipo de layout na negociação.
     *
     * @throws Exception
     */
    public void selecionarTipoLayout() throws Exception {
        // Obter o tipo de layout selecionado ou cuja seleção foi desfeita
        TipoLayoutTO tipoLayout = (TipoLayoutTO) JSFUtilities.getRequestAttribute("tipoLayout");

        // Caso o tipo de layout tenha sido selecionado:
        if (tipoLayout.getSelecionado()) {
            // Relacionar o tipo de layout à negociação
            this.getNegociacaoEvento().getAmbiente().setTipoLayout(tipoLayout);

            // Desfazer a seleção dos outros tipos de layout
            for (TipoLayoutTO tl : this.getTiposLayout()) {
                if (!tipoLayout.equals(tl)) {
                    tl.setSelecionado(false);
                }
            }
        } else {
            // Caso a seleção do tipo de layout foi desfeita
            // Remover o tipo de layout da negociação
            this.getNegociacaoEvento().getAmbiente().setTipoLayout(null);
        }
    }

    /**
     * Realiza ou desfaz a seleção de um serviço na negociação.
     *
     * @throws Exception
     */
    public void selecionarServico() throws Exception {
        limparMensagens();
        // Obter o serviço selecionado ou cuja seleção foi desfeita
        NegEvPerfilEventoServicoTO servico = (NegEvPerfilEventoServicoTO) JSFUtilities.getRequestAttribute("servico");

        adicionarServico(servico);
    }

    /**
     * Responsável por
     *
     * <AUTHOR> 13/04/2011
     * @param servico
     * @throws Exception
     */
    private void adicionarServico(NegEvPerfilEventoServicoTO servico) throws Exception {
        // Caso o serviço tenha sido selecionado:
        if (servico.getSelecionado()) {
            // Relacionar o serviço à negociação
            if (!this.getNegociacaoEvento().getServicos().contains(servico)) {
                this.getNegociacaoEvento().getServicos().add(servico);
            }

            this.getServicosAExcluir().remove(servico.getCodigo());
        } else {
            // Caso a seleção do serviço tenha sido desfeita
            // Remover o serviço da negociação
            this.getNegociacaoEvento().getServicos().remove(servico);

            if ((servico.getCodigo() != null) && !servico.getCodigo().equals(0)) {
                this.getServicosAExcluir().add(servico.getCodigo());
            }
        }

        // Calcular o valor do serviço
        CEControle.getCEFacade().calcularValorServico(servico);

        // Calcular negociação
        this.calcular();
    }

    /**
     * Calcula o valor do serviço cuja quantidade está sendo alterada.
     *
     * @throws Exception
     */
    public void calcularValorServico() throws Exception {
        limparMensagens();
        // Obter o serviço cuja quantidade está sendo alterada
        NegEvPerfilEventoServicoTO servico = (NegEvPerfilEventoServicoTO) JSFUtilities.getRequestAttribute("servico");
        try {
            if (servico.getQuantidade().equals(0)) {
                servico.setValor(0.0);
                return;
            }

            // Calcular o valor
            CEControle.getCEFacade().calcularValorServico(servico);

            // Calcular a negociação
            this.calcular();
        } catch (Exception e) {
            this.qtdAlteradaServico = true;
            alterarQuantidadeServico(servico);

        }
    }

    /**
     * Responsável por trocar a quantidade de um serviço caso esta seja um valor
     * que não está contido em um intervalo
     *
     * <AUTHOR>
     * @param servico
     * @throws Exception
     */
    public void alterarQuantidadeServico(NegEvPerfilEventoServicoTO servico) throws Exception {
        getCEFacade().calcularValorServico(servico);
        // Calcular a negociação
        this.calcular();

    }

    /**
     * Método que monta uma string informando as faixas de quantidade de um
     * serviço ou produto
     *
     * <AUTHOR>
     * @param faixas
     * @return
     */
    public String informarFaixas(List<FaixaQuantidade> faixas) {
        String informe = "";
        int cont = 0;

        for (FaixaQuantidade faixa : faixas) {
            if ((cont != 0) && (cont != faixas.size() - 1)) {
                informe = informe + ", ";
            } else {
                if ((cont != 0) && (cont == faixas.size() - 1)) {
                    informe = informe + " e ";

                }

            }
            informe = informe + faixa.getMinimo() + " a " + faixa.getMaximo();
            cont++;
        }
        return informe;
    }

    /**
     * Ordena as faixas de quantidade
     *
     * @param faixas
     * @return
     */
    public List<FaixaQuantidade> ordenarFaixas(Map<FaixaQuantidade, Double> faixas) {
        List<FaixaQuantidade> faixasOrdenadas = new ArrayList<FaixaQuantidade>();
        int cont = 0;
        for (FaixaQuantidade faixa : faixas.keySet()) {
            if (cont == 0) {
                faixasOrdenadas.add(0, faixa);
            } else {
                if (faixa.getMaximo() <= faixasOrdenadas.get(cont - 1).getMinimo()) {
                    faixasOrdenadas.add(cont, faixasOrdenadas.get(cont - 1));
                    faixasOrdenadas.remove((cont - 1));
                    faixasOrdenadas.add(cont - 1, faixa);
                } else {
                    faixasOrdenadas.add(cont, faixa);
                }
            }
            cont++;
        }
        return faixasOrdenadas;

    }

    /**
     * Responsável por selecionar ou desfazer a seleção de um bem de consumo.
     *
     * @throws Exception
     */
    public void selecionarBemConsumo() throws Exception {
        // Obter o bem de consumo selecionado
        NegEvPerfilEventoProdutoLocacaoTO bemConsumo = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo");

        // Realizar as operações necessárias
        this.arranjarBemConsumoSelecionado(bemConsumo);
    }

    /**
     * Responsável por realizar as operações necessárias quando há seleção ou
     * remoção de uma seleção de um bem de consumo.
     *
     * @param bemConsumo
     * @throws Exception
     */
    private void arranjarBemConsumoSelecionado(final NegEvPerfilEventoProdutoLocacaoTO bemConsumo) throws Exception {
        // Caso o bem de consumo foi selecionado
        if (bemConsumo.getSelecionado()) {
            // Adicioná-lo à lista de bens de consumo relacionados à negociação
            this.getNegociacaoEvento().getBensConsumo().add(bemConsumo);

            // Removê-lo da lista de bens de consumo persistidos que devem ser excluídos
            this.getProdutosLocacaoAExcluir().remove(bemConsumo.getCodigo());
        } else {
            // Caso foi desfeita a seleção do bem de consumo

            // Removê-lo da lista de bens de consumo relacionados à negociação
            this.getNegociacaoEvento().getBensConsumo().remove(bemConsumo);

            // Caso seja um bem de consumo persistido, adicioná-lo à lista de bens de consumo persistidos que devem ser excluídos
            if ((bemConsumo.getCodigo() != null) && !bemConsumo.getCodigo().equals(0)) {
                this.getProdutosLocacaoAExcluir().add(bemConsumo.getCodigo());
            }
        }
        if (bemConsumo.getQuantidade().equals(0)) {
            bemConsumo.setValor(0.0);
            return;
        }
        // Calcular o valor do bem de consumo
        CEControle.getCEFacade().calcularValorProdutoLocacao(bemConsumo);

        this.calcular();
    }

    /**
     * Calcula o valor do bem de consumo cuja quantidade está sendo alterada.
     *
     * @throws Exception
     */
    public void calcularValorBemConsumo() throws Exception {
        limparMensagens();
        // Obter o bem de consumo cuja quantidade está sendo alterada
        NegEvPerfilEventoProdutoLocacaoTO bemConsumo = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo");
        calcularBemConsumo(bemConsumo);
    }

    /**
     * Método extraído do métedo calcularValorBemConsumo, para ser usado em
     * outras partes do orçamento
     *
     * @param bemConsumo
     * @throws Exception
     * <AUTHOR>
     */
    private void calcularBemConsumo(NegEvPerfilEventoProdutoLocacaoTO bemConsumo) throws Exception {
        try {
            if (bemConsumo.getQuantidade().equals(0)) {
                bemConsumo.setValor(0.0);
                bemConsumo.setValorUnitario(0.0);
                return;
            }

            // Calcular o valor
            CEControle.getCEFacade().calcularValorProdutoLocacao(bemConsumo);
            // Calcular a negociação
            this.calcular();
        } catch (Exception e) {
            this.qtdAlteradaBensConsumo = true;
            alterarQuantidadeProduto(bemConsumo);
            this.setFaixasProdutos(informarFaixas(ordenarFaixas(bemConsumo.getFaixasValor())));
        }
    }

    /**
     * Responsável por selecionar ou desfazer a seleção de um utensílio.
     *
     * @throws Exception
     */
    public void selecionarUtensilio() throws Exception {
        // Obter o utensílio selecionado
        NegEvPerfilEventoProdutoLocacaoTO utensilio = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio");

        // Realizar as operações necessárias
        this.arranjarUtensilioSelecionado(utensilio);
    }

    /**
     * Responsável por realizar as operações necessárias quando há seleção ou
     * remoção de uma seleção de um bem de consumo.
     *
     * @param utensilio
     * @throws Exception
     */
    private void arranjarUtensilioSelecionado(final NegEvPerfilEventoProdutoLocacaoTO utensilio) throws Exception {
        // Caso o utensílio foi selecionado
        if (utensilio.getSelecionado()) {
            // Adicioná-lo à lista de utensílios relacionados à negociação
            this.getNegociacaoEvento().getUtensilios().add(utensilio);

            // Removê-lo da lista de utensílios persistidos que devem ser excluídos
            this.getProdutosLocacaoAExcluir().remove(utensilio.getCodigo());
        } else {
            // Caso foi desfeita a seleção do utensílio

            // Removê-lo da lista de utensílios relacionados à negociação
            this.getNegociacaoEvento().getUtensilios().remove(utensilio);

            // Caso seja um utensílio persistido, adicioná-lo à lista de utensílios persistidos que devem ser excluídos
            if ((utensilio.getCodigo() != null) && !utensilio.getCodigo().equals(0)) {
                this.getProdutosLocacaoAExcluir().add(utensilio.getCodigo());
            }
        }
        if (utensilio.getQuantidade().equals(0)) {
            utensilio.setValor(0.0);
            return;
        }
        // Calcular o valor do utensílio
        CEControle.getCEFacade().calcularValorProdutoLocacao(utensilio);

        this.calcular();
    }

    /**
     * Calcula o valor do utensilio cuja quantidade está sendo alterada.
     *
     * @throws Exception
     */
    public void calcularValorUtensilio() throws Exception {
        // Obter o utensílio cuja quantidade está sendo alterada
        NegEvPerfilEventoProdutoLocacaoTO utensilio = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio");
        calcularUtensilio(utensilio);
    }

    /**
     * Método extraído do métedo calcularValorUtensilio, para ser usado em
     * outras partes do orçamento
     *
     * @param utensilio
     * @throws Exception
     * <AUTHOR>
     */
    private void calcularUtensilio(NegEvPerfilEventoProdutoLocacaoTO utensilio) throws Exception {
        limparMensagens();
        try {
            if (utensilio.getQuantidade().equals(0)) {
                utensilio.setValor(0.0);
                utensilio.setValorUnitario(0.0);
                return;
            }

            // Calcular o valor
            CEControle.getCEFacade().calcularValorProdutoLocacao(utensilio);
            // Calcular a negociação
            this.calcular();
        } catch (Exception e) {
            this.qtdAlteradaUtensilio = true;
            alterarQuantidadeProduto(utensilio);
            this.setFaixasProdutos(informarFaixas(ordenarFaixas(utensilio.getFaixasValor())));
        }
    }

    /**
     * Responsável por selecionar ou desfazer a seleção de um brinquedo.
     *
     * @throws Exception
     */
    public void selecionarBrinquedo() throws Exception {
        // Obter o brinquedo selecionado
        NegEvPerfilEventoProdutoLocacaoTO brinquedo = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo");

        // Realizar as operações necessárias
        this.arranjarBrinquedoSelecionado(brinquedo);
    }

    /**
     * Responsável por realizar as operações necessárias quando há seleção ou
     * remoção de uma seleção de um brinquedo.
     *
     * @param brinquedo
     * @throws Exception
     */
    private void arranjarBrinquedoSelecionado(final NegEvPerfilEventoProdutoLocacaoTO brinquedo) throws Exception {
        // Caso o brinquedo foi selecionado
        if (brinquedo.getSelecionado()) {
            // Adicioná-lo à lista de brinquedos relacionados à negociação
            this.getNegociacaoEvento().getBrinquedos().add(brinquedo);

            // Removê-lo da lista de brinquedos persistidos que devem ser excluídos
            this.getProdutosLocacaoAExcluir().remove(brinquedo.getCodigo());
        } else {
            // Caso foi desfeita a seleção do brinquedo

            // Removê-lo da lista de brinquedos relacionados à negociação
            this.getNegociacaoEvento().getBrinquedos().remove(brinquedo);

            // Caso seja um brinquedo persistido, adicioná-lo à lista de brinquedos persistidos que devem ser excluídos
            if ((brinquedo.getCodigo() != null) && !brinquedo.getCodigo().equals(0)) {
                this.getProdutosLocacaoAExcluir().add(brinquedo.getCodigo());
            }
        }
        if (brinquedo.getQuantidade().equals(0)) {
            brinquedo.setValor(0.0);
            return;
        }
        // Calcular o valor do brinquedo
        CEControle.getCEFacade().calcularValorProdutoLocacao(brinquedo);

        this.calcular();
    }

    /**
     * Responsável por realizar as operações necessárias quando há seleção ou
     * remoção de uma seleção de um brinquedo.
     */
    private void arranjarServicoSelecionado(final NegEvPerfilEventoServicoTO servico) throws Exception {
        // Caso o servico foi selecionado
        if (servico.getSelecionado()) {
            // Adicioná-lo à lista de servicos relacionados à negociação
            if (!this.getNegociacaoEvento().getServicos().contains(servico)) {
                this.getNegociacaoEvento().getServicos().add(servico);
            }

            // Removê-lo da lista de servicos persistidos que devem ser excluídos
            this.getServicosAExcluir().remove(servico.getCodigo());
        } else {
            // Caso foi desfeita a seleção do brinquedo

            // Removê-lo da lista de brinquedos relacionados à negociação
            this.getNegociacaoEvento().getServicos().remove(servico);

            // Caso seja um brinquedo persistido, adicioná-lo à lista de brinquedos persistidos que devem ser excluídos
            if ((servico.getCodigo() != null) && !servico.getCodigo().equals(0)) {
                this.getProdutosLocacaoAExcluir().add(servico.getCodigo());
            }
        }
        if (servico.getQuantidade().equals(0)) {
            servico.setValor(0.0);
            return;
        }
        // Calcular o valor do brinquedo
        CEControle.getCEFacade().calcularValorServico(servico);

        this.calcular();
    }

    /**
     * Calcula o valor do brinquedo cuja quantidade está sendo alterada.
     *
     * @throws Exception
     *
     * @throws Exception
     */
    public void calcularValorBrinquedo() throws Exception {
        limparMensagens();
        // Obter o brinquedo cuja quantidade está sendo alterada
        NegEvPerfilEventoProdutoLocacaoTO brinquedo = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo");
        calcularBrinquedo(brinquedo);
    }

    /**
     * Método extraído do métedo calcularValorBrinquedo, para ser usado em
     * outras partes do orçamento
     *
     * @param brinquedo
     * @throws Exception
     * <AUTHOR>
     */
    private void calcularBrinquedo(NegEvPerfilEventoProdutoLocacaoTO brinquedo) throws Exception {
        try {
            if (brinquedo.getQuantidade().equals(0)) {
                brinquedo.setValor(0.0);
                brinquedo.setValorUnitario(0.0);
                return;
            }

            // Calcular o valor
            CEControle.getCEFacade().calcularValorProdutoLocacao(brinquedo);
            // Calcular a negociação
            this.calcular();
        } catch (Exception e) {
            this.qtdAlteradaBrinquedo = true;
            alterarQuantidadeProduto(brinquedo);
            this.setFaixasProdutos(informarFaixas(ordenarFaixas(brinquedo.getFaixasValor())));
        }
    }

    /**
     * @param produto
     * @throws Exception
     */
    public void alterarQuantidadeProduto(NegEvPerfilEventoProdutoLocacaoTO produto) throws Exception {
        for (FaixaQuantidade faixa : produto.getFaixasValor().keySet()) {
            if (produto.getQuantidade() > faixa.getMinimo() && produto.getQuantidade() > faixa.getMaximo()) {
                produto.setQuantidade(faixa.getMaximo());
            }
        }
        // Calcular o valor
        CEControle.getCEFacade().calcularValorProdutoLocacao(produto);
        // Calcular a negociação
        this.calcular();

    }

    /**
     * Realiza ou desfaz a seleção de uma condição de pagamento na negociação.
     *
     * @throws Exception
     */
    public void selecionarCondicaoPagamento() throws Exception {
        // Obter a condição de pagamento selecionada ou cuja seleção foi desfeita
        NegEvCondicaoPagamentoTO condicaoPagamento = null;
        for (NegEvCondicaoPagamentoTO condicaoPag : this.getCondicoesPagamento()) {
            condicaoPag.setSelecionado(Boolean.FALSE);
            if (condicaoPag.getCodigoCondicaoPagamento().equals(this.getCodigoCondicao())) {
                condicaoPag.setSelecionado(Boolean.TRUE);
                condicaoPagamento = condicaoPag;
            }
        }

        if (condicaoPagamento == null) {
            //Verifica se ja existe uma condicao de pagamento ja selecionada na negociacao
            if (!UteisValidacao.valorNulo(this.negociacaoEvento, "condicaoPagamento")) {
                condicaoPagamento = this.negociacaoEvento.getCondicaoPagamento();
            }
        }

        // Caso a condição de pagamento tenha sido selecionada:
        if (condicaoPagamento != null && condicaoPagamento.getSelecionado()) {

            // Caso outra condição de pagamento houvesse sido persistida, marcá-la para exclusão
            if ((this.getNegociacaoEvento().getCondicaoPagamento().getCodigo() != null)
                    && !this.getNegociacaoEvento().getCondicaoPagamento().getCodigo().equals(0)
                    && !this.getNegociacaoEvento().getCondicaoPagamento().getCodigo().equals(condicaoPagamento.getCodigo())) {

                this.setCondicaoPagamentoAExcluir(this.getNegociacaoEvento().getCondicaoPagamento().getCodigo());
            }

            // Caso seja selecionada novamente uma condição de pagamento persistida, desmarcá-la para exclusão
            if ((condicaoPagamento.getCodigo() != null) && !condicaoPagamento.getCodigo().equals(0)) {
                this.setCondicaoPagamentoAExcluir(null);
            }

            // Relacionar a condição de pagamento à negociação
            this.getNegociacaoEvento().setCondicaoPagamento(condicaoPagamento);

            // Desfazer a seleção de outras condições de pagamento
            for (NegEvCondicaoPagamentoTO condicao : this.getCondicoesPagamento()) {
                if (!condicaoPagamento.equals(condicao)) {
                    condicao.setSelecionado(false);
                }
            }
        } else {
            // Caso foi desfeita a seleção da condição de pagamento

            // Caso outra condição de pagamento houvesse sido persistida, marcá-la para exclusão
            if ((this.getNegociacaoEvento().getCondicaoPagamento().getCodigo() != null)
                    && !this.getNegociacaoEvento().getCondicaoPagamento().getCodigo().equals(0)) {
                this.setCondicaoPagamentoAExcluir(this.getNegociacaoEvento().getCondicaoPagamento().getCodigo());
            }

            // Limpar a condição de pagamento da negociação
            this.getNegociacaoEvento().setCondicaoPagamento(null);
        }
    }

    /**
     * Seleciona o medelo de contrato
     */
    public void selecionarModeloContrato() {
        // para modelo de contrato até perfil evento modelo de contrato faça
        for (ModeloContratoTO modeloContrato : this.getNegociacaoEvento().getPerfilEventoTO().getModelosContrato()) {
            // se o códgo do modelo de contrato for igual ao modelo de contrato de negociao do evento
            // negociaçãoEvento recebe o modelo de contrato
            if (modeloContrato.getCodigo().equals(this.getNegociacaoEvento().getModeloContrato().getCodigo())) {
                this.getNegociacaoEvento().setModeloContrato(modeloContrato);
            }
        }
    }

    /**
     * Relaciona os possíveis ambientes descritos no perfil de evento à
     * negociação.
     */
    private void relacionarAmbientesNaNegociacao() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();

        // Obter os ambientes possíveis para o perfil de evento
        List<PerfilEventoAmbienteTO> perfilEventoAmbientes = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoAmbienteTOs();

        // Relacionar os possíveis ambientes
        this.setAmbientes(new ArrayList<NegEvPerfilEventoAmbienteTO>());

        List<AmbienteVO> listaAmbienteVO = null;
        if (this.getNegociacaoEvento().getCodigoEventoInteresse() != null) {
            listaAmbienteVO = getCEFacade().consultarAmbNegociacaoEvento(this.getNegociacaoEvento().getCodigoEventoInteresse());
        }

        // Percorrer os possíveis ambientes
        for (PerfilEventoAmbienteTO perfilEventoAmbiente : perfilEventoAmbientes) {
            NegEvPerfilEventoAmbienteTO ambiente;

            ambiente = new NegEvPerfilEventoAmbienteTO();
            ambiente.setCodigoAmbiente(perfilEventoAmbiente.getCodigoAmbiente());
            ambiente.setDescricaoAmbiente(perfilEventoAmbiente.getDescricaoAmbiente());
            ambiente.setNrMaximoConvidado(perfilEventoAmbiente.getNrMaximoConvidados());
            ambiente.setValor(perfilEventoAmbiente.getValor());
            ambiente.setValorReal(perfilEventoAmbiente.getValor());
            ambiente.setPerfilEventoSazonalidadeTOs(perfilEventoAmbiente.getPerfilEventoSazonalidadeTOs());
            // adiciona os layouts do ambiente
            for (PerfilEventoAmbienteLayoutTO layout : perfilEventoAmbiente.getLayouts()) {
                ambiente.getLayouts().add(new SelectItem(layout.getCodigo(), layout.getNomeArquivo()));
                this.getItensLayouts().put(ambiente.getCodigoAmbiente(), ambiente.getLayouts());
            }
            // }
            boolean contem = false;
            for (SelectItem item : itens) {
                Integer codigo = (Integer) item.getValue();
                if (codigo.equals(ambiente.getCodigoAmbiente())) {
                    contem = true;
                }
            }
            if (!contem) // CRIANDO LISTA DE AMBIENTES POSSIVEIS PARA O PERFIL SELECIONADO
            {
                itens.add(new SelectItem(ambiente.getCodigoAmbiente(), ambiente.getDescricaoAmbiente()));
            }
            this.setItensAmbientes(itens);

            if (listaAmbienteVO != null && this.getNegociacaoEvento().getAmbientes().size() <= 0) {
                for (AmbienteVO ambienteVO : listaAmbienteVO) {
                    if (ambienteVO.getCodigo().equals(ambiente.getCodigoAmbiente())) {
                        this.getAmbientes().add(ambiente);
                        this.getNegociacaoEvento().setAmbiente(ambiente);
                        if (this.getNegociacaoEvento().getAmbientes() == null) {
                            this.getNegociacaoEvento().setAmbientes(new ArrayList<NegEvPerfilEventoAmbienteTO>());
                        }
                        this.getNegociacaoEvento().getAmbientes().add(ambiente);
                    }
                }
            }

//			this.getAmbientes().add(ambiente);
        }

        if (this.getAmbientes().size() <= 0) {
            novoAmbiente();
        }
    }

    /**
     * Relaciona os possíveis bens de consumo descritos no perfil de evento à
     * negociação.
     */
    private void relacionarBensConsumoNaNegociacao() throws Exception {
        // Obter os bens de consumo possíveis para o perfil de evento
        List<PerfilEventoProdutoLocacaoTO> perfilEventoBensConsumo = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoBensConsumo();

        this.setBensConsumo(new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>());

        this.relacionarProdutoLocacao(perfilEventoBensConsumo, this.getNegociacaoEvento().getBensConsumo(), this.getBensConsumo());
    }

    /**
     * Relaciona os possíveis utensílios descritos no perfil de evento à
     * negociação.
     */
    private void relacionarUtensiliosNaNegociacao() throws Exception {
        // Obter os utensílios possíveis para o perfil de evento
        List<PerfilEventoProdutoLocacaoTO> perfilEventoUtensilios = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoUtensilios();

        this.setUtensilios(new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>());

        this.relacionarProdutoLocacao(perfilEventoUtensilios, this.getNegociacaoEvento().getUtensilios(), this.getUtensilios());
    }

    /**
     * Relaciona os possíveis brinquedos descritos no perfil de evento à
     * negociação.
     */
    private void relacionarBrinquedosNaNegociacao() throws Exception {
        // Obter os brinquedos possíveis para o perfil de evento
        List<PerfilEventoProdutoLocacaoTO> perfilEventoBrinquedos = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoBrinquedos();

        this.setBrinquedos(new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>());

        this.relacionarProdutoLocacao(perfilEventoBrinquedos, this.getNegociacaoEvento().getBrinquedos(), this.getBrinquedos());
    }

    /**
     * Relaciona os possíveis Produto de locação descritos no perfil de evento à
     * negociação.
     */
    private void relacionarProdutoLocacao(final List<PerfilEventoProdutoLocacaoTO> perfilEventoProdutos,
            final List<NegEvPerfilEventoProdutoLocacaoTO> produtosRelacionadosNaNegociacao,
            final List<NegEvPerfilEventoProdutoLocacaoTO> produtos) throws Exception {

        List<NegEvPerfilEventoProdutoLocacaoTO> produtosDisponiveis = CEControle.getCEFacade().relacionarProdutosLocacaoNaNegociacao(
                perfilEventoProdutos);

        List<NegEvPerfilEventoProdutoLocacaoTO> produtosExistentes = new ArrayList<NegEvPerfilEventoProdutoLocacaoTO>();
        produtosExistentes.addAll(produtosRelacionadosNaNegociacao);

        // Percorrer os produtos de locação disponíveis para relacioná-los à negociação
        for (NegEvPerfilEventoProdutoLocacaoTO produtoLocacao : produtosDisponiveis) {

            boolean b = produtoLocacao.getObrigatorio();

            // Caso seja alteração de uma negociação:

            // Percorrer os produtos existentes na negociação
            for (int i = 0; i < produtosRelacionadosNaNegociacao.size(); i++) {
                NegEvPerfilEventoProdutoLocacaoTO prod = produtosRelacionadosNaNegociacao.get(i);
                if ((prod.getCodigo() != null) && !prod.getCodigo().equals(0)
                        && prod.getCodigoProdutoLocacao().equals(produtoLocacao.getCodigoProdutoLocacao()) && !prod.getExtra()) {
                    produtoLocacao.setCodigo(prod.getCodigo());
                    produtoLocacao.setTextoLivre(prod.getTextoLivre());
                    produtoLocacao.setQuantidade(prod.getQuantidade());
                    produtoLocacao.setValor(prod.getValor());
                    produtoLocacao.setValorUnitario(prod.getValorUnitario());
                    produtoLocacao.setSelecionado(prod.getSelecionado());
                    produtoLocacao.setTipoDesconto(prod.getTipoDesconto());
                    produtoLocacao.setDesconto(prod.getDesconto());
                    produtosRelacionadosNaNegociacao.remove(i);
                    produtosRelacionadosNaNegociacao.add(i, produtoLocacao);

                    // Remover o brinquedo da lista de brinquedos guardados
                    produtosExistentes.remove(prod);

                    b = false;
                }
            }

            // Caso o produto seja obrigatório selecioná-lo
            if (b) {
                produtoLocacao.setSelecionado(true);
                produtosRelacionadosNaNegociacao.add(produtoLocacao);
            }

            produtos.add(produtoLocacao);
        }

        for (NegEvPerfilEventoProdutoLocacaoTO prod : produtosExistentes) {
            prod.setValorUnitario(prod.getValor() / prod.getQuantidade());
        }

        // Adicionar os produtos que sobraram entre os já existentes
        produtos.addAll(produtosExistentes);
    }

    /**
     * Relaciona os possíveis serviços descritos no perfil de evento à
     * negociação.
     *
     * @throws Exception
     */
    private void relacionarServicosNaNegociacao() throws Exception {
        // Obter os serviços possíveis para o perfil de evento
        List<PerfilEventoServicoTO> perfilEventoServicos = this.getNegociacaoEvento().getPerfilEventoTO().getPerfilEventoServicoTOs();
        Map<Integer, Boolean> obgs = new HashMap<Integer, Boolean>();
        // Relacionar os possíveis serviços
        this.setServicos(CEControle.getCEFacade().relacionarServicosNaNegociacao(perfilEventoServicos));
        //caso seja um novo evento, percorrer lista de serviços identificando os obrigatórios
        if (UteisValidacao.emptyNumber(this.getNegociacaoEvento().getCodigo())) {
            for (NegEvPerfilEventoServicoTO servico : this.getServicos()) {
                if (servico.getObrigatorio()) {
                    adicionarServico(servico);
                }
            }
        } else //guardar as obrigatoriedades num mapa para obtê-las posteriormente
        {
            for (NegEvPerfilEventoServicoTO servico : this.getServicos()) {
                obgs.put(servico.getCodigoServico(), servico.getObrigatorio());
            }
        }
        // Caso seja uma alteração de negociação:
        // Percorrer os serviços já existentes nesta negociação
        for (int i = 0; i < this.getNegociacaoEvento().getServicos().size(); i++) {
            NegEvPerfilEventoServicoTO servicoAAlterar = this.getNegociacaoEvento().getServicos().get(i);
            if ((servicoAAlterar.getCodigo() != null) && !servicoAAlterar.getCodigo().equals(0)) {
                // Percorrer os possíveis serviços
                for (int j = 0; j < this.getServicos().size(); j++) {
                    NegEvPerfilEventoServicoTO servico = this.getServicos().get(j);
                    // Caso o possível serviço seja existente na negociação,
                    // substituir o serviço
                    if (servicoAAlterar.getCodigoServico().equals(servico.getCodigoServico())) {
                        servico.setCodigo(servicoAAlterar.getCodigo());
                        servico.setTextoLivre(servicoAAlterar.getTextoLivre());
                        servico.setQuantidade(servicoAAlterar.getQuantidade());
                        servico.setValor(servicoAAlterar.getValor());
                        servico.setObrigatorio(obgs.get(servico.getCodigoServico()));
                        servico.setValorUnitario(servicoAAlterar.getValorUnitario());
                        servico.setSelecionado(servicoAAlterar.getSelecionado());
                        servico.setCodigoFornecedor(servicoAAlterar.getCodigoFornecedor());
                        servico.setDescricaoFornecedor(servicoAAlterar.getDescricaoFornecedor());

                        this.getNegociacaoEvento().getServicos().remove(i);
                        this.getNegociacaoEvento().getServicos().add(i, servico);
                    }
                }
            }
        }
    }
    private TipoProdutoLocacao tipoProdLoc;
    private Boolean alteracaoValorBemConsumo;
    private Boolean alteracaoValorUtensilio;
    private Boolean alteracaoValorBrinquedo;
    private Boolean alteracaoValorServico;

    /**
     * @return the alteracaoValorServico
     */
    public Boolean getAlteracaoValorServico() {
        if (this.alteracaoValorServico == null) {
            this.alteracaoValorServico = Boolean.FALSE;
        }
        return alteracaoValorServico;
    }

    /**
     * @param alteracaoValorServico the alteracaoValorServico to set
     */
    public void setAlteracaoValorServico(Boolean alteracaoValorServico) {
        this.alteracaoValorServico = alteracaoValorServico;
    }

    /**
     * @return O campo tipoProdLoc.
     */
    public TipoProdutoLocacao getTipoProdLoc() {
        return this.tipoProdLoc;
    }

    /**
     * @param tipoProdLoc O novo valor de tipoProdLoc.
     */
    public void setTipoProdLoc(final TipoProdutoLocacao tipoProdLoc) {
        this.tipoProdLoc = tipoProdLoc;
    }

    /**
     * @return O campo alteracaoValorBemConsumo.
     */
    public Boolean getAlteracaoValorBemConsumo() {
        if (this.alteracaoValorBemConsumo == null) {
            this.alteracaoValorBemConsumo = Boolean.FALSE;
        }
        return this.alteracaoValorBemConsumo;
    }

    /**
     * @param servicoAlteracao the servicoAlteracao to set
     */
    public void setServicoAlteracao(NegEvPerfilEventoServicoTO servicoAlteracao) {
        this.servicoAlteracao = servicoAlteracao;
    }

    /**
     * @return the servicoAlteracao
     */
    public NegEvPerfilEventoServicoTO getServicoAlteracao() {
        return servicoAlteracao;
    }

    /**
     * @param alteracaoValorBemConsumo O novo valor de alteracaoValorBemConsumo.
     */
    public void setAlteracaoValorBemConsumo(final Boolean alteracaoValorBemConsumo) {
        this.alteracaoValorBemConsumo = alteracaoValorBemConsumo;
    }

    /**
     * @return O campo alteracaoValorUtensilio.
     */
    public Boolean getAlteracaoValorUtensilio() {
        if (this.alteracaoValorUtensilio == null) {
            this.alteracaoValorUtensilio = Boolean.FALSE;
        }
        return this.alteracaoValorUtensilio;
    }

    /**
     * @param alteracaoValorUtensilio O novo valor de alteracaoValorUtensilio.
     */
    public void setAlteracaoValorUtensilio(final Boolean alteracaoValorUtensilio) {
        this.alteracaoValorUtensilio = alteracaoValorUtensilio;
    }

    /**
     * @return O campo alteracaoValorBrinquedo.
     */
    public Boolean getAlteracaoValorBrinquedo() {
        if (this.alteracaoValorBrinquedo == null) {
            this.alteracaoValorBrinquedo = Boolean.FALSE;
        }
        return this.alteracaoValorBrinquedo;
    }

    /**
     * @param perfisComAmbiente the perfisComAmbiente to set
     */
    public void setPerfisComAmbiente(List<String> perfisComAmbiente) {
        this.perfisComAmbiente = perfisComAmbiente;
    }

    /**
     * @return the perfisComAmbiente
     */
    public List<String> getPerfisComAmbiente() {
        if (perfisComAmbiente == null) {
            perfisComAmbiente = new ArrayList<String>();
        }
        return perfisComAmbiente;
    }

    /**
     * @param alteracaoValorBrinquedo O novo valor de alteracaoValorBrinquedo.
     */
    public void setAlteracaoValorBrinquedo(final Boolean alteracaoValorBrinquedo) {
        this.alteracaoValorBrinquedo = alteracaoValorBrinquedo;
    }

    public void selTipoProdutoLocacao(final ActionEvent event) {
        Object component = event.getComponent().getAttributes().get("tipoProdutoLocacao");
        this.setTipoProdLoc(TipoProdutoLocacao.getTipoProdutoLocacao(Integer.valueOf(component.toString())));
        switch (this.getTipoProdLoc()) {
            case BENS_DE_CONSUMO:
                this.setProdutoAlteracao((NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo"));
                break;
            case UTENSILIOS:
                this.setProdutoAlteracao((NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio"));
                break;
            case BRINQUEDOS:
                this.setProdutoAlteracao((NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo"));
        }

    }

    public void selAlteraServico(final ActionEvent event) {
        this.setServicoAlteracao((NegEvPerfilEventoServicoTO) JSFUtilities.getRequestAttribute("servico"));
    }

    public void autorizarAlteracaoValorProdutoLocacao() {
        try {
            limparMsg();
            this.checarCredenciais();
            this.verificarAutorizacao();
            this.getProdutoAlteracao().setAlteracaoValor(Boolean.TRUE);
        } catch (Exception e) {
            this.getProdutoAlteracao().setAlteracaoValor(Boolean.FALSE);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    public void autorizarAlteracaoValorServico() {
        try {
            limparMsg();
            this.checarCredenciais();
            this.verificarAutorizacao();
            this.getServicoAlteracao().setAlteracaoValor(Boolean.TRUE);
        } catch (Exception e) {
            this.getServicoAlteracao().setAlteracaoValor(Boolean.FALSE);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    public void confirmarAlteracaoValorProdutoLocacao() throws Exception {
        this.getProdutoAlteracao().setAlteracaoValor(Boolean.FALSE);
        switch (this.getTipoProdLoc()) {
            case BENS_DE_CONSUMO:
                this.calcularValorBemConsumo();
                break;
            case UTENSILIOS:
                this.calcularValorUtensilio();
                break;
            case BRINQUEDOS:
                this.calcularValorBrinquedo();
        }
    }

    /**
     * Confirma a alteração no valor unitário de um serviço extra.
     *
     * @throws Exception
     */
    public void confirmarAlteracaoValorServico() throws Exception {
        this.getServicoAlteracao().setAlteracaoValor(Boolean.FALSE);
        this.calcularValorServico();

    }

    /**
     * Seleciona a data do evento
     *
     * @throws Exception
     */
    public void selecionarDataEvento() throws Exception {
        limparMsg();

        limparMensagens();

        // criar lista de itens
        List<SelectItem> itens = new ArrayList<SelectItem>();

        Boolean listaContemPerfilSelecionado = Boolean.FALSE;
        Boolean haPerfilSelecionado = (this.getNegociacaoEvento().getPerfilEventoTO().getCodigo() != null)
                && !this.getNegociacaoEvento().getPerfilEventoTO().getCodigo().equals(0);

        // verificar se uma data já foi informada
        if (this.getNegociacaoEvento().getDataEvento() != null) {
            // se já foi informada uma data, pesquisar todos os perfis de evento
            List<PerfilEventoTO> perfis = CEControle.getCEFacade().consultarPerfilEvento(new PerfilEventoTO());

            // percorrer os perfis
            for (PerfilEventoTO perfil : perfis) {
                // verificar se a data informada se encaixa no perfil
                Date dataEvento = Uteis.getDataComHoraZerada(this.getNegociacaoEvento().getDataEvento());
                Date dataInicioPerfil = Uteis.getDataComHoraZerada(perfil.getDataInicio());
                Date dataTerminoPerfil = Uteis.getDataComHoraZerada(perfil.getDataTermino());

                if ((dataEvento.after(dataInicioPerfil) || dataEvento.equals(dataInicioPerfil))
                        && (dataEvento.before(dataTerminoPerfil) || dataEvento.equals(dataInicioPerfil))) {
                    // se positivo, inserir perfil na lista de itens
                    itens.add(new SelectItem(perfil.getCodigo(), perfil.getDescricao()));
                    if (haPerfilSelecionado && perfil.getCodigo().equals(this.getNegociacaoEvento().getPerfilEventoTO().getCodigo())) {
                        listaContemPerfilSelecionado = Boolean.TRUE;
                    }
                }
            }
        }

        if (!listaContemPerfilSelecionado) {
            this.getNegociacaoEvento().setPerfilEventoTO(null);
            this.limparNegociacao();
        }
        if (this.getNegociacaoEvento().getAmbiente() != null
                && this.getNegociacaoEvento().getAmbiente().getPerfilEventoSazonalidadeTOs() != null
                && !this.getNegociacaoEvento().getAmbiente().getPerfilEventoSazonalidadeTOs().isEmpty()) {
            //verificar sazonalidades
            this.calcular();
        }

        this.setItensPerfisEvento(itens);
        if (this.getNovoOrcamento()) {
            this.informarPerfisComAmbiente();
        }
    }

    /**
     * Preenche a combo de Perfis de Evento.
     *
     * @return Lista de <code>SelectItem</code> referente aos Perfis de Evento.
     * @throws Exception
     */
    public List<SelectItem> getItensPerfisEvento() throws Exception {
        if (this.itensPerfisEvento == null) {
            if ((this.getNegociacaoEvento().getCodigo() != null) && !this.getNegociacaoEvento().getCodigo().equals(0)) {
                this.selecionarDataEvento();
            } else {
                this.itensPerfisEvento = new ArrayList<SelectItem>();
            }
        }
        return this.itensPerfisEvento;
    }

    public void setItensPerfisEvento(final List<SelectItem> itensPerfisEvento) {
        this.itensPerfisEvento = itensPerfisEvento;
    }

    /**
     * Preenche a combo de Modelos de Contrato.
     *
     * @return Lista de <code>SelectItem</code> referente aos Modelos de
     * Contrato.
     */
    public List<SelectItem> getItensModelosContrato() {
        List<SelectItem> itens = new ArrayList<SelectItem>();

        for (ModeloContratoTO modeloContrato : this.getNegociacaoEvento().getPerfilEventoTO().getModelosContrato()) {
            itens.add(new SelectItem(modeloContrato.getCodigo(), modeloContrato.getNomeArquivo()));
        }

        return itens;
    }

    /**
     * Preenche a combo de Tipos de Desconto.
     *
     * @return Lista de <code>SelectItem</code> referente aos Tipos de Desconto.
     */
    public List<SelectItem> getTiposDesconto() {
        List<SelectItem> itens = new ArrayList<SelectItem>();

        for (FormaCalculo tipoDesconto : FormaCalculo.values()) {
            itens.add(new SelectItem(tipoDesconto.getCodigo(), tipoDesconto.getDescricao()));
        }

        return itens;
    }

    /**
     * Faz o encaminhamento para a página de exibição de orçamento se a data de
     * realização do evento for menor do que a data final do perfil do evento
     *
     * @return
     */
    public String abrirTelaExibeOrcamento() {
        // verificar se a data de realização do evento é menor do que a data final do perfil evento
        try {
            limparMsg();
            if (this.getNegociacaoEvento().getPerfilEventoTO().getDataTermino().before(this.getNegociacaoEvento().getDataEvento())) {
                // caso seja, lançar excessão
                throw new ValidacaoException("parametros.dataNegociacaoMaiorDataPerfil");
            } else {
                // se não for, direcionar até a página de exibição
                return "exibeOrcamento";
            }
        } catch (ValidacaoException e) {
            // trata a exceção
            Exception exTratada = this.tratarMensagemExcecao(e);
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(exTratada.getMessage());
            // recarrega a pagina
            return "orcamentoDetalhado";
        }
    }

    /**
     * Detalha o evento selecionado
     *
     * @throws Exception
     */
    public void exibirDetalhesEvento() throws Exception {
        this.evento = CEControle.getCEFacade().obterEventoInteresse(this.getNegociacaoEvento().getCodigoEventoInteresse());
        this.evento.setInteressado(CEControle.getCEFacade().obterInteressado(this.evento.getInteressado().getCodigo()));

    }

    /**
     * Verifica se o periodo escolhido para o evento não coincide com um já
     * existente
     *
     * <AUTHOR>
     * @param reservasDia
     * @return boolean true para não existencia de conflitos, false para
     * existencia
     */
    public boolean verificaConflitoHorarios(final List<ReservaTO> reservasDia, final TipoAmbienteTO tipoAmbiente) {
        int numEventos = 0;

        limparMsg();

        if (reservasDia != null) {
            for (ReservaTO reserva : reservasDia) {

                if ((this.getNegociacaoEvento().getCodigo() == null) || !this.getNegociacaoEvento().getCodigo().equals(reserva.getCodigo())) {
                    Calendar c = Calendario.getInstance();

                    c.setTime(reserva.getHorarioInicial());
                    Date hrInicial = Uteis.getDateTime(this.getNegociacaoEvento().getHorarioInicial(), c.get(Calendar.HOUR_OF_DAY), c.get(Calendar.MINUTE), c.get(Calendar.SECOND));

                    c.setTime(reserva.getHorarioFinalExibicao());
                    Date hrFinal = Uteis.getDateTime(this.getNegociacaoEvento().getHorarioFinal(), c.get(Calendar.HOUR_OF_DAY), c.get(Calendar.MINUTE), c.get(Calendar.SECOND));
                    // inserir tempo adicional no horario final da reserva
                    hrFinal = Uteis.somarCampoData(hrFinal, Calendar.MINUTE, tipoAmbiente.getTempoAdicionalPosteriorMin());

                    //verificar termino dia posterior
                    if (hrFinal.before(hrInicial)) {
                        hrFinal = Formatador.obterHorario("23:59");
                    }

                    // verificar se o horario inicial do evento está dentro do período da reserva
                    if ((this.getNegociacaoEvento().getHorarioInicial().after(hrInicial) || this.getNegociacaoEvento().getHorarioInicial().equals(hrInicial))
                            && this.getNegociacaoEvento().getHorarioInicial().before(hrFinal)) {
                        this.setMensagemID("operacoes.adicao.erro.periodoIndisponivel");
                        return false;
                    }

                    // verificar se o horario final do evento está dentro do período da reserva
                    if (this.getNegociacaoEvento().getHorarioFinal().after(hrInicial)
                            && (this.getNegociacaoEvento().getHorarioFinal().before(hrFinal) || this.getNegociacaoEvento().getHorarioFinal().equals(hrFinal))) {
                        this.setMensagemID("operacoes.adicao.erro.periodoIndisponivel");
                        return false;
                    }

                    if ((this.getNegociacaoEvento().getHorarioInicial().before(hrInicial) || this.getNegociacaoEvento().getHorarioInicial().equals(hrInicial))
                            && (this.getNegociacaoEvento().getHorarioFinal().after(hrFinal) || this.getNegociacaoEvento().getHorarioFinal().equals(hrFinal))) {
                        this.setMensagemID("operacoes.adicao.erro.periodoIndisponivel");
                        return false;
                    }
                }
                if (reserva.getTipo().equals(TipoReserva.EVENTO)) {
                    numEventos++;
                }
            }
        }
        // verificar se o numero de eventos do dia não vai exceder o máximo permitido pelo tipo com a inclusão de um novo
        if (tipoAmbiente.getQtdMaximaReservasDia() < ((this.getNegociacaoEvento().getCodigo() == null)
                || this.getNegociacaoEvento().getCodigo().equals(0) ? numEventos + 1 : numEventos)) {
            this.setMensagemID("operacoes.adicao.erro.reservasExcedentes");
            return false;
        }
        return true;
    }

    /**
     * Responsavel por preencher os horarios com os horarios de exibição
     *
     * <AUTHOR>
     */
    public void setarHorarios() {
        if (this.getNegociacaoEvento() != null) {
            if (this.getNegociacaoEvento().getHorarioFinalExibicao() != null
                    && this.getNegociacaoEvento().getHorarioFinalExibicao().before(this.getNegociacaoEvento().getHorarioInicial())) {
                this.getNegociacaoEvento().setHorarioFinal(Formatador.obterHorario("23:59"));
            } else {
                this.getNegociacaoEvento().setHorarioFinal(this.getNegociacaoEvento().getHorarioFinalExibicao());
            }
        }
    }

    /*------------- DESCONTO DE PRODUTOS e AMBIENTES -----------*/
    private NegEvPerfilEventoProdutoLocacaoTO produtoLocacaoDesconto;
    private NegEvPerfilEventoAmbienteTO ambienteDesconto;
    private TipoProdutoLocacao tipoProduto;

    /**
     * Responsavel por informar o registro de bem consumo que receberá o
     * desconto
     */
    public void informarDescontoBemConsumo() {
        this.produtoLocacaoDesconto = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo");
        tipoProduto = TipoProdutoLocacao.BENS_DE_CONSUMO;
        this.setDescontoLiberado(Boolean.FALSE);
    }

    /**
     * Responsavel por informar o registro de ambiente que receberá o desconto
     */
    public void informarDescontoAmbiente() {
        this.ambienteDesconto = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");
        this.setDescontoLiberado(Boolean.FALSE);
    }

    public void removerDescontoAmbiente() throws Exception {
        NegEvPerfilEventoAmbienteTO ambienteDesconto = (NegEvPerfilEventoAmbienteTO) JSFUtilities.getRequestAttribute("ambiente");
        ambienteDesconto.setDesconto(0.0);
        ambienteDesconto.setTipoDesconto(null);

        this.calcular();
    }

    /**
     * Responsavel por informar o registro de utensilio que receberá o desconto
     */
    public void informarDescontoUtensilio() {
        this.produtoLocacaoDesconto = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio");
        tipoProduto = TipoProdutoLocacao.UTENSILIOS;
        this.setDescontoLiberado(Boolean.FALSE);
    }

    /**
     * Responsavel por informar o registro de brinquedo que receberá o desconto
     */
    public void informarDescontoBrinquedo() {
        this.produtoLocacaoDesconto = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo");
        tipoProduto = TipoProdutoLocacao.BRINQUEDOS;
        this.setDescontoLiberado(Boolean.FALSE);
    }

    public void removerDescontoBemConsumo() throws Exception {
        NegEvPerfilEventoProdutoLocacaoTO produtoLocacaoDesconto = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo");
        produtoLocacaoDesconto.setDesconto(0.0);
        produtoLocacaoDesconto.setTipoDesconto(null);
        calcularBemConsumo(produtoLocacaoDesconto);
        this.calcular();
    }

    public void removerDescontoBrinquedo() throws Exception {
        NegEvPerfilEventoProdutoLocacaoTO produtoLocacaoDesconto = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo");
        produtoLocacaoDesconto.setDesconto(0.0);
        produtoLocacaoDesconto.setTipoDesconto(null);
        calcularBrinquedo(produtoLocacaoDesconto);
        this.calcular();
    }

    public void removerDescontoUtensilio() throws Exception {
        NegEvPerfilEventoProdutoLocacaoTO produtoLocacaoDesconto = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio");
        produtoLocacaoDesconto.setDesconto(0.0);
        produtoLocacaoDesconto.setTipoDesconto(null);
        calcularUtensilio(produtoLocacaoDesconto);
        this.calcular();
    }

    /**
     * @return o produto que receberá o desconto
     */
    public NegEvPerfilEventoProdutoLocacaoTO getProdutoLocacaoDesconto() {
        return produtoLocacaoDesconto;
    }

    /**
     * Liberar para o usuario cadastrar o desconto após o mesmo ter informado
     * username e senha corretos
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void liberarDesconto() {
        try {
            //checar username e senha
            this.checarCredenciais();
            //liberar desconto
            this.setDescontoLiberado(Boolean.TRUE);
            limparMsg();
            this.setMensagemDetalhada("");
        } catch (Exception e) {
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * REsponsavel por confirmar o desconto indicado pelo usuario
     *
     * @throws Exception
     * <AUTHOR>
     * @throws Exception
     */
    public void confirmarDesconto() {
        try {
            limparMsg();
            this.verificarAutorizacao();
            if (getCEFacade().permiteDesconto(produtoLocacaoDesconto.getValor(), produtoLocacaoDesconto.getDesconto(),
                    produtoLocacaoDesconto.getTipoDesconto())) {

                //verificar qual tipo de produto foi escolhido
                switch (tipoProduto) {
                    //calcular de acordo com cada tipo
                    case BENS_DE_CONSUMO:
                        calcularBemConsumo(produtoLocacaoDesconto);
                        break;
                    case BRINQUEDOS:
                        calcularBrinquedo(produtoLocacaoDesconto);
                        break;
                    case UTENSILIOS:
                        calcularUtensilio(produtoLocacaoDesconto);
                        break;
                }
                produtoLocacaoDesconto = null;
            } else {
                produtoLocacaoDesconto.setTipoDesconto(null);
                produtoLocacaoDesconto.setDesconto(0.0);
                // setar mensagem
                this.setMensagemID("operacoes.desconto.valorNaoPermitido");
            }
        } catch (Exception e) {
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    /**
     * Responsável por calcular e validar o desconto informado para o ambiente
     *
     * <AUTHOR> 11/03/2011
     * @throws Exception
     */
    public void confirmarDescontoAmbiente() {
        try {
            this.verificarAutorizacao();
            limparMsg();
            //validar o desconto
            if (getCEFacade().permiteDesconto(ambienteDesconto.getValor(), ambienteDesconto.getDesconto(),
                    ambienteDesconto.getTipoDesconto())) {
                this.calcular();
                ambienteDesconto = null;
            } else {
                ambienteDesconto.setTipoDesconto(null);
                ambienteDesconto.setDesconto(0.0);
                // setar mensagem
                this.setMensagemID("operacoes.desconto.valorNaoPermitido");
            }
        } catch (Exception e) {
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada("msg_erro", e.getMessage());

        }



    }
    /* ------------ EDIÇÃO TEXTO LIVRE ------------- */
    private NegEvPerfilEventoServicoTO servicoTextoLivreEditado;

    /**
     * Exibe o texto livre do serviço
     */
    public void exibirTextoLivreServico() {
        this.servicoTextoLivreEditado = (NegEvPerfilEventoServicoTO) JSFUtilities.getRequestAttribute("servico");
        this.setTextoLivre(this.servicoTextoLivreEditado.getTextoLivre());
    }
    private NegEvServicoTerceirizadoTO servTerceirizadoTextoLivreEditado;

    /**
     * Exibe o texto livre do serviço
     */
    public void exibirTextoLivreServTerceirizado() {
        this.servTerceirizadoTextoLivreEditado = (NegEvServicoTerceirizadoTO) JSFUtilities.getRequestAttribute("servicoTerceirizado");
        this.setTextoLivre(this.servTerceirizadoTextoLivreEditado.getTextoLivre());
    }
    private NegEvPerfilEventoProdutoLocacaoTO bemConsumoTextoLivreEditado;

    /**
     * Exibe o texto livre do BemConsumo
     */
    public void exibirTextoLivreBemConsumo() {
        this.bemConsumoTextoLivreEditado = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("bemConsumo");
        this.setTextoLivre(this.bemConsumoTextoLivreEditado.getTextoLivre());
    }
    private NegEvPerfilEventoProdutoLocacaoTO utensilioTextoLivreEditado;

    /**
     * Exibe o texto livre do Utensilio
     */
    public void exibirTextoLivreUtensilio() {
        this.utensilioTextoLivreEditado = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("utensilio");
        this.setTextoLivre(this.utensilioTextoLivreEditado.getTextoLivre());
    }
    private NegEvPerfilEventoProdutoLocacaoTO brinquedoTextoLivreEditado;

    /**
     * Exibe o texto livre do Brinquedo
     */
    public void exibirTextoLivreBrinquedo() {
        this.brinquedoTextoLivreEditado = (NegEvPerfilEventoProdutoLocacaoTO) JSFUtilities.getRequestAttribute("brinquedo");
        this.setTextoLivre(this.brinquedoTextoLivreEditado.getTextoLivre());
    }
    private NegEvCaucaoCreditoTO caucaoTextoLivreEditado;

    /**
     * Exibe o texto livre do Brinquedo
     */
    public void exibirTextoLivreCaucao() {
        this.caucaoTextoLivreEditado = (NegEvCaucaoCreditoTO) JSFUtilities.getRequestAttribute("caucaoCredito");
        this.setTextoLivre(this.caucaoTextoLivreEditado.getObservacao());
    }

    /**
     * Fecha a edição do texto livre
     */
    public void fecharEdicaoTextoLivre() {
        if (this.servicoTextoLivreEditado != null) {
            this.servicoTextoLivreEditado.setTextoLivre(this.getTextoLivre());
            this.servicoTextoLivreEditado = null;
        } else if (this.bemConsumoTextoLivreEditado != null) {
            this.bemConsumoTextoLivreEditado.setTextoLivre(this.getTextoLivre());
            this.bemConsumoTextoLivreEditado = null;
        } else if (this.utensilioTextoLivreEditado != null) {
            this.utensilioTextoLivreEditado.setTextoLivre(this.getTextoLivre());
            this.utensilioTextoLivreEditado = null;
        } else if (this.servTerceirizadoTextoLivreEditado != null) {
            this.servTerceirizadoTextoLivreEditado.setTextoLivre(this.getTextoLivre());
            this.servTerceirizadoTextoLivreEditado = null;
        } else if (this.servTerceirizadoTextoLivreEditado != null) {
            this.servTerceirizadoTextoLivreEditado.setTextoLivre(this.getTextoLivre());
            this.servTerceirizadoTextoLivreEditado = null;
        } else if (this.brinquedoTextoLivreEditado != null) {
            this.brinquedoTextoLivreEditado.setTextoLivre(this.getTextoLivre());
            this.brinquedoTextoLivreEditado = null;
        } else {
            this.caucaoTextoLivreEditado.setObservacao(this.getTextoLivre());
            this.caucaoTextoLivreEditado = null;
        }
        this.setTextoLivre(null);
    }

    /**
     * Consulta o responsável pelo contrato, caso existam alteração
     */
    public void consultarResponsavel() {
        try {
            limparMsg();

            this.getNegEvContrato().setResponsavelContrato(
                    new Usuario().consultarPorChavePrimaria(this.getNegEvContrato().getResponsavelContrato().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            this.setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsável por salvar a negociação (caso não esteja salva),
     * montar o contrato da mesma e verificar se o cliente já existe, alterando
     * o fluxo se não existir.
     *
     * @throws Exception
     */
    public void autorizarFecharNegociacao() throws Exception {
        limparMsg();

//		if (!this.negociacaoEvento.getEventoReserva()){
//			this.setMensagemID("operacoes.salvar.negociacao.erro.eventoSemReserva");
//			return;
//		}
        if ((this.evento != null && this.evento.getSituacao() != null
                && !this.evento.getSituacao().equals(Situacao.PRE_RESERVADO))) {
            this.setMensagemID("operacoes.salvar.negociacao.erro.eventoSemReserva");
            return;
        }

        // o método exibirDetalhesEvento pode ser chamado aqui pois apenas inicializa o evento, de acordo com o codigo do mesmo
        this.exibirDetalhesEvento();

        if ((this.negociacaoEvento.getCodigo() == null) || this.negociacaoEvento.getCodigo().equals(0)) {
            this.getNegociacaoEvento().setSituacao(Situacao.PENDENTE);

            // Salvar a negociação
            this.salvar();
        }

        // verificar se existe cliente
        if (this.evento.getInteressado().getPessoa().getCodigo() == null || this.evento.getInteressado().getPessoa().getCodigo() == 0) {

            this.setFecharNegociacaoAutorizada(Boolean.FALSE);
            this.setClienteInexistente(Boolean.TRUE);
            this.setMensagemID("operacoes.adicao.erro.clienteInexistente");
            ClienteControle clienteControle = new ClienteControle();
            clienteControle.setGravarInteressado(true);
            clienteControle.novo();
            clienteControle.getClienteVO().setPessoa(this.evento.getInteressado().getPessoa());
            clienteControle.getClienteVO().getEmpresa().setCodigo(this.getNegociacaoEvento().getEmpresa().getCodigo());
            clienteControle.setPessoaVO(this.evento.getInteressado().getPessoa());
            clienteControle.getPessoaVO().setAdicionar(true);
            JSFUtilities.storeOnSession("ClienteControle", clienteControle);
            JSFUtilities.storeOnSession("codigoInteressado", this.evento.getInteressado().getCodigo());
        } else {
            this.setFecharNegociacaoAutorizada(Boolean.TRUE);
            this.setClienteInexistente(Boolean.FALSE);
            negEvContrato = pesquisarContratoEvento();
            if (negEvContrato.getCodigo() == null || negEvContrato.getCodigo() == 0) // montar o contrato
            {
                this.montaNegociacaoEventoContrato();
            }
        }
    }

    /**
     * Monta o objeto negociacaoeventocontrato para inclusão no banco.
     *
     * @throws Exception
     * <AUTHOR>
     *
     */
    public void montaNegociacaoEventoContrato() throws Exception {
        // o nome do evento pode ser recuperado no objeto evento
        this.negEvContrato.setNomeEvento(this.evento.getNomeEvento());
        // empresa logada no sistema
        this.negEvContrato.setEmpresa(this.getNegociacaoEvento().getEmpresa().getCodigo());

        // datas
        this.negEvContrato.setDataEvento(this.negociacaoEvento.getDataEvento());
        this.negEvContrato.setDataPagamento(this.negEvContrato.getDataEvento());
        // vigencia, do dia atual
        this.negEvContrato.setVigenciaDe(Calendario.hoje());
        // até o dia do evento
        this.negEvContrato.setVigenciaAte(this.negociacaoEvento.getDataEvento());
        this.negEvContrato.setVigenciaAteAjustada(this.negociacaoEvento.getDataEvento());
        // valores
        this.negEvContrato.setValorFinal(this.negociacaoEvento.getValorFinal());
        // verificar o tipo do desconto
        if (this.negociacaoEvento.getTipoDesconto() != null) {
            if (this.negociacaoEvento.getTipoDesconto().equals(FormaCalculo.PERCENTUAL.getCodigo())) {
                // caso seja percentual, calcular o valor decimal do desconto
                this.negEvContrato.setValorDescontoPercentual(this.negociacaoEvento.getDesconto());
                Double desconto = this.negociacaoEvento.getValorTotal() / 100 * this.negociacaoEvento.getDesconto();
                this.negEvContrato.setValorDesconto(desconto);
                this.negEvContrato.setValorDescontoEspecifico(desconto);
            } else {
                // caso seja decimal, calcular o valor percentual do desconto
                this.negEvContrato.setValorDescontoPercentual(100 * this.negociacaoEvento.getDesconto()
                        / this.negociacaoEvento.getValorTotal());
                this.negEvContrato.setValorDesconto(this.negociacaoEvento.getDesconto());
                this.negEvContrato.setValorDescontoEspecifico(this.negociacaoEvento.getDesconto());
            }
        }

        this.negEvContrato.setSomaProduto(this.negociacaoEvento.getValorFinal());
        this.negEvContrato.setValorBaseCalculo(this.negociacaoEvento.getValorFinal());
        // observacao - texto livre da negociacao
        this.negEvContrato.setObservacao(this.negociacaoEvento.getTextoLivre());
        // verificar forma de pagamento
        if (this.negociacaoEvento.getCondicaoPagamento().getNrParcelas() > 1) {
            this.negEvContrato.setDividirProdutoNasParcelas(true);
        } else {
            this.negEvContrato.setDividirProdutoNasParcelas(false);
        }
        // TODO verificar este campo
        this.negEvContrato.setPagarComBoleto(false);
        // perfil evento escolhido na negociacao
        this.negEvContrato.setPerfilEvento(this.negociacaoEvento.getPerfilEventoTO().getCodigo());
        // valor 1 = ATIVO
        this.negEvContrato.setSituacao(1);
        this.negEvContrato.setCodigoEvento(this.evento.getCodigo());
        // setar pessoa
        this.negEvContrato.setPessoa(this.evento.getInteressado().getPessoa().getCodigo());
        this.negEvContrato.setResponsavelContrato(getUsuarioLogado());
    }

    /**
     * Armazenar atributo na sessão para recuperar na tela de pagamento
     */
    public void colocarCodigoContratoNaSessao() {
        JSFUtilities.storeOnSession("codigoContratoEvento", this.negEvContrato.getCodigo());
    }

    /**
     *
     * @param event
     * @throws Exception
     */
    public void selFecharNegociacao(ActionEvent event) throws Exception {
        Object component = event.getComponent().getAttributes().get("codigoEvento");
        getNegociacaoEvento().setCodigoEventoInteresse(Integer.valueOf(component.toString()));

        abrirOrcamentoDetalhado();
        autorizarFecharNegociacao();
    }

    /**
     *
     * @param event
     * @throws Exception
     */
    public void selCarregarNegociacaoPorEvento(ActionEvent event) throws Exception {
        Object component = event.getComponent().getAttributes().get("codigoEvento");
        this.getNegociacaoEvento().setCodigoEventoInteresse(Integer.valueOf(component.toString()));
        colocarNegociacaoNaSessao();
    }

    /**
     * Metodo que modifica o status da negociacao para autorizado e grava uma
     * conversa do tipo REALIZAR_CHECKLIST
     *
     * Autor: Pedro Y. Saito Criado em 15/02/2011
     */
    public String autorizarEvento() throws Exception {
        try {
            if (this.getEvento() == null) {
                exibirDetalhesEvento();
            }
            this.verificarAutorizacao();
            limparMsg();

            //checar username e senha
            getFacade().getControleAcesso().verificarLoginUsuario(this.getUsuarioLogado().getUsername().trim(), this.responsavelEncerramento.getSenha().trim().toUpperCase());

            //carregando a negociacao
            this.negociacaoEvento = CEControle.getCEFacade().carregarNegociacaoEventoPorEventoInteresse(this.negociacaoEvento.getCodigoEventoInteresse());
            //carregadno evento interesse
            EventoInteresseVO eventoInteresseVO = CEControle.getCEFacade().obterEventoInteresse(this.negociacaoEvento.getCodigoEventoInteresse());

            //autorizando o evento
            CEControle.getCEFacade().autorizarEvento(this.negociacaoEvento.getCodigoEventoInteresse(), this.getUsuarioLogado().getCodigo(), eventoInteresseVO.getInteressado().getCodigo(), this.getMensagemInternalizacao(TipoContato.REALIZACAO_CHECKLIST.getTextoID()));

            getCEFacade().registrarMudancaSituacao(this.getEvento().getSituacao(), Situacao.AUTORIZADO,
                    this.getNegociacaoEvento().getCodigoEventoInteresse(), getUsuarioLogado().getNome());

            CadastroInicialControle cico = (CadastroInicialControle) FacesContext.getCurrentInstance().getExternalContext().getRequestMap().get("CadastroInicialControle");
            if (cico != null) {
                return cico.abrirDetalhamento();
            } else {
                return "detalhamentoEvento";
            }

        } catch (Exception e) {
            this.setMensagemID("operacoes.erro");
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    /**
     * Armazenar atributo na sessão para recuperar em um outro controle
     */
    public void colocarNegociacaoNaSessao() {
        JSFUtilities.storeOnSession("negociacaoEvento", this.negociacaoEvento);

    }

    /**
     * Actionlistener que coloca a negociação na sessaão
     */
    public void selCadastrarEvento(ActionEvent event) {
        setarHorarios();
        colocarNegociacaoNaSessao();

    }

    /**
     * @param faixasProdutos the faixasProdutos to set
     */
    public void setFaixasProdutos(String faixasProdutos) {
        this.faixasProdutos = faixasProdutos;
    }

    /**
     * @return the faixasProdutos
     */
    public String getFaixasProdutos() {
        return faixasProdutos;
    }

    /**
     * @param descontoLiberado the descontoLiberado to set
     */
    public void setDescontoLiberado(Boolean descontoLiberado) {
        this.descontoLiberado = descontoLiberado;
    }

    public Boolean getDescontoLiberado() {
        if (descontoLiberado == null) {
            descontoLiberado = Boolean.FALSE;
        }
        return descontoLiberado;
    }

    /**
     * @param novoOrcamento the novoOrcamento to set
     */
    public void setNovoOrcamento(Boolean novoOrcamento) {
        this.novoOrcamento = novoOrcamento;
    }

    /**
     * @return the novoOrcamento
     */
    public Boolean getNovoOrcamento() {
        if (novoOrcamento == null) {
            novoOrcamento = Boolean.FALSE;
        }
        return novoOrcamento;
    }

    /**
     * @param faixasServico the faixasServico to set
     */
    public void setFaixasServico(String faixasServico) {
        this.faixasServico = faixasServico;
    }

    /**
     * @return the faixasServico
     */
    public String getFaixasServico() {
        return faixasServico;
    }

    /**
     * @param clienteInexistente the clienteInexistente to set
     */
    public void setClienteInexistente(Boolean clienteInexistente) {

        this.clienteInexistente = clienteInexistente;
    }

    /**
     * @return the clienteInexistente
     */
    public Boolean getClienteInexistente() {
        if (clienteInexistente == null) {
            clienteInexistente = Boolean.FALSE;
        }
        return clienteInexistente;
    }

    public void setFecharNegociacaoAutorizada(Boolean fecharNegociacaoAutorizada) {
        this.fecharNegociacaoAutorizada = fecharNegociacaoAutorizada;
    }

    /**
     * @param produtoAlteracao the produtoAlteracao to set
     */
    public void setProdutoAlteracao(NegEvPerfilEventoProdutoLocacaoTO produtoAlteracao) {
        this.produtoAlteracao = produtoAlteracao;
    }

    /**
     * @return the produtoAlteracao
     */
    public NegEvPerfilEventoProdutoLocacaoTO getProdutoAlteracao() {
        return produtoAlteracao;
    }

    /**
     * @return the fecharNegociacao
     */
    public Boolean getFecharNegociacaoAutorizada() {
        if (fecharNegociacaoAutorizada == null) {
            fecharNegociacaoAutorizada = Boolean.FALSE;
        }
        return fecharNegociacaoAutorizada;
    }

    public NegociacaoEventoContratoTO pesquisarContratoEvento() throws Exception {
        NegociacaoEventoContratoTO negEvContrato = new NegociacaoEventoContratoTO();
        negEvContrato.setCodigoEvento(negociacaoEvento.getCodigoEventoInteresse());
        List<NegociacaoEventoContratoTO> negEvContratos = getCEFacade().consultarNegociacaoEventoContrato(negEvContrato);
        if (negEvContratos != null && !negEvContratos.isEmpty()) {
            negEvContrato = negEvContratos.get(0);
        }
        return negEvContrato;
    }

    /**
     * Salva a negociacao setando como reserva
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void salvarReserva() throws Exception {
        //setar o evento como reserva
        getCEFacade().alterarReservaNegociacao(this.getNegociacaoEvento(), Boolean.TRUE, Situacao.PRE_RESERVADO);
        //salvar reserva
        CEControle.getCEFacade().atualizarPreReserva(this.getNegociacaoEvento().getCodigoEventoInteresse(), Calendario.hoje());
        //registrar contato
        getCEFacade().salvarContato(TipoContato.SALVAR_ORCAMENTO,
                this.getNegociacaoEvento().getCodigoEventoInteresse(),
                this.getMensagemInternalizacao(TipoContato.SALVAR_ORCAMENTO.getTextoID()),
                this.getUsuarioLogado().getCodigo(), this.getEvento().getInteressado().getCodigo(),
                this.getNegociacaoEvento(), null);
        //atualizando status do evento
        CEControle.getCEFacade().alterarSituacaoEvento(this.getNegociacaoEvento().getCodigoEventoInteresse(), Situacao.PRE_RESERVADO);
        if (!this.getEvento().getSituacao().equals(Situacao.PRE_RESERVADO)) {
            CEControle.getCEFacade().registrarMudancaSituacao(this.getEvento().getSituacao(),
                    Situacao.PRE_RESERVADO, this.getNegociacaoEvento().getCodigoEventoInteresse(), getUsuarioLogado().getNome());
        }
    }

    /**
     * Salva a negociacao setando como não reserva
     *
     * @throws Exception
     */
    public void salvarSemReserva() throws Exception {

        //setar o evento como reserva
        getCEFacade().alterarReservaNegociacao(this.getNegociacaoEvento(), Boolean.FALSE, Situacao.NAO_RESERVADO);
        //this.salvar();
        //atualizando status do evento
        CEControle.getCEFacade().alterarSituacaoEvento(this.getNegociacaoEvento().getCodigoEventoInteresse(), Situacao.NAO_RESERVADO);
        if (!this.getEvento().getSituacao().equals(Situacao.NAO_RESERVADO)) {
            CEControle.getCEFacade().registrarMudancaSituacao(this.getEvento().getSituacao(),
                    Situacao.NAO_RESERVADO, this.getNegociacaoEvento().getCodigoEventoInteresse(), getUsuarioLogado().getNome());
        }
    }

    public String salvarEncerramento() throws Exception {
        try {
            this.verificarAutorizacao();
            limparMsg();

            //checar username e senha
            getFacade().getControleAcesso().verificarLoginUsuario(this.getUsuarioLogado().getUsername().trim(), this.responsavelEncerramento.getSenha().trim().toUpperCase());

            //carregando a negociacao
            this.negociacaoEvento = CEControle.getCEFacade().carregarNegociacaoEventoPorEventoInteresse(this.negociacaoEvento.getCodigoEventoInteresse());
            //setar o evento como não reserva
            this.negociacaoEvento.setEventoReserva(Boolean.FALSE);
            //setando a situacao para o evento negociado
            this.getNegociacaoEvento().setSituacao(Situacao.ENCERRADO);

            EventoEncerrarVO encerrarEventoVO = new EventoEncerrarVO();
            //setando o encerrarEventoVO
            encerrarEventoVO.setNegociacao(negociacaoEvento);
            encerrarEventoVO.setCodigoUsuarioCadastro(this.getUsuarioLogado().getCodigo());
            encerrarEventoVO.setObservacao(this.getTextoEncerramento());
            EventoInteresseVO eventoInteresseVO = CEControle.getCEFacade().obterEventoInteresse(this.negociacaoEvento.getCodigoEventoInteresse());
            encerrarEventoVO.setCodigoInteressado(eventoInteresseVO.getInteressado().getCodigo());

            //salvar
            CEControle.getCEFacade().encerrarEvento(encerrarEventoVO);
            //registrar log
            //LOG
            registrarLogCE(encerrarEventoVO, new EventoEncerrarVO(), null, "ENCERRAR EVENTO", true);

            CadastroInicialControle cico = (CadastroInicialControle) FacesContext.getCurrentInstance().getExternalContext().getRequestMap().get("CadastroInicialControle");
            if (cico != null) {
                return cico.abrirDetalhamento();
            } else {
                return "detalhamentoEvento";
            }
        } catch (Exception e) {
            this.setMensagemID(e.getMessage());
            return "";
        }
    }

    /**
     * Método responsável por montar uma mensagem ao usuario com o nome dos
     * perfis que possuem o ambiente de interesse
     *
     * @throws Exception
     */
    public void informarPerfisComAmbiente() throws Exception {
        this.setPerfisComAmbiente(CEControle.getCEFacade().listarPerfisPorAmbiente(this.getAmbienteInteresse().getCodigo(), this.getNegociacaoEvento().getDataEvento()));
    }

    /**
     * @return lista com nomes de perfis
     */
    public String getPerfisComAmbienteFormatado() {
        String perfis = null;
        if (!this.getPerfisComAmbiente().isEmpty()) {
            perfis = this.getPerfisComAmbiente().get(0);
        }
        for (int i = 1; i < this.getPerfisComAmbiente().size(); i++) {
            perfis += ", " + this.getPerfisComAmbiente().get(i);
        }
        return perfis;

    }

    /**
     * Responsavel por voltar a pagina de orçamento detalhado para edição de
     * dados
     *
     * @return
     * @throws Exception
     */
    public String editarDados() throws Exception {
        String retorno;
        if (this.evento == null) {
            this.exibirDetalhesEvento();
        }
        if (this.getEvento().getSituacao().equals(Situacao.ENCERRADO)) {
            retorno = "exibeOrcamento";
            this.setMensagemID("operacoes.alterarValores.encerrado");
        } else {
            this.setNovoOrcamento(Boolean.FALSE);
            this.setAmbientes(this.getNegociacaoEvento().getAmbientes());
            this.setarLayoutsDosAmbientes(this.getAmbientes());
            retorno = "orcamentoDetalhado";
        }
        this.setNegociacaoEventoLog(this.getNegociacaoEvento().clone());
        this.setValorEventoAntesAlteracao(this.getNegociacaoEvento().getValorFinal());
        this.setServicos(this.getNegociacaoEvento().getServicos());
        return retorno;

    }

    /**
     * Setar os possiveis layouts dos ambientes.
     *
     * @param ambientes
     */
    public void setarLayoutsDosAmbientes(List<NegEvPerfilEventoAmbienteTO> ambientes) {
        for (NegEvPerfilEventoAmbienteTO ambiente : ambientes) {
            ambiente.setLayouts(this.getItensLayouts().get(ambiente.getCodigoAmbiente()));
        }
    }

    /**
     * Lista de SelectItem de serviços terceirizados
     *
     * @return
     * @throws Exception
     */
    public List<SelectItem> getListaServicoTercerizado() throws Exception {
        List<SelectItem> result;
        try {
            List<SelectItem> itens = new ArrayList<SelectItem>();
            List<FornecedorServicoTO> servTerce = FacadeManager.getFacade().getCentralEventosFacade().consultaFornecedorService();
            for (FornecedorServicoTO servico : servTerce) {
                itens.add(new SelectItem(servico.getCodigo(), servico.getDescServico()));
            }
            result = itens;
        } catch (Exception e) {
            result = null;
        }
        return result;
    }

    /**
     * Validar término do evento em dia posterior e consistência de horários
     *
     * @param ambiente
     * @return boolean indicando validade
     * <AUTHOR>
     */
    public void validarHorarioAmbientes(NegEvPerfilEventoAmbienteTO ambiente) {
        try {
            //validar segundo as regras de negócio
            getCEFacade().validarHorarioAmbientes(ambiente);
        } catch (Exception e) {
            //tratar a excessão
//			this.setMensagemDetalhada(ambiente.getDescricaoAmbiente() +" - " +
//					this.getMensagemInternalizacao(e.getMessage()));
            ambiente.setMsg(ambiente.getDescricaoAmbiente() + " - "
                    + this.getMensagemInternalizacao(e.getMessage()));
        }
    }

    /**
     * Método que retorna uma lista com os fornecedores relacionado ao servico
     *
     * @return os fornecedores
     * @throws Exception
     */
    public List<SelectItem> getListaFornecedor() throws Exception {
        List<SelectItem> result;
        try {
            List<SelectItem> itens = new ArrayList<SelectItem>();
            List<FornecedorVO> fornecedors = getFacade().getFornecedor().consultarFornecedor(false);
            for (FornecedorVO fornecedor : fornecedors) {
                itens.add(new SelectItem(fornecedor.getCodigo(), fornecedor.getPessoa().getNome()));
            }
            result = itens;
        } catch (Exception e) {
            result = new ArrayList<SelectItem>();
        }
        return result;
    }

    /**
     * @param estadoAlteracao the estadoAlteracao to set
     */
    public void setEstadoAlteracao(EstadoAlteracao estadoAlteracao) {
        this.estadoAlteracao = estadoAlteracao;
    }

    /**
     * @return the estadoAlteracao
     */
    public EstadoAlteracao getEstadoAlteracao() {
        if (estadoAlteracao == null) {
            estadoAlteracao = EstadoAlteracao.NAO_PERMITIDA;
        }
        return estadoAlteracao;
    }

    /**
     * @return O campo perfilSelecionado.
     */
    public Boolean getPerfilSelecionado() {
        return this.perfilSelecionado;
    }

    /**
     * @param alteracaoPermitida the alteracaoPermitida to set
     */
    public void setAlteracaoPermitida(Boolean alteracaoPermitida) {
        this.alteracaoPermitida = alteracaoPermitida;
    }

    /**
     * @param negociacaoEventoLog the negociacaoEventoLog to set
     */
    public void setNegociacaoEventoLog(NegociacaoEventoTO negociacaoEventoLog) {
        this.negociacaoEventoLog = negociacaoEventoLog;
    }

    /**
     * @return the negociacaoEventoLog
     */
    public NegociacaoEventoTO getNegociacaoEventoLog() {
        if (negociacaoEventoLog == null) {
            negociacaoEventoLog = new NegociacaoEventoTO();
        }
        return negociacaoEventoLog;
    }

    /**
     * @return the alteracaoPermitida
     */
    public Boolean getAlteracaoPermitida() {
        if (alteracaoPermitida == null) {
            alteracaoPermitida = Boolean.FALSE;
        }
        return alteracaoPermitida;
    }

    /**
     * @param perfilSelecionado O novo valor de perfilSelecionado.
     */
    public void setPerfilSelecionado(Boolean perfilSelecionado) {
        this.perfilSelecionado = perfilSelecionado;
    }

    /**
     * @return O campo itensAmbientes.
     */
    public List<SelectItem> getItensAmbientes() {
        if (this.itensAmbientes == null) {
            this.itensAmbientes = new ArrayList<SelectItem>();
        }
        return this.itensAmbientes;
    }

    /**
     * @param alterarValores the alterarValores to set
     */
    public void setAlterarValores(Boolean alterarValores) {
        this.alterarValores = alterarValores;
    }

    /**
     * @return the alterarValores
     */
    public Boolean getAlterarValores() {
        if (alterarValores == null) {
            alterarValores = Boolean.FALSE;
        }
        return alterarValores;
    }

    /**
     * @param alteraNegociacao the alteraNegociacao to set
     */
    public void setAlteraNegociacao(AlteraValoresTO alteraNegociacao) {
        this.alteraNegociacao = alteraNegociacao;
    }

    /**
     * @return the alteraNegociacao
     */
    public AlteraValoresTO getAlteraNegociacao() {
        if (alteraNegociacao == null) {
            alteraNegociacao = new AlteraValoresTO();
        }
        return alteraNegociacao;
    }

    /**
     * @param itensAmbientes O novo valor de itensAmbientes.
     */
    public void setItensAmbientes(List<SelectItem> itensAmbientes) {
        this.itensAmbientes = itensAmbientes;
    }

    /**
     * @return O campo itensLayouts.
     */
    public Map<Integer, List<SelectItem>> getItensLayouts() {
        if (this.itensLayouts == null) {
            this.itensLayouts = new HashMap<Integer, List<SelectItem>>();
        }
        return this.itensLayouts;
    }

    /**
     * @param itensLayouts O novo valor de itensLayouts.
     */
    public void setItensLayouts(Map<Integer, List<SelectItem>> itensLayouts) {
        this.itensLayouts = itensLayouts;
    }

    /**
     * @return O campo dataEventoMenorAtual.
     */
    public boolean isDataEventoMenorAtual() {
        return this.dataEventoMenorAtual;
    }

    /**
     * @param dataEventoMenorAtual O novo valor de dataEventoMenorAtual.
     */
    public void setDataEventoMenorAtual(boolean dataEventoMenorAtual) {
        this.dataEventoMenorAtual = dataEventoMenorAtual;
    }
    //------------------------------------ INICIO - VALIDAÇÕES --------------------------------------------------//

    /**
     * Testa se algum ambiente da lista não possui um ambiente relacionado ou
     * horarios válidos
     *
     * @return
     */
    private boolean validaAmbientesEvento() {
        boolean validade = true;
        for (NegEvPerfilEventoAmbienteTO ambiente : this.getAmbientes()) {
            //se não possuir ambiente relacionado
            if (UteisValidacao.emptyNumber(ambiente.getCodigoAmbiente())) {
                validade = false;
                this.setMensagemID("campoObrigatorio.ambiente.ambienteRelacionado");
            }
            //se não possuir nr de convidados relacionado
            if (UteisValidacao.emptyNumber(ambiente.getNrMaximoConvidado())) {
                validade = false;
                this.setMensagemID("campoObrigatorio.ambiente.nrConvidados");
            }
            //se não possuir horarios válidas
            if (!UteisValidacao.nenhumNulo(ambiente.getHorarioInicial(), ambiente.getHorarioFinalExibicao())) {
                validade = false;
                this.setMensagemID("campoObrigatorio.ambiente.horarios");
            }
        }
        return validade;
    }

    public boolean validaDataEvento() throws Exception {
        boolean result = true;
        limparMsg();

        if (UteisValidacao.dataMenorDataAtual(this.getNegociacaoEvento().getDataEvento())) {
            result = false;
            this.setMensagemID("operacoes.data.evento.menor");
        }

        return result;
    }

    /**
     * Validar o horário da visita, usado na tela de agenda visita
     *
     * <AUTHOR>
     * @param agenda
     * @return
     * @throws Exception
     */
    public boolean validaVisita(final AgendaVisitaVO agenda) throws Exception {
        this.negociacaoEvento = new NegociacaoEventoTO();
        this.negociacaoEvento.setDataEvento(agenda.getDataVisita());
        this.negociacaoEvento.setHorarioFinal(agenda.getHorarioFinal());
        this.negociacaoEvento.setHorarioInicial(agenda.getHorarioMarcado());
        this.negociacaoEvento.getAmbiente().setCodigoAmbiente(agenda.getAmbiente());
        return this.validaDisponibilidadeHorarioEvento();
    }

    /**
     * Método responsável por verificar se o horario escolhido está disponivel
     * para a realização do evento
     *
     * <AUTHOR>
     * @return false se não possuir o horário livre, verdadeiro se possuir
     * @throws Exception
     */
    public boolean validaDisponibilidadeHorarioEvento() throws Exception {
        boolean existeConflito = false;

        limparMsg();

        for (NegEvPerfilEventoAmbienteTO nepeaTO : this.getNegociacaoEvento().getAmbientes()) {
            //Seta o ambiente que sera verificado a disponibilidade de horario
            this.getNegociacaoEvento().setAmbiente(nepeaTO);

            // consultar o tipo do ambiente escolhido
            TipoAmbienteTO tipoAmbiente = CEControle.getCEFacade().obterTipoDoAmbiente(this.getNegociacaoEvento().getAmbiente().getCodigoAmbiente());
            long periodoEvento;
            if (this.getNegociacaoEvento().getHorarioFinalExibicao().before(this.getNegociacaoEvento().getHorarioInicial())) {
                periodoEvento = Uteis.minutosEntreDatas(this.getNegociacaoEvento().getHorarioInicial(),
                        this.negociacaoEvento.getHorarioFinal()) + Uteis.minutosEntreDatas(
                        Calendario.getDataComHoraZerada(this.getNegociacaoEvento().getHorarioInicial()),
                        this.getNegociacaoEvento().getHorarioFinalExibicao());
                periodoEvento += 1;

            } else {
                periodoEvento = Uteis.minutosEntreDatas(this.getNegociacaoEvento().getHorarioInicial(), this.getNegociacaoEvento().getHorarioFinalExibicao());
            }


            // verificar se o período informado possui a qtd minima de horas do tipo de ambiente
            if (periodoEvento < (tipoAmbiente.getDuracaoMinimaHrs() * 60)) {
                this.setMensagemID("operacoes.adicao.erro.quantidadeMinima");
                return false;
            }


            // verificar se o periodo escolhido para o evento está dentro do periodo de funcionamento do ambiente
            if (nepeaTO.getHorarioInicial().before(tipoAmbiente.getHorarioInicial())
                    || nepeaTO.getHorarioFinal().after(tipoAmbiente.getHorarioFinal())) {
                this.setMensagemID("operacoes.adicao.erro.periodoInvalido");
                this.setMensagemDetalhada("\nO ambiente " + nepeaTO.getDescricaoAmbiente() + " está disponível de "
                        + Formatador.formatarHorario(tipoAmbiente.getHorarioInicial()) + " até "
                        + Formatador.formatarHorario(tipoAmbiente.getHorarioFinal()));

                return false;
            }

            // pegar todas as reservas para o dia do evento
            List<ReservaTO> eventosDia = CEControle.getCEFacade().consultarReservasDia(this.getNegociacaoEvento().getDataEvento(),
                    this.getNegociacaoEvento().getAmbiente().getCodigoAmbiente());

            // verificar se o periodo escolhido para o evento não coincide com um já existente
            existeConflito = this.verificaConflitoHorarios(eventosDia, tipoAmbiente);
        }

        return existeConflito;
    }

    //------------------------------------ FIM - VALIDAÇÕES --------------------------------------------------//
    //------------------------------------ INICIO - ALTERAR VALORES --------------------------------------------------//
    /**
     * Responsavel por analisar as alterações no valor da negociao e direcionar
     * o sistema corretamente
     *
     * <AUTHOR>
     */
    public void autorizaAlteracao() {
        try {

            if(!Calendario.maior(Calendario.hoje(),getEvento().getDataEvento())) {
                if (!validaDisponibilidadeHorarioEvento())
                    throw new ConsistirException(getMensagem());
            }
            this.verificarAutorizacao();
            this.setAlteraNegociacao(new AlteraValoresTO());
            //setar o valor em banco da negociação
            this.getAlteraNegociacao().setValorInicial(this.getValorEventoAntesAlteracao());
            //setar a diferenca entre o valor atual e o valor inicial
            this.getAlteraNegociacao().setDiferenca(this.getNegociacaoEvento().getValorFinal() - this.getAlteraNegociacao().getValorInicial());
            this.getAlteraNegociacao().setDiferencaOriginal(getAlteraNegociacao().getDiferenca().doubleValue());
            //setar o estado que a alteração vai ganhar
            this.setEstadoAlteracao(getCEFacade().autorizaAlteracao(this.getNegociacaoEvento(),
                    this.getAlteraNegociacao().getValorInicial(), this.getAlteraNegociacao().getDiferenca()));
            //se for necessário configurar a alteração, setar a flag de mostrar a modal
            if (this.getEstadoAlteracao().equals(EstadoAlteracao.CONFIGURAR)) {
                this.setAlterarValores(Boolean.TRUE);
                //Preencher lista com parcelas do evento
                preencherParcelas();

                //verificar o crédito que o aluno tem
                getAlteraNegociacao().setValorCredito(0.0);
                List<NegEvCaucaoCreditoTO> creditos = getCEFacade().getNegEvCaucaoCredito().
                        consultarPorNegociacaoEvento(getNegociacaoEvento().getCodigo(), TipoCaucaoCredito.CREDITO, true);
                if (creditos != null) {
                    for (NegEvCaucaoCreditoTO credito : creditos) {
                        getAlteraNegociacao().setCredito(credito);
                        getAlteraNegociacao().setValorCredito(getAlteraNegociacao().getValorCredito() + credito.getValor());
                    }
                }

            } else //se for necessário configurar a alteração, setar a flag de mostrar a modal
            if (this.getEstadoAlteracao().equals(EstadoAlteracao.PERMITIDA)) {
                this.setAlteracaoPermitida(Boolean.TRUE);
            } else if (this.getEstadoAlteracao().equals(EstadoAlteracao.NAO_PERMITIDA)) {
                this.setMensagemID("operacoes.alterarValores.valorMenor");
                this.setAlteracaoPermitida(Boolean.FALSE);
                this.setAlterarValores(Boolean.FALSE);
            }
            if (this.getAlteraNegociacao().getValorInicial() < this.getAlteraNegociacao().getValorPago()) {
                this.getAlteraNegociacao().setDiferenca(this.getNegociacaoEvento().getValorFinal() - this.getAlteraNegociacao().getValorPago());
                this.getAlteraNegociacao().setDiferencaOriginal(getAlteraNegociacao().getDiferenca().doubleValue());
            }
        } catch (Exception e) {
            this.setMensagem(e.getMessage());
            this.setAlterarValores(Boolean.FALSE);

        }
    }

    /**
     * Preencher lista com parcelas do evento
     *
     * @throws Exception
     */
    private void preencherParcelas() throws Exception {
        double valorPago = 0.0;
        double valorAberto = 0.0;
        this.getAlteraNegociacao().setParcelas(getCEFacade().consultarParcelasPorEvento(this.getNegociacaoEvento().getCodigoEventoInteresse()));
        this.getAlteraNegociacao().setNrParcelas(this.getAlteraNegociacao().getParcelas().size());
        Ordenacao.ordenarLista(this.getAlteraNegociacao().getParcelas(), "codigo");
        for (MovParcelaVO parcela : this.getAlteraNegociacao().getParcelas()) {
            if (parcela.getSituacao().equals("PG")) {
                this.getAlteraNegociacao().setValorUnicoPago(parcela.getValorParcela());
                valorPago += parcela.getValorParcela();
                parcela.setParcelaEscolhida(false);
            } else {
                valorAberto += parcela.getValorParcela();
                parcela.setParcelaEscolhida(true);
            }
        }
        this.getAlteraNegociacao().setValorAberto(valorAberto);
        this.getAlteraNegociacao().setValorPago(valorPago);
    }

    /**
     * Preencher lista com parcelas do evento
     *
     * @throws Exception
     */
    public void cancelarAlteracao() throws Exception {
        this.setAlterarValores(Boolean.FALSE);
    }

    /**
     * @throws Exception
     *
     */
    public void montarParcelas() throws Exception {

        montaNegociacaoEventoContrato();
        //se a operacção for gerar novas parcelas
        if (this.getAlteraNegociacao().getOperacao().equals(GERAR_NOVAS_PARCELAS)) {
            getCEFacade().montarNovasParcelas(this.getAlteraNegociacao(), this.getNegociacaoEvento().getValorFinal(),
                    this.getUsuarioLogado(), this.getNegEvContrato());
            this.getAlteraNegociacao().setNrParcelas(getAlteraNegociacao().getNrParcelasNovo());
        } else {
            //se não, alterar as parcelas existentes
            this.getAlteraNegociacao().setParcelasNovas(
                    this.getAlteraNegociacao().getParcelas());
        }
        calcularSobra();
    }

    /**
     * Responsável por calcularSobra
     *
     * <AUTHOR> 11/02/2011
     * @throws Exception
     */
    public void calcularSobra() throws Exception {
        getCEFacade().calcularSobra(this.getAlteraNegociacao(), this.getNegociacaoEvento().getValorFinal(),
                this.getAlteraNegociacao().getValorCredito());
    }

    /**
     * Confirma as alterações e salva
     *
     * @throws Exception
     */
    public String confirmarAlteracoes() throws Exception {
        try {
            this.salvar();

            if (getAlteraNegociacao().getUsarCredito()) {
                this.setAlterarValores(getAlteraNegociacao().getSobraCredito().doubleValue() < 0.0);

                getAlteraNegociacao().getCredito().setDevolvido(escolhaDevolucaoCredito == DEVOLVER_CREDITO
                        || getAlteraNegociacao().getSobraCredito().doubleValue() <= 0.0);
                if (getAlteraNegociacao().getCredito().getDevolvido()) {
                    getFacade().getCentralEventosFacade()
                            .getNegEvCaucaoCredito().incluirDevolucao(
                            Uteis.arredondarForcando2CasasDecimais(getAlteraNegociacao().getSobraCredito()),
                            getUsuario(), Calendario.hoje(), getNegociacaoEvento().getCodigo());
                }

                getAlteraNegociacao().getCredito().setValor(
                        getAlteraNegociacao().getSobraCredito().doubleValue() < 0.0 ? 0.0
                        : getAlteraNegociacao().getSobraCredito().doubleValue());

                getFacade().getCentralEventosFacade().getNegEvCaucaoCredito().alterar(
                        getAlteraNegociacao().getCredito(), getNegociacaoEvento().getCodigo());
            }

            this.setAlteracaoPermitida(Boolean.FALSE);
            if (this.getAlterarValores()) {
                getCEFacade().salvarParcelas(this.getAlteraNegociacao().getParcelasNovas(),
                        this.getNegociacaoEvento().getCodigoEventoInteresse());
                this.setAlterarValores(Boolean.FALSE);
            }
            //LOG
            registrarLogCE(this.getNegociacaoEvento(), this.getNegociacaoEventoLog(), this.getNegociacaoEvento().getCodigo(), "NEGOCIACAO", false);
            CadastroInicialControle controlador = (CadastroInicialControle) getControlador(CadastroInicialControle.class.getSimpleName());
            if (controlador != null) {
                controlador.abrirDetalhamentoSemRedirecionar();
            }
            return "detalhamentoEvento";
        } catch (Exception e) {
            this.setMensagem(e.getMessage());
            this.setAlterarValores(Boolean.FALSE);
            return "orcamentoDetalhado";
        }
    }

    //------------------------------------ FIM - ALTERAR VALORES --------------------------------------------------//
    /**
     * @return O campo responsavelEncerramento.
     */
    public UsuarioVO getResponsavelEncerramento() {
        if (this.responsavelEncerramento == null) {
            this.responsavelEncerramento = new UsuarioVO();
        }
        return this.responsavelEncerramento;
    }

    /**
     * @param responsavelEncerramento O novo valor de responsavelEncerramento.
     */
    public void setResponsavelEncerramento(UsuarioVO responsavelEncerramento) {
        this.responsavelEncerramento = responsavelEncerramento;
    }

    /**
     * @param ambienteDesconto the ambienteDesconto to set
     */
    public void setAmbienteDesconto(NegEvPerfilEventoAmbienteTO ambienteDesconto) {
        this.ambienteDesconto = ambienteDesconto;
    }

    /**
     * @return the ambienteDesconto
     */
    public NegEvPerfilEventoAmbienteTO getAmbienteDesconto() {
        return ambienteDesconto;
    }

    /**
     * @return O campo textoEncerramento.
     */
    public String getTextoEncerramento() {
        return this.textoEncerramento;
    }

    public Boolean getEventoAutorizado() throws Exception {
        if (!this.getOrcamentoFicticio()) {
            if (evento == null) {
                this.exibirDetalhesEvento();
            }
            return (evento.getSituacao().equals(Situacao.CONFIRMADO) || evento.getSituacao().equals(Situacao.AUTORIZADO));
        } else {
            return false;
        }

    }

    /**
     * @param textoEncerramento O novo valor de textoEncerramento.
     */
    public void setTextoEncerramento(String textoEncerramento) {
        this.textoEncerramento = textoEncerramento;
    }

    /**
     * @param codigoCondicao the codigoCondicao to set
     */
    public void setCodigoCondicao(Integer codigoCondicao) {
        this.codigoCondicao = codigoCondicao;
    }

    /**
     * @return the codigoCondicao
     */
    public Integer getCodigoCondicao() {
        return codigoCondicao;
    }

    /**
     * @return O campo caucaoECredito.
     */
    public NegEvCaucaoCreditoTO getCaucaoECredito() {
        if (this.caucaoECredito == null) {
            this.caucaoECredito = new NegEvCaucaoCreditoTO();
        }
        return this.caucaoECredito;
    }

    /**
     * @param caucaoECredito O novo valor de caucaoECredito.
     */
    public void setCaucaoECredito(NegEvCaucaoCreditoTO caucaoECredito) {
        this.caucaoECredito = caucaoECredito;
    }

    /**
     * Metodo utilizado na tela include_modal_clienteInexistente.jsp, para que
     * nao seja mostrado na tela de exibicao do orcamento quando for feito um
     * cadastro de um cliente
     *
     * Autor: Pedro Y. Saito Criado em 23/02/2011
     */
    public void fecharModalClienteInexistente() {
        this.setClienteInexistente(Boolean.FALSE);
    }

    public void setValorEventoAntesAlteracao(Double valorEventoSemAlteracao) {
        this.valorEventoAntesAlteracao = valorEventoSemAlteracao;
    }

    public Double getValorEventoAntesAlteracao() {
        if (valorEventoAntesAlteracao == null) {
            valorEventoAntesAlteracao = new Double(0.0);
        }
        return valorEventoAntesAlteracao;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
        if (listaSelectItemEmpresa == null) {
            montarListaSelectItemEmpresa();
        }
        return listaSelectItemEmpresa;
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        listaSelectItemEmpresa = new ArrayList<SelectItem>();
        if (!getUsuarioLogado().getAdministrador()) {
            return;
        }
        List<EmpresaVO> resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        listaSelectItemEmpresa.add(new SelectItem(0, ""));
        for (EmpresaVO obj : resultadoConsulta) {
            listaSelectItemEmpresa.add(new SelectItem(obj.getCodigo().intValue(), obj.getNome()));
        }
    }

    public boolean getTodasParcelasPagas() {
        if (alteraNegociacao == null || alteraNegociacao.getParcelas() == null) {
            return false;
        }
        for (MovParcelaVO parcela : alteraNegociacao.getParcelas()) {
            if (!parcela.getSituacao().equals("PG")) {
                return false;
            }
        }
        alteraNegociacao.setOperacao(AlteraValoresTO.GERAR_NOVAS);
        return true;
    }

    public String getDisplayTabelaNovasParcelas() {
        if (getTodasParcelasPagas()) {

            return "";
        }
        return "display: none;";
    }

    public void usarCredito() {
        try {
            getAlteraNegociacao().setDiferenca(getAlteraNegociacao().getDiferencaOriginal());
            if (getAlteraNegociacao().getUsarCredito()) {
                getAlteraNegociacao().setSobraCredito(getAlteraNegociacao().getValorCredito() - getAlteraNegociacao().getDiferenca());
                if (getAlteraNegociacao().getSobraCredito() < 0.0) {
                    getAlteraNegociacao().setDiferenca(getAlteraNegociacao().getDiferenca() - getAlteraNegociacao().getValorCredito());
                }
            } else {
                getAlteraNegociacao().setSobraCredito(0.0);
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public Integer getEscolhaDevolucaoCredito() {
        return escolhaDevolucaoCredito;
    }

    public void setEscolhaDevolucaoCredito(Integer escolhaDevolucaoCredito) {
        this.escolhaDevolucaoCredito = escolhaDevolucaoCredito;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void abrirModalConfirmarAlteracoes(){
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Novas Parcelas",
                "Deseja salvar as novas parcelas?",
                this, "confirmarAlteracoes", "", "", "", "");
    }
}
