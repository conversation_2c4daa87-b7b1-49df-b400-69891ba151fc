package br.com.pactosolucoes.ce.provaconceito.simonides;

import java.sql.ResultSet;
import java.util.List;

import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe responsável por fazer percistencia aos dados Camada de negócio
 * 
 * <AUTHOR>
 * 
 */
public class ProvaConceito_Simonides_DAO extends CEDao implements ProvaConceito_Simonides_IF {

	// Construtor padrão da classe
	public ProvaConceito_Simonides_DAO() throws Exception {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao#montarDados(java.sql.ResultSet)
	 */
	@Override
	public ProvaConceito_Simonides_TO montarDados(final ResultSet dadosSQL) throws Exception {
		ProvaConceito_Simonides_TO provaconceito_Simonides = new ProvaConceito_Simonides_TO();
		provaconceito_Simonides.setCodigo(dadosSQL.getInt("codigo"));
		provaconceito_Simonides.setNome(dadosSQL.getString("nome"));
		provaconceito_Simonides.setDescricao(dadosSQL.getString("descricao"));
		provaconceito_Simonides.setData(dadosSQL.getDate("data"));
		provaconceito_Simonides.setValor(dadosSQL.getDouble("valor"));
		provaconceito_Simonides.setVerdadeiro(dadosSQL.getBoolean("verdadeiro"));

		return provaconceito_Simonides;

	}

	/**
	 * @see br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_IF#alterar(br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_TO)
	 *      Método de alterar um dado do banco
	 */
	@Override
	public void alterar(final ProvaConceito_Simonides_TO provaConceito_Simonides) throws Exception {
		// Cria a String que irá conter o codigo sql
		String sql = "UPDATE provaconceitosimonides SET nome = ?, descricao = ?, data = ?, valor = ?, verdadeiro = ? WHERE codigo = ?";
		// Prepara a consulta
		Declaracao dc = new Declaracao(sql, this.con);
		int i = 0;
		dc.setString(++i, provaConceito_Simonides.getNome());
		dc.setString(++i, provaConceito_Simonides.getDescricao());
		dc.setDate(++i, new java.sql.Date(provaConceito_Simonides.getData().getTime()));
		dc.setDouble(++i, provaConceito_Simonides.getValor());
		dc.setBoolean(++i, provaConceito_Simonides.isVerdadeiro());
		dc.setInt(++i, provaConceito_Simonides.getCodigo());

		// Executa a consulta
		dc.execute();

	}

	/**
	 * @see br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_IF#consultar(br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_TO)
	 *      Método de listar os dados do banco
	 * @return ResultSet
	 */
	@SuppressWarnings("unchecked")
	public List<ProvaConceito_Simonides_TO> listar(final ProvaConceito_Simonides_TO filtro) throws Exception {

		// Cria a string sql
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, nome, descricao, data, valor, verdadeiro FROM provaconceitosimonides");
		// verificar se existe um filtro
		if (filtro != null) {
			if (!filtro.getNome().equals("") && (filtro.getNome() != null)) {
				sql.append(" WHERE nome LIKE ?");
			}
		}
		// Prepara a conexão
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		if (filtro != null) {
			if (!filtro.getNome().equals("") && (filtro.getNome() != null)) {
				dc.setString(1, filtro.getNome());
			}
		}
		// Executa a consulta e joga no resultset
		ResultSet rs = dc.executeQuery();
		// retorna a lista com os registros consultados
		return this.montarDadosConsulta(rs);

	}

	/**
	 * @see br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_IF#excluir(br.com.pactosolucoes.ce.provaconceito.ProvaConceito_Simonides_TO)
	 *      Método de excluir um dado do banco
	 */
	@Override
	public void excluir(final ProvaConceito_Simonides_TO provaConceito_Simonides) throws Exception {
		// Cria um string de sql
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM provaconceitosimonides WHERE codigo = ?");
		// Executa a consulta
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, provaConceito_Simonides.getCodigo());
		dc.execute();

	}

	/**
	 * @see br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_IF#excluir(br.com.pactosolucoes.ce.provaconceito.ProvaConceito_Simonides_TO)
	 *      Método de incluir um dado do banco
	 */
	@Override
	public void incluir(final ProvaConceito_Simonides_TO provaConceito_Simonides) throws Exception {
		// Cria a string sql
		StringBuilder sql = new StringBuilder();
		sql.append("	INSERT INTO provaconceitosimonides ( nome, descricao, data, valor, verdadeiro) VALUES (?,?,?,?,?)");
		// Prepara a conexão
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setString(++i, provaConceito_Simonides.getNome());
		dc.setString(++i, provaConceito_Simonides.getDescricao());
		dc.setDate(++i, new java.sql.Date(provaConceito_Simonides.getData().getTime()));
		dc.setDouble(++i, provaConceito_Simonides.getValor());
		dc.setBoolean(++i, provaConceito_Simonides.isVerdadeiro());
		// Executa a consulta
		dc.execute();
		// Fecha conexão

	}

	/**
	 * @see br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_IF#obter(java.lang.Integer) Método de obtem um dado do
	 *      banco pelo código
	 * @return ResultSet
	 */
	@Override
	public ProvaConceito_Simonides_TO obter(final Integer codigo) throws Exception {
		// Criar a string sql
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, nome, descricao, data,valor, verdadeiro FROM provaconceitosimonides WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		// preparar a consulta
		dc.setInt(1, codigo);
		ResultSet rs = dc.executeQuery();
		// montar os dados e retornar o objeto
		rs.next();
		return this.montarDados(rs);
	}

	@Override
	public List<ProvaConceito_Simonides_TO> consultar(final ProvaConceito_Simonides_TO filtro) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

}
