package br.com.pactosolucoes.ce.provaconceito;

import java.io.Serializable;
import java.util.Date;

/**
 * TO: Transfers Object: Responsável por fazer os acessos aos atributos do banco de das páginas.jsp
 * 
 * <AUTHOR>
 * 
 */
public class ProvaConceitoTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 464194844699895162L;

	private Integer codigo;
	private String nome;
	private String descricao;
	private Date data;
	private double valor;
	private boolean verdadeiro;

	public ProvaConceitoTO() {

	}

	/**
	 * @return the codigo
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            the codigo to set
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the nome
	 */
	public String getNome() {
		return this.nome;
	}

	/**
	 * @param nome
	 *            the nome to set
	 */
	public void setNome(final String nome) {
		this.nome = nome;
	}

	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            the descricao to set
	 */
	public void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return the data
	 */
	public Date getData() {
		return this.data;
	}

	/**
	 * @param data
	 *            the data to set
	 */
	public void setData(final Date data) {
		this.data = data;
	}

	/**
	 * @return the valor
	 */
	public double getValor() {
		return this.valor;
	}

	/**
	 * @param valor
	 *            the valor to set
	 */
	public void setValor(final double valor) {
		this.valor = valor;
	}

	/**
	 * @return the verdadeiro
	 */
	public boolean isVerdadeiro() {
		return this.verdadeiro;
	}

	/**
	 * @param verdadeiro
	 *            the verdadeiro to set
	 */
	public void setVerdadeiro(final boolean verdadeiro) {
		this.verdadeiro = verdadeiro;
	}

}
