/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.enumerador;

/**
 * Enumerador de Situação.
 * 
 * <AUTHOR>
 */
public enum Situacao {

	// Enumeradores
	PENDENTE(1, "Pendente"), 
	CADASTRADO(2, "Cadastrado"), 
	ORCADO(3, "Orçado"), 
	PRE_RESERVADO(4, "Pré-Reservado"), 
	NAO_RESERVADO(5, "Não-Reservado"),
	CONFIRMADO(7, "Confirmado"), 
	ENCERRADO(8, "Encerrado"),
	INTERESSADO(11, "Interessado"),
	CANCELADO(12,"Cancelado"),
	AUTORIZADO(25, "Autorizado");
	// Atributos
	private Integer codigo;
	private String descricao;

	// Métodos da Classe
	/**
	 * Método que seta o código e descrição
	 * 
	 * @param codigo
	 * @param descricao
	 */
	private Situacao(final Integer codigo, final String descricao) {
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return situacao
	 */
	public static Situacao getSituacao(final Integer codigo) {
		Situacao situacao = null;
		for (Situacao sit : Situacao.values()) {
			if (sit.getCodigo().equals(codigo)) {
				situacao = sit;
			}
		}
		return situacao;
	}

	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	public void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

}
