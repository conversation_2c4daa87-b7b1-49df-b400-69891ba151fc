package br.com.pactosolucoes.ce.comuns.to;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.FornecedorVO;

public class FornecedorServicoTO extends SuperTO implements Cloneable {

    private static final long serialVersionUID = 8453838189060674037L;
    /**
     *  Transfer Object para encapsulamento de dados relacionados à entidade Serviço e Fornecedor.
     * <AUTHOR>
     */
    @ChavePrimaria
    private Integer codigo = 0;
    private Integer codServico = 0;
    private String descServico = "";
    private ServicoTO servico;
    private FornecedorVO fornecedor;

    public FornecedorServicoTO clone() throws CloneNotSupportedException {
        return (FornecedorServicoTO) super.clone();
    }

    /**
     * @return the servico
     */
    public ServicoTO getServico() {
        if (servico == null) {
            this.servico = new ServicoTO();
        }
        return servico;
    }

    /**
     * @param servico the servico to set
     */
    public void setServico(ServicoTO servico) {
        this.servico = servico;
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the codServico
     */
    public Integer getCodServico() {
        return codServico;
    }

    /**
     * @param codServico the codServico to set
     */
    public void setCodServico(Integer codServico) {
        this.codServico = codServico;
    }

    /**
     * @return the descServico
     */
    public String getDescServico() {
        return descServico;
    }

    /**
     * @param descServico the descServico to set
     */
    public void setDescServico(String descServico) {
        this.descServico = descServico;
    }

    /**
     * @param fornecedor the fornecedor to set
     */
    public void setFornecedor(FornecedorVO fornecedor) {
        this.fornecedor = fornecedor;
    }

    /**
     * @return the fornecedor
     */
    public FornecedorVO getFornecedor() {
        if (fornecedor == null) {
            fornecedor = new FornecedorVO();
        }
        return fornecedor;
    }
}
