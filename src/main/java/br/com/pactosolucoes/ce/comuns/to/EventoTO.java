/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.Date;

import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Evento.
 * 
 * <AUTHOR>
 */
public class EventoTO implements Serializable {

	private static final long serialVersionUID = -1655094000695443735L;

	// Atributos
	private Integer codigo;
	private String nome;
	private String nomeCliente;
	private Date dataInteresse;
	private String ambiente;
	private Double valorFinal;
	private Situacao situacao;
	private Integer nrEventos;
	private String nomePerfilEvento;

	// Getters and Setters
	
	
	
	
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo nome.
	 */
	public String getNome() {
		return this.nome;
	}

	/**
	 * @param nome
	 *            O novo valor de nome.
	 */
	public void setNome(final String nome) {
		this.nome = nome;
	}

	/**
	 * @return O campo nomeCliente.
	 */
	public String getNomeCliente() {
		return this.nomeCliente;
	}

	/**
	 * @param nomeCliente
	 *            O novo valor de nomeCliente.
	 */
	public void setNomeCliente(final String nomeCliente) {
		this.nomeCliente = nomeCliente;
	}

	/**
	 * @return O campo dataInteresse.
	 */
	public Date getDataInteresse() {
		return this.dataInteresse;
	}

	/**
	 * @return O campo dataInteresse.
	 */
	public String getDataInteresseFormatada() {
		return Formatador.formatarDataPadrao(this.dataInteresse);
	}

	/**
	 * @param dataInteresse
	 *            O novo valor de dataInteresse.
	 */
	public void setDataInteresse(final Date dataInteresse) {
		this.dataInteresse = dataInteresse;
	}

	/**
	 * @return O campo ambiente.
	 */
	public String getAmbiente() {
		return this.ambiente;
	}

	/**
	 * @param ambiente
	 *            O novo valor de ambiente.
	 */
	public void setAmbiente(final String ambiente) {
		this.ambiente = ambiente;
	}

	/**
	 * @return O campo valorFinal.
	 */
	public Double getValorFinal() {
		return this.valorFinal;
	}

	/**
	 * @return O campo valorFinal.
	 */
	public String getValorFinalMonetario() {
		return Formatador.formatarValorMonetario(this.getValorFinal());
	}

	/**
	 * @param valorFinal
	 *            O novo valor de valorFinal.
	 */
	public void setValorFinal(final Double valorFinal) {
		this.valorFinal = valorFinal;
	}

	/**
	 * @return O campo situacao.
	 */
	public Situacao getSituacao() {
		return this.situacao;
	}

	/**
	 * @param situacao
	 *            O novo valor de situacao.
	 */
	public void setSituacao(final Situacao situacao) {
		this.situacao = situacao;
	}

	

	/**
	 * @param nrEventos the nrEventos to set
	 */
	public void setNrEventos(Integer nrEventos) {
		this.nrEventos = nrEventos;
	}

	/**
	 * @return the nrEventos
	 */
	public Integer getNrEventos() {
		return nrEventos;
	}

	/**
	 * @return the nomePerfilEvento
	 */
	public String getNomePerfilEvento() {
		return nomePerfilEvento;
	}

	/**
	 * @param nomePerfilEvento the nomePerfilEvento to set
	 */
	public void setNomePerfilEvento(String nomePerfilEvento) {
		this.nomePerfilEvento = nomePerfilEvento;
	}

}
