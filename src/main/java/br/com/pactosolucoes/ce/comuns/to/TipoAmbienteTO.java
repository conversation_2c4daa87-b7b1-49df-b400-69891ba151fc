/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;

import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TipoAmbienteTO extends SuperVO implements Serializable, Cloneable {


	/**
	 * 
	 */
	private static final long serialVersionUID = -2762066891772119905L;
	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private String descricao;
	private Integer duracaoMinimaHrs;
	private Integer qtdMaxReservasDia;
	private Integer tempoAdicionalPosteriorMin;
	private Date horarioInicial;
	@NaoControlarLogAlteracao
	private Date horarioFinal;
	private Date horarioFimExibicao;

	public TipoAmbienteTO clone() throws CloneNotSupportedException{
		return (TipoAmbienteTO) super.clone();
	}
	
	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	@Override
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	@Override
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	public void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return O campo duracaoMinimaHrs.
	 */
	public Integer getDuracaoMinimaHrs() {
		return this.duracaoMinimaHrs;
	}

	/**
	 * @param duracaoMinimaHrs
	 *            O novo valor de duracaoMinimaHrs.
	 */
	public void setDuracaoMinimaHrs(final Integer duracaoMinimaHrs) {
		this.duracaoMinimaHrs = duracaoMinimaHrs;
	}

	/**
	 * @return O campo qtdMaximaReservasDia.
	 */
	public Integer getQtdMaximaReservasDia() {
		return this.qtdMaxReservasDia;
	}

	/**
	 * @param qtdMaxReservasDia
	 *            O novo valor de qtdMaximaReservasDia.
	 */
	public void setQtdMaximaReservasDia(final Integer qtdMaxReservasDia) {
		this.qtdMaxReservasDia = qtdMaxReservasDia;
	}

	/**
	 * @return O campo tempoAdicionalPosteriorMin.
	 */
	public Integer getTempoAdicionalPosteriorMin() {
		return this.tempoAdicionalPosteriorMin;
	}

	/**
	 * @param tempoAdicionalPosteriorMin
	 *            O novo valor de tempoAdicionalPosteriorMin.
	 */
	public void setTempoAdicionalPosteriorMin(final Integer tempoAdicionalPosteriorMin) {
		this.tempoAdicionalPosteriorMin = tempoAdicionalPosteriorMin;
	}

	/**
	 * @return O campo horarioInicial.
	 */
	public Date getHorarioInicial() {
		return this.horarioInicial;
	}

	/**
	 * @param horarioInicial
	 *            O novo valor de horarioInicial.
	 */
	public void setHorarioInicial(final Date horarioInicial) {
		this.horarioInicial = horarioInicial;
	}

	/**
	 * @return O campo horarioFinal.
	 */
	public Date getHorarioFinal() {
		return this.horarioFinal;
	}

	/**
	 * @param horarioFinal
	 *            O novo valor de horarioFinal.
	 */
	public void setHorarioFinal(final Date horarioFinal) {
		this.horarioFinal = horarioFinal;
	}

	/**
	 * Retorna como String o atributo horarioInicial desse TO
	 * 
	 * @return
	 */
	public String getHorarioInicialString() {
		return Formatador.formatarHorario(this.getHorarioInicial());
	}

	/**
	 * Recebe uma hora em string e monta no atributo horaInicial desse TO
	 * 
	 * @param data
	 * @throws ParseException
	 */
	public void setHorarioInicialString(final String data) throws ParseException {
		this.setHorarioInicial(Formatador.obterHorario(data));
	}

	/**
	 * Retorna como String o atributo horarioInicial desse TO
	 * 
	 * @return
	 */
	public String getHorarioFinalString() {
		return Formatador.formatarHorario(this.getHorarioFinal());
	}
	/**
	 * Retorna como String o atributo horarioInicial desse TO
	 * 
	 * @return
	 */
	public String getHorarioFinalExibicaoString() {
		return Formatador.formatarHorario(this.getHorarioFinalExibicao());
	}
	/**
	 * Recebe uma hora em string e monta no atributo horarioFinal desse TO
	 * 
	 * @param data
	 * @throws ParseException
	 */
	public void setHorarioFinalString(final String data) throws ParseException {
		this.setHorarioFinal(Formatador.obterHorario(data));
	}
	/**
	 * Recebe uma hora em string e monta no atributo horarioFinal desse TO
	 * 
	 * @param data
	 * @throws ParseException
	 */
	public void setHorarioFinalExibicaoString(final String data) throws ParseException {
		this.setHorarioFinalExibicao(Formatador.obterHorario(data));
	}
	/**
	 * @return the horarioFinalExibicao
	 */
	public Date getHorarioFinalExibicao() {
		return this.horarioFimExibicao;
	}

	/**
	 * @param horarioFinalExibicao
	 *            the horarioFinalExibicao to set
	 */
	public void setHorarioFinalExibicao(final Date horarioFinalExibicao) {
		this.horarioFimExibicao = horarioFinalExibicao;
	}

	public Date getHorarioFimExibicao() {
		return horarioFimExibicao;
}

	public void setHorarioFimExibicao(Date horarioFimExibicao) {
		this.horarioFimExibicao = horarioFimExibicao;
	}

	public Integer getQtdMaxReservasDia() {
		return qtdMaxReservasDia;
	}

	public void setQtdMaxReservasDia(Integer qtdMaxReservasDia) {
		this.qtdMaxReservasDia = qtdMaxReservasDia;
	}
}
