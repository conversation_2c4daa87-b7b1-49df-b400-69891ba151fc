package br.com.pactosolucoes.ce.comuns.enumerador;

/**
 * Enumerador tipos de ambiente
 * 
 * <AUTHOR>
 */
public enum EnumTipoAmbiente {

	// Enumeradores
	SALAO(1), QUADRA(2), PISCINA(3), QUIOSQUE(4);

	// Atributos
	private Integer codigo;

	// Métodos
	/**
	 * Método que seta o código
	 * 
	 * @param codigo
	 */
	private EnumTipoAmbiente(final Integer codigo) {
		this.setCodigo(codigo);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return tipoAmbiente
	 */
	public static EnumTipoAmbiente getTipoAmbiente(final Integer codigo) {
		EnumTipoAmbiente tipoAmbiente = null;
		for (EnumTipoAmbiente tipo : EnumTipoAmbiente.values()) {
			if (tipo.getCodigo().equals(codigo)) {
				tipoAmbiente = tipo;
			}
		}
		return tipoAmbiente;
	}

	// Getters and Setters
	/**
	 * @return the codigo
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            the codigo to set
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

}
