package br.com.pactosolucoes.ce.comuns.to;

import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.Serializable;
import java.util.Date;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Pessoa.
 * 
 * <AUTHOR>
 */
public class PessoaTO implements Serializable {

	private static final long serialVersionUID = -6366865770742728130L;

	// Atributos
	private Integer codigo;
	private String nomeCompleto;
	private Date dataNascimento;
	private String cpf;
	private String telefoneCelular;
	private String telefoneComercial;
	private String telefoneFixo;
	private String email;
	private Integer codigoCliente;
	private String chaveZW;
	private String nomeEmpresa;
	private String situacaoCliente;
	private String matricula;
	private String codigoColaborador;

	private String bi;
	private Integer nuit;
	private String inscMunicipal;
	private String inscEstadual;
	private String endereco;
	private String bairro;
	private String cidade;
	private String uf;
	private String cep;
	public String toJSON() throws JSONException {
		JSONObject json = new JSONObject();
		json.put("codigo", this.codigo);
		json.put("nomeCompleto", this.nomeCompleto);
		json.put("dataNascimento", this.dataNascimento != null ? this.dataNascimento.getTime() : null);
		json.put("cpf", this.cpf);
		json.put("telefoneCelular", this.telefoneCelular);
		json.put("telefoneComercial", this.telefoneComercial);
		json.put("telefoneFixo", this.telefoneFixo);
		json.put("email", this.email);
		json.put("codigoCliente", this.codigoCliente);
		json.put("chaveZW", this.chaveZW);
		json.put("nomeEmpresa", this.nomeEmpresa);
		json.put("situacaoCliente", this.situacaoCliente);
		json.put("matricula", this.matricula);
		json.put("codigoColaborador", this.codigoColaborador);
		return json.toString();
	}

// Getters and Setters
	
	
	/**
	 * @return the codigo
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	

	/**
	 * @param codigo
	 *            the codigo to set
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the nomeCompleto
	 */
	public String getNomeCompleto() {
		return this.nomeCompleto;
	}

	/**
	 * @param nomeCompleto
	 *            the nomeCompleto to set
	 */
	public void setNomeCompleto(final String nomeCompleto) {
		this.nomeCompleto = nomeCompleto;
	}

	/**
	 * @return the dataNascimento
	 */
	public Date getDataNascimento() {
		return this.dataNascimento;
	}

	/**
	 * @param dataNascimento
	 *            the dataNascimento to set
	 */
	public void setDataNascimento(final Date dataNascimento) {
		this.dataNascimento = dataNascimento;
	}

	/**
	 * @return the cpf
	 */
	public String getCpf() {
		return this.cpf;
	}

	/**
	 * @param cpf
	 *            the cpf to set
	 */
	public void setCpf(final String cpf) {
		this.cpf = cpf;
	}

	/**
	 * @return the telefoneCelular
	 */
	public String getTelefoneCelular() {
		return this.telefoneCelular;
	}

	/**
	 * @param telefoneCelular
	 *            the telefoneCelular to set
	 */
	public void setTelefoneCelular(final String telefoneCelular) {
		this.telefoneCelular = telefoneCelular;
	}

	/**
	 * @return the telefoneComercial
	 */
	public String getTelefoneComercial() {
		return this.telefoneComercial;
	}

	/**
	 * @param telefoneComercial
	 *            the telefoneComercial to set
	 */
	public void setTelefoneComercial(final String telefoneComercial) {
		this.telefoneComercial = telefoneComercial;
	}

	/**
	 * @return the telefoneFixo
	 */
	public String getTelefoneFixo() {
		return this.telefoneFixo;
	}

	/**
	 * @param telefoneFixo
	 *            the telefoneFixo to set
	 */
	public void setTelefoneFixo(final String telefoneFixo) {
		this.telefoneFixo = telefoneFixo;
	}

	/**
	 * @return the email
	 */
	public String getEmail() {
		return this.email;
	}

	/**
	 * @param email
	 *            the email to set
	 */
	public void setEmail(final String email) {
		this.email = email;
	}

	public String getBi() {
		return bi;
	}

	public void setBi(String bi) {
		this.bi = bi;
	}

	public Integer getNuit() {
		return nuit;
	}

	public void setNuit(Integer nuit) {
		this.nuit = nuit;
	}
	/**
	 * Método que recebe um objeto pessoaVO e preenche os dados de pessoaTO
	 * 
	 * @param pessoaVO
	 * <AUTHOR>
	 */
	public static PessoaTO preencheComPessoaVO(final PessoaVO pessoaVO) {
		final PessoaTO pessoaTO = new PessoaTO();
		pessoaTO.setNomeCompleto(pessoaVO.getNome());
		pessoaTO.setCodigo(pessoaVO.getCodigo());
		pessoaTO.setEmail(pessoaVO.getEmail());
		// Se telefoneVO diferente de nulo
		if (pessoaVO.getTelefoneVOs() != null) {
			// itera no for
			for (TelefoneVO tel : pessoaVO.getTelefoneVOs()) {
				// se tipoTelefone igual a Celular
				// seta o celular
				// se não seta o telefone comercial
				if (tel.getTipoTelefone().equals("CE")) {
					pessoaTO.setTelefoneCelular(tel.getNumero());
				} else
				// se tipotelefone igual Comercial
				// seta o telefoneComercial
				// se não se o tipoTelefone igual a residencial seta o telefoneFixo
				if (tel.getTipoTelefone().equals("CO")) {
					pessoaTO.setTelefoneComercial(tel.getNumero());
				} else if (tel.getTipoTelefone().equals("RE")) {
					pessoaTO.setTelefoneFixo(tel.getNumero());
				}
			}
		}
		if (!UteisValidacao.emptyString(pessoaVO.getInscEstadual())) {
			pessoaTO.setInscEstadual(pessoaVO.getInscEstadual());
		}
		if (!UteisValidacao.emptyString(pessoaVO.getInscMunicipal())) {
			pessoaTO.setInscEstadual(pessoaVO.getInscMunicipal());
		}
		pessoaTO.setCidade(pessoaVO.getCidade_Apresentar());
		pessoaTO.setUf(pessoaVO.getEstadoVO().getSigla());
		if (pessoaVO.getEnderecoVOs() != null) {
			for (EnderecoVO end : pessoaVO.getEnderecoVOs()) {
				pessoaTO.setEndereco(end.getEndereco());
				pessoaTO.setBairro(end.getBairro());
				pessoaTO.setCep(end.getCep());
				break;
			}
		}
		return pessoaTO;

	}

	public Integer getCodigoCliente() {
		return codigoCliente;
	}

	public void setCodigoCliente(Integer codigoCliente) {
		this.codigoCliente = codigoCliente;
	}

	public String getChaveZW() {
		return chaveZW;
	}

	public void setChaveZW(String chaveZW) {
		this.chaveZW = chaveZW;
	}

	public String getNomeEmpresa() {
		return nomeEmpresa;
	}

	public void setNomeEmpresa(String nomeEmpresa) {
		this.nomeEmpresa = nomeEmpresa;
	}

	public String getSituacaoCliente() {
		return situacaoCliente;
	}

	public void setSituacaoCliente(String situacaoCliente) {
		this.situacaoCliente = situacaoCliente;
	}

	public String getMatricula() {
		return matricula;
	}

	public void setMatricula(String matricula) {
		this.matricula = matricula;
	}

	public String getCodigoColaborador() {
		return codigoColaborador;
	}

	public void setCodigoColaborador(String codigoColaborador) {
		this.codigoColaborador = codigoColaborador;
	}
	public String getInscMunicipal() {
		return inscMunicipal;
	}

	public void setInscMunicipal(String inscMunicipal) {
		this.inscMunicipal = inscMunicipal;
	}

	public String getInscEstadual() {
		return inscEstadual;
	}

	public void setInscEstadual(String inscEstadual) {
		this.inscEstadual = inscEstadual;
	}

	public String getEndereco() {
		return endereco;
	}

	public void setEndereco(String endereco) {
		this.endereco = endereco;
	}

	public String getBairro() {
		return bairro;
	}

	public void setBairro(String bairro) {
		this.bairro = bairro;
	}

	public String getCidade() {
		return cidade;
	}

	public void setCidade(String cidade) {
		this.cidade = cidade;
	}

	public String getUf() {
		return uf;
	}

	public void setUf(String uf) {
		this.uf = uf;
	}

	public String getCep() {
		return cep;
	}

	public void setCep(String cep) {
		this.cep = cep;
	}
}
