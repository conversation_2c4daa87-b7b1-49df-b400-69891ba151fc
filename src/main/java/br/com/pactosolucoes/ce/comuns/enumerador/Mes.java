package br.com.pactosolucoes.ce.comuns.enumerador;

/**
 * Enumerador de mês
 * 
 * <AUTHOR>
 */
public enum Mes {

    VAZIO(0, ""),
    JANEIRO(1, "Janeiro"),
    FEVEREIRO(2, "Fevereiro"),
    MARCO(3, "<PERSON><PERSON><PERSON>"),
    ABRIL(4, "<PERSON><PERSON><PERSON>"),
    MAIO(5, "<PERSON><PERSON>"),
    JUNHO(6, "<PERSON><PERSON>"),
    JULHO(7, "<PERSON><PERSON>"),
    AGOSTO(8, "Agosto"),
    SETEMBRO(9, "Setembro"),
    OUTUBRO(10, "Outubro"),
    NOVEMBRO(11, "Novembro"),
    DEZEMBRO(12, "Dezembro");

    private int codigo;
    private String descricao;

    Mes(final int codigo, final String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static Mes getMesPeloCodigo(int codigo) {
        for (Mes mes : Mes.values()) {
            if (mes.getCodigo() == codigo)
                return mes;
        }
        return Mes.VAZIO;
    }
    
    public static Mes getMesPelaDescricao(String descricao) {
        for (Mes mes : Mes.values()) {
            if (mes.getDescricao().equals(descricao))
                return mes;
        }
        return Mes.VAZIO;
    }

    public String getDescricao() {
        return this.descricao;
    }

    public void setDescricao(final String descricao) {
        this.descricao = descricao;
    }

    public int getCodigo() {
        return this.codigo;
    }

    public void setCodigo(final int codigo) {
        this.codigo = codigo;
    }

    public String getCodigoStr(){
        return getCodigo() < 10 ? "0"+getCodigo() : String.valueOf(getCodigo());
    }
}
