package br.com.pactosolucoes.ce.comuns.enumerador;

import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Enumerador de tipos de operação.
 * 
 * <AUTHOR>
 */
public enum FormaCalculo {

	// Enumeradores
	PERCENTUAL(1, "Percentual"), VALOR(2, "Valor");

	// Atributos
	private Integer codigo;
	private String descricao;

	// Métodos da Classe
	/**
	 * Método que seta o código e descrição
	 * 
	 * @param codigo
	 * @param descricao
	 */
	private FormaCalculo(final Integer codigo, final String descricao) {
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return formaCalculo
	 */
	public static FormaCalculo getFormaCalculo(final Integer codigo) {
		FormaCalculo formaCalculo = null;
		for (FormaCalculo fc : FormaCalculo.values()) {
			if (fc.getCodigo().equals(codigo)) {
				formaCalculo = fc;
			}
		}
		return formaCalculo;
	}

	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	private void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	private void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

	public static List<SelectItem> toSelectedItens() {
		ArrayList<SelectItem> itens = new ArrayList<SelectItem>();
		for (FormaCalculo formaCalculo : values()) {
			itens.add(new SelectItem(formaCalculo.codigo, formaCalculo.descricao));
		}
		return itens;
	}

}
