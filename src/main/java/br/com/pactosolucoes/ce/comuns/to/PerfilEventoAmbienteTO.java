package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade PerfilEventoAmbiente.
 * 
 * <AUTHOR>
 */
public class PerfilEventoAmbienteTO implements Serializable, Cloneable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1935078226241755337L;
	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private Integer codigoAmbiente;
	private String descricaoAmbiente;
	private Double valor;
	private String observacao;
	private Integer nrMaximoConvidados;
	@Lista
	private List<PerfilEventoSazonalidadeTO> perfilEventoSazonalidadeTOs;
	@Lista
	private List<PerfilEventoAmbienteLayoutTO> layouts;
	
	private Boolean contemEvento = Boolean.FALSE;

	// Getters and Setters
	
	
	public PerfilEventoAmbienteTO clone() throws CloneNotSupportedException{
		PerfilEventoAmbienteTO ambiente = (PerfilEventoAmbienteTO) super.clone();
		ambiente.setPerfilEventoSazonalidadeTOs(new ArrayList<PerfilEventoSazonalidadeTO>());
		ambiente.setLayouts(new ArrayList<PerfilEventoAmbienteLayoutTO>());
		for (PerfilEventoSazonalidadeTO sazon : this.getPerfilEventoSazonalidadeTOs()){
			ambiente.getPerfilEventoSazonalidadeTOs().add(sazon.clone());
		}
		for (PerfilEventoAmbienteLayoutTO lay : this.getLayouts()){
			ambiente.getLayouts().add(lay.clone());
		}
		return (PerfilEventoAmbienteTO) super.clone();
	}
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo codigoAmbiente.
	 */
	public Integer getCodigoAmbiente() {
		return this.codigoAmbiente;
	}

	/**
	 * @param codigoAmbiente
	 *            O novo valor de codigoAmbiente.
	 */
	public void setCodigoAmbiente(final Integer codigoAmbiente) {
		this.codigoAmbiente = codigoAmbiente;
	}

	/**
	 * @return O campo descricaoAmbiente.
	 */
	public String getDescricaoAmbiente() {
		return this.descricaoAmbiente;
	}

	/**
	 * @param descricaoAmbiente
	 *            O novo valor de descricaoAmbiente.
	 */
	public void setDescricaoAmbiente(final String descricaoAmbiente) {
		this.descricaoAmbiente = descricaoAmbiente;
	}

	/**
	 * @return O campo valor.
	 */
	public Double getValor() {
		return this.valor;
	}

	/**
	 * @return O campo valor.
	 */
	public String getValorMonetario() {
		return Formatador.formatarValorMonetario(this.valor);
	}

	/**
	 * @return O campo valor.
	 */
	public String getValorFormatado() {
		return Formatador.formatarValorNumerico(this.valor);
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValor(final Double valor) {
		this.valor = valor;
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValorFormatado(final String valor) {
		this.valor = Formatador.obterValorNumerico(valor);
	}

	/**
	 * @return O campo observacao.
	 */
	public String getObservacao() {
		return this.observacao;
	}

	/**
	 * @param observacao
	 *            O novo valor de observacao.
	 */
	public void setObservacao(final String observacao) {
		this.observacao = observacao;
	}

	/**
	 * @return O campo nrMaximoConvidados.
	 */
	public Integer getNrMaximoConvidados() {
		return this.nrMaximoConvidados;
	}

	/**
	 * @param nrMaximoConvidados
	 *            O novo valor de nrMaximoConvidados.
	 */
	public void setNrMaximoConvidados(final Integer nrMaximoConvidados) {
		this.nrMaximoConvidados = nrMaximoConvidados;
	}

	/**
	 * @return O campo perfilEventoSazonalidadeTOs.
	 */
	public List<PerfilEventoSazonalidadeTO> getPerfilEventoSazonalidadeTOs() {
		// Se perfilEventoSazonalidadeTOs igual a nulo
		// perfilEventoSazonalidadeTOs cria uma nova lista de PerfilEventoSazonalidadeTO
		if (this.perfilEventoSazonalidadeTOs == null) {
			this.perfilEventoSazonalidadeTOs = new ArrayList<PerfilEventoSazonalidadeTO>();
		}
		return this.perfilEventoSazonalidadeTOs;
	}

	/**
	 * @param perfilEventoSazonalidadeTOs
	 *            O novo valor de perfilEventoSazonalidadeTOs.
	 */
	public void setPerfilEventoSazonalidadeTOs(final List<PerfilEventoSazonalidadeTO> perfilEventoSazonalidadeTOs) {
		this.perfilEventoSazonalidadeTOs = perfilEventoSazonalidadeTOs;
	}

	/**
	 * 
	 * @return O campo layouts.
	 */
	public List<PerfilEventoAmbienteLayoutTO> getLayouts() {
		// Se layouts igual a nulo
		// layouts cria uma nova lista de PerfilEventoAmbienteLayoutTO
		if (this.layouts == null) {
			this.layouts = new ArrayList<PerfilEventoAmbienteLayoutTO>();
		}
		return this.layouts;
	}

	/**
	 * @param layouts
	 *            O novo valor de layouts.
	 */
	public void setLayouts(final List<PerfilEventoAmbienteLayoutTO> layouts) {
		this.layouts = layouts;
	}
	public Boolean getContemEvento() {
		return contemEvento;
	}
	public void setContemEvento(Boolean contemEvento) {
		this.contemEvento = contemEvento;
	}

}