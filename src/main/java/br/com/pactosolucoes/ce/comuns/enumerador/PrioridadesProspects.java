package br.com.pactosolucoes.ce.comuns.enumerador;

public enum PrioridadesProspects {
	CONDICAO_UM(100), CONDICAO_DOIS(80), CONDICAO_TRES(75), CONDICAO_QUATRO(70),
	CONDICAO_CINCO(65), CONDICAO_SEIS(60), CONDICAO_SETE(20), CANCELADO_ENCERRADO(0);
	
	private Integer prioridade;
	
	private PrioridadesProspects(Integer prioridade ){
		this.prioridade = prioridade;
	}


	/**
	 * @return the prioridade
	 */
	public Integer getPrioridade() {
		return prioridade;
	}

}
