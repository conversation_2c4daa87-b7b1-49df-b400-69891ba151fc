package br.com.pactosolucoes.ce.comuns.enumerador;

import br.com.pactosolucoes.enumeradores.EntidadeRateioEnum;

/**
 * Enumerador de tipos de produto para locação.
 * 
 * <AUTHOR>
 */
public enum TipoProdutoLocacao {

	// Enumeradores
	BENS_DE_CONSUMO(1, "Bens de consumo", EntidadeRateioEnum.BENS_CONSUMO), 
				UTENSILIOS(2, "Utensílios", EntidadeRateioEnum.UTENSILIOS), 
				BRINQUEDOS(3, "Brinquedos", EntidadeRateioEnum.BRINQUEDOS);

	// Atributos
	private Integer codigo;
	private String descricao;
	private EntidadeRateioEnum entidade;
	// Métodos da Classe
	/**
	 * Método que seta o código e descrição
	 * 
	 * @param codigo
	 * @param descricao
	 */
	private TipoProdutoLocacao(final Integer codigo, final String descricao, EntidadeRateioEnum entidade) {
		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setEntidade(entidade);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return tipoProdutoLocacao
	 */
	public static TipoProdutoLocacao getTipoProdutoLocacao(final Integer codigo) {
		TipoProdutoLocacao tipoProdLocacao = null;
		for (TipoProdutoLocacao tipo : TipoProdutoLocacao.values()) {
			if (tipo.getCodigo().equals(codigo)) {
				tipoProdLocacao = tipo;
			}
		}
		return tipoProdLocacao;
	}

	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	private void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	private void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

	public void setEntidade(EntidadeRateioEnum entidade) {
		this.entidade = entidade;
	}

	public EntidadeRateioEnum getEntidade() {
		return entidade;
	}

}
