package br.com.pactosolucoes.ce.comuns.enumerador;

/**
 * Enumerador de tipos de produto para locação.
 * 
 * <AUTHOR>
 */
public enum TipoTelefone {

	// Enumeradores
	RESIDENCIAL("RE", "Residencial"), COMERCIAL("CO", "Comercial"), CELULAR("CE", "Celular"), FAX("FA","Fax");

	// Atributos
	private String codigo;
	private String descricao;

	// Métodos da Classe
	/**
	 * Método que seta o código e descrição
	 * 
	 * @param codigo
	 * @param descricao
	 */
	private TipoTelefone(final String codigo, final String descricao) {
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return tipoProdutoLocacao
	 */
	public static TipoTelefone getTipoTelefone(final String codigo) {
		TipoTelefone tipoProdutoLocacao = null;
		for (TipoTelefone tipo : TipoTelefone.values()) {
			if (tipo.getCodigo().equals(codigo)) {
				tipoProdutoLocacao = tipo;
			}
		}
		return tipoProdutoLocacao;
	}

	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public String getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	private void setCodigo(final String codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	private void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

}
