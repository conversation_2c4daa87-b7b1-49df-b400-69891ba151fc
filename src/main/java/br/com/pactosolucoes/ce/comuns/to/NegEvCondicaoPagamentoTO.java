/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Negociação de Evento com Condição de pagamento.
 * 
 * <AUTHOR>
 */
public class NegEvCondicaoPagamentoTO implements Serializable, Cloneable {

	private static final long serialVersionUID = -5893315702730219636L;

	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private Integer codCondicaoPag;
	private Integer intervaloEntreParcela;
	private Double percentualValorEntrada;
	private Boolean condicaoPagDefault;
	private Boolean entrada;
	private Integer nrParcelas;
	private String descrCondicPag;
	@NaoControlarLogAlteracao
	private Boolean selecionado;

	
	public NegEvCondicaoPagamentoTO clone() throws CloneNotSupportedException{
		return (NegEvCondicaoPagamentoTO) super.clone();
	}
	
	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo codigoCondicaoPagamento.
	 */
	public Integer getCodigoCondicaoPagamento() {
		return this.codCondicaoPag;
	}

	/**
	 * @param codCondicaoPag
	 *            O novo valor de codigoCondicaoPagamento.
	 */
	public void setCodigoCondicaoPagamento(final Integer codCondicaoPag) {
		this.codCondicaoPag = codCondicaoPag;
	}

	/**
	 * @return O campo intervaloEntreParcela.
	 */
	public Integer getIntervaloEntreParcela() {
		return this.intervaloEntreParcela;
	}

	/**
	 * @param intervaloEntreParcela
	 *            O novo valor de intervaloEntreParcela.
	 */
	public void setIntervaloEntreParcela(final Integer intervaloEntreParcela) {
		this.intervaloEntreParcela = intervaloEntreParcela;
	}

	/**
	 * @return O campo percentualValorEntrada.
	 */
	public Double getPercentualValorEntrada() {
		return this.percentualValorEntrada;
	}

	/**
	 * @param percentualValorEntrada
	 *            O novo valor de percentualValorEntrada.
	 */
	public void setPercentualValorEntrada(final Double percentualValorEntrada) {
		this.percentualValorEntrada = percentualValorEntrada;
	}

	/**
	 * @return O campo condicaoPagamentoDefault.
	 */
	public Boolean getCondicaoPagamentoDefault() {
		return this.condicaoPagDefault;
	}

	/**
	 * @param condicaoPagamentoDefault
	 *            O novo valor de condicaoPagamentoDefault.
	 */
	public void setCondicaoPagamentoDefault(final Boolean condicaoPagamentoDefault) {
		this.condicaoPagDefault = condicaoPagamentoDefault;
	}

	/**
	 * @return O campo entrada.
	 */
	public Boolean getEntrada() {
		return this.entrada;
	}

	/**
	 * @param entrada
	 *            O novo valor de entrada.
	 */
	public void setEntrada(final Boolean entrada) {
		this.entrada = entrada;
	}

	/**
	 * @return O campo nrParcelas.
	 */
	public Integer getNrParcelas() {
		return this.nrParcelas;
	}

	/**
	 * @param nrParcelas
	 *            O novo valor de nrParcelas.
	 */
	public void setNrParcelas(final Integer nrParcelas) {
		this.nrParcelas = nrParcelas;
	}

	/**
	 * @return O campo descricaoCondicaoPagamento.
	 */
	public String getDescricaoCondicaoPagamento() {
		return this.descrCondicPag;
	}

	/**
	 * @param descricaoCondicaoPagamento
	 *            O novo valor de descricaoCondicaoPagamento.
	 */
	public void setDescricaoCondicaoPagamento(final String descricaoCondicaoPagamento) {
		this.descrCondicPag = descricaoCondicaoPagamento;
	}

	/**
	 * Se o campo selecionado for igual a nulo retorna falso
	 * 
	 * @return O campo selecionado.
	 */
	public Boolean getSelecionado() {
		if (this.selecionado == null) {
			this.selecionado = false;
		}
		return this.selecionado;
	}

	/**
	 * @param selecionado
	 *            O novo valor de selecionado.
	 */
	public void setSelecionado(final Boolean selecionado) {
		this.selecionado = selecionado;
	}

}
