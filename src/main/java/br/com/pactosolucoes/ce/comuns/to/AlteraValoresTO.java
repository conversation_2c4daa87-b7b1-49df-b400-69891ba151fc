package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;

public class AlteraValoresTO implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3252167027075116437L;
	public static final Integer GERAR_NOVAS = 1, ALTERAR_PARCELAS=2;
	private Integer nrParcelas;
	private Integer nrParcelasPagas;
	private Integer nrParcelasTotal;
	private Double valorAberto;
	private Double valorPago;
	private Integer nrParcelasNovo;
	private Date dataPrimeiraParcela;
	private Double valorInicial;
	private Double diferenca;
	private List<MovParcelaVO> parcelas;
	private List<MovParcelaVO> parcelasNovas;
	private Integer operacao;
	private Double sobra;
	private String mensagem;
	private Double valorUnicoPago;
        private Double valorCredito;
        private boolean usarCredito = false;
        private Double sobraCredito;
        private Double diferencaOriginal;
        private NegEvCaucaoCreditoTO credito = new NegEvCaucaoCreditoTO();

    public Double getSobraCredito() {
        return sobraCredito;
    }

    public void setSobraCredito(Double sobraCredito) {
        this.sobraCredito = sobraCredito;
    }

    public boolean getUsarCredito() {
        return usarCredito;
    }

    public void setUsarCredito(boolean usarCredito) {
        this.usarCredito = usarCredito;
    }

    public Double getValorCredito() {
        return valorCredito;
    }

    public void setValorCredito(Double valorCredito) {
        this.valorCredito = valorCredito;
    }

	/**
	 * @return the nrParcelas
	 */
	public Integer getNrParcelas() {
		return nrParcelas;
	}
	/**
	 * @param nrParcelas the nrParcelas to set
	 */
	public void setNrParcelas(Integer nrParcelas) {
		this.nrParcelas = nrParcelas;
	}
	/**
	 * @return the nrParcelasNovo
	 */
	public Integer getNrParcelasNovo() {
		return nrParcelasNovo;
	}
	/**
	 * @param nrParcelasNovo the nrParcelasNovo to set
	 */
	public void setNrParcelasNovo(Integer nrParcelasNovo) {
		this.nrParcelasNovo = nrParcelasNovo;
	}
	/**
	 * @return the dataPrimeiraParcela
	 */
	public Date getDataPrimeiraParcela() {
		return dataPrimeiraParcela;
	}
	/**
	 * @param dataPrimeiraParcela the dataPrimeiraParcela to set
	 */
	public void setDataPrimeiraParcela(Date dataPrimeiraParcela) {
		this.dataPrimeiraParcela = dataPrimeiraParcela;
	}
	/**
	 * @return the valorInicial
	 */
	public Double getValorInicial() {
		return valorInicial;
	}
	/**
	 * @param valorInicial the valorInicial to set
	 */
	public void setValorInicial(Double valorInicial) {
		this.valorInicial = valorInicial;
	}
	/**
	 * @return the diferenca
	 */
	public Double getDiferenca() {
		return diferenca;
	}
	/**
	 * @param diferenca the diferenca to set
	 */
	public void setDiferenca(Double diferenca) {
		this.diferenca = diferenca;
	}
	/**
	 * @return the parcelas
	 */
	public List<MovParcelaVO> getParcelas() {
		return parcelas;
	}
	/**
	 * @param parcelas the parcelas to set
	 */
	public void setParcelas(List<MovParcelaVO> parcelas) {
		this.parcelas = parcelas;
	}

	public String getValorInicialMonetario() {
		return Formatador.formatarValorMonetario(valorInicial);
	}
        public String getSobraCreditoMonetario() {
		return Formatador.formatarValorMonetario(Uteis.arredondarForcando2CasasDecimais(sobraCredito));
	}
	public String getDiferencaMonetario() {
		return Formatador.formatarValorMonetario(diferenca);
	}
	public String getValorPagoMonetario() {
		return Formatador.formatarValorMonetario(valorPago);
	}
	public String getValorAbertoMonetario() {
		return Formatador.formatarValorMonetario(valorAberto);
	}
	public String getSobraMonetario() {
		return Formatador.formatarValorMonetario(sobra);
	}
	public String getValorUnicoPagoMonetario() {
		return Formatador.formatarValorMonetario(valorUnicoPago);
	}
        public String getValorCreditoMonetario() {
            return Formatador.formatarValorMonetario(valorCredito);
        }
	/**
	 * @param valorPago the valorPago to set
	 */
	public void setValorPago(Double valorPago) {
		this.valorPago = valorPago;
	}
	/**
	 * @return the valorPago
	 */
	public Double getValorPago() {
		return valorPago;
	}
	/**
	 * @param valorAberto the valorAberto to set
	 */
	public void setValorAberto(Double valorAberto) {
		this.valorAberto = valorAberto;
	}
	/**
	 * @return the valorAberto
	 */
	public Double getValorAberto() {
		return valorAberto;
	}
	/**
	 * @param parcelasNovas the parcelasNovas to set
	 */
	public void setParcelasNovas(List<MovParcelaVO> parcelasNovas) {
		this.parcelasNovas = parcelasNovas;
	}
	/**
	 * @return the parcelasNovas
	 */
	public List<MovParcelaVO> getParcelasNovas() {
		if(parcelasNovas == null){
			parcelasNovas = new ArrayList<MovParcelaVO>();
		}
		return parcelasNovas;
	}
	/**
	 * @param operacao the operacao to set
	 */
	public void setOperacao(Integer operacao) {
		this.operacao = operacao;
	}
	/**
	 * @return the operacao
	 */
	public Integer getOperacao() {
		if(operacao == null){
			operacao = new Integer(ALTERAR_PARCELAS);
		}
		return operacao;
	}
	/**
	 * @param totalParcelas the totalParcelas to set
	 */
	public void setSobra(Double totalParcelas) {
		this.sobra = totalParcelas;
	}
	/**
	 * @return the totalParcelas
	 */
	public Double getSobra() {
		if(sobra == null)
			sobra = 0.0;
		return sobra;
	}
	/**
	 * @param mensagem the mensagem to set
	 */
	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}
	/**
	 * @return the mensagem
	 */
	public String getMensagem() {
		return mensagem;
	}
	/**
	 * @param valorUnicoPago the valorUnicoPago to set
	 */
	public void setValorUnicoPago(Double valorUnicoPago) {
		this.valorUnicoPago = valorUnicoPago;
	}
	/**
	 * @return the valorUnicoPago
	 */
	public Double getValorUnicoPago() {
		return valorUnicoPago;
	}
	/**
	 * @param nrParcelasPagas the nrParcelasPagas to set
	 */
	public void setNrParcelasPagas(Integer nrParcelasPagas) {
		this.nrParcelasPagas = nrParcelasPagas;
	}
	/**
	 * @return the nrParcelasPagas
	 */
	public Integer getNrParcelasPagas() {
		if(nrParcelasPagas == null)
			nrParcelasPagas = new Integer(0);
		return nrParcelasPagas;
	}
	/**
	 * @param nrParcelasTotal the nrParcelasTotal to set
	 */
	public void setNrParcelasTotal(Integer nrParcelasTotal) {
		this.nrParcelasTotal = nrParcelasTotal;
	}
	/**
	 * @return the nrParcelasTotal
	 */
	public Integer getNrParcelasTotal() {
		return nrParcelasTotal;
	}
	
	public String getStyle(){
		if(this.getSobra() < 0.0){
			return "sobraNegativa";
		}else
			return "sobraPositiva";
	}

    public NegEvCaucaoCreditoTO getCredito() {
        return credito;
    }

    public void setCredito(NegEvCaucaoCreditoTO credito) {
        this.credito = credito;
    }

    public Double getDiferencaOriginal() {
        return diferencaOriginal;
    }

    public void setDiferencaOriginal(Double diferencaOriginal) {
        this.diferencaOriginal = diferencaOriginal;
    }

        
}
