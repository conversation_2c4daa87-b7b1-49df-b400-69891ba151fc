/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import java.io.File;
import java.io.Serializable;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade perfil evento com ambiente de layout.
 * 
 * <AUTHOR>
 */
public class PerfilEventoAmbienteLayoutTO implements Serializable, Cloneable {
    private static final long serialVersionUID = 8330292458857270878L;



	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private String nomeArquivo;
	private String descricao;
	private File arquivo;
	private String nomeAmbiente;
	@NaoControlarLogAlteracao
	private Boolean contemEvento = Boolean.FALSE;

	
	public PerfilEventoAmbienteLayoutTO clone() throws CloneNotSupportedException{
		
		return (PerfilEventoAmbienteLayoutTO)super.clone();
		
	}
	// Getters and Setters
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo( Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo nomeArquivo.
	 */
	public String getNomeArquivo() {
		return this.nomeArquivo;
	}

	/**
	 * @param nomeArquivo
	 *            O novo valor de nomeArquivo.
	 */
	public void setNomeArquivo( String nomeArquivo) {
		this.nomeArquivo = nomeArquivo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	public void setDescricao( String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return O campo arquivo.
	 */
	public File getArquivo() {
		return this.arquivo;
	}

	/**
	 * @param arquivo
	 *            O novo valor de arquivo.
	 */
	public void setArquivo( File arquivo) {
		this.arquivo = arquivo;
	}

	/**
	 * @param nomeAmbiente the nomeAmbiente to set
	 */
	public void setNomeAmbiente(String nomeAmbiente) {
		this.nomeAmbiente = nomeAmbiente;
	}

	/**
	 * @return the nomeAmbiente
	 */
	public String getNomeAmbiente() {
		return nomeAmbiente;
	}
	public Boolean getContemEvento() {
		return contemEvento;
	}
	public void setContemEvento(Boolean contemEvento) {
		this.contemEvento = contemEvento;
	}

}
