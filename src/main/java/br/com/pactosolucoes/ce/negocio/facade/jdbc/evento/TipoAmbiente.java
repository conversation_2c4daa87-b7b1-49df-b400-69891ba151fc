package br.com.pactosolucoes.ce.negocio.facade.jdbc.evento;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.evento.TipoAmbienteInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.Ordenacao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>TipoEventoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 * 
 * <AUTHOR>
 */
public class TipoAmbiente extends CEDao implements TipoAmbienteInterfaceFacade {

	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public TipoAmbiente() throws Exception {
		super();
		this.setIdEntidade("tipoambiente");
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<TipoAmbienteTO> consultar() throws Exception {
//		super.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, descricao, duracaominimahrs, horariofinal, horarioinicial," +
				"horariofinalexibicao, qtdmaximareservasdia, tempoadicionalposteriormin \n");
		sql.append("FROM tipoambiente");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	@SuppressWarnings("unchecked")
	public List<TipoAmbienteTO> consultarTA(final TipoAmbienteTO tipoAmbiente) throws Exception {
//		super.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, descricao, duracaominimahrs, horariofinal, horariofinalexibicao, horarioinicial, qtdmaximareservasdia, tempoadicionalposteriormin \n");
		sql.append("FROM tipoambiente ORDER BY descricao");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public TipoAmbienteTO obter(final Integer codigo) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, descricao, duracaominimahrs, qtdmaximareservasdia, tempoadicionalposteriormin, horarioinicial, horariofinal, horariofinalexibicao \n");
		sql.append("FROM tipoambiente \n");
		sql.append("WHERE codigo = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigo);

		ResultSet tabelaResultado = dc.executeQuery();
		List<TipoAmbienteTO> result = this.montarDadosConsulta(tabelaResultado);

		return result.isEmpty() ? new TipoAmbienteTO() : result.get(0);
	}

	/**
	 * Monta um objeto <code>TipoAmbienteTO</code> a partir do <code>ResultSet</code> de um tipoAmbiente.
	 * 
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>TipoAmbienteTO</code>.
	 * @throws Exception
	 * <AUTHOR>
	 */
	@Override
	public TipoAmbienteTO montarDados(final ResultSet dadosSQL) throws Exception {
		TipoAmbienteTO obj = new TipoAmbienteTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setDescricao(dadosSQL.getString("descricao"));
		obj.setDuracaoMinimaHrs(dadosSQL.getInt("duracaominimahrs"));
		obj.setHorarioFinal(dadosSQL.getTime("horariofinal"));
		obj.setHorarioFinalExibicao(dadosSQL.getTime("horariofinalexibicao"));
		obj.setHorarioInicial(dadosSQL.getTime("horarioinicial"));
		obj.setQtdMaximaReservasDia(dadosSQL.getInt("qtdmaximareservasdia"));
		obj.setTempoAdicionalPosteriorMin(dadosSQL.getInt("tempoadicionalposteriormin"));
		return obj;
	}

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe <code>TipoAmbienteTO</code>.
	 * 
	 * @param tipoAmbiente
	 *            - Objeto da classe <code>TipoAmbienteTO</code> que será gravado no banco de dados.
	 * @throws Exception
	 *             - Caso haja problemas de conexão.
	 */
	public void incluir(final TipoAmbienteTO tipoAmbiente) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		// Cria a string sql
		StringBuilder sql = new StringBuilder();
		sql.append("insert into tipoambiente (descricao, duracaominimahrs, qtdmaximareservasdia, tempoadicionalposteriormin, horarioinicial, horariofinal, horariofinalexibicao)" +
						" values (?,?,?,?,?,?,?)");
		// Prepara a conexão
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setString(++i, tipoAmbiente.getDescricao());
		dc.setInt(++i, tipoAmbiente.getDuracaoMinimaHrs());
		dc.setInt(++i, tipoAmbiente.getQtdMaximaReservasDia());
		dc.setInt(++i, tipoAmbiente.getTempoAdicionalPosteriorMin());
		dc.setTimestamp(++i, new java.sql.Timestamp(tipoAmbiente.getHorarioInicial().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(tipoAmbiente.getHorarioFinal().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(tipoAmbiente.getHorarioFinalExibicao().getTime()));
		// Executa a consulta
		dc.execute();
		// Fecha conexão

	}

	/**
	 * Operação responsável por excluir no BD um objeto da classe <code>TipoAmbienteTO</code>.
	 * 
	 * @param obj
	 *            - Objeto da classe <code>TipoAmbienteTO</code> que será removido no banco de dados.
	 * @throws Exception
	 *             - Caso haja problemas de conexão.
	 */
	public void excluir(final TipoAmbienteTO obj) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		Declaracao dc = new Declaracao("DELETE FROM tipoambiente WHERE CODIGO = ?", this.con);
		dc.setInt(1, obj.getCodigo());
		dc.execute();

	}

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe <code>TipoAmbienteTO</code>.
	 * 
	 * @param tipoAmbiente
	 *            - Objeto da classe <code>TipoAmbienteTO</code> que será alterada no banco de dados.
	 * @exception Exception
	 *                - Caso haja problemas de conexão.
	 */
	public void alterar(final TipoAmbienteTO tipoAmbiente) throws Exception {
//		super.alterarObj(this.getIdEntidade());
		// Cria a String que irá conter o codigo sql
		String sql = "UPDATE tipoambiente SET descricao = ?, duracaominimahrs = ?, qtdmaximareservasdia = ?, tempoadicionalposteriormin = ?, horarioinicial = ?," +
				" horariofinal = ?, horariofinalexibicao = ?  WHERE codigo = ?";
		// Prepara a consulta
		Declaracao dc = new Declaracao(sql, this.con);
		int i = 0;
		dc.setString(++i, tipoAmbiente.getDescricao());
		dc.setInt(++i, tipoAmbiente.getDuracaoMinimaHrs());
		dc.setInt(++i, tipoAmbiente.getQtdMaximaReservasDia());
		dc.setInt(++i, tipoAmbiente.getTempoAdicionalPosteriorMin());
		dc.setTimestamp(++i, new java.sql.Timestamp(tipoAmbiente.getHorarioInicial().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(tipoAmbiente.getHorarioFinal().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(tipoAmbiente.getHorarioFinalExibicao().getTime()));
		dc.setInt(++i, tipoAmbiente.getCodigo());
		// Executa a consulta
		dc.execute();
	}

	/**
	 * Operação responsável por consultar por codigo no BD os dados de um objeto da classe <code>TipoAmbienteTO</code>.
	 * 
	 * @param valorConsulta
	 * @return montarDadosConsulta
	 * <AUTHOR>
	 */
	@SuppressWarnings("unchecked")
	public List consultarPorCodigo(final Integer valorConsulta) throws Exception {
//		super.consultarObj(this.getIdEntidade());
		String sqlStr = "SELECT * FROM tipoambiente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
		Statement stm = this.con.createStatement();
		ResultSet tabelaResultado = stm.executeQuery(sqlStr);
		return (this.montarDadosConsulta(tabelaResultado));
	}

	/**
	 * Operação responsável por consultar por descrição no BD os dados de um objeto da classe <code>TipoAmbienteTO</code>.
	 * 
	 * @param valorConsulta
	 * @return montarDadosConsulta
	 * <AUTHOR>
	 */
	@SuppressWarnings("unchecked")
	public List<TipoAmbienteTO> consultarPorDescricao(final String valorConsulta) throws Exception {
//		super.consultarObj(this.getIdEntidade());
		String sqlStr = "SELECT * FROM tipoambiente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase()
				+ "%') ORDER BY descricao";
		Statement stm = this.con.createStatement();
		ResultSet tabelaResultado = stm.executeQuery(sqlStr);
		return (this.montarDadosConsulta(tabelaResultado));
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	public boolean consultarExisteNegociacaoAmbiente(final Integer valorConsulta) throws Exception {
//		super.consultarObj(this.getIdEntidade());
		int i = 0;
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT AMB.tipoambiente, NEPEA.ambiente \n");
		sql.append("FROM ambiente AMB \n");
		sql.append("INNER JOIN negociacaoeventoperfileventoambiente NEPEA ON NEPEA.ambiente = AMB.codigo \n");
		sql.append("WHERE AMB.tipoambiente = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(++i, valorConsulta);
		// executar a pesquisa e guardar num resultset
		ResultSet tabelaResultado = dc.executeQuery();

		// retornar a lista gerada pelo montardadosProspects
		return tabelaResultado.next();
	}

	public Boolean existeTiposAmbienteNome(String descricao) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, descricao, duracaominimahrs, horariofinal, horarioinicial," +
				"horariofinalexibicao, qtdmaximareservasdia, tempoadicionalposteriormin \n");
		sql.append("FROM tipoambiente WHERE upper( descricao ) like('" + descricao.toUpperCase() + "')");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		ResultSet tabelaResultado = dc.executeQuery();
		return tabelaResultado.next();
	}
	private PreparedStatement getPS() throws SQLException {
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT\n");
		sql.append(" codigo,descricao, duracaominimahrs,qtdmaximareservasdia,tempoadicionalposteriormin,horarioinicial,horariofinal\n");
		sql.append(" FROM tipoambiente");
		return con.prepareStatement(sql.toString());
	}
	public String consultarJSON() throws Exception {

		StringBuilder json;
		boolean dados;
		try (ResultSet rs = getPS().executeQuery()) {
			TipoAmbienteTO tipoAmbiente = new TipoAmbienteTO();
			json = new StringBuilder();
			json.append("{\"aaData\":[");
			dados = false;
			while (rs.next()) {
				dados = true;
				json.append("[\"").append(rs.getInt("codigo")).append("\",");
				json.append("\"").append(rs.getString("descricao")).append("\",");
				json.append("\"").append(rs.getInt("qtdmaximareservasdia")).append("\",");
				json.append("\"").append(rs.getInt("tempoadicionalposteriormin")).append("\",");
				json.append("\"").append(Formatador.formatarHorario(rs.getTime("horarioinicial"))).append("\",");
				json.append("\"").append(Formatador.formatarHorario(rs.getTime("horariofinal"))).append("\"],");
			}
		}
		if (dados) {
			json.deleteCharAt(json.toString().length() - 1);
		}
		json.append("]}");
		return json.toString();
	}
	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {
		List lista = new ArrayList();
		try(ResultSet rs = getPS().executeQuery()) {
			while (rs.next()) {
				TipoAmbienteTO tipoAmbiente = new TipoAmbienteTO();
				String geral = rs.getInt("codigo") + rs.getString("descricao") + rs.getInt("duracaominimahrs") + rs.getInt("qtdmaximareservasdia") + rs.getInt("tempoadicionalposteriormin") +
						rs.getDate("horarioinicial") + rs.getDate("horariofinal");
				if (geral.toLowerCase().contains(filtro.toLowerCase())) {
					tipoAmbiente.setCodigo(rs.getInt("codigo"));
					tipoAmbiente.setDescricao(rs.getString("descricao"));
					tipoAmbiente.setDuracaoMinimaHrs(rs.getInt("duracaominimahrs"));
					tipoAmbiente.setQtdMaximaReservasDia(rs.getInt("qtdmaximareservasdia"));
					tipoAmbiente.setTempoAdicionalPosteriorMin(rs.getInt("tempoadicionalposteriormin"));
					tipoAmbiente.setHorarioInicial(rs.getDate("horarioinicial"));
					tipoAmbiente.setHorarioFinal(rs.getDate("horariofinal"));
					lista.add(tipoAmbiente);
				}
			}
		}
		if (campoOrdenacao.equals("Descrição")) {
			Ordenacao.ordenarLista(lista, "descricao");
		} else if (campoOrdenacao.equals("Reservas/dia(Máximo)")) {
			Ordenacao.ordenarLista(lista, "qtdMaxReservasDia");
		} else if (campoOrdenacao.equals("Adicional")) {
			Ordenacao.ordenarLista(lista, "tempoAdicionalPosteriorMin");
		} else if (campoOrdenacao.equals("Inicial")) {
			Ordenacao.ordenarLista(lista, "horarioInicial");
		} else if (campoOrdenacao.equals("Final")) {
			Ordenacao.ordenarLista(lista, "horarioFinal");
		}

		if (ordem.contains("desc")) {
			Collections.reverse(lista);
		}
		return lista;

	}

}
