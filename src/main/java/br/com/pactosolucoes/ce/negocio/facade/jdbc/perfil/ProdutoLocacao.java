package br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.perfil.ProdutoLocacaoInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ProdutoLocacaoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ProdutoLocacaoVO</code>. Encapsula toda a
 * interação com o banco de dados.
 * 
 * @see SuperEntidade
 * <AUTHOR>
 */
public class ProdutoLocacao extends CEDao implements ProdutoLocacaoInterfaceFacade {

	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public ProdutoLocacao() throws Exception {
		super();		
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @seebr.com.pactosolucoes.ce.negocio.interfaces.perfil.ProdutoLocacaoInterfaceFacade#consultar(br.com.pactosolucoes.ce.comuns.to.
	 * ProdutoLocacaoTO)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<ProdutoLocacaoTO> consultar(final ProdutoLocacaoTO filtro) throws Exception {
//		super.consultarObj(this.getIdEntidade());
		// Prepara a string com o sql
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT PL.* \n");
		sql.append("FROM produtolocacao PL \n");
		// realiza as verificações para conhecer quais filtros usar na consulta
		if (filtro != null) {
			sql.append("WHERE 1 = 1 \n");

			if ((filtro.getDescricao() != null) && !filtro.getDescricao().equals("")) {
				sql.append("AND UPPER(PL.descricao) LIKE ? \n");
			}
			if (filtro.getTipo() != null) {
				sql.append("AND PL.tipo = ? \n");
			}
		}
		sql.append("ORDER BY PL.descricao");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		int numParam = 0;

		if (filtro != null) {
			if ((filtro.getDescricao() != null) && !filtro.getDescricao().equals("")) {
				dc.setString(++numParam, filtro.getDescricao().toUpperCase() + "%");
			}
			if (filtro.getTipo() != null) {
				dc.setInt(++numParam, filtro.getTipo().getCodigo());
			}
		}
		// guarda o resultado da consulta num ResultSet
		ResultSet tabelaResultado = dc.executeQuery();
		// retorna uma lista com os dados montados
		return (this.montarDadosConsulta(tabelaResultado));
	}

	/**
	 * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>) em um objeto da classe
	 * <code>ProdutoLocacaoTO</code>.
	 * 
	 * @param dadosSQL
	 * @return O objeto da classe <code>ProdutoLocacaoTO</code> com os dados devidamente montados.
	 * @throws Exception
	 */
	@Override
	public ProdutoLocacaoTO montarDados(final ResultSet dadosSQL) throws SQLException {
		ProdutoLocacaoTO obj = new ProdutoLocacaoTO();
		obj.setCodigo(dadosSQL.getInt("CODIGO"));
		obj.setDescricao(dadosSQL.getString("DESCRICAO"));
		obj.setEstoque(dadosSQL.getInt("ESTOQUE"));
		obj.setMinimoEstoque(dadosSQL.getInt("MINIMOESTOQUE"));
		obj.setRastreado(dadosSQL.getBoolean("RASTREADO"));
		obj.setTipo(TipoProdutoLocacao.getTipoProdutoLocacao(dadosSQL.getInt("TIPO")));
		obj.setValor(dadosSQL.getDouble("VALOR"));
		return obj;
	}

	/**
	 * Obtem os prdutos de locação
	 * 
	 * @param codigoPrm
	 *            identificador de produto locação
	 * @param nivelMontarDados
	 * @return montar dados dados da consulta
	 * @throws Exception
	 */
	public ProdutoLocacaoTO obter(final Integer codigoPrm) throws Exception {
		String sql = "SELECT * FROM PRODUTOLOCACAO WHERE CODIGO = ?";
		try (PreparedStatement sqlConsultar = this.con.prepareStatement(sql)) {
			sqlConsultar.setInt(1, codigoPrm.intValue());
			try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
				if (!tabelaResultado.next()) {
					throw new ConsistirException("Dados Não Encontrados ( Produto de Locação ).");
				}
				return (this.montarDados(tabelaResultado));
			}
		}
	}

	/**
	 * Método responsável por incluir no banco os registros de Produtos de Locação
	 * 
	 * @param obj
	 * @throws Exception
	 */
	public Integer incluir(final ProdutoLocacaoTO obj) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		String sql = "INSERT INTO PRODUTOLOCACAO ( DESCRICAO, ESTOQUE, MINIMOESTOQUE, TIPO, RASTREADO, VALOR ) VALUES ( ?, ?, ?, ?, ?, ? )";
		try (PreparedStatement sqlInserir = this.con.prepareStatement(sql)) {
			int i = 1;
			sqlInserir.setString(i++, obj.getDescricao());
			sqlInserir.setInt(i++, obj.getEstoque());
			if (obj.getMinimoEstoque() != null && obj.getMinimoEstoque().intValue() != 0) {
				sqlInserir.setInt(i++, obj.getMinimoEstoque());
			} else {
				sqlInserir.setNull(i++, 0);
			}
			sqlInserir.setInt(i++, obj.getTipo().getCodigo());
			sqlInserir.setBoolean(i++, obj.getRastreado());
			sqlInserir.setDouble(i++, obj.getValor());
			sqlInserir.execute();
		}
		obj.setCodigo(this.obterValorChavePrimariaCodigo());
		return this.obterValorChavePrimariaCodigo();
	}

	/**
	 * Exclui os registros referentes ao objeto passado como parametro
	 * 
	 * @param obj
	 * @throws SQLException
	 */
	public void excluir(final ProdutoLocacaoTO obj) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		Declaracao dc = new Declaracao("DELETE FROM PRODUTOLOCACAO WHERE CODIGO = ?", this.con);
		dc.setInt(1, obj.getCodigo().intValue());
		dc.execute();
	}

	/**
	 * Altera os registros referentes ao objeto passado como parametro
	 * 
	 * @param obj
	 * @throws Exception
	 */
	public void alterar(final ProdutoLocacaoTO obj) throws Exception {
//		super.alterarObj(this.getIdEntidade());
		String sql = "UPDATE PRODUTOLOCACAO SET DESCRICAO = ?, ESTOQUE = ?, MINIMOESTOQUE = ?, TIPO = ?, RASTREADO = ?, VALOR = ? WHERE CODIGO = ?";
		try (PreparedStatement sqlInserir = this.con.prepareStatement(sql)) {
			sqlInserir.setString(1, obj.getDescricao());
			sqlInserir.setInt(2, obj.getEstoque());
			sqlInserir.setInt(3, obj.getMinimoEstoque());
			sqlInserir.setInt(4, obj.getTipo().getCodigo());
			sqlInserir.setBoolean(5, obj.getRastreado());
			sqlInserir.setDouble(6, obj.getValor());
			sqlInserir.setInt(7, obj.getCodigo());
			sqlInserir.execute();
		}
	}
	
    
    /**
	 * Responsável por consultar todos os registros de produtos do central de eventos de forma simplificada, montando num mapa.
	 * <AUTHOR>
	 * 12/03/2013
	 */
	public Map<Integer, String> consultarSimplificado(TipoProdutoLocacao tipoProduto) throws Exception{
		Map<Integer, String> mapResult = new HashMap<Integer, String>();
		String sql = "SELECT descricao, codigo FROM produtolocacao WHERE tipo = "+tipoProduto.getCodigo();
		ResultSet consulta = criarConsulta(sql,con);
		while(consulta.next()){
			mapResult.put(consulta.getInt("codigo"), consulta.getString("descricao"));
		}
		return mapResult;
	}
	private PreparedStatement getPS() throws SQLException {
		StringBuilder sql = new StringBuilder();
		sql.append(" Select codigo,descricao,valor,tipo,rastreado\n");
		sql.append(" FROM ProdutoLocacao");
		return con.prepareStatement(sql.toString());
	}
    public String produtoRastreadoDescricao(Boolean rastreado){
		if(rastreado)
			return "Sim";
		else
			return "Não";
	}
	public String consultarJSON() throws Exception {
		StringBuilder json;
		boolean dados;
		try (ResultSet rs = getPS().executeQuery()) {
			json = new StringBuilder();
			json.append("{\"aaData\":[");
			dados = false;
			while (rs.next()) {
				dados = true;
				json.append("[\"").append(rs.getInt("codigo")).append("\",");
				json.append("\"").append(rs.getString("descricao")).append("\",");
				json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valor"))).append("\",");
				json.append("\"").append(TipoProdutoLocacao.getTipoProdutoLocacao(rs.getInt("tipo")).getDescricao()).append("\",");
				json.append("\"").append(produtoRastreadoDescricao(rs.getBoolean("rastreado"))).append("\"],");
			}
		}
		if (dados) {
			json.deleteCharAt(json.toString().length() - 1);
		}
		json.append("]}");
		return json.toString();
	}
	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
		List<ProdutoLocacaoTO> lista = new ArrayList<ProdutoLocacaoTO>();
		try(ResultSet rs = getPS().executeQuery()) {
			while (rs.next()) {
				ProdutoLocacaoTO produtoLocacao = new ProdutoLocacaoTO();
				String geral = rs.getInt("codigo") + rs.getString("descricao") + Formatador.formatarValorMonetario(rs.getDouble("valor"))
						+ TipoProdutoLocacao.getTipoProdutoLocacao(rs.getInt("tipo")).getDescricao() + produtoRastreadoDescricao(rs.getBoolean("rastreado"));
				if (geral.toLowerCase().contains(filtro.toLowerCase())) {
					produtoLocacao.setCodigo(rs.getInt("codigo"));
					produtoLocacao.setDescricao(rs.getString("descricao"));
					produtoLocacao.setValor(rs.getDouble("valor"));
					produtoLocacao.setTipo(TipoProdutoLocacao.getTipoProdutoLocacao(rs.getInt("tipo")));
					produtoLocacao.setRastreado(rs.getBoolean("rastreado"));
					lista.add(produtoLocacao);
				}
			}
		}
		if (campoOrdenacao.equals("Código")) {
			Ordenacao.ordenarLista(lista, "codigo");
		} else if (campoOrdenacao.equals("Nome")) {
			Ordenacao.ordenarLista(lista, "descricao");
		} else if (campoOrdenacao.equals("Tipo")) {
			Ordenacao.ordenarLista(lista, "tipoLocacao_Apresentar");
		} else if (campoOrdenacao.equals("Rastreado")) {
			Ordenacao.ordenarLista(lista, "rastreamentoString");
		} else if (campoOrdenacao.equals("Valor")) {
			Ordenacao.ordenarLista(lista, "valor");
		}
		if (ordem.contains("desc")) {
			Collections.reverse(lista);
		}
		return lista;
	}

}
