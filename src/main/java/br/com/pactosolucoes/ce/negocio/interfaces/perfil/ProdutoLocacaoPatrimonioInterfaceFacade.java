package br.com.pactosolucoes.ce.negocio.interfaces.perfil;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoPatrimonioTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 * 
 */
public interface ProdutoLocacaoPatrimonioInterfaceFacade extends SuperInterface {

	/**
	 * Responsável por realizar a consulta de <code>ProdutoLocacaoPatrimonio</code>.
	 * 
	 * @param filtro
	 *            Objeto que contem filtros pertinentes à entidade utilizado na consulta.
	 * @return <code>List<ProdutoLocacaoTO></code> Contendo vários objetos da classe <code>ProdutoLocacaoPatrimonioTO</code> resultantes da
	 *         consulta.
	 */
	List<ProdutoLocacaoPatrimonioTO> consultar(ProdutoLocacaoPatrimonioTO filtro) throws Exception;

	/**
	 * Obtem um determinado produto de locacao patrimonio.
	 * 
	 * @param codigo
	 *            Código identificador do produto de locacao patrimonio
	 * @return produto de locacao patrimonio que possui o código identificador
	 * @throws Exception
	 */
	ProdutoLocacaoPatrimonioTO obter(String codigo) throws Exception;

	/**
	 * Inclui um produto de locacao patrimonio.
	 * 
	 * @param perfil
	 *            produto de locacao patrimonio a ser incluído
	 * @throws Exception
	 */
	void incluir(ProdutoLocacaoPatrimonioTO patrimonio) throws Exception;

	/**
	 * Altera um produto de locacao patrimonio.
	 * 
	 * @param perfil
	 *            produto de locacao patrimonio a ser alterado
	 * @throws Exception
	 */
	void alterar(ProdutoLocacaoPatrimonioTO patrimonio) throws Exception;

	/**
	 * Exclui uma lista de patrimonios.
	 * 
	 * @param codigos
	 *            Códigos identificadores dos patrimônios a serem excluídos.
	 * @throws Exception
	 */
	void excluir(List<String> codigos) throws Exception;

}
