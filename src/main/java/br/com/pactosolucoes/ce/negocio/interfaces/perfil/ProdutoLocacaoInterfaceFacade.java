package br.com.pactosolucoes.ce.negocio.interfaces.perfil;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 */
public interface ProdutoLocacaoInterfaceFacade extends SuperInterface {

	/**
	 * Responsável por realizar a consulta de <code>ProdutoLocacao</code>.
	 * 
	 * @param filtro
	 *            Objeto que contem filtros pertinentes à entidade utilizado na consulta.
	 * @return <code>List<ProdutoLocacaoTO></code> Contendo vários objetos da classe <code>ProdutoLocacaoTO</code> resultantes da consulta.
	 */
	List<ProdutoLocacaoTO> consultar(ProdutoLocacaoTO filtro) throws Exception;
	
	public Map<Integer, String> consultarSimplificado(TipoProdutoLocacao tipoProduto) throws Exception;

	public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException;

	public String consultarJSON() throws Exception;

}
