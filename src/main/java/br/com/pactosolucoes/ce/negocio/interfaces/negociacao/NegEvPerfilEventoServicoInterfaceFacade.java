/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.negociacao;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoServicoTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 */
public interface NegEvPerfilEventoServicoInterfaceFacade extends SuperInterface {
	/**
	 * Responsável por realizar uma consulta de <code>NegEvPerfilEventoServico</code> através do valor do atributo
	 * <code>Integer codigoNegociacaoEvento</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido. Faz
	 * uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
	 * 
	 * @return List Contendo vários objetos da classe <code>NegEvPerfilEventoServico</code> resultantes da consulta.
	 * @throws Execption.
	 */
	List<NegEvPerfilEventoServicoTO> consultarPorNegociacaoEvento(Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe <code>NegEvPerfilEventoServico</code>.
	 * 
	 * @param negEvPerfilEventoServico
	 *            Objeto da classe <code>NegEvPerfilEventoServico</code> que será gravado no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            identificador da negociação.
	 * @throws Exception
	 */
	void incluir(NegEvPerfilEventoServicoTO negEvPerfilEventoServico, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe <code>NegEvPerfilEventoServico</code>.
	 * 
	 * @param negEvPerfilEventoServico
	 *            Objeto da classe <code>NegEvPerfilEventoServico</code> que será alterada no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            Identificador da negociação.
	 * @throws Execption.
	 */
	void alterar(NegEvPerfilEventoServicoTO negEvPerfilEventoServico, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por excluir no BD um objeto da classe <code>NegEvPerfilEventoServico</code>.
	 * 
	 * @param negEvPerfilEventoServico
	 *            Objeto da classe <code>NegEvPerfilEventoAmbiente</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluir(NegEvPerfilEventoServicoTO negEvPerfilEventoServico) throws Exception;

	/**
	 * Operação responsável por excluir varios objeto da classe <code>NegEvPerfilEventoProdutoLocacao</code> que tenham o mesmo código
	 * 
	 * @param codigos
	 *            lista de inteiros
	 * @throws Exception
	 */
	void excluir(List<Integer> codigos) throws Exception;

	/**
	 * Operação responsável por excluir uma negociação de evento no BD um objeto da classe <code>NegEvPerfilEventoServico</code>.
	 * 
	 * @param codigoNegociacao
	 *            Objeto da classe <code>NegEvPerfilEventoServico</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluirPorNegociacaoEvento(Integer codigoNegociacao) throws Exception;

}
