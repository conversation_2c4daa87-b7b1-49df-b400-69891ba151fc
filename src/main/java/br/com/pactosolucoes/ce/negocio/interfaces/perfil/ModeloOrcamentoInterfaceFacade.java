/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.perfil;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.ModeloOrcamentoTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 */
public interface ModeloOrcamentoInterfaceFacade extends SuperInterface {

	
	/**
	 * Responsável por consultar os modelos de orcamento registrados no perfil de evento escolhido na negociação do evento
	 * trazendo, se existir, as datas e horarios de envio por email e impressão
	 * <AUTHOR>
	 * 10/03/2011
	 * @param codigoPerfilEvento
	 * @param codigoNegociacao
	 * @return
	 * @throws Exception
	 */
	List<ModeloOrcamentoTO> consultarPorPerfilENegociacao(Integer codigoPerfilEvento, Integer codigoNegociacao) throws Exception;
	
	/**
	 * Operação Responsável por realizar uma consulta de <code>ModeloOrcamento</code> através do valor do atributo
	 * <code>Integer codigoPerfilEvento</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido. Faz uso
	 * da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
	 * 
	 * @param codigoPerfilEvento
	 * @return List - Contendo vários objetos da classe <code>ModeloOrcamento</code> resultantes da consulta.
	 * @throws Execption.
	 */
	List<ModeloOrcamentoTO> consultarPorPerfilEvento(final Integer codigoPerfilEvento) throws Exception;
	/**
	 * Inclui um modelo de contrato.
	 * 
	 * @param modeloContrato
	 *            Modelo de orçamento a ser incluído.
	 * @param codigoPerfilEvento
	 *            Código identificador do perfil de evento.
	 * @return Código identificador do modelo inserido.
	 * @throws Exception
	 */
	Integer incluir(ModeloOrcamentoTO modeloContrato, Integer codigoPerfilEvento) throws Exception;

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe <code>ModeloOrcamento</code>.
	 * 
	 * @param modeloOrcamento
	 *            - Objeto da classe <code>ModeloOrcamento</code> que será alterada no banco de dados.
	 * @param codigoPerfilEvento
	 *            - Identificador do perfil de evento.
	 * @throws Execption.
	 */
	void alterar(ModeloOrcamentoTO modeloContrato, Integer codigoPerfilEvento) throws Exception;

	/**
	 * Operação responsável por excluir varios objeto da classe <code>ModeloOrcamento</code> que tenham o mesmo código
	 * 
	 * @param codigos
	 *            - lista de inteiros
	 * @throws Exception
	 */
	void excluir(List<Integer> codigos) throws Exception;

	/**
	 * Operação responsável por excluir um perfil de evento de evento no BD um objeto da classe <code>ModeloOrcamento</code>.
	 * 
	 * @param codigoPerfilEvento
	 *            - Objeto da classe <code>Integer</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluirPorPerfilEvento(Integer codigoPerfilEvento) throws Exception;

	/**
	 * Operação responsável por obter no banco de dados um objeto da classe <code>ModeloOrcamento</code>.
	 * 
	 * @param codigo
	 *            Objeto da classe <code>ModeloOrcamento</code> que será obtido no banco de dados.
	 * @throws Exception
	 * @return montar dados da consulta.
	 */
	ModeloOrcamentoTO obter(Integer codigo) throws Exception;

}
