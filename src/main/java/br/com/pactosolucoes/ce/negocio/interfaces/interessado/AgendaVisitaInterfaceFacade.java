package br.com.pactosolucoes.ce.negocio.interfaces.interessado;


import br.com.pactosolucoes.ce.negocio.interessado.AgendaVisitaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public interface AgendaVisitaInterfaceFacade extends SuperInterface {

	void incluir(AgendaVisitaVO evento) throws Exception;

	void alterar(AgendaVisitaVO evento) throws Exception;

	void excluir(Integer codigo) throws Exception;

}
