/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.negociacao;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoAmbienteTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * <AUTHOR>
 */
public interface NegEvPerfilEventoAmbienteInterfaceFacade extends SuperInterface {
	/**
	 * Operação responsável por consultar um ambiente envolvido em uma negociação no banco de dados um objeto da classe
	 * <code>NegEvPerfilEventoAmbiente</code>.
	 * 
	 * @param codigoNegociacaoEvento
	 *            código identificador da gegociação.
	 * @throws Exception
	 * @return caso seja vazio retorna nulo se não retorna o o ambiente envolvido na negociação expecificada
	 */
	List<NegEvPerfilEventoAmbienteTO> consultarPorNegociacaoEvento(Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe <code>NegEvPerfilEventoAmbiente</code>.
	 * 
	 * @param negEvPerfilEventoAmbiente
	 *            Objeto da classe <code>NegEvPerfilEventoAmbiente</code> que será gravado no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            Identificador da negociação.
	 * @throws Exception.
	 */
	void incluir(NegEvPerfilEventoAmbienteTO negEvPerfilEventoAmbiente, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe <code>NegEvPerfilEventoAmbiente</code>.
	 * 
	 * @param negEvPerfilEventoAmbiente
	 *            Objeto da classe <code>NegEvPerfilEventoAmbiente</code> que será alterada no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            Identificador da negociação.
	 * @throws Execption.
	 */
	void alterar(NegEvPerfilEventoAmbienteTO negEvPerfilEventoAmbiente, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por excluir uma negociação de evento condição de pagamento no BD um objeto da classe
	 * <code>NegEvPerfilEventoAmbiente</code>.
	 * 
	 * @param negEvPerfilEventoAmbiente
	 *            Objeto da classe <code>Integet</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluir(NegEvPerfilEventoAmbienteTO negEvPerfilEventoAmbiente) throws Exception;

	/**
	 * Operação responsável por excluir por código de evento no BD um objeto da classe <code>NegEvPerfilEventoAmbiente</code>.
	 * 
	 * @param codigo
	 *            Objeto da classe <code>Integer</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluirPorCodigo(Integer codigo) throws Exception;

	/**
	 * Operação responsável por excluir uma negociação de evento no BD um objeto da classe <code>NegEvPerfilEventoAmbiente</code>.
	 * 
	 * @param codigoNegociacao
	 *            Objeto da classe <code>Integer</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluirPorNegociacaoEvento(Integer codigoNegociacao) throws Exception;

}
