package br.com.pactosolucoes.ce.negocio.facade.jdbc.interessado;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import br.com.pactosolucoes.ce.negocio.interessado.AgendaVisitaVO;
import br.com.pactosolucoes.ce.negocio.interfaces.interessado.AgendaVisitaInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>AgendaVisitaVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 * 
 * @see SuperEntidade
 * <AUTHOR>
 */
public class AgendaVisita extends SuperEntidade implements AgendaVisitaInterfaceFacade {

	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public AgendaVisita() throws Exception {
		super();
	}

	@Override
	public void alterar(final AgendaVisitaVO evento) throws Exception {
//		super.alterarObj(this.getIdEntidade());
	}

	@Override
	public void excluir(final Integer codigo) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM AGENDAVISITA WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(1, codigo);
		dc.execute();
	}
	/**
	 * Excluir visitas relacionadas a um evento
	 * @param codigo
	 * @throws Exception 
	 */
	public void excluirPorEvento(Integer codigo) throws Exception{
//		super.excluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM AGENDAVISITA WHERE EVENTO = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(1, codigo);
		dc.execute();
	}

	/**
	 * Atualiza a flag que determina que uma visita ja foi realizada
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 14/02/2011
	 */
	public void atualizarVisitaRealizada(boolean visita, int codigo) throws Exception{
//		super.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE AGENDAVISITA SET realizada = ? WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setBoolean(1, visita);
		dc.setInt(2, codigo);
		dc.execute();
	}
	
	/**
	 * Atualiza a data para uma nova visita
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 14/02/2011
	 */
	public void atualizarDataVisita(Date novaDataVisita, int codigo) throws Exception{
//		super.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE AGENDAVISITA SET datavisita = ? WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setDate(1, new java.sql.Date(novaDataVisita.getTime()));
		dc.setInt(2, codigo);
		dc.execute();
	}
	
	/**
	 * (non-Javadoc)
	 * 
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.interessado.AgendaVisitaInterfaceFacade#incluir(br.com.pactosolucoes.ce.negocio.interessado.AgendaVisitaVO)
	 */
	@Override
	public void incluir(final AgendaVisitaVO visita) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO agendavisita( datavisita, horariomarcado, interessado, ambiente, tipovisita, duracaomin, observacao, usuariocadastro ");
		if(visita.getCodigoEvento()== null){
			sql.append(" ) ");
		}else{
			sql.append(", evento) ");
		}
		sql.append("VALUES (?, ?, ?, ?, ?, ?, ?, ?");
		if(visita.getCodigoEvento()== null){
			sql.append(" ) ");
		}else{
			sql.append(", ?) ");
		}
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setDate(++i, new java.sql.Date(visita.getDataVisita().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(visita.getHorarioMarcado().getTime()));
		dc.setInt(++i, visita.getInteressado().getCodigo());
		dc.setInt(++i, visita.getAmbiente());
		dc.setInt(++i, visita.getTipoVisita());
		dc.setInt(++i, visita.getDuracaoMin());
		dc.setString(++i, visita.getObservacoes());
		dc.setInt(++i, visita.getUsuarioCad());
		if(visita.getCodigoEvento()!= null){
			dc.setInt(++i, visita.getCodigoEvento());
		}
		dc.execute();
	}

	/**
	 * Obtem a AgendaVisitaVO pelo codigo passado, caso nao seja encontrado
	 * nem um registro será retornado null
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 14/02/2011
	 */
	public AgendaVisitaVO obter (int codigo) throws Exception {
		AgendaVisitaVO vo = null; 
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, datavisita, horariomarcado, interessado, ambiente, tipovisita ");
		sql.append(", duracaomin, observacao, usuariocadastro, evento, realizada");
		sql.append(" FROM agendavisita ");
		sql.append(" WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(1, codigo);
		ResultSet rs = dc.executeQuery();
        if (rs.next()) {
        	vo = new AgendaVisitaVO();
        	vo.setCodigo(rs.getInt("codigo"));
        	vo.setDataVisita(rs.getDate("datavisita"));
        	vo.setHorarioMarcado(rs.getTime("horariomarcado"));
        	vo.getInteressado().setCodigo(rs.getInt("interessado"));
        	vo.setAmbiente(rs.getInt("ambiente"));
        	vo.setTipoVisita(rs.getInt("tipovisita"));
        	vo.setDuracaoMin(rs.getInt("duracaomin"));
        	vo.setObservacoes(rs.getString("observacao"));
        	vo.setUsuarioCad(rs.getInt("usuariocadastro"));
        	vo.setCodigoEvento(rs.getInt("evento"));
        	vo.setRealizado(rs.getBoolean("realizada"));
        	
        	// Atualizar data agendada com horário
    		if (vo.getHorarioFinal() == null) {
    			vo.setHorarioFinal(Uteis.somarCampoData(vo.getHorarioMarcado(), Calendar.MINUTE, vo.getDuracaoMin()));
    		}
        }
        return vo;
	}
	
}
