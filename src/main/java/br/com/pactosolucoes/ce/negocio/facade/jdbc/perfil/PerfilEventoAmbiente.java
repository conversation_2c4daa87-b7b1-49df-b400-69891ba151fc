/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import negocio.facade.jdbc.arquitetura.SuperEntidade;
import br.com.pactosolucoes.ce.comuns.enumerador.SituacaoAmbiente;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.perfil.PerfilEventoAmbienteInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PerfilEventoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PerfilEventoVO</code>. Encapsula toda a
 * interação com o banco de dados.
 * 
 * @see SuperEntidade
 * <AUTHOR>
 * 
 */
public class PerfilEventoAmbiente extends CEDao implements PerfilEventoAmbienteInterfaceFacade {

	/**
	 * @throws Exception
	 */
	public PerfilEventoAmbiente() throws Exception {
		super();
		this.setIdEntidade("PerfilEvento");
	}

	@SuppressWarnings("unchecked")
	public List<PerfilEventoAmbienteTO> consultarPorPerfilEvento(final Integer codigoPerfilEvento) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT PEA.*, A.descricao AS descricaoambiente \n");
		sql.append("FROM perfileventoambiente PEA \n");
		sql.append("INNER JOIN ambiente A ON PEA.ambiente = A.codigo and A.situacao=?\n");
		sql.append("WHERE PEA.perfilevento = ? \n");
		sql.append("ORDER BY A.descricao");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, SituacaoAmbiente.ATIVO.getCodigo());
		dc.setInt(2, codigoPerfilEvento);

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}
	
	public void incluir(final PerfilEventoAmbienteTO ambiente, final Integer codigoPerfilEvento) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO perfileventoambiente (perfilevento, ambiente, valor, observacao, nrmaximoconvidados) \n");
		sql.append("VALUES (?, ?, ?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoPerfilEvento);
		dc.setInt(2, ambiente.getCodigoAmbiente());
		dc.setDouble(3, ambiente.getValor());
		dc.setString(4, ambiente.getObservacao());
		dc.setInt(5, ambiente.getNrMaximoConvidados());

		dc.execute();
		ambiente.setCodigo(this.obterValorChavePrimariaCodigo());
	}

	public void alterar(final PerfilEventoAmbienteTO ambiente, final Integer codigoPerfilEvento) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE perfileventoambiente SET perfilevento = ?, ambiente = ?, valor = ?, observacao = ?, nrmaximoconvidados = ? \n");
		sql.append("WHERE codigo = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoPerfilEvento);
		dc.setInt(2, ambiente.getCodigoAmbiente());
		dc.setDouble(3, ambiente.getValor());
		dc.setString(4, ambiente.getObservacao());
		dc.setInt(5, ambiente.getNrMaximoConvidados());
		dc.setInt(6, ambiente.getCodigo());

		dc.execute();
	}

	public void excluir(final List<Integer> codigos) throws Exception {
		StringBuilder sql = new StringBuilder("DELETE FROM perfileventoambiente WHERE codigo IN (");
		for (int i = 0; i < (codigos.size() - 1); i++) {
			sql.append(codigos.get(i) + ", ");
		}
		sql.append(codigos.get(codigos.size() - 1) + ")");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.execute();
	}

	public void excluirPorPerfilEvento(final Integer codigoPerfilEvento) throws Exception {
		Declaracao dc = new Declaracao("DELETE FROM perfileventoambiente WHERE perfilevento = ?", this.con);
		dc.setInt(1, codigoPerfilEvento);
		dc.execute();
	}

	@Override
	public PerfilEventoAmbienteTO montarDados(final ResultSet dadosSQL) throws Exception {
		PerfilEventoAmbienteTO obj = new PerfilEventoAmbienteTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setNrMaximoConvidados(dadosSQL.getInt("nrmaximoconvidados"));
		obj.setObservacao(dadosSQL.getString("observacao"));
		obj.setValor(dadosSQL.getDouble("valor"));
		obj.setCodigoAmbiente(dadosSQL.getInt("ambiente"));
		obj.setDescricaoAmbiente(dadosSQL.getString("descricaoambiente"));
		obj.setContemEvento(verificarExisteVinculo(obj));
		return obj;
	}
	
	public boolean verificarExisteVinculo(PerfilEventoAmbienteTO pea) throws Exception{
		String sql = "SELECT * FROM negociacaoeventoperfileventoambiente nepa , perfileventoambientelayout peal "
				    +"WHERE nepa.tipolayout = peal.codigo AND peal.perfileventoambiente = "+pea.getCodigo();
		ResultSet resultSet = criarConsulta(sql,con);
		return resultSet.next();
	}

}
