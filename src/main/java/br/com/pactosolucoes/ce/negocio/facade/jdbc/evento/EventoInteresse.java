package br.com.pactosolucoes.ce.negocio.facade.jdbc.evento;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpSession;

import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import br.com.pactosolucoes.ce.comuns.enumerador.EnumTipoVisita;
import br.com.pactosolucoes.ce.comuns.enumerador.PrioridadesProspects;
import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoContato;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoReserva;
import br.com.pactosolucoes.ce.comuns.to.DisponibilidadeTO;
import br.com.pactosolucoes.ce.comuns.to.EventoTO;
import br.com.pactosolucoes.ce.comuns.to.ProspectsTO;
import br.com.pactosolucoes.ce.comuns.to.ReservaTO;
import br.com.pactosolucoes.ce.comuns.to.TagsTO;
import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegociacaoEvento;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil.PerfilEvento;
import br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade;
import br.com.pactosolucoes.comuns.util.DateUtilities;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import negocio.comuns.utilitarias.Calendario;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>EventoInteresseVO</code>. Responsável
 * por implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 *
 * <AUTHOR>
 */
public class EventoInteresse extends CEDao implements EventoInteresseInterfaceFacade {

	private static final Integer TOTALMENTE_DISPONIVEL = 1;
	private static final Integer HORARIOS_DISPONIVEIS = 2;
	private static final Integer SEM_HORARIOS_DISPONIVEIS = 3;
	private static final String HA_OUTRAS_RESERVAS = "Há outras reservas";
	private static final String NAO_HA_OUTRAS_RESERVAS = "Não há outras reservas";
	private static final String DISPONIVEL = "Há disponibilidade";
	private static final String SEM_DISPONIBILIDADE = "Não há disponibilidade";
	private static final String VAZIO = "Vazio";
	private static final String LOTADO = "Lotado";
	private static final String NADA = "";
	private static final String DIA_VAZIO = "     Dia Vazio    ";

	/**
	 * Construtor padrão da classe
	 *
	 * @throws Exception
	 */
	public EventoInteresse() throws Exception {
		super();
	}

	public EventoInteresse(Connection con) throws Exception {
		super(con);
	}

	public EventoInteresse(HttpSession session) throws Exception {
		super(session);
	}

	/**
	 * Responsável por montar os dados de prospects, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
	 *
	 * @return List Contendo vários objetos da classe <code>ProspectsTO</code> resultantes da consulta.
	 */
	public static List<ProspectsTO> montarDadosProspects(final ResultSet tabelaResultado) throws Exception {
		// criar lista de prospects
		List<ProspectsTO> vetResultado = new ArrayList<ProspectsTO>();
		// enquanto houverem resultados na resultset
		while (tabelaResultado.next()) {
			// instanciar objeto prospects
			ProspectsTO obj = new ProspectsTO();
			// montar objeto
			obj.setCliente(tabelaResultado.getString("NOMECLIENTE"));
			obj.setEvento(tabelaResultado.getString("NOMEEVENTO"));
			obj.setDataProximoContato(tabelaResultado.getDate("PROXIMOCV"));
			obj.setCodigoInteresse(tabelaResultado.getInt("eventointeresse"));
			obj.setSituacao(Situacao.getSituacao(tabelaResultado.getInt("SITUACAO")));
			obj.setInteressado(tabelaResultado.getInt("INTERESSADO"));
			if(obj.getSituacao().equals(Situacao.CADASTRADO) || obj.getSituacao().equals(Situacao.PENDENTE)){
				Integer perfilInteresse = tabelaResultado.getInt("PERFILINTERESSE");
				if(!UteisValidacao.emptyNumber(perfilInteresse)){
					obj.setTipoEvento(new PerfilEvento().obterNomePerfil(perfilInteresse));
				}
			}else{
				obj.setTipoEvento(tabelaResultado.getString("DESCRICAO"));
			}

			obj.setPrioridade(tabelaResultado.getInt("PRIORIDADE"));
			obj.setTipoContato(tabelaResultado.getInt("TIPOCONTATO"));
			if(obj.getSituacao() != null && obj.getSituacao().getCodigo()>2){
				obj.setData(new NegociacaoEvento().getDataEvento(obj.getCodigoInteresse()));
			}else
				obj.setData(tabelaResultado.getDate("DATAINTERESSE"));
			if (tabelaResultado.getInt("CODIGOCONVERSA") > 0) {
				obj.setPossuiConversa(true);
			}
			if (obj.getAmbiente() == null){
				obj.setAmbiente(new AmbienteVO());
			}
			obj.getAmbiente().setDescricao(tabelaResultado.getString("AMBIDESC"));

			//Inicio - set a classe css (arq.: ce.css) de acordo com o grau de prioridade
			if (obj.getPrioridade() == PrioridadesProspects.CONDICAO_UM.getPrioridade() ||
					obj.getPrioridade() == PrioridadesProspects.CONDICAO_DOIS.getPrioridade() ){
				obj.setCssColor("cor_forte");
			} else if (obj.getPrioridade() == PrioridadesProspects.CONDICAO_TRES.getPrioridade()
					|| obj.getPrioridade() == PrioridadesProspects.CONDICAO_QUATRO.getPrioridade()){
				obj.setCssColor("cor_normal");
			} else if (obj.getDataProximoContato() != null
					&& obj.getPrioridade() <= PrioridadesProspects.CONDICAO_CINCO.getPrioridade()
					&& obj.getPrioridade() != PrioridadesProspects.CANCELADO_ENCERRADO.getPrioridade()
					&& Uteis.nrDiasEntreDatas(Calendario.hoje(), obj.getDataProximoContato() ) < 0){
				obj.setCssColor("cor_suave");
			}
			//Fim - set a classe css (arq.: ce.css) de acordo com o grau de prioridade

			// inserir resultado na lista
			vetResultado.add(obj);
		}
		// retornar a lista

		return vetResultado;
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	@Override
	public void alterar(final EventoInteresseVO evento) throws Exception {
//		super.alterarObj(this.getIdEntidade());
		boolean dataInteressaInformada = evento.getDataInteresse() != null;
		boolean ambienteInteresseInformado = (evento.getAmbienteInteresse() != null) && (evento.getAmbienteInteresse().getCodigo() != null)
				&& !evento.getAmbienteInteresse().getCodigo().equals(0);


		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE eventointeresse SET interessado = ?, ");
		if (dataInteressaInformada) {
			sql.append("datainteresse = ?, ");
		}
		if (ambienteInteresseInformado) {
			sql.append("ambienteinteresse = ?, ");
		}
		sql.append("observacao = ?, numeroconvidados = ?, nomeevento = ?, situacao = ?, queme = ? \n");
		sql.append("WHERE codigo = ?");

		Declaracao stm = new Declaracao(sql.toString(), this.con);
		int i = 0;
		stm.setInt(++i, evento.getInteressado().getCodigo());
		if (dataInteressaInformada) {
			stm.setDate(++i, new java.sql.Date(evento.getDataInteresse().getTime()));
		}
		if (ambienteInteresseInformado) {
			stm.setInt(++i, evento.getAmbienteInteresse().getCodigo());
		}
		stm.setString(++i, evento.getObservacao());
		stm.setInt(++i, evento.getNumeroConvidados());
		stm.setString(++i, evento.getNomeEvento());
		stm.setInt(++i, evento.getSituacao().getCodigo());
		stm.setString(++i, evento.getQuemE());
		stm.setInt(++i, evento.getCodigo());

		stm.execute();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	@Override
	public void excluir(final Integer codigo) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		try {
			// desabilitar o auto commit
			this.con.setAutoCommit(false);
			this.excluir(this.getIdEntidade());
			// preparar a string
			String sql = "DELETE FROM EVENTOINTERESSE WHERE ((codigo = ?))";
			// preparar o preparedstatement
			PreparedStatement sqlExcluir = this.con.prepareStatement(sql);
			// setar o código como parametro
			sqlExcluir.setInt(1, codigo.intValue());
			// executar a consulta
			sqlExcluir.execute();
			// comitar
			this.con.commit();
			// caso erros
		} catch (Exception e) {
			// cancelar a transação
			this.con.rollback();
			this.con.setAutoCommit(true);
			throw e;
		} finally {
			this.con.setAutoCommit(true);
		}
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	public int incluirRegistro(final EventoInteresseVO obj) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		boolean perfilInformado = !UteisValidacao.emptyNumber(obj.getCodigoPerfilEvento());
		boolean dataInteressaInformada = obj.getDataInteresse() != null;
		boolean ambienteInteresseInformado = (obj.getAmbienteInteresse() != null) && (obj.getAmbienteInteresse().getCodigo() != null)
				&& !obj.getAmbienteInteresse().getCodigo().equals(0);

		StringBuilder sql = new StringBuilder();
		// TODO: não está inserindo horariomarcadohr e horariomarcadomin, pois estes campos não estão na tela 'Cadastro Inicial'
		sql.append("INSERT INTO eventointeresse( ");
		sql.append("interessado, ");
		if (dataInteressaInformada) {
			sql.append("datainteresse, ");
		}
		if (ambienteInteresseInformado) {
			sql.append("ambienteinteresse, ");
		}
		if (perfilInformado) {
			sql.append("perfilinteresse, ");
		}
		sql.append("observacao, numeroconvidados, nomeevento, situacao, queme ");
		// sql.append(", horariomarcadohr, horariomarcadomin");
		sql.append(") VALUES ( ?, ?, ");
		if (dataInteressaInformada) {
			sql.append("?, ");
		}
		if (ambienteInteresseInformado) {
			sql.append("?, ");
		}
		if (perfilInformado) {
			sql.append("?, ");
		}
		sql.append("?, ?, ?, ?");
		// sql.append(", ?, ? ");
		sql.append(");");

		Declaracao stm = new Declaracao(sql.toString(), this.con);
		int i = 0;
		stm.setInt(++i, obj.getInteressado().getCodigo());
		if (dataInteressaInformada) {
			stm.setDate(++i, new java.sql.Date(obj.getDataInteresse().getTime()));
		}
		if (ambienteInteresseInformado) {
			stm.setInt(++i, obj.getAmbienteInteresse().getCodigo());
		}
		if (perfilInformado) {
			stm.setInt(++i, obj.getCodigoPerfilEvento());
		}
		stm.setString(++i, obj.getObservacao());
		stm.setInt(++i, obj.getNumeroConvidados());
		stm.setString(++i, obj.getNomeEvento());
		stm.setInt(++i, obj.getSituacao().getCodigo());
		stm.setString(++i, obj.getQuemE());
		stm.execute();
		return this.obterValorChavePrimariaCodigo();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.EventoInteresseInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public EventoInteresseVO obter(final Integer codigo) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, interessado, perfilinteresse, datainteresse, ambienteinteresse, observacao, numeroconvidados, nomeevento, situacao, queme ");
		sql.append("FROM eventointeresse ");
		sql.append("WHERE codigo = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigo);
		ResultSet tabelaResultado = dc.executeQuery();

		List<EventoInteresseVO> result = this.montarDadosConsulta(tabelaResultado);
		// Retorna a primeira posição da lista que é o resultado da consulta
		return result.isEmpty() ? null : result.get(0);

	}

	/**
	 * Responsável por realizar uma consulta de <code>EventoInteresseVO</code> através do valor do atributo
	 * <code>EventoInteresseVO parametro</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido. Faz
	 * uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
	 *
	 * @param parametro
	 * @return List - Contendo vários objetos da classe <code>EventoInteresseVO</code> resultantes da consulta.
	 * @exception Exception
	 *                - Caso haja problemas de conexão.
	 */
	@SuppressWarnings("unchecked")
	public List<EventoInteresseVO> consultarConversas(final EventoInteresseVO parametro) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		boolean controlarAcesso = false;
		this.consultar(this.getIdEntidade(), controlarAcesso);
		String sqlStr = "SELECT codigo, interessado, datainteresse, ambienteinteresse, observacao, numeroconvidados, "
				+ "nomeevento, situacao, queme FROM eventointeresse";
		Statement stm = this.con.createStatement();
		ResultSet tabelaResultado = stm.executeQuery(sqlStr);
		return (this.montarDadosConsulta(tabelaResultado));

	}

	/**
	 * Faz uma consulta no banco para ver se existe NomeInteressado
	 *
	 * @return tabelaResultado
	 * @throws Exception
	 * <AUTHOR>
	 */
	public String consultarNomeInteressado(final Integer codigoEvento) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		String nome = "";
		StringBuilder sql = new StringBuilder();
		sql
				.append("SELECT I.NOMECLIENTE FROM INTERESSADO I INNER JOIN EVENTOINTERESSE EI ON EI.INTERESSADO = I.CODIGO WHERE EI.CODIGO = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoEvento);
		ResultSet rs = dc.executeQuery();
		if (rs.next()) {
			nome = rs.getString("NOMECLIENTE");
		}

		return nome;

	}

	/**
	 * Operação responsável por obter a chave primaria no banco de dados um objeto da classe <code>EventoInteresseVO</code>.
	 *
	 * @throws Exception
	 *             - Caso haja problemas de conexão, restrição de acesso ou validação de dados.
	 * @return um inteiro da tabela de resultado
	 */
	public Integer obterValorChavePrimariaCodigo() throws Exception {
		this.inicializar();
		Declaracao dc = new Declaracao("SELECT MAX(codigo) FROM eventointeresse", this.con);
		ResultSet tabelaResultado = dc.executeQuery();
		tabelaResultado.next();
		return tabelaResultado.getInt(1);
	}

	/**
	 * Monta um objeto <code>EventoInteresseVO</code> a partir do <code>ResultSet</code> de uma consulta a visitas.
	 *
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>EventoInteresseVO</code>.
	 * @throws Exception
	 */
	public EventoInteresseVO montarDados(final ResultSet dadosSQL) throws Exception {
		// monta o objeto EventoInteresseVO com os dados da consulta
		EventoInteresseVO obj = new EventoInteresseVO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.getInteressado().setCodigo(dadosSQL.getInt("interessado"));
		obj.setDataInteresse(dadosSQL.getDate("datainteresse"));
		obj.getAmbienteInteresse().setCodigo(dadosSQL.getInt("ambienteinteresse"));
		obj.setObservacao(dadosSQL.getString("observacao"));
		obj.setNumeroConvidados(dadosSQL.getInt("numeroconvidados"));
		obj.setNomeEvento(dadosSQL.getString("nomeevento"));
		obj.setSituacao(Situacao.getSituacao(dadosSQL.getInt("situacao")));
		obj.setQuemE(dadosSQL.getString("queme"));
		obj.setCodigoPerfilEvento(dadosSQL.getInt("perfilinteresse"));
		obj.setDataEvento();

		// retorna o objeto
		return obj;
	}

	public EventoInteresseVO montarDadosEvento(final ResultSet dadosSQL) throws Exception{
		// monta o objeto EventoInteresseVO com os dados da consulta
		EventoInteresseVO obj = new EventoInteresseVO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.getInteressado().setCodigo(dadosSQL.getInt("interessado"));
		obj.setDataInteresse(dadosSQL.getDate("datainteresse"));
		obj.getAmbienteInteresse().setCodigo(dadosSQL.getInt("ambienteinteresse"));
		obj.setObservacao(dadosSQL.getString("observacao"));
		obj.setNumeroConvidados(dadosSQL.getInt("numeroconvidados"));
		obj.setNomeEvento(dadosSQL.getString("nomeevento"));
		obj.setSituacao(Situacao.getSituacao(dadosSQL.getInt("situacao")));
		obj.setNomePerfilEvento(dadosSQL.getString("descricao"));
		obj.setQuemE(dadosSQL.getString("queme"));
		obj.setCodigoPerfilEvento(dadosSQL.getInt("perfilinteresse"));
		obj.setDataEvento();

		// retorna o objeto
		return obj;

	}

	/**
	 * Altera a situação de um determinado Evento.
	 *
	 * @param codigoEventoInteresse
	 *            Código identificador do Evento.
	 * @param situacao
	 *            Situação do Evento.
	 * @throws Exception
	 */
	public void alterarSituacao(final Integer codigoEventoInteresse, final Situacao situacao) throws Exception {
//		super.alterarObj(this.getIdEntidade());
		// Preparar query para alteração da situação
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE eventointeresse SET situacao = ? ");
		sql.append("WHERE codigo = ?");

		Declaracao stm = new Declaracao(sql.toString(), this.con);

		// Estabelecer parâmetros
		stm.setInt(1, situacao.getCodigo());
		stm.setInt(2, codigoEventoInteresse);

		// Executar alteração
		stm.execute();
	}

	/**
	 * Faz a consulta nas tabelas de EventoInteresse e Interessado, tendo como filtro os dados recebidos como parametro contidos no objeto
	 * ProspectsTO
	 *
	 * @param prospect
	 * @return Lista de objetos ProspectTO
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List<ProspectsTO> listarProspects(final ProspectsTO prospect,ConfPaginacao confPaginacao,
                boolean apenasEventos, boolean apenasConversas, boolean todasSituacoes,
                boolean dataSimples, Integer codigoRecibo) throws Exception {
		//1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
		confPaginacao.configurarNavegacao();
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT PROSPECTS.*, \n");
		sql.append("CASE \n");
		sql.append("WHEN PROSPECTS.ORDA AND PROSPECTS.ORDC AND NOT PROSPECTS.ORDI THEN 100 \n");
		sql.append("WHEN PROSPECTS.ORDD AND NOT PROSPECTS.ORDI THEN 80      \n");
		sql.append("WHEN PROSPECTS.ORDE AND NOT PROSPECTS.ORDI THEN 75      \n");
		sql.append("WHEN PROSPECTS.ORDF AND NOT PROSPECTS.ORDI THEN 70      \n");
		sql.append("WHEN PROSPECTS.ORDG AND NOT PROSPECTS.ORDI THEN 65      \n");
//		sql.append("WHEN PROSPECTS.ORDH AND NOT PROSPECTS.ORDI THEN 60      \n");
		sql.append("WHEN PROSPECTS.ORDI THEN 0       \n");
		sql.append("ELSE 20 \n");
		sql.append("END AS PRIORIDADE \n");
		sql.append("FROM ( \n");
		sql.append("SELECT E.CODIGO      AS EVENTOINTERESSE  , \n");
		sql.append("       I.CODIGO      AS INTERESSADO, \n");
		sql.append("       E.DATAINTERESSE                      , \n");
		sql.append("       E.PERFILINTERESSE                      , \n");
		sql.append("       E.NOMEEVENTO                      , \n");
		sql.append("       CON.PROXIMOCV                       , \n");
		sql.append("       CON.CODIGOCONVERSA                , \n");
		sql.append("       CON.TIPOCONTATO                    , \n");
		sql.append("       I.NOMECLIENTE                     , \n");
		sql.append("       PE.DESCRICAO                      , \n");
		sql.append("       AMB.DESCRICAO as AMBIDESC         , \n");
		sql.append("       CASE \n");
		sql.append("              WHEN E.situacao IS NULL \n");
		sql.append("              THEN 11 \n");
		sql.append("              ELSE E.situacao \n");
		sql.append("       END AS situacao, \n");
		sql.append("CASE \n");
		//A = possuir reserva, estar no status pre-reservado
		sql.append(" WHEN E.situacao = ? \n");
        sql.append(" THEN TRUE \n");
        sql.append(" ELSE FALSE \n");
        sql.append("END AS orda, \n");

        //C = estar a menos de 5 dias do evento, diferença da data de
        //hoje com a data da negociação (data de hoje parametrizada, ou seja, preenchida 'no java')
        sql.append("CASE \n");
        sql.append(" WHEN NE.dataevento -  ? < interval '5 days' \n");
        sql.append(" THEN TRUE \n");
        sql.append(" ELSE FALSE \n");
        sql.append("END AS ordc, \n");
        //D = data do proximo contato do tipo pre reserva com data do proximo contato menor que (hoje + 5 dias)
        sql.append("CASE \n");
        sql.append("  WHEN (CON.PROXIMO - ? <=  interval '0 days') AND ( E.situacao = ? ) \n");
        sql.append(" THEN TRUE \n");
        sql.append(" ELSE FALSE \n");
        sql.append("END AS ordd, \n");
        //E = contato do tipo emissão de orçamento sem data do proximo contato.
        sql.append("CASE \n");
        sql.append(" WHEN E.situacao = ? AND CON.PROXIMO IS NULL \n");
        sql.append(" THEN TRUE \n");
        sql.append(" ELSE FALSE \n");
        sql.append("END AS orde, \n");
        //F = contato do tipo cadastro inicial, pendente, interessado, não-reservado sem data do proximo contato.
        sql.append("CASE \n");
        sql.append(" WHEN ( E.situacao = ? OR E.situacao = ? OR E.situacao = ? OR E.situacao = ?  ) AND CON.PROXIMO IS NULL \n");
        sql.append(" THEN TRUE \n");
        sql.append(" ELSE FALSE \n");
        sql.append("END AS ordf, \n");

        //G = contato do tipo agenda visita com data do proximo contato EXPIRADA e não prereserva
        sql.append("CASE \n");
        sql.append("WHEN (con.tipocontato = ? ) OR (CON.PROXIMO - ? <=  interval '0 days' AND E.situacao <> ? )\n");
        sql.append("THEN TRUE    \n");
        sql.append("ELSE FALSE \n");
        sql.append("END AS ordg, \n");

		//I = evento cancelado ou encerrado
        sql.append("CASE \n");
        sql.append(" WHEN (E.situacao = ? OR E.situacao = ? ) \n");
        sql.append(" THEN TRUE \n");
        sql.append(" ELSE FALSE \n");
        sql.append("END AS ordi \n");

        sql.append("FROM   eventointeresse E \n");
		sql.append("       FULL JOIN (\n");

		sql.append("       	SELECT  DISTINCT ON (CV.interessado) CV.interessado, CV.eventointeresse, CV.dataproximocontato  AS proximoCV, CV.tipocontato, CV.codigo AS codigoconversa,");
		sql.append("       		AV.datavisita AS proximoAV, AV.interessado AS interessadoAV,");
		sql.append("       		CASE WHEN ((AV.datavisita IS NULL) OR (CV.dataproximocontato <  AV.datavisita)) THEN CV.dataproximocontato ELSE AV.datavisita END AS PROXIMO");
		sql.append("       	FROM conversa AS CV");
		sql.append("       		LEFT JOIN AGENDAVISITA AV ON AV.interessado = CV.INTERESSADO");
		sql.append("       	WHERE ultimocontato IS TRUE");

		sql.append("              ) AS CON \n");
		sql.append("       ON     E.codigo = con.eventointeresse \n");
		sql.append("       LEFT JOIN INTERESSADO I \n");
		sql.append("       ON     I.CODIGO = E.INTERESSADO \n");
		sql.append("       OR     I.CODIGO = CON.INTERESSADO \n");
		sql.append("       LEFT JOIN negociacaoevento NE \n");
		sql.append("	   ON     NE.EVENTOINTERESSE = E.CODIGO \n");
		sql.append(" 	   LEFT JOIN negociacaoperfilevento NPE");
		sql.append("       ON    NPE.negociacaoevento = NE.codigo \n");
		sql.append("       LEFT JOIN perfilevento PE \n");
		sql.append("       ON    NPE.perfilevento = PE.codigo \n");
		sql.append("       LEFT JOIN \n");
		sql.append("              (SELECT  interessado, \n");
		sql.append("                       MAX(codigo)AS ultimoevento \n");
		sql.append("              FROM     eventointeresse \n");
		sql.append("              GROUP BY interessado \n");
		sql.append("              ) AS U \n");
		sql.append("       ON     U.interessado = I.codigo \n");
		sql.append("       LEFT JOIN AMBIENTE AMB \n");
		sql.append("       ON     AMB.CODIGO = E.AMBIENTEINTERESSE \n");
		sql.append("WHERE  ( \n");
		sql.append("              ( \n");
		sql.append("                     E.codigo               IS NULL \n");
		sql.append("              AND    U.ultimoevento         IS NULL \n");
		sql.append("              AND    CON.codigoconversa IS NOT NULL \n");
		sql.append("              ) \n");
		sql.append("       OR \n");
		sql.append("              ( \n");
		sql.append("                     E.CODIGO IS NOT NULL \n");
		sql.append("              ) \n");
		sql.append("       )");

		confPaginacao.iniciarPaginacao(this);

		filtrosProspects(prospect, sql,apenasEventos, apenasConversas,  dataSimples, codigoRecibo);

		sql.append(" ) AS PROSPECTS ");
		//irá filtrar se exibe os eventos cancelados ou encerrados
		if(!todasSituacoes){
			sql.append("WHERE PROSPECTS.SITUACAO != ? AND PROSPECTS.SITUACAO != ? \n");
		}
		sql.append(" ORDER BY PRIORIDADE DESC,  proximocv ASC, datainteresse ASC");

		confPaginacao.addPaginacao(sql);
		//seta os valores dos filtros
		filtrosValoresStatement(prospect, confPaginacao.getStm(), todasSituacoes, dataSimples, codigoRecibo);

		// executar a pesquisa e guardar num resultset
		ResultSet tabelaResultado = confPaginacao.consultaPaginada();

		// retornar a lista gerada pelo montardadosProspects
		return EventoInteresse.montarDadosProspects(tabelaResultado);
	}

	/**
	 * Filtros da consulta de prospects
	 * @param prospect
	 * @param sql
	 * @throws Exception
	 */
	private void filtrosProspects(final ProspectsTO prospect, StringBuffer sql,boolean apenasEventos, boolean apenasConversas,
			boolean dataSimples, Integer codigoRecibo) throws Exception {
		// verificar os filtros preenchidos

		if ((prospect.getCliente() != null) && !prospect.getCliente().equals("")) {
			sql.append("AND UPPER(I.NOMECLIENTE) iLIKE ? ");
		}
		if ((prospect.getEvento() != null) && !prospect.getEvento().equals("")) {
			sql.append("AND UPPER(E.NOMEEVENTO) iLIKE ? ");
		}
		if (prospect.getData() != null && dataSimples) {
			sql.append("AND E.DATAINTERESSE = ? ");
		}
		if ((prospect.getAmbiente().getCodigo() != null) && !prospect.getAmbiente().getCodigo().equals(0)) {
			sql.append("AND E.AMBIENTEINTERESSE = ? ");
		}
		if ((prospect.getAmbiente().getTipoAmbiente() != null) && !prospect.getAmbiente().getTipoAmbiente().equals(0)) {
			sql.append("AND AMB.TIPOAMBIENTE = ? ");
		}
		if (prospect.getDataInicio() != null && !dataSimples) {
			sql.append("AND E.DATAINTERESSE >= ? \n");
		}
		if (prospect.getDataFim() != null  && !dataSimples) {
			sql.append("AND E.DATAINTERESSE <= ? \n");
		}
		if (!UteisValidacao.emptyNumber(prospect.getCodigoSituacao()) && !prospect.getCodigoSituacao().equals(11)) {
			sql.append("AND E.SITUACAO = ?  \n");
		} else if (!UteisValidacao.emptyNumber(prospect.getCodigoSituacao()) && prospect.getCodigoSituacao().equals(11)){
			sql.append("AND E.SITUACAO IS NULL  \n");
		}
		if(apenasConversas && !apenasEventos){
			sql.append("AND CODIGOCONVERSA IS NOT NULL \n");
			sql.append("AND E.CODIGO IS NULL \n");
		}
		if(!apenasConversas && apenasEventos){
			sql.append("AND E.CODIGO IS NOT NULL \n");
		}
                if(codigoRecibo != null && codigoRecibo > 0){
                    sql.append(" AND E.CODIGO IN (SELECT distinct(eventointeresse) FROM  negociacaoeventocontrato negcon \n");
                    sql.append(" INNER JOIN negociacaoeventocontratoparcelas negconpa ON negconpa.contrato = negcon.codigo  \n");
                    sql.append(" INNER JOIN pagamentomovparcela pagmp ON pagmp.movparcela = negconpa.parcela AND pagmp.recibopagamento = ?) \n");
                }


	}

	/**
	 * Setar os parametros da consulta
	 * @param stm
	 * @throws SQLException
	 * @throws Exception
	 */
	private void filtrosValoresStatement(ProspectsTO prospect, PreparedStatementPersonalizado stm, 
                boolean todasSituacoes, boolean dataSimples, Integer codigoRecibo) throws SQLException, Exception {
		int i = 0;
		//A = possuir reserva, estar no status pre-reservado
		stm.setInt(i++, Situacao.PRE_RESERVADO.getCodigo());

		stm.setDate(i++, new java.sql.Date(Calendario.hoje().getTime()));
		//WHEN (CON.PROXIMO - ? < interval '5 days') AND (E.situacao = ?)
		stm.setDate(i++, new java.sql.Date(Calendario.hoje().getTime()));
		stm.setInt(i++, Situacao.PRE_RESERVADO.getCodigo());


		//E = contato do tipo emissão de orçamento sem data do proximo contato.
		stm.setInt(i++, Situacao.ORCADO.getCodigo());
        //F = contato do tipo cadastro inicial, pendente, interessado, não-reservado sem data do proximo contato.
		stm.setInt(i++, Situacao.CADASTRADO.getCodigo());
		stm.setInt(i++, Situacao.PENDENTE.getCodigo());
		stm.setInt(i++, Situacao.INTERESSADO.getCodigo());
		stm.setInt(i++, Situacao.NAO_RESERVADO.getCodigo());

        //G = contato do tipo agenda visita com data do proximo contato EXPIRADA e não prereserva
		stm.setInt(i++, TipoContato.AGENDAR_VISITA.getCodigo());
		stm.setDate(i++, new java.sql.Date(Calendario.hoje().getTime()));
		stm.setInt(i++, Situacao.PRE_RESERVADO.getCodigo());

		//H = evento marcado (status confirmado)
//		stm.setInt(i++, Situacao.CONFIRMADO.getCodigo());


		//I = evento cancelado ou encerrado
		stm.setInt(i++, Situacao.ENCERRADO.getCodigo());
		stm.setInt(i++, Situacao.CANCELADO.getCodigo());

		// verificar os filtros preenchidos
		if ((prospect.getCliente() != null) && !prospect.getCliente().equals("")) {
			stm.setString(i++, "%"+prospect.getCliente()+"%");
		}
		if ((prospect.getEvento() != null) && !prospect.getEvento().equals("")) {
			stm.setString(i++, "%"+prospect.getEvento()+"%");
		}
		if (prospect.getData() != null && dataSimples) {
			stm.setDate(i++, new java.sql.Date(prospect.getData().getTime()));
		}
		if ((prospect.getAmbiente().getCodigo() != null) && !prospect.getAmbiente().getCodigo().equals(0)) {
			stm.setInt(i++, prospect.getAmbiente().getCodigo());
		}
		if ((prospect.getAmbiente().getTipoAmbiente() != null) && !prospect.getAmbiente().getTipoAmbiente().equals(0)) {
			stm.setInt(i++, prospect.getAmbiente().getTipoAmbiente());
		}
		if (prospect.getDataInicio() != null && !dataSimples) {
			stm.setDate(i++, new java.sql.Date(prospect.getDataInicio().getTime()));
		}
		if (prospect.getDataFim() != null && !dataSimples) {
			stm.setDate(i++, new java.sql.Date(prospect.getDataFim().getTime()));
		}
		if (!UteisValidacao.emptyNumber(prospect.getCodigoSituacao()) && !prospect.getCodigoSituacao().equals(11)) {
			stm.setInt(i++, prospect.getCodigoSituacao());
		}
		if(!todasSituacoes){
			stm.setInt(i++, Situacao.CANCELADO.getCodigo());
			stm.setInt(i++, Situacao.ENCERRADO.getCodigo());
		}
                if(codigoRecibo != null && codigoRecibo > 0){
                    stm.setInt(i++, codigoRecibo);
                }
	}

	/**
	 * Responsável por realizar uma consulta de <code>EventoInteresseVO</code> através do valor do atributo Faz uso da operação
	 * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
	 *
	 * @param ambiente
	 * @param dataInicio
	 * @param dataFim
	 * @param reservas
	 * @throws Exception
	 */
	private void consultarReservasEventos(final AmbienteVO ambiente, final Date dataInicio, final Date dataFim,
			final Map<Date, List<ReservaTO>> reservas) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		// Montar query de consulta aos eventos
		StringBuilder sql = new StringBuilder();

		sql.append("SELECT AMB.tipoambiente AS codigotipoambiente, AMB.descricao AS descricaoambiente, NE.codigo,");
		sql.append("EI.nomeevento AS nome, NE.dataevento AS data, NE.horarioinicial as horaInitNeg, NE.horariofinal as horaFimNeg,");
		sql.append("NE.horariofinalexibicao, EI.situacao, EI.numeroconvidados, U.nome as usuario,");
		sql.append("Inter.nomecliente, inter.telefone, inter.telefonecomercial, inter.celular ");
		sql.append(",NEPEA.horarioinicial as horarioinicial, NEPEA.horariofinal as horariofinal, NEPEA.horariofinalexibicao as hrFimExibAmb \n");
		sql.append("FROM ambiente AMB \n");
		sql.append("INNER JOIN negociacaoeventoperfileventoambiente NEPEA ON NEPEA.ambiente = AMB.codigo \n");
		sql.append("INNER JOIN negociacaoevento NE ON NEPEA.negociacaoevento = NE.codigo \n");
		sql.append("INNER JOIN eventointeresse EI ON NE.eventointeresse = EI.codigo \n");
		sql.append("INNER JOIN usuario U ON NE.usuariocadastro = U.codigo \n");
		sql.append("INNER JOIN interessado INTER ON EI.interessado = INTER.codigo \n");
		sql.append("WHERE AMB.codigo = ? \n");
		sql.append("AND NE.dataevento >= ? AND NE.dataevento <= ? ");
		sql.append("AND NE.eventoreserva is true AND EI.situacao <> ?");

		// Disponibilizar parâmetros da consulta aos eventos
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, ambiente.getCodigo());
		dc.setDate(2, new java.sql.Date(Calendario.getDataComHoraZerada(dataInicio).getTime()));
		dc.setDate(3, new java.sql.Date(Uteis.getDateTime(dataFim, 23, 59, 59).getTime()));
		dc.setInt(4, Situacao.CANCELADO.getCodigo());

		// obter resultado da consulta a eventos
		ResultSet tabelaResultado = dc.executeQuery();

		this.montarReservasEventos(tabelaResultado, reservas);
	}

	// private void consultarReservasEventosPorSituacao(AmbienteVO ambiente, Date dataInicio, Date dataFim, Situacao situacao,
	// Map<Date, List<ReservaTO>> reservas) throws Exception {
	// // Montar query de consulta aos eventos
	// StringBuilder sql = new StringBuilder();
	//
	// sql
	// .append("SELECT AMB.tipoambiente AS codigotipoambiente, AMB.descricao AS descricaoambiente, NE.codigo, EI.nomeevento AS nome, NE.dataevento AS data, NE.horarioinicial, NE.horariofinal, EI.situacao \n");
	// sql.append("FROM ambiente AMB \n");
	// sql.append("INNER JOIN negociacaoeventoperfileventoambiente NEPEA ON NEPEA.ambiente = AMB.codigo \n");
	// sql.append("INNER JOIN negociacaoevento NE ON NEPEA.negociacaoevento = NE.codigo \n");
	// sql.append("INNER JOIN eventointeresse EI ON NE.eventointeresse = EI.codigo \n");
	// sql.append("WHERE AMB.codigo = ? \n");
	// sql.append("AND NE.dataevento >= ? AND NE.dataevento <= ? \n");
	// sql.append("AND EI.situacao = ?");
	//
	// // Disponibilizar parâmetros da consulta aos eventos
	// Declaracao dc = new Declaracao(sql.toString(), con);
	// dc.setInt(1, ambiente.getCodigo());
	// dc.setDate(2, new java.sql.Date(Uteis.getDataComHoraZerada(dataInicio).getTime()));
	// dc.setDate(3, new java.sql.Date(Uteis.getDateTime(dataFim, 23, 59, 59).getTime()));
	// dc.setInt(4, situacao.getCodigo());
	//
	// // obter resultado da consulta a eventos
	// ResultSet tabelaResultado = dc.executeQuery();
	//
	// montarReservasEventos(tabelaResultado, reservas);
	// }

	/**
	 * Monta um objeto <code>ReservaTO</code> a partir do <code>ResultSet</code>.
	 *
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>ReservaTO</code>.
	 * @throws Exception
	 */
	private ReservaTO montarDadosReserva(final ResultSet dadosSQL) throws Exception {
		// monta o objeto ReservaTO com os dados da consulta
		ReservaTO obj = new ReservaTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setNome(dadosSQL.getString("nome"));

		// Obter horário inicial marcado
		Time horario = dadosSQL.getTime("horarioinicial");

		obj.setHorarioInicial(dadosSQL.getDate("data"));
		// Atualizar data agendada com horário
		if (horario != null) {
			Calendar calendar = Calendario.getInstance();
			calendar.setTime(horario);
			obj.setHorarioInicial(Uteis.getDateTime(obj.getHorarioInicial(), calendar.get(Calendar.HOUR_OF_DAY), calendar
					.get(Calendar.MINUTE), calendar.get(Calendar.SECOND)));
		}


		// retorna o objeto
		return obj;
	}

	/**
	 * Monta um objeto <code>ReservaTO</code> a partir do <code>ResultSet</code> de uma consulta a eventos.
	 *
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>ReservaTO</code>.
	 * @throws Exception
	 */
	private ReservaTO montarDadosReservaEvento(final ResultSet dadosSQL) throws Exception {

		ReservaTO evento = this.montarDadosReserva(dadosSQL);
		evento.setResponsavel(dadosSQL.getString("usuario"));
		evento.setNrConvidados(dadosSQL.getInt("numeroconvidados"));
		evento.setNomeCliente(dadosSQL.getString("nomecliente"));
		evento.setTel(dadosSQL.getString("telefone"));
		evento.setTelCom(dadosSQL.getString("telefonecomercial"));
		evento.setTelCel(dadosSQL.getString("celular"));
		// Obter horário final marcado
		Time horario = dadosSQL.getTime("horariofinal");
		Time horarioExibicao = dadosSQL.getTime("horariofinalexibicao");

		evento.setHorarioFinal(dadosSQL.getDate("data"));

		// Atualizar data agendada com horário
		if (horario != null) {
			Calendar calendar = Calendario.getInstance();
			calendar.setTime(horario);
			evento.setHorarioFinal(Uteis.getDateTime(evento.getHorarioFinal(), calendar.get(Calendar.HOUR_OF_DAY), calendar
					.get(Calendar.MINUTE), calendar.get(Calendar.SECOND)));
		}
		// Atualizar data agendada com horário
		if (horario != null) {
			Calendar calendar = Calendario.getInstance();
			calendar.setTime(horarioExibicao);
			evento.setHorarioFinalExibicao(Uteis.getDateTime(evento.getHorarioFinal(), calendar.get(Calendar.HOUR_OF_DAY), calendar
					.get(Calendar.MINUTE), calendar.get(Calendar.SECOND)));
		}


		return evento;
	}

	/**
	 * Monta um objeto <code>ReservaTO</code> a partir do <code>ResultSet</code> de uma consulta a visitas.
	 *
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>ReservaTO</code>.
	 * @throws Exception
	 */
	private ReservaTO montarDadosReservaVisita(final ResultSet dadosSQL) throws Exception {
		ReservaTO visita = this.montarDadosReserva(dadosSQL);

		visita.setResponsavel(dadosSQL.getString("usuario"));
		visita.setHorarioFinal(dadosSQL.getDate("data"));
		visita.setRealizada(dadosSQL.getBoolean("realizada")); // AV.realizada
		int tipoVisita = dadosSQL.getInt("tipovisita");

		int duracaoEmMinutos;
		if (EnumTipoVisita.VISITA_EM_ABERTO.getCodigo().equals(tipoVisita)) {
			duracaoEmMinutos = dadosSQL.getInt("duracaomin");
		} else {
			duracaoEmMinutos = dadosSQL.getInt("tipovisitaduracaomin");
		}

		// Atualizar data agendada com horário
		if (visita.getHorarioFinal() != null) {
			visita.setHorarioFinal(Uteis.somarCampoData(visita.getHorarioInicial(), Calendar.MINUTE, duracaoEmMinutos));
			visita.setHorarioFinalExibicao(visita.getHorarioFinal());
		}

		return visita;
	}

	/**
	 * Monta um objeto <code>ReservaTO</code> a partir do <code>ResultSet</code> de uma consulta a visitas.
	 *
	 * @param tabelaResultado
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @throws Exception
	 */
	private void montarReservasEventos(final ResultSet tabelaResultado, final Map<Date, List<ReservaTO>> reservas) throws Exception {
		while (tabelaResultado.next()) {
			// Montar os dados da reserva
			ReservaTO reserva = this.montarDadosReservaEvento(tabelaResultado);

			// Caso
			if (reserva.getCodigo() != 0) {
				// Set a situação do evento
				reserva.setSituacao(Situacao.getSituacao(tabelaResultado.getInt("situacao")));
				// Set o tipo de reserva (Evento)
				reserva.setTipo(TipoReserva.EVENTO);
				//data
				reserva.setData(tabelaResultado.getDate("data"));
				// Adicionar reserva ao mapa que guarda as reservas por dia
				List<ReservaTO> listaEventos = reservas.get(Uteis.getDataComHoraZerada(reserva.getHorarioInicial()));
				if (listaEventos == null) {
					listaEventos = new ArrayList<ReservaTO>();
					reservas.put(Uteis.getDataComHoraZerada(reserva.getHorarioInicial()), listaEventos);
				}
				listaEventos.add(reserva);
			}
		}
	}

	/**
	 * Responsável por realizar uma consulta de <code>EventoInteresseVO</code> através do valor do atributo Faz uso da operação
	 * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
	 *
	 * @param ambiente ambiente para consultar
	 * @param dataInicio data de inicio da consulta
	 * @param dataFim data de fim da consulta
	 * @param reservas mapa das reservas para preencher
	 * @throws Exception
	 */
	private void consultarReservasVisitas(final AmbienteVO ambiente, final Date dataInicio, final Date dataFim,
			final Map<Date, List<ReservaTO>> reservas) throws Exception {
		// Montar query de consulta à visitas
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT AV.usuariocadastro, AV.codigo, INTR.nomecliente AS nome, AV.datavisita AS data, ");
		sql.append("AV.horariomarcado AS horarioinicial, AV.duracaomin, AV.tipovisita, AV.realizada, ");
		sql.append("TV.duracaomin AS tipovisitaduracaomin, U.nome as usuario \n");
		sql.append("FROM agendavisita AV \n");
		sql.append("INNER JOIN tipovisita TV ON AV.tipovisita = TV.codigo \n");
		sql.append("INNER JOIN interessado INTR ON AV.interessado = INTR.codigo \n");
		sql.append("INNER JOIN usuario U ON AV.usuariocadastro = U.codigo \n");
		sql.append("WHERE AV.ambiente = ? \n");
		sql.append("AND AV.datavisita >= ? \n");
		sql.append("AND AV.datavisita <= ?");

		// Disponibilizar parâmetros da consulta à visitas
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, ambiente.getCodigo());
		dc.setDate(2, new java.sql.Date(Uteis.getDataComHoraZerada(dataInicio).getTime()));
		dc.setDate(3, new java.sql.Date(Uteis.getDateTime(dataFim, 23, 59, 59).getTime()));

		// obter resultado da consulta à visitas
		ResultSet tabelaResultado = dc.executeQuery();

		while (tabelaResultado.next()) {
			// Montar os dados da reserva
			ReservaTO reserva = this.montarDadosReservaVisita(tabelaResultado);

			// Caso
			if (reserva.getCodigo() != 0) {
				// Set o tipo de reserva (Visita)
				reserva.setTipo(TipoReserva.VISITA);

				// Adicionar reserva ao mapa que guarda as reservas por dia
				List<ReservaTO> listaVisitas = reservas.get(Uteis.getDataComHoraZerada(reserva.getHorarioInicial()));
				if (listaVisitas == null) {
					listaVisitas = new ArrayList<ReservaTO>();
					reservas.put(Uteis.getDataComHoraZerada(reserva.getHorarioInicial()), listaVisitas);
				}
				listaVisitas.add(reserva);
			}
		}
	}

	/**
	 * Monta um objeto <code>Map</code> a partir do <code>ResultSet</code> de uma consulta a visitas.
	 *
     * @param ambiente ambiente para consultar
     * @param dataInicio data de inicio da consulta
     * @param dataFim data de fim da consulta
     * @param reservas mapa das reservas para preencher
	 * @return result
	 * @throws Exception
	 */
	private Map<String, Object> montarResultadoConsultaDispAmb(final AmbienteVO ambiente, final Date dataInicio, final Date dataFim,
			final Map<Date, List<ReservaTO>> reservas) throws Exception {
		// Criar mapa que conterá o resultado
		Map<String, Object> result = new HashMap<String, Object>();

		TipoAmbiente amb = new TipoAmbiente();

		// Tipo do ambiente que está sendo consultado
		TipoAmbienteTO tipoAmbiente = amb.obter(ambiente.getTipoAmbiente());
		// Guardar o nome do ambiente na primeira posição do mapa que contem o resultado
		result.put(Integer.toString(0), new Object[] { tipoAmbiente.getCodigo(), ambiente.getDescricao(), ambiente.getCodigo() });

		// Obter número de dias entre as datas inicial e final passadas como parâmetro
		int nrDias = (int) DateUtilities.nrDiasEntreDatas(dataInicio, dataFim);

		// Zerar o horário da data inicial
		Date data = Uteis.getDataComHoraZerada(dataInicio);

		// Percorrer os dias entre as datas inicial e final
		for (int i = 1; i <= (nrDias + 1); i++) {

			if (i > 1) {
				data = Uteis.getDataComHoraZerada(Uteis.somarDias(data, 1));
			}
			// criar um verificador para identificar se existe horarios disponiveis
			boolean haOutrosHorarios = false;
			// Adicionar um objeto DisponibilidadeTO que contenha as reservas para este dia na posição correspondente do mapa que guarda os
			// resultados
			List<ReservaTO> reservasDia = reservas.get(data);

			// Ordenar as reservas por dia
			if (reservasDia != null) {
				Collections.sort(reservasDia);
			}

			// Alocar novo objeto DisponibilidadeTO
			DisponibilidadeTO disponibilidadeTO = new DisponibilidadeTO();

			// Verificar se há mais que 2 reservas para o ambiente neste dia
			boolean haOutrasReservas = (reservasDia != null) && (reservasDia.size() > 2);

			Calendar calendar = Calendario.getInstance();
			calendar.setTime(tipoAmbiente.getHorarioInicial());

			// Obter horário inicial para reservas no tipo de ambiente
			Date horarioInicial = Uteis.getDateTime(data, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), calendar
					.get(Calendar.SECOND));

			calendar.setTime(tipoAmbiente.getHorarioFinal());

			// Obter horário final para reservas no tipo de ambiente
			Date horarioFinal = Uteis.getDateTime(data, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), calendar
					.get(Calendar.SECOND));

			calendar.setTime(tipoAmbiente.getHorarioFinalExibicao());

			// Obter horário final para reservas no tipo de ambiente
			Date horarioFinalExibicao = Uteis.getDateTime(data, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), calendar
					.get(Calendar.SECOND));

			// Criar variável que guarda o horário final da última reserva iterada
			Date horarioFinalReservaAnt = horarioInicial;

			// Caso não haja reservas para o dia:
			if ((reservasDia == null) || reservasDia.isEmpty()) {
				// O ambiente estará 'totalmente disponível'
				disponibilidadeTO.setStatus(EventoInteresse.TOTALMENTE_DISPONIVEL);

			} else {
				// Caso haja reservas para o dia:
				// calcular numero de eventos, excluindo visitas
				Integer nrEventos = reservasDia.size();
				for (ReservaTO reserva : reservasDia) {
					if (reserva.getTipo().equals(TipoReserva.VISITA))
						nrEventos--;

				}
				// Alterar o status do ambiente (caso haja vagas, o status será alterado para 'horários disponíveis', caso contrário, para
				// 'lotado')
				disponibilidadeTO.setStatus(tipoAmbiente.getQtdMaximaReservasDia() - nrEventos > 0 ? EventoInteresse.HORARIOS_DISPONIVEIS
						: EventoInteresse.SEM_HORARIOS_DISPONIVEIS);

				// Percorrer as reservas para o dia
				for (int j = 0; j < reservasDia.size(); j++) {
					int mins;

					// Caso seja a primeira reserva:
					if (j == 0) {
						// Label da primeira reserva do dia
						disponibilidadeTO.setEvento1(Formatador.formatarHorario(reservasDia.get(j).getHorarioInicial()) + " - "
								+ reservasDia.get(j).getNome());

						// Guardar minutos entre o horário inicial para reservas e o horário inicial da reserva
						mins = (int) Uteis.minutosEntreDatas(horarioInicial, reservasDia.get(j).getHorarioInicial());

					} else {
						// Caso não seja a primeira reserva:

						// Guardar minutos entre o horário final da reserva anterior e o horário inicial da reserva atual
						mins = (int) Uteis.minutosEntreDatas(horarioFinalReservaAnt, reservasDia.get(j).getHorarioInicial());

						// Caso seja a segunda reserva:
						if (j == 1) {
							// Label da segunda reserva do dia
							disponibilidadeTO.setEvento2(Formatador.formatarHorario(reservasDia.get(j).getHorarioInicial()) + " - "
									+ reservasDia.get(j).getNome());
						}
					}

					// Preencher os espaços vazios entre reservas com objetos ReservaTO vazios com a duração mínima (Do horário inicial ao
					// final e adicionando o tempo posterior aos eventos previsto no tipo do ambiente)
					while (mins >= (tipoAmbiente.getDuracaoMinimaHrs() * 60) + tipoAmbiente.getTempoAdicionalPosteriorMin()) {
						ReservaTO reserva = new ReservaTO();
						reserva.setHorarioInicial(horarioFinalReservaAnt);
						reserva.setHorarioFinal(Uteis.somarCampoData(horarioFinalReservaAnt, Calendar.HOUR_OF_DAY, tipoAmbiente
								.getDuracaoMinimaHrs()));

						disponibilidadeTO.getReservas().add(reserva);

						int qtdMin = (tipoAmbiente.getDuracaoMinimaHrs() * 60) + tipoAmbiente.getTempoAdicionalPosteriorMin();

						horarioFinalReservaAnt = Uteis.somarCampoData(horarioFinalReservaAnt, Calendar.MINUTE, qtdMin);

						mins -= qtdMin;
						haOutrosHorarios = true;
					}

					// Adicionar reserva à lista de reservas do ambiente para o dia
					disponibilidadeTO.getReservas().add(reservasDia.get(j));

					// Guardar o horário final da última reserva iterada
					horarioFinalReservaAnt = Uteis.somarCampoData(reservasDia.get(j).getHorarioFinal(), Calendar.MINUTE, tipoAmbiente
							.getTempoAdicionalPosteriorMin());
				}

			}

			// Preencher o espaço vazios entre a última reserva e o horário final para reservas do tipo de ambiente com objetos
			// ReservaTO vazios com a duração mínima (Do horário inicial ao final e adicionando o tempo posterior aos eventos previsto
			// no tipo do ambiente)
			while (Uteis.somarCampoData(horarioFinalReservaAnt, Calendar.MINUTE,
					((tipoAmbiente.getDuracaoMinimaHrs() * 60) + tipoAmbiente.getTempoAdicionalPosteriorMin())).before(horarioFinal)) {
				ReservaTO reserva = new ReservaTO();
				reserva.setHorarioInicial(horarioFinalReservaAnt);
				reserva.setHorarioFinal(Uteis.somarCampoData(horarioFinalReservaAnt, Calendar.HOUR_OF_DAY, tipoAmbiente
						.getDuracaoMinimaHrs()));

				disponibilidadeTO.getReservas().add(reserva);

				horarioFinalReservaAnt = Uteis.somarCampoData(reserva.getHorarioFinal(), Calendar.MINUTE, tipoAmbiente
						.getTempoAdicionalPosteriorMin());
				haOutrosHorarios = true;
			}
			//se ainda o horario da ultima reserva não preencher o horario do ambiente, adicionar mais um horário vazio com
			//o restante
			if (horarioFinalReservaAnt.before(horarioFinal)) {
				ReservaTO reserva = new ReservaTO();
				reserva.setHorarioInicial(horarioFinalReservaAnt);
				reserva.setHorarioFinal(horarioFinalExibicao);
				disponibilidadeTO.getReservas().add(reserva);
			}


			disponibilidadeTO.setLabel1(haOutrasReservas ? EventoInteresse.HA_OUTRAS_RESERVAS : EventoInteresse.NAO_HA_OUTRAS_RESERVAS);

			disponibilidadeTO
					.setLabel2(disponibilidadeTO.getStatus().equals(EventoInteresse.SEM_HORARIOS_DISPONIVEIS) ? EventoInteresse.SEM_DISPONIBILIDADE
							: EventoInteresse.DISPONIVEL);
			disponibilidadeTO.setLabel3(disponibilidadeTO.getStatus().equals(EventoInteresse.TOTALMENTE_DISPONIVEL) ? EventoInteresse.VAZIO
					: disponibilidadeTO.getStatus().equals(EventoInteresse.SEM_HORARIOS_DISPONIVEIS) ? EventoInteresse.LOTADO : null);

			if ((haOutrasReservas == false)
					&& (disponibilidadeTO.getStatus().equals(EventoInteresse.DISPONIVEL) == false && (disponibilidadeTO.getStatus()
							.equals(EventoInteresse.TOTALMENTE_DISPONIVEL)) == true)) {
				result.put(Integer.toString(i), disponibilidadeTO);
				disponibilidadeTO.setLabel1(haOutrasReservas ? EventoInteresse.HA_OUTRAS_RESERVAS : EventoInteresse.NADA);

				disponibilidadeTO
						.setLabel2(disponibilidadeTO.getStatus().equals(EventoInteresse.SEM_HORARIOS_DISPONIVEIS) ? EventoInteresse.SEM_DISPONIBILIDADE
								: EventoInteresse.DIA_VAZIO);
				disponibilidadeTO
						.setLabel3(disponibilidadeTO.getStatus().equals(EventoInteresse.TOTALMENTE_DISPONIVEL) ? EventoInteresse.NADA
								: disponibilidadeTO.getStatus().equals(EventoInteresse.SEM_HORARIOS_DISPONIVEIS) ? EventoInteresse.LOTADO
										: null);
				result.put(Integer.toString(i), disponibilidadeTO);
			} else {
				if (disponibilidadeTO.getStatus().equals(EventoInteresse.HORARIOS_DISPONIVEIS))
					// se houverem outras reservas mas não houverem outros horarios, o status é alterado para "sem horarios disposniveis"
					disponibilidadeTO.setStatus(!haOutrosHorarios ? EventoInteresse.SEM_HORARIOS_DISPONIVEIS
							: EventoInteresse.HORARIOS_DISPONIVEIS);
			}
			result.put(Integer.toString(i), disponibilidadeTO);

		}
		return result;
	}

	/**
	 * Consulta todos os eventos e visitas agendados para um ambiente em um certo período.
	 *
	 * @param ambiente
	 *            Ambiente.
	 * @param dataInicio
	 *            Data inícial do período em que os eventos e visitas serão consultados.
	 * @param dataFim
	 *            Data final do período em que os eventos e visitas serão consultados.
	 * @return Todos os eventos e visitas agendados para o ambiente no período informado.
	 * @throws Exception
	 * <AUTHOR>
	 */
	public Map<String, Object> consultarDispAmb(final AmbienteVO ambiente, final Date dataInicio, final Date dataFim) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		// Criar mapa que guardará todas as reservas por dia
		Map<Date, List<ReservaTO>> reservas = new HashMap<Date, List<ReservaTO>>();

		this.consultarReservasEventos(ambiente, dataInicio, dataFim, reservas);
		this.consultarReservasVisitas(ambiente, dataInicio, dataFim, reservas);
        this.consultarReservasAula(ambiente, dataInicio, dataFim, reservas);
		return this.montarResultadoConsultaDispAmb(ambiente, dataInicio, dataFim, reservas);
	}

    private void consultarReservasAula(final AmbienteVO ambiente, final Date dataInicio, final Date dataFim,
                                       final Map<Date, List<ReservaTO>> reservas) throws Exception {
        SimpleDateFormat formatoHora = new SimpleDateFormat("HH:mm");
        Map<String, HashMap<Date, List<ReservaTO>>> mapaDeDias = montarMapaDeDias(dataInicio, dataFim);

        String sql = "SELECT\n" +
                "  t.descricao      AS nome,\n" +
                "  ht.horainicial   AS horarioinicial,\n" +
                "  ht.horafinal     AS horariofinal,\n" +
                "  ht.diasemana     AS diaSemana,\n" +
                "  ht.codigo        AS codigo,\n" +
                "  ht.nrmaximoaluno AS maximo\n" +
                "FROM public.horarioturma ht\n" +
                "  INNER JOIN public.ambiente amb\n" +
                "    ON ht.ambiente = amb.codigo\n" +
                "  INNER JOIN public.turma t\n" +
                "    ON ht.turma = t.codigo\n" +
                "  INNER JOIN public.modalidade mod\n" +
                "    ON t.modalidade = mod.codigo\n" +
                "WHERE amb.codigo = ?\n" +
                "      AND mod.ativo = TRUE\n" +
                "      AND t.datafinalvigencia > '"+Uteis.getDataJDBC(Calendario.hoje())+" 23:59:59'\n" +
                "      OR (ht.codigo IN (SELECT\n" +
                "                          DISTINCT horarioturma\n" +
                "                        FROM public.matriculaalunohorarioturma maht\n" +
                "                          INNER JOIN public.horarioturma ht\n" +
                "                            ON ht.codigo = maht.horarioturma AND ht.ambiente = ?\n" +
                "                        WHERE datafim > '"+Uteis.getDataJDBC(Calendario.hoje())+" 23:59:59'))\n" +
                "ORDER BY amb.descricao, ht.diasemana";

        Declaracao dc = new Declaracao(sql, this.con);
        dc.setInt(1, ambiente.getCodigo());
        dc.setInt(2, ambiente.getCodigo());

        ResultSet tabelaResultado = dc.executeQuery();
        while (tabelaResultado.next()) {
            Time horarioInicial = new Time(formatoHora.parse(tabelaResultado.getString("horarioinicial")).getTime());
            Time horarioFinal = new Time(formatoHora.parse(tabelaResultado.getString("horariofinal")).getTime());

            String diaDaSemana = tabelaResultado.getString("diasemana");
            HashMap<Date, List<ReservaTO>> itemMapa = mapaDeDias.get(diaDaSemana);
            for (Map.Entry<Date, List<ReservaTO>> item : itemMapa.entrySet()) {
                ReservaTO reserva = new ReservaTO();
                reserva.setCodigo(tabelaResultado.getInt("codigo"));
                reserva.setNome(tabelaResultado.getString("nome"));
                reserva.setNrConvidados(tabelaResultado.getInt("maximo"));
                reserva.setTipo(TipoReserva.AULA);

                reserva.setHorarioInicial(item.getKey());
                reserva.setHorarioFinal(item.getKey());
                reserva.setHorarioFinalExibicao(item.getKey());

                Calendar horaInicial = Calendario.getInstance();
                horaInicial.setTime(horarioInicial);
                reserva.setHorarioInicial(Uteis.getDateTime(reserva.getHorarioInicial(),
                        horaInicial.get(Calendar.HOUR_OF_DAY),
                        horaInicial.get(Calendar.MINUTE),
                        horaInicial.get(Calendar.SECOND)));

                Calendar horaFinal = Calendario.getInstance();
                horaFinal.setTime(horarioFinal);
                reserva.setHorarioFinal(Uteis.getDateTime(reserva.getHorarioFinal(),
                        horaFinal.get(Calendar.HOUR_OF_DAY),
                        horaFinal.get(Calendar.MINUTE),
                        horaFinal.get(Calendar.SECOND)));
                reserva.setHorarioFinalExibicao(Uteis.getDateTime(reserva.getHorarioFinal(),
                        horaFinal.get(Calendar.HOUR_OF_DAY),
                        horaFinal.get(Calendar.MINUTE),
                        horaFinal.get(Calendar.SECOND)));

                reserva.setData(item.getKey());
                item.getValue().add(reserva);
            }
        }

        for (Map.Entry<String, HashMap<Date, List<ReservaTO>>> itemMapa : mapaDeDias.entrySet()) {
            for (Map.Entry<Date, List<ReservaTO>> itemItem : itemMapa.getValue().entrySet()) {
                for (ReservaTO reserva : itemItem.getValue()) {
                    List<ReservaTO> listaEventos = reservas.get(Calendario.getDataComHoraZerada(reserva.getHorarioInicial()));
                    if (listaEventos == null) {
                        listaEventos = new ArrayList<ReservaTO>();
                        reservas.put(Calendario.getDataComHoraZerada(reserva.getHorarioInicial()), listaEventos);
                    }
                        listaEventos.add(reserva);
                }
            }
        }
    }

    private Map<String, HashMap<Date, List<ReservaTO>>> montarMapaDeDias(Date dataIni, Date dataFim){
        Map<String, HashMap<Date, List<ReservaTO>>> mapaDaSemana = new HashMap<String, HashMap<Date, List<ReservaTO>>>();
        mapaDaSemana.put("SG", new HashMap<Date, List<ReservaTO>>());
        mapaDaSemana.put("TR", new HashMap<Date, List<ReservaTO>>());
        mapaDaSemana.put("QA", new HashMap<Date, List<ReservaTO>>());
        mapaDaSemana.put("QI", new HashMap<Date, List<ReservaTO>>());
        mapaDaSemana.put("SX", new HashMap<Date, List<ReservaTO>>());
        mapaDaSemana.put("SB", new HashMap<Date, List<ReservaTO>>());
        mapaDaSemana.put("DM", new HashMap<Date, List<ReservaTO>>());

        long qtdEntreDias = Uteis.nrDiasEntreDatasSemHoraZerada(dataIni, dataFim);
        for (int i = 0; i <= qtdEntreDias - 1; i++) {
            Date diaTemp = Uteis.somarDias(dataIni, i);
            Calendar dia = Calendario.getInstance(diaTemp);
            int diaDaSemana = dia.get(Calendar.DAY_OF_WEEK);
            switch (diaDaSemana){
                case Calendar.SUNDAY:
                    mapaDaSemana.get("DM").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
                case Calendar.MONDAY:
                    mapaDaSemana.get("SG").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
                case Calendar.TUESDAY:
                    mapaDaSemana.get("TR").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
                case Calendar.WEDNESDAY:
                    mapaDaSemana.get("QA").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
                case Calendar.THURSDAY:
                    mapaDaSemana.get("QI").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
                case Calendar.FRIDAY:
                    mapaDaSemana.get("SX").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
                case Calendar.SATURDAY:
                    mapaDaSemana.get("SB").put(diaTemp, new ArrayList<ReservaTO>());
                    break;
            }
        }

        return mapaDaSemana;
    }

	/**
	 * Consulta todos os evento de interesse
	 *
	 * @param parametro
	 * @return montar dados da consulta
	 */
	@SuppressWarnings("unchecked")
	public List<EventoInteresseVO> consultar(final EventoInteresseVO parametro) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT EI.codigo, EI.perfilinteresse, EI.queme, EI.interessado, EI.datainteresse, EI.ambienteinteresse, EI.observacao, EI.numeroconvidados, EI.nomeevento, EI.situacao, PE.descricao \n");
		sql.append("FROM eventointeresse EI \n");
		sql.append("INNER JOIN interessado INTER ON EI.interessado = INTER.codigo \n");
		sql.append("INNER JOIN ambiente AMB ON EI.ambienteinteresse = AMB.codigo \n");
		sql.append("LEFT JOIN negociacaoevento NE ON NE.eventointeresse = EI.codigo \n");
		sql.append("LEFT JOIN negociacaoperfilevento NPE ON NPE.negociacaoevento = NE.codigo \n");
		sql.append("LEFT JOIN perfilevento PE ON NPE.perfilevento = PE.codigo \n");

		sql.append("WHERE 1=1 \n");

		if ((parametro.getInteressado().getCodigo() != null) && !parametro.getInteressado().getCodigo().equals(0)) {
			sql.append("AND EI.interessado = ? \n");
		}
		if ((parametro.getInteressado().getNomeCliente() != null) && !parametro.getInteressado().getNomeCliente().equals("")) {
			sql.append("AND INTER.nomecliente iLIKE ? \n");
		}
		if ((parametro.getNomeEvento() != null) && !parametro.getNomeEvento().equals("")) {
			sql.append("AND EI.nomeevento iLIKE ? \n");
		}
		if (parametro.getDataInicio() != null) {
			sql.append("AND EI.datainteresse >= ? \n");
		}
		if (parametro.getDataFim() != null) {
			sql.append("AND EI.datainteresse <= ? \n");
		}
		if ((parametro.getAmbienteInteresse().getTipoAmbiente() != null) && !parametro.getAmbienteInteresse().getTipoAmbiente().equals(0)) {
			sql.append("AND AMB.tipoambiente = ? \n");
		}
		if ((parametro.getAmbienteInteresse().getCodigo() != null) && !parametro.getAmbienteInteresse().getCodigo().equals(0)) {
			sql.append("AND EI.ambienteinteresse = ? \n");
		}
		if (parametro.getSituacao() != null) {
			sql.append("AND EI.situacao = ? \n");
		}
		if ((parametro.getInteressado().getPessoa().getCodigo() != null) && !parametro.getInteressado().getPessoa().getCodigo().equals(0)) {
			sql.append("AND INTER.pessoa = ? \n");
		}

		if(parametro.getAmbienteInteresse().getTipoAmbiente() != null || parametro.getAmbienteInteresse().getCodigo() != null){
			if(parametro.getAmbienteInteresse().getCodigo()!= null && parametro.getAmbienteInteresse().getCodigo().equals(0)){
				sql.append(" ORDER BY EI.ambienteinteresse ASC ");
			}else{
				if(parametro.getAmbienteInteresse().getTipoAmbiente()!= null && parametro.getAmbienteInteresse().getTipoAmbiente().equals(0))
					sql.append(" ORDER BY AMB.tipoambiente ASC ");

			}
		}

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		int numParam = 0;
		if ((parametro.getInteressado().getCodigo() != null) && (parametro.getInteressado().getCodigo().intValue() != 0)) {
			dc.setInt(++numParam, parametro.getInteressado().getCodigo());
		}
		if ((parametro.getInteressado().getNomeCliente() != null) && !parametro.getInteressado().getNomeCliente().equals("")) {
			dc.setString(++numParam, parametro.getInteressado().getNomeCliente().toUpperCase() + "%");
		}
		if ((parametro.getNomeEvento() != null) && !parametro.getNomeEvento().equals("")) {
			dc.setString(++numParam, parametro.getNomeEvento().toUpperCase() + "%");
		}
		if (parametro.getDataInicio() != null) {
			dc.setDate(++numParam, new java.sql.Date(Uteis.getDataComHoraZerada(parametro.getDataInicio()).getTime()));
		}
		if (parametro.getDataFim() != null) {
			dc.setDate(++numParam, new java.sql.Date(Uteis.getDataComHoraZerada(parametro.getDataFim()).getTime()));
		}
		if ((parametro.getAmbienteInteresse().getTipoAmbiente() != null) && !parametro.getAmbienteInteresse().getTipoAmbiente().equals(0)) {
			dc.setInt(++numParam, parametro.getAmbienteInteresse().getTipoAmbiente());
		}
		if ((parametro.getAmbienteInteresse().getCodigo() != null) && !parametro.getAmbienteInteresse().getCodigo().equals(0)) {
			dc.setInt(++numParam, parametro.getAmbienteInteresse().getCodigo());
		}
		if (parametro.getSituacao() != null) {
			dc.setInt(++numParam, parametro.getSituacao().getCodigo());
		}
		if ((parametro.getInteressado().getPessoa().getCodigo() != null) && !parametro.getInteressado().getPessoa().getCodigo().equals(0)) {
			dc.setInt(++numParam, parametro.getInteressado().getPessoa().getCodigo());
		}

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsultaEvento(tabelaResultado));
	}

	/**
	 * Método que lista de eventos já cadastrados com o mesmo ambiente e mesmo dia
	 *
	 * @param ambiente
	 * @param dia
	 * @return Eventos cadastrados no dia e ambiente em questão
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List<ReservaTO> obterReservasDiaPorAmbiente(final AmbienteVO ambiente, final Date dia) throws Exception {
		// Criar mapa que guardará todas as reservas por dia
		Map<Date, List<ReservaTO>> reservas = new HashMap<Date, List<ReservaTO>>();
		this.consultarReservasEventos(ambiente, dia, dia, reservas);
		this.consultarReservasVisitas(ambiente, dia, dia, reservas);

        Date diaFim = Uteis.somarDias(dia, 1);
        this.consultarReservasAula(ambiente, dia, diaFim, reservas);
		return reservas.get(Uteis.getDataComHoraZerada(dia));
	}

	/**
	 * Faz a consulta totalizando alguns campos selecionados na pesquisa geral
	 * @param parametro
	 * @return
	 * @throws Exception
	 */
	public List<EventoTO> consultarTotalizacao(EventoInteresseVO parametro) throws Exception{
//		super.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT COUNT(EI.codigo) AS nreventos, SUM(NE.valortotal) AS valortotal, EI.situacao ");
		if(parametro.getAmbienteInteresse().getCodigo()!= null )
			sql.append(", AMB.descricao AS ambiente ");
		else
			if((parametro.getAmbienteInteresse().getTipoAmbiente()!= null))
				sql.append(", TA.descricao as tipo ");


		sql.append("FROM eventointeresse EI ");
		sql.append(" LEFT join negociacaoevento NE on EI.codigo = NE.eventointeresse");
		sql.append(" INNER JOIN ambiente AMB ON EI.ambienteinteresse = AMB.codigo");
		sql.append(" INNER JOIN tipoambiente TA ON TA.codigo = AMB.tipoambiente");

		sql.append(" WHERE 1=1 ");
		if (parametro.getDataInicio() != null) {
			sql.append("AND EI.datainteresse >= ? \n");
		}
		if (parametro.getDataFim() != null) {
			sql.append("AND EI.datainteresse <= ? \n");
		}
		if ((parametro.getAmbienteInteresse().getTipoAmbiente() != null) && !parametro.getAmbienteInteresse().getTipoAmbiente().equals(0)) {
			sql.append("AND AMB.tipoambiente = ? \n");
		}
		if ((parametro.getAmbienteInteresse().getCodigo() != null) && !parametro.getAmbienteInteresse().getCodigo().equals(0)) {
			sql.append("AND EI.ambienteinteresse = ? \n");
		}
		if (parametro.getSituacao() != null) {
			sql.append("AND EI.situacao = ? \n");
		}
		sql.append(" GROUP by EI.situacao");

		if(parametro.getAmbienteInteresse().getCodigo()!= null )
			sql.append(", AMB.descricao ");
		else
			if((parametro.getAmbienteInteresse().getTipoAmbiente()!= null))
				sql.append(", TA.descricao ");


		Declaracao dc = new Declaracao(sql.toString(), con);
		int numParam=0;

		if (parametro.getDataInicio() != null) {
			dc.setDate(++numParam, new java.sql.Date(Uteis.getDataComHoraZerada(parametro.getDataInicio()).getTime()));
		}
		if (parametro.getDataFim() != null) {
			dc.setDate(++numParam, new java.sql.Date(Uteis.getDataComHoraZerada(parametro.getDataFim()).getTime()));
		}
		if ((parametro.getAmbienteInteresse().getTipoAmbiente() != null) && !parametro.getAmbienteInteresse().getTipoAmbiente().equals(0)) {
			dc.setInt(++numParam, parametro.getAmbienteInteresse().getTipoAmbiente());
		}
		if ((parametro.getAmbienteInteresse().getCodigo() != null) && !parametro.getAmbienteInteresse().getCodigo().equals(0)) {
			dc.setInt(++numParam, parametro.getAmbienteInteresse().getCodigo());
		}
		if (parametro.getSituacao() != null) {
			dc.setInt(++numParam, parametro.getSituacao().getCodigo());
		}

		ResultSet tabelaResultado = dc.executeQuery();


		return montarDadosEventoTotal(parametro, tabelaResultado);
	}

	/**
	 * MOnta os dados da consulta por totalização
	 * @param filtro
	 * @param tabelaResultado
	 * @return lista de eventos
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List<EventoTO> montarDadosEventoTotal(EventoInteresseVO filtro, ResultSet tabelaResultado) throws Exception{
		List<EventoTO> result = new ArrayList<EventoTO>();
		while(tabelaResultado.next()){
			EventoTO evento = new EventoTO();
			evento.setValorFinal(tabelaResultado.getDouble("valortotal"));
			evento.setNrEventos(tabelaResultado.getInt("nreventos"));
			evento.setSituacao(Situacao.getSituacao(tabelaResultado.getInt("situacao")));
			//se o ambiente for diferente de nulo
			if((filtro.getAmbienteInteresse().getCodigo()!= null)){
				//caso o codigo seja diferente de 0, setar como nome do ambiente
				evento.setAmbiente(tabelaResultado.getString("ambiente"));
				evento.setNome("Eventos do Ambiente");
			}else{
				//caso o tipo seja diferente de 0, setar o tipo no ambiente
				if((filtro.getAmbienteInteresse().getTipoAmbiente()!= null)){
						evento.setNome("Eventos do Tipo de Ambiente: "+tabelaResultado.getString("tipo"));
				}else{
					evento.setNome("Eventos de Situação: "+evento.getSituacao().getDescricao());
			}
			}
			result.add(evento);

		}
		return result;
	}

	/**
	 * Responsavel por atualizar a data de pre reserva do evento
	 * @param preReserva data que será inserida
	 * @param codigoEvento codigo representando o evento
	 * @throws Exception se houverem erros
	 * <AUTHOR>
	 */
	public void atualizarDataPreReserva(Date preReserva, Integer codigoEvento) throws Exception{
		//criar sql
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE eventointeresse SET dataprereserva = ? WHERE codigo = ?");
		//criar o statement e setar parametros
		Declaracao dc = new Declaracao(sql.toString(), con);
		int i =0;
		dc.setDate(++i, new java.sql.Date(preReserva.getTime()));
		dc.setInt(++i, codigoEvento);
		//executar o comando
		dc.execute();
	}

	public List montarDadosConsultaEvento(final ResultSet tabelaResultado) throws Exception {
		final List vetResultado = new ArrayList();
		while (tabelaResultado.next()) {
			final Object obj = this.montarDadosEvento(tabelaResultado);
			vetResultado.add(obj);
		}
		return vetResultado;
	}

	/**
	 * @return
	 * @throws Exception
	 */
	public TagsTO consultarTags(Integer codigoEvento) throws Exception{
//		this.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT   e.codigo          , e.nomeevento   , i.nomecliente     , \n");
		sql.append("         pc.descricao as descparcela, fp.descricao   as formapagamento, p.rg, \n");
		sql.append("         p.cfp             , en.endereco    , en.bairro         , \n");
		sql.append("         t.numero as  tel  , t.tipotelefone , e.numeroconvidados, \n");
		sql.append("         ch.datacompesancao, ch.agencia     , ch.conta , ch.banco, \n");
		sql.append("         ch.numero        as cheque, pc.valorparcela, pc.datavencimento , \n");
		sql.append("         pc.dataregistro, en.enderecocorrespondencia, cid.nome as cidade \n");
		//tabela evento interesse
		sql.append("FROM     eventointeresse e \n");
		sql.append("         INNER JOIN interessado i \n");
		sql.append("         ON       e.interessado = i.codigo \n");
		//tabela interessado
		sql.append("         INNER JOIN negociacaoevento neg \n");
		sql.append("         ON       neg.eventointeresse = e.codigo \n");
		//tabela negociacaoeventocontrato
		sql.append("         LEFT JOIN negociacaoeventocontrato NEC \n");
		sql.append("         ON       nec.eventointeresse = e.codigo \n");
		sql.append("         LEFT JOIN pessoa p \n");
		//tabela pessoa
		sql.append("         ON       p.codigo = i.pessoa \n");
		sql.append("         LEFT JOIN endereco en \n");
		//tabela endereco
		sql.append("         ON       en.pessoa = p.codigo \n");
		sql.append("         LEFT JOIN cidade c \n");
		//tabela cidade
		sql.append("         ON       p.cidade = c.codigo \n");
		sql.append("         LEFT JOIN telefone t \n");
		//tabela telefone
		sql.append("         ON       t.pessoa = p.codigo \n");
		//tabela negociacaoeventocontratopagamento
		sql.append("         LEFT JOIN negociacaoeventocontratopagamento cpg \n");
		sql.append("         ON       cpg.contrato = nec.codigo \n");
		sql.append("         LEFT JOIN movpagamento pg \n");
		sql.append("         ON       pg.codigo = cpg.movpagamento \n");
		sql.append("         LEFT JOIN formapagamento fp \n");
		sql.append("         ON       fp.codigo = pg.formapagamento \n");
		//tabela negociacaoeventocontratoparcelas
		sql.append("         LEFT JOIN negociacaoeventocontratoparcelas cpc \n");
		sql.append("         ON       cpc.contrato = nec.codigo \n");
		sql.append("         LEFT JOIN movparcela pc \n");
		sql.append("         ON       pc.codigo = cpc.parcela \n");
		//tabela cheque
		sql.append("         LEFT JOIN cheque ch \n");
		sql.append("         ON       ch.movpagamento = pg.codigo \n");
		//tabela cidade
		sql.append("LEFT JOIN cidade cid on cid.codigo = p.cidade \n");
		sql.append("WHERE    e.codigo                 = ? \n");
		sql.append("GROUP BY e.codigo          , e.nomeevento   , i.nomecliente     , \n");
		sql.append("         fp.descricao      , pc.descricao   , p.rg              , \n");
		sql.append("         p.cfp             , en.endereco    , en.bairro         , \n");
		sql.append("         t.numero          , t.tipotelefone , e.numeroconvidados, \n");
		sql.append("         ch.datacompesancao, ch.agencia     , ch.conta , ch.banco, \n");
		sql.append("         ch.numero         , pc.valorparcela, pc.datavencimento , \n");
		sql.append("         pc.dataregistro, en.enderecocorrespondencia, cid.nome \n");
		sql.append("ORDER BY e.codigo");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(1, codigoEvento);
		ResultSet rs = dc.executeQuery();
		//montar as tags e retornar o objeto
		return montarTags(rs);
	}

	/**
	 * Responsavel por montar os dados da consulta das tags no objeto tagsto
	 * @param rs
	 * @return
	 * @throws Exception
	 */
	private TagsTO montarTags(ResultSet rs) throws Exception {
		TagsTO tag = new TagsTO();
		//listas de string que irão conter uma especie de chave para os atributos que serão agrupados em lista
		List<String> parcelas = new ArrayList<String>();
		List<String> cheques = new ArrayList<String>();
		List<String> enderecos = new ArrayList<String>();
		List<String> telefones = new ArrayList<String>();
		//percorrer o result set preenchendo as tags com os resultados obtidos
		while(rs.next()){
			tag.setNomeCliente(rs.getString("nomecliente"));
			tag.setCidade(rs.getString("cidade"));
			tag.setCpf(rs.getString("cfp"));
			tag.setFormaPagamento(rs.getString("formapagamento"));
			tag.setNomeEvento(rs.getString("nomeevento"));
			tag.setRg(rs.getString("rg"));
			tag.setQtdConvidados(rs.getString("numeroconvidados"));
			//verificar se a parcela já está inserida na lista de parcelas da tag e inserir se não for o caso
			if(rs.getString("descparcela")!=null && !parcelas.contains(rs.getString("descparcela"))){
				MovParcelaVO parcela = new MovParcelaVO();
				parcela.setDescricao(rs.getString("descparcela"));
				parcela.setValorParcela(rs.getDouble("valorparcela"));
				parcela.setDataRegistro(rs.getDate("dataregistro"));
				parcela.setDataVencimento(rs.getDate("datavencimento"));
				tag.getParcelas().add(parcela);
				//adcionar a lista de chaves
				parcelas.add(rs.getString("descparcela"));

			}
			//verificar se o telefone já está inserido na lista de telefones da tag e inserir se não for o caso
			if(rs.getString("tel")!=null && !telefones.contains(rs.getString("tel"))){
				TelefoneVO tel = new TelefoneVO();
				tel.setNumero(rs.getString("tel"));
				tel.setTipoTelefone(rs.getString("tipoTelefone"));
				tag.getTelefones().add(tel);
				//adcionar a lista de chaves
				telefones.add(rs.getString("tel"));
			}
			//verificar se o endereco já está inserido na lista de enderecos da tag e inserir se não for o caso
			if(rs.getString("endereco")!=null && !enderecos.contains(rs.getString("endereco"))){
				EnderecoVO end = new EnderecoVO();
				end.setBairro(rs.getString("bairro"));
				end.setEndereco(rs.getString("endereco"));
				tag.getEnderecos().add(end);
				//adcionar a lista de chaves
				enderecos.add(rs.getString("endereco"));
			}
			//verificar se o cheque já está inserido na lista de cheques da tag e inserir se não for o caso
			if(rs.getString("cheque")!=null && !cheques.contains(rs.getString("cheque"))){
				ChequeVO cheque = new ChequeVO();
				cheque.setBanco(this.obterBanco(Integer.parseInt(rs.getString("banco"))));
				cheque.setNumero(rs.getString("cheque"));
				cheque.setDataCompensacao(rs.getDate("datacompesancao"));
				cheque.setConta(rs.getString("conta"));
				cheque.setAgencia(rs.getString("agencia"));
				tag.getCheque().add(cheque);
				//adcionar a lista de chaves
				cheques.add(rs.getString("cheque"));
			}

		}
		return tag;
	}

	/**
	 * Método criado para obter o nome do banco do cheque usado para pagamento do evento
	 * consultado para preenchimento das tags.
	 * Foi necessário sua criação pelo fato que é impossivel fazer um INNER JOIN entre as tabelas
	 * cheque e banco na consulta sql, pois o campo banco da tabela cheque é do tipo varchar, mesmo
	 * sendo responsavel por relacionamento direto com o campo codigo da tabela cheque
	 * @param banco
	 * @return objeto bancoVO obtido na consulta
	 * @throws Exception
	 * <AUTHOR>
	 */
	private BancoVO obterBanco(Integer banco) throws Exception{
		String sql = "SELECT nome FROM banco WHERE codigo = ?";
		Declaracao dc = new Declaracao(sql, con);
		dc.setInt(1, banco);
		ResultSet rs = dc.executeQuery();
		BancoVO bancoVO = new BancoVO();
		if(rs.next()){
			bancoVO.setNome(rs.getString("nome"));
		}
		return bancoVO;

	}

	@Override
	public Integer obterInteressadoEvento(Integer evento) throws Exception {
		Integer codigo = null;
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT interessado FROM eventointeresse WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, evento);
		ResultSet rs = dc.executeQuery();
		if (rs.next()) {
			codigo = rs.getInt("interessado");
		}
		return codigo;
	}

}

