/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.negociacao;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoProdutoLocacaoTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 */
public interface NegEvPerfilEventoProdutoLocacaoInterfaceFacade extends SuperInterface {
	/**
	 * Responsável por realizar uma consulta de <code>NegEvPerfilEventoProdutoLocacao</code> através do valor do atributo
	 * <code>TipoProdutoLocacao tipo</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido. Faz uso da
	 * operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
	 * 
	 * @param codigoNegociacaoEvento
	 *            . identificador de negociação
	 * @return List Contendo vários objetos da classe <code>NegEvPerfilEventoProdutoLocacao</code> resultantes da consulta.
	 * @throws Execption.
	 */
	List<NegEvPerfilEventoProdutoLocacaoTO> consultarPorNegociacaoEvento(Integer codigoNegociacaoEvento, TipoProdutoLocacao tipo)
			throws Exception;

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe <code>NegEvPerfilEventoProdutoLocacao</code>.
	 * 
	 * @param negEvPerfilEventoProdutoLocacao
	 *            Objeto da classe <code>NegEvPerfilEventoAmbienteTO</code> que será gravado no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            identificador da negociação.
	 * @throws Exception
	 */
	void incluir(NegEvPerfilEventoProdutoLocacaoTO negEvPerfilEventoProdutoLocacao, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe <code>NegEvPerfilEventoAmbienteTO</code>.
	 * 
	 * @param negEvPerfilEventoAmbiente
	 *            Objeto da classe <code>NegEvPerfilEventoProdutoLocacaoTO</code> que será alterada no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            identificador da negociação.
	 * @throws Exception
	 */
	void alterar(NegEvPerfilEventoProdutoLocacaoTO negEvPerfilEventoProdutoLocacao, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por excluir no BD um objeto da classe <code>NegEvPerfilEventoProdutoLocacaoTO</code>.
	 * 
	 * @param negEvPerfilEventoProdutoLocacao
	 *            Objeto da classe <code>NegEvPerfilEventoProdutoLocacaoTO</code> que será removido no banco de dados.
	 * @throws Execption
	 */
	void excluir(NegEvPerfilEventoProdutoLocacaoTO negEvPerfilEventoProdutoLocacao) throws Exception;

	/**
	 * Operação responsável por excluir varios objeto da classe <code>NegEvPerfilEventoProdutoLocacaoTO</code> que tenham o mesmo código
	 * 
	 * @param codigos
	 *            lista de inteiros
	 * @throws Exception
	 */
	void excluir(List<Integer> codigos) throws Exception;

	/**
	 * Operação responsável por excluir uma negociação de evento no BD um objeto da classe <code>NegEvPerfilEventoProdutoLocacaoTO</code>.
	 * 
	 * @param codigoNegociacao
	 *            código do identificador.
	 * @throws Execption.
	 */
	void excluirPorNegociacaoEvento(Integer codigoNegociacao) throws Exception;

}
