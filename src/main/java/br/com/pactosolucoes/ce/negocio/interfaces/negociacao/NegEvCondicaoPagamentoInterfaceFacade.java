/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.negociacao;

import br.com.pactosolucoes.ce.comuns.to.NegEvCondicaoPagamentoTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 */
public interface NegEvCondicaoPagamentoInterfaceFacade extends SuperInterface {

	/**
	 * Operação responsável por consultar uma negociação de evento no banco de dados um objeto da classe <code>NegEvCondicaoPagamento</code>
	 * .
	 * 
	 * @param codigo
	 *            Objeto da classe <code>Integer</code> que será consultado no banco de dados.
	 * @throws Exception
	 * @return caso seja vazio retorna nulo se não retorna o resultado da consulta
	 */
	NegEvCondicaoPagamentoTO consultarPorNegociacaoEvento(Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe <code>NegEvCondicaoPagamento</code>.
	 * 
	 * @param negEvCondicaoPagamento
	 *            Objeto da classe <code>NegEvCondicaoPagamento</code> que será gravado no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            Identificador da negociação.
	 * @throws Exception.
	 */
	void incluir(NegEvCondicaoPagamentoTO negEvCondicaoPagamento, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por alterar no BD os dados de um objeto da classe <code>NegEvCondicaoPagamento</code>.
	 * 
	 * @param negEvCondicaoPagamento
	 *            Objeto da classe <code>NegEvCondicaoPagamento</code> que será alterada no banco de dados.
	 * @param codigoNegociacaoEvento
	 *            Identificador da negociação.
	 * @throws Execption.
	 */
	void alterar(NegEvCondicaoPagamentoTO negEvCondicaoPagamento, Integer codigoNegociacaoEvento) throws Exception;

	/**
	 * Operação responsável por excluir uma negociação de evento condição de pagamento no BD um objeto da classe
	 * <code>NegEvCondicaoPagamento</code>.
	 * 
	 * @param codigoNegEvCondicaoPagamento
	 *            Objeto da classe <code>Integet</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluir(Integer codigoNegEvCondicaoPagamento) throws Exception;

	/**
	 * Operação responsável por excluir uma negociação de evento no BD um objeto da classe <code>NegEvCondicaoPagamento</code>.
	 * 
	 * @param codigoNegociacao
	 *            Objeto da classe <code>Integer</code> que será removido no banco de dados.
	 * @throws Execption.
	 */
	void excluirPorNegociacaoEvento(Integer codigoNegociacao) throws Exception;

}
