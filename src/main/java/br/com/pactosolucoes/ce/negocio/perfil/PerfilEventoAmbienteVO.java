package br.com.pactosolucoes.ce.negocio.perfil;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.AmbienteVO;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;

/**
 * Reponsável por manter os dados da entidade PerfilEventoAmbiente. Classe do tipo VO - Value Object composta pelos atributos da entidade
 * com visibilidade protegida e os métodos de acesso a estes atributos. Classe utilizada para apresentar e manter em memória os dados desta
 * entidade.
 * 
 * @see SuperVO
 * <AUTHOR>
 */
public class PerfilEventoAmbienteVO extends SuperVO {

	@ChavePrimaria
	protected Integer codigo;
	@ChaveEstrangeira
	protected PerfilEventoVO perfilEvento;
	@ChaveEstrangeira
	protected AmbienteVO ambiente;
	protected Double valor;
	protected String observacao;
	protected Integer nrMaximoConvidados;
	protected DiaSemana diaSemana;

	/**
	 * Construtor padrão da classe <code>PerfilEventoVO</code>. Cria uma nova instância desta entidade já inicializando seus atributos.
	 */
	public PerfilEventoAmbienteVO() {
		super();
		this.inicializarDados();
	}

	/**
	 * Realiza a inicialização dos atributos do objeto.
	 */
	public void inicializarDados() {
		this.setCodigo(0);
		this.setPerfilEvento(new PerfilEventoVO());
		this.setAmbiente(new AmbienteVO());
		this.setValor(0.0);
		this.setObservacao("");
		this.setNrMaximoConvidados(0);
	}

	/**
	 * @return O campo codigo.
	 */
	@Override
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	@Override
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo perfilEvento.
	 */
	public PerfilEventoVO getPerfilEvento() {
		return this.perfilEvento;
	}

	/**
	 * @param perfilEvento
	 *            O novo valor de perfilEvento.
	 */
	public void setPerfilEvento(final PerfilEventoVO perfilEvento) {
		this.perfilEvento = perfilEvento;
	}

	/**
	 * @return O campo ambiente.
	 */
	public AmbienteVO getAmbiente() {
		return this.ambiente;
	}

	/**
	 * @param ambiente
	 *            O novo valor de ambiente.
	 */
	public void setAmbiente(final AmbienteVO ambiente) {
		this.ambiente = ambiente;
	}

	/**
	 * @return O campo valor.
	 */
	public Double getValor() {
		return this.valor;
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValor(final Double valor) {
		this.valor = valor;
	}

	/**
	 * @return O campo observacao.
	 */
	public String getObservacao() {
		return this.observacao;
	}

	/**
	 * @param observacao
	 *            O novo valor de observacao.
	 */
	public void setObservacao(final String observacao) {
		this.observacao = observacao;
	}

	/**
	 * @return O campo nrMaximoConvidados.
	 */
	public Integer getNrMaximoConvidados() {
		return this.nrMaximoConvidados;
	}

	/**
	 * @param nrMaximoConvidados
	 *            O novo valor de nrMaximoConvidados.
	 */
	public void setNrMaximoConvidados(final Integer nrMaximoConvidados) {
		this.nrMaximoConvidados = nrMaximoConvidados;
	}

	/**
	 * @return O campo diaSemana.
	 */
	public DiaSemana getDiaSemana() {
		return this.diaSemana;
	}

	/**
	 * @param diaSemana
	 *            O novo valor de diaSemana.
	 */
	public void setDiaSemana(final DiaSemana diaSemana) {
		this.diaSemana = diaSemana;
	}

}
