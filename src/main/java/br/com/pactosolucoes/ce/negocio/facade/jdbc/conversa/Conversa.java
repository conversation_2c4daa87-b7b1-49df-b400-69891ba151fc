package br.com.pactosolucoes.ce.negocio.facade.jdbc.conversa;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoContato;
import br.com.pactosolucoes.ce.comuns.to.ConversaTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.conversa.ConversaInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConversaTO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConversaTO</code>. Encapsula toda a interação
 * com o banco de dados.
 * 
 * @see ConversaTO
 * @see CEDao
 * @see ConversaInterfaceFacade
 */
public class Conversa extends CEDao implements ConversaInterfaceFacade {

	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public Conversa() throws Exception {
		super();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.ConversaInterfaceFacade#alterar(br.com.pactosolucoes.ce.comuns.to.ConversaTO)
	 */
	@Override
	public void alterar(final ConversaTO parametro) throws Exception {
//		super.alterarObj(this.getIdEntidade());
		// Cria a string sql
		final StringBuilder sql = new StringBuilder();
		sql.append("UPDATE conversa SET descricao = ?, interessado = ?, formacontato = ?, atendente = ?");

		if ((parametro.getCodigoEvento() != null) && !parametro.getCodigoEvento().equals(0)) {
			sql.append(", eventointeresse = ?");
		}
		if (parametro.getDataConversa() != null) {
			sql.append(", dataconversa = ?");
		}
		if (parametro.getDataProxConversa() != null) {
			sql.append(", dataproximocontato = ?");
		}

		sql.append("\nWHERE codigo = ?");

		// Prepara a conexão
		final Declaracao dc = new Declaracao(sql.toString(), this.con);

		int aux = 0;
		dc.setString(++aux, parametro.getDescricao());
		dc.setInt(++aux, parametro.getCodigoInteressado());
		dc.setInt(++aux, parametro.getForma());
		dc.setInt(++aux, parametro.getCodigoUsuarioResponsavel());

		if ((parametro.getCodigoEvento() != null) && !parametro.getCodigoEvento().equals(0)) {
			dc.setInt(++aux, parametro.getCodigoEvento());
		}
		if (parametro.getDataConversa() != null) {
			dc.setDate(++aux, new java.sql.Date(parametro.getDataConversa().getTime()));
		}
		if (parametro.getDataProxConversa() != null) {
			dc.setDate(++aux, new java.sql.Date(parametro.getDataProxConversa().getTime()));
		}

		dc.setInt(++aux, parametro.getCodigo());
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.ConversaInterfaceFacade#excluir(br.com.pactosolucoes.ce.comuns.to.ConversaTO)
	 */
	@Override
	public void excluir(final Integer interessado, final Integer evento) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		// Cria um string de sql
		final StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM conversa WHERE interessado = ? AND eventointeresse = ?");
		// Executa a consulta
		final Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, interessado);
		dc.setInt(2, evento);
		dc.execute();

	}
	public void excluirPorCodigo(final Integer codigo) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		// Cria um string de sql
		final StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM conversa WHERE codigo = ?");
		// Executa a consulta
		final Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigo);
		dc.execute();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.evento.ConversaInterfaceFacade#incluir(br.com.pactosolucoes.ce.comuns.to.ConversaTO)
	 */
	@Override
	public int incluir(final ConversaTO parametro) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		// se existe uma data de proxima conversa
		if (parametro.getDataProxConversa() != null)
			// desmarcar a conversa anterior desse interessado como ultimo contato
			this.desmarcarUltimoContato(parametro.getCodigoInteressado(), parametro.getCodigoEvento());
		// Incluir uma conversa, não alterando o atributo ultimocontato de nenhum registro
		incluirSemUltimoContato(parametro);
		// se existe uma data de proxima conversa
		if (parametro.getDataProxConversa() != null)
			// marcar como ultimo contato
			this.marcarUltimoContato();
		return this.obterValorChavePrimariaCodigo();
	}

	/**
	 * Inclui uma conversa, não alterando o atributo ultimocontato de nenhum registro 
	 * @param parametro
	 * @throws Exception
	 * @throws SQLException
	 */
	private void incluirSemUltimoContato(final ConversaTO parametro) throws Exception, SQLException {
//		super.incluirObj(this.getIdEntidade());
		// Cria a string sql
		final StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO conversa (descricao, interessado, formacontato, atendente");

		if ((parametro.getCodigoEvento() != null) && !parametro.getCodigoEvento().equals(0)) {
			sql.append(", eventointeresse");
		}
		if (parametro.getDataConversa() != null) {
			sql.append(", dataconversa");
		}
		if (parametro.getDataProxConversa() != null) {
			sql.append(", dataproximocontato");
		}

		sql.append(",tipocontato)\n");
		sql.append("VALUES (?,?,?,?");

		if ((parametro.getCodigoEvento() != null) && !parametro.getCodigoEvento().equals(0)) {
			sql.append(",?");
		}
		if (parametro.getDataConversa() != null) {
			sql.append(",?");
		}
		if (parametro.getDataProxConversa() != null) {
			sql.append(",?");
		}

		sql.append(",?)");

		// Prepara a conexão
		final Declaracao dc = new Declaracao(sql.toString(), this.con);

		int aux = 0;
		dc.setString(++aux, parametro.getDescricao());
		dc.setInt(++aux, parametro.getCodigoInteressado());
		dc.setInt(++aux, parametro.getForma());
		dc.setInt(++aux, parametro.getCodigoUsuarioResponsavel());

		if ((parametro.getCodigoEvento() != null) && !parametro.getCodigoEvento().equals(0)) {
			dc.setInt(++aux, parametro.getCodigoEvento());
		}
		if (parametro.getDataConversa() != null) {
			dc.setTimestamp(++aux, new java.sql.Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));
		}
		if (parametro.getDataProxConversa() != null) {
			dc.setDate(++aux, new java.sql.Date(parametro.getDataProxConversa().getTime()));
		}
		dc.setInt(++aux, parametro.getTipoContato().getCodigo());
		// Executa a consulta
		dc.execute();
		// Fecha conexão
	}

	/**
	 * Operação responsável por obter a chave primaria no banco de dados um objeto da classe <code>ConversaTO</code>.
	 * 
	 * @param codigo
	 *            - Objeto da classe <code>ConversaTO</code> que será gravado no banco de dados.
	 * @throws Exception
	 *             - Caso haja problemas de conexão, restrição de acesso ou validação de dados.
	 * @return um inteiro da tabela de resultado
	 */
	@Override
	public Integer obterValorChavePrimariaCodigo() throws Exception {
		this.inicializar();
		Declaracao dc = new Declaracao("SELECT MAX(codigo) FROM conversa", this.con);
		final ResultSet tabelaResultado = dc.executeQuery();
		tabelaResultado.next();
		return tabelaResultado.getInt(1);
	}


	/**
	 * Monta um objeto <code>ConversaTO</code> a partir do <code>ResultSet</code> de uma conversa.
	 * 
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>ConversaTO</code>.
	 * @throws Exception
	 */
	@Override
	public ConversaTO montarDados(final ResultSet dadosSQL) throws Exception {
		ConversaTO conversa = new ConversaTO();
		conversa.setCodigo(dadosSQL.getInt("codigo"));
		conversa.setDescricao(dadosSQL.getString("descricao"));
		conversa.setDataConversa(dadosSQL.getTimestamp("dataconversa"));
		conversa.setDataProxConversa(dadosSQL.getDate("dataproximocontato"));
		conversa.setTipoContato(TipoContato.getTipoVisita(dadosSQL.getInt("tipocontato")));
		conversa.setCodigoUsuarioResponsavel(dadosSQL.getInt("atendente"));
		conversa.setUsuario(new Usuario().consultarPorChavePrimaria(conversa.getCodigoUsuarioResponsavel(),
													Uteis.NIVELMONTARDADOS_DADOSBASICOS));
		return conversa;

	}

	/**
	 * Operação responsável por listar o histórico das conversas no banco de dados um objeto da classe <code>ConversaTO</code>.
	 * 
	 * @param codigoInteressado
	 *            - Objeto da classe <code>ConversaTO</code> que será gravado no banco de dados.
	 * @param codigoEventoInteresse
	 *            - Objeto da classe <code>ConversaTO</code> que será gravado no banco de dados.
	 * @throws Exception
	 *             - Caso haja problemas de conexão, restrição de acesso ou validação de dados.
	 * @return montar dados da consulta
	 */
	@SuppressWarnings("unchecked")
	public List<ConversaTO> listarHistorico(final Integer codigoInteressado, final Integer codigoEventoInteresse) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		boolean controlarAcesso = false;
		this.consultar(this.getIdEntidade(), controlarAcesso);
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT CON.* FROM conversa CON \n");
		sql.append("WHERE CON.interessado = ? \n");
		if (codigoEventoInteresse != null) {
			sql.append("AND CON.eventointeresse = ? \n");
		}
		sql.append("ORDER BY CON.dataconversa DESC");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setInt(++i, codigoInteressado);
		if (codigoEventoInteresse != null) {
			dc.setInt(++i, codigoEventoInteresse);
		}
		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}
	
	/**
	 * Responsavel por desmarcar o atributo ultimocontato do registro anterior ao que será inserido
	 * @param codigoInteressado o código do interessado deverá ser sempre válido
	 * @param codigoEvento o código do evento pode ser nulo ou não
	 * @throws Exception se houverem erros
	 * <AUTHOR>
	 */
	public void desmarcarUltimoContato(Integer codigoInteressado, Integer codigoEvento) throws Exception{
//		this.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		//atualizar ultimocontato para falso onde o código for igual ao maior código de conversa onde o interessado for igual
		//ao interessado passado como parametro e o evento for igual ao evento passado como parametro
		//ou o evento for nulo, para atualizar as conversas não vinculadas a evento desse interessado
		sql.append("UPDATE conversa SET ultimocontato = false WHERE codigo = (SELECT MAX(CODIGO) FROM conversa WHERE " );
		sql.append("interessado = ? ");
		//se o código do evento não for nulo, é o caso de conversa vinculada a evento
		if(codigoEvento != null && !codigoEvento.equals(0)){
			sql.append("AND eventointeresse = ?)");
		}else{
			sql.append("AND eventointeresse IS NULL)");
		}
		Declaracao dc = new Declaracao(sql.toString(), con);
		int i = 0;
		dc.setInt(++i, codigoInteressado);
		if(codigoEvento != null && !codigoEvento.equals(0)){
			dc.setInt(++i, codigoEvento);
		}
		dc.execute();
	}
	/**
	 * Responsavel por marcar o atributo ultimocontato do registro inserido com verdadeiro
	 * @throws Exception se houverem erros
	 * <AUTHOR>
	 */
	public void marcarUltimoContato() throws Exception{
//		this.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		//atualizar ultimocontato para true onde o código for igual ao maior código de conversa 
		//ou seja, o ultimo inserido
		sql.append("UPDATE conversa SET ultimocontato = TRUE WHERE codigo = (SELECT MAX(CODIGO) FROM conversa)" );
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.execute();
	}
	

}
