package br.com.pactosolucoes.ce.negocio.perfil;

import java.util.Date;

import negocio.comuns.arquitetura.SuperVO;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.ce.comuns.enumerador.FormaCalculo;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoOperacao;

/**
 * Reponsável por manter os dados da entidade PerfilEventoSazonalidade. Classe do tipo VO - Value Object composta pelos atributos da
 * entidade com visibilidade protegida e os métodos de acesso a estes atributos. Classe utilizada para apresentar e manter em memória os
 * dados desta entidade.
 * 
 * @see SuperVO
 * <AUTHOR>
 */
public class PerfilEventoSazonalidadeVO extends SuperVO {

	@ChavePrimaria
	protected Integer codigo;
	protected Dia<PERSON><PERSON>a dia<PERSON>emana;
	protected Date dataInicio;
	protected Date dataFim;
	protected TipoOperacao tipoOperacao;
	protected FormaCalculo formaCalculo;
	protected Double valor;
	@ChaveEstrangeira
	protected PerfilEventoAmbienteVO perfilEventoAmbiente;

	/**
	 * Construtor padrão da classe <code>PerfilEventoSazonalidadeVO</code>. Cria uma nova instância desta entidade já inicializando seus
	 * atributos.
	 */
	public PerfilEventoSazonalidadeVO() {
		super();
		this.inicializarDados();
	}

	/**
	 * Realiza a inicialização dos atributos do objeto.
	 */
	public void inicializarDados() {
		this.setCodigo(0);
		this.setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
		this.setDataFim(negocio.comuns.utilitarias.Calendario.hoje());
		this.setValor(0.0);
		this.setPerfilEventoAmbiente(new PerfilEventoAmbienteVO());
	}

	/**
	 * @return O campo codigo.
	 */
	@Override
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	@Override
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo diaSemana.
	 */
	public DiaSemana getDiaSemana() {
		return this.diaSemana;
	}

	/**
	 * @param diaSemana
	 *            O novo valor de diaSemana.
	 */
	public void setDiaSemana(final DiaSemana diaSemana) {
		this.diaSemana = diaSemana;
	}

	/**
	 * @return O campo dataInicio.
	 */
	public Date getDataInicio() {
		return this.dataInicio;
	}

	/**
	 * @param dataInicio
	 *            O novo valor de dataInicio.
	 */
	public void setDataInicio(final Date dataInicio) {
		this.dataInicio = dataInicio;
	}

	/**
	 * @return O campo dataFim.
	 */
	public Date getDataFim() {
		return this.dataFim;
	}

	/**
	 * @param dataFim
	 *            O novo valor de dataFim.
	 */
	public void setDataFim(final Date dataFim) {
		this.dataFim = dataFim;
	}

	/**
	 * @return O campo tipoOperacao.
	 */
	public TipoOperacao getTipoOperacao() {
		return this.tipoOperacao;
	}

	/**
	 * @param tipoOperacao
	 *            O novo valor de tipoOperacao.
	 */
	public void setTipoOperacao(final TipoOperacao tipoOperacao) {
		this.tipoOperacao = tipoOperacao;
	}

	/**
	 * @return O campo formaCalculo.
	 */
	public FormaCalculo getFormaCalculo() {
		return this.formaCalculo;
	}

	/**
	 * @param formaCalculo
	 *            O novo valor de formaCalculo.
	 */
	public void setFormaCalculo(final FormaCalculo formaCalculo) {
		this.formaCalculo = formaCalculo;
	}

	/**
	 * @return O campo valor.
	 */
	public Double getValor() {
		return this.valor;
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValor(final Double valor) {
		this.valor = valor;
	}

	/**
	 * @return O campo perfilEventoAmbiente.
	 */
	public PerfilEventoAmbienteVO getPerfilEventoAmbiente() {
		return this.perfilEventoAmbiente;
	}

	/**
	 * @param perfilEventoAmbiente
	 *            O novo valor de perfilEventoAmbiente.
	 */
	public void setPerfilEventoAmbiente(final PerfilEventoAmbienteVO perfilEventoAmbiente) {
		this.perfilEventoAmbiente = perfilEventoAmbiente;
	}

}
