package br.com.pactosolucoes.ce.negocio.perfil;

import java.util.Date;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;

/**
 * Reponsável por manter os dados da entidade ContratoPadraoCE. Classe do tipo VO - Value Object composta pelos atributos da entidade com
 * visibilidade protegida e os métodos de acesso a estes atributos. Classe utilizada para apresentar e manter em memória os dados desta
 * entidade.
 * 
 * @see SuperVO
 * <AUTHOR>
 */
public class ContratoPadraoCEVO extends SuperVO {

	@ChavePrimaria
	protected Integer codigo;
	protected String textoPadrao;
	protected Date dataCadastro;
	@ChaveEstrangeira
	protected UsuarioVO usuarioResponsavel;

	/**
	 * Construtor padrão da classe <code>ContratoPadraoCEVO</code>. Cria uma nova instância desta entidade já inicializando seus atributos.
	 */
	public ContratoPadraoCEVO() {
		super();
		this.inicializarDados();
	}

	/**
	 * Realiza a inicialização dos atributos do objeto.
	 */
	public void inicializarDados() {
		this.setCodigo(Integer.valueOf(0));
		this.setTextoPadrao("");
		this.setDataCadastro(negocio.comuns.utilitarias.Calendario.hoje());
		this.setUsuarioResponsavel(new UsuarioVO());
	}

	/**
	 * @return O campo codigo
	 */
	@Override
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	@Override
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo textoPadrao
	 */
	public String getTextoPadrao() {
		return this.textoPadrao;
	}

	/**
	 * @param textoPadrao
	 *            O novo valor de textoPadrao.
	 */
	public void setTextoPadrao(final String textoPadrao) {
		this.textoPadrao = textoPadrao;
	}

	/**
	 * @return O campo dataCadastro
	 */
	public Date getDataCadastro() {
		return this.dataCadastro;
	}

	/**
	 * @param dataCadastro
	 *            O novo valor de dataCadastro.
	 */
	public void setDataCadastro(final Date dataCadastro) {
		this.dataCadastro = dataCadastro;
	}

	/**
	 * @return O campo usuarioResponsavel
	 */
	public UsuarioVO getUsuarioResponsavel() {
		return this.usuarioResponsavel;
	}

	/**
	 * @param usuarioResponsavel
	 *            O novo valor de usuarioResponsavel.
	 */
	public void setUsuarioResponsavel(final UsuarioVO usuarioResponsavel) {
		this.usuarioResponsavel = usuarioResponsavel;
	}

}
