/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.turmas.servico.impl;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.agendatotal.json.*;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.controlecredito.json.ControleCreditoTreinoJSON;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.integracao.importacao.EmpresaJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.json.AlunoAulaAcessoJSON;
import br.com.pactosolucoes.turmas.json.TotalizadorOcupacaoJSON;
import br.com.pactosolucoes.turmas.servico.dto.*;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import br.com.pactosolucoes.turmas.utlisRemove.RemoveAlunoAula;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sun.xml.ws.util.StringUtils;
import controle.arquitetura.exceptions.ServiceException;
import controle.financeiro.MovParcelaControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.basico.enumerador.TipoToleranciaAulaEnum;
import negocio.comuns.contrato.*;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeHorarioVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FeriadoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.*;
import negocio.facade.jdbc.crm.Agenda;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.*;
import negocio.interfaces.contrato.*;
import negocio.interfaces.crm.AgendaInterfaceFacade;
import negocio.interfaces.crm.FecharMetaDetalhadoInterfaceFacade;
import negocio.interfaces.crm.FeriadoInterfaceFacade;
import negocio.interfaces.crm.HistoricoContatoInterfaceFacade;
import negocio.interfaces.financeiro.AulaAvulsaDiariaInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import negocio.interfaces.plano.*;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.RedeEmpresaVO;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.conviteaulaexperimental.ConviteAulaExperimentalService;
import servicos.impl.gestaoaula.GestaoAulaService;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.impl.turma.HistoricoProfessorTurmaServiceImpl;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import servicos.interfaces.ConviteAulaExperimentalServiceInterface;
import servicos.interfaces.turma.HistoricoProfessorTurmaService;
import servicos.oamd.RedeEmpresaService;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.pix.PixStatusEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static negocio.comuns.utilitarias.Uteis.firstLetterUpper;
import static negocio.comuns.utilitarias.Uteis.getData;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * <AUTHOR>
 */
public class TurmasServiceImpl implements TurmasServiceInterface, AutoCloseable {

    private Connection con;
    private TurmaInterfaceFacade turmaDao;
    private ItemCampanhaInterfaceFacade itenCampanhaDao;
    private EmpresaInterfaceFacade empresaDao;
    private SituacaoClienteSinteticoDW swDao;
    private HorarioTurmaInterfaceFacade horarioTurmaDao;
    private FecharMetaDetalhadoInterfaceFacade fecharMetaDetalhadoDao;
    private MatriculaAlunoHorarioTurmaInterfaceFacade matriculaHorarioDao;
    private AulaDesmarcadaInterfaceFacade aulaDesmarcadaDao;
    private ContratoInterfaceFacade contratoDao;
    private ContratoOperacaoInterfaceFacade contratoOperacaoDao;
    private ContratoModalidade contratoModalidadeDao;
    private ContratoModalidadeTurmaInterfaceFacade contratoModalidadeTurmaDao;
    private ReposicaoInterfaceFacade reposicaoDao;
    private PresencaInterfaceFacade presencaDao;
    private AcessoClienteInterfaceFacade acessoDao;
    private ControleCreditoTreinoInterfaceFacade controleCreditoDao;
    private ContratoDuracaoCreditoTreinoInterfaceFacade contratoDuracaoCreditoDao;
    private ConviteAulaExperimentalServiceInterface conviteAulaExperimentalDao;
    private ModalidadeInterfaceFacade modalidadeDao;
    private AgendaInterfaceFacade agendaDao;
    private UsuarioInterfaceFacade usuarioDao;
    private ColaboradorInterfaceFacade colaboradorDao;
    private DemandaHorarioTurmaInterfaceFacade demandaHorarioTurmaDao;
    private ClienteInterfaceFacade clienteDao;
    private PeriodoAcessoClienteInterfaceFacade periodoAcessoDao;
    private AulaConfirmadaInterfaceFacade aulaConfirmadaDao;
    private PerfilAcessoInterfaceFacade perfilAcessoDAO;
    private ControleAcesso controleAcessoDAO;
    private FeriadoInterfaceFacade feriadoDAO;
    private HistoricoProfessorTurmaService hpService;
    private MovParcelaInterfaceFacade movParcelaDao = null;
    protected ConfiguracaoSistemaVO configuracaoSistemaVO;
    private ConfiguracaoSistemaInterfaceFacade configuracaoDao = null;
    private ProdutoInterfaceFacade produtoDao = null;
    private AulaAvulsaDiariaInterfaceFacade aulaAvulsaDiariaDao = null;
    private TotalpassInterfaceFacade totalpassDao = null;
    private MovProdutoInterfaceFacade movProduto = null;
    private PessoaInterfaceFacade pessoaDao;
    private Log logDAO;
    private HistoricoContato historicoContatoDAO;
    private final KeySemaphore keySemaphore = new KeySemaphore();

    public TurmasServiceImpl(Connection con) throws Exception {
        this.con = con;
        this.logDAO = new Log(con);
    }

    public TurmasServiceImpl() {
    }

    @Override
    public List<TurmaVO> consultarTurmas(boolean somenteVigentes, boolean somenteAulaCheia, Integer modalidade, Integer empresa) throws Exception {
        return getTurmaDao().consultar(somenteVigentes, somenteAulaCheia, modalidade, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);
    }

    @Override
    public List<TurmaDTO> consultarTurmasIdade(boolean somenteVigentes, boolean somenteAulaCheia, Integer empresa, Integer idade, String modalidades, Integer codigoPessoa) throws Exception {
        List<TurmaVO> turmas = getTurmaDao().consultar(somenteVigentes, somenteAulaCheia, null, empresa, Uteis.NIVELMONTARDADOS_TODOS, false, idade, modalidades, codigoPessoa);
        List<TurmaDTO> turmasDTO = new ArrayList<>();
        for(TurmaVO turma : turmas){
            TurmaDTO dto = new TurmaDTO();
            dto.setCodigo(turma.getCodigo());
            dto.setDescricao(turma.getDescricao());
            dto.setIdadeMinima(turma.getIdadeMinima());
            dto.setIdadeMaxima(turma.getIdadeMaxima());
            dto.setModalidade(turma.getModalidade().getCodigo());
            dto.setModalidadeDesc(turma.getModalidade().getNome());

            dto.setDiasSemana(new ArrayList<>());
            DiaSemanaTurmaDTO domingo = new DiaSemanaTurmaDTO();
            domingo.setDiaSemanaDescricao("1");
            domingo.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(domingo);
            DiaSemanaTurmaDTO segunda = new DiaSemanaTurmaDTO();
            segunda.setDiaSemanaDescricao("2");
            segunda.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(segunda);
            DiaSemanaTurmaDTO terca = new DiaSemanaTurmaDTO();
            terca.setDiaSemanaDescricao("3");
            terca.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(terca);
            DiaSemanaTurmaDTO quarta = new DiaSemanaTurmaDTO();
            quarta.setDiaSemanaDescricao("4");
            quarta.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(quarta);
            DiaSemanaTurmaDTO quinta = new DiaSemanaTurmaDTO();
            quinta.setDiaSemanaDescricao("5");
            quinta.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(quinta);
            DiaSemanaTurmaDTO sexta = new DiaSemanaTurmaDTO();
            sexta.setDiaSemanaDescricao("6");
            sexta.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(sexta);
            DiaSemanaTurmaDTO sabado = new DiaSemanaTurmaDTO();
            sabado.setDiaSemanaDescricao("7");
            sabado.setHorarios(new ArrayList<>());
            dto.getDiasSemana().add(sabado);

            for(HorarioTurmaVO horario : turma.getHorarioTurmaVOs()){
                HorarioTurmaDTO horarioDTO = new HorarioTurmaDTO();
                horarioDTO.setCodigo(horario.getCodigo());
                horarioDTO.setTurma(turma.getCodigo());
                horarioDTO.setIdentificador(horario.getIdentificadorTurma());
                horarioDTO.setHoraInicial(horario.getHoraInicial());
                horarioDTO.setHoraFinal(horario.getHoraFinal());
                horarioDTO.setDiaSemana(horario.getDiaSemana());
                horarioDTO.setNrMaximoAluno(horario.getNrMaximoAluno());
                horarioDTO.setProfessor(horario.getProfessor().getPessoa().getNomeAbreviado());
                horarioDTO.setAmbiente(horario.getAmbiente().getDescricao());
                horarioDTO.setNivelTurma(horario.getNivelTurma().getDescricao());
                horarioDTO.setDiaSemanaNumero(horario.getDiaSemanaNumero());
                horarioDTO.setOcupacao(horario.getNrAlunoMatriculado() + horario.getNrAlunoMatriculadosFuturo());

                if(horario.getDiaSemanaNumero() == 1){
                    dto.getDiasSemana().get(0).getHorarios().add(horarioDTO);
                }else if(horario.getDiaSemanaNumero() == 2){
                    dto.getDiasSemana().get(1).getHorarios().add(horarioDTO);
                }else if(horario.getDiaSemanaNumero() == 3){
                    dto.getDiasSemana().get(2).getHorarios().add(horarioDTO);
                }else if(horario.getDiaSemanaNumero() == 4){
                    dto.getDiasSemana().get(3).getHorarios().add(horarioDTO);
                }else if(horario.getDiaSemanaNumero() == 5){
                    dto.getDiasSemana().get(4).getHorarios().add(horarioDTO);
                }else if(horario.getDiaSemanaNumero() == 6){
                    dto.getDiasSemana().get(5).getHorarios().add(horarioDTO);
                }else if(horario.getDiaSemanaNumero() == 7){
                    dto.getDiasSemana().get(6).getHorarios().add(horarioDTO);
                }
            }
            turmasDTO.add(dto);
        }
        return turmasDTO;
    }

    @Override
    public List<AgendamentoDesmarcadoJSON> consultarDesmarcados(Date inicio, Date fim, Integer empresa) throws Exception {
        return getAulaDesmarcadaDao().consultarAgendamentosDesmarcados(inicio, fim, empresa);
    }

    @Override
    public List<HorarioTurmaVO> consultarHorarioTurmas(Date inicio, Date fim, Integer turma, Integer modalidade, Integer empresa) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas) throws Exception {
        return consultarParaAgenda(inicio, fim, turma, modalidades, empresa, aulasColetivas, null, null, null, null, false);
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasAluno(Date inicio, Date fim, Integer matricula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select dia, identificador, horainicial, horafinal, m.nome as modalidade, colp.nome as professor from alunohorarioturma aht ");
        sql.append(" inner join horarioturma ht on ht.codigo = aht.horarioturma ");
        sql.append(" inner join turma t on t.codigo = ht.turma ");
        sql.append(" inner join cliente cli on cli.codigo = aht.cliente ");
        sql.append(" inner join modalidade m on m.codigo = t.modalidade ");
        sql.append(" inner join colaborador col on col.codigo = ht.professor ");
        sql.append(" inner join pessoa colp on col.pessoa = colp.codigo ");
        sql.append(" where cli.codigomatricula = ?  ");
        sql.append(" and dia between  ? and ? ");
        sql.append(" and (t.datafinalvigencia is null or t.datafinalvigencia >  aht.dia) ");
        sql.append(" order by aht.dia ");

        List<AgendaTotalJSON> lista;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, matricula);
            stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Uteis.getDataComUltimaHora(fim)));
            lista = new ArrayList<AgendaTotalJSON>();
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    Date data = rs.getDate("dia");
                    AgendaTotalJSON item = new AgendaTotalJSON();
                    item.setInicioVigencia(data);
                    item.setInicio(rs.getString("horainicial"));
                    item.setFim(rs.getString("horafinal"));
                    item.setInicio(Uteis.getData(data) + " " + item.getInicio());
                    item.setFim(Uteis.getData(data) + " " + item.getFim());
                    item.setTitulo(rs.getString("identificador"));
                    item.setTipo(rs.getString("modalidade"));
                    item.setResponsavel(rs.getString("professor"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    public HistoricoProfessorTurmaVO professorDaEpoca(Date dia, Integer codigoHorarioTurma,
                                                      Map<Integer, List<HistoricoProfessorTurmaVO>> mapaHistoricoProfessores) {

        //se a aula ainda vai acontecer, não faz sentido usar o historico
        if (Calendario.maiorOuIgual(dia, Calendario.hoje())
                || UteisValidacao.emptyList(mapaHistoricoProfessores.get(codigoHorarioTurma))) {
            return null;
        }
        List<HistoricoProfessorTurmaVO> historico = Ordenacao.ordenarLista(mapaHistoricoProfessores.get(codigoHorarioTurma), "inicio");
        Collections.reverse(historico);
        for (HistoricoProfessorTurmaVO hst : historico) {
            if ((hst.getFim() == null && Calendario.menorOuIgual(hst.getInicio(), dia))
                    || (Calendario.menorOuIgual(hst.getInicio(), dia) && Calendario.maiorOuIgual(hst.getFim(), dia))) {
                return hst;
            }
        }
        return null;

    }

    @Override
    public List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas,
                                                     Date agora, Map<String, Date> mapaMatriculas,
                                                     Map<String, List<Date>> mapaReposicoes, Integer matricula,
                                                     boolean isApp) throws Exception {

        return consultarParaAgenda(inicio, fim, turma, modalidades, empresa, aulasColetivas, agora, mapaMatriculas,
                mapaReposicoes, matricula, isApp, false);
    }

    @Override
    public List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas,
                                                     Date agora, Map<String, Date> mapaMatriculas,
                                                     Map<String, List<Date>> mapaReposicoes, Integer matricula,
                                                     boolean isApp, boolean isAgendaOnline) throws Exception {
        EmpresaVO empresaVO = null;
        ParamFeriadoAulaCheiaTO paramFeriadoAulaCheiaTO;
        List<ParamFeriadoAulaCheiaTO> listParamFeriadoAulaCheiaTO = new ArrayList<ParamFeriadoAulaCheiaTO>();
        boolean verificaEmpresaTrabalhaFeriado = true;
        List<Integer> todasEmpresas;
        Set<Date> diasFeriados = null;
        List<AgendaTotalJSON> lista = new ArrayList<AgendaTotalJSON>();
        Map<String, List<Date>> mapaDesmarcacoes = obterMapaDesmarcacoes(null, matricula, inicio, fim);
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, fim);
        if (UteisValidacao.notEmptyNumber(empresa)) {
            empresaVO = getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            diasFeriados = getFeriadoDAO().consultarPorPeriodoEmpresaAulaCheia(inicio, fim, empresaVO);
            if (null != diasFeriados && !diasFeriados.isEmpty()) {
                Iterator<Date> it = diasEntreDatas.iterator();
                if (!empresaVO.isPermMarcarAulaFeriado()) {
                    while (it.hasNext()) {
                        Date dataRemove = it.next();
                        for (Date diaFeriado : diasFeriados) {
                            if (Calendario.dataNoMesmoDiaMes(diaFeriado, dataRemove)) {
                                it.remove();
                                break;
                            }
                        }
                    }
                }
            }

        } else {
            todasEmpresas = getEmpresaTodas();
            for (int i = 0; i < todasEmpresas.size(); i++) {
                empresaVO = getEmpresaDao().consultarPorChavePrimaria(todasEmpresas.get(i), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (empresaVO.isPermMarcarAulaFeriado()) {
                    verificaEmpresaTrabalhaFeriado = false;
                    break;
                }
            }

            for (int i = 0; i < todasEmpresas.size(); i++) {
                empresaVO = getEmpresaDao().consultarPorChavePrimaria(todasEmpresas.get(i), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                diasFeriados = getFeriadoDAO().consultarPorPeriodoEmpresaAulaCheia(inicio, fim, empresaVO);
                if (diasFeriados.size() > 0 && empresaVO.isPermMarcarAulaFeriado()) {
                    PreencheParamFeriadoAulaCheiaTO(empresaVO, listParamFeriadoAulaCheiaTO);
                }
                Iterator<Date> it = diasEntreDatas.iterator();
                if (verificaEmpresaTrabalhaFeriado) {
                    while (it.hasNext()) {
                        Date dataRemove = it.next();
                        for (Date diaFeriado : diasFeriados) {
                            if (Calendario.dataNoMesmoDiaMes(diaFeriado, dataRemove)) {
                                it.remove();
                                break;
                            }
                        }
                    }
                }
            }
        }
        Map<Integer, List<HistoricoProfessorTurmaVO>> mapaHistoricoProfessores = getHpService().montarHistoricoProfessor(inicio);
        Map<String, List<AgendaTotalJSON>> turmasParaAgenda = getTurmaDao()
                .consultarHorariosTurmaParaAgenda(inicio, empresa, modalidades, aulasColetivas, null, matricula, isApp, isAgendaOnline);

        Map<Integer, ColaboradorVO> colaboradorResponsavel = new HashMap<Integer, ColaboradorVO>();
        Map<String, Integer> mapaOcupacaoPorId = new HashMap<String, Integer>();
        Map<String, Integer> mapaQuantidadeAlunosAulaNaoColetiva = new HashMap<String, Integer>();
        Map<String, Integer> mapaCountRemove = new HashMap<String, Integer>();

        for (Date d : diasEntreDatas) {
            String diaDaSemana = Uteis.obterDiaSemanaData(d);
            List<AgendaTotalJSON> turmas = turmasParaAgenda.get(diaDaSemana);
            if (turmas == null) {
                continue;
            }
            AGENDAMENTOS:
            for (AgendaTotalJSON a : turmas) {

                //CONSULTAR FOTO PARA APLICATIVO
                if (!UteisValidacao.emptyNumber(a.getCodigoResponsavel())) {
                    ColaboradorVO colaboradorVO = colaboradorResponsavel.get(a.getCodigoResponsavel());
                    if (colaboradorVO == null) {
                        colaboradorVO = getColaboradorDao().consultarPorChavePrimaria(a.getCodigoResponsavel(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        colaboradorResponsavel.put(a.getCodigoResponsavel(), colaboradorVO);
                    }
                    a.setFotoProfessor(colaboradorVO.getPessoa().getUrlFoto());
                }

                if (mapaMatriculas != null
                        && mapaMatriculas.get(a.getId()) != null
                        && Calendario.maiorOuIgual(mapaMatriculas.get(a.getId()), d)
                        && !(mapaDesmarcacoes.get(a.getId()) != null && mapaDesmarcacoes.get(a.getId()).contains(d))
                ) {
                    continue;
                }
                if (mapaReposicoes != null
                        && mapaReposicoes.get(a.getId()) != null) {
                    List<Date> dias = mapaReposicoes.get(a.getId());
                    for (Date dia : dias) {
                        if (Calendario.igual(dia, d)) {
                            continue AGENDAMENTOS;
                        }
                    }
                }
                Date agDia = Calendario.getDataComHora(d, a.getInicio());
                if (!UteisValidacao.emptyNumber(a.getToleranciaApresentarApp())) {
                    agDia = Uteis.somarCampoData(agDia, Calendar.MINUTE, a.getToleranciaApresentarApp());
                }
                if ((agora == null || agDia.after(agora))
                        && Calendario.maiorOuIgual(d, a.getInicioVigencia())
                        && Calendario.menorOuIgual(d, a.getFimVigencia())) {
                    AgendaTotalJSON agendaTotalJSON = new AgendaTotalJSON(d, a, a.getAulaCheia() ? null :
                            professorDaEpoca(d, Integer.parseInt(a.getId()), mapaHistoricoProfessores));

                    agendaTotalJSON.setValorProduto(a.getValorProduto());
                    agendaTotalJSON.setCodigoProduto(a.getCodigoProduto());
                    agendaTotalJSON.setDiaSemana(diaDaSemana);
                    if (agendaTotalJSON.getAulaCheia()) {
                        Integer ocupacao = mapaOcupacaoPorId.get(agendaTotalJSON.getId());
                        if (ocupacao == null) {
                            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                                    "SELECT COUNT(*) as ocupacao FROM alunohorarioturma WHERE horarioturma = " + agendaTotalJSON.getId()
                                            + " AND dia = '" + Uteis.getData(d, "yyyy-MM-dd") + "'", con)) {
                                ocupacao = rs.next() ? rs.getInt("ocupacao") : 0;
                            }
                            mapaOcupacaoPorId.put(agendaTotalJSON.getId(), ocupacao);
                        }
                        agendaTotalJSON.setNrVagasPreenchidas(ocupacao);
                    } else {
                        Integer quantidadeAlunosAulaNaoColetiva = mapaQuantidadeAlunosAulaNaoColetiva.get(agendaTotalJSON.getId());
                        if (quantidadeAlunosAulaNaoColetiva == null) {
                            quantidadeAlunosAulaNaoColetiva = turmaDao.contarAulasAlunosNaoColetivas(d, Integer.valueOf(agendaTotalJSON.getId()), a.getNrVagas());
                            mapaQuantidadeAlunosAulaNaoColetiva.put(agendaTotalJSON.getId(), quantidadeAlunosAulaNaoColetiva);
                        }

                        agendaTotalJSON.setNrVagasPreenchidas(quantidadeAlunosAulaNaoColetiva);
                    }
                    if (UteisValidacao.notEmptyNumber(empresa)) {
                        if (empresaVO == null) {
                            empresaVO = getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        }
                        agendaTotalJSON.setEmpresa(empresaVO.getCodigo());
                        if (isAulaFeriado(empresaVO, diasFeriados, d)) {
                            final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraAberturaFeriado());
                            final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraFechamentoFeriado());
                            final Long horaInicio = Calendario.pegaHoraEmMilisegundos(a.getInicio());
                            final Long horaFim = Calendario.pegaHoraEmMilisegundos(a.getFim());

                            if (isHorarioIntervaloNaoPermitido(diasFeriados, Calendario.getDataComHoraZerada(agDia), horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                continue;
                            }
                        }
                    } else if (listParamFeriadoAulaCheiaTO.size() > 0) {
                        for (ParamFeriadoAulaCheiaTO param : listParamFeriadoAulaCheiaTO) {
                            empresaVO = getEmpresaDao().consultarPorChavePrimaria(a.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                            if (!empresaVO.isPermMarcarAulaFeriado()) {
                                continue AGENDAMENTOS;
                            }
                            if (a.getEmpresa() == param.getEmpresa()) {
                                final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(param.getHorarioInicial());
                                final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(param.getHorarioFinal());
                                final long horaInicio = Calendario.pegaHoraEmMilisegundos(a.getInicio());
                                final long horaFim = Calendario.pegaHoraEmMilisegundos(a.getFim());

                                if (isHorarioIntervaloNaoPermitido(diasFeriados, inicio, horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                    continue AGENDAMENTOS;
                                }
                            } else {
                                continue;
                            }
                        }
                    }
                    lista.add(agendaTotalJSON);
                }
            }
        }
        return lista;
    }

    private void PreencheParamFeriadoAulaCheiaTO(EmpresaVO empresaVO, List<ParamFeriadoAulaCheiaTO> listParamFeriadoAulaCheiaTO) {
        ParamFeriadoAulaCheiaTO paramFeriadoAulaCheiaTO;
        paramFeriadoAulaCheiaTO = new ParamFeriadoAulaCheiaTO();
        paramFeriadoAulaCheiaTO.setEmpresa(empresaVO.getCodigo());
        paramFeriadoAulaCheiaTO.setHorarioInicial(empresaVO.getHoraAberturaFeriado());
        paramFeriadoAulaCheiaTO.setHorarioFinal(empresaVO.getHoraFechamentoFeriado());
        listParamFeriadoAulaCheiaTO.add(paramFeriadoAulaCheiaTO);
    }

    public List<Integer> getEmpresaTodas() throws Exception {
        List<Integer> todasEmpresas = new ArrayList<Integer>();
        try {
            List consultarTodas = getEmpresaDao().consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Iterator iterator = consultarTodas.iterator(); iterator.hasNext(); ) {
                EmpresaVO obj = (EmpresaVO) iterator.next();
                todasEmpresas.add(obj.getCodigo());
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            throw e;
        }
        return todasEmpresas;
    }

    private boolean isHorarioIntervaloNaoPermitido(final Set<Date> diasFeriados, final Date inicio, final Long horaAbertura, final Long horaFechamento,
                                                   final Long horaInicio, final Long horaFim) {
        try {
            boolean naoPermitido = false;

            for (Date diaFeriado : diasFeriados) {
                if (Calendario.dataNoMesmoDiaMes(diaFeriado, inicio) && (!(Calendario.getAno(diaFeriado) < Calendario.getAno(inicio)) ||
                        isFeriadoRecorrente(diaFeriado))) {
                    naoPermitido = true;
                    break;
                }
            }
            return (horaAbertura != null && horaFechamento != null && horaInicio != null && horaFim != null)
                    && naoPermitido && (horaInicio < horaAbertura || horaInicio >= horaFechamento || horaFim > horaFechamento);
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isFeriadoRecorrente(Date diaFeriado) {
        Feriado feriadoDAO;
        try {
            feriadoDAO = new Feriado(con);
            List<FeriadoVO> feriados = feriadoDAO.consultarFeriadoPorDiaAndMes(diaFeriado, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (FeriadoVO feriado : feriados) {
                if (feriado.getNaoRecorrente() != null && !feriado.getNaoRecorrente()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        } finally {
            feriadoDAO = null;
        }
    }

    private boolean isAulaFeriado(EmpresaVO empresaVO, Set<Date> diasFeriados, Date d) {
        if (empresaVO.isPermMarcarAulaFeriado() && diasFeriados != null) {
            for (Date diaFeriado : diasFeriados) {
                if (Calendario.dataNoMesmoDiaMes(diaFeriado, d)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public String inserirAlunoExperimental(String key, Integer idHorarioTurma,
                                           Date data, Integer matricula, Integer usuario, Integer produtoFreePass) throws Exception {
        Integer cliente;
        Integer empresa;
        Integer empresaAula;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, empresa, pessoa FROM cliente WHERE codigomatricula = " + matricula, con)) {
            if (rs.next()) {
                cliente = rs.getInt("codigo");
                empresa = rs.getInt("empresa");

            } else {
                throw new Exception("Aluno não encontrado!");
            }
        }

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT empresa FROM turma t" +
                " inner join horarioturma h on h.turma = t.codigo" +
                " WHERE h.codigo = " + idHorarioTurma, con)) {
            if (rs.next()) {
                empresaAula = rs.getInt("empresa");

            } else {
                throw new Exception("Aula não encontrada!");
            }
        }

        if (!UteisValidacao.emptyNumber(produtoFreePass)) {
            if (usuario != null && usuario > 0 && !(validarUsuarioTemPermissao(usuario, empresaAula, "PermissaoFreePass", " 2.51 - Lançar Free Pass ") &&
                    validarUsuarioTemPermissao(usuario, empresaAula, "AulaAvulsaDiaria", " 4.01 - Diária "))) {
                throw new Exception("Sem Permissão (2.51 - Lançar Free Pass ou 4.01 - Diária)");
            }
        }
        return inserirReposicaoAlunoExperimental(key, data, idHorarioTurma, cliente, usuario,
                OrigemSistemaEnum.AULA_CHEIA.getCodigo(), empresa, produtoFreePass);

    }

    private void proibirMarcarAulaPorParcelVencida(final String key, final Integer empresa, final Integer pessoa) throws Exception {
        if (DaoAuxiliar.retornarAcessoControle(key).existeParcelaVencida(empresa, pessoa) && existeParcelaVencida(empresa, pessoa)) {
            throw new Exception(String.format("Não foi possível marcar esta aula pelo motivo: \"%s\"",
                    SituacaoAcessoEnum.RV_BLOQALUNOPARCELAABERTA.getDescricao()));
        }
    }

    private void proibirMarcarAulaAntesPagamentoPrimeiraParcela(final Integer contrato) throws Exception {
        // Consulta para recuperar a primeira parcela (identificada por "PARCELA 1") do contrato
        final String sql =
                "SELECT mp.descricao, mp.situacao " +
                        "FROM movparcela mp " +
                        "WHERE mp.contrato = " + contrato + " " +
                        "AND mp.descricao = 'PARCELA 1' " +
                        "LIMIT 1";

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                // Verifica se a situação indica que o pagamento foi efetuado ("PG" para Pago)
                if (!"PG".equals(rs.getString("situacao")) && !"RG".equals(rs.getString("situacao")) && !"CA".equals(rs.getString("situacao"))) {
                    throw new Exception(
                            "Não foi possível marcar esta aula porque o pagamento da PRIMEIRA PARCELA ainda não foi confirmado. "
                    );
                }
            } else {
                // Se não encontrar o registro da primeira parcela, bloqueia o check-in
                throw new Exception(
                        "Não foi possível marcar esta aula porque o pagamento da PRIMEIRA PARCELA ainda não foi confirmado. "
                );
            }
        }
    }

    private boolean existePixConcluido(ResultSet rs) throws SQLException {
        final String concludedStatus = PixStatusEnum.CONCLUIDA.toString();
        while (rs.next()) {
            if (concludedStatus.equals(rs.getString("status"))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String marcarDesmarcarAlunoApp(String key, Integer idHorarioTurma, Date data, Integer matricula, boolean marcar,
                                          boolean bloquearParcelaVencida, boolean cfgProibirMarcarAulaAntesPagamentoPrimeiraParcela,
                                          Integer contrato) throws Exception {


        TurmaVO turmaVO = getTurmaDao().consultarPorHorarioTurma(idHorarioTurma, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (turmaVO != null && turmaVO.getAulaColetiva()) {
            return "Não é possível marcar aula coletiva pelo processo de marcação em turma!";
        }

        Integer cliente;
        Integer empresa;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, empresa, pessoa FROM cliente WHERE codigomatricula = " + matricula, con)) {
            Integer pessoa;
            if (rs.next()) {
                cliente = rs.getInt("codigo");
                empresa = rs.getInt("empresa");
                pessoa = rs.getInt("pessoa");
                if (contrato == null || contrato == 0) {
                    contrato = obterCodigoContratoAoMarcarDesmarcarApp(cliente, data, pessoa, idHorarioTurma);
                }
                if (marcar && bloquearParcelaVencida) {
                    proibirMarcarAulaPorParcelVencida(key, empresa, pessoa);
                }
                if (marcar && cfgProibirMarcarAulaAntesPagamentoPrimeiraParcela) {
                    proibirMarcarAulaAntesPagamentoPrimeiraParcela(contrato);
                }
            } else {
                return "Aluno não encontrado!";
            }
        }
        if (marcar) {

            if(!turmaInformadaPossuiCreditosParaMarcacao(contrato, turmaVO.getCodigo(), cliente, data, idHorarioTurma)) {
                throw new Exception("Você não possui aulas desmarcadas para essa modalidade em seu contrato. Favor, selecionar a modalidade correta.") ;
            }
            Integer horarioTurmaOrigem = idHorarioTurma;
            Date dataOrigem = data;
            Integer saldoRepor = 0; // aulas desmarcadas e créditos extras(gerados por ajuste manual de crédito). É quantidade total de aulas que podem gerar reposição
            try {
                String saldoAluno = consultarSaldoAluno(matricula, false, contrato);
                saldoRepor = Integer.valueOf(saldoAluno.split(";")[0]);
            }catch (Exception e){
            }
            Integer nrAulasExtras = nrAulasExtras(matricula, contrato); //apenas créditos extras(gerados por ajuste manual de crédito), não considera desmarcações

            boolean marcarAulaExtra = false;
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select vendacreditotreino from contrato where codigo = " + contrato, this.con)) {
                if(rs.next()){
                    if(saldoRepor <= 0 &&  rs.getBoolean("vendacreditotreino")){
                        throw new Exception("Saldo insuficiente");
                    }
                    marcarAulaExtra = rs.getBoolean("vendacreditotreino") && (nrAulasExtras > 0);
                }
            }

            if (!marcarAulaExtra) { // tem que pegar uma desmacação para gerar a reposição
                if(!turmaInformadaPossuiCreditosParaMarcacao(contrato, turmaVO.getCodigo(), cliente, data, idHorarioTurma)) {
                    throw new Exception("Você não possui aulas desmarcadas para essa modalidade em seu contrato. Favor, selecionar a modalidade correta.") ;
                }
                try (ResultSet rsModalidade = SuperFacadeJDBC.criarConsulta("select modalidade from horarioturma  "
                        + " inner join turma on turma.codigo = horarioturma.turma and horarioturma.codigo = " + idHorarioTurma, con)) {
                    if (rsModalidade.next()) {
                        try {
                            String aulaARepor = aulaARepor(cliente, contrato, rsModalidade.getInt("modalidade"));
                            if (!UteisValidacao.emptyString(aulaARepor)) {
                                String[] split = aulaARepor.split("_");
                                horarioTurmaOrigem = Integer.valueOf(split[0]);
                                dataOrigem = Uteis.getDate(split[1], "dd/MM/yy");
                            }
                        } catch (Exception e) {
                            horarioTurmaOrigem = null;
                            dataOrigem = null;
                        }
                    }
                }
            }
            if (horarioTurmaOrigem == null || dataOrigem == null) {
                throw new Exception("Este contrato não possui aulas a repor");
            }
            return reporAula(key, dataOrigem, data, horarioTurmaOrigem, idHorarioTurma, cliente, contrato, null,
                    OrigemSistemaEnum.APP_TREINO.getCodigo(), empresa, marcarAulaExtra, null, null, null);
        } else {
            return desmarcarAluno(key, idHorarioTurma, data, cliente, contrato, null, OrigemSistemaEnum.APP_TREINO.getCodigo(), empresa, false, null, null);
        }

    }

    private boolean turmaInformadaPossuiCreditosParaMarcacao(Integer contrato, Integer codTurma, Integer cliente, Date data, Integer idHorarioTurma) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select c.vendacreditotreino from contrato c join plano p on c.plano = p.codigo where c.codigo = " + contrato, con)) {
            if (rs.next()) {
                if (rs.getBoolean("vendacreditotreino")) {
                    return true;
                }
                Integer codModalidade = extrairModalidade(cliente, data, idHorarioTurma, false);
                try (ResultSet rs2 = SuperFacadeJDBC.criarConsulta("select * from auladesmarcada a join turma t on t.codigo = a.turma where contrato = " + contrato
                        + " and t.modalidade = " + codModalidade + " and datareposicao is null and permiteReporAulaDesmarcada = true", con)) {
                    if (rs2.next()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private Integer obterCodigoContratoAoMarcarDesmarcarApp(Integer codCliente, Date dia, Integer codPessoa, Integer idHorarioTurma) throws Exception {
        boolean contratoConcomitanteAlunoAtivo = contratoConcomitanteAlunoAtivo(codCliente, dia);
        if (contratoConcomitanteAlunoAtivo) {
            try (ResultSet rsContrato = SuperFacadeJDBC.criarConsulta(
                    "select ct.codigo as codigocontrato \n" +
                            "from contrato ct \n" +
                            "inner join contratomodalidade cm on cm.contrato = ct.codigo \n" +
                            "inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo \n" +
                            "inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma = cmt.codigo \n" +
                            "where cmht.horarioturma = " + idHorarioTurma + " \n" +
                            "and ct.pessoa = " + codPessoa + " \n" +
                            "and ct.situacao = 'AT'",
                    con)) {
                if (rsContrato.next()) {
                    return rsContrato.getInt("codigocontrato");
                }
            }
        }
        // se não for concomitante ou não localizar pelo horarioturma, consultar o contrato presente em situacaoclientesinteticodw
        return getContratoDao().consultarContratoVigentePorPessoa(codPessoa, false, true, Uteis.NIVELMONTARDADOS_MINIMOS).getCodigo();
    }

    public String desmarcarAlunoExperimental(String key, Integer idHorarioTurma, Date data, Integer codigoCliente, UsuarioVO usuarioVO, Integer origemSistema) {
        try {
            ReposicaoVO reposicaoVO = getReposicaoDao().consultaReposicaoClienteDiaTurma(codigoCliente, idHorarioTurma, data, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (reposicaoVO != null && !UteisValidacao.emptyNumber(reposicaoVO.getCodigo())) {
                reposicaoVO.setAulaExperimental(true);
                reposicaoVO.setUsuarioVO(usuarioVO); //usado para gerar responsável historico
                getReposicaoDao().excluirSemValidarPermissao(reposicaoVO);
                try {
                    ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select nomecliente, codigopessoa, matricula from situacaoclientesinteticodw where codigocliente = " + codigoCliente,
                            con);
                    AgendaTotalJSON json = new AgendaTotalJSON();
                    if (resultSet.next()) {
                        json.setMatricula(resultSet.getInt("matricula"));
                        json.setNomeAluno(resultSet.getString("nomecliente"));
                        logAluno(idHorarioTurma + "_" + Calendario.getDataAplicandoFormatacao(data, "dd/MM/yyyy"),
                                usuarioVO.getCodigo(),
                                resultSet.getInt("codigopessoa"),
                                json,
                                origemSistema, true, false, false, false, codigoCliente);
                    }
                } catch (Exception e) {
                    Uteis.logar(e, TurmasServiceImpl.class);
                }
            }

            return "removido";
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    @Override
    public String desmarcarAluno(String key, Integer idHorarioTurma, Date data, Integer codigoCliente,
                                 Integer codigoContrato, Integer usuario, Integer origemSistema, Integer empresa, boolean isExperimental, Integer operacaoColetiva, String justificativa) throws Exception {
        boolean ignorarAntecedencia = false;
        usuario = usuario == null ? 0 : usuario;
        UsuarioVO usuarioVO = new UsuarioVO();
        if (Objects.equals(OrigemSistemaEnum.getOrigemSistema(origemSistema), OrigemSistemaEnum.APP_TREINO)) {
            usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
        } else if (usuario > 0) {
            usuarioVO = getUsuarioDao().consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_LOGIN);
            ignorarAntecedencia = validarUsuarioTemPermissao(usuario, empresa, "DesmarcarAulaForaTolerancia", " 2.64 - Permitir desmarcar aula fora da tolerância da turma ");
        }
        if (UteisValidacao.emptyNumber(codigoContrato) || isExperimental) {
            return desmarcarAlunoExperimental(key, idHorarioTurma, data, codigoCliente, usuarioVO, origemSistema);
        }
        try {
            GestaoAulaService service = new GestaoAulaService(con, key);
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT turma FROM horarioturma WHERE codigo = " + idHorarioTurma, con)) {
                try (ResultSet rsPessoa = SuperFacadeJDBC.criarConsulta("SELECT pessoa, matricula FROM cliente WHERE codigo = " + codigoCliente, con)) {
                    if (rs.next() && rsPessoa.next()) {
                        Integer matricula = rsPessoa.getInt("matricula");
                        Integer pessoa = rsPessoa.getInt("pessoa");
                        if (Objects.equals(OrigemSistemaEnum.getOrigemSistema(origemSistema), OrigemSistemaEnum.APP_TREINO)) {
                            verificarSituacaoAluno(matricula, null);
                        }
                        AulaDesmarcadaVO aulaDesmarcada = new AulaDesmarcadaVO();
                        aulaDesmarcada.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(origemSistema));

                        aulaDesmarcada.getEmpresaVO().setCodigo(empresa);

                        EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        Integer toleranciaOcup = empresaVO.getToleranciaOcupacaoTurma();
                        aulaDesmarcada.setContratoVO(getContratoDao().consultarContratoVigentePorPessoaEDataMaisVigencia
                                (pessoa, data, false, toleranciaOcup, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                        // Pode haver mais de um contrato vigente, será setado o contrato vigente com o codigo que veio do Treino.
                        List<ContratoVO> listaContratos = getContratoDao().consultarContratosVigerntePorDataPessoa
                                (pessoa, data, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        for (ContratoVO contrato : listaContratos) {
                            if (contrato.getCodigo().equals(codigoContrato)) {
                                aulaDesmarcada.setContratoVO(contrato);
                                break;
                            }
                        }

                        aulaDesmarcada.setDataLancamento(Calendario.hoje());
                        aulaDesmarcada.setDataOrigem(data);
                        aulaDesmarcada.getClienteVO().setCodigo(codigoCliente);
                        aulaDesmarcada.getClienteVO().getPessoa().setCodigo(rsPessoa.getInt("pessoa"));
                        HorarioTurmaVO hTurma = getHorarioDao().consultarPorCodigo(idHorarioTurma, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        aulaDesmarcada.setHorarioTurmaVO(hTurma);
                        TurmaVO turma = getTurmaDao().consultarPorChavePrimaria(rs.getInt("turma"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        aulaDesmarcada.setTurmaVO(turma);
                        aulaDesmarcada.setUsuarioVO(usuarioVO);
                        if (justificativa != null) {
                            aulaDesmarcada.setJustificativa(justificativa);
                        }
                        if (operacaoColetiva != null) {
                            OperacaoColetivaVO operacaoColetivaVO = new OperacaoColetiva(con).consultarPorChavePrimaria(operacaoColetiva, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            aulaDesmarcada.setOperacaoColetiva(operacaoColetivaVO);
                        }

                        ReposicaoVO reposicao = getReposicaoDao().consultarReposicaoJaLancada(aulaDesmarcada.getClienteVO().getCodigo(),
                                aulaDesmarcada.getHorarioTurmaVO().getCodigo(),
                                aulaDesmarcada.getTurmaVO().getCodigo(), null, aulaDesmarcada.getDataOrigem());
                        if (reposicao == null || UteisValidacao.emptyNumber(reposicao.getCodigo())) {
                            if (aulaDesmarcada.getContratoVO() != null && UteisValidacao.emptyNumber(aulaDesmarcada.getContratoVO().getCodigo())) {
                                try (ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("SELECT datafim FROM matriculaalunohorarioturma WHERE horarioturma = " + idHorarioTurma + " AND contrato = " + codigoContrato, con)) {
                                    if (rsContrato.next()) {
                                        Date datafim = rsContrato.getDate("datafim");
                                        if (data.compareTo(datafim) <= 0) {
                                            aulaDesmarcada.getContratoVO().setCodigo(codigoContrato);
                                        }
                                    }
                                }
                            }
                            service.desmarcarAula(aulaDesmarcada, ignorarAntecedencia);
                        } else {// É reposição:
                            reposicao.setTurmaDestino(turma);
                            reposicao.setHorarioTurma(hTurma);
                            reposicao.setUsuario(aulaDesmarcada.getUsuarioVO());
                            reposicao.setCliente(aulaDesmarcada.getClienteVO());
                            service.desmarcarReposicao(reposicao, ignorarAntecedencia);
                        }
                        if (Objects.equals(OrigemSistemaEnum.getOrigemSistema(origemSistema), OrigemSistemaEnum.APP_TREINO)) {
                            return consultarSaldoAluno(matricula, false, codigoContrato);
                        } else {
                            return "Aula desmarcada com sucesso!";
                        }
                    } else {
                        throw new Exception("Turma não encontrada.");
                    }
                }
            }
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    public boolean alunoAtivo(Integer cliente, String situacaoContrato) {
        try {
            if (situacaoContrato != null && (situacaoContrato.equals("NO") || situacaoContrato.equals("AV"))) {
                return true;
            }

            ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT situacao from cliente where codigo = " + cliente, con);
            if (resultSet.next()) {
                return resultSet.getString("situacao").equals("AT");
            }
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return false;
    }

    private void validarVisualizarProdutoGymPass(ParamAlunoAulaCheiaTO param) throws Exception {
        ResultSet rs = turmaDao.consultarToleranciaTurma(param.getCodigoHorarioTurma());
        if (rs.next()) {
            boolean visualizaGymPass = rs.getBoolean("visualizarprodutosgympass");
            if (!visualizaGymPass && !retornaValidaAlunoGymPass(param.getCodigoCliente())) {
                throw new Exception("Esta aula não está disponível para os alunos Gympass.");
            }
        }
    }

    private void validarVisualizarProdutoTotalPass(ParamAlunoAulaCheiaTO param) throws Exception {
        ResultSet rs = turmaDao.consultarToleranciaTurma(param.getCodigoHorarioTurma());
        if (rs.next()) {
            boolean visualizaGymPass = rs.getBoolean("visualizarprodutostotalpass");
            if (!visualizaGymPass && retornaValidaAlunoTotalPass(param.getCodigoCliente())) {
                throw new Exception("Esta aula não está disponível para os alunos Totalpass.");
            }
        }
    }

    private void validaLimiteDeAulasTotalPass(Integer codigo_cliente, Integer empresa) throws Exception {
        if (limiteDeAulasPorDiaTotalpassAtingido(codigo_cliente, empresa) && getClienteDao().consultarClienteUltimoAcessoTotalPass(codigo_cliente)) {
            throw new Exception("Limite de aulas por dia atingido para Total Pass!");
        }

    }

    private void validarLimiteAgregadosAtingidoPorAula(Integer codigoHorarioTurma, Date diaAula) throws Exception {
        // validar se a aula já atingiu o limite de vagas para clientes agregados (gympass, totalpass)
        // foi utilizada a mesma lógica do detalhamento da aula do consultarAgendadosAulaColetiva(...) do AgendaModoBDServiceImpl presente no projeto do treino

        HorarioTurmaVO horarioTurmaVO = getHorarioDao().consultarPorCodigo(codigoHorarioTurma, Uteis.NIVELMONTARDADOS_MINIMOS);
        // se o valor for zero não precisa validar
        if (horarioTurmaVO.getLimiteVagasAgregados().equals(0)) {
            return;
        }
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        try {
            if (horarioTurmaVO.getLimiteVagasAgregados() != null && horarioTurmaVO.getLimiteVagasAgregados() > 0) {
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT c.codigo as cliente,p.codigo as pessoa, aht.bookingid, pe.tipoTotalPass as totalPass, s.situacao, aht.dia \n");
                sql.append("FROM alunohorarioturma aht \n");
                sql.append("INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma \n");
                sql.append("INNER JOIN turma t ON t.codigo = ht.turma \n");
                sql.append("LEFT JOIN cliente c ON c.codigo = aht.cliente \n");
                sql.append("LEFT JOIN pessoa p ON p.codigo = c.pessoa \n");
                sql.append("LEFT JOIN situacaoclientesinteticodw s on s.codigocliente = c.codigo \n");
                sql.append("LEFT JOIN periodoacessocliente pe on pe.codigo = (SELECT max(codigo) FROM periodoacessocliente WHERE pessoa = p.codigo AND tipoTotalPass IS TRUE)  \n");
                sql.append("WHERE aht.dia BETWEEN '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(diaAula))).append("' ");
                sql.append("AND '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(diaAula, "23:59:59"))).append("' \n");
                sql.append("AND ht.codigo = ").append(codigoHorarioTurma);

                try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                    while (rs.next()) {
                        AgendadoJSON agendado = new AgendadoJSON();
                        agendado.setTotalPass(rs.getBoolean("totalPass"));
                        String bookingid = rs.getString("bookingid");

                        try {
                            if (rs.getString("situacao") != null && !rs.getString("situacao").equals("AT") && UteisValidacao.emptyString(bookingid)) {
                                try (ResultSet rsCheckinGympass = SuperFacadeJDBC.criarConsulta("SELECT EXISTS ( \n" +
                                        "   SELECT ic.token \n" +
                                        "   FROM infocheckin ic \n" +
                                        "   INNER JOIN periodoacessocliente pac ON pac.codigo = ic.periodoacesso \n" +
                                        "   WHERE ic.cliente = " + rs.getInt("cliente") + " \n" +
                                        "   AND '" + rs.getDate("dia") + "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso  \n" +
                                        ") as temCheckin", con)) {
                                    if (rsCheckinGympass.next()) {
                                        agendado.setGymPass(rsCheckinGympass.getBoolean("temCheckin"));
                                    }
                                }

                                // valida caso quando checkin não é feito automaticamente via chamada gympass webhook
                                if (!agendado.isGymPass()) {
                                    try (ResultSet rsCheckinManualGympass = SuperFacadeJDBC.criarConsulta("SELECT EXISTS (" +
                                            "   SELECT * FROM periodoacessocliente pac  \n" +
                                            "   WHERE pac.pessoa = " + rs.getInt("pessoa") + " \n" +
                                            "   AND pac.tokengympass IS NOT NULL AND pac.tokengympass <> ''  \n" +
                                            "   AND '" + rs.getDate("dia") + "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso  \n" +
                                            ") as temCheckin", con)) {
                                        if (rsCheckinManualGympass.next()) {
                                            agendado.setGymPass(rsCheckinManualGympass.getBoolean("temCheckin"));
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            Uteis.logarDebug("Erro ao montar dados relacionados a alunos gympass agendados: " + e.getMessage());
                        }

                        if (!UteisValidacao.emptyString(bookingid)) {
                            agendado.setGymPass(true);
                        }

                        lista.add(agendado);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao validarLimiteAgregadosAtingidoPorAula: " + e.getMessage());
        }

        Integer qtVagasOcupadasAlunosAgregados = 0;
        for (AgendadoJSON agendado : lista) {
            if (agendado.isGymPass() || agendado.isTotalPass()) {
                qtVagasOcupadasAlunosAgregados++;
            }
        }

        if (qtVagasOcupadasAlunosAgregados >= horarioTurmaVO.getLimiteVagasAgregados()) {
            throw new Exception("Limite de vagas para alunos agregados atingido!");
        }
    }

    private Boolean retornaValidaAlunoGymPass(Integer codigoCliente) throws Exception {
        String TokenGymPass = getClienteDao().consultarTokenGymPassCliente(codigoCliente);
        return UteisValidacao.emptyString(TokenGymPass);
    }

    private Boolean retornaValidaAlunoTotalPass(Integer codigoCliente) throws Exception {
        boolean isClienteTotalPass = getClienteDao().consultarClienteTotalPass(codigoCliente);
        return isClienteTotalPass;
    }

    private void validarIdade(ParamAlunoAulaCheiaTO param, AgendaTotalJSON json) throws Exception {
        if( UteisValidacao.emptyNumber(json.getIdadeMaxima()) && UteisValidacao.emptyNumber(json.getIdadeMaximaMeses())
                && UteisValidacao.emptyNumber(json.getIdadeMinima()) && UteisValidacao.emptyNumber(json.getIdadeMinimaMeses())
        ){
            return;
        }

        try (ResultSet dataNascimento = SuperFacadeJDBC.criarConsulta("select p.datanasc from pessoa as p " +
                "inner join cliente as c on c.pessoa = p.codigo where c.codigo = " + param.getCodigoCliente(), con)) {
            String validaIdade = "";
            if (dataNascimento.next()) {
                Date dataNasc = dataNascimento.getDate("datanasc");
                if (dataNasc == null) {
                    dataNasc = Calendario.hoje();
                }
                String dataNascCalcular = Uteis.getDataAplicandoFormatacao(dataNasc, "yyyy-MM-dd");
                validaIdade = Uteis.CalculaIdadeComMeses(dataNascCalcular);
            }
            String[] split = validaIdade.split("-");
            int idadeAnoAluno = 0;
            int idadeMesAluno = 0;
            if(isNotBlank(split[0])) {
                idadeAnoAluno = Integer.parseInt(split[0]);
                idadeMesAluno = Integer.parseInt(split[1]);
            }
            if ((idadeAnoAluno < json.getIdadeMinima()) || (json.getIdadeMinima() == idadeAnoAluno && idadeMesAluno < json.getIdadeMinimaMeses())) {
                throw new Exception("A idade do aluno é inferior ao limite permitido para participar desta aula.");
            }
            if ((idadeAnoAluno > json.getIdadeMaxima()) || idadeAnoAluno ==  json.getIdadeMaxima() && idadeMesAluno >  json.getIdadeMaximaMeses()){
                throw new Exception("A idade do aluno é superior ao limite permitido para participar desta aula.");
            }
        }
    }
    private void validarNivel(ParamAlunoAulaCheiaTO param, AgendaTotalJSON json) throws Exception {
        if(UteisValidacao.emptyString(json.getNivel()) || UteisValidacao.emptyString(json.getNiveis())){
            return;
        }
        String[] split = json.getNiveis().split(",");
        boolean algumNivelValido = false;
        for (String s : split) {
            try {
                Integer codigoNivel = Integer.valueOf(s);
                if(codigoNivel > 0){
                    algumNivelValido = true;
                    if(codigoNivel.equals(param.getNivel())) {
                        return;
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        if(algumNivelValido){
            throw new Exception("O aluno não possui um dos níveis exigidos para a aula.");
        }


    }

    private void validarQuantidadeMaximaAulaExperimental(ParamAlunoAulaCheiaTO param, AgendaTotalJSON json) throws Exception {
        if(json.getQtdeMaximaAlunoExperimental() != null && json.getQtdeMaximaAlunoExperimental() > 0){
            if(json.getNrVagasPreenchidasExperimental() >= json.getQtdeMaximaAlunoExperimental()){
                throw new Exception("Todas as vagas para essa aula experimental já foram preenchidas");
            }
        }

    }

    private void validarToleranciaAula(ParamAlunoAulaCheiaTO param, AgendaTotalJSON json) throws Exception {
        if (param.getIgnorarTolerancia()) {
            return;
        }
        ResultSet rs = turmaDao.consultarToleranciaTurma(param.getCodigoHorarioTurma());
        if (rs.next()) {
            Integer tolerancia = rs.getInt("tolerancia");
            Integer tipoTolerancia = rs.getInt("tipoTolerancia");
            Date agoraTimeZone = dataComTimezone(param.getEmpresa());

            Date inicioAula = Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm");
            if (TipoToleranciaAulaEnum.APOS_INICIO.getCodigo().equals(tipoTolerancia)) {
                Long diferencaEmMinutos = Uteis.minutosEntreDatas(inicioAula, agoraTimeZone);
                if (diferencaEmMinutos > tolerancia) {
                    throw new Exception("Você só pode marcar "
                            + (getMensagemAntecedencia(tolerancia))
                            + " após o início da aula.");
                }
            } else {
                Long diferencaEmMinutos = Uteis.minutosEntreDatas(agoraTimeZone, inicioAula);
                if (diferencaEmMinutos < tolerancia) {
                    throw new Exception("Você só pode marcar até "
                            + (getMensagemAntecedencia(tolerancia))
                            + " antes do início da aula.");
                }
            }

        }
    }

    @Override
    public String inserirAlunoAulaCheiaConversasIA(ParamAlunoAulaCheiaTO param) {
        // ESTE FLUXO É EXCLUSIVO PARA O CONVERSA AI ***********
        // apenas uma cópia do inserirAlunoAulaCheia(ParamAlunoAulaCheiaTO param) como não vai precisar validar as mesmas
        // coisas é mais seguro criar um novo que seja exclusivo para o conversas ia
        Connection transactionConnection = null;
        boolean originalAutoCommit = true;
        try {
            keySemaphore.acquire(param.getKey(), "inserirAlunoAulaCheia");

            // Obter uma nova conexão do pool para gerenciamento explícito de transação
            transactionConnection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            originalAutoCommit = transactionConnection.getAutoCommit();
            transactionConnection.setAutoCommit(false);

            AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
            alunoHorario.setCliente(param.getCodigoCliente());
            alunoHorario.getHorarioTurma().setCodigo(param.getCodigoHorarioTurma());
            alunoHorario.setExperimental(param.isAulaExperimental());
            alunoHorario.setData(param.getData());
            alunoHorario.setUsuario(param.getCodigoUsuario());
            alunoHorario.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(param.getOrigemSistema()));

            // Usar a conexão transacional para todas as operações
            HorarioTurmaInterfaceFacade horarioDao = new HorarioTurma(transactionConnection);
            if (horarioDao.alunoAgendadoHorarioDia(alunoHorario)) {
                throw new Exception("Você já se matriculou nessa aula!");
            }

            SituacaoClienteSinteticoDW swDao = new SituacaoClienteSinteticoDW(transactionConnection);
            SituacaoClienteSinteticoDWVO situacaoSintetico = swDao.obterSituacaoContratoCliente(alunoHorario.getCliente());

            MovProdutoInterfaceFacade movProdutoDao = new MovProduto(transactionConnection);
            boolean alunoDesafioVigente = movProdutoDao.verificarDesafioVigente(alunoHorario);
            AgendaTotalJSON json = consultarUmaTurma(param.getCodigoHorarioTurma(), param.getData());

            ClienteVO clienteVO = new Cliente(transactionConnection).consultarPorCodigo(param.getCodigoCliente(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            validarNivel(param, json);
            validarIdade(param, json);
            validarToleranciaAula(param, json);
            validarQuantidadeMaximaAulaExperimental(param, json);
            validarToleranciaEmpresa(param, json);

            Integer codigoEmpresa = param.getEmpresa();
            if (!UteisValidacao.emptyNumber(param.getCodigoHorarioTurma())) {
                TurmaInterfaceFacade turmaDao = new Turma(transactionConnection);
                TurmaVO turmaVO = turmaDao.consultarPorHorarioTurma(param.getCodigoHorarioTurma(), Uteis.NIVELMONTARDADOS_MINIMOS);
                codigoEmpresa = turmaVO != null && turmaVO.getEmpresa() != null && !UteisValidacao.emptyNumber(turmaVO.getEmpresa().getCodigo()) ? turmaVO.getEmpresa().getCodigo() : param.getEmpresa();
            }

            EmpresaInterfaceFacade empresaDao = new Empresa(transactionConnection);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (empresaVO.getCapacidadeSimultanea() > 0) {
                HorarioTurmaVO horarioTurmaVO = horarioDao.consultarPorChavePrimaria(param.getCodigoHorarioTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                List<Integer> clientesAgendados = horarioDao.consultarClientesComAgendamentosNoHorario(param.getData(), param.getCodigoHorarioTurma(), empresaVO.getCodigo(), false);

                AcessoClienteInterfaceFacade acessoDao = new AcessoCliente(transactionConnection);
                List<Integer> clientesDentroDaAcademia = acessoDao.consultarClientesNoHorario(param.getData(), horarioTurmaVO.getHoraInicial(), empresaVO.getCodigo());

                List<Integer> clientes = new ArrayList<>();
                for (Integer clienteAgendado : clientesAgendados) {
                    if (!clientes.contains(clienteAgendado)) {
                        clientes.add(clienteAgendado);
                    }
                }

                for (Integer clienteDentroDaAcademia : clientesDentroDaAcademia) {
                    if (!clientes.contains(clienteDentroDaAcademia)) {
                        clientes.add(clienteDentroDaAcademia);
                    }
                }

                clientes.remove(param.getCodigoCliente());

                int quantidadeClientes = clientes.size();
                if (quantidadeClientes >= empresaVO.getCapacidadeSimultanea()) {
                    throw new ConsistirException("A Academia se encontra lotada para o horário escolhido.");
                }
            }

            if (!empresaVO.isPermMarcarAulaFeriado()) {
                FeriadoInterfaceFacade feriadoDAO = new Feriado(transactionConnection);
                if (feriadoDAO.validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresaVO, param.getData())) {
                    throw new ConsistirException("A Academia não é aberta nesse dia: " + Uteis.getData(param.getData()) + " , pois é feriado.");
                }
            }

            String situacao = swDao.obterSituacaoCliente(alunoHorario.getCliente());

            SituacaoClienteSinteticoDWVO st = swDao.qtdCreditoAluno(alunoHorario.getCliente());

            FilaDeEsperaTO filaDeEsperaParam = new FilaDeEsperaTO();

            validarAulaEstaCheia(json, false, filaDeEsperaParam);

            alunoHorario.setDatalancamento(new java.sql.Timestamp(dataComTimezone(param.getEmpresa()).getTime()));
            boolean aulaCheia = json.getNrVagas() <= json.getNrVagasPreenchidas();
            if (aulaCheia) {
                throw new Exception("A aula já está cheia!");
            } else {
                // Usar a conexão transacional para incluir o aluno na aula
                horarioDao.incluirAlunoAulaCheia(alunoHorario, json, param.getKey());
            }

            // Commit explícito da transação após todas as operações de banco
            transactionConnection.commit();

            json.setDatalancamento(alunoHorario.getDatalancamento());

            if (param.isSomenteValidar()) {
                return "VALIDADO OK";
            }
            json.setMatricula(clienteVO.getCodigoMatricula());
            // preencherJSON(param.getCodigoCliente(), json);
            //Adiciona o aluno adicionado ao contador de vagas
            json.setNrVagasPreenchidas(json.getNrVagasPreenchidas() + 1);
            logAluno(param.getCodigoHorarioTurma() + "_" + Calendario.getDataAplicandoFormatacao(param.getData(), "dd/MM/yyyy"),
                    param.getCodigoUsuario(),
                    clienteVO.getPessoa().getCodigo(),
                    json,
                    param.getOrigemSistema(),
                    false,
                    true,
                    false,
                    false, clienteVO.getCodigo());
            return json.toJSON();
        } catch (Exception e) {
            // Rollback em caso de exceção
            if (transactionConnection != null) {
                try {
                    transactionConnection.rollback();
                } catch (SQLException rollbackEx) {
                    Uteis.logar(rollbackEx, TurmasServiceImpl.class);
                }
            }
            e.printStackTrace();
            Uteis.logar(e, TurmasServiceImpl.class);
            return "ERRO: " + e.getMessage();
        } finally {
            // Restaurar o estado original do autocommit e fechar a conexão
            if (transactionConnection != null) {
                try {
                    transactionConnection.setAutoCommit(originalAutoCommit);
                    transactionConnection.close();
                } catch (SQLException finallyEx) {
                    finallyEx.printStackTrace();
                    Uteis.logar(finallyEx, TurmasServiceImpl.class);
                }
            }
            keySemaphore.release(param.getKey(), "inserirAlunoAulaCheia");
        }
    }

    @Override
    public String inserirAlunoAulaCheia(ParamAlunoAulaCheiaTO param) {
        try {
            keySemaphore.acquire(param.getKey(), "inserirAlunoAulaCheia");
            if (param.isValidarModalidade()) {
                if(param.getNrValidarVezesModalidade() > 0) {
                    int totalAulas = contarAulasPorModalidade(
                            param.getCodigoCliente(),
                            param.getCodigoHorarioTurma(),
                            param.getData(),
                            param.getModalidade()
                    );
                    if (totalAulas >= param.getNrValidarVezesModalidade()) {
                        throw new Exception("Reagende ou cancele uma aula já marcada para liberar mais um agendamento nesta modalidade. ");
                    }
                } else {
                    boolean aulaNaoValidaModalidade = getTurmaDao().consultarConfigNaoValidarModalidadeContratoAula(param.getCodigoHorarioTurma());
                    param.setValidarModalidade(!aulaNaoValidaModalidade);
                }
            }
            int falta = contarFaltasAluno(param.getCodigoCliente(), param.getCodigoHorarioTurma(), param.getQtdtempobloqueioaluno(), param.getData());
            if(falta > 0 && param.getQtdfaltasbloqueioaluno() > 0 && falta >= param.getQtdfaltasbloqueioaluno()){
                if(OrigemSistemaEnum.getOrigemSistema(param.getOrigemSistema()).getDescricao().equals("Agenda Web")){
                    throw new Exception("Este aluno faltou "+falta+" "+ (falta > 1 ? "vezes" : "vez")+".\n Por favor aguarde "+param.getQtdtempobloqueioaluno()+" "+ (param.getQtdtempobloqueioaluno() > 1 ? "dias" : "dia" ) +"  para fazer novo agendamento nesta modalidade.");
                }else{
                    throw new Exception("Você faltou "+falta+" "+ (falta > 1 ? "vezes" : "vez")+".\n Por favor aguarde "+param.getQtdtempobloqueioaluno()+" "+ (param.getQtdtempobloqueioaluno() > 1 ? "dias" : "dia" ) +"  para fazer novo agendamento nesta modalidade.");
                }
            }
            Boolean dependente = false;
            AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
            alunoHorario.setCliente(param.getCodigoCliente());
            alunoHorario.setAutorizado(param.getAutorizado());
            alunoHorario.getHorarioTurma().setCodigo(param.getCodigoHorarioTurma());
            alunoHorario.setExperimental(param.isAulaExperimental());
            alunoHorario.setData(param.getData());
            alunoHorario.setUsuario(param.getCodigoUsuario());
            alunoHorario.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(param.getOrigemSistema()));
            if (getHorarioDao().alunoAgendadoHorarioDia(alunoHorario)) {
                throw new Exception("Você já se matriculou nessa aula!");
            }
            boolean contratoConcomitanteAlunoAtivo = contratoConcomitanteAlunoAtivo(alunoHorario.getCliente(), alunoHorario.getData());
            SituacaoClienteSinteticoDWVO situacaoSintetico = getSwDao().obterSituacaoContratoCliente(alunoHorario.getCliente());
            if (!contratoConcomitanteAlunoAtivo) {
                boolean taTrancado = false;
                try {
                    taTrancado = (getContratoOperacaoDao().existeOperacaoParaEstaData(alunoHorario.getCliente(), "TR", alunoHorario.getData(), situacaoSintetico.getCodigoContrato())
                            && !getContratoOperacaoDao().existeOperacaoParaEstaData(alunoHorario.getCliente(), "RT", alunoHorario.getData(), situacaoSintetico.getCodigoContrato()));
                } catch (Exception e) {
                    Uteis.logar(e.getMessage());
                }
                if ((!UteisValidacao.emptyString(situacaoSintetico.getSituacaoContrato()) && (situacaoSintetico.getSituacaoContrato().equals("TR") || situacaoSintetico.getSituacaoContrato().equals("TV")))
                        || taTrancado) {
                    throw new Exception("Não foi possível incluir o aluno na aula, porque o mesmo se encontra em trancamento.");
                } else if (getContratoOperacaoDao().existeOperacaoParaEstaData(alunoHorario.getCliente(), "CR", alunoHorario.getData(), situacaoSintetico.getCodigoContrato())) {
                    throw new Exception("Não foi possível incluir o aluno na aula, porque nesse dia o mesmo se encontra em férias.");
                } else if (getContratoOperacaoDao().existeOperacaoParaEstaData(alunoHorario.getCliente(), "AT", alunoHorario.getData(), situacaoSintetico.getCodigoContrato())) {
                    throw new Exception("Não foi possível incluir o aluno na aula, porque nesse dia o mesmo se encontra com Atestado.");
                }
            } else {
                validarContratoConcomitantePelaAulaSelecionada(alunoHorario, param);
            }

            boolean alunoDesafioVigente = getMovProduto().verificarDesafioVigente(alunoHorario);
            AgendaTotalJSON json = consultarUmaTurma(param.getCodigoHorarioTurma(), param.getData());

            ClienteVO clienteVO = new Cliente(con).consultarPorCodigo(param.getCodigoCliente(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PeriodoAcessoClienteVO periodoAcessoClienteGymPass = new PeriodoAcessoCliente(con).consultarPorDataPessoaTipoAcesso(param.getData(), clienteVO.getPessoa().getCodigo(), "PL", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boolean temFreePass = periodoAcessoClienteGymPass != null;
            if (!temFreePass) {
                periodoAcessoClienteGymPass = new PeriodoAcessoClienteVO();
            }

            validarNivel(param, json);
            validarIdade(param, json);
            validarToleranciaAula(param, json);
            if(!UteisValidacao.emptyString(clienteVO.getSituacao()) && (!clienteVO.getSituacao().equals("AT") || temFreePass)){
                validarVisualizarProdutoGymPass(param);
                validarVisualizarProdutoTotalPass(param);
                validaLimiteDeAulasTotalPass(param.getCodigoCliente(), param.getEmpresa());
                if (verificarSeAlunoEAgregado(param.getCodigoCliente())) {
                    validarLimiteAgregadosAtingidoPorAula(param.getCodigoHorarioTurma(), param.getData());
                }
                validarQuantidadeMaximaAulaExperimental(param, json);
            }
            validarToleranciaEmpresa(param, json);

            if (alunoDesafioVigente) {
                json.setAulaExperimental(true);
                alunoHorario.setExperimental(true);
                alunoHorario.setDesafio(true);
            } else {
                boolean temModalidade = false;
                if (!alunoHorario.getExperimental()) {
                    try {
                        temModalidade = Boolean.parseBoolean(temModalidade(param.getCodigoCliente(), param.getCodigoHorarioTurma(), param.getNrAulasExperimentais(), Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"), param, temFreePass));
                    } catch (Exception e) {
                        temModalidade = temModalidadeTitular(param.getCodigoCliente(), param.getCodigoHorarioTurma(), Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"));
                        if (temModalidade) {
                            dependente = true;
                        } else {
                            throw e;
                        }
                    }

                }
                Integer freepass = null;
                if (param.isValidarModalidade() && !alunoHorario.getExperimental() && !temModalidade) {
                    String ret = temModalidade(param.getCodigoCliente(), param.getCodigoHorarioTurma(), param.getNrAulasExperimentais(), param.getData(), param, temFreePass);
                    if (!UteisValidacao.emptyString(ret)) {
                        freepass = Integer.parseInt(ret);
                    } else {
                        freepass = null;
                    }
                }

                if (freepass == null && (!temModalidade && (param.isValidarModalidade() || param.isValidarHorario()))) {
                    try {
                        freepass = validarHorarioAulaExperimental(param, json);
                    } catch (Exception e) {
                        freepass = temHorarioTitular(param, Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"), e);
                        dependente = true;
                    }
                }

                if (freepass == null && param.isControlarFreepass() && !param.isValidarModalidade() && !param.isValidarHorario()
                        && !alunoAtivo(param.getCodigoCliente(), situacaoSintetico.getSituacaoContrato()) && !temModalidade) {
                    freepass = verificarControlePorFreepass(Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"), param);
                }

                if (freepass == null) {
                    Integer modalidade = !UteisValidacao.emptyNumber(json.getCodigoTipo())
                            ? json.getCodigoTipo()
                            : extrairModalidade(param.getCodigoCliente(), param.getData(), param.getCodigoHorarioTurma(), param.isPermAlunoMarcarAulaOutraEmpresa());
                    freepass = getHorarioDao().produtoDiaria(param.getCodigoCliente(), modalidade, param.getData());
                }

                if (freepass != null && !param.isSomenteValidar()) {
                    String inserirReposicaoAlunoExperimental = inserirReposicaoAlunoExperimental(param.getKey(),
                            param.getData(),
                            param.getCodigoHorarioTurma(),
                            param.getCodigoCliente(),
                            param.getCodigoUsuario(),
                            param.getOrigemSistema(),
                            param.getEmpresa(),
                            freepass);
                    if (inserirReposicaoAlunoExperimental.startsWith("ERRO:")) {
                        throw new Exception(inserirReposicaoAlunoExperimental.replace("ERRO:", ""));
                    }
                    alunoHorario.setExperimental(true);
                    preencherJSON(param.getCodigoCliente(), json);
                    //Adiciona o aluno adicionado ao contador de vagas
                    json.setNrVagasPreenchidas(json.getNrVagasPreenchidas() + 1);
                    return json.toJSON();
                }
            }
            Integer codigoEmpresa = param.getEmpresa();
            if(!UteisValidacao.emptyNumber(param.getCodigoHorarioTurma())) {
                TurmaVO turmaVO = turmaDao.consultarPorHorarioTurma(param.getCodigoHorarioTurma(), Uteis.NIVELMONTARDADOS_MINIMOS);
                codigoEmpresa = turmaVO != null && turmaVO.getEmpresa() != null && !UteisValidacao.emptyNumber(turmaVO.getEmpresa().getCodigo()) ? turmaVO.getEmpresa().getCodigo() : param.getEmpresa();
            }
            EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (empresaVO.getCapacidadeSimultanea() > 0) {
                HorarioTurmaVO horarioTurmaVO = getHorarioDao().consultarPorChavePrimaria(param.getCodigoHorarioTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                List<Integer> clientesAgendados = getHorarioDao().consultarClientesComAgendamentosNoHorario(param.getData(), param.getCodigoHorarioTurma(), empresaVO.getCodigo(), false);
                List<Integer> clientesDentroDaAcademia = getAcessoDao().consultarClientesNoHorario(param.getData(), horarioTurmaVO.getHoraInicial(), empresaVO.getCodigo());

                List<Integer> clientes = new ArrayList<>();
                for (Integer clienteAgendado : clientesAgendados) {
                    if (!clientes.contains(clienteAgendado)) {
                        clientes.add(clienteAgendado);
                    }
                }

                for (Integer clienteDentroDaAcademia : clientesDentroDaAcademia) {
                    if (!clientes.contains(clienteDentroDaAcademia)) {
                        clientes.add(clienteDentroDaAcademia);
                    }
                }

                clientes.remove(param.getCodigoCliente());

                int quantidadeClientes = clientes.size();
                if (quantidadeClientes >= empresaVO.getCapacidadeSimultanea()) {
                    throw new ConsistirException("A Academia se encontra lotada para o horário escolhido.");
                }
            }

            if (!empresaVO.isPermMarcarAulaFeriado()) {
                if (getFeriadoDAO().validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresaVO, param.getData())) {
                    throw new ConsistirException("A Academia não é aberta nesse dia: " + Uteis.getData(param.getData()) + " , pois é feriado.");
                }
            }
            if (param.isProibirComParcelaVencida()) {
                try (ResultSet rsPessoa = SuperFacadeJDBC.criarConsulta("select pessoa from cliente where codigo = " + param.getCodigoCliente(), getCon())) {
                    if (rsPessoa.next()) {
                        final int codPessoa = rsPessoa.getInt(1);
                        proibirMarcarAulaPorParcelVencida(param.getKey(), param.getEmpresa(), codPessoa);
                    }
                }
            }
            if (param.isProibirMarcarAulaAntesPagamentoPrimeiraParcela()) {
                proibirMarcarAulaAntesPagamentoPrimeiraParcela(situacaoSintetico.getCodigoContrato());
            }
            Boolean usarReposicao = false;
            if (!alunoHorario.getExperimental() && !dependente) {
                usarReposicao = verificarRestricoesMarcacao(param.getCodigoCliente(), json, param.getData(), param.isPermAlunoMarcarAulaOutraEmpresa(),
                        param.isValidarModalidade(), json.isValidarRestricoesMarcacao(),
                        param.getNrAulasExperimentais() == null ? 0 : param.getNrAulasExperimentais(), periodoAcessoClienteGymPass.getTokenGymPass(), param.isControlarFreepass());

            }
            String situacao = getSwDao().obterSituacaoCliente(alunoHorario.getCliente());
            if (dependente) {
                situacao = "AT";
                AfastamentoContratoDependente afastamentoContratoDependenteDAO = new AfastamentoContratoDependente(con);
                boolean temAfastamentoDependente = afastamentoContratoDependenteDAO.existeAfastamentoParaData(alunoHorario.getCliente(), Calendario.hoje());
                if (temAfastamentoDependente) {
                    throw new Exception("Agendamento não permitido: O aluno está em período de afastamento (férias/atestado).");
                }
            }
            if ("VI".equals(situacao)) {
                if (Calendario.maior(param.getData(), Calendario.hoje())) {
                    ContratoVO contratoVO = getContratoDao().consultarContratoVigentePorPessoaSintetico(clienteVO.getPessoa().getCodigo(), param.getData());
                    if (contratoVO != null && "AT".equals(contratoVO.getSituacao())) {
                        situacao = "AT";
                    }
                }
            }

            if (!"AT".equals(situacao) && !alunoHorario.getExperimental() && !alunoDesafioVigente) {
                boolean temPeriodoDeAcesso = DaoAuxiliar.retornarAcessoControle(param.getKey()).getClienteDao().possuiPeriodoDeAcesso(param.getCodigoCliente());
                if ("IN".equals(situacao) && !temPeriodoDeAcesso && param.getNrAulasExperimentais() < 1) {
                    throw new Exception("O aluno não está ativo.");
                } else if (UteisValidacao.emptyString(periodoAcessoClienteGymPass.getTokenGymPass()) && !retornaValidaAlunoTotalPass(param.getCodigoCliente())) {
                    throw new Exception("AULAEXPERIMENTAL A aula não está dentro do horário do seu plano, deseja usar uma de suas " + param.getNrAulasExperimentais() + " aulas experimentais?");
                }
            }

            SituacaoClienteSinteticoDWVO st = getSwDao().qtdCreditoAluno(alunoHorario.getCliente());
            Boolean validarSaldoCredito = st.isValidarSaldoCreditoTreino() && !st.getSituacao().equals("IN");

            Boolean isAlunoDependenteComPlanoCredito = !UteisValidacao.emptyNumber(clienteVO.getTitularPlanoCompartilhado()) && validarSaldoCredito;

            validarSeTemCreditoContratosConcomitantesPorModalidade(st, param.getData(), json.getCodigoTipo(), validarSaldoCredito, contratoConcomitanteAlunoAtivo, isAlunoDependenteComPlanoCredito);

            FilaDeEsperaTO filaDeEsperaParam = new FilaDeEsperaTO();
            filaDeEsperaParam.setCodigoAluno(param.getCodigoCliente());
            filaDeEsperaParam.setCodigoHorarioTurma(param.getCodigoHorarioTurma());
            filaDeEsperaParam.setDataEntrada(Calendario.hoje());
            filaDeEsperaParam.setDia(Uteis.getDataAplicandoFormatacao(alunoHorario.getData(), "dd/MM/yyyy HH:mm:ss"));

            validarAulaEstaCheia(json, param.getFilaEspera() != null && param.getFilaEspera(), filaDeEsperaParam);

            alunoHorario.setDatalancamento(new java.sql.Timestamp(dataComTimezone(param.getEmpresa()).getTime()));
            boolean aulaCheia = json.getNrVagas() <= json.getNrVagasPreenchidas();
            if(aulaCheia){
                if (param.getFilaEspera() != null && param.getFilaEspera()) {
                    FilaDeEsperaVO filaDeEsperaVO = new FilaDeEsperaVO();
                    filaDeEsperaVO.setCodigoAluno(filaDeEsperaParam.getCodigoAluno());
                    filaDeEsperaVO.setCodigoHorarioTurma(filaDeEsperaParam.getCodigoHorarioTurma());
                    filaDeEsperaVO.setDataEntrada(filaDeEsperaParam.getDataEntrada());
                    filaDeEsperaVO.setDia(filaDeEsperaParam.getDia());

                    getHorarioDao().incluirAlunoFilaEspera(filaDeEsperaVO);
                    try {
                        Date date = Uteis.getDataJDBC(Uteis.getDate(filaDeEsperaVO.getDia()));
                        atualizarOrdemFilaEspera(param.getCodigoHorarioTurma(),date);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                }
            } else {
                getHorarioDao().incluirAlunoAulaCheia(alunoHorario, json, param.getKey());
                if(usarReposicao){
                    getHorarioDao().atualizarReposicao(alunoHorario);
                }
                if (param.getDescontarCreditoContratoAulaMarcada() && validarSaldoCredito) {
                    descontarCreditoContratoAulaMarcada(alunoHorario);
                }
            }
           String[] inicio = json.getInicio().split(" ");
            try {
                chamarNotificacaoPushEntrouNaAula(
                        param.getKey(),
                        alunoHorario.getCliente(),
                        "Tá na agenda " + String.valueOf(Character.toChars(9989)),
                        firstLetterUpper(json.getTitulo().toLowerCase()) + " agendada em " + inicio[0] + " às " + inicio[1] + ". Aproveite!"
                );
            } catch (Exception e) {
                Uteis.logar(e, TurmasServiceImpl.class);
            }
            json.setDatalancamento(alunoHorario.getDatalancamento());

            if (param.isSomenteValidar()) {
                return "VALIDADO OK";
            }

            if (alunoHorario.getExperimental() && !alunoDesafioVigente) {
                abaterAulaExperimental(param.getCodigoCliente(), param.getNrAulasExperimentais(), param.isPermAlunoMarcarAulaOutraEmpresa());
            }

            preencherJSON(param.getCodigoCliente(), json);
            //Adiciona o aluno adicionado ao contador de vagas
            json.setNrVagasPreenchidas(json.getNrVagasPreenchidas() + 1);
            logAluno(param.getCodigoHorarioTurma() + "_" + Calendario.getDataAplicandoFormatacao(param.getData(), "dd/MM/yyyy"),
                    param.getCodigoUsuario(),
                    clienteVO.getPessoa().getCodigo(),
                    json,
                    param.getOrigemSistema(),
                    false,
                    true,
                    false,
                    false, clienteVO.getCodigo());
            return json.toJSON();
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
            return "ERRO: " + e.getMessage();
        } finally {
            keySemaphore.release(param.getKey(), "inserirAlunoAulaCheia");
        }
    }

    private boolean verificarSeAlunoEAgregado(Integer codigoCliente) throws Exception {
        ResultSet resultSet1 = SuperFacadeJDBC.criarConsulta("select exists(select gympassuniquetoken from cliente where codigo = " + codigoCliente + " and gympassuniquetoken is not null)", con);

        if (resultSet1.next()) {
            if (resultSet1.getBoolean("exists")) {
                return true;
            }
        }

        ResultSet resultSet2 = SuperFacadeJDBC.criarConsulta("select exists(select * from cliente c inner join periodoacessocliente p on p.pessoa = c.pessoa where c.situacao in ('VI', 'IN') and p.tipototalpass = true and c.codigo = " + codigoCliente + ")", con);

        if (resultSet2.next()) {
            if (resultSet2.getBoolean("exists")) {
                return true;
            }
        }

        return false;
    }

    private int consultarCodigoProdutoFreepass() throws Exception{
        StringBuilder sbSql = new StringBuilder();
        sbSql.append("SELECT codigo FROM produto\n");
        sbSql.append("WHERE tipoproduto = 'FR'\n");
        sbSql.append("AND NOT desativado\n");
        sbSql.append("AND nrdiasvigencia = 1\n");
        sbSql.append("ORDER BY codigo LIMIT 1\n");

        ResultSet rsCodigoProduto = SuperFacadeJDBC.criarConsulta(sbSql.toString(), con);
        rsCodigoProduto.next();
        return rsCodigoProduto.getInt("codigo");

    }

    private void validarAulaEstaCheia(AgendaTotalJSON json, boolean filaEspera, FilaDeEsperaTO filaDeEsperaParam) throws Exception {
        if (json.getNrVagas() <= json.getNrVagasPreenchidas()) {
            if (filaEspera) {
                if (!alunoEstaNaFila(filaDeEsperaParam.getCodigoAluno(), filaDeEsperaParam.getCodigoHorarioTurma().toString(), filaDeEsperaParam.getDia())) {
                    inserirNaFilaDeEspera(filaDeEsperaParam, false);
                } else {
                    throw new Exception("Aluno já se encontra na fila de espera");
                }
            } else {
                throw new Exception("A aula já está cheia!");
            }
        }
    }

    @Override
    public String inserirNaFilaDeEspera(FilaDeEsperaTO param, Boolean insereNaFila) {
        try {
            FilaDeEsperaVO alunoHorario = new FilaDeEsperaVO();
            alunoHorario.setCodigoAluno(param.getCodigoAluno());
            alunoHorario.setCodigoHorarioTurma(param.getCodigoHorarioTurma());
            alunoHorario.setDataEntrada(param.getDataEntrada());
            alunoHorario.setDia(param.getDia());
            alunoHorario.setVinculoComAula("espera");
            if (alunoEstaNaFila(param.getCodigoAluno(), param.getCodigoHorarioTurma().toString(), param.getDia())) {
                throw new ServiceException("Aluno já se encontra na fila de espera");
            }
            if(insereNaFila){
                getHorarioDao().incluirAlunoFilaEspera(alunoHorario);
                try {
                    Date date = Uteis.getDataJDBC(Uteis.getDate(alunoHorario.getDia()));
                    atualizarOrdemFilaEspera(param.getCodigoHorarioTurma(),date);
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select nomecliente, codigopessoa, matricula, codigocontrato from situacaoclientesinteticodw where codigocliente = " + alunoHorario.getCodigoAluno(),con);
                if (resultSet.next()) {
                    AgendaTotalJSON json = consultarUmaTurma(alunoHorario.getCodigoHorarioTurma(), Uteis.getDataJDBC(Uteis.getDate(alunoHorario.getDia())));
                    json.setNrVagasPreenchidas(json.getNrVagasPreenchidas() + 1);
                    json.setMatricula(resultSet.getInt("matricula"));
                    json.setNomeAluno(resultSet.getString("nomecliente"));
                    logAluno(alunoHorario.getCodigoHorarioTurma() + "_" + alunoHorario.getDia(),
                            param.getCodigoUsuario(),
                            resultSet.getInt("codigopessoa"),
                            json,
                            param.getOrigemSistema(), false, true, false, false, alunoHorario.getCodigoAluno());
                }
            }
            return "Aluno inserido com sucesso na fila de espera!";
          } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    @Override
    public void removerDaFilaDeEspera(FilaDeEsperaTO param) throws Exception {
        try {
            SimpleDateFormat formatoEntrada = new SimpleDateFormat("yyyyMMdd");
            Integer pessoa = null;
            String nomePessoa = null;
            Date data = formatoEntrada.parse(param.getDia());

            SuperFacadeJDBC.executarConsulta("DELETE FROM filaesperaturma WHERE cliente = " + param.getCodigoAluno() + "\n"
                    + " and horarioTurma = " + param.getCodigoHorarioTurma() + "\n"
                    + " and dataRegistro = '"+ param.getDia() +"'", con);

            ResultSet resultSetPessoa = SuperFacadeJDBC.criarConsulta("select c.pessoa, p.nome from cliente c  inner join pessoa p on p.codigo = c.pessoa  where c.codigo = " + param.getCodigoAluno(), con);
            if (resultSetPessoa.next()) {
                pessoa = resultSetPessoa.getInt("pessoa");
                nomePessoa = resultSetPessoa.getString("nome");
            }



            StringBuilder descricao = new StringBuilder();
            descricao.append("[matricula:").append(param.getMatricula()).append("]<br/>");
            descricao.append("[aluno:").append(nomePessoa).append("]<br/>");

            if (param.getOrigemSistema() != null) {
                descricao.append("[origem:").append(OrigemSistemaEnum.getOrigemSistema(param.getOrigemSistema()).getDescricao()).append("]");
            }
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            try {
                Date date = formatter.parse(param.getDia());
                atualizarOrdemFilaEspera(param.getCodigoHorarioTurma(),date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            UsuarioVO usuarioVO = new UsuarioVO();
            if(param.getCodigoUsuario()!=null){
                ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select username, nome from usuario where codigo = " + param.getCodigoUsuario(), con);
                if (resultSet.next()) {
                    usuarioVO.setCodigo(param.getCodigoUsuario());
                    usuarioVO.setUsername(resultSet.getString("username"));
                    usuarioVO.setNome(resultSet.getString("nome"));
                }
            }else{
                usuarioVO.setNome(nomePessoa);
            }

            incluirLog(param.getCodigoHorarioTurma() + "_" + Calendario.getDataAplicandoFormatacao(data, "dd/MM/yyyy"),
                    "ALUNO_AULA_COLETIVA",
                    "Aluno desmarcado fila de espera",
                    usuarioVO,
                    "EXCLUSÃO",
                    "Desmarcação de aula fila de espera" ,
                    descricao.toString(),
                    descricao.toString(),
                    pessoa, param.getCodigoAluno());
        } catch (Exception e) {
            e.getMessage();
        }
    }

    @Override
    public List<Integer> consultarFilaPorModalidade(FilaDeEsperaTO param, Integer modalidade) throws Exception {
        List<Integer> codigosModalidades = new ArrayList<>();
        try {
            codigosModalidades = new ArrayList<>();
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select fet.codigo from filaesperaturma fet \n" +
                    "         join horarioturma ht on ht.codigo = fet.horarioturma \n" +
                    "        join turma t on ht.turma = t.codigo where t.modalidade = " + modalidade + " and fet.cliente = " + param.getCodigoAluno() + " and dataRegistro between '" + param.getDia() + "' and '" + param.getDia() + "'", con);
            while (rs.next()) {
                codigosModalidades.add(rs.getInt("codigo"));
            }
        } catch (Exception e) {
            e.getMessage();
        }
        return codigosModalidades;
    }

    @Override
    public void removerDaFilaDeEsperaPorCodigo(List<Integer> codigo) {
        try {
            List<String> codigoStrList = codigo.stream().map(String::valueOf).collect(Collectors.toList());
            String codigosStr = String.join(",", codigoStrList);
            String sql = "DELETE FROM filaesperaturma WHERE codigo IN (" + codigosStr + ")";

            try (PreparedStatement stmt = con.prepareStatement(sql)) {
                stmt.executeUpdate();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void removerAlunoDeTodasAsFilaDeEspera(Integer codigoAluno, String data) throws Exception {
        try {
            SuperFacadeJDBC.executarConsulta("DELETE FROM filaesperaturma WHERE cliente = " + codigoAluno + "\n"
                    + " and dataRegistro between '" + data + "' and '" + data + "'", con);
        } catch (Exception e) {
            e.getMessage();
        }
    }

    public boolean alunoEstaNaFila(Integer codigoAluno, String horarioTurma, String dataRegistro) throws Exception {
        Integer quantidade = 0;
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select count(f.*) "
                    + "from filaesperaturma f "
                    + "where f.cliente = " + codigoAluno + "\n"
                    + "and f.horarioturma = " + horarioTurma + "\n"
                    + "and f.dataregistro = '"+ dataRegistro+"' " , con);
            if (rs.next()) {
                quantidade = rs.getInt(1);
            }
        } catch (Exception e) {
            e.getMessage();
        }

        if (quantidade > 0) {
            return true;
        } else {
            return false;
        }

    }

    private void validarContratoConcomitantePelaAulaSelecionada(AlunoHorarioTurmaVO alunoHorario, ParamAlunoAulaCheiaTO param) throws Exception {
        Cliente cliente = new Cliente(con);
        ClienteVO clienteVO = cliente.consultarPorCodigo(param.getCodigoCliente(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Integer codigoModalidade = null;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select m.codigo from horarioturma ht inner join turma t on t.codigo = ht.turma " +
                "inner join modalidade m on m.codigo = t.modalidade where ht.codigo = " + param.getCodigoHorarioTurma(), getCon())) {
            if (rs.next()) {
                codigoModalidade = rs.getInt(1);
            }
        }
        Contrato contrato = new Contrato(con);
        List<ContratoVO> listaContrato = contrato.consultarPorSituacaoContratoECodigoPessoa("AT",
                clienteVO.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        String listaCodContratos = "";
        boolean existeOperacaoCRParaTodosContratos = true;
        for (ContratoVO c : listaContrato) {
            listaCodContratos += "," + c.getCodigo();
            if (existeOperacaoCRParaTodosContratos) {
                existeOperacaoCRParaTodosContratos = getContratoOperacaoDao().existeOperacaoParaEstaData(
                        alunoHorario.getCliente(), "CR", alunoHorario.getData(), c.getCodigo());
            }
        }
        if (existeOperacaoCRParaTodosContratos) {
            throw new Exception("Não foi possível incluir o aluno na aula, porque nesse dia o mesmo se encontra em férias.");
        } else {
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select contrato from contratomodalidade c where modalidade = " + codigoModalidade +
                    " and contrato in (" + listaCodContratos.replaceFirst(",", "") + ")", getCon())) {
                if (rs.next()) {
                    if (getContratoOperacaoDao().existeOperacaoParaEstaData(
                            alunoHorario.getCliente(), "CR", alunoHorario.getData(), rs.getInt(1))) {
                        throw new Exception("Não foi possível incluir o aluno na aula, porque nesse dia o mesmo se encontra em férias.");
                    }
                }
            }
        }
    }

    private void logAluno(String codigo,
                          Integer usuario,
                          Integer pessoa,
                          AgendaTotalJSON agendamento,
                          Integer origem,
                          Boolean removendo,
                          Boolean coletiva,
                          Boolean experimental, Boolean autorizado, Integer cliente) {
        try {
            UsuarioVO usuarioVO = new UsuarioVO();
            if (OrigemSistemaEnum.BOOKING_GYMPASS.getCodigo().equals(origem)) {
                usuarioVO.setUsername("Booking Gympass");
                usuarioVO.setNome("Booking Gympass");
            } else {
                try {
                    if (OrigemSistemaEnum.getOrigemSistema(origem)!=null && OrigemSistemaEnum.getOrigemSistema(origem).equals(OrigemSistemaEnum.APP_TREINO) && (usuario != null && usuario.equals(0))) {
                        // Se origem é do APP_TREINO e usuario = 0, é ação feita pelo aluno pelo app, utilizar usuário recorrencia
                        usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                    } else {
                        usuarioVO.setCodigo(usuario);
                        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select username, nome from usuario where codigo = " + usuario, con);
                        if (resultSet.next()) {
                            usuarioVO.setUsername(resultSet.getString("username"));
                            usuarioVO.setNome(resultSet.getString("nome"));
                        }
                    }
                } catch (Exception ex) {
                    Uteis.logar(ex, TurmasServiceImpl.class);
                }
            }
            StringBuilder descricao = new StringBuilder();
            descricao.append("[matricula:").append(agendamento.getMatricula()).append("]<br/>");
            if (autorizado) {
                descricao.append("[Aluno de integração]").append("<br/>");
            }
            if (experimental) {
                descricao.append("[Aluno experimental:").append(agendamento.getNomeAluno()).append("]<br/>");
            }
            descricao.append("[aluno:").append(agendamento.getNomeAluno()).append("]<br/>");
            if (origem != null) {
                descricao.append("[origem:").append(OrigemSistemaEnum.getOrigemSistema(origem).getDescricao()).append("]");
            }
            String alunoRemovendoFila;
            String alunoMarcadoFila ;

            if(agendamento.getNrVagas() < agendamento.getNrVagasPreenchidas()){
                alunoMarcadoFila = "Aluno marcado fila de espera";
                alunoRemovendoFila = "Aluno desmarcado fila de espera";
            }else{
                alunoMarcadoFila = "Aluno marcado";
                alunoRemovendoFila = "Aluno desmarcado";
            }
            incluirLog(codigo,
                    coletiva ? "ALUNO_AULA_COLETIVA" : "ALUNO_TURMA",
                    removendo ? alunoRemovendoFila : alunoMarcadoFila,
                    usuarioVO,
                    removendo ? "EXCLUSÃO" : "INCLUSÃO",
                    removendo ? "Desmarcação de aula" : "Marcação de aula",
                    removendo ? "" : descricao.toString(),
                    removendo ? descricao.toString() : "",
                    pessoa, cliente);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }

    }

    private Date dataComTimezone(Integer empresa) {
        try {
            String timeZoneDefault = getEmpresaDao().obterTimeZoneDefault(empresa);
            return Calendario.hoje(timeZoneDefault);
        } catch (Exception e) {
            return Calendario.hoje();
        }

    }

    private boolean contratoConcomitanteAlunoAtivo(int codigocliente, Date data) throws Exception {
        try {
            try (ResultSet rsAuno = SuperFacadeJDBC.criarConsulta(
                    "SELECT situacao, pessoa from cliente where codigo = " + codigocliente, con)) {
                if (rsAuno.next()) {
                    int pessoa = rsAuno.getInt("pessoa");
                    String situacao = rsAuno.getString("situacao");
                    if (situacao.equals(SituacaoClienteEnum.ATIVO.getCodigo())) {
                        return getContratoDao().existeContratoConcomitantePessoaNaData(pessoa, data);
                    }

                }
            }
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return false;
    }

    private MovProdutoInterfaceFacade getMovProduto() throws Exception {
        if (movProduto == null) {
            movProduto = new MovProduto(getCon());
        }
        movProduto.setCon(getCon());
        return movProduto;
    }

    private Integer validarHorarioAulaExperimental(ParamAlunoAulaCheiaTO param, AgendaTotalJSON json) throws Exception {
        if (param.isValidarHorario() && !param.isAulaExperimental()) {
            return temHorario(param, Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"));
        }
        return null;
    }

    private Boolean testaMesmaEmpresaAlunoTurma(ParamAlunoAulaCheiaTO param) throws Exception {
        Integer empresaTurma = 0;
        Integer empresaCliente = 0;
        try (ResultSet rsEmpresaHorarioTurma = SuperFacadeJDBC.criarConsulta(
                "SELECT t.empresa from horarioturma ht " +
                        "inner join turma t on t.codigo = ht.turma " +
                        "where ht.codigo = " + param.getCodigoHorarioTurma(), con)) {
            if (rsEmpresaHorarioTurma.next()) {
                empresaTurma = rsEmpresaHorarioTurma.getInt("empresa");
            }
        }
        try (ResultSet rsEmpresaCliente = SuperFacadeJDBC.criarConsulta(
                "SELECT empresa from cliente where codigo = " + param.getCodigoCliente(), con)) {
            if (rsEmpresaCliente.next()) {
                empresaCliente = rsEmpresaCliente.getInt("empresa");
            }
        }
        return empresaTurma == empresaCliente;
    }

    public static String getMensagemAntecedencia(Integer minutos) {
        String msg;
        if (minutos < 60) {
            msg = minutos + " minuto" + (minutos > 1 ? "s" : "");
        } else if (minutos < (60 * 24)) {
            int horas = (minutos / 60);
            msg = horas + " hora" + (horas > 1 ? "s" : "");
        } else {
            int dias = (minutos / (60 * 24));
            msg = dias + " dia" + (dias > 1 ? "s" : "");
        }
        return msg;
    }

    public void preencherJSON(int codigoCliente, AgendaTotalJSON json) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT nraulaexperimental, nomecliente, matricula from situacaoclientesinteticodw "
                + " where codigocliente = " + codigoCliente, con)) {
            if (rs.next()) {
                json.setAulasExperimentais(rs.getInt("nraulaexperimental"));
                json.setNomeAluno(rs.getString("nomecliente"));
                json.setMatricula(rs.getInt("matricula"));
            }
        }
    }


    public List<FilaDeEsperaTO> consultarFilaJson(FilaDeEsperaTO param, boolean apenasOPrimeiro, boolean removerAoConsultar) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT f.codigo, f.cliente, f.dataregistro, f.horarioturma, f.dataentrada, f.ordem, c.matricula  \n ");
        sb.append(" FROM filaesperaturma f \n");
        sb.append(" inner join cliente c  on c.codigo = f.cliente \n ");
        sb.append(" where f.dataRegistro = '" + param.getDia() + "'\n");
        if (!UteisValidacao.emptyNumber(param.getCodigoHorarioTurma())) {
            sb.append("  and f.horarioturma = " + param.getCodigoHorarioTurma() + "\n");
        }
        if (!UteisValidacao.emptyString(param.getMatricula())) {
            sb.append("  and c.codigomatricula = " + param.getMatricula() + "\n");
        }
        sb.append(" order by f.ordem ");
        if (apenasOPrimeiro) {
            sb.append(" limit 1");
        }
        List<FilaDeEsperaTO> fila = new ArrayList<>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sb.toString(), con)) {
            while (rs.next()) {
                FilaDeEsperaTO json = new FilaDeEsperaTO();
                json.setCodigoAluno(rs.getInt("cliente"));
                json.setDia(rs.getString("dataregistro"));
                json.setCodigoHorarioTurma(rs.getInt("horarioturma"));
                json.setDataEntrada(rs.getDate("dataentrada"));
                json.setMatricula(rs.getString("matricula"));
                fila.add(json);
            }
        }
        if (!UteisValidacao.emptyString(fila.toString()) && fila.size() > 0 && removerAoConsultar) {
            removerDaFilaDeEspera(fila.get(0));
        }
        return fila;
    }


    public void abaterAulaExperimental(Integer codigoCliente, int nrAulasExperimentais, boolean permAlunoMarcarAulaOutraEmpresa) throws Exception {

        List<Integer> modalidades = new ArrayList<Integer>();
        atualizarNrAulasExperimentaisModalidadeSintetico(codigoCliente, nrAulasExperimentais, modalidades, null, permAlunoMarcarAulaOutraEmpresa, false);
        SuperFacadeJDBC.executarConsulta("UPDATE situacaoclientesinteticodw "
                + "set nraulaexperimental = (nraulaexperimental-1) where codigocliente = " + codigoCliente, con);
    }


    public int atualizarNrAulasExperimentaisModalidadeSintetico(int codigoCliente, int nrAulasExperimentais, List<Integer> modalidades, Date dataAula, boolean permAlunoMarcarAulaOutraEmpresa,
                                                                boolean validarPlanoVigente) throws Exception {
        String sqlModalidades = "";
        String colunaQuery = "";
        try (ResultSet rsSintetico = SuperFacadeJDBC.criarConsulta("SELECT modalidades, nraulaexperimental, datavigenciaateajustada, "
                + "(nraulaexperimental is null) as aenula "
                + " FROM situacaoclientesinteticodw where codigocliente =" + codigoCliente, con)) {
            if (rsSintetico.next()) {
                if (rsSintetico.getBoolean("aenula")) {
                    SuperFacadeJDBC.executarConsulta("UPDATE situacaoclientesinteticodw set nraulaexperimental = " + nrAulasExperimentais
                            + " where codigocliente = " + codigoCliente, con);
                } else {
                    nrAulasExperimentais = rsSintetico.getInt("nraulaexperimental");
                }
                if (dataAula != null) {
                    if (permAlunoMarcarAulaOutraEmpresa) {
                        colunaQuery = "tipo";
                        sqlModalidades = "select m.tipo as tipo from contrato c " +
                                "inner join contratomodalidade cm on cm.contrato = c.codigo " +
                                "inner join cliente cli on cli.pessoa = c.pessoa " +
                                "inner join modalidade m on cm.modalidade = m.codigo " +
                                "where cli.codigo = " + codigoCliente + " and  '" + Uteis.getDataFormatoBD(dataAula) + "' " +
                                "between c.vigenciade and c.vigenciaateajustada";

                        try (ResultSet rsTipoModalidades = SuperFacadeJDBC.criarConsulta(sqlModalidades, con)) {
                            while (rsTipoModalidades.next()) {
                                modalidades.add(rsTipoModalidades.getInt(colunaQuery));
                            }
                        }
                    }
                    Collections.sort(modalidades);
                    if (modalidades != null && !modalidades.isEmpty() && UteisValidacao.emptyNumber(modalidades.get(0))) {
                        colunaQuery = "modalidade";
                        sqlModalidades = "select cm.modalidade from contrato c " +
                                "inner join contratomodalidade cm on cm.contrato = c.codigo " +
                                "inner join cliente cli on cli.pessoa = c.pessoa " +
                                "where cli.codigo = " + codigoCliente + " and  '" + Uteis.getDataFormatoBD(dataAula) + "' " +
                                "between c.vigenciade and c.vigenciaateajustada";

                        try (ResultSet rsTipoModalidades = SuperFacadeJDBC.criarConsulta(sqlModalidades, con)) {
                            while (rsTipoModalidades.next()) {
                                modalidades.add(rsTipoModalidades.getInt(colunaQuery));
                            }
                        }
                    }
                }

                if (contratoConcomitanteAlunoAtivo(codigoCliente, dataAula)) {
                    consultarModalidadesContratos(codigoCliente, dataAula, modalidades);
                } else if (dataAula == null || modalidades.isEmpty()) {
                    Date vigenciaAteAjustada = rsSintetico.getDate("datavigenciaateajustada");
                    if (vigenciaAteAjustada != null && dataAula != null && validarPlanoVigente && Uteis.nrDiasEntreDatasSemHoraZerada(dataAula, vigenciaAteAjustada) < 0) {
                        consultarModalidadesContratos(codigoCliente, dataAula, modalidades);
                    } else {
                        String[] split = rsSintetico.getString("modalidades") == null ? new String[]{} : rsSintetico.getString("modalidades").split("\\|");
                        for (String ar : split) {
                            if (ar != null && !ar.isEmpty() && !ar.equals("null")) {
                                modalidades.add(Integer.valueOf(ar));
                            }
                        }
                    }
                }
            }
        }

        return nrAulasExperimentais;
    }

    private void consultarModalidadesContratos(int codCliente, Date data, List<Integer> modalidades) {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select cm.modalidade " +
                        "from contratomodalidade cm " +
                        "inner join contrato c on c.codigo = cm.contrato " +
                        "inner join pessoa p on p.codigo = c.pessoa " +
                        "inner join cliente cl on cl.pessoa = p.codigo " +
                        "where cl.codigo = " + codCliente +
                        "and '" + Uteis.getDataJDBC(data) + "' between c.vigenciade and c.vigenciaateajustada ",
                getCon())) {
            while (rs.next()) {
                modalidades.add(rs.getInt("modalidade"));
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Integer temHorario(ParamAlunoAulaCheiaTO param, Date diahora) throws Exception {
        return temHorario(param, diahora,  null);
    }
    public Integer temHorarioTitular(ParamAlunoAulaCheiaTO param, Date diahora, Exception e) throws Exception {
        try (ResultSet rsTitular = SuperFacadeJDBC.criarConsulta("select titularplanocompartilhado from cliente " +
                "where codigo = " + param.getCodigoCliente(), con)) {
            if (rsTitular.next() && rsTitular.getInt("titularplanocompartilhado") > 0) {
                return temHorario(param, diahora, rsTitular.getInt("titularplanocompartilhado"));
            }
        }
        throw e;
    }
    public Integer temHorario(ParamAlunoAulaCheiaTO param, Date diahora, Integer titular) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT h.* FROM contratohorario ch ");
        sb.append("INNER JOIN horario h ON h.codigo = ch.horario ");
        sb.append("INNER JOIN contrato c ON ch.contrato = c.codigo ");
        sb.append("INNER JOIN cliente cli ON cli.pessoa = c.pessoa ");

        if (param.isValidarModalidade()) {
            sb.append("INNER JOIN contratomodalidade cm on c.codigo = cm.contrato ");
            if (param.isPermAlunoMarcarAulaOutraEmpresa()) {
                sb.append("INNER JOIN modalidade m on m.codigo = cm.modalidade ");
                sb.append("INNER join turma t on ((cm.modalidade = t.modalidade) or (m.tipo = (select tm.tipo from modalidade tm where tm.codigo = t.modalidade))) ");
            } else {
                sb.append("INNER join turma t on cm.modalidade = t.modalidade ");
            }
            sb.append("INNER JOIN horarioturma ht on t.codigo = ht.turma ");
        }

        sb.append("WHERE cli.codigo = ").append(titular == null ? param.getCodigoCliente() : titular).append(" ");
        sb.append("AND c.situacao = 'AT' ");
        if (param.isValidarModalidade()) {
            sb.append(" and ht.codigo = ").append(param.getCodigoHorarioTurma());
        }

        try (ResultSet rsHorario = SuperFacadeJDBC.criarConsulta(sb.toString(), con)) {
            AcessoControle acessoControle = new AcessoControle(con);
            while (rsHorario.next()) {
                HorarioVO horario = Horario.montarDados(rsHorario, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, con);
                if (acessoControle.validarHorario(horario, diahora, false)){
                    return null;
                }
            }
        }
        if (param.getCodigoCliente() != null && titular == null) {
            // se aluno possui diária não precisa validar se tem horario
            Integer tipomodalidade = extrairModalidade(param.getCodigoCliente(), param.getData(), param.getCodigoHorarioTurma(), param.isPermAlunoMarcarAulaOutraEmpresa());
            if (!UteisValidacao.emptyNumber(tipomodalidade)) {
                boolean temDiaria = getHorarioDao().temDiaria(param.getCodigoCliente(), tipomodalidade, param.getData());
                if (temDiaria) {
                    return getHorarioDao().produtoDiaria(param.getCodigoCliente(), tipomodalidade, param.getData());
                }
            }
        }
        if (param.isControlarFreepass()) {
            return verificarControlePorFreepass(diahora, param);
        } else if (param.getNrAulasExperimentais() < 1) {
            throw new Exception("A aula não está dentro do horário do seu plano e você não tem aulas experimentais.");
        } else {
            throw new Exception("AULAEXPERIMENTAL A aula não está dentro do horário do seu plano, deseja usar uma de suas " + param.getNrAulasExperimentais() + " aulas experimentais?");
        }
    }

    public Integer verificarControlePorFreepass(Date dia, ParamAlunoAulaCheiaTO param) throws Exception {

        Boolean existeToken = getPeriodoAcessoDao().existePeriodoAcessoGympassVigentePorCliente(param.getCodigoCliente());

        if (!retornaValidaAlunoGymPass(param.getCodigoCliente()) && !existeToken) {
            throw new Exception("Não foi possivel agendar essa aula! " +
                    "Realize o check in na GymPass e tente novamente ou entre em contato com a recepção.");
        }
        Integer freePass = getClienteDao().jaTemFreePass(dia, param.getCodigoCliente());
        if (freePass == null) {
            Integer modalidadeDoHorario = getHorarioDao().modalidadeDoHorario(param.getCodigoHorarioTurma());
            freePass = getHorarioDao().produtoDiaria(param.getCodigoCliente(), modalidadeDoHorario, dia);
            if (freePass == null) {
                throw new Exception("CONTROLAR_FREE_PASS");
            }
        }
        return freePass;
    }


    public Boolean temModalidadeTitular(Integer codigoCliente, Integer codigoHorarioTurma, Date dataAula) throws Exception {
        try {
            Integer titular = 0;
            try (ResultSet rsTitular = SuperFacadeJDBC.criarConsulta("select titularplanocompartilhado from cliente where codigo = " + codigoCliente, con)) {
                if (rsTitular.next()) {
                    titular = rsTitular.getInt("titularplanocompartilhado");
                }
            }
            if (titular > 0) {
                List<Integer> modalidades = new ArrayList<>();
                Integer tipomodalidade = extrairModalidade(codigoCliente, dataAula, codigoHorarioTurma, false);
                String sqlModalidades = "select cm.modalidade from contrato c " +
                        "inner join contratomodalidade cm on cm.contrato = c.codigo " +
                        "inner join cliente cli on cli.pessoa = coalesce(c.pessoaoriginal, c.pessoa)  " +
                        "inner join contratodependente cd on cd.contrato = c.codigo " +
                        "where cli.codigo = " + titular + " \n" +
                        "and cd.cliente = " + codigoCliente + " \n " +
                        "and '" + Uteis.getDataFormatoBD(dataAula) + "' between cd.datainicio and cd.datafinalajustada";

                try (ResultSet rsTipoModalidades = SuperFacadeJDBC.criarConsulta(sqlModalidades, con)) {
                    while (rsTipoModalidades.next()) {
                        modalidades.add(rsTipoModalidades.getInt("modalidade"));
                    }
                }
                if (UteisValidacao.emptyList(modalidades)) {
                    String sqlModalidadesV2 = "select cm.modalidade from contrato c \n" +
                            "inner join contratomodalidade cm on cm.contrato = c.codigo \n" +
                            "inner join cliente cli on cli.pessoa = c.pessoa \n" +
                            "inner join contratodependente cd on cd.contrato = c.codigo \n" +
                            "where cli.codigo = " + titular + " \n" +
                            "and cd.cliente = " + codigoCliente + " \n " +
                            "and '" + Uteis.getDataFormatoBD(dataAula) + "' between cd.datainicio and cd.datafinalajustada";

                    try (ResultSet rsTipoModalidades = SuperFacadeJDBC.criarConsulta(sqlModalidadesV2, con)) {
                        while (rsTipoModalidades.next()) {
                            modalidades.add(rsTipoModalidades.getInt("modalidade"));
                        }
                    }
                }
                return modalidades.contains(tipomodalidade);
            }

        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return Boolean.FALSE;
    }

    public String temModalidade(Integer codigoCliente, Integer codigoHorarioTurma,
                                Integer nrAulasExperimentais, Date dataAula, ParamAlunoAulaCheiaTO param,
                                boolean temFreePass) throws Exception {

        Integer tipomodalidade = extrairModalidade(codigoCliente, dataAula, codigoHorarioTurma, param.isPermAlunoMarcarAulaOutraEmpresa());
        ModalidadeEmpresa modalidadeEmpresa = new ModalidadeEmpresa(con);
        List<Integer> modalidades = new ArrayList<>();
        nrAulasExperimentais = atualizarNrAulasExperimentaisModalidadeSintetico(codigoCliente, nrAulasExperimentais, modalidades, dataAula, param.isPermAlunoMarcarAulaOutraEmpresa(), temFreePass);

        if (UteisValidacao.emptyList(modalidades)) {
            Integer modalidadeAulaAvulsaDiaria = getHorarioDao().obterModalidadeAulaAvulsaDiaria(param.getCodigoCliente(), dataAula);
            if (!UteisValidacao.emptyNumber(modalidadeAulaAvulsaDiaria)) {
                modalidades.add(modalidadeAulaAvulsaDiaria);
            }
        }

        List<Integer> listaEmpresaModalidade = modalidadeEmpresa.listaEmpresasModalidade(tipomodalidade);
        boolean modalidadeTemEmpresa = listaEmpresaModalidade.contains(param.getEmpresa());
        if (param.isControlarFreepass() && !modalidades.contains(tipomodalidade) && (param.isValidarModalidade() || param.isValidarHorario())) {
            if (!param.isValidarModalidade() && param.isValidarHorario() && !param.isAulaExperimental()) {
                Integer hr = temHorario(param, dataAula);
                return UteisValidacao.emptyNumber(hr) ? null : hr.toString();
            } else {
                return verificarControlePorFreepass(dataAula, param).toString();
            }
        } else if (param.isPermAlunoMarcarAulaOutraEmpresa()) {
            testaTipoModalidadeAulaExperimentais(nrAulasExperimentais, tipomodalidade, modalidades, param);
            if (param.isValidarHorario() && !param.isAulaExperimental()) {
                temHorario(param, dataAula);
            }
            return (modalidades.contains(tipomodalidade) ? "TRUE" : "FALSE");
        } else {
            if (!modalidadeTemEmpresa) {
                if (nrAulasExperimentais < 1) {
                    throw new Exception("Esta aula não pertence a esta empresa, você não possui mais aulas experimentais");
                } else {
                    throw new Exception("AULAEXPERIMENTAL Esta aula não pertence a esta empresa, deseja usar uma de suas " + nrAulasExperimentais + " aulas experimentais?");
                }
            } else {
                testaTipoModalidadeAulaExperimentais(nrAulasExperimentais, tipomodalidade, modalidades, param);
            }

        }
        param.setNrAulasExperimentais(nrAulasExperimentais);
        return null;
    }


    private void testaTipoModalidadeAulaExperimentais(Integer nrAulasExperimentais, Integer tipomodalidade, List<Integer> modalidades, ParamAlunoAulaCheiaTO param) throws Exception {
        if (param.isValidarModalidade()) {
            if (!modalidades.contains(tipomodalidade) && nrAulasExperimentais < 1) {
                throw new Exception("Você não possui essa modalidade e não tem mais aulas experimentais");
            } else if (!modalidades.contains(tipomodalidade)) {
                throw new Exception("AULAEXPERIMENTAL Você não tem essa modalidade, deseja usar uma de suas " + nrAulasExperimentais + " aulas experimentais?");
            }
        }
    }

    private Integer extrairModalidade(Integer codigoCliente, Date dataAula, Integer codigoHorarioTurma, Boolean PermAlunoMarcarAulaOutraEmpresa) {
        Integer modalidade = 0;
        if (PermAlunoMarcarAulaOutraEmpresa) {
            try {
                String sqlTipoModalidade = "select m.tipo from horarioturma ht" +
                        " inner join turma t on ht.turma = t.codigo and ht.codigo = " + codigoHorarioTurma +
                        " inner join modalidade m on t.modalidade = m.codigo";

                try (ResultSet rsTipoModalidade = SuperFacadeJDBC.criarConsulta(sqlTipoModalidade, con)) {
                    if (rsTipoModalidade.next()) {
                        modalidade = rsTipoModalidade.getInt("tipo");
                        if (modalidade == null) {
                            modalidade = 0;
                        }
                    }
                }
            } catch (Exception e) {
                return 0;
            }
        }
        if (UteisValidacao.emptyNumber(modalidade)) {
            try {
                try (ResultSet rsModalidade = SuperFacadeJDBC.criarConsulta("select modalidade from horarioturma ht\n" +
                        "inner join turma t on ht.turma = t.codigo and ht.codigo = " + codigoHorarioTurma, con)) {
                    if (rsModalidade.next()) {
                        modalidade = rsModalidade.getInt("modalidade");
                    }
                }
            } catch (Exception e) {
                return 0;
            }
        }
        return modalidade;
    }

    @Override
    public String excluirAlunoAulaCheia(String key, Integer codigoCliente, Integer codigoPassivo, Integer codigoHorarioTurma, Date data, Integer empresa,
                                        Integer minutosDesmarcarAntecedencia,
                                        Integer origem,
                                        Integer usuario,
                                        Integer limiteReposicoes,
                                        Boolean manterRenovacao,
                                        Boolean bloquearGerarReposicaoAulaJaReposta) {
        try {
            AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
            alunoHorario.setCliente(codigoCliente);
            alunoHorario.setPassivo(codigoPassivo);
            alunoHorario.getHorarioTurma().setCodigo(codigoHorarioTurma);
            alunoHorario.setData(data);
            AgendaTotalJSON json = consultarUmaTurma(codigoHorarioTurma, data);
            Date agoraTimeZone = dataComTimezone(empresa);
            //Valida se é possível e quanto pode agendar com antecedência
            Date horaDaAula = Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm");
            Long diferencaEmMinutos = Uteis.minutosEntreDatas(agoraTimeZone, horaDaAula);
            if (diferencaEmMinutos < minutosDesmarcarAntecedencia && minutosDesmarcarAntecedencia != 0) {
                throw new Exception("soPodeDesmarcarAulaAte;"
                        + (getMensagemAntecedencia(minutosDesmarcarAntecedencia)));
            }
            getHorarioDao().excluirAlunoAulaCheia(alunoHorario);

            UsuarioVO usuarioVO = null;
            try {
                if (OrigemSistemaEnum.getOrigemSistema(origem).equals(OrigemSistemaEnum.APP_TREINO) && usuario.equals(0)) {
                    usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                } else {
                    usuarioVO = getUsuarioDao().consultarPorCodigo(usuario, Uteis.NIVELMONTARDADOS_TODOS);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }
            Integer codColaborador = usuarioVO != null ? usuarioVO.getColaboradorVO().getCodigo() : 0;

            getControleCreditoDao().atualizarControleCreditoTreinoAulaColetivaDesmarcada(alunoHorario.getCliente(), alunoHorario.getHorarioTurma().getCodigo(), alunoHorario.getData(), usuarioVO);

            removerAulaCheiaConfirmada(key, (!UteisValidacao.emptyNumber(codigoPassivo) ? 0 : alunoHorario.getCliente()), (UteisValidacao.emptyNumber(codigoPassivo) ? 0 : codigoPassivo), alunoHorario.getHorarioTurma().getCodigo(), alunoHorario.getData(), codColaborador, OrigemSistemaEnum.getOrigemSistema(origem));
            ReposicaoVO reposicaoVO = getReposicaoDao().consultaReposicaoClienteDiaTurma(alunoHorario.getCliente(), alunoHorario.getHorarioTurma().getCodigo(), alunoHorario.getData(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (null != reposicaoVO) {
                getReposicaoDao().excluir(reposicaoVO);
            }
            OrigemSistemaEnum origemEnum = UteisValidacao.emptyNumber(origem) ?
                    null : OrigemSistemaEnum.getOrigemSistema(origem);
            try {
                ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select nomecliente, codigopessoa, matricula, codigocontrato from situacaoclientesinteticodw where codigocliente = " + codigoCliente,
                        con);

                if (resultSet.next()) {
                    getHorarioDao().gravarAlunoHorarioTurmaDesmarcado(codigoHorarioTurma,
                            codigoCliente,
                            resultSet.getInt("codigocontrato"),
                            horaDaAula,
                            origemEnum == null ? "" : origemEnum.getDescricao(),
                            usuario,
                            limiteReposicoes,
                            manterRenovacao,
                            bloquearGerarReposicaoAulaJaReposta);
                    json.setMatricula(resultSet.getInt("matricula"));
                    json.setNomeAluno(resultSet.getString("nomecliente"));
                    logAluno(codigoHorarioTurma + "_" + Calendario.getDataAplicandoFormatacao(data, "dd/MM/yyyy"),
                            usuario,
                            resultSet.getInt("codigopessoa"),
                            json,
                            origem, true, true, false, false, codigoCliente);
                }
            } catch (Exception e) {
                Uteis.logar(e, TurmasServiceImpl.class);
            }
            inserirAlunoAulaDaFila(data, codigoHorarioTurma, key);
            atualizarOrdemFilaEspera(codigoHorarioTurma, data);
            return "OK";
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    private void atualizarOrdemFilaEspera(Integer codigoHorarioTurma, Date dia) throws Exception {
        List<FilaDeEsperaTO> filaDeEsperaAtualizada = new ArrayList<>();
        String sql = "SELECT f.cliente, f.ordem FROM filaesperaturma f " +
                "WHERE f.horarioturma = " + codigoHorarioTurma + " and f.dataRegistro = '"+ dia+"' ORDER BY f.ordem asc ,  f.dataentrada asc ";

        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                FilaDeEsperaTO aluno = new FilaDeEsperaTO();
                aluno.setCodigoAluno(rs.getInt("cliente"));
                aluno.setOrdem(rs.getInt("ordem"));
                filaDeEsperaAtualizada.add(aluno);
            }

            for (int i = 0; i < filaDeEsperaAtualizada.size(); i++) {
                filaDeEsperaAtualizada.get(i).setOrdem(i + 1);
                atualizarOrdemAluno(filaDeEsperaAtualizada.get(i));
            }
        } catch (Exception e) {
            throw new Exception("Erro ao atualizar ordem da fila de espera: " + e.getMessage(), e);
        }
    }

    private void atualizarOrdemAluno(FilaDeEsperaTO aluno) throws Exception {
        String sql = "UPDATE filaesperaturma SET ordem = ? WHERE cliente = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, aluno.getOrdem());
            ps.setInt(2, aluno.getCodigoAluno());
            ps.executeUpdate();
        } catch (SQLException e) {
            throw new Exception("Erro ao atualizar ordem do aluno: " + e.getMessage(), e);
        }
    }

    private void inserirAlunoAulaDaFila(Date dia, Integer codigoHorarioTurma, String key) throws Exception {
        FilaDeEsperaTO paramConsultaFila = new FilaDeEsperaTO();
        paramConsultaFila.setDia(Uteis.getDataJDBC(dia).toString());
        paramConsultaFila.setCodigoHorarioTurma(codigoHorarioTurma);

        List<FilaDeEsperaTO> filaDeEsperaTOES = consultarFilaJson(paramConsultaFila, true, true);

        while (!filaDeEsperaTOES.isEmpty()) {
            FilaDeEsperaTO primeiro = filaDeEsperaTOES.get(0);
            try {
                AgendaTotalJSON json = consultarUmaTurma(codigoHorarioTurma, dia);

                // Calcula aulas já marcadas no dia antes da validação
                int aulasMarcadasDia = getHorarioDao().nrAulasColetivasMarcadasAlunoPorModalidadePeriodo(
                        json.getCodigoTipo(),
                        null,
                        primeiro.getCodigoAluno(),
                        null,
                        dia,
                        dia,
                        false
                );

                // Valida restrições para o aluno da fila
                boolean podeInserir = verificarRestricoesMarcacao(
                        primeiro.getCodigoAluno(),
                        json,
                        dia,
                        false,
                        true,
                        true,
                        0,
                        null,
                        false
                );

                if (podeInserir) {
                    inserirAluno(json, primeiro, codigoHorarioTurma, dia, key);
                    break;
                } else {
                    removerAlunoFilaDeEspera(primeiro.getCodigoAluno(), dia, json, aulasMarcadasDia);
                }
            } catch (Exception e) {
                removerAlunoFilaDeEspera(primeiro.getCodigoAluno(), dia, null, 0);
                System.err.println("Erro ao processar aluno da fila: " + e.getMessage());
            }

            filaDeEsperaTOES = consultarFilaJson(paramConsultaFila, true, true);
        }
        atualizarOrdemFilaEspera(codigoHorarioTurma, dia);
    }

    private void inserirAluno(AgendaTotalJSON json, FilaDeEsperaTO primeiro, Integer codigoHorarioTurma, Date dia, String key) throws Exception {
        AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
        alunoHorario.setCliente(primeiro.getCodigoAluno());
        alunoHorario.getHorarioTurma().setCodigo(codigoHorarioTurma);
        alunoHorario.setExperimental(false);
        alunoHorario.setData(dia);
        alunoHorario.setUsuario(null);
        alunoHorario.setOrigemSistema(OrigemSistemaEnum.FILA_ESPERA);
        alunoHorario.setEspera(true);
        alunoHorario.setVinculoComAula("ESPERA");

        getHorarioDao().incluirAlunoAulaCheia(alunoHorario, json, key);

        ResultSet resultSet = SuperFacadeJDBC.criarConsulta(
                "select nomecliente, codigopessoa, matricula from situacaoclientesinteticodw where codigocliente = " + primeiro.getCodigoAluno(), con);

        if (resultSet.next()) {
            json.setMatricula(resultSet.getInt("matricula"));
            json.setNomeAluno(resultSet.getString("nomecliente"));

            logAluno(
                    codigoHorarioTurma + "_" + Calendario.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"),
                    0,
                    resultSet.getInt("codigopessoa"),
                    json,
                    OrigemSistemaEnum.FILA_ESPERA.getCodigo(),
                    false,
                    true,
                    false,
                    false,
                    primeiro.getCodigoAluno()
            );
        }

        enviarNotificacao(
                "Você ganhou a vaga na aula " + firstLetterUpper(json.getTitulo().toLowerCase()) + ".",
                primeiro.getCodigoAluno(),
                key
        );
    }

    private void removerAlunoFilaDeEspera(Integer alunoHorario, Date dia, AgendaTotalJSON json, int aulasMarcadasDia) throws Exception {
        try {
            FilaDeEsperaTO paramFilaDeEspera = new FilaDeEsperaTO();
            paramFilaDeEspera.setCodigoAluno(alunoHorario);
            paramFilaDeEspera.setDia(Uteis.getData(dia));
            List<Integer> codigosFila = consultarFilaPorModalidade(paramFilaDeEspera, json != null ? json.getCodigoTipo() : null);
            if (!codigosFila.isEmpty()) {
                removerDaFilaDeEsperaPorCodigo(codigosFila);
                logAluno(
                        "Removido_" + alunoHorario + "_" + Calendario.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"),
                        0,
                        alunoHorario,
                        json,
                        OrigemSistemaEnum.FILA_ESPERA.getCodigo(),
                        true,
                        true,
                        false,
                        false,
                        alunoHorario
                );
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void enviarNotificacao(String mensagem, Integer codigoAluno, String key) {
        try {
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
            HttpClient httpClient = ExecuteRequestHttpService.createConnector();
            HttpPost post = new HttpPost(urlTreino + "/prest/aula/" + key + "/mensagemAoEntrarNaAula");
            post.setHeader("Accept", "application/json");
            post.setHeader("headerValue", "HeaderInformation");

            JSONObject json = new JSONObject();
            json.put("codigoAluno", codigoAluno.toString());
            json.put("mensagem", mensagem);

            StringEntity entity = new StringEntity(json.toString(), "UTF-8");
            entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
            post.setEntity(entity);
            HttpResponse response = httpClient.execute(post);
        } catch (UnsupportedEncodingException e) {
            e.getMessage();
        } catch (Exception ex) {
            ex.getMessage();
        }
    }

    public void validarModalidadeContratoVigenteCliente(Integer idHorarioTurmaReposicao,
                                                        Integer codigoCliente, Date dataReposicao) throws ConsistirException, Exception {
        if (!SuperFacadeJDBC.existe("select ht.codigo from horarioturma ht\n"
                + " INNER JOIN turma t ON ht.turma = t.codigo\n"
                + " AND ht.codigo = " + idHorarioTurmaReposicao
                + " WHERE t.modalidade IN (\n" +
                " SELECT distinct coalesce(tipo.codigo, cm.modalidade) FROM contratomodalidade cm \n" +
                " inner join contrato ct on ct.codigo=cm.contrato \n" +
                " inner join pessoa p on p.codigo=ct.pessoa \n" +
                "inner join modalidade m on m.codigo = cm.modalidade\n" +
                "left join modalidade tipo on tipo.tipo = m.tipo\n" +
                " inner join cliente c on c.pessoa=p.codigo and c.codigo =  " + codigoCliente + " " +
                " and '" + Uteis.getDataJDBC(dataReposicao) + "' between ct.vigenciade and ct.vigenciaateajustada )", con)) {
            throw new ConsistirException("O aluno não tem direito a participar de turma dessa modalidade.");
        }
    }

    public void validarPossuiAulaNesseHorario(Integer codigoCliente, Date dataReposicao, Integer idHorarioTurmaReposicao,
                                              Integer idHorarioTurmaOrigem) throws Exception {
        try (ResultSet rsHorarioTurma = SuperFacadeJDBC.criarConsulta("SELECT horainicial, horafinal, diasemana FROM horarioturma WHERE codigo = " + idHorarioTurmaReposicao, con)) {
            Map<String, List<Date>> mapaDesmarcacoes = obterMapaDesmarcacoes(codigoCliente);
            if (rsHorarioTurma.next()) {
                Date inicio = Calendario.getDataComHora(dataReposicao, rsHorarioTurma.getString("horainicial"));
                Date fim = Calendario.getDataComHora(dataReposicao, rsHorarioTurma.getString("horafinal"));
                String diaSemana = rsHorarioTurma.getString("diasemana");
                try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select horainicial, horafinal, ht.codigo from matriculaalunohorarioturma rp\n"
                        + " INNER JOIN situacaoclientesinteticodw sw ON sw.codigocontrato = rp.contrato "
                        + " INNER JOIN horarioturma ht ON rp.horarioturma = ht.codigo "
                        + " AND sw.codigoCliente = " + codigoCliente
                        + " AND ht.diasemana = '" + diaSemana
                        + "' AND rp.datafim >= '" + Uteis.getDataAplicandoFormatacao(dataReposicao, "yyyy-MM-dd")
                        + "'"
                        + (idHorarioTurmaOrigem == null ? "" : (" AND ht.codigo <> " + idHorarioTurmaOrigem)), con)) {
                    while (rs.next()) {
                        List<Date> datas = mapaDesmarcacoes.get(rs.getString("codigo"));
                        datas = datas == null ? new ArrayList<Date>() : datas;
                        Date inicioMat = Calendario.getDataComHora(dataReposicao, rs.getString("horainicial"));
                        Date fimMat = Calendario.getDataComHora(dataReposicao, rs.getString("horafinal"));
                        // Somar e subtrair necessário para quando o aluno está em uma aula que termina às 20h e quer fazer uma reposição em outra aula que começa às 20h
                        inicioMat = Calendario.somarMinutos(inicioMat, 1);
                        fimMat = Calendario.subtrairMinutos(fimMat, 1);
                        if (Calendario.periodosEmInterseccao(inicio, fim, inicioMat, fimMat)
                                && !datas.contains(dataReposicao)) {
                            throw new Exception("Existe uma aula marcada para esse horário!");
                        }
                    }
                }
                Map<String, List<Date>> mapaReposicoes = obterMapaReposicoes(null, codigoCliente, null);
                for (String k : mapaReposicoes.keySet()) {
                    List<Date> datas = mapaReposicoes.get(k);
                    if (datas.contains(dataReposicao)) {
                        try (ResultSet rsHorarioTurmaReposicao = SuperFacadeJDBC.criarConsulta("SELECT horainicial, horafinal, diasemana FROM horarioturma WHERE codigo = " + k, con)) {
                            if (rsHorarioTurmaReposicao.next()) {
                                Date inicioRep = Calendario.getDataComHora(dataReposicao, rsHorarioTurmaReposicao.getString("horainicial"));
                                Date fimRep = Calendario.getDataComHora(dataReposicao, rsHorarioTurmaReposicao.getString("horafinal"));
                                if (Calendario.periodosEmInterseccao(inicio, fim, inicioRep, fimRep)) {
                                    throw new Exception("Existe uma aula marcada para esse horário!");
                                }
                            }
                        }

                    }
                }
            }
        }
    }

    @Override
    public String reporAula(String key, Date dataOrigem, Date dataReposicao,
                            Integer idHorarioTurmaOrigem, Integer idHorarioTurmaReposicao,
                            Integer codigoCliente, Integer codigoContrato, Integer usuario, Integer origemSistema, Integer empresa, Boolean forcarMarcarAulaExtra,
                            Integer spiviSeat, Integer spiviEventID, Integer spiviClientID) throws Exception {
        try {

            TurmaVO turmaOrigemV = getTurmaDao().consultarPorHorarioTurma(idHorarioTurmaOrigem, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            TurmaVO turmaDestinoV = getTurmaDao().consultarPorHorarioTurma(idHorarioTurmaReposicao, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

            if (turmaOrigemV != null && !turmaOrigemV.getModalidade().getCodigo().equals(turmaDestinoV.getModalidade().getCodigo())
                    && (UteisValidacao.emptyNumber(turmaOrigemV.getModalidade().getTipo()) || UteisValidacao.emptyNumber(turmaDestinoV.getModalidade().getTipo()) || !turmaOrigemV.getModalidade().getTipo().equals(turmaDestinoV.getModalidade().getTipo()))) {
                throw new Exception("Modalidade da aula a ser reposta deve ser igual ou do mesmo tipo da modalidade da aula de origem!");
            }
            validarPossuiAulaNesseHorario(codigoCliente, dataReposicao, idHorarioTurmaReposicao, idHorarioTurmaOrigem);

            if (codigoContrato != null && codigoContrato != 0) {
                validarModalidadeContratoVigenteCliente(idHorarioTurmaReposicao, codigoCliente, dataReposicao);
            }

            AgendaTotalJSON json = consultarUmaTurma(idHorarioTurmaReposicao, dataReposicao);
            if ((turmaDestinoV != null && !turmaDestinoV.getAulaColetiva() && turmaDestinoV.isBloquearReposicaoAcimaLimite()) && json.getNrVagasPreenchidas() != null && json.getNrVagas() <= json.getNrVagasPreenchidas()) {
                throw new Exception("A aula já está cheia!");
            }

            ContratoDuracaoCreditoTreinoVO contratoCreditos = null;

            GestaoAulaService service = new GestaoAulaService(con, key);
            ResultSet rsReposicao;
            ResultSet rsPessoa;
            Integer turmaOrigem;
            Integer turmaDestino;
            Integer matricula;
            try (ResultSet rsOrigem = SuperFacadeJDBC.criarConsulta("SELECT turma FROM horarioturma WHERE codigo = " + idHorarioTurmaOrigem, con)) {
                rsReposicao = SuperFacadeJDBC.criarConsulta("SELECT turma FROM horarioturma WHERE codigo = " + idHorarioTurmaReposicao, con);
                rsPessoa = SuperFacadeJDBC.criarConsulta("SELECT pessoa, codigomatricula FROM cliente WHERE codigo = " + codigoCliente, con);
                turmaOrigem = 0;
                turmaDestino = 0;
                matricula = null;
                if (rsOrigem.next()) {
                    turmaOrigem = rsOrigem.getInt("turma");
                }
            }
            if (rsReposicao.next()) {
                turmaDestino = rsReposicao.getInt("turma");
            }

            ReposicaoVO reposicaoOrigem = null;
            if (idHorarioTurmaOrigem != null) {
                AulaDesmarcadaVO aulaDesmarcadaVO = getAulaDesmarcadaDao().consultarAulaDesmarcadaPorDiaHorarioContrato(codigoCliente, empresa,
                        codigoContrato, turmaOrigem, idHorarioTurmaOrigem,
                        dataOrigem, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, true, true);


                if (!UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()")) {
                    reposicaoOrigem = getReposicaoDao().consultarReposicaoJaLancada(codigoCliente,
                            idHorarioTurmaOrigem, turmaOrigem, null, dataOrigem);
                }
                if (reposicaoOrigem != null && !UteisValidacao.emptyNumber(reposicaoOrigem.getCodigo())) {
                    turmaOrigem = reposicaoOrigem.getTurmaOrigem().getCodigo();
                    idHorarioTurmaOrigem = reposicaoOrigem.getHorarioTurmaOrigem().getCodigo();
                    dataOrigem = reposicaoOrigem.getDataOrigem();
                }
            }

            ReposicaoVO reposicao = new ReposicaoVO();
            reposicao.setSpiviSeatID(spiviSeat);
            reposicao.setSpiviClientID(spiviClientID);
            reposicao.setSpiviEventID(spiviEventID);
            reposicao.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(origemSistema));
            if (reposicao.getOrigemSistemaEnum().equals(OrigemSistemaEnum.APP_TREINO) || UteisValidacao.emptyNumber(usuario)) {
                reposicao.setUsuario(getUsuarioDao().getUsuarioRecorrencia());
            } else {
                reposicao.setUsuario(new UsuarioVO());
                reposicao.getUsuario().setCodigo(usuario);
            }
            reposicao.setDataLancamento(dataComTimezone(empresa));
            reposicao.setDataOrigem(dataOrigem);
            reposicao.setDataReposicao(dataReposicao);
            reposicao.setCliente(new ClienteVO());
            reposicao.getCliente().setCodigo(codigoCliente);
            reposicao.getCliente().setEmpresa(getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            if (rsPessoa.next()) {
                reposicao.getCliente().getPessoa().setCodigo(rsPessoa.getInt("pessoa"));
                matricula = rsPessoa.getInt("codigomatricula");
                verificarSituacaoAluno(matricula, null);
                VerificaIdadeTurmaAluno(rsPessoa, turmaDestino);
            }

            if (reposicao.getContrato() == null) {
                reposicao.setContrato(
                        getContratoDao().consultarContratoVigentePorPessoaEData
                                (reposicao.getCliente().getPessoa().getCodigo(), dataReposicao, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                if (!codigoCliente.equals(reposicao.getContrato().getCodigo())) {
                    List<ContratoVO> listaContratos = getContratoDao().consultarContratosVigerntePorDataPessoa
                            (reposicao.getCliente().getPessoa().getCodigo(), dataReposicao, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    Optional<ContratoVO> result = listaContratos.stream().filter(contratoVO -> contratoVO.getCodigo().equals(codigoContrato)).findAny();
                    result.ifPresent(reposicao::setContrato);
                }
            } else {
                // Pode haver mais de um contrato vigente, será setado o contrato vigente com o codigo que veio do Treino.
                List<ContratoVO> listaContratos = getContratoDao().consultarContratosVigerntePorDataPessoa
                        (reposicao.getCliente().getPessoa().getCodigo(), dataReposicao, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                for (ContratoVO contrato : listaContratos) {
                    if (contrato.getCodigo().equals(codigoContrato)) {
                        reposicao.setContrato(contrato);
                        break;
                    }
                }
            }
            reposicao.getContrato().getCliente().setCodigo(codigoCliente);
            if (reposicao.getContrato().isVendaCreditoTreino()) {
                contratoCreditos = getContratoDuracaoCreditoDao().consultarPorContrato(codigoContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            reposicao.setHorarioTurma(getHorarioDao().consultarPorChavePrimaria(idHorarioTurmaReposicao, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            reposicao.setTurmaDestino(getTurmaDao().consultarPorChavePrimaria(turmaDestino, Uteis.NIVELMONTARDADOS_TODOS));
            if ((idHorarioTurmaOrigem != null && contratoCreditos == null)
                    || (contratoCreditos != null && contratoCreditos.getTipoHorarioCreditoTreinoEnum().equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA) && idHorarioTurmaOrigem != null)) {
                reposicao.setHorarioTurmaOrigem(getHorarioDao().consultarPorChavePrimaria(idHorarioTurmaOrigem, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                reposicao.setTurmaOrigem(getTurmaDao().consultarPorChavePrimaria(turmaOrigem, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } else {
                reposicao.setHorarioTurmaOrigem(reposicao.getHorarioTurma());
                reposicao.setTurmaOrigem(getTurmaDao().consultarPorChavePrimaria(turmaDestino, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            obterQuantidadeAlunosMatriculados(reposicao, json.getNrVagas());
            if ((contratoCreditos == null
                    || contratoCreditos.getTipoHorarioCreditoTreinoEnum().equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA))
                    && !forcarMarcarAulaExtra) {
                service.reporAula(reposicao, false, reposicaoOrigem, empresa);
            } else {
                reposicao.setMarcacaoAula(true);
                service.marcarAula(reposicao);
            }
            if (reposicaoOrigem != null && !UteisValidacao.emptyNumber(reposicaoOrigem.getCodigo())) {
                getReposicaoDao().excluirSemValidarPermissao(reposicaoOrigem);
            }
            return consultarSaldoAluno(matricula, forcarMarcarAulaExtra, codigoContrato);
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    private void obterQuantidadeAlunosMatriculados(ReposicaoVO reposicaoVO, int numeroVagas) throws Exception {
        //validar o limite de minutos de antecedência para marcar aula.
        boolean temPermissao;
        temPermissao = !reposicaoVO.getOrigemSistemaEnum().equals(OrigemSistemaEnum.APP_TREINO) && validarPermissaoMarcarAulaRetroativa(reposicaoVO);
        reposicaoVO.setPermitirLancarAulaRetroativa(temPermissao);
        if (!temPermissao) {
            TimeZone tz = TimeZone.getTimeZone(empresaDao.obterTimeZoneDefault(reposicaoVO.getTurmaDestino().getEmpresa().getCodigo()));
            Date agora = Calendario.hojeCalendar(tz).getTime();
            ReposicaoVO.validarMinutosAntecedenciaMarcarAula(reposicaoVO.getTurmaDestino(), reposicaoVO.getHorarioTurma(), reposicaoVO.getDataReposicao(), agora);
        }
        //validar a quantidade de alunos já inscritos na turma e verificar se está cheia ou não.
        int totalMatriculados = new HorarioTurma(con).nrAlunosMatriculadosRenovacaoRematricula(reposicaoVO.getHorarioTurma(), reposicaoVO.getDataReposicao(), null);
        Integer totalDesmacados = new AulaDesmarcada(con).consultarTotalAulasDesmarcadas(reposicaoVO.getTurmaDestino().getEmpresa().getCodigo(), reposicaoVO.getHorarioTurma().getCodigo(), reposicaoVO.getDataReposicao(), null);
        Integer totalReposicao = new Reposicao(con).consultarTotalReposicao(reposicaoVO.getHorarioTurma().getCodigo(), reposicaoVO.getDataReposicao(), null);
        int vagasPreenchidas = (totalMatriculados + totalReposicao) - totalDesmacados;
        if ((reposicaoVO.getTurmaDestino() != null && !reposicaoVO.getTurmaDestino().getAulaColetiva() && reposicaoVO.getTurmaDestino().isBloquearReposicaoAcimaLimite()) && numeroVagas <= vagasPreenchidas) {
            throw new Exception("Número de vagas excedido! Consulte as turmas novamente e verifique as vagas disponíveis.");
        }
        reposicaoVO.getHorarioTurma().setNrAlunoMatriculado((totalMatriculados + totalReposicao) - totalDesmacados);
    }

    private boolean validarPermissaoMarcarAulaRetroativa(ReposicaoVO reposicaoVO) {
        boolean retorno = false;
        try {
            Usuario usuario = new Usuario(con);
            UsuarioVO usuarioValidado = usuario.consultarPorCodigo(reposicaoVO.getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN);
            ControleAcesso controleAcesso = new ControleAcesso(con);
            Permissao permissao = new Permissao(con);
            for (Object o : usuarioValidado.getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissao.consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                controleAcesso.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuarioValidado, "PermiteMarcarAlunoForaTolerancia", "2.70 - Permite marcar e repor aula no passado");
            }
            retorno = true;
        } catch (Exception ex) {
            Uteis.logar("Erro na validação de permissão de marcar aula retroativa.");
        }

        return retorno;
    }

    private void VerificaIdadeTurmaAluno(ResultSet rsPessoa, Integer turmaDestino) throws Exception {
        try (ResultSet rsDataNasc = SuperFacadeJDBC.criarConsulta("SELECT nome, datanasc FROM pessoa WHERE codigo = " + rsPessoa.getInt("pessoa"), con)) {
            try (ResultSet rsValidaIdade = SuperFacadeJDBC.criarConsulta("SELECT idademinima, idademinimameses, idademaxima, idademaximameses FROM turma WHERE codigo = " + turmaDestino, con)) {
                String nomeException;
                if (rsDataNasc.next()) {
                    if (rsValidaIdade.next()) {
                        Date idadaAluno = rsDataNasc.getDate("datanasc");
                        String testaIdade = idadaAluno != null ? Uteis.CalculaIdadeComMeses(idadaAluno.toString()) : "0-0";
                        String[] split = testaIdade.split("-");
                        int idadeAno = Integer.parseInt(split[0]);
                        int idadeMes = Integer.parseInt(split[1]);
                        int idadeMinimaAno = rsValidaIdade.getInt("idademinima");
                        int idadeMinimaMes = rsValidaIdade.getInt("idademinimameses");
                        int idadeMaximaAno = rsValidaIdade.getInt("idademaxima");
                        int idadeMaximaMes = rsValidaIdade.getInt("idademaximameses");
                        if ((idadeAno < idadeMinimaAno) || (idadeMinimaAno == idadeAno && idadeMes < idadeMinimaMes)) {
                            nomeException = "O limite de idade minimo da turma é de " + idadeMinimaAno;
                            if (idadeMinimaAno > 1) {
                                nomeException += " anos ";
                            } else if (idadeMinimaAno == 1) {
                                nomeException += " ano";
                            }
                            if (idadeMinimaMes > 1) {
                                nomeException += "e " + idadeMinimaMes + " meses, ";
                            } else if (idadeMinimaMes == 1) {
                                nomeException += "e " + idadeMinimaMes + " mês, ";
                            }
                            nomeException += "e o aluno tem " + idadeAno;
                            if (idadeAno > 1) {
                                nomeException += " anos ";
                            } else if (idadeAno == 1) {
                                nomeException += " ano";
                            }
                            if (idadeMes > 1) {
                                nomeException += "e " + idadeMes + " meses";
                            } else if (idadeMinimaMes == 1) {
                                nomeException += "e " + idadeMes + " mês";
                            }
                            throw new Exception(nomeException);
                        } else if ((idadeAno > idadeMaximaAno) || idadeAno == idadeMaximaAno && idadeMes > idadeMaximaMes) {
                            nomeException = "O limite de idade maxima da turma é de " + idadeMaximaAno;
                            if (idadeMaximaAno > 1) {
                                nomeException += " anos ";
                            } else if (idadeMaximaAno == 1) {
                                nomeException += " ano";
                            }
                            if (idadeMaximaMes > 1) {
                                nomeException += "e " + idadeMaximaMes + " meses, ";
                            } else if (idadeMaximaMes == 1) {
                                nomeException += "e " + idadeMaximaMes + " mês, ";
                            }
                            nomeException += "e o aluno tem " + idadeAno;
                            if (idadeAno > 1) {
                                nomeException += " anos ";
                            } else if (idadeAno == 1) {
                                nomeException += " ano";
                            }
                            if (idadeMes > 1) {
                                nomeException += "e " + idadeMes + " meses";
                            } else if (idadeMinimaMes == 1) {
                                nomeException += "e " + idadeMes + " mês";
                            }
                            throw new Exception(nomeException);
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<AgendadoJSON> consultarAgendadosParaAgenda(Date inicio, Date fim, Integer empresa) throws Exception {
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        lista.addAll(getMatriculaHorarioDao().consultarHorariosTurmaParaAgenda(inicio, empresa));
        lista.addAll(getHorarioDao().consultarAgendadosAulaColetiva(inicio, fim, empresa));
        return lista;
    }

    @Override
    public List<AgendadoJSON> consultarReposicoesParaAgenda(Date inicio, Date fim, Integer empresa) throws Exception {
        return getReposicaoDao().consultarReposicoesParaAgenda(inicio, fim, empresa, null, false, null, null);
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public TurmaInterfaceFacade getTurmaDao() throws Exception {
        if (turmaDao == null) {
            turmaDao = new Turma(getCon());
        }
        turmaDao.setCon(getCon());
        return turmaDao;
    }

    public MatriculaAlunoHorarioTurmaInterfaceFacade getMatriculaHorarioDao() throws Exception {
        if (matriculaHorarioDao == null) {
            matriculaHorarioDao = new MatriculaAlunoHorarioTurma(getCon());
        }
        matriculaHorarioDao.setCon(getCon());
        return matriculaHorarioDao;
    }

    public HorarioTurmaInterfaceFacade getHorarioDao() throws Exception {
        if (horarioTurmaDao == null) {
            horarioTurmaDao = new HorarioTurma(getCon());
        }
        horarioTurmaDao.setCon(getCon());
        return horarioTurmaDao;
    }

    public ControleCreditoTreinoInterfaceFacade getControleCreditoDao() throws Exception {
        if (controleCreditoDao == null) {
            controleCreditoDao = new ControleCreditoTreino(getCon());
        }
        controleCreditoDao.setCon(getCon());
        return controleCreditoDao;
    }

    public SituacaoClienteSinteticoDW getSwDao() throws Exception {
        if (swDao == null) {
            swDao = new SituacaoClienteSinteticoDW(getCon());
        }
        swDao.setCon(getCon());
        return swDao;
    }

    public EmpresaInterfaceFacade getEmpresaDao() throws Exception {
        if (empresaDao == null) {
            empresaDao = new Empresa(getCon());
        }
        empresaDao.setCon(getCon());
        return empresaDao;
    }

    public AulaDesmarcadaInterfaceFacade getAulaDesmarcadaDao() throws Exception {
        if (aulaDesmarcadaDao == null) {
            aulaDesmarcadaDao = new AulaDesmarcada(getCon());
        }
        aulaDesmarcadaDao.setCon(getCon());
        return aulaDesmarcadaDao;
    }

    public ReposicaoInterfaceFacade getReposicaoDao() throws Exception {
        if (reposicaoDao == null) {
            reposicaoDao = new Reposicao(getCon());
        }
        reposicaoDao.setCon(getCon());
        return reposicaoDao;
    }

    public ContratoModalidade getContratoModalidadeDao() throws Exception {
        if (contratoModalidadeDao == null) {
            contratoModalidadeDao = new ContratoModalidade(getCon());
        }
        contratoModalidadeDao.setCon(getCon());
        return contratoModalidadeDao;
    }

    public ContratoModalidadeTurmaInterfaceFacade getContratoModalidadeTurmaDao() throws Exception {
        if (contratoModalidadeTurmaDao == null) {
            contratoModalidadeTurmaDao = new ContratoModalidadeTurma(getCon());
        }
        contratoModalidadeTurmaDao.setCon(getCon());
        return contratoModalidadeTurmaDao;
    }

    public ContratoInterfaceFacade getContratoDao() throws Exception {
        if (contratoDao == null) {
            contratoDao = new Contrato(getCon());
        }
        contratoDao.setCon(getCon());
        return contratoDao;
    }

    public HistoricoProfessorTurmaService getHpService() throws Exception {
        if (hpService == null) {
            hpService = new HistoricoProfessorTurmaServiceImpl(getCon());
        }
        hpService.setCon(getCon());
        return hpService;
    }

    public PresencaInterfaceFacade getPresencaDao() throws Exception {
        if (presencaDao == null) {
            presencaDao = new Presenca(getCon());
        }
        presencaDao.setCon(getCon());
        return presencaDao;
    }

    public AcessoClienteInterfaceFacade getAcessoDao() throws Exception {
        if (acessoDao == null) {
            acessoDao = new AcessoCliente(getCon());
        }
        acessoDao.setCon(getCon());
        return acessoDao;
    }

    public ContratoDuracaoCreditoTreinoInterfaceFacade getContratoDuracaoCreditoDao() throws Exception {
        if (contratoDuracaoCreditoDao == null) {
            contratoDuracaoCreditoDao = new ContratoDuracaoCreditoTreino(getCon());
        }
        contratoDuracaoCreditoDao.setCon(getCon());
        return contratoDuracaoCreditoDao;
    }

    public ConviteAulaExperimentalServiceInterface getConviteAulaExperimentalDao() throws Exception {
        if (conviteAulaExperimentalDao == null) {
            conviteAulaExperimentalDao = new ConviteAulaExperimentalService(getCon());
        }
        return conviteAulaExperimentalDao;
    }

    public ModalidadeInterfaceFacade getModalidadeDao() throws Exception {
        if (modalidadeDao == null) {
            modalidadeDao = new Modalidade(getCon());
        }
        modalidadeDao.setCon(getCon());
        return modalidadeDao;
    }

    public AgendaInterfaceFacade getAgendaDao() throws Exception {
        if (agendaDao == null) {
            agendaDao = new Agenda(getCon());
        }
        agendaDao.setCon(getCon());
        return agendaDao;
    }

    public UsuarioInterfaceFacade getUsuarioDao() throws Exception {
        if (usuarioDao == null) {
            usuarioDao = new Usuario(getCon());
        }
        usuarioDao.setCon(getCon());
        return usuarioDao;
    }

    public ColaboradorInterfaceFacade getColaboradorDao() throws Exception {
        if (colaboradorDao == null) {
            colaboradorDao = new Colaborador(getCon());
        }
        colaboradorDao.setCon(getCon());
        return colaboradorDao;
    }

    private DemandaHorarioTurmaInterfaceFacade getDemandaHorarioTurmaDao() throws Exception {
        if (demandaHorarioTurmaDao == null) {
            demandaHorarioTurmaDao = new DemandaHorarioTurma(getCon());
        }
        demandaHorarioTurmaDao.setCon(getCon());
        return demandaHorarioTurmaDao;
    }

    private ClienteInterfaceFacade getClienteDao() throws Exception {
        if (clienteDao == null) {
            clienteDao = new Cliente(getCon());
        }
        clienteDao.setCon(getCon());
        return clienteDao;
    }

    public AulaConfirmadaInterfaceFacade getAulaConfirmadaDao() throws Exception {
        if (aulaConfirmadaDao == null) {
            aulaConfirmadaDao = new AulaConfirmada(getCon());
        }
        aulaConfirmadaDao.setCon(getCon());
        return aulaConfirmadaDao;
    }

    private PeriodoAcessoClienteInterfaceFacade getPeriodoAcessoDao() throws Exception {
        if (periodoAcessoDao == null) {
            periodoAcessoDao = new PeriodoAcessoCliente(getCon());
        }
        periodoAcessoDao.setCon(getCon());
        return periodoAcessoDao;

    }

    public MovParcelaInterfaceFacade getMovParcelaDao() throws Exception {
        movParcelaDao = new MovParcela(getCon());
        return movParcelaDao;
    }

    public void setTurmaDao(Turma turmaDao) {
        this.turmaDao = turmaDao;
    }

    public ProdutoInterfaceFacade getProdutoDao() throws Exception {
        if (produtoDao == null) {
            produtoDao = new Produto(getCon());
        }
        return produtoDao;
    }

    public AulaAvulsaDiariaInterfaceFacade getAulaAvulsaDiariaDao() throws Exception {
        if (aulaAvulsaDiariaDao == null) {
            aulaAvulsaDiariaDao = new AulaAvulsaDiaria(getCon());
        }
        return aulaAvulsaDiariaDao;
    }

    public TotalpassInterfaceFacade getTotalPassDao() throws Exception {
        if (totalpassDao == null) {
            totalpassDao = new ConfigTotalPass(getCon());
        }
        return totalpassDao;
    }

    public HistoricoContatoInterfaceFacade getHistoricoContatoDao() throws Exception {
        if (historicoContatoDAO == null) {
            historicoContatoDAO = new HistoricoContato(getCon());
        }
        return historicoContatoDAO;
    }


    @Override
    public List<AgendadoJSON> consultarAlunosPorHorarioTurma(Integer empresa,
                                                             Integer matricula, String nome, Boolean somenteAtivos, Integer horarioTurma, Date dia) throws Exception {
        return consultarAlunos(empresa, matricula, nome, null, somenteAtivos, horarioTurma, dia);
    }

    @Override
    public List<AgendadoJSON> consultarAlunos(Integer empresa, Integer matricula,
                                              String nome, Integer modalidade, Boolean somenteAtivos,
                                              Integer horarioTurma, Date dia) throws Exception {
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        JSONArray array = getSwDao().consultarClientesModalidade(empresa,
                matricula, nome, modalidade, somenteAtivos, horarioTurma, dia);
        for (int i = 0; i < array.length(); i++) {
            lista.add(new AgendadoJSON(array.getJSONObject(i)));
        }
        return lista;
    }

    @Override
    public String aulaARepor(Integer cliente, Integer contrato, Integer modalidade) throws Exception {
        String identificador = getAulaDesmarcadaDao().obterIdentificadorAulaDesmarcada(cliente, contrato, modalidade);
        if (identificador != null && !identificador.isEmpty()) {
            return identificador;
        }
        return getMatriculaHorarioDao().obterIdentificadorProximaAulaModalidade(cliente, contrato, modalidade);
    }

    @Override
    public String gravarTurmaAulaCheia(TurmaAulaCheiaJSON json, String key) throws Exception {
        try {
            TurmaVO turma = json.toTurmaVO();
            if (turma.getUsuario() != null) {
                getTurmaDao().incluir(turma);
                gravarFotoAula(turma, json, key);
                String[] horariosSplit = json.getHorarios().split(";");
                List<HorarioTurmaVO> horarios = montarListaExistente(null, horariosSplit, json, turma);
                for (HorarioTurmaVO horarioTurma : horarios) {
                    horarioTurma.setDataEntrouTurma(turma.getDataInicialVigencia());
                    getHorarioDao().incluir(horarioTurma);
                }
                alterarPontuacaoItenCampanhaClubeVantagens(turma, true);
                incluirLogAulaColetivaGravar(turma);
                return "ok";
            } else {
                return "erro";
            }

        } catch (Exception e) {
            return e.getMessage();
        }
    }

    public void incluirLogAulaColetivaGravar(TurmaVO turma) throws Exception {
        getColaboradorDao();
        getModalidadeDao();
        getUsuarioDao();
        UsuarioVO usuarioVO = new UsuarioVO();
        if (turma.getUsuario() != null && turma.getUsuario() != 0) {
            usuarioVO = this.usuarioDao.consultarPorChavePrimaria(turma.getUsuario(), Uteis.NIVELMONTARDADOS_TODOS);
        }
        ColaboradorVO colaboradorVO = this.colaboradorDao.consultarPorChavePrimaria(turma.getProfessor(), Uteis.NIVELMONTARDADOS_TODOS);
        ModalidadeVO modalidadeVO = this.modalidadeDao.consultarPorChavePrimaria(turma.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        String valorLimiteVagasAgregados = turma.getLimiteVagasAgregados() != null ? turma.getLimiteVagasAgregados().toString() : "0";

        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Nome Aula", turma.getDescricao(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Capacidade", turma.getCapacidade().toString(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Limite agregados", valorLimiteVagasAgregados, "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Modalidade", modalidadeVO.getNome(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Dias", turma.getDias(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Horários", turma.getHorarios(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Professor", colaboradorVO.getPessoa().getNome(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Data inicial vigência", turma.getDataInicialVigencia_Apresentar(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Data final vigência", turma.getDataFinalVigencia_Apresentar(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Mensagem", turma.getMensagem(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Controlar check-in", turma.isValidarRestricoesMarcacao() ? "Sim" : "Não", "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Não validar modalidade", turma.isNaoValidarModalidadeContrato() ? "Sim" : "Não", "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Meta de ocupação", turma.getMeta() == null ? "" : turma.getMeta().toString(), "", null, null);
        incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Bonificação", turma.getBonificacao() == null ? "" : turma.getBonificacao().toString(), "", null, null);
        if (!UteisValidacao.emptyNumber(turma.getProdutoGymPass())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Produto Gympass", turma.getProdutoGymPass().toString(), "", null, null);
        }
        if (!UteisValidacao.emptyNumber(turma.getIdClasseGymPass())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Id Class Gympass", turma.getIdClasseGymPass().toString(), "", null, null);
        }
        if (!UteisValidacao.emptyString(turma.getUrlTurmaVirtual())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "INCLUSÃO", "Url turma virtual", turma.getUrlTurmaVirtual().toString(), "", null, null);
        }
    }

    public void incluirLog(String turma, String nomeEntidade, String nomeEntidadeDescricao, UsuarioVO usuarioVO,
                           String op, String campo, String valorNovo, String valorAntigo, Integer pessoa, Integer cliente) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(turma);
        obj.setNomeEntidade(nomeEntidade);
        obj.setNomeEntidadeDescricao(nomeEntidadeDescricao);
        obj.setOperacao(op);
        if (usuarioVO != null) {
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
        }
        obj.setNomeCampo(campo);
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior(valorAntigo);
        obj.setValorCampoAlterado(valorNovo);
        obj.setPessoa(pessoa == null ? 0 : pessoa);
        obj.setCliente(cliente);
        try {
            this.logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void alterarPontuacaoItenCampanhaClubeVantagens(TurmaVO turmaVO, boolean novaTurma) throws Exception {
        ItemCampanhaVO itenAula = new ItemCampanhaVO();
        if (!novaTurma) {
            itenAula = getItenCampanhaDao().consultarPorChaveEstrangeira(turmaVO.getEmpresa().getCodigo(), turmaVO.getCodigo(), TipoItemCampanhaEnum.AULA.getCodigo());
        }
        if (UteisValidacao.emptyNumber(itenAula.getCodigo())) {
            itenAula = new ItemCampanhaVO();
            itenAula.setPontos(turmaVO.getPontosBonus());
            itenAula.setEmpresa(turmaVO.getEmpresa());
            itenAula.setTipoItemCampanha(TipoItemCampanhaEnum.AULA);
            itenAula.setChaveestrangeira(turmaVO.getCodigo());
            getItenCampanhaDao().incluir(itenAula);
        } else {
            itenAula.setPontos(turmaVO.getPontosBonus());
            getItenCampanhaDao().alterar(itenAula);
        }
    }

    public List<HorarioTurmaVO> montarListaExistente(Integer codigoTurma, String[] horariosSplit, TurmaAulaCheiaJSON json, TurmaVO turma) throws Exception {
        Integer semNivel = getTurmaDao().codigoNivelTurma();
        String[] dias = json.getDias().split(";");
        List<HorarioTurmaVO> horarios = codigoTurma == null || codigoTurma == 0
                ? new ArrayList<HorarioTurmaVO>()
                : getHorarioDao().consultarHorarioTurmaComBaseHorarios(codigoTurma, horariosSplit, dias, false);


        for (String dia : dias) {
            DiaSemana diaEnum = DiaSemana.getDiaSemana(dia);
            OUTER:
            for (String horario : horariosSplit) {
                String[] horas = horario.split(" - ");
                for (HorarioTurmaVO hrVO : horarios) {
                    hrVO.setNrMaximoAluno(json.getCapacidade());
                    hrVO.setLimiteVagasAgregados(json.getLimiteVagasAgregados());
                    hrVO.setIdentificadorTurma(json.getNome());
                    hrVO.setAmbiente(new AmbienteVO());
                    hrVO.getAmbiente().setCodigo(json.getAmbiente());
                    hrVO.setProfessor(new ColaboradorVO());
                    hrVO.getProfessor().setCodigo(json.getProfessor());
                    hrVO.setSituacao("AT");
                    hrVO.setToleranciaEntradaMinutos(json.getTolerancia());
                    if (hrVO.getHoraInicial().equals(horas[0])
                            && hrVO.getHoraFinal().equals(horas[1])
                            && hrVO.getDiaSemana().equals(diaEnum.getCodigo())) {
                        continue OUTER;
                    }
                    hrVO.setDataSaiuTurma(null);
                }
                HorarioTurmaVO horarioTurma = new HorarioTurmaVO();
                horarioTurma.setDataEntrouTurma(turma.getDataInicialVigencia());
                horarioTurma.setTurma(turma.getCodigo());
                horarioTurma.setDiaSemana(diaEnum.getCodigo());
                horarioTurma.setDiaSemanaNumero(diaEnum.getNumeral());
                horarioTurma.setHoraInicial(horas[0]);
                horarioTurma.setHoraFinal(horas[1]);
                horarioTurma.setNrMaximoAluno(json.getCapacidade());
                horarioTurma.setLimiteVagasAgregados(json.getLimiteVagasAgregados());
                horarioTurma.setNivelTurma(new NivelTurmaVO());
                horarioTurma.getNivelTurma().setCodigo(semNivel);
                horarioTurma.setIdentificadorTurma(json.getNome());
                horarioTurma.setSituacao("AT");
                horarioTurma.setAmbiente(new AmbienteVO());
                horarioTurma.getAmbiente().setCodigo(json.getAmbiente());
                horarioTurma.setProfessor(new ColaboradorVO());
                horarioTurma.getProfessor().setCodigo(json.getProfessor());
                horarioTurma.setToleranciaEntradaMinutos(json.getTolerancia());
                horarios.add(horarioTurma);
            }
        }

        return horarios;
    }

    @Override
    public String alterarTurmaAulaCheia(TurmaAulaCheiaJSON json, String key) throws Exception {
        try {
            TurmaVO turma = json.toTurmaVO();
            getTurmaDao();
            TurmaVO turmaAntiga = this.turmaDao.consultarPorChavePrimaria(turma.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            String[] horariosSplit = json.getHorarios().split(";");
            String[] dias = json.getDias().split(";");
            turma.setHorarioTurmaVOs(montarListaExistente(json.getCodigo(), horariosSplit, json, turma));
            turma.setHorarioTurmaVOsExclusao(getHorarioDao().consultarHorarioTurmaComBaseHorarios(json.getCodigo(), horariosSplit, dias, true));
            AlunoHorarioTurmaVO dataHorario = getHorarioDao().montarDadosBasicoAlunoHorarioTurmaUltimaDataComAgendamento(turma.getCodigo(), turma.getDataInicialVigencia(), turma.getDataFinalVigencia());
            if(dataHorario != null) {
                preencherDataHorarioSaiuTurma(turma, dataHorario.getData());
            } else {
                preencherDataHorarioSaiuTurma(turma, null);
            }
            getTurmaDao().alterar(turma);
            gravarFotoAula(turma, json, key);
            getTurmaDao().atualizarUsuarioDesativou(turma);
            alterarPontuacaoItenCampanhaClubeVantagens(turma, false);

            List<HorarioTurmaVO> horariosTurma = turma.getHorarioTurmaVOs();
            List<HorarioTurmaVO> horariosExclusao = turma.getHorarioTurmaVOsExclusao();

            if (!UteisValidacao.emptyList(horariosTurma)) {
                Date dataInicialVigencia = turma.getDataInicialVigencia();

                for (HorarioTurmaVO h : horariosTurma) {
                    if (horariosExclusao.isEmpty() || !containsDiaSemana(horariosExclusao, h.getDiaSemana())) {
                        if (h.getDataEntrouTurma() == null || Calendario.menorOuIgual(dataInicialVigencia, h.getDataEntrouTurma())) {
                            h.setDataEntrouTurma(dataInicialVigencia);
                        }
                    } else {
                        h.setDataEntrouTurma(dataInicialVigencia);
                    }
                    getHorarioDao().alterar(h);
                }
            }

            incluirLogAulaColetivaAlterar(turma, turmaAntiga);
            return "ok";
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private boolean containsDiaSemana(List<HorarioTurmaVO> horarios, String diaSemana) {
        for (HorarioTurmaVO h : horarios) {
            if (h.getDiaSemana().equals(diaSemana)) {
                return true;
            }
        }
        return false;
    }

    private void gravarFotoAula(TurmaVO turma, TurmaAulaCheiaJSON json, String key) throws Exception {
        String fotoKeyAnterior = getTurmaDao().obterFotoKeyTurma(turma.getCodigo());
        boolean manterFotoAnterior = json != null && json.isManterFotoAnterior();

        if (json.getImage() != null) {
            String novaFotoKey = MidiaService.getInstance().uploadObjectFromByteArray(key,
                    MidiaEntidadeEnum.FOTO_AULA,
                    turma.getCodigo().toString() + ";" + UUID.randomUUID(),
                    json.getImage());

            turma.setFotokey(novaFotoKey + "?time=" + System.currentTimeMillis());
            getTurmaDao().salvarFotoKeyTurma(turma.getCodigo(), turma.getFotokey());

            if (isNotBlank(fotoKeyAnterior) && !fotoKeyAnterior.equals(novaFotoKey)) {
                MidiaService.getInstance().deleteObject(fotoKeyAnterior);
            }
        } else {
            if(manterFotoAnterior && isNotBlank(fotoKeyAnterior)){

            }
            if(!manterFotoAnterior && isNotBlank(fotoKeyAnterior)) {
                MidiaService.getInstance().deleteObject(fotoKeyAnterior);
                getTurmaDao().salvarFotoKeyTurma(turma.getCodigo(), "");
            }
        }
    }


    public void incluirLogAulaColetivaAlterar(TurmaVO turma, TurmaVO turmaAntiga) throws Exception {
        getUsuarioDao();
        getModalidadeDao();
        UsuarioVO usuarioVO = new UsuarioVO();
        if (turma.getUsuario() != null && turma.getUsuario() != 0) {
            usuarioVO = this.usuarioDao.consultarPorChavePrimaria(turma.getUsuario(), Uteis.NIVELMONTARDADOS_TODOS);
        }
        ModalidadeVO modalidadeVO = this.modalidadeDao.consultarPorChavePrimaria(turma.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        ModalidadeVO modalidadeVOAntiga = this.modalidadeDao.consultarPorChavePrimaria(turmaAntiga.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if (!turma.getDescricao().equals(turmaAntiga.getDescricao())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Nome aula", turma.getDescricao(), turmaAntiga.getDescricao(), null, null);
        }
        if (turma.getCapacidade() != turmaAntiga.getCapacidade()) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Capacidade", turma.getCapacidade().toString(), turmaAntiga.getCapacidade().toString(), null, null);
        }
        if (turma.getLimiteVagasAgregados() != turmaAntiga.getLimiteVagasAgregados()) {
            String valorAntes = turmaAntiga.getLimiteVagasAgregados() == null ? "" : turmaAntiga.getLimiteVagasAgregados().toString();
            String valorDepois = turma.getLimiteVagasAgregados() == null ? "" : turma.getLimiteVagasAgregados().toString();
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Limite agregados", valorDepois, valorAntes, null, null);
        }
        if (turma.getProfessor() != turmaAntiga.getProfessor()) {
            getColaboradorDao();
            ColaboradorVO professorNovo = this.colaboradorDao.consultarPorChavePrimaria(turma.getProfessor(), Uteis.NIVELMONTARDADOS_TODOS);
            ColaboradorVO professorAntigo = this.colaboradorDao.consultarPorChavePrimaria(turmaAntiga.getProfessor(), Uteis.NIVELMONTARDADOS_TODOS);
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Professor", professorNovo.getPessoa().getNome(), professorAntigo.getPessoa().getNome(), null, null);
        }
        if (!modalidadeVO.getNome().equals(modalidadeVOAntiga.getNome())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Modalidade", modalidadeVO.getNome(), modalidadeVOAntiga.getNome(), null, null);
        }
        if (!turma.getDias().equals(turmaAntiga.getDias())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Dias", turma.getDias(), turmaAntiga.getDias(), null, null);
        }
        if (!turma.getDataInicialVigencia_Apresentar().equals(turmaAntiga.getDataInicialVigencia_Apresentar())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Data inicial vigência", turma.getDataInicialVigencia_Apresentar(), turmaAntiga.getDataInicialVigencia_Apresentar(), null, null);
        }
        if (!turma.getDataFinalVigencia_Apresentar().equals(turmaAntiga.getDataFinalVigencia_Apresentar())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Data final vigência", turma.getDataFinalVigencia_Apresentar(), turmaAntiga.getDataFinalVigencia_Apresentar(), null, null);
        }

        String[] horariosNovo = turma.getHorarios().split(";");
        String[] horariosAntigo = turmaAntiga.getHorarios().split(";");
        Boolean existeHorairo = false;

        for (int i = 0; horariosNovo.length > i; i++) {
            existeHorairo = false;
            for (int j = 0; horariosAntigo.length > j; j++) {
                if (horariosNovo[i].equals(horariosAntigo[j])) {
                    existeHorairo = true;
                }
            }
            if (!existeHorairo) {
                incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Horário novo", horariosNovo[i], "", null, null);
            }
        }

        for (int i = 0; horariosAntigo.length > i; i++) {
            existeHorairo = false;
            for (int j = 0; horariosNovo.length > j; j++) {
                if (horariosAntigo[i].equals(horariosNovo[j])) {
                    existeHorairo = true;
                }
            }
            if (!existeHorairo) {
                incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Horário antigo", "", horariosAntigo[i], null, null);
            }
        }

        if (!turma.getMensagem().equals(turmaAntiga.getMensagem())) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Mensagem",
                    turma.getMensagem(), turmaAntiga.getMensagem(), null, null);
        }

        if (turma.isValidarRestricoesMarcacao() != turmaAntiga.isValidarRestricoesMarcacao()) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Controlar check-in",
                    turma.isValidarRestricoesMarcacao() ? "Sim" : "Não",
                    turmaAntiga.isValidarRestricoesMarcacao() ? "Sim" : "Não", null, null);
        }

        if (turma.isNaoValidarModalidadeContrato() != turmaAntiga.isNaoValidarModalidadeContrato()) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", "Não validar modalidade",
                    turma.isNaoValidarModalidadeContrato() ? "Sim" : "Não",
                    turmaAntiga.isNaoValidarModalidadeContrato() ? "Sim" : "Não", null, null);
        }
        logCampoString(turma, turmaAntiga, usuarioVO,
                UteisValidacao.emptyNumber(turma.getMeta()) ? "" : turma.getMeta().toString(),
                UteisValidacao.emptyNumber(turmaAntiga.getMeta()) ? "" : turmaAntiga.getMeta().toString(),
                "Meta de ocupação");

        logCampoString(turma, turmaAntiga, usuarioVO,
                UteisValidacao.emptyNumber(turma.getBonificacao()) ? "" : turma.getBonificacao().toString(),
                UteisValidacao.emptyNumber(turmaAntiga.getBonificacao()) ? "" : turmaAntiga.getBonificacao().toString(),
                "Bonificação");

        logCampoString(turma, turmaAntiga, usuarioVO,
                UteisValidacao.emptyNumber(turma.getProdutoGymPass()) ? "" : turma.getProdutoGymPass().toString(),
                UteisValidacao.emptyNumber(turmaAntiga.getProdutoGymPass()) ? "" : turmaAntiga.getProdutoGymPass().toString(),
                "Produto Gympass");

        logCampoString(turma, turmaAntiga, usuarioVO,
                UteisValidacao.emptyNumber(turma.getIdClasseGymPass()) ? "" : turma.getIdClasseGymPass().toString(),
                UteisValidacao.emptyNumber(turmaAntiga.getIdClasseGymPass()) ? "" : turmaAntiga.getIdClasseGymPass().toString(),
                "Id Class Gympass");

        logCampoString(turma, turmaAntiga, usuarioVO,
                turma.getUrlTurmaVirtual() == null ? "" : turma.getUrlTurmaVirtual(),
                turmaAntiga.getUrlTurmaVirtual() == null ? "" : turmaAntiga.getUrlTurmaVirtual(),
                "Url turma virtual");

    }


    private void logCampoString(TurmaVO turma, TurmaVO turmaAntiga, UsuarioVO usuarioVO, String nova, String antiga, String descricao) {
        if (!antiga.equals(nova)) {
            incluirLog(turma.getCodigo().toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "ALTERAÇÃO", descricao,
                    nova,
                    antiga, null, null);
        }
    }

    private void preencherDataHorarioSaiuTurma(TurmaVO turma, Date dataHorario) throws Exception {
        List<HorarioTurmaVO> horariosOld = getHorarioDao().consultarHorarioTurmas(turma.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (HorarioTurmaVO horarioOld : horariosOld) {
            boolean contemHoraro = false;
            for (HorarioTurmaVO horarioAtual : turma.getHorarioTurmaVOs()) {
                if (horarioOld.getCodigo().equals(horarioAtual.getCodigo())) {
                    contemHoraro = true;
                }
            }
            if (!contemHoraro) {
                for (Object obj : turma.getHorarioTurmaVOsExclusao()) {
                    HorarioTurmaVO horarioSaiu = (HorarioTurmaVO) obj;
                    if (horarioOld.getCodigo().equals(horarioSaiu.getCodigo()) && horarioSaiu.getDataSaiuTurma() == null) {
                        if(dataHorario != null
                                && Calendario.maior(dataHorario, Calendario.hoje())) {
                            horarioSaiu.setDataSaiuTurma(dataHorario);
                        } else {
                            horarioSaiu.setDataSaiuTurma(Calendario.hoje());
                        }
                    }
                }
            }
        }
    }

    public List<TurmaAulaCheiaJSON> obterAulasColetivas(Integer empresa, ListaPaginadaTO paginacao, JSONObject filtros) throws Exception {
        return getTurmaDao().obterAulasColetivasPaginada(empresa, paginacao, filtros);
    }

    public JSONArray logAula(Integer empresa, ListaPaginadaTO paginacao, JSONObject filtros) throws Exception {
        JSONArray logs = new JSONArray();
        String idAula = filtros.getString("idAula");
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select codigo, responsavelalteracao, nomecampo, valorcampoanterior, " +
                " valorcampoalterado, dataalteracao, nomeentidadedescricao, chaveprimaria " +
                " from log where nomeentidade = 'ALUNO_AULA_COLETIVA' " +
                " order by dataalteracao desc", con);

        while (resultSet.next()) {
            String chavePrimaria = resultSet.getString("chaveprimaria");
            String nomeEntidadeDescricao = resultSet.getString("nomeentidadedescricao");

            if (chavePrimaria.contains(idAula) ||
                    nomeEntidadeDescricao.equals("Movimentação na Fila de Espera") ||
                    nomeEntidadeDescricao.equals("Aluno fixado em aula") ||
                    nomeEntidadeDescricao.equals("Aluno desafixado em aula")) {
                JSONObject json = new JSONObject();
                json.put("codigo", resultSet.getInt("codigo"));
                json.put("responsavelalteracao", resultSet.getString("responsavelalteracao"));
                json.put("nomecampo", resultSet.getString("nomecampo"));

                json.put("valorcampoanterior", resultSet.getString("valorcampoanterior") != null ? resultSet.getString("valorcampoanterior") : "");
                json.put("valorcampoalterado", resultSet.getString("valorcampoalterado") != null ? resultSet.getString("valorcampoalterado") : "");
                json.put("dataalteracao", resultSet.getTimestamp("dataalteracao").getTime());
                json.put("nomeentidadedescricao", nomeEntidadeDescricao);
                logs.put(json);
            }
        }
        return logs;
    }

    public JSONArray logAgendaAulas(Integer empresa, ListaPaginadaTO paginacao, JSONObject filtros) throws Exception {
        JSONArray logs = new JSONArray();
        String idAula = filtros.optString("idAula");
        String quicksearchValue = filtros.optString("quicksearchValue");
        StringBuilder sql = new StringBuilder();
        sql.append(" select date_trunc('minute', dataalteracao) as dtalteracao, \n");
        sql.append(" chaveprimaria, responsavelalteracao, operacao, t.descricao as turma, min(dataalteracao) as hora from log l \n");
        sql.append(" left join turma t on l.nomeentidade = 'AULA_COLETIVA' and t.codigo = cast(l.chaveprimaria as integer) \n");
        sql.append(" where l.nomeentidade = 'AULA_COLETIVA' \n");
        Integer codigoPesquisa = null;
        try {
            codigoPesquisa = Integer.valueOf(quicksearchValue);
        } catch (Exception e) {
        }
        if (UteisValidacao.notEmptyNumber(codigoPesquisa)) {
            sql.append(" and chaveprimaria = '").append(codigoPesquisa).append("' \n");
        } else if (!UteisValidacao.emptyString(quicksearchValue)) {
            sql.append(" and (");
            sql.append(" UPPER(responsavelalteracao) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            sql.append(" or UPPER(t.descricao) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            sql.append(")\n");
        }
        Long inicio = filtros.optLong("dataInicio");
        Long fim = filtros.optLong("dataFim");
        if (inicio != null && inicio > 0l) {
            sql.append(" AND l.dataalteracao >= '").append(Uteis.getData(Calendario.getDataComHoraZerada(new Date(inicio)), "yyyy-MM-dd")).append("' \n");
        }
        if (fim != null && fim > 0l) {
            sql.append(" AND l.dataalteracao <= '").append(Calendario.fimDoDia(new Date(fim))).append("' \n");
        }

        JSONArray tipos = filtros.optJSONArray("tipo");
        if (tipos != null) {
            String tipoStr = "";
            for (int i = 0; i < tipos.length(); i++) {
                String t = tipos.getString(i);
                switch (t) {
                    case "INSERT":
                        tipoStr += ",'INCLUSÃO'";
                        break;
                    case "DELETE":
                        tipoStr += ",'EXCLUSÃO'";
                        break;
                    case "UPDATE":
                        tipoStr += ",'ALTERAÇÃO'";
                        break;
                }
            }
            sql.append(" and l.operacao in (").append(tipoStr.replaceFirst(",", "")).append(")\n");
        }
        if (!UteisValidacao.emptyString(idAula)) {
            sql.append(" and chaveprimaria = '").append(idAula).append("' \n");
        }
        sql.append(" group by 1,2,3,4,5 \n");
        sql.append(" order by 1 desc \n");

        ResultSet rsCount = SuperFacadeJDBC.criarConsulta("select count(chaveprimaria) as cont from (" + sql.toString() + ") as t ", con);
        paginacao.setCount(rsCount.next() ? rsCount.getInt("cont") : 0);

        sql.append(" LIMIT " + paginacao.getLimit());
        sql.append(" OFFSET " + paginacao.getOffset());

        ResultSet rsAula = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        while (rsAula.next()) {
            JSONObject log = new JSONObject();
            log.put("codigo", rsAula.getInt("chaveprimaria"));
            log.put("descricao", rsAula.getString("turma"));
            log.put("username", rsAula.getString("responsavelalteracao"));
            log.put("operacao", rsAula.getString("operacao"));
            log.put("dataalteracao", rsAula.getTimestamp("hora").getTime());

            ResultSet rsAlteracoes = SuperFacadeJDBC.criarConsulta("Select nomecampo, valorcampoanterior, " +
                    " valorcampoalterado  " +
                    " from log where nomeentidade = 'AULA_COLETIVA' " +
                    " and date_trunc('minute', dataalteracao) = '" + rsAula.getString("dtalteracao") + "' " +
                    " and responsavelalteracao = '" + rsAula.getString("responsavelalteracao") + "' " +
                    " and operacao = '" + rsAula.getString("operacao") + "' " +
                    " and chaveprimaria = '" + rsAula.getString("chaveprimaria") + "' ", con);

            JSONArray jsonAulas = new JSONArray();
            while (rsAlteracoes.next()) {
                JSONObject json = new JSONObject();
                json.put("nomecampo", rsAlteracoes.getString("nomecampo"));
                json.put("valorcampoanterior", rsAlteracoes.getString("valorcampoanterior"));
                json.put("valorcampoalterado", rsAlteracoes.getString("valorcampoalterado"));
                jsonAulas.put(json);
            }
            log.put("alteracoes", jsonAulas);
            logs.put(log);
        }
        return logs;
    }


    public List<TurmaAulaCheiaJSON> obterAulasColetivas(Integer empresa) throws Exception {
        return getTurmaDao().obterAulasColetivas(empresa);
    }

    @Override
    public List<AgendadoJSON> obterPresencas(Integer empresa) throws Exception {
        return getPresencaDao().consultarPresencasTVGestor(Uteis.obterPrimeiroDiaMes(Calendario.hoje()),
                Calendario.hoje(),
                empresa);
    }

    @Override
    public JSONArray obterUltimosAcessos(Integer empresa) throws Exception {
        return getAcessoDao().obterUltimosAcessos(empresa, null);
    }

    @Override
    public List<AgendaTotalJSON> consultarProximasAulas(Integer matricula, boolean proximos30dias) throws Exception {
        Integer saldo = 0;
        try {
            String consultarSaldoAluno = consultarSaldoAluno(matricula, false, null);
            String[] split = consultarSaldoAluno.split(";");
            saldo = Integer.valueOf(split[0]);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }

        verificarSituacaoAluno(matricula, null);

        Integer codigoCliente;
        Date agora;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT empresa, codigo FROM cliente WHERE codigomatricula = " + matricula, con)) {
            codigoCliente = null;
            agora = Calendario.hoje();
            if (rs.next()) {
                codigoCliente = rs.getInt("codigo");
                TimeZone tz = TimeZone.getTimeZone(getEmpresaDao().obterTimeZoneDefault(rs.getInt("empresa")));
                agora = Calendario.hojeCalendar(tz).getTime();
            }
        }
        Map<Date, List<Integer>> mapaAgendamentosDesmarcados = getAulaDesmarcadaDao().consultarAgendamentosDesmarcados(codigoCliente);
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        aulas.addAll(getReposicaoDao().consultarProximasReposicoes(matricula, agora, false, null, null));
        if (proximos30dias) {
            aulas.addAll(getMatriculaHorarioDao().consultarAulasProximos30dias(matricula, agora, mapaAgendamentosDesmarcados, (saldo + aulas.size())));
        } else {
            aulas.addAll(getMatriculaHorarioDao().consultarProximasAulasModalidadesDiferentes(matricula, mapaAgendamentosDesmarcados));
        }
        return Ordenacao.ordenarLista(aulas, "inicio");
    }

    @Override
    public List<AgendaTotalJSON> consultarProximasAulaCheia(Integer matricula) throws Exception {
        return getMatriculaHorarioDao().consultarProximasAulasAulaCheia(matricula);
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasDesmarcadasSemReposicao(Integer matricula, Date inicio, Date fim, Integer modalidade) throws Exception {
        return getMatriculaHorarioDao().consultarAulasDesmarcadasSemReposicao(matricula, inicio, fim, modalidade);
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasDiaAluno(Integer matricula, Date dia, Integer modalidade) throws Exception {
        Integer tipoModalidade = 0;
        if (UteisValidacao.notEmptyNumber(modalidade)) {
            ModalidadeVO modalidadeVO = getModalidadeDao().consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UtilReflection.objetoMaiorQueZero(modalidadeVO, "getCodigo()")) {
                tipoModalidade = modalidadeVO.getTipo();
            } else {
                throw new Exception("Não existe modalidade de código " + modalidade);
            }
            if (!getContratoModalidadeDao().existeContratoAtivoComEssaModalidade(matricula, dia, modalidade, tipoModalidade)) {
                throw new Exception("SemModalidadeNoContrato");
            }
        }
        List<AgendadoJSON> reposicoes = getReposicaoDao().consultarReposicoesParaAgenda(dia, dia, null, matricula, true, modalidade, tipoModalidade);
        List<AgendaTotalJSON> reposicoesMarcadas = getReposicaoDao().consultarProximasReposicoes(matricula, dia, true, modalidade, tipoModalidade);
        List<AgendaTotalJSON> aulasDia = getMatriculaHorarioDao().consultarAulasDia(matricula, dia, reposicoes, modalidade, tipoModalidade);
        aulasDia.addAll(reposicoesMarcadas);
        return aulasDia;
    }

    public JSONObject obterContratoVigentePorPessoaSintetico(Integer codigoPessoa, Date dataAtual) throws Exception {
        ContratoVO contrato = getContratoDao().consultarContratoVigentePorPessoaSintetico(codigoPessoa, dataAtual);
        if (contrato.getSituacao() != null) {
            return new JSONObject(contrato);
        } else {
            throw new Exception("Não existe contrato vigente nesta data");
        }
    }

    @Override
    public List<ControleCreditoTreinoJSON> consultarExtratoCreditosAluno(Integer matricula, Date dataLimite) throws Exception {

        if (dataLimite != null) {
            dataLimite = Uteis.getDataComUltimaHora(dataLimite);
        }

        List<ControleCreditoTreinoJSON> extrato = new ArrayList<ControleCreditoTreinoJSON>();
        Integer saldoAteData = 0;
        if (dataLimite != null) {
            try (ResultSet rsSaldoAteData = SuperFacadeJDBC.criarConsulta("SELECT coalesce(SUM(quantidade),0) as saldo from controlecreditotreino cc\n"
                    + " INNER JOIN situacaoclientesinteticodw sc ON sc.codigocontrato = cc.contrato "
                    + " AND sc.matricula = " + matricula + " AND cc.datalancamento < '"
                    + Uteis.getDataAplicandoFormatacao(dataLimite, "yyyy-MM-dd HH:mm:ss")
                    + "' ", con)) {
                saldoAteData = rsSaldoAteData.next() ? rsSaldoAteData.getInt("saldo") : 0;
            }
        }
        List<ControleCreditoTreinoVO> lista = getControleCreditoDao().consultarSimples(matricula, dataLimite, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (int i = 0; i < lista.size(); i++) {
            ControleCreditoTreinoVO obj = lista.get(i);
            if (i == 0) {
                obj.setSaldo(saldoAteData + obj.getQuantidade());
            } else {
                ControleCreditoTreinoVO objAnterior = lista.get(i - 1);
                obj.setSaldo(objAnterior.getSaldo() + obj.getQuantidade());
            }
            extrato.add(new ControleCreditoTreinoJSON(obj));
        }
        return extrato;
    }

    @Override
    public List<AgendaTotalJSON> consultarParaAgendaModalidadesAluno(Date inicio, Date fim, Integer matricula, boolean isApp) throws Exception {

        verificarSituacaoAluno(matricula, null);

        Integer empresa = null;
        Set<Integer> modalidades = new HashSet<>();
        Date agora;
        try (ResultSet rsempresa = SuperFacadeJDBC.criarConsulta("SELECT empresa FROM cliente WHERE codigomatricula = " + matricula, con)) {
            agora = Calendario.hoje();
            if (rsempresa.next()) {
                TimeZone tz = TimeZone.getTimeZone(getEmpresaDao().obterTimeZoneDefault(rsempresa.getInt("empresa")));
                agora = Calendario.hojeCalendar(tz).getTime();
            }
        }

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select tipohorario, modalidade, cli.empresa from contratomodalidade cm\n"
                + " INNER JOIN contrato c ON c.codigo = cm.contrato AND c.situacao = 'AT'\n"
                + " INNER JOIN cliente cli ON cli.pessoa = c.pessoa AND cli.codigomatricula = " + matricula
                + " INNER JOIN contratoduracao cd ON cd.contrato = c.codigo \n"
                + " LEFT JOIN contratoduracaocreditotreino cdc ON cd.codigo = cdc.contratoduracao \n"
                + "inner join modalidade m on m.codigo = cm.modalidade \n"
                + "where m.utilizarturma is true ", con)) {
            while (rs.next()) {
                empresa = rs.getInt("empresa");
                modalidades.add(rs.getInt("modalidade"));
            }
        }

        try (ResultSet rsModaDes = SuperFacadeJDBC.criarConsulta("SELECT turma.modalidade FROM auladesmarcada a \n"
                + " INNER JOIN contrato con ON a.contrato = con.codigo AND con.situacao = 'AT'\n"
                + " INNER JOIN cliente cli ON con.pessoa = cli.pessoa\n"
                + " INNER JOIN turma ON turma.codigo = a.turma"
                + " inner join modalidade m on m.codigo = turma.modalidade \n"
                + " WHERE cli.codigomatricula = " + matricula
                + " and datareposicao is null and  permiteReporAulaDesmarcada = true "
                + "and m.utilizarturma is true "
                + "and con.datarematricularealizada is not null and con.datarematricularealizada >= CURRENT_DATE "
                + "order by dataorigem ", con)) {
            while (rsModaDes.next()) {
                modalidades.add(rsModaDes.getInt("modalidade"));
            }
        }

        Map<String, Date> mapaMatriculas = obterMapaMatriculas(matricula);
        Map<String, List<Date>> mapaReposicoes = obterMapaReposicoes(matricula, null, Calendario.hoje());
        List<AgendaTotalJSON> listaAgenda = consultarParaAgenda(inicio, fim, null, new ArrayList<>(modalidades), empresa, false, agora,
                mapaMatriculas, mapaReposicoes, matricula, isApp);
        List<AgendaTotalJSON> aulasDesmarcadasSemReposicao = consultarAulasDesmarcadasSemReposicao(matricula, inicio, fim, null);
        for (AgendaTotalJSON aulaDesmarcadaSemReposicao : aulasDesmarcadasSemReposicao) {
            boolean utilizarTurma = verificarModalidade(aulaDesmarcadaSemReposicao.getCodigoTipo());
            boolean existe = false;
            for (AgendaTotalJSON aulaMatriculada : listaAgenda) {
                if (aulaMatriculada.getCodDia().equals(aulaDesmarcadaSemReposicao.getCodDia())) {
                    existe = true;
                }
            }
            if (!existe && utilizarTurma) {
                listaAgenda.add(aulaDesmarcadaSemReposicao);
            }
        }
        return listaAgenda;
    }

    private Boolean verificarModalidade(Integer codigoModalidade) {
        Boolean utilizarTurma = false;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT utilizarTurma FROM Modalidade WHERE codigo = ").append(codigoModalidade);
        return utilizarTurma;
    }

    private Map<String, List<Date>> obterMapaReposicoes(Integer matricula, Integer codigoCliente, Date data) throws Exception {
        Map<String, List<Date>> mapa = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select rp.horarioturma, rp.datareposicao, h.horainicial ,h.horafinal from reposicao rp\n");
        sql.append("INNER JOIN situacaoclientesinteticodw sw ON sw.codigocontrato = rp.contrato \n");
        sql.append("inner join horarioturma h on h.codigo = rp.horarioturma \n");
        if (data != null) {
            sql.append(" AND datareposicao >= '").append(Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd")).append("'\n");
        }
        if (codigoCliente == null) {
            sql.append(" AND sw.matricula = ").append(matricula);
        } else {
            sql.append(" AND sw.codigoCliente = ").append(codigoCliente);
        }

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                List<Date> get = mapa.get(rs.getString("horarioturma"));
                if (get == null) {
                    get = new ArrayList<Date>();
                    mapa.put(rs.getString("horarioturma"), get);
                }
                Date dataReposicao = rs.getDate("datareposicao");
                String horarioInicial = rs.getString("horainicial");
                String[] inicialHoraMinuto = horarioInicial.split(":");
                String horaInicial = inicialHoraMinuto[0];
                String minutiInicial = inicialHoraMinuto[1];
                dataReposicao = Calendario.getDataComHoraZerada(dataReposicao);
                dataReposicao.setHours(Integer.parseInt(horaInicial));
                dataReposicao.setMinutes(Integer.parseInt(minutiInicial));
                get.add(dataReposicao);
            }
        }
        return mapa;
    }

    private Map<String, List<Date>> obterMapaDesmarcacoes(Integer codigoCliente) throws Exception {
        return obterMapaDesmarcacoes(codigoCliente, null, null, null);
    }

    private Map<String, List<Date>> obterMapaDesmarcacoes(Integer codigoCliente, Integer matricula, Date inicio, Date fim) throws Exception {
        Map<String, List<Date>> mapa = new HashMap<String, List<Date>>();
        StringBuilder sql = new StringBuilder();
        sql.append("select horarioturma, dataorigem from auladesmarcada a\n");
        sql.append("INNER JOIN situacaoclientesinteticodw sw ON sw.codigocontrato = a.contrato \n");
        if (matricula == null) {
            sql.append("AND sw.codigoCliente = ").append(codigoCliente);
        } else {
            sql.append("AND sw.matricula = ").append(matricula);
            sql.append("AND a.dataorigem BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' AND '");
            sql.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' ");
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                List<Date> get = mapa.get(rs.getString("horarioturma"));
                if (get == null) {
                    get = new ArrayList<Date>();
                    mapa.put(rs.getString("horarioturma"), get);
                }
                get.add(rs.getDate("dataorigem"));
            }
        }
        return mapa;
    }

    private Map<String, Date> obterMapaMatriculas(Integer matricula) throws Exception {
        Map<String, Date> mapa = new HashMap<String, Date>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT rp.horarioturma, datafim FROM matriculaalunohorarioturma rp\n" +
                "INNER JOIN contrato con ON rp.contrato = con.codigo AND con.situacao = 'AT'\n" +
                "INNER JOIN cliente cli ON con.pessoa = cli.pessoa \n" +
                "WHERE cli.codigomatricula = " + matricula + "\n" +
                "ORDER BY datafim ", con)) {
            while (rs.next()) {
                mapa.put(rs.getString("horarioturma"), rs.getDate("datafim"));
            }
        }
        return mapa;
    }

    private ConviteAulaExperimentalVO consultarConviteAulaExperimental(Integer codigoConvite) throws Exception {
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = null;
        if ((codigoConvite == null) || (codigoConvite <= 0)) {
            throw new ConsistirException("O parâmetro codigoConvite dever ser informado.");
        }
        conviteAulaExperimentalVO = getConviteAulaExperimentalDao().consultarPorCodigo(codigoConvite, Uteis.NIVELMONTARDADOS_TODOS);
        if (!UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getCodigo()")) {
            throw new ConsistirException("Operação não permitida. O convite foi excluído.");
        }
        if (Calendario.maior(Calendario.getDataComHoraZerada(Calendario.hoje()), Calendario.getDataComHoraZerada(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getVigenciaFinal()))) {
            throw new ConsistirException("Operação não permitida. O convite já expirou.");
        }
        return conviteAulaExperimentalVO;
    }

    public List<AgendaTotalJSON> consultarAulaExperimentalAgendada(TipoTurmaEnum tipoTurmaEnum, Integer codigoConvite) throws Exception {
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = consultarConviteAulaExperimental(codigoConvite);
        List<AgendaTotalJSON> listaRetornar = new ArrayList<AgendaTotalJSON>();
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getCodigo()")) {
            List<AgendaVO> listaAgenda = getAgendaDao().consultarPorConviteAulaExperimental(tipoTurmaEnum, conviteAulaExperimentalVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            AgendaTotalJSON agendaTotalJSON;
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
            for (AgendaVO agendaVO : listaAgenda) {
                agendaTotalJSON = new AgendaTotalJSON();
                agendaTotalJSON.setInicio(sdf.format(agendaVO.getDataAgendamentoComHora()));
                agendaTotalJSON.setTitulo(agendaVO.getModalidade().getNome());
                agendaTotalJSON.setTipo(agendaVO.getModalidade().getNome());
                listaRetornar.add(agendaTotalJSON);
            }
        }
        return listaRetornar;
    }


    @Override
    public List<AgendaTotalJSON> consultarTurmasParaAgendarAulaExperimental(TipoTurmaEnum tipoTurmaEnum, Integer codigoConvite, Date inicio, Date fim) throws Exception {
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = consultarConviteAulaExperimental(codigoConvite);
        List<Integer> modalidades = new ArrayList<Integer>();
        List<AgendaTotalJSON> lista = null;
        List<ModalidadeVO> listaModalidadeComTurmaAtiva = getModalidadeDao().consultarModalidadeAtivaComTurma(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo(), tipoTurmaEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getListaModalidade().size() > 0) {
            for (TipoConviteAulaExperimentalModalidadeVO conviteModalidade : conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getListaModalidade()) {
                for (ModalidadeVO modalidadeVO : listaModalidadeComTurmaAtiva) {
                    if (conviteModalidade.getModalidadeVO().getCodigo().equals(modalidadeVO.getCodigo())) {
                        modalidades.add(conviteModalidade.getModalidadeVO().getCodigo());
                    }
                }
            }
            lista = consultarParaAgenda(inicio, fim, null, modalidades, conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo(), false);
            lista = restringirModalidadeHorarioAulaExperimental(conviteAulaExperimentalVO, lista);
        } else {
            for (ModalidadeVO modalidadeVO : listaModalidadeComTurmaAtiva) {
                modalidades.add(modalidadeVO.getCodigo());
            }
            lista = consultarParaAgenda(inicio, fim, null, modalidades, conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo(), false);
        }
        List<AgendaTotalJSON> listaAux = new ArrayList<AgendaTotalJSON>();
        if ((modalidades.size() <= 0) && (tipoTurmaEnum != TipoTurmaEnum.TODOS)) {
            for (AgendaTotalJSON agendaTotalJSON : lista) {
                if ((tipoTurmaEnum == TipoTurmaEnum.AULA_CHEIA) && (agendaTotalJSON.getAulaCheia())) {
                    listaAux.add(agendaTotalJSON);
                } else if ((tipoTurmaEnum == TipoTurmaEnum.TURMA_ZW) && (!agendaTotalJSON.getAulaCheia())) {
                    listaAux.add(agendaTotalJSON);
                }
            }
        }
        if (modalidades.size() <= 0) {
            lista.clear();
            lista.addAll(listaAux);
        }
        return lista;
    }

    private List<AgendaTotalJSON> restringirModalidadeHorarioAulaExperimental(ConviteAulaExperimentalVO conviteAulaExperimentalVO, List<AgendaTotalJSON> listaAgenda) throws Exception {
        List<AgendaTotalJSON> listaRetornar = new ArrayList<AgendaTotalJSON>();
        for (AgendaTotalJSON agendaTotalJSON : listaAgenda) {
            for (TipoConviteAulaExperimentalModalidadeVO conviteModalidade : conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getListaModalidade()) {
                if (agendaTotalJSON.getCodigoTipo().equals(conviteModalidade.getModalidadeVO().getCodigo())) {
                    if ((conviteModalidade.getListaHorarioAux() != null) && (conviteModalidade.getListaHorarioAux().size() > 0)) {
                        for (TipoConviteAulaExperimentalModalidadeHorarioVO horario : conviteModalidade.getListaHorarioAux()) {
                            if ((horario.getDiasSemana() != null) && (!horario.getDiasSemana().trim().equals(""))) {
                                // validar dia da semana e horario
                                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                                int diaSemana = Calendario.getDiaSemana(sdf.parse(agendaTotalJSON.getInicio()));
                                String[] diasSemana = horario.getDiasSemana().split(",");
                                for (String dia : diasSemana) {
                                    if (Integer.parseInt(dia) == diaSemana) {
                                        if (validarHorarioAulaExperimental(agendaTotalJSON, horario)) {
                                            listaRetornar.add(agendaTotalJSON);
                                        }
                                    }
                                }
                            } else {
                                // validar horario
                                if (validarHorarioAulaExperimental(agendaTotalJSON, horario)) {
                                    listaRetornar.add(agendaTotalJSON);
                                }
                            }
                        }
                    } else {
                        listaRetornar.add(agendaTotalJSON);
                    }
                }
            }
        }
        return listaRetornar;
    }

    private boolean validarHorarioAulaExperimental(AgendaTotalJSON agendaTotalJSON, TipoConviteAulaExperimentalModalidadeHorarioVO horario) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        Date dataAula = sdf.parse(agendaTotalJSON.getInicio());
        Calendar horaInicialPermitida = Calendario.getInstance();
        horaInicialPermitida.setTime(dataAula);
        horaInicialPermitida.set(Calendar.HOUR_OF_DAY, Integer.parseInt(horario.getHoraInicial().split(":")[0]));
        horaInicialPermitida.set(Calendar.MINUTE, Integer.parseInt(horario.getHoraInicial().split(":")[1]));

        Calendar horaFinalPermitida = Calendario.getInstance();
        horaFinalPermitida.setTime(dataAula);
        horaFinalPermitida.set(Calendar.HOUR_OF_DAY, Integer.parseInt(horario.getHoraFinal().split(":")[0]));
        horaFinalPermitida.set(Calendar.MINUTE, Integer.parseInt(horario.getHoraFinal().split(":")[1]));

        return ((Calendario.maiorOuIgualComHora(dataAula, horaInicialPermitida.getTime()))
                && (Calendario.menorOuIgualComHora(dataAula, horaFinalPermitida.getTime())));

    }

    @Override
    public List<AgendaTotalJSON> consultarTurmasAluno(Integer matricula) throws Exception {
        return getMatriculaHorarioDao().consultarTurmasAluno(matricula);
    }

    @Override
    public String consultarSaldoAluno(Integer matricula, Boolean forcarMarcar, Integer contrato) throws Exception {

        if (contrato == null) {
            return saldoSemContrato(matricula, forcarMarcar);
        }

        verificarSituacaoAluno(matricula, contrato);

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" select tipohorario, quantidadecreditodisponivel from contratoduracaocreditotreino cdc \n"
                + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                + " where cd.contrato = " + contrato, con)) {
            if (rs.next()) {
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipohorario"));
                if (tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
                    Integer saldoExtra = nrAulasARepor(matricula);
                    return (saldoExtra) + ";" + (forcarMarcar ? TipoHorarioCreditoTreinoEnum.LIVRE.getTipoMsg() : tipo.getTipoMsg());
                }
                Integer creditoDisponivel = getControleCreditoDao().consultarSaldoCredito(contrato);
                Integer marcacoesFuturas = numeroAulasExtraMarcadas(contrato); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrMarcacoesNoDia = nrReposicoesDoDia(contrato);//reposições de hoje que podem ter tido utilizacao
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(contrato, Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrMarcacoesNoDia -= nrCreditosUsadosNoDia;
                if (nrMarcacoesNoDia <= 0) {
                    nrMarcacoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                }
                Integer saldoVirtual = creditoDisponivel - marcacoesFuturas - nrMarcacoesNoDia;
                saldoVirtual = saldoVirtual < 0 ? 0 : saldoVirtual;
                return saldoVirtual + ";" + tipo.getTipoMsg();
            } else {
                return consultarAulasDesmarcadasAlunoSemReposicoes(matricula, true, 0) + ";" + TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getTipoMsg();
            }
        }
    }

    @Override
    public String consultarSaldoAlunoRepor(Integer matricula, Boolean forcarMarcar, Integer contrato) throws Exception {
        return consultarAulasDesmarcadasAlunoSemReposicoes(matricula, true, contrato) + ";" + TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getTipoMsg();
    }
    @Override
    public String consultarSaldoAlunoMarcar(Integer matricula, boolean forcarMarcar, Integer contrato) throws Exception {
        String filtroContrato = contrato > 0 ? " and cd.contrato = " + contrato : "";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" select tipohorario,saldocreditotreino,s.codigocontrato  from contratoduracaocreditotreino cdc \n"
                + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                + " INNER JOIN situacaoclientesinteticodw s ON s.codigocontrato = cd.contrato and matricula = " + matricula
                + filtroContrato, con)) {
            if (rs.next()) {
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipohorario"));
                if (tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
                    Integer saldoExtra = nrAulasARepor(matricula);
                    return (saldoExtra) + ";" + (forcarMarcar ? TipoHorarioCreditoTreinoEnum.LIVRE.getTipoMsg() : tipo.getTipoMsg());
                }
                Integer marcacoesFuturas = numeroAulasExtraMarcadas(rs.getInt("codigocontrato")); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrMarcacoesNoDia = nrReposicoesDoDia(rs.getInt("codigocontrato"));//reposições de hoje que podem ter tido utilizacao
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(rs.getInt("codigocontrato"), Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrMarcacoesNoDia -= nrCreditosUsadosNoDia;
                if (nrMarcacoesNoDia <= 0) {
                    nrMarcacoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                }
                Integer saldoVirtual = (rs.getInt("saldocreditotreino") - marcacoesFuturas) - nrMarcacoesNoDia;
                return saldoVirtual.toString() + ";" + tipo.getTipoMsg();
            }
        }
        return "0;creditos";
    }

    private String saldoSemContrato(Integer matricula, Boolean forcarMarcar) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" select tipohorario,saldocreditotreino,s.codigocontrato  from contratoduracaocreditotreino cdc \n"
                + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                + " INNER JOIN situacaoclientesinteticodw s ON s.codigocontrato = cd.contrato and matricula = " + matricula, con)) {
            if (rs.next()) {
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipohorario"));
                if (tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
                    Integer saldoExtra = nrAulasARepor(matricula);
                    return (saldoExtra) + ";" + (forcarMarcar ? TipoHorarioCreditoTreinoEnum.LIVRE.getTipoMsg() : tipo.getTipoMsg());
                }
                Integer marcacoesFuturas = numeroAulasExtraMarcadas(rs.getInt("codigocontrato")); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrMarcacoesNoDia = nrReposicoesDoDia(rs.getInt("codigocontrato"));//reposições de hoje que podem ter tido utilizacao
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(rs.getInt("codigocontrato"), Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrMarcacoesNoDia -= nrCreditosUsadosNoDia;
                if (nrMarcacoesNoDia <= 0) {
                    nrMarcacoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                }
                Integer saldoVirtual = (rs.getInt("saldocreditotreino") - marcacoesFuturas) - nrMarcacoesNoDia;
                return saldoVirtual.toString() + ";" + tipo.getTipoMsg();
            } else {
                return consultarAulasDesmarcadasAlunoSemReposicoes(matricula, true, 0) + ";" + TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getTipoMsg();
            }
        }
    }

    @Override
    public JSONArray modalidadesContrato(Integer contrato, Integer matricula) throws Exception {
        JSONArray modalidades = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select modalidade from contratomodalidade c \n" +
                "inner join contrato con on con.codigo = c.contrato \n" +
                "inner join cliente cli on cli.pessoa = con.pessoa \n" +
                "where c.contrato =  " + contrato + " and cli.codigomatricula = " + matricula, con)) {
            while (rs.next()) {
                modalidades.put(rs.getInt("modalidade"));
            }
        }
        return modalidades;
    }

    public Integer consultarAulasDesmarcadasAluno(Integer matricula, boolean reduzirReposicoes) throws Exception {
        int aulasDesmarcadas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT count(a.codigo) as nr FROM auladesmarcada a \n"
                + "INNER JOIN situacaoclientesinteticodw s ON a.contrato = s.codigocontrato "
                + "WHERE s.matricula = " + matricula, con)) {
            aulasDesmarcadas = rs.next() ? rs.getInt("nr") : 0;
        }
        if (!reduzirReposicoes) {
            return aulasDesmarcadas;
        }
        int reposicoes;
        try (ResultSet rsR = SuperFacadeJDBC.criarConsulta("SELECT count(a.codigo) as nr FROM reposicao a \n"
                + "INNER JOIN situacaoclientesinteticodw s ON a.contrato = s.codigocontrato "
                + "WHERE s.matricula = " + matricula, con)) {
            reposicoes = rsR.next() ? rsR.getInt("nr") : 0;
        }
        return aulasDesmarcadas - reposicoes;
    }

    public Integer consultarAulasDesmarcadasAlunoSemReposicoes(Integer matricula, boolean contratoNormal, Integer contrato) throws Exception {
        Integer validadeReposicao = 0;
        boolean habilitarSomaDeAulaNaoVigente = false;
        if (contratoNormal) {
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select tempoAposFaltaReposicao, habilitarSomaDeAulaNaoVigente from empresa e inner join contrato c on c.empresa = e.codigo "
                    + "inner join situacaoclientesinteticodw s ON c.codigo = s.codigocontrato where  s.matricula = " + matricula, con)) {
                if (rs.next()) {
                    validadeReposicao = rs.getInt("tempoAposFaltaReposicao");
                    habilitarSomaDeAulaNaoVigente = rs.getBoolean("habilitarSomaDeAulaNaoVigente");
                }
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(distinct a.codigo) as nr \n");
        sql.append("FROM auladesmarcada a \n");
        sql.append("INNER JOIN contrato c ON a.contrato = c.codigo AND c.situacao = 'AT'\n");
        sql.append("INNER JOIN cliente cli ON c.pessoa = cli.pessoa\n");
        sql.append("INNER JOIN contratomodalidade cm on cm.contrato = c.codigo\n");
        sql.append("INNER JOIN contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo\n");
        sql.append("INNER JOIN horarioTurma ht ON ht.codigo = a.horarioTurma \n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append("INNER JOIN modalidade m ON t.modalidade = m.codigo \n");
        sql.append("WHERE  permiteReporAulaDesmarcada = true and datareposicao is null\n");
        sql.append(" AND cli.codigomatricula = ").append(matricula);
        // quando habilitarSomaDeAulaNaoVigente habilitado compara com outros códigos de turmas que não estão presentes no contratomodalidadeturma
        if (!habilitarSomaDeAulaNaoVigente) {
            sql.append(" AND a.turma = cmt.turma \n");
        }
        sql.append(" AND m.utilizarturma is true \n");
        if (UteisValidacao.notEmptyNumber(validadeReposicao)) {
            sql.append(" and a.dataOrigem >= '").append(Uteis.getDataJDBC(Uteis.somarDias(Calendario.hoje(), -validadeReposicao))).append("' ");
        }
        if(contrato > 0) {
            sql.append(" and c.codigo = ").append(contrato);
        }
        int aulasDesmarcadas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            aulasDesmarcadas = rs.next() ? rs.getInt("nr") : 0;
        }
        return aulasDesmarcadas;
    }

    public Integer valorAlteracoesSaldo(Integer matricula, Date data, TipoOperacaoCreditoTreinoEnum... tipos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(c.codigo) as nr from controlecreditotreino c\n");
        sql.append("INNER JOIN situacaoclientesinteticodw s ON c.contrato = s.codigocontrato\n");
        sql.append("WHERE s.matricula = ").append(matricula).append("\n");
        if (data != null) {
            sql.append("AND  dataoperacao >= '").append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(data), "yyyy-MM-dd HH:mm:ss")).append("'\n");
        }
        String f = "";
        for (TipoOperacaoCreditoTreinoEnum t : tipos) {
            f += "," + t.getCodigo();
        }
        sql.append("AND tipooperacaocreditotreino IN(").append(f.replaceFirst(",", "")).append(")\n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            return rs.next() ? rs.getInt("nr") : 0;
        }

    }

    @Override
    public JSONArray obterAcessosDia(Integer empresa, Date dia) throws Exception {
        return getAcessoDao().obterUltimosAcessos(empresa, dia);
    }

    @Override
    public JSONArray obterProfessores(Integer empresa, boolean somenteAtivos) throws Exception {
        return getColaboradorDao().obterProfessores(empresa, somenteAtivos);
    }

    @Override
    public void marcarEuQueroHorario(Integer codigoAluno, Integer horarioturma, Date data) throws Exception {
        DemandaHorarioTurmaVO demandaHorarioTurmaVO = getDemandaHorarioTurmaDao().novo();
        ClienteVO cliente = getClienteDao().consultarPorCodigo(codigoAluno, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        HorarioTurmaVO horarioTurma = getHorarioDao().consultarPorCodigo(horarioturma, Uteis.NIVELMONTARDADOS_MINIMOS);

        if (cliente == null)
            throw new Exception("Não existe cliente com a código: " + codigoAluno);


        if (horarioTurma == null)
            throw new Exception("Não existe Horário Turma com o código: " + horarioturma);

        demandaHorarioTurmaVO.setCliente(cliente);
        demandaHorarioTurmaVO.setHorarioTurma(horarioTurma);
        demandaHorarioTurmaVO.setData(data);
        demandaHorarioTurmaVO.setDataProcura(Calendario.hoje());
        getDemandaHorarioTurmaDao().incluir(demandaHorarioTurmaVO);
    }

    @Override
    public void desmarcarEuQueroHorario(Integer codigoAluno, Integer horarioturma, Date data) throws Exception {
        ClienteVO cliente = getClienteDao().consultarPorCodigo(codigoAluno, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        getDemandaHorarioTurmaDao().excluir(cliente.getCodigo(), horarioturma, data);
    }

    @Override
    public JSONArray consultarDemandasSintetico(JSONArray filtroProfessores, JSONArray filtroModalidaes, JSONArray filtroAmbientes,
                                                JSONArray filtroTurmas, JSONArray filtroHorarios, JSONArray filtroDiasDaSemana,
                                                Date dataInicio, Date dataFim) throws Exception {
        List<SinteticoDemandaHorarioTurmaTO> lista = getDemandaHorarioTurmaDao()
                .consultarSintetico(filtroProfessores, filtroModalidaes, filtroAmbientes,
                        filtroTurmas, filtroHorarios, filtroDiasDaSemana,
                        dataInicio, dataFim);
        JSONArray jsonArray = new JSONArray();
        for (SinteticoDemandaHorarioTurmaTO agrup : lista) {
            jsonArray.put(agrup.toJSON());
        }
        return jsonArray;
    }

    @Override
    public JSONArray consultarDemandasAnalitico(JSONArray filtroProfessores, JSONArray filtroModalidaes, JSONArray filtroAmbientes, JSONArray filtroTurmas, JSONArray filtroHorarios, JSONArray filtroDiasDaSemana, Date dataInicio, Date dataFim) throws Exception {
        List<AnaliticoDemandaHorarioTurmaTO> lista = getDemandaHorarioTurmaDao()
                .consultarAnalitico(filtroProfessores, filtroModalidaes, filtroAmbientes,
                        filtroTurmas, filtroHorarios, filtroDiasDaSemana,
                        dataInicio, dataFim);
        JSONArray jsonArray = new JSONArray();
        for (AnaliticoDemandaHorarioTurmaTO agrup : lista) {
            jsonArray.put(agrup.toJSON());
        }

        return jsonArray;
    }

    @Override
    public JSONArray consultarTodasTurmas() throws Exception {
        return getTurmaDao().consultarTodas();
    }

    @Override
    public JSONArray consultarTodasHoraInicial() throws Exception {
        return getHorarioDao().consultarTodasHoraInicial();
    }

    @Override
    public String desativarAulaColetiva(Integer codigoAula, Integer usuario) throws Exception {
        getTurmaDao();
        TurmaVO turmaVO = this.turmaDao.consultarPorChavePrimaria(codigoAula, Uteis.NIVELMONTARDADOS_TODOS);
        boolean existeVinculo = false;
        if (turmaVO.getAulaColetiva()) {
            existeVinculo = getTurmaDao().existeVinculoFuturoAlunoAulaColetiva(codigoAula);
        } else {
            existeVinculo = getTurmaDao().existemAlunosNaTurma(turmaVO);
        }
        if (!existeVinculo) {
            String result = getTurmaDao().desativarAulaColetiva(codigoAula, usuario);
            if (result.equals("ok")) {
                incluirLogAulaColetivaDesativar(codigoAula, usuario, turmaVO);
            }
        } else {
            return "Não é possível excluir a " + (turmaVO.getAulaColetiva() ? "aula" : "turma") + " por ter alunos vinculados.";
        }
        return "ok";
    }

    public void incluirLogAulaColetivaDesativar(Integer codigoAula, Integer usuario, TurmaVO turmaVO) throws Exception {
        getUsuarioDao();
        UsuarioVO usuarioVO = this.usuarioDao.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_TODOS);

        incluirLog(codigoAula.toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "EXCLUSÃO", "Nome aula", "", turmaVO.getDescricao(), null, null);
        incluirLog(codigoAula.toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "EXCLUSÃO", "Data final vigência", Uteis.getDataFormatoBD(Uteis.somarDias(Calendario.hoje(), -1)), turmaVO.getDataFinalVigencia_Apresentar(), null, null);
        incluirLog(codigoAula.toString(), "AULA_COLETIVA", "Aula Coletiva", usuarioVO, "EXCLUSÃO", "Situação", "IN", "AT", null, null);
    }

    @Override
    public AgendaTotalJSON consultarUmaTurma(Integer codigoHorarioTurma, Date dia) throws Exception {
        AgendaTotalJSON item = null;
        Map<String, List<AgendaTotalJSON>> turmasParaAgenda = getTurmaDao().consultarHorariosTurmaParaAgenda(dia, 0, null,
                false, codigoHorarioTurma, null, false);
        String diaDaSemana = Uteis.obterDiaSemanaData(dia);
        List<AgendaTotalJSON> turmas = turmasParaAgenda.get(diaDaSemana);

        if (turmas != null) {
            for (AgendaTotalJSON a : turmas) {
                AgendaTotalJSON agendaTotalJSON = new AgendaTotalJSON(dia, a);
                agendaTotalJSON.setDiaSemana(diaDaSemana);
                if (agendaTotalJSON.getAulaCheia()) {
                    try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                            "SELECT COUNT(*) as ocupacao FROM alunohorarioturma WHERE horarioturma = " + agendaTotalJSON.getId()
                                    + " AND dia = '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "'", con)) {
                        agendaTotalJSON.setNrVagasPreenchidas(rs.next() ? rs.getInt("ocupacao") : 0);
                    }
                    try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                            "SELECT COUNT(*) as ocupacaoExperimental FROM alunohorarioturma WHERE horarioturma = " + agendaTotalJSON.getId()
                                    + " AND dia = '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")  + "'"
                                    + " AND aulaexperimental is true ", con)) {
                        agendaTotalJSON.setNrVagasPreenchidasExperimental(rs.next() ? rs.getInt("ocupacaoExperimental") : 0);
                    }
                } else {
                    calcularVagasPreenchidas(agendaTotalJSON, dia);
                }
                item = agendaTotalJSON;
            }
        }

        return item;
    }

    private void calcularVagasPreenchidas(AgendaTotalJSON agenda, Date dia) throws Exception {
        try {
            List<AgendadoJSON> agendados = consultarAgendadosParaAgenda(Calendario.getDataComHoraZerada(dia),
                    Calendario.getDataComHora(dia, "23:59:59"), agenda.getEmpresa());
            List<AgendadoJSON> reposicoes = consultarReposicoesParaAgenda(Calendario.getDataComHoraZerada(dia),
                    Calendario.getDataComHora(dia, "23:59:59"), agenda.getEmpresa());
            List<AgendamentoDesmarcadoJSON> desmarcados = consultarDesmarcados(Calendario.getDataComHoraZerada(dia),
                    Calendario.getDataComHora(dia, "23:59:59"), agenda.getEmpresa());

            int vagasPreenchidas = calcularVagasPreenchidas(agendados, reposicoes, desmarcados, agenda, dia);
            agenda.setNrVagasPreenchidas(vagasPreenchidas);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int calcularVagasPreenchidas(List<AgendadoJSON> agendados, List<AgendadoJSON> reposicoes, List<AgendamentoDesmarcadoJSON> desmarcados, AgendaTotalJSON agenda, Date dia) throws ParseException {
        int vagasPreenchidas = 0;

        List<AgendadoJSON> agendamentosValidos = agendados.stream()
                .filter(agendado -> {
                    try {
                        return isAgendamentoValido(agendado, agenda);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());

        for (AgendadoJSON reposicao : reposicoes) {
            if (isAgendamentoValido(reposicao, agenda)) {
                vagasPreenchidas++;
            }
        }

        for (AgendadoJSON agendado : agendamentosValidos) {
            boolean foiDesmarcado = desmarcados.stream()
                    .anyMatch(desmarcado -> isDesmarcado(agendado, desmarcado, agenda, dia));
            if (!foiDesmarcado && !isAlunoJaAgendadoComoReposicao(agendado, reposicoes)) {
                vagasPreenchidas++;
            }
        }

        return vagasPreenchidas;
    }

    private boolean isAlunoJaAgendadoComoReposicao(AgendadoJSON agendado, List<AgendadoJSON> reposicoes) {
        return reposicoes.stream().anyMatch(reposicao -> isAlunoJaAgendado(reposicao, agendado));
    }

    private boolean isAgendamentoValido(AgendadoJSON agendado, AgendaTotalJSON agenda) throws ParseException {
        return agenda.getId().equals(agendado.getId_agendamento()) &&
                Calendario.menorOuIgual(Calendario.getDate("dd/MM/yyyy", agendado.getInicio()), Calendario.getDate("dd/MM/yyyy", agenda.getInicio())) &&
                Calendario.maiorOuIgual(Calendario.getDate("dd/MM/yyyy", agendado.getFim()), Calendario.getDate("dd/MM/yyyy", agenda.getFim()));
    }

    private boolean isDesmarcado(AgendadoJSON agendado, AgendamentoDesmarcadoJSON desmarcado, AgendaTotalJSON agenda, Date dia) {
        String idAgendamento = agenda.getId() + "_" + Uteis.getData(dia, "ddMMyy");
        return desmarcado.getIdAgendamento().equals(idAgendamento) &&
                desmarcado.getCodigoContrato().equals(agendado.getCodigoContrato());
    }

    private boolean isAlunoJaAgendado(AgendadoJSON reposicao, AgendadoJSON agendado) {
        return reposicao.getMatricula().equals(agendado.getMatricula()) && !agendado.isDesmarcado();
    }

    public Integer nrAulasExtras(Integer matricula, Integer codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        if(UteisValidacao.emptyNumber(codigoContrato)){
            sql.append("select sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.codigocontrato from situacaoclientesinteticodw sc where matricula = ").append(matricula);
        } else {
            sql.append("select c.codigo  as codigocontrato, cdt.quantidadecreditodisponivel as saldoCreditoTreino from contrato c inner join contratoduracao cd on cd.contrato = c.codigo inner join contratoduracaocreditotreino cdt on cd.codigo = cdt.contratoduracao \n" +
                    "where c.codigo = ").append(codigoContrato);
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                Integer contrato = rs.getInt("codigocontrato");
                Integer saldo = rs.getInt("saldoCreditoTreino");
                ContratoVO contratoVO = getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                List<ContratoModalidadeTurmaVO> modalidadesTurmas = montarTodosContratoModalidadeTurmaCreditoTreino(contrato, null);
                Map<Integer, Date> mapaDia = contratoVO.verificarTotalCreditoPossivelUtilizar(Calendario.hoje(), modalidadesTurmas, obterUltimaDataMatriculaAluno(contratoVO), null);
                Integer nrAulasRestantesPossiveis = 0;  // quantidade de aulas do aluno dentro da vigencia da turma
                Integer nrAulasDesmarcadasPraFrente = calcularDesmarcadosReposicoes(contrato, Calendario.hoje(), false, false); // aulas futuras que foram desmarcadas
                Integer nrAulasDesmarcadas = calcularDesmarcadosReposicoes(contrato, null, true, null); // aulas desmarcadas aguardando reposicao(ainda não debitaram crédito)
                Set<Map.Entry<Integer, Date>> set = mapaDia.entrySet();
                for (Map.Entry<Integer, Date> ent : set) {
                    nrAulasRestantesPossiveis = ent.getKey();
                }
                Integer nrAulasExtraMarcadas = numeroAulasExtraMarcadas(contrato); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrReposicoesFuturasAulaDermacadas = nrReposicoesFututasDeAulasDesmarcadas(contrato); //reposicões futuras de aulas desmarcadas(ainda não debitaram crédito)
                Integer nrReposicoesNoDia = nrReposicoesDoDia(contrato);//reposições de hoje que podem ter tido utilizacao
                Integer quantidadeDeAulasFuturasNasTurmas = nrAulasDesmarcadasPraFrente > nrAulasRestantesPossiveis ? 0
                        : (nrAulasRestantesPossiveis - nrAulasDesmarcadasPraFrente); // Quantidade de aulas possívies menos desmarcações futuras.

                //sistema considera nos calculos acima, aula e reposições de hoje para frente. Caso o aluno tenha consumido a aula  ou reposicão de hoje
                // Essa aula/reposicao de hoje, já consumiu crédito e deve ser subtraída. Como falta só é computada de madruga, em primeiro momento não é necessário verificar
                // faltas. Mas consigerando uma melhoria futura, onde um processo rodasse de hora em hora já dando falta para alunos de turma que já passaram do horário,
                // irei verificar, mas como disse, do modo que está hoje não interfere. Ajuste manuais serão desconsiderados, pois afetam não tem ligações com as aulas de hoje.
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(contrato, Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrCreditosUsadosNoDia = nrCreditosUsadosNoDia - nrReposicoesNoDia;
                if (nrCreditosUsadosNoDia >= 0) {
                    nrReposicoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                } else {
                    nrReposicoesNoDia = Math.abs(nrCreditosUsadosNoDia);
                    nrCreditosUsadosNoDia = 0;  // créditos foram para reposições do dia
                }
                Integer nrAulasExtras = saldo - (quantidadeDeAulasFuturasNasTurmas - nrCreditosUsadosNoDia) - nrAulasExtraMarcadas - nrAulasDesmarcadas - nrReposicoesFuturasAulaDermacadas - nrReposicoesNoDia;
                return nrAulasExtras;
            }
        }
        return 0;
    }


    public Integer nrAulasARepor(Integer matricula) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.codigocontrato "
                + " FROM situacaoclientesinteticodw sc where matricula = " + matricula, con)) {
            if (rs.next()) {
                Integer contrato = rs.getInt("codigocontrato");
                Integer saldo = rs.getInt("saldoCreditoTreino");
                Integer nrAulasExtras = nrAulasExtras(matricula, contrato);
                Integer nrAulasDesmarcadas = consultarAulasDesmarcadasAlunoSemReposicoes(matricula, false, 0);
                Integer nrAulasARepor = (nrAulasExtras < 0 ? 0 : nrAulasExtras) + nrAulasDesmarcadas;
                if (nrAulasARepor > saldo) {
                    nrAulasARepor = saldo;
                }
                if (nrAulasARepor > 0) {
                    return nrAulasARepor;
                }
            }
        }
        return 0;
    }

    public Date obterUltimaDataMatriculaAluno(ContratoVO contratoVO) throws Exception {
        try (ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select max(datafim) as ultima from matriculaalunohorarioturma  where contrato  = " + contratoVO.getCodigo(), con)) {
            if (resultSet.next()) {
                return resultSet.getDate("ultima") == null ? contratoVO.getVigenciaAteAjustada() : resultSet.getDate("ultima");
            }
        }
        return contratoVO.getVigenciaAteAjustada();
    }

    public Boolean temAulaExtra(Integer contrato, Integer modalidade, Integer saldo) throws Exception {
        ClienteVO clienteVO = getClienteDao().consultarPorCodigoContrato(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Integer aulasExtras = nrAulasExtras(clienteVO.getCodigoMatricula(), contrato);
        return aulasExtras > 0;
    }

    private int calcularDesmarcadosReposicoes(int codigoContrato, Date limite, boolean considerarPermiteReporAulaDesmarcada, Boolean considerarRepostas) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  count(ad.codigo) as total \n");
        sql.append("from aulaDesmarcada  ad \n");
        sql.append("inner join horarioTurma ht on ht.codigo = ad.horarioTurma \n");
        sql.append(" where ad.contrato = ").append(codigoContrato);
        if (considerarPermiteReporAulaDesmarcada) {
            sql.append(" and permiteReporAulaDesmarcada = true ");
        }
        if (limite == null) {
            sql.append(" and datareposicao is null ");
        } else {
            sql.append(" and ad.dataOrigem::date >= '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("' ");
            if (considerarRepostas != null && considerarRepostas) {
                sql.append(" and ( datareposicao is null or to_timestamp(to_char(datareposicao, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') > '");
                sql.append(sdf.format(Calendario.hoje())).append("' )");
            }
        }
        int numeroReposicao;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroReposicao = 0;
            while (rs.next()) {
                numeroReposicao = rs.getInt("total");
            }
        }
        return numeroReposicao;
    }

    private List<ContratoModalidadeTurmaVO> montarTodosContratoModalidadeTurmaCreditoTreino(Integer contrato, Integer modalidade) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" SELECT cmt.* FROM contratomodalidadeturma cmt \n" +
                " INNER JOIN contratomodalidade cm ON cmt.contratomodalidade = cm.codigo \n" +
                " WHERE cm.contrato = " + contrato
                + (modalidade == null ? "" : " AND modalidade = " + modalidade), con)) {
            return ContratoModalidadeTurma.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
        }
    }

    public Integer consultarUltimoAlunoHorarioTurmaInserido() throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" SELECT codigo FROM alunohorarioturma \n" +
                " order by codigo desc limit 1", con)) {
            if (rs.next()) {
                return rs.getInt("codigo");
            }
            return null;
        }
    }

    private int numeroDesmarcados(int contrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  count(distinct(ad.cliente)) as total from aulaDesmarcada  ad").append("\n");
        sql.append(" INNER JOIN matriculaalunohorarioturma mht ON mht.contrato  = ad.contrato and mht.horarioturma = ad.horarioTurma ").append("\n");
        sql.append("        LEFT JOIN (Select max(codigo),horarioturma,cliente,datareposicao,codigo from reposicao rep \n");
        sql.append("GROUP BY rep.horarioturma,rep.cliente,rep.datareposicao,rep.codigo order by rep.codigo desc limit 1) as rep ON rep.horarioTurma = ad.horarioTurma and rep.cliente = ad.cliente \n");
        sql.append("where ad.contrato = ").append(contrato).append(" and ((rep.codigo = 0 or rep.codigo is null) or  not rep.datareposicao > ad.datareposicao)\n");
        int numeroDesmarcado;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroDesmarcado = 0;
            while (rs.next()) {
                numeroDesmarcado = rs.getInt("total");
            }
        }
        return numeroDesmarcado;
    }

    private int numeroReposicao(int contrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) as total from reposicao where contrato = ").append(contrato);
        int numeroReposicao;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroReposicao = 0;
            while (rs.next()) {
                numeroReposicao = rs.getInt("total");
            }
        }
        return numeroReposicao;
    }

    public int numeroAulasExtraMarcadas(int codigoContrato) throws Exception {  // avalia aulas extras, tanto pra contrato com turma e contrato livre obrigatorio marcar horário
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
        sql.append(" LEFT JOIN auladesmarcada a on a.reposicao = r.codigo \n");
        sql.append(" WHERE  r.turmaorigem = r.turmadestino  \n");
        sql.append(" AND r.horarioturma = r.horarioturmaorigem  \n");
        sql.append(" AND cast(r.datareposicao as date) = cast (r.dataorigem as date)  \n");
        sql.append(" AND r.contrato = ").append(codigoContrato);
        sql.append(" AND a.codigo is null \n");
        sql.append(" AND r.datareposicao::date > '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    private int nrReposicoesFututasDeAulasDesmarcadas(int codigoContrato) throws Exception { // avalia aulas desmarcadas
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
        sql.append(" inner JOIN auladesmarcada a on a.reposicao = r.codigo \n");
        sql.append(" WHERE  r.contrato = ").append(codigoContrato);
        sql.append(" AND r.datareposicao > '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    public int nrReposicoesDoDia(int codigoContrato) throws Exception { // avalia aulas desmarcadas
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
        sql.append(" WHERE  r.contrato = ").append(codigoContrato);
        sql.append(" AND r.datareposicao = '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    private void verificarSituacaoAluno(Integer matricula, Integer contrato) throws Exception {
        try (ResultSet sit = SuperFacadeJDBC.criarConsulta("select codigocliente, situacaocontrato from situacaoclientesinteticodw where matricula = " + matricula, con)) {
            if (sit.next()) {
                String situacaoContrato = sit.getString("situacaocontrato");
                Integer codigocliente = sit.getInt("codigocliente");

                boolean contratoConcomitanteAlunoAtivo = contratoConcomitanteAlunoAtivo(codigocliente, Calendario.hoje());
                boolean existeOperacao = false;
                if (contratoConcomitanteAlunoAtivo && contrato != null) {
                    ContratoOperacaoVO contratoOperacaoVO = getContratoOperacaoDao().obterOperacaoParaEstaData("CR", new Date(), contrato);
                    if (contratoOperacaoVO != null) {
                        existeOperacao = true;
                        situacaoContrato = contratoOperacaoVO.getTipoOperacao();
                    }
                }

                if ((!contratoConcomitanteAlunoAtivo || existeOperacao) && situacaoContrato != null && !situacaoContrato.isEmpty()) {
                    switch (situacaoContrato) {
                        case "TR":
                            throw new Exception("Aluno está Trancado");
                        case "AE":
                            throw new Exception("Aluno em Atestado");
                        case "CR":
                            throw new Exception("Aluno em Férias");
                    }
                }
            }
        }

    }

    public List<AgendadoJSON> consultarAlunosDeUmaAula(Integer codigoHorario, Date dia) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT (CASE \n" +
                "        WHEN pes.fotokey IS NOT NULL THEN pes.fotokey \n" +
                "        ELSE aage.fotokey END) as fotokey, \n");
        sql.append(" 	   (CASE \n");
        sql.append(" 	    WHEN pes.codigo IS NOT NULL THEN pes.codigo \n");
        sql.append(" 	    ELSE aage.codigopessoa END) AS codigopessoa, \n");
        sql.append(" 	    cli.codigo AS codigocliente, \n");
        sql.append(" 	   aht.codigo AS codigoagendamento, ht.horainicial, ht.horafinal, \n");
        sql.append(" 	   (CASE  \n");
        sql.append(" 	    WHEN pes.nome IS NOT NULL THEN pes.nome \n");
        sql.append(" 	    ELSE aage.nomepessoa END) AS nome, \n");
        sql.append(" 	    pes.datanasc, \n");
        sql.append(" 	   (CASE  \n");
        sql.append(" 	    WHEN cli.matricula IS NOT NULL THEN cli.matricula \n");
        sql.append(" 	    ELSE aage.codigomatricula::VARCHAR(255) END) AS matricula, \n");
        sql.append(" 	    ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = cli.pessoa), ';') as telefones, \n");
        sql.append("        aulaexperimental  \n");
        sql.append(" FROM alunohorarioturma aht \n");
        sql.append(" LEFT JOIN cliente cli ON cli.codigo = aht.cliente \n");
        sql.append(" LEFT JOIN pessoa pes ON cli.pessoa = pes.codigo \n");
        sql.append(" LEFT JOIN horarioturma ht ON ht.codigo = aht.horarioturma \n");
        sql.append(" LEFT JOIN autorizacaoacessogrupoempresarial aage ON aage.codigo = aht.autorizado \n");
        sql.append(" WHERE aht.dia = ? AND aht.horarioturma = ?");
        sql.append(" ORDER BY pes.nome");
        List<AgendadoJSON> alunos;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setDate(1, Uteis.getDataJDBC(dia));
            stm.setInt(2, codigoHorario);
            try (ResultSet rs = stm.executeQuery()) {

                HashMap<String, AgendamentoConfirmadoJSON> confirmados = obterMapaConfirmados(dia, dia);
                alunos = new ArrayList<AgendadoJSON>();
                while (rs.next()) {
                    AgendadoJSON aluno = new AgendadoJSON();
                    aluno.setUrlFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                    aluno.setNome(rs.getString("nome"));
                    aluno.setDataNascimento(rs.getString("datanasc"));
                    aluno.setMatricula(rs.getString("matricula"));
                    aluno.setTelefones(rs.getString("telefones"));
                    aluno.setFim(rs.getString("horafinal"));
                    aluno.setInicio(rs.getString("horainicial"));
                    aluno.setId_agendamento(rs.getString("codigoagendamento"));
                    aluno.setCodigoCliente(rs.getInt("codigocliente"));
                    aluno.setCodigoPessoa(rs.getInt("codigopessoa"));
                    aluno.setExperimental(rs.getBoolean("aulaexperimental"));
                    String identificador = codigoHorario + "_" + Uteis.getData(dia, "ddMMyy") + "_" + aluno.getCodigoCliente();
                    aluno.setConfirmado(confirmados.get(identificador) != null);
                    alunos.add(aluno);
                }
            }
        }
        return alunos;
    }

    /**
     * No instancia RemoveAlunoAula removemos e todos os alunos duplicados, desmarcados e realocados para outra ou para esta turma.
     *
     * @param codigoHorario
     * @param dia
     * @return
     * @throws Exception
     */
    public List<AgendadoJSON> consultarAlunosAulaNaoColetiva(Integer codigoHorario, Date dia) throws Exception {
        Map<String, List<AgendadoJSON>> resultMap = getTurmaDao().consultarAulasAlunosNaoColetivas(dia, codigoHorario);
        List<AgendadoJSON> alunos = resultMap.get("ALUNOS");
        List<Integer> listIdCodigoPessoaDesmarcada = getTurmaDao().getCodigoAlunaAulaDesmarcada(codigoHorario, dia, false);
        return new RemoveAlunoAula(alunos, resultMap.get("REALOCADOS"), listIdCodigoPessoaDesmarcada).getAlunos();
    }


    @Override
    public List<ClienteSintenticoJson> findClienteSintetico(List<AgendadoJSON> alunos) throws Exception {
        return getTurmaDao().findClienteSintetico(alunos);
    }


    public HashMap<String, AgendamentoConfirmadoJSON> obterMapaConfirmados(Date diaIn, Date diaFim) throws Exception {
        List<AgendamentoConfirmadoJSON> confirmados = consultarConfirmados(diaIn, diaFim);
        HashMap<String, AgendamentoConfirmadoJSON> agendamentoConfirmado = new HashMap<String, AgendamentoConfirmadoJSON>();
        for (AgendamentoConfirmadoJSON agendamento : confirmados) {
            agendamentoConfirmado.put(agendamento.getIdAgendamento() + "_" + agendamento.getCodigoCliente(), agendamento);
        }
        return agendamentoConfirmado;
    }

    @Override
    public void inserirConfirmacaoAulaAluno(int cliente, int horarioTurma, Integer colaborador, Date dia) throws Exception {
        AulaConfirmadaVO aulaConfirmadaVO = new AulaConfirmadaVO();
        aulaConfirmadaVO.setDia(dia);
        aulaConfirmadaVO.setColaboradorVO(new ColaboradorVO());
        if (UteisValidacao.notEmptyNumber(colaborador)) {
            aulaConfirmadaVO.getColaboradorVO().setCodigo(colaborador);
        }
        aulaConfirmadaVO.setClienteVO(new ClienteVO());
        aulaConfirmadaVO.getClienteVO().setCodigo(cliente);
        aulaConfirmadaVO.setHorarioTurmaVO(new HorarioTurmaVO());
        aulaConfirmadaVO.getHorarioTurmaVO().setCodigo(horarioTurma);
        aulaConfirmadaVO.setDataLancamento(Calendario.hoje());
        aulaConfirmadaVO.setOrigemSistemaEnum(OrigemSistemaEnum.ZW);
        getAulaConfirmadaDao().incluirSemCommit(aulaConfirmadaVO);

    }

    @Override
    public void presencaAulaCheiaAluno(String key, int cliente, int horarioTurma, int codColaborador, Date dia, OrigemSistemaEnum origem, Boolean confirmar) throws Exception {
        if (confirmar) {
            if (Calendario.maior(dia, Calendario.hoje())) {
                throw new Exception("Não é permitido marcar presença para datas futuras");
            }
            inserirConfirmacaoAulaCheiaAluno(key, cliente, horarioTurma, codColaborador, dia, origem);
        } else {
            removerAulaCheiaConfirmada(key, cliente, 0, horarioTurma, dia, codColaborador, origem);
        }
    }

    @Override
    public void inserirConfirmacaoAulaCheiaAluno(final String key, int cliente, int horarioTurma, int codColaborador, Date dia, OrigemSistemaEnum origem) throws Exception {
        AulaConfirmadaVO aulaConfirmadaVO = new AulaConfirmadaVO();
        aulaConfirmadaVO.setDia(dia);
        aulaConfirmadaVO.setColaboradorVO(new ColaboradorVO());
        if (codColaborador > 0) {
            aulaConfirmadaVO.getColaboradorVO().setCodigo(codColaborador);
        }
        ClienteVO clienteVO = new Cliente(con).consultarPorCodigo(cliente, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        aulaConfirmadaVO.setClienteVO(clienteVO);
        aulaConfirmadaVO.setHorarioTurmaVO(new HorarioTurmaVO());
        aulaConfirmadaVO.getHorarioTurmaVO().setCodigo(horarioTurma);
        Date agoraTimeZone = dataComTimezone(clienteVO.getEmpresa().getCodigo());
        aulaConfirmadaVO.setDataLancamento(agoraTimeZone);
        aulaConfirmadaVO.setOrigemSistemaEnum(OrigemSistemaEnum.ZW);
        getAulaConfirmadaDao().incluir(aulaConfirmadaVO);

        processarAcesso(key, cliente, dia, false, horarioTurma);
        try {
            tratarAulaReposicaoOuExperimental(key, dia, horarioTurma, cliente, 0, false);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }

        try {
            String chavePrimaria = horarioTurma + "_" + Calendario.getData(dia, "dd/MM/yyyy");
            UsuarioVO usuarioVO = new UsuarioVO();
            try {
                if (origem != null && origem.equals(OrigemSistemaEnum.APP_TREINO) && codColaborador == 0) {
                    usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                } else {
                    usuarioVO = getUsuarioDao().consultarPorColaborador(codColaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ;
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }

            StringBuilder descricao = new StringBuilder();
            descricao.append("<br/>[matricula: ").append(aulaConfirmadaVO.getClienteVO().getCodigoMatricula()).append("]<br/>");
            descricao.append("[aluno: ").append(aulaConfirmadaVO.getClienteVO().getPessoa().getNome()).append("]<br/>");
            String descricaoOrigem = origem != null ? origem.getDescricao() : "Não informada";
            descricao.append("[origem: ").append(descricaoOrigem).append("]");

            incluirLog(chavePrimaria, "PRESENCA", "Presença Confirmada", usuarioVO, "ALTERAÇÃO", "Presença Confirmada",
                    descricao.toString(), "", null, cliente);
        } catch (Exception ex) {
            Uteis.logar(ex, TurmasServiceImpl.class);
        }
    }

    @Override
    public void removerAulaConfirmada(int cliente, int horarioTurma, Date dia) throws Exception {
        AulaConfirmadaVO aulaConfirmadaVO = new AulaConfirmadaVO();
        aulaConfirmadaVO.setDia(dia);
        aulaConfirmadaVO.setClienteVO(new ClienteVO());
        aulaConfirmadaVO.getClienteVO().setCodigo(cliente);
        aulaConfirmadaVO.setHorarioTurmaVO(new HorarioTurmaVO());
        aulaConfirmadaVO.getHorarioTurmaVO().setCodigo(horarioTurma);
        getAulaConfirmadaDao().excluir(aulaConfirmadaVO);
        try {
            tratarAulaReposicaoOuExperimental(DAO.resolveKeyFromConnection(getAulaConfirmadaDao().getCon()), dia, horarioTurma, cliente, 0, true);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
    }

    @Override
    public void removerAulaCheiaConfirmada(final String key, int cliente, int passivo, int horarioTurma, Date dia, Integer codColaborador, OrigemSistemaEnum origem) throws Exception {
        AulaConfirmadaVO aulaConfirmadaVO = new AulaConfirmadaVO();
        aulaConfirmadaVO.setDia(dia);
        aulaConfirmadaVO.setClienteVO(new ClienteVO());
        aulaConfirmadaVO.getClienteVO().setCodigo(cliente);
        aulaConfirmadaVO.setPassivoVO(new PassivoVO());
        aulaConfirmadaVO.getPassivoVO().setCodigo(passivo);
        aulaConfirmadaVO.setHorarioTurmaVO(new HorarioTurmaVO());
        aulaConfirmadaVO.getHorarioTurmaVO().setCodigo(horarioTurma);
        getAulaConfirmadaDao().excluir(aulaConfirmadaVO);

        if(!UteisValidacao.emptyNumber(cliente)) {
            processarAcesso(key, cliente, dia, true, horarioTurma);
        }
        try {
            tratarAulaReposicaoOuExperimental(key, dia, horarioTurma, cliente, passivo, true);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }

        try {
            ClienteVO clienteVO = !UteisValidacao.emptyNumber(passivo) ? null : new Cliente(con).consultarPorCodigo(cliente, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = new UsuarioVO();
            try {
                if (origem != null && origem.equals(OrigemSistemaEnum.APP_TREINO) && codColaborador == 0) {
                    usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                } else {
                    usuarioVO = getUsuarioDao().consultarPorColaborador(codColaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }

            String chavePrimaria = horarioTurma + "_" + Calendario.getData(dia, "dd/MM/yyyy");

            StringBuilder descricao = new StringBuilder();
            if(!UteisValidacao.emptyNumber(passivo)){
                descricao.append("<br/>[lead: ").append(passivo).append("]<br/>");
            }else{
                descricao.append("<br/>[matricula: ").append(clienteVO.getCodigoMatricula()).append("]<br/>");
                descricao.append("[aluno: ").append(clienteVO.getPessoa().getNome()).append("]<br/>");
            }
            String descricaoOrigem = origem != null ? origem.getDescricao() : "Não informada";
            descricao.append("[origem: ").append(descricaoOrigem).append("]");

            incluirLog(chavePrimaria, "PRESENCA", "Presença Desconfirmada", usuarioVO, "ALTERAÇÃO",
                    "Presença Desconfirmada", "", descricao.toString(), null, cliente);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
    }

    @Override
    public List<AgendamentoConfirmadoJSON> consultarConfirmados(Date inicio, Date fim) throws Exception {
        return getAulaConfirmadaDao().consultarAgendamentosConfirmados(inicio, fim);
    }

    public Integer obterPontosAluno(Integer matricula) throws Exception {
        Integer retorno = 0;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT max(hp.codigo) as codigo from historicopontos hp \n"
                + "inner join cliente c on c.codigo = hp.cliente \n"
                + "where codigomatricula = " + matricula, con)) {

            if (rs.getInt("codigo") > 0) {
                Double valor;
                try (ResultSet rsNovo = SuperFacadeJDBC.criarConsulta("SELECT pontostotal from historicopontos where codigo = " + rs.getInt("codigo"), con)) {
                    valor = rsNovo.getDouble("pontostotal");
                }
                retorno = valor.intValue();
            }
        }

        return retorno;
    }

    public int nrCreditosUsadosDiaFaltaUtilizacao(int codigoContrato, Date dataReferencia) throws Exception { // avalia aulas extras
        StringBuilder sql = new StringBuilder();
        sql.append(" select sum(quantidade)*-1 AS qtde from controlecreditotreino \n");
        sql.append(" WHERE   contrato = ").append(codigoContrato);
        sql.append(" AND tipooperacaocreditotreino in (").append(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo());
        sql.append(",").append(TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo()).append(") \n");
        sql.append(" AND dataoperacao::date = '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    //metodo para realizar validação entre os créditos e as aulas disponiveis
    public Integer diferencaEntreCreditosAulas(Integer matricula) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.codigocontrato "
                + " FROM situacaoclientesinteticodw sc where matricula = " + matricula, con)) {
            if (rs.next()) {
                Integer contrato = rs.getInt("codigocontrato");
                Integer saldo = rs.getInt("saldoCreditoTreino");
                ContratoVO contratoVO = getContratoDao().consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                List<ContratoModalidadeTurmaVO> modalidadesTurmas = montarTodosContratoModalidadeTurmaCreditoTreino(contrato, null);
                Map<Integer, Date> mapaDia = contratoVO.verificarTotalCreditoPossivelUtilizar(Calendario.hoje(), modalidadesTurmas, obterUltimaDataMatriculaAluno(contratoVO), null);
                Integer nrAulasRestantesPossiveis = 0;  // quantidade de aulas do aluno dentro da vigencia da turma
                Integer nrAulasDesmarcadasPraFrente = calcularDesmarcadosReposicoes(contrato, Calendario.hoje(), false, false); // aulas futuras que foram desmarcadas
                Integer nrAulasDesmarcadas = calcularDesmarcadosReposicoes(contrato, null, true, null); // aulas desmarcadas aguardando reposicao(ainda não debitaram crédito)
                Set<Map.Entry<Integer, Date>> set = mapaDia.entrySet();
                for (Map.Entry<Integer, Date> ent : set) {
                    nrAulasRestantesPossiveis = ent.getKey();
                }
                Integer nrAulasExtraMarcadas = numeroAulasExtraMarcadas(contrato); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrReposicoesFuturasAulaDermacadas = nrReposicoesFututasDeAulasDesmarcadas(contrato); //reposicões futuras de aulas desmarcadas(ainda não debitaram crédito)
                Integer quantidadeDeAulasFuturasNasTurmas = nrAulasDesmarcadasPraFrente > nrAulasRestantesPossiveis ? 0
                        : (nrAulasRestantesPossiveis - nrAulasDesmarcadasPraFrente); // Quantidade de aulas possívies menos desmarcações futuras.

                //sistema considera nos calculos acima, aula e reposições de hoje para frente. Caso o aluno tenha consumido a aula  ou reposicão de hoje
                // Essa aula/reposicao de hoje, já consumiu crédito e deve ser subtraída. Como falta só é computada de madruga, em primeiro momento não é necessário verificar
                // faltas. Mas consigerando uma melhoria futura, onde um processo rodasse de hora em hora já dando falta para alunos de turma que já passaram do horário,
                // irei verificar, mas como disse, do modo que está hoje não interfere. Ajuste manuais serão desconsiderados, pois afetam não tem ligações com as aulas de hoje.
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(contrato, Calendario.hoje());
                Integer nrAulasExtras = saldo - quantidadeDeAulasFuturasNasTurmas - nrAulasExtraMarcadas - nrAulasDesmarcadas - nrReposicoesFuturasAulaDermacadas - nrCreditosUsadosNoDia;
                return nrAulasExtras;
            }
        }
        return 0;
    }

    public boolean validarUsuarioTemPermissao(Integer codigoUsuario, Integer empresa, String permissao, String descricao) {
        try {
            UsuarioVO usuario = getUsuarioDao().consultarPorChavePrimaria(codigoUsuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (usuario.getAdministrador()) {
                return true;
            }
            PerfilAcessoVO perfil = getPerfilAcessoDAO().consultarPorUsuarioEmpresa(codigoUsuario, empresa, Uteis.NIVELMONTARDADOS_TODOS);
            getControleAcessoDAO().verificarPermissaoUsuarioFuncionalidade(perfil, usuario, permissao, descricao);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public PerfilAcessoInterfaceFacade getPerfilAcessoDAO() throws Exception {
        if (perfilAcessoDAO == null) {
            perfilAcessoDAO = new PerfilAcesso(getCon());
        }
        return perfilAcessoDAO;
    }

    public void setPerfilAcessoDAO(PerfilAcessoInterfaceFacade perfilAcessoDAO) {
        this.perfilAcessoDAO = perfilAcessoDAO;
    }

    public ControleAcesso getControleAcessoDAO() throws Exception {
        if (controleAcessoDAO == null) {
            controleAcessoDAO = new ControleAcesso(getCon());
        }
        return controleAcessoDAO;
    }

    public void setControleAcessoDAO(ControleAcesso controleAcessoDAO) {
        this.controleAcessoDAO = controleAcessoDAO;
    }

    public FeriadoInterfaceFacade getFeriadoDAO() throws Exception {
        if (feriadoDAO == null) {
            feriadoDAO = new Feriado(getCon());
        }
        return feriadoDAO;
    }

    public ItemCampanhaInterfaceFacade getItenCampanhaDao() throws Exception {
        if (itenCampanhaDao == null)
            itenCampanhaDao = new ItemCampanha(getCon());
        return itenCampanhaDao;
    }

    public ConfiguracaoSistemaInterfaceFacade getConfiguracaoDao() throws Exception {
        configuracaoDao = (new ConfiguracaoSistema(getCon()));
        return configuracaoDao;
    }

    public void setFeriadoDAO(FeriadoInterfaceFacade feriadoDAO) {
        this.feriadoDAO = feriadoDAO;
    }


    @Override
    public String inserirReposicaoAlunoExperimental(final String key, final Date dia,
                                                    final Integer idHorarioTurma,
                                                    final Integer codigoCliente, final Integer usuario,
                                                    final Integer origemSistema, final Integer empresa, final Integer produtoFreePass) throws Exception {
        try {
            AgendaTotalJSON json = consultarUmaTurma(idHorarioTurma, dia);
            if (json.getNrVagasPreenchidas() != null && json.getNrVagas() <= json.getNrVagasPreenchidas()) {
                throw new Exception("A aula já está cheia!");
            }
            GestaoAulaService service = new GestaoAulaService(con, key);
            boolean produtoDiaria = false;
            boolean temDiaria = false;
            if (UteisValidacao.notEmptyNumber(produtoFreePass)) {
                produtoDiaria = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM produto WHERE tipoproduto = 'DI' and codigo = " + produtoFreePass, con).next();
                if (produtoDiaria) {
                    temDiaria = getHorarioDao().temDiaria(codigoCliente, json.getCodigoTipo(), dia);
                }
            }

            ResultSet rsPessoa;
            Integer turma;
            Boolean coletiva = false;
            try (ResultSet rsHorarioTurma = SuperFacadeJDBC.criarConsulta("SELECT turma FROM horarioturma WHERE codigo = " + idHorarioTurma, con)) {
                rsPessoa = SuperFacadeJDBC.criarConsulta("SELECT pessoa, codigomatricula FROM cliente WHERE codigo = " + codigoCliente, con);
                turma = 0;
                if (rsHorarioTurma.next()) {
                    turma = rsHorarioTurma.getInt("turma");
                    try (ResultSet rsTurma = SuperFacadeJDBC.criarConsulta("select aulacoletiva from turma where codigo = " + turma, con)) {
                        coletiva = rsTurma.next() && rsTurma.getBoolean("aulacoletiva");
                    }
                }
            }
            ReposicaoVO reposicao = new ReposicaoVO();
            reposicao.setOrigemSistemaEnum(OrigemSistemaEnum.getOrigemSistema(origemSistema));
            if (usuario == null || usuario == 0) {
                reposicao.setUsuario(getUsuarioDao().getUsuarioRecorrencia());
            } else {
                reposicao.setUsuario(new UsuarioVO());
                reposicao.getUsuario().setCodigo(usuario);
            }
            reposicao.setDataLancamento(dataComTimezone(empresa));
            reposicao.setDataOrigem(dia);
            reposicao.setDataReposicao(dia);
            reposicao.setCliente(new ClienteVO());
            reposicao.getCliente().setCodigo(codigoCliente);
            reposicao.getCliente().getEmpresa().setCodigo(empresa);

            if (rsPessoa.next()) {
                reposicao.getCliente().getPessoa().setCodigo(rsPessoa.getInt("pessoa"));
            }
            reposicao.setHorarioTurma(getHorarioDao().consultarPorChavePrimaria(idHorarioTurma, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            reposicao.setTurmaDestino(getTurmaDao().consultarPorChavePrimaria(turma, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            reposicao.setHorarioTurmaOrigem(reposicao.getHorarioTurma());
            reposicao.setTurmaOrigem(reposicao.getTurmaDestino());

            reposicao.setMarcacaoAula(true);
            reposicao.setAulaExperimental(true);
            reposicao.setProdutoFreePass(produtoFreePass);

            if (getReposicaoDao().existeReposicaoAlunoNaqueleDia(reposicao.getCliente().getPessoa().getCodigo(), reposicao.getHorarioTurma(), dia)) {
                throw new Exception("O aluno já está matriculado nessa aula!");
            }
            ClienteVO clienteVO = getClienteDao().consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
            reposicao.setAulaExperimental(!temDiaria && !produtoDiaria);
            if (!temDiaria && produtoDiaria) {
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### INCLUSÃO DE DIÁRIA VIA inserirReposicaoAlunoExperimental - TurmasServiceImpl.java ");
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Matrícula " + clienteVO.getMatricula());
                ProdutoVO produtoVO = getProdutoDao().obterProdutoCfgEmpresa(produtoFreePass, empresa);
                AulaAvulsaDiariaVO diariaVO = new AulaAvulsaDiariaVO();
                diariaVO.setEmpresa(reposicao.getCliente().getEmpresa());
                diariaVO.setCliente(clienteVO);
                diariaVO.setDataInicio(dia);
                diariaVO.setDataLancamento(dataComTimezone(empresa));
                diariaVO.setDataRegistro(dia);
                diariaVO.getModalidade().setCodigo(json.getCodigoTipo());
                diariaVO.setNomeComprador(clienteVO.getPessoa().getNome());
                diariaVO.setProduto(produtoVO);
                diariaVO.setResponsavel(reposicao.getUsuario());
                diariaVO.setValor(produtoVO.getValorFinal());
                getAulaAvulsaDiariaDao().incluirSemCommit(diariaVO, OrigemSistemaEnum.getOrigemSistema(origemSistema));
                Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### FINALIZOU INCLUSÃO DE DIÁRIA VIA inserirReposicaoAlunoExperimental - TurmasServiceImpl.java ");
            }
            if (coletiva) {
                Integer alunoAulaColetiva = inserirAlunoAulaColetiva(codigoCliente,
                        idHorarioTurma,
                        dia,
                        OrigemSistemaEnum.getOrigemSistema(origemSistema),
                        usuario, null, key);
                getAgendaDao().gravarAgendamentoAulaColetivaExperimental(dia, json.getInicio(), codigoCliente,
                        json.getCodigoTipo(),
                        json.getEmpresa(),
                        produtoFreePass,
                        usuario,
                        alunoAulaColetiva,
                        (temDiaria || produtoDiaria) ? TipoAgendaEnum.AULA_DIARIA : TipoAgendaEnum.AULA_EXPERIMENTAL);
            } else {
                service.marcarAula(reposicao, (temDiaria || produtoDiaria));
            }
            try {
                json.setMatricula(Integer.valueOf(clienteVO.getMatricula()));
                json.setNomeAluno(clienteVO.getPessoa().getNome());
                logAluno(idHorarioTurma + "_" + Calendario.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"),
                        usuario,
                        clienteVO.getPessoa().getCodigo(),
                        json,
                        origemSistema,
                        false,
                        coletiva,
                        true, false, clienteVO.getCodigo());
            } catch (Exception e) {
                Uteis.logar(e, TurmasServiceImpl.class);
            }
            return "sucesso";
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    public Integer inserirAlunoAulaColetiva(Integer cliente,
                                            Integer horarioTurma,
                                            Date dia,
                                            OrigemSistemaEnum origemSistema,
                                            Integer usuario,
                                            Integer autorizado,
                                            final String key) throws Exception {
        AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
        alunoHorario.setExperimental(true);
        alunoHorario.setDesafio(false);
        alunoHorario.setCliente(cliente);
        alunoHorario.setHorarioTurma(new HorarioTurmaVO());
        alunoHorario.getHorarioTurma().setCodigo(horarioTurma);
        alunoHorario.setData(dia);
        alunoHorario.setOrigemSistema(origemSistema);

        Integer codigoEmpresa = null;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "SELECT t.empresa FROM horarioturma ht INNER JOIN turma t ON t.codigo = ht.turma WHERE ht.codigo = " + horarioTurma, con)) {
            if (rs.next()) {
                codigoEmpresa = rs.getInt("empresa");
            }
        }

        if (codigoEmpresa != null) {
            alunoHorario.setDatalancamento(new java.sql.Timestamp(dataComTimezone(codigoEmpresa).getTime()));
        } else {
            alunoHorario.setDatalancamento(Calendario.hoje());
        }

        alunoHorario.setUsuario(usuario);
        alunoHorario.setAutorizado(autorizado);
        getHorarioDao().incluirAlunoAulaCheia(alunoHorario, null, key);

        AgendaTotalJSON json = consultarUmaTurma(horarioTurma, dia);
        String[] inicio = json.getInicio().split(" ");
        try {
            chamarNotificacaoPushEntrouNaAula(
                    key,
                    alunoHorario.getCliente(),
                    "Tá na agenda " + String.valueOf(Character.toChars(9989)),
                    firstLetterUpper(json.getTitulo().toLowerCase()) + " agendada em " + inicio[0] + " às " + inicio[1] + ". Aproveite!"
            );
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return alunoHorario.getCodigo();
    }

    @Override
    public void marcarPresenca(final String key, final Date dia,
                               final Integer idHorarioTurma,
                               final Integer codigoCliente,
                               boolean reposicao, boolean desmarcar,
                               Integer codColaborador, OrigemSistemaEnum origem) throws Exception {
        if (Calendario.maior(dia, Calendario.hoje())) {
            throw new Exception("Não é permitido marcar presença para datas futuras");
        }

        ClienteVO cliente = getClienteDao().consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
        verificarSituacaoAluno(cliente.getCodigoMatricula(), null);

        String chavePrimaria = "";
        StringBuilder descricao = new StringBuilder();
        Boolean conteudoLogOk = false;
        UsuarioVO usuarioVO = new UsuarioVO();
        try {
            try {
                if (origem != null && origem.equals(OrigemSistemaEnum.APP_TREINO) && codColaborador == 0) {
                    usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                } else {
                    usuarioVO = getUsuarioDao().consultarPorColaborador(codColaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }
            String descricaoOrigem = origem != null ? origem.getDescricao() : "Não informada";
            chavePrimaria = idHorarioTurma + "_" + Calendario.getData(dia, "dd/MM/yyyy");
            descricao.append("<br/>[matricula: ").append(cliente.getCodigoMatricula()).append("]<br/>");
            descricao.append("[aluno: ").append(cliente.getPessoa().getNome()).append("]<br/>");
            descricao.append("[origem: ").append(descricaoOrigem).append("]");
            conteudoLogOk = true;
        } catch (Exception ex) {
            Uteis.logar(ex, TurmasServiceImpl.class);
            conteudoLogOk = false;
        }

        if (desmarcar && !reposicao) {
            try (ResultSet rsPessoa = SuperFacadeJDBC.criarConsulta("select pessoa from cliente where codigo = " + codigoCliente, con)) {
                if (rsPessoa.next()) {
                    PresencaVO presencaVO = getPresencaDao().consultarPorPessoaHorarioPeriodoPresenca(rsPessoa.getInt("pessoa"),
                            idHorarioTurma, dia, dia, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    getPresencaDao().excluir(presencaVO);
                    processarAcesso(key, codigoCliente, presencaVO.getDataPresenca(), desmarcar, idHorarioTurma);

                    try {
                        if (conteudoLogOk) {
                            incluirLog(chavePrimaria, "PRESENCA", "Presença Desconfirmada", usuarioVO, "ALTERAÇÃO",
                                    "Presença Desconfirmada", "", descricao.toString(), null, codigoCliente);
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, TurmasServiceImpl.class);
                    }

                    try {
                        MgbServiceImpl mgbService = new MgbServiceImpl(getCon());
                        if (cliente.getEmpresa() != null && !UteisValidacao.emptyNumber(cliente.getEmpresa().getCodigo()) && mgbService.integradoMgb(cliente.getEmpresa().getCodigo())) {
                            mgbService.presencaAlunoTurmaMgb(cliente.getEmpresa().getCodigo(), cliente.getCodigoMatricula(), idHorarioTurma, dia, false);
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, TurmasServiceImpl.class);
                    }
                }
            }
        } else if (reposicao) {
            tratarAulaReposicaoOuExperimental(key, dia, idHorarioTurma, codigoCliente, 0, desmarcar);
            try {
                if (conteudoLogOk) {
                    incluirLog(chavePrimaria, "PRESENCA", desmarcar ? "Presença Desconfirmada" : "Presença Confirmada",
                            usuarioVO, "ALTERAÇÃO", desmarcar ? "Presença Desconfirmada" : "Presença Confirmada",
                            descricao.toString(), "", null, codigoCliente);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }
        } else {
            String sql = "select codigo, empresa, contrato from matriculaalunohorarioturma " +
                    " where horarioturma = " + idHorarioTurma +
                    " and pessoa in (select pessoa from cliente where codigo = " + codigoCliente + ") " +
                    " and '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "' between datainicio and datafim";
            try (ResultSet rsMaht = SuperFacadeJDBC.criarConsulta(sql, con)) {
                if (rsMaht.next()) {
                    Integer codigoMaht = rsMaht.getInt("codigo");
                    Integer codigoEmpresa = rsMaht.getInt("empresa");
                    Integer codigoContrato = rsMaht.getInt("contrato");
                    PresencaVO presencaVO = new PresencaVO();
                    presencaVO.setDataChamada(Calendario.hoje());
                    presencaVO.setDataPresenca(dia);
                    presencaVO.setDadosTurma(codigoMaht);
                    getPresencaDao().incluir(presencaVO);
                    processarAcesso(key, codigoCliente, presencaVO.getDataPresenca(), desmarcar, idHorarioTurma, codigoEmpresa, codigoContrato);

                    try {
                        if (conteudoLogOk) {
                            incluirLog(chavePrimaria, "PRESENCA", "Presença Confirmada", usuarioVO, "ALTERAÇÃO",
                                    "Presença Confirmada", descricao.toString(), "", null, codigoCliente);
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, TurmasServiceImpl.class);
                    }

                    try {
                        MgbServiceImpl mgbService = new MgbServiceImpl(getCon());
                        if (cliente.getEmpresa() != null && !UteisValidacao.emptyNumber(cliente.getEmpresa().getCodigo()) && mgbService.integradoMgb(cliente.getEmpresa().getCodigo())) {
                            mgbService.presencaAlunoTurmaMgb(cliente.getEmpresa().getCodigo(), cliente.getCodigoMatricula(), idHorarioTurma, dia, true);
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, TurmasServiceImpl.class);
                    }
                }
            }
        }
    }

    private void tratarAulaReposicaoOuExperimental(String key, Date dia, Integer idHorarioTurma, Integer codigoCliente, Integer codigoPassivo, boolean desmarcar) throws Exception {
        String paramCliente = "cliente";
        if(!UteisValidacao.emptyNumber(codigoPassivo)){
            paramCliente = "passivo";
        }
        Reposicao reposicaoDao = new Reposicao(con);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from reposicao where datareposicao = '" +
                Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") +
                "' and horarioturma = " + idHorarioTurma +
                " and "+paramCliente+" = " + codigoCliente, con)) {
            if (rs.next()) {
                reposicaoDao.atualizarPresenca(rs.getInt("codigo"), desmarcar ? null : dia);
                if(!UteisValidacao.emptyNumber(codigoCliente)) {
                    processarAcesso(key, codigoCliente, dia, desmarcar, idHorarioTurma);
                }
            }
        }
    }

    public void processarAcesso(String chave, Integer codcliente, Date dataAcesso, boolean desmarcar, Integer idHorarioTurma) throws Exception {
        processarAcesso(chave, codcliente, dataAcesso, desmarcar, idHorarioTurma, null, null);
    }

    public void processarAcesso(String chave, Integer codcliente, Date dataAcesso, boolean desmarcar, Integer idHorarioTurma, Integer codigoEmpresa, Integer codigoContrato) throws Exception {
        ClienteVO cliente = getClienteDao().consultarPorChavePrimaria(codcliente, Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
        ContratoVO contratoVO = null;
        if (codigoContrato != null ){
            contratoVO = getContratoDao().consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        HorarioTurmaVO horarioTurma = getHorarioDao().consultarPorChavePrimaria(idHorarioTurma, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Integer codigoEmpresaCadastroCliente = cliente.getEmpresa().getCodigo();
        //Registrar acesso na empresa onde o contrato está vinculado para multiempresa.
        if (!UteisValidacao.emptyObject(contratoVO) && !UteisValidacao.emptyNumber(codigoEmpresa) && contratoVO.getEmpresa().getCodigo().equals(codigoEmpresa)
                && !codigoEmpresa.equals(codigoEmpresaCadastroCliente)){
            codigoEmpresaCadastroCliente = codigoEmpresa;
        }
        EmpresaVO emp = getEmpresaDao().consultarPorChavePrimaria(codigoEmpresaCadastroCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (emp.isAcessoChamada()) {
            AcessoClienteVO ac = getAcessoDao().consultarAcessoHorarioTurma(cliente, dataAcesso,
                    MeioIdentificacaoEnum.LISTACHAMADA, horarioTurma, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
            ControleCreditoTreino ccDao = new ControleCreditoTreino(con);
            boolean descontouCreditoAoMarcarAula = ccDao.descontouCreditoAoMarcarAula(codcliente, horarioTurma, dataAcesso);
            // se nao tem acesso para este dia
            if (UteisValidacao.emptyNumber(ac.getCodigo()) && !desmarcar && !descontouCreditoAoMarcarAula) {
                cliente.setUsuarioAux(zillyonWebFacade.getUsuarioRecorrencia());
                cliente.setUaCliente(getAcessoDao().consultarPorCodigo(cliente.getUaCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                dataAcesso = Uteis.getDataHoraJDBC(dataAcesso, horarioTurma.getHoraInicial());
                AcessoClienteVO acResumido = getAcessoDao().registrarAcessoCliente(dataAcesso,
                        cliente, SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO,
                        DirecaoAcessoEnum.DA_ENTRADA, emp.getLocalAcessoChamada(),
                        emp.getColetorChamada(), null, MeioIdentificacaoEnum.LISTACHAMADA,
                        ccDao, chave);
                if (UteisValidacao.emptyNumber(cliente.getUaCliente().getCodigo()) || Calendario.maior(dataAcesso, cliente.getUaCliente().getDataHoraEntrada())) {
                    getClienteDao().registrarUltimoAcesso(cliente.getCodigo(), acResumido.getCodigo());
                    getSwDao().registrarUltimoAcesso(cliente.getCodigo(), acResumido.getDataHoraEntrada());
                }
            } else if (UteisValidacao.notEmptyNumber(ac.getCodigo()) && desmarcar) {
                String observacao = "Desmarcação de presença lista chamada.";
                getAcessoDao().excluir(ac, observacao, ccDao);
                cliente.setUaCliente(getAcessoDao().consultarUltimoAcesso(cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                getClienteDao().registrarUltimoAcesso(cliente.getCodigo(), cliente.getUaCliente().getCodigo());
                getSwDao().registrarUltimoAcesso(cliente.getCodigo(), cliente.getUaCliente().getDataHoraEntrada());
            }
            zillyonWebFacade = null;
            ccDao = null;
        }
    }


    public JSONArray alunosPresentesTurma(Integer horarioturma, Date dia) throws Exception {
        JSONArray array = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append(" select cli.codigo as cliente from presenca p ");
        sql.append(" inner join matriculaalunohorarioturma maht on maht.codigo = p.dadosturma ");
        sql.append(" inner join cliente cli on cli.pessoa = maht.pessoa ");
        sql.append(" where p.datapresenca  = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd"));
        sql.append("' and maht.horarioturma = ").append(horarioturma);
        try (ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (resultSet.next()) {
                array.put(resultSet.getInt("cliente"));
            }
        }
        return array;
    }

    public int reposicoesFuturasParaUtilizarContratosCreditoSemTurma(int codigoContrato, Date dataReferencia) throws Exception { // avalia aulas de reposicação que poderão consumir crédito
        Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(codigoContrato, dataReferencia);
        StringBuilder sql = new StringBuilder();
        sql.append(" select count(codigo) as qtde from reposicao \n");
        sql.append(" WHERE   contrato = ").append(codigoContrato);
        sql.append(" and datareposicao::date >= '").append(Uteis.getDataAplicandoFormatacao(dataReferencia, "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
                numeroAulas -= nrCreditosUsadosNoDia;
            }
        }
        return numeroAulas;
    }

    @Override
    public List<AgendaTotalJSON> consultarAulas(ClienteVO cliente, Integer contrato) throws Exception {

        Map<Date, List<Integer>> mapaAgendamentosDesmarcados = getAulaDesmarcadaDao().consultarAgendamentosDesmarcados(cliente.getCodigo());
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();

        aulas.addAll(getMatriculaHorarioDao().consultarProximasAulasModalidadesDiferentes(Integer.parseInt(cliente.getMatricula()), mapaAgendamentosDesmarcados));

        return Ordenacao.ordenarLista(aulas, "inicio");
    }

    public Integer nrReposicoesContratoAluno(Integer matricula) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.codigocontrato "
                + " FROM situacaoclientesinteticodw sc where matricula = " + matricula, con)) {
            if (rs.next()) {
                Integer codigoContrato = rs.getInt("codigocontrato");
                StringBuilder sql = new StringBuilder();
                sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
                sql.append(" WHERE  r.contrato = ").append(codigoContrato);
                ResultSet rsRepo = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                int numeroAulas = 0;
                while (rsRepo.next()) {
                    numeroAulas = rsRepo.getInt("qtde");
                }
                return numeroAulas;
            }
        }
        return 0;
    }

    public FecharMetaDetalhadoInterfaceFacade getFecharMetaDetalhadoDao() throws Exception {
        if (fecharMetaDetalhadoDao == null) {
            fecharMetaDetalhadoDao = new FecharMetaDetalhado(getCon());
        }
        return fecharMetaDetalhadoDao;
    }

    private boolean verificarRestricoesMarcacao(Integer codigoCliente, AgendaTotalJSON json,
                                                Date data, boolean permAlunoMarcarAulaOutraEmpresa,
                                                boolean validarModalidade, boolean validarRestricoesMarcacao,
                                                int nrAulaExperimentais, String tokenGymPass, Boolean controlarFreePass) throws Exception {
        boolean usarReposicao = false;
        boolean semRestricoes = true;
        Integer tipoModalidade = 0;
        Integer codigoModalidade = 0;
        String sqlTipoModalidade = "select codigo, tipo from modalidade where codigo = " + json.getCodigoTipo();
        try (ResultSet rsTipoModalidade = SuperFacadeJDBC.criarConsulta(sqlTipoModalidade, con)) {
            if (rsTipoModalidade.next()) {
                if (!permAlunoMarcarAulaOutraEmpresa) {
                    codigoModalidade = rsTipoModalidade.getInt("codigo");
                } else {
                    tipoModalidade = rsTipoModalidade.getInt("tipo");
                    if (UteisValidacao.emptyNumber(tipoModalidade)) {
                        codigoModalidade = rsTipoModalidade.getInt("codigo");
                    }
                }
            }
        }

        String sqlConsulta = sqlConsultaRestricoes(codigoCliente, data, permAlunoMarcarAulaOutraEmpresa, tipoModalidade, codigoModalidade);

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlConsulta, con)) {
            boolean temContrato = false;
            int vezessemana = 0;
            boolean restringirmarcacaoaulascoletivas = false;
            boolean restringirMarcacaoAulaPorNrVezesModalidade = false;
            int restringirqtdmarcacaoporDiageral = 0;
            int restringirqtdmarcacaoporDia = 0;
            Date vigenciaContrato = null;
            while (rs.next()) {
                temContrato = true;
                vezessemana += rs.getInt("vezessemana");
                if (!restringirmarcacaoaulascoletivas) {
                    restringirmarcacaoaulascoletivas = rs.getBoolean("restringirmarcacaoaulascoletivas");
                }
                restringirqtdmarcacaoporDia += rs.getInt("restringirqtdmarcacaoporpia");
                restringirqtdmarcacaoporDiageral += rs.getInt("restringirqtdmarcacaopordiageral");
                restringirMarcacaoAulaPorNrVezesModalidade = rs.getBoolean("restringirMarcacaoAulaPorNrVezesModalidade");
                vigenciaContrato = rs.getDate("vigenciade");
            }

            if (temContrato) {
                if (validarRestricoesMarcacao) {

                    if (restringirMarcacaoAulaPorNrVezesModalidade) {
                        semRestricoes= false;
                        Date inicio = Uteis.obterPrimeiroEUltimoDiaSemana(Boolean.TRUE, data);
                        Date fim = Uteis.obterPrimeiroEUltimoDiaSemana(Boolean.FALSE, data);
                        int aulasMarcadas = getHorarioDao().nrAulasColetivasMarcadasAlunoPorModalidadePeriodoContabilizadoPorDia(
                                json.getCodigoTipo(),
                                codigoCliente,
                                inicio, fim, true);

                        if (vezessemana <= aulasMarcadas) {
                            int reposicoesDisponiveisAulaColetiva = getHorarioDao().reposicoesDisponiveisAulaColetiva(codigoCliente);
                            if (reposicoesDisponiveisAulaColetiva > 0) {
                                usarReposicao = true;
                            } else {
                                throw new Exception("O número de aulas marcadas na semana para a modalidade "
                                        + json.getTipo() + ", já foi atingido. O total permitido é de " + vezessemana);
                            }
                        } else {
                            semRestricoes = true;
                        }
                    }

                    if (restringirmarcacaoaulascoletivas) {
                        semRestricoes = false;
                        Date inicio = Uteis.obterPrimeiroEUltimoDiaSemana(Boolean.TRUE, data);
                        Date fim = Uteis.obterPrimeiroEUltimoDiaSemana(Boolean.FALSE, data);
                        int aulasMarcadas = getHorarioDao().nrAulasColetivasMarcadasAlunoPorModalidadePeriodo(json.getCodigoTipo(), vigenciaContrato,
                                codigoCliente,
                                data,
                                inicio, fim, true);
                        if (vezessemana <= aulasMarcadas) {
                            int reposicoesDisponiveisAulaColetiva = getHorarioDao().reposicoesDisponiveisAulaColetiva(codigoCliente);
                            if (reposicoesDisponiveisAulaColetiva > 0) {
                                usarReposicao = true;
                            } else {
                                throw new Exception("O número de aulas marcadas na semana para a modalidade "
                                        + json.getTipo() + ", já foi atingido. O total permitido é de " + vezessemana);
                            }
                        } else {
                            semRestricoes = true;
                        }
                    }

                    if (restringirqtdmarcacaoporDia > 0) {
                        semRestricoes = false;
                        int aulasMarcadasDia = getHorarioDao().nrAulasColetivasMarcadasAlunoPorModalidadePeriodo(
                                json.getCodigoTipo(), null,
                                codigoCliente,
                                null,
                                data, data, false);
                        if (aulasMarcadasDia >= restringirqtdmarcacaoporDia) {
                            throw new Exception("Já foi marcada " + aulasMarcadasDia + " aula(s) nesse dia para a modalidade "
                                    + json.getTipo() + ". O plano só permite " + restringirqtdmarcacaoporDia + " aula(s) por dia dessa modalidade.");
                        } else {
                            semRestricoes = true;
                        }
                    }

                    if (restringirqtdmarcacaoporDiageral > 0) {
                        semRestricoes = false;
                        int aulasMarcadasDia = getHorarioDao().nrAulasColetivasMarcadasAlunoPorPeriodo(
                                codigoCliente,
                                null,
                                data, data);
                        if (aulasMarcadasDia >= restringirqtdmarcacaoporDiageral) {
                            throw new Exception("Já foi marcada " + aulasMarcadasDia + " aula(s) nesse dia. O plano só permite "
                                    + restringirqtdmarcacaoporDiageral + " aula(s) por dia.");
                        } else {
                            semRestricoes = true;
                        }
                    }
                }
            } else if (!retornaValidaAlunoGymPass(codigoCliente) && UteisValidacao.emptyString(tokenGymPass)) {
                throw new Exception("Não foi possível agendar essa aula! " +
                        "Realize o check in na GymPass e tente novamente ou entre em contato com a recepção.");
            }else if (validarModalidade && controlarFreePass) {
                throw new Exception("Você não possui a modalidade, entre em contato com a recepção para agendar uma aula experimental.");
            }else if (validarModalidade && nrAulaExperimentais > 0 && UteisValidacao.emptyString(tokenGymPass)) {
                throw new Exception("AULAEXPERIMENTAL O aluno não possui a modalidade, deseja usar uma de suas " + nrAulaExperimentais + " aulas experimentais?");
            } else if (validarModalidade && nrAulaExperimentais == 0) {
                throw new Exception("O aluno não possui a modalidade e não tem mais aulas experimentais.");
            } else { // valida restrição de plano para modalidades que não estão no contrato
                String planoRestricao = "select p.restringirqtdmarcacaoporpia, p.restringirqtdmarcacaopordiageral "
                        + "from contrato con "
                        + "inner join cliente cli on cli.pessoa = con.pessoa "
                        + "inner join plano p on p.codigo = con.plano "
                        + " where cli.codigo = " + codigoCliente
                        + " and con.vigenciaateajustada >=  '" + Uteis.getDataFormatoBD(data) + "' and restringirqtdmarcacaoporpia > 0 order by con.vigenciade limit 1 ";
                try (ResultSet rsRestricao = SuperFacadeJDBC.criarConsulta(planoRestricao, con)) {
                    if (rsRestricao.next()) {
                        int restringirqtdmarcacaoporpia = rsRestricao.getInt("restringirqtdmarcacaoporpia");
                        if (restringirqtdmarcacaoporpia > 0) {
                            int aulasMarcadasDia = getHorarioDao().nrAulasColetivasMarcadasAlunoPorModalidadePeriodo(json.getCodigoTipo(), null,
                                    codigoCliente,
                                    null,
                                    data, data, false);
                            if (aulasMarcadasDia >= restringirqtdmarcacaoporpia) {
                                throw new Exception("Já foi marcada uma aula nesse dia para a modalidade " + json.getTipo() + ". O plano só permite uma aula por dia dessa modalidade.");
                            }
                        }
                        int restringirqtdmarcacaopordiageral = rsRestricao.getInt("restringirqtdmarcacaopordiageral");
                        if (restringirqtdmarcacaopordiageral > 0) {
                            int aulasMarcadasDia = getHorarioDao().nrAulasColetivasMarcadasAlunoPorPeriodo(
                                    codigoCliente,
                                    null,
                                    data, data);
                            if (aulasMarcadasDia >= restringirqtdmarcacaopordiageral) {
                                throw new Exception("Já foi marcada uma aula nesse dia para a modalidade " + json.getTipo() + ". O plano só permite uma aula por dia dessa modalidade.");
                            }
                        }
                    }
                }
            }
        }

        return usarReposicao || semRestricoes;
    }

    private static String sqlConsultaRestricoes(Integer codigoCliente, Date data, boolean permAlunoMarcarAulaOutraEmpresa, Integer tipoModalidade, Integer codigoModalidade) {
        String sqlConsulta = "select p.restringirqtdmarcacaoporpia, p.restringirmarcacaoaulascoletivas, "
                + "p.restringirqtdmarcacaopordiageral, vezessemana, p.restringirMarcacaoAulaPorNrVezesModalidade, "
                + "con.vigenciade "
                + "from contrato con "
                + "inner join cliente cli on cli.pessoa = con.pessoa "
                + "inner join plano p on p.codigo = con.plano "
                + "inner join contratomodalidade cm on cm.contrato = con.codigo "
                + "inner join modalidade m on cm.modalidade = m.codigo";
        sqlConsulta += " where cli.codigo = " + codigoCliente;

        if (!permAlunoMarcarAulaOutraEmpresa || UteisValidacao.emptyNumber(tipoModalidade)) {
            sqlConsulta += " and m.codigo = " + codigoModalidade;
        } else {
            sqlConsulta += " and coalesce(m.tipo,0) = " + tipoModalidade;
        }

        sqlConsulta += " and con.vigenciade <=  '" + Uteis.getDataFormatoBD(data) + "' ";
        sqlConsulta += " and con.vigenciaateajustada >=  '" + Uteis.getDataFormatoBD(data) + "' order by con.vigenciade  ";
        return sqlConsulta;
    }

    public String atualizarConfiguaracaoNrAulasExperimentaisSintetico(int nrAulasExperimentaisAntesAlteracao, int valorNovo) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw\n" +
                "SET nraulaexperimental = \n" +
                "(CASE \n" +
                "   WHEN (nraulaexperimental IS NULL) THEN " + valorNovo + "\n" +
                "   WHEN ((nraulaexperimental - " + nrAulasExperimentaisAntesAlteracao + ") + " + valorNovo + ") < 0 THEN 0 \n" +
                "   ELSE ((nraulaexperimental - " + nrAulasExperimentaisAntesAlteracao + ") + " + valorNovo + ") \n" +
                "END);";

        boolean atualizado = SuperFacadeJDBC.executarConsulta(sql, con);
        return atualizado ? "OK" : "ERRO";
    }

    public String atualizarConfiguaracaoNrAulasExperimentaisSinteticoTodos(int valorNovo) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw \n" +
                "SET nraulaexperimental = " + valorNovo + ";";

        boolean atualizado = SuperFacadeJDBC.executarConsulta(sql, con);
        return atualizado ? "OK" : "ERRO";
    }

    @Override
    public List<TurmaAulaCheiaJSON> obterAulasColetivasPorDiaSemana(Integer empresa, Integer dia) throws Exception {
        return getTurmaDao().obterAulasColetivasPorDiaSemana(empresa, dia, dataComTimezone(empresa));
    }

    @Override
    public List<AlunoAulaAcessoJSON> obterAlunosDaAulaComAcesso(Integer horarioTurma,
                                                                String dataAula) throws Exception {

        return getTurmaDao().obterAlunosDaAulaComAcesso(horarioTurma, dataAula);

    }

    @Override
    public Boolean validarColetorPreenchido(Integer horarioTurma) throws Exception {
        return getTurmaDao().validarColetorPreenchido(horarioTurma);
    }

    @Override
    public Boolean existeReposicao(Integer horarioTurma, Integer cliente, String dataDaAula) throws Exception {
        return getReposicaoDao().consultaReposicaoClienteDiaTurma(cliente, horarioTurma, Uteis.getDate(dataDaAula), Uteis.NIVELMONTARDADOS_DADOSBASICOS) != null;
    }

    private boolean existeParcelaVencida(final Integer empresa, final Integer codPessoa) throws Exception {
        configuracaoSistemaVO = getConfiguracaoDao().buscarPorCodigo(1, false,
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        EmpresaVO empresaVo = getEmpresaDao().consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        return (getMovParcelaDao().existeParcelaVencidaSegundoConfiguracoes(
                codPessoa, empresaVo, configuracaoSistemaVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
    }

    public List<EmpresaJSON> consultarUnidadesEmpresa() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo, nome FROM empresa WHERE ativa is true ORDER BY nome");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        List<EmpresaJSON> json = new ArrayList<EmpresaJSON>();
        while (rs.next()) {
            EmpresaJSON obj = new EmpresaJSON();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setNome(rs.getString("nome"));
            json.add(obj);
        }
        return json;
    }

    @Override
    public AlunoAulaAcessoJSON obterAlunosAcesso(AlunoAulaAcessoJSON aluno, String dataInicial, String dataFinal, int codAluno) throws Exception {

        StringBuffer sql = new StringBuffer();
        sql.append("select tipoacesso,meioidentificacaoentrada,meioidentificacaosaida from acessocliente where cliente = ")
                .append(codAluno).append(" ");
        sql.append("AND dthrentrada between '")
                .append(dataInicial).append("' ");
        sql.append("AND '")
                .append(dataFinal).append("' \n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {

            while (rs.next()) {
                aluno.setTipoacesso(rs.getInt("tipoacesso"));
                aluno.setMeioidentificacaoentrada(rs.getInt("meioidentificacaoentrada"));
                aluno.setMeioidentificacaosaida(rs.getInt("meioidentificacaosaida"));
                return aluno;
            }
        }
        return null;

    }

    public void setEmpresaDao(EmpresaInterfaceFacade empresaDao) {
        this.empresaDao = empresaDao;
    }

    public List<HorarioTurmaDTO> horariosTurma(Integer turma) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, horainicial, horafinal, diasemana from horarioturma where turma =  " + turma,
                con);
        List<HorarioTurmaDTO> horarios = new ArrayList<>();
        while (rs.next()) {
            HorarioTurmaDTO dto = new HorarioTurmaDTO();
            dto.setCodigo(rs.getInt("codigo"));
            dto.setHoraInicial(rs.getString("horainicial"));
            dto.setHoraFinal(rs.getString("horafinal"));
            dto.setDiaSemana(rs.getString("diasemana"));
            horarios.add(dto);
        }
        return horarios;
    }

    public ContratoOperacaoInterfaceFacade getContratoOperacaoDao() throws Exception {
        if (contratoOperacaoDao == null) {
            contratoOperacaoDao = new ContratoOperacao(getCon());
        }
        contratoOperacaoDao.setCon(getCon());
        return contratoOperacaoDao;
    }

    private PessoaInterfaceFacade getPessoaDao() throws Exception {
        if (pessoaDao == null) {
            pessoaDao = new Pessoa(getCon());
        }
        pessoaDao.setCon(getCon());
        return pessoaDao;
    }

    public String atualizarSpiviClientID(final Integer codigoPessoa, final Integer spiviClientID) throws Exception {
        getPessoaDao().alterarSpiviClientID(codigoPessoa, spiviClientID);
        return "ok";
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    public void desmarcarAulasOperacaoColetiva(OperacaoColetivaVO operacaoColetiva, String key) throws Exception {

        TurmaVO turmaVO = getTurmaDao().consultarPorChavePrimaria(operacaoColetiva.getTurmaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        List<MatriculaAlunoHorarioTurmaVO> matriculaAlunoHorarioTurmaVOSList = getMatriculaHorarioDao().consultarPorIdentificadorTurma(turmaVO.getIdentificador(), operacaoColetiva.getDataInicio(), operacaoColetiva.getDataFinal(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        try {
            con.setAutoCommit(false);
            for (MatriculaAlunoHorarioTurmaVO mt : matriculaAlunoHorarioTurmaVOSList) {
                ClienteVO cliente = getClienteDao().consultarPorCodigoPessoa(mt.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Date datainicial = operacaoColetiva.getDataInicio();
                if (Calendario.maiorOuIgualComHora(mt.getDataInicio(), datainicial) && Calendario.maiorOuIgualComHora(operacaoColetiva.getDataFinal(), mt.getDataInicio())) {
                    datainicial = mt.getDataInicio();
                }
                if (Calendario.entreComHora(datainicial, mt.getDataInicio(), mt.getDataFim())) {
                    while (Calendario.menorOuIgual(datainicial, operacaoColetiva.getDataFinal())) {
                        String diaSem = Calendario.getDiaDaSemanaAbreviado(datainicial);
                        if (diaSem.equals(mt.getHorarioTurma().getDiaSemana())) {
                            desmarcarAluno(key, mt.getHorarioTurma().getCodigo(), datainicial, cliente.getCodigo(), mt.getContrato().getCodigo(), getUsuarioDao().getUsuarioRecorrencia().getCodigo(), OrigemSistemaEnum.ZW.getCodigo(), cliente.getEmpresa().getCodigo(), false, operacaoColetiva.getCodigo(), null);
                            if(operacaoColetiva.isCancelarParcelasEmAbertoAlunosDesmarcados()) {
                                cancelarParcelasEmAberto(operacaoColetiva, mt.getContrato().getCodigo());
                            }
                        }
                        datainicial = Calendario.somarDias(datainicial, 1);
                    }
                }
            }
            operacaoColetiva.setStatus(StatusOperacaoColetivaEnum.PROCESSADA);
            operacaoColetiva.setDataprocessamento(Calendario.hoje());
            operacaoColetiva.setResultado("Aulas do Período foram desmarcadas!");
        } catch (Exception e) {
            con.rollback();
            operacaoColetiva.setStatus(StatusOperacaoColetivaEnum.ERRO);
            operacaoColetiva.setResultado(Uteis.getData(Calendario.hoje()) + " - Erro ao processar operacao:" + e.getMessage());
        } finally {
            con.setAutoCommit(true);
        }
        OperacaoColetiva operacaoDAO = new OperacaoColetiva(con);
        operacaoDAO.altetarSemCommit(operacaoColetiva);
        operacaoDAO = null;
    }

    public void cancelarParcelasEmAberto(OperacaoColetivaVO operacaoColetivaVO, Integer codigoContrato) throws Exception {
        try {
            if (UteisValidacao.emptyNumber(codigoContrato)) {
                throw new Exception("Código do contrato não informado!");
            }

            Usuario usuarioDAO = new Usuario(con);
            UsuarioVO usuarioRecorrencia = usuarioDAO.getUsuarioRecorrencia();
            usuarioDAO = null;

            MovParcela movParcelaDAO = new MovParcela(con);
            List<MovParcelaVO> parcelasEmAberto = movParcelaDAO.consultarMensalidadesEmAbertoPorContrato(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS, operacaoColetivaVO.getDataInicio());
            movParcelaDAO = null;

            List<MovParcelaVO> parcelasCancelar = new ArrayList<>();
            for (MovParcelaVO movParcela : parcelasEmAberto) {
                boolean vencimentoParcelaEstaDentroPeriodoOperacao = Calendario.entre(movParcela.getDataVencimento(), operacaoColetivaVO.getDataInicio(), operacaoColetivaVO.getDataFinal());
                if (vencimentoParcelaEstaDentroPeriodoOperacao) {
                    parcelasCancelar.add(movParcela);
                }
            }

            MovParcelaControle movParcelaControle = new MovParcelaControle();
            movParcelaControle.setListaParcelasPagar(parcelasCancelar);
            movParcelaControle.setJustificativaCancelamento("CANCELADO POR OPERACAO COLETIVA - DESMARCAÇÃO DE AULAS DE TURMAS DOS CONTRATOS DOS ALUNOS");
            movParcelaControle.cancelarParcelas(usuarioRecorrencia);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public JSONArray alunosCrossfitMes(Integer mes, Integer ano, Integer empresa, Integer professor, Integer diaDaSemana, String horaInicial, String horaFinal, Integer max, Integer index, Boolean count) throws Exception {

        Date inicio = Calendario.getInstance(ano, mes, 1).getTime();
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(inicio);

        StringBuilder sql = new StringBuilder();
        if (count) {
            sql.append(" SELECT COUNT(*) as total \n");
        } else {
            sql.append(" SELECT s.situacao, s.codigocliente, s.matricula, s.nomecliente, ht.professor, aht.dia, ht.horainicial, ht.horafinal, mod.nome  \n");
        }
        sql.append(" from alunohorarioturma aht\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" INNER JOIN modalidade mod ON t.modalidade = mod.codigo\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw s on s.codigocliente = aht.cliente \n");
        sql.append(" WHERE mod.crossfit and aht.dia BETWEEN '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio))).append("' ");
        sql.append(" AND '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59"))).append("' \n");
        sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)").append(" \n");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" AND ht.professor = ").append(professor).append(" \n");
        }
        if (!UteisValidacao.emptyString(horaInicial) && !UteisValidacao.emptyString(horaFinal)) {
            sql.append("  AND ht.horainicial >= '").append(horaInicial).append("' AND ht.horafinal <= '").append(horaFinal).append("'  \n");
        }
        if (!UteisValidacao.emptyNumber(diaDaSemana)) {
            sql.append("  AND date_part('dow',aht.dia) = '").append(diaDaSemana).append("' \n");
        }
        if (!UteisValidacao.emptyNumber(max) && index != null && !count) {
            sql.append(" LIMIT ").append(max).append(" OFFSET ").append(index);
        }

        JSONArray array = new JSONArray();

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                if (count) {
                    json.put("total", rs.getInt("total"));
                } else {
                    json.put("cliente", rs.getInt("codigocliente"));
                    json.put("professor", rs.getInt("professor"));
                    json.put("matricula", rs.getString("matricula"));
                    json.put("situacao", rs.getString("situacao"));
                    json.put("nome", rs.getString("nomecliente"));
                    json.put("inicio", rs.getString("horainicial"));
                    json.put("fim", rs.getString("horafinal"));
                    json.put("dia", Uteis.getData(rs.getDate("dia")));
                    json.put("modalidade", rs.getString("nome"));
                }
                array.put(json);
            }
        }
        return array;
    }

    public JSONArray alunosCancelados(Integer empresa, Integer mes, Integer ano, Integer professor) throws Exception {
        Date inicio = Calendario.getInstance(ano, mes, 1).getTime();
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(inicio);

        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT c.* from contratooperacao c  \n");
        sql.append(" INNER JOIN contrato con on con.codigo = c.contrato \n");
        sql.append(" INNER JOIN cliente cli on cli.pessoa = con.pessoa\n");
        sql.append(" INNER JOIN historicovinculo h1 on h1.cliente = cli.codigo and h1.tipohistoricovinculo = 'EN' \n");
        sql.append(" and h1.tipocolaborador = 'TW' and h1.colaborador =").append(professor);
        sql.append(" and h1.dataregistro <= c.dataoperacao");
        sql.append(" left join historicovinculo h2 on h2.cliente = cli.codigo and h2.tipohistoricovinculo = 'EN' \n");
        sql.append(" and h2.tipocolaborador = 'TW' and h2.colaborador <> h1.colaborador and h2.dataregistro > h1.dataregistro");
        sql.append(" where cli.empresa =").append(empresa).append("\n");
        sql.append(" and dataoperacao between '").append(inicio).append("'\n");
        sql.append(" and '").append(fim).append("' \n");
        sql.append(" and tipooperacao = 'CA' and h2.codigo is null\n");

        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("codigoContrato", rs.getInt("contrato"));
                json.put("situacao", rs.getString("tipooperacao"));
                array.put(json);
            }
        }
        return array;
    }

    @Override
    public String inserirAlunoAulaCheiaBooking(final ParamAlunoAulaCheiaTO param, String booking) {
        try {
            AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
            alunoHorario.setCliente(param.getCodigoCliente());
            alunoHorario.getHorarioTurma().setCodigo(param.getCodigoHorarioTurma());
            alunoHorario.setExperimental(param.isAulaExperimental());
            alunoHorario.setData(param.getData());
            alunoHorario.setUsuario(param.getCodigoUsuario());
            alunoHorario.setPassivo(param.getCodigoPassivo());
            alunoHorario.setIndicado(param.getCodigoIndicado());
            if (UteisValidacao.emptyNumber(alunoHorario.getUsuario())) {
                try {
                    UsuarioVO usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                    alunoHorario.setUsuario(usuarioVO.getCodigo());
                } catch (Exception e) {
                    Uteis.logar(e, TurmasServiceImpl.class);
                }
            }
            alunoHorario.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(param.getOrigemSistema()));
            AgendaTotalJSON json = consultarUmaTurma(param.getCodigoHorarioTurma(), param.getData());
            FilaDeEsperaTO filaDeEsperaParam = new FilaDeEsperaTO();
            filaDeEsperaParam.setCodigoAluno(param.getCodigoCliente());
            filaDeEsperaParam.setCodigoHorarioTurma(param.getCodigoHorarioTurma());
            filaDeEsperaParam.setDataEntrada(Calendario.hoje());
            filaDeEsperaParam.setDia(Uteis.getDataAplicandoFormatacao(alunoHorario.getData(), "dd/MM/yyyy HH:mm:ss"));
            validarAulaEstaCheia(json, false, filaDeEsperaParam);
            if (verificarSeAlunoEAgregado(param.getCodigoCliente())) {
                validarLimiteAgregadosAtingidoPorAula(param.getCodigoHorarioTurma(), param.getData());
            }

            if (limiteDeAulasPorDiaBookingGympassAtingido(param.getCodigoCliente(), param.getEmpresa(), param.getData())) {
                throw new Exception("Limite de aulas por dia atingido!");
            }

            if (param.isSomenteValidar()) {
                return "VALIDADO OK";
            }

            alunoHorario.setDatalancamento(new java.sql.Timestamp(dataComTimezone(param.getEmpresa()).getTime()));

            getHorarioDao().incluirAlunoAulaCheia(alunoHorario, json, param.getKey());
            String[] inicio = json.getInicio().split(" ");
            try {
                chamarNotificacaoPushEntrouNaAula(
                        param.getKey(),
                        alunoHorario.getCliente(),
                        "Tá na agenda " + String.valueOf(Character.toChars(9989)),
                        firstLetterUpper(json.getTitulo().toLowerCase()) + " agendada em " + inicio[0] + " às " + inicio[1] + ". Aproveite!"
                );
            } catch (Exception e) {
                Uteis.logar(e, TurmasServiceImpl.class);
            }
            if (!UteisValidacao.emptyNumber(param.getCodigoCliente())) {
                preencherJSON(param.getCodigoCliente(), json);
            }
            //Adiciona o aluno adicionado ao contador de vagas
            json.setNrVagasPreenchidas(json.getNrVagasPreenchidas() + 1);

            getHorarioDao().incluirAlunoAulaCheiaBooking(alunoHorario.getCodigo(), booking);
            try {
                ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select nomecliente, " +
                                "codigopessoa, matricula from situacaoclientesinteticodw where codigocliente = " + param.getCodigoCliente(),
                        con);
                if (resultSet.next()) {
                    json.setMatricula(resultSet.getInt("matricula"));
                    json.setNomeAluno(resultSet.getString("nomecliente"));
                    logAluno(param.getCodigoHorarioTurma() + "_" + Calendario.getDataAplicandoFormatacao(param.getData(), "dd/MM/yyyy"),
                            param.getCodigoUsuario(),
                            resultSet.getInt("codigopessoa"),
                            json,
                            OrigemSistemaEnum.BOOKING_GYMPASS.getCodigo(), false, true, false, false, param.getCodigoCliente());
                }
            } catch (Exception e) {
                Uteis.logar(e, TurmasServiceImpl.class);
            }
            return json.toJSON();
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    public JSONObject dadosBooking(String classID, String matricula) {
        JSONObject obj = new JSONObject();
        try {
            ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select descricao, codigo from turma where idclassegympass = '" + classID + "' ", con);
            if (resultSet.next()) {
                obj.put("turma", resultSet.getString("descricao"));
                obj.put("idturma", resultSet.getInt("codigo"));
            }

            ResultSet resultAulas = SuperFacadeJDBC.criarConsulta("select a.dia, a.bookingid, ht.horainicial, ht.identificadorturma from alunohorarioturma a\n" +
                    " inner join cliente c on c.codigo = a.cliente\n" +
                    " inner join horarioturma ht on ht.codigo = a.horarioturma\n" +
                    " where c.codigomatricula = " + matricula +
                    " order by a.codigo desc\n" +
                    " limit 20", con);
            JSONArray aulas = new JSONArray();
            while (resultAulas.next()) {
                JSONObject aula = new JSONObject();
                aula.put("dia", resultAulas.getDate("dia").getTime());
                aula.put("bookingid", resultAulas.getString("bookingid"));
                aula.put("horainicial", resultAulas.getString("horainicial"));
                aula.put("identificadorturma", resultAulas.getString("identificadorturma"));
                aulas.put(aula);
            }
            obj.put("aulas", aulas);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return obj;
    }

    @Override
    public Integer aulasAluno(String modalidades, Integer matricula, Long dataLimite, Date dataInicio, Date dataFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count(ht.*) as aulas from alunohorarioturma ah \n");
        sql.append(" inner join cliente cli on cli.codigo = ah.cliente \n");
        sql.append(" inner join horarioturma ht on ht.codigo = ah.horarioturma \n");
        sql.append(" inner join turma t on t.codigo = ht.turma \n");
        sql.append(" where cli.codigomatricula = ").append(matricula);

        String[] modalidade = modalidades.split(",");
        List<Integer> listaModalidade = new ArrayList() {
            {
                for (String lista : modalidade) {
                    if (!UteisValidacao.emptyString(lista)) {
                        add(Integer.parseInt(lista));
                    }
                }
            }
        };

        if (!UteisValidacao.emptyList(listaModalidade)) {
            sql.append(" AND t.modalidade IN ( ").append(Uteis.montarListaIN(listaModalidade)).append(" ) \n");
        }
        if (dataLimite != null) {
            sql.append(" and ah.dia::date >= '").append(Calendario.getDataAplicandoFormatacao(new Date(dataLimite), "yyyy-MM-dd")).append("'");
        } else {
            sql.append(" and ah.dia::date between '").append(dataInicio).append("' and '").append(dataFim).append("' \n");
        }
        Statement stm = this.con.createStatement();
        ResultSet resultSet = stm.executeQuery(sql.toString());
        int nraulas = resultSet.next() ? resultSet.getInt("aulas") : 0;

        sql = new StringBuilder();
        sql.append(" select count(pr.*) as aulas from presenca pr \n");
        sql.append(" inner join matriculaalunohorarioturma mth on mth.codigo = pr.dadosturma \n");
        sql.append(" inner join cliente cli on cli.pessoa = mth.pessoa \n");
        sql.append(" inner join horarioturma ht on ht.codigo = mth.horarioturma \n");
        sql.append(" inner join turma t on t.codigo = ht.turma \n");
        sql.append(" where cli.codigomatricula = ").append(matricula);
        if (!UteisValidacao.emptyList(listaModalidade)) {
            sql.append(" AND t.modalidade IN ( ").append(Uteis.montarListaIN(listaModalidade)).append(" ) \n");
        }
        if (dataLimite != null) {
            sql.append(" and pr.datapresenca::date >= '").append(Calendario.getDataAplicandoFormatacao(new Date(dataLimite), "yyyy-MM-dd")).append("'");
        } else {
            sql.append(" and pr.datapresenca::date between '").append(dataInicio).append("' and '").append(dataFim).append("' \n");
        }
        stm = this.con.createStatement();
        resultSet = stm.executeQuery(sql.toString());
        nraulas += (resultSet.next() ? resultSet.getInt("aulas") : 0);

        sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS aulas FROM reposicao r \n");
        sql.append(" INNER JOIN cliente c ON c.codigo = r.cliente \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append(" WHERE c.codigomatricula = ").append(matricula);
        if (!UteisValidacao.emptyList(listaModalidade)) {
            sql.append(" AND t.modalidade IN ( ").append(Uteis.montarListaIN(listaModalidade)).append(" ) \n");
        }
        if (dataLimite != null) {
            sql.append(" AND r.datareposicao::date >= '").append(Calendario.getDataAplicandoFormatacao(new Date(dataLimite), "yyyy-MM-dd")).append("'");
        } else {
            sql.append(" AND r.datareposicao::date BETWEEN '").append(dataInicio).append("' and '").append(dataFim).append("' \n");
        }
        stm = this.con.createStatement();
        resultSet = stm.executeQuery(sql.toString());
        nraulas += resultSet.next() ? resultSet.getInt("aulas") : 0;

        return nraulas;

    }

    public JSONObject obterDiaSemanaEHorarioAlunoPorPeriodo(Integer matricula, String data) throws Exception {
        String diasSemana = "";
        String horarios = "";
        String professores = "";
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ht.diasemana, ht.horainicial, ht.horafinal, pes.nome AS professor \n");
        sql.append(" FROM matriculaalunohorarioturma maht \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma \n");
        sql.append(" INNER JOIN colaborador col ON col.codigo = ht.professor \n");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = col.pessoa \n");
        sql.append(" WHERE maht.contrato IN ( \n");
        sql.append("     SELECT con.codigo \n");
        sql.append("     FROM contrato con \n");
        sql.append("     INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
        sql.append("     WHERE cli.codigomatricula = ").append(matricula).append(" \n");
        sql.append("     AND '").append(data).append("' between vigenciade and vigenciaate \n");
        sql.append(" )");

        Statement stm = this.con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        while (rs.next()) {
            String dia = rs.getString("diasemana");
            String horario = rs.getString("horainicial") + " / " + rs.getString("horafinal");
            String professor = rs.getString("professor");
            diasSemana += diasSemana.contains(dia) ? "" : " " + dia;
            horarios += horarios.contains(horario) ? "" : ";" + horario;
            professores += professores.contains(professor) ? "" : ";" + professor;
        }

        if (horarios.equals("") && diasSemana.equals("") && professores.equals("")) {
            StringBuilder sqlHorario = new StringBuilder();
            sqlHorario.append(" SELECT h.descricao  \n");
            sqlHorario.append(" FROM contratohorario ch \n");
            sqlHorario.append(" INNER JOIN horario h ON h.codigo = ch.horario  \n");
            sqlHorario.append(" WHERE ch.contrato IN ( \n");
            sqlHorario.append("     SELECT con.codigo \n");
            sqlHorario.append("     FROM contrato con \n");
            sqlHorario.append("     INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
            sqlHorario.append("     WHERE cli.codigomatricula = ").append(matricula).append(" \n");
            sqlHorario.append("     AND '").append(data).append("' between vigenciade and vigenciaate \n");
            sqlHorario.append(" )");

            Statement stmHorario = this.con.createStatement();
            ResultSet rsHorario = stmHorario.executeQuery(sqlHorario.toString());
            if (rsHorario.next()) {
                horarios = ";" + StringUtils.capitalize(rsHorario.getString("descricao").toLowerCase());
                diasSemana = ";" + StringUtils.capitalize(rsHorario.getString("descricao").toLowerCase());
                professores = ";-";
            }
        }

        if (diasSemana.length() > 0) {
            diasSemana = new StringBuilder(diasSemana).deleteCharAt(0).toString();
        }
        if (horarios.length() > 0) {
            horarios = new StringBuilder(horarios).deleteCharAt(0).toString();
        }
        if (professores.length() > 0) {
            professores = new StringBuilder(professores).deleteCharAt(0).toString();
        }

        JSONObject json = new JSONObject();
        json.put("diasSemana", diasSemana);
        json.put("horarios", horarios);
        json.put("professores", professores);

        return json;
    }

    public Integer contarAulasPrevistasMes(Integer matricula, Integer mes, String data) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ht.diasemananumero, ht.diasemana \n");
        sql.append(" FROM matriculaalunohorarioturma maht \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma \n");
        sql.append(" WHERE maht.contrato IN ( \n");
        sql.append("     SELECT con.codigo \n");
        sql.append("     FROM contrato con \n");
        sql.append("     INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
        sql.append("     WHERE cli.codigomatricula = ").append(matricula).append(" \n");
        sql.append("     AND '").append(data).append("' between vigenciade and vigenciaate \n");
        sql.append(" )");

        Statement stm = this.con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        Integer qtAulasPrevistas = 0;
        while (rs.next()) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_WEEK, rs.getInt("diasemananumero"));
            calendar.set(Calendar.MONTH, mes);
            qtAulasPrevistas += calendar.getActualMaximum(Calendar.DAY_OF_WEEK_IN_MONTH);
        }

        if (qtAulasPrevistas == 0) {
            sql = new StringBuilder();
            sql.append(" SELECT cm.vezessemana \n");
            sql.append(" FROM contratomodalidade cm \n");
            sql.append(" WHERE cm.contrato IN ( \n");
            sql.append("     SELECT con.codigo \n");
            sql.append("     FROM contrato con \n");
            sql.append("     INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
            sql.append("     WHERE cli.codigomatricula = ").append(matricula).append(" \n");
            sql.append("     AND '").append(data).append("' between vigenciade and vigenciaate \n");
            sql.append(" )");

            stm = this.con.createStatement();
            rs = stm.executeQuery(sql.toString());
            while (rs.next()) {
                qtAulasPrevistas += (rs.getInt("vezessemana") * 4);
            }
        }

        return qtAulasPrevistas;
    }

    public JSONObject totalizarOcupacoes(Long inicio, Long fim, Integer empresa, Integer professor) throws Exception {
        JSONArray array = ocupacaoAulaDia(inicio, fim, empresa, professor, null, true);
        Map<String, AulaOcupacao> modalidades = new HashMap<>();
        Map<String, AulaOcupacao> professores = new HashMap<>();
        Double totalBonificacoes = 0.0;
        Double totalTaxa = 0.0;
        int totalColetivas = 0;
        TotalizadorOcupacaoJSON totalPorAula = new TotalizadorOcupacaoJSON();
        int presencas = 0;
        int faltas = 0;
        Set<Integer> alunos = new HashSet<>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject aula = array.getJSONObject(i);
            alunos.addAll(new HashSet() {{
                for (int x = 0; x < aula.getJSONArray("alunos").length(); x++) {
                    add(aula.getJSONArray("alunos").get(x));
                }
            }});
            totalBonificacoes += aula.getDouble("valorBonusAcumulado");
            if (aula.getBoolean("aulacoletiva")) {
                totalColetivas++;
            }
            if (!Double.isNaN(aula.optDouble("ocupacaoAcumulada")) && aula.optInt("capacidadeAcumulada") != 0) {
                totalTaxa += (aula.optDouble("ocupacaoAcumulada") / aula.optInt("capacidadeAcumulada")) * 100.0;
            }
            presencas += aula.getInt("presentesAcumulada");
            faltas += aula.getInt("ocupacaoAcumulada") - aula.getInt("presentesAcumulada");
            totalPorAula.incrementar(aula.getInt("capacidadeAcumulada"), aula.getInt("ocupacaoAcumulada"));
            addAula(modalidades, String.valueOf(aula.getInt("modalidade")), aula.getInt("capacidadeAcumulada"), aula.getInt("ocupacaoAcumulada"), aula.getString("nomeModalidade"));
            addAula(professores, String.valueOf(aula.getInt("professor")), aula.getInt("capacidadeAcumulada"), aula.getInt("ocupacaoAcumulada"), aula.getString("nomePessoa"));
        }
        JSONObject totais = new JSONObject();
        totais.put("aulas", totalPorAula.toJSON());
        TotalizadorOcupacaoJSON totalPorModalidade = new TotalizadorOcupacaoJSON();
        for (AulaOcupacao aulaOcupacao : modalidades.values()) {
            totalPorModalidade.incrementar(aulaOcupacao.capacidade, aulaOcupacao.ocupacao);
        }
        totais.put("modalidades", totalPorModalidade.toJSON());
        TotalizadorOcupacaoJSON totalPorProfessor = new TotalizadorOcupacaoJSON();
        for (AulaOcupacao aulaOcupacao : professores.values()) {
            totalPorProfessor.incrementar(aulaOcupacao.capacidade, aulaOcupacao.ocupacao);
        }
        totais.put("professores", totalPorProfessor.toJSON());
        totais.put("faltas", faltas);
        totais.put("presencas", presencas);
        totais.put("alunos", alunos.size());
        if (UteisValidacao.emptyNumber(totalColetivas)) {
            totais.put("totalAulas", 0.0);
        } else {
            totais.put("totalAulas", (totalTaxa / totalColetivas));
        }
        totais.put("totalBonificacoes", totalBonificacoes);
        return totais;
    }


    public JSONArray ocupacaoAulaDia(Long inicio,
                                     Long fim,
                                     Integer empresa,
                                     Integer professor,
                                     String quicksearchValue, Boolean totalizarAlunos) throws Exception {
        return ocupacaoAulaDia(inicio,
                fim,
                empresa,
                professor,
                quicksearchValue, totalizarAlunos, false);
    }


    public JSONArray ocupacaoAulaDiaV2(Long inicio,
                                     Long fim,
                                     Integer empresa,
                                     Integer professor,
                                     String quicksearchValue, Boolean totalizarAlunos, Boolean semAgrupar, String mapaAulasExcluidas, String mapaProfessoresSubstituidos) throws Exception {

        Date inicioDt = new Date(inicio);
        Date fimDt = Calendario.getDataComHora(new Date(fim), "23:59");

        Map<Integer, LocalDate> listaDataExcluida = new Hashtable<>();
//        Map<Integer, LocalDate> listaMapaProfessoresSubstituidos = new Hashtable<>();
        ObjectMapper mapper = new ObjectMapper();
        Map<String, List<String>> mapaExc = mapper.readValue(mapaAulasExcluidas, Map.class);
//        Map<String, List<String>> mapaSubst = mapper.readValue(mapaProfessoresSubstituidos, Map.class);
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
        for (Map.Entry<String, List<String>> entry : mapaExc.entrySet()) {
            String chave = entry.getKey();
            String dataStr = entry.getValue().get(0);
            Date date = sdf.parse(dataStr);
            LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            listaDataExcluida.put(Integer.valueOf(chave),localDate );
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" select \n");
        sql.append(" ht.codigo, \n");
        sql.append(" ht.horainicial, \n");
        sql.append(" ht.horafinal, \n");
        sql.append(" t.aulacoletiva, \n");
        sql.append(" ht.diasemana, \n");
        sql.append(" nrmaximoaluno as capacidade, \n");
        sql.append(" t.descricao, \n");
        sql.append(" t.meta, \n");
        sql.append(" t.bonificacao, \n");
        sql.append(" c.codigo as professor, \n");
        sql.append(" m.codigo as modalidade, \n");
        sql.append(" m.nome as nomeModalidade, \n");
        sql.append(" pes.nome as nomePessoa, \n");
        sql.append(" t.datainicialvigencia, \n");
        sql.append(" t.datafinalvigencia \n");
        sql.append(" from horarioturma ht \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append(" inner join colaborador c on c.codigo = ht.professor \n");
        sql.append(" inner join pessoa pes on pes.codigo = c.pessoa \n");
        sql.append(" inner join modalidade m on m.codigo = t.modalidade \n");
        sql.append(" and ht.situacao = 'AT' \n");
        sql.append(" and t.datainicialvigencia <= '"+Uteis.getDataJDBC(Calendario.getDataComHoraZerada(fimDt))+" 23:59' \n");
        sql.append(" AND t.datafinalvigencia >= '"+Uteis.getDataJDBC(Calendario.getDataComHoraZerada(inicioDt))+" 00:00' \n");
        sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" AND ht.professor = ").append(professor);
        }
        if (semAgrupar) {
            sql.append(" AND t.aulacoletiva ");
        }
        if (quicksearchValue != null && !quicksearchValue.equals("")) {
            sql.append(" AND (UPPER(t.descricao) LIKE UPPER('%").append(quicksearchValue).append("%') ");
            sql.append(" OR UPPER(m.nome) LIKE UPPER ('%").append(quicksearchValue).append("%') ");
            sql.append(" OR UPPER(pes.nome) LIKE UPPER ('%").append(quicksearchValue).append("%') ");
            sql.append(" ) ");
        }
        Map<DiaSemana, List<JSONObject>> mapaDiasSemana = new HashMap() {{
            for (DiaSemana dia : DiaSemana.values()) {
                put(dia, new ArrayList<>());
            }
        }};

        Integer codigoProfessor = 0;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                DiaSemana diasemana = DiaSemana.getDiaSemana(rs.getString("diasemana"));
                if (diasemana != null) {
                    JSONObject json = new JSONObject();
                    json.put("codigo", rs.getInt("codigo"));
                    json.put("capacidade", rs.getInt("capacidade"));
                    json.put("diaSemana", diasemana.getDescricao());
                    json.put("aulacoletiva", rs.getBoolean("aulacoletiva"));
                    json.put("professor", rs.getInt("professor"));
                    codigoProfessor = rs.getInt("professor");
                    json.put("meta", rs.getInt("meta"));
                    json.put("bonificacao", rs.getDouble("bonificacao"));
                    json.put("horario", rs.getString("horainicial"));
                    json.put("modalidade", rs.getInt("modalidade"));
                    json.put("aula", rs.getString("descricao"));
                    json.put("nomeModalidade", rs.getString("nomeModalidade"));
                    json.put("nomePessoa", rs.getString("nomePessoa"));
                    json.put("turma_datainicialvigencia", rs.getString("datainicialvigencia"));
                    json.put("turma_datafinalvigencia", rs.getString("datafinalvigencia"));
                    mapaDiasSemana.get(diasemana).add(json);
                }
            }
        }
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicioDt, fimDt);
        JSONArray array = new JSONArray();
        Map<Integer, Set<Integer>> mapaAlunos = new HashMap<>();

        for (Date dia : diasEntreDatas) {
            Calendar cal = Calendario.getInstance();
            cal.setTime(dia);
            DiaSemana diaSemana = DiaSemana.getDiaSemanaNumeral(cal.get(Calendar.DAY_OF_WEEK));
            List<JSONObject> aulas = mapaDiasSemana.get(diaSemana);
            for (JSONObject aula : aulas) {

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime dataHora = LocalDateTime.parse(aula.getString("turma_datainicialvigencia"), formatter);
                LocalDate dataIni = dataHora.toLocalDate();

                LocalDateTime dataHoraFim = LocalDateTime.parse(aula.getString("turma_datafinalvigencia"), formatter);
                LocalDate dataFim = dataHoraFim.toLocalDate();

                LocalDate dataParaVerificar = dia.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                boolean dentroDoIntervalo = (!dataParaVerificar.isBefore(dataIni)) && (!dataParaVerificar.isAfter(dataFim));

                if(dentroDoIntervalo){
                    LocalDate diaLocalDate = dia.toInstant()
                            .atZone(ZoneId.of("America/Sao_Paulo"))
                            .toLocalDate();
                    boolean verifica = true;

                    for (Map.Entry<Integer, LocalDate> entry : listaDataExcluida.entrySet()) {
                        if(entry.getKey().equals(aula.getInt("codigo")) && entry.getValue().equals(diaLocalDate)){
                            verifica = false;
                        }
                    }

                    ObjectMapper mapperr = new ObjectMapper();
                    Map<String, Map<String, Map<String, Object>>> mapa = mapperr.readValue(mapaProfessoresSubstituidos, Map.class);
                    for (Map.Entry<String, Map<String, Map<String, Object>>> turmaEntry : mapa.entrySet()) {

                        Map<String, Map<String, Object>> datas = turmaEntry.getValue();

                        for (Map.Entry<String, Map<String, Object>> dataEntry : datas.entrySet()) {
                            String dataChave = dataEntry.getKey();
                            Map<String, Object> atributos = dataEntry.getValue();
                            Date date = sdf.parse(dataChave);
                            if(date.equals(dia) && atributos.get("codigoProfessorOriginal").equals(codigoProfessor) && atributos.get("codigoHorarioTurma").equals(aula.getInt("codigo"))){
                                verifica = false;
                            }
                        }
                    }

                    if(verifica){
                        JSONObject clone = new JSONObject(aula, JSONObject.getNames(aula));
                        clone.put("dia", dia.getTime());
                        boolean aulacoletiva = aula.getBoolean("aulacoletiva");
                        StringBuilder sqlOcupacao = new StringBuilder();
                        StringBuilder sqlPresenca = new StringBuilder();
                        StringBuilder sqlPresencaReposicao = new StringBuilder();
                        if (aulacoletiva) {
                            sqlOcupacao.append("select p.codigo as pessoa, ");
                            sqlOcupacao.append("exists ( ");
                            sqlOcupacao.append("select cliente  from aulaconfirmada ");
                            sqlOcupacao.append("WHERE diaaula ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' ");
                            sqlOcupacao.append("and horario ="+aula.getInt("codigo")+" and cliente = c.codigo ");
                            sqlOcupacao.append(") as presente ");
                            sqlOcupacao.append("from alunohorarioturma aht ");
                            sqlOcupacao.append("inner join cliente c on c.codigo = aht.cliente ");
                            sqlOcupacao.append("inner join pessoa p on p.codigo = c.pessoa ");
                            sqlOcupacao.append("WHERE aht.dia = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  and aht.horarioturma = "+aula.getInt("codigo")+" ");

                        } else {
                            sqlOcupacao.append("select p.codigo as pessoa, ");
                            sqlOcupacao.append("exists ( ");
                            sqlOcupacao.append("select p.codigo  from presenca p ");
                            sqlOcupacao.append("inner join matriculaalunohorarioturma maht2 on maht2.codigo = p.dadosturma ");
                            sqlOcupacao.append("where p.datapresenca ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' and  p.dadosturma = maht.codigo ");
                            sqlOcupacao.append(") as presente ");
                            sqlOcupacao.append("from matriculaalunohorarioturma  maht ");
                            sqlOcupacao.append("join pessoa p on p.codigo = maht.pessoa ");
                            sqlOcupacao.append("join cliente c on c.pessoa = p.codigo ");
                            sqlOcupacao.append("WHERE '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  BETWEEN datainicio and datafim  and horarioturma = "+aula.getInt("codigo")+" ");
                            sqlOcupacao.append("and c.codigo not in (select cliente  from auladesmarcada a where horarioturma = "+aula.getInt("codigo")+" and dataorigem = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"') ");

                            sqlPresencaReposicao.append(" select c.pessoa ,r.datapresenca from reposicao r ");
                            sqlPresencaReposicao.append(" inner join cliente c on c.codigo = r.cliente ");
                            sqlPresencaReposicao.append(" where r.horarioturma = ").append(aula.getInt("codigo"));;
                            sqlPresencaReposicao.append(" and r.datareposicao = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
                        }
                        Integer ocupacao = 0;
                        Integer presentes = 0;
                        Set<Integer> codAlunos = new HashSet<>();
                        try (ResultSet rsOcupacao = SuperFacadeJDBC.criarConsulta(sqlOcupacao.toString(), con)) {
                            while (rsOcupacao.next()){
                                ocupacao++;
                                if (totalizarAlunos) {
                                    codAlunos.add(rsOcupacao.getInt("pessoa"));
                                }
                                if(rsOcupacao.getBoolean("presente")){
                                    presentes++;
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (!UteisValidacao.emptyString(sqlPresencaReposicao.toString())) {
                            ResultSet rsPresencaReposicao = SuperFacadeJDBC.criarConsulta(sqlPresencaReposicao.toString(), con);
                            while (rsPresencaReposicao.next()){
                                ocupacao ++;
                                if (totalizarAlunos) {
                                    codAlunos.add(rsPresencaReposicao.getInt("pessoa"));
                                }
                                if(rsPresencaReposicao.getDate("datapresenca")!=null){
                                    presentes++;
                                }
                            }
                        }

                        Integer capacidade = clone.getInt("capacidade");

                        try {
                            Double taxa = (ocupacao.doubleValue() / capacidade.doubleValue()) * 100.0;
                            clone.put("taxa", taxa);
                            Double meta = clone.getDouble("meta");
                            if (meta <= taxa) {
                                clone.put("valorBonus", clone.getDouble("bonificacao"));
                            } else {
                                clone.put("valorBonus", 0.0);
                            }
                        } catch (Exception e) {
                            clone.put("valorBonus", 0.0);
                        }

                        clone.put("ocupacao", ocupacao);
                        clone.put("presentes", presentes);

                        Set<Integer> cods = codAlunos;
                        Set<Integer> alunos = mapaAlunos.get(aula.getInt("codigo"));
                        if (alunos == null) {
                            alunos = new HashSet<>();
                            mapaAlunos.put(aula.getInt("codigo"), alunos);
                        }
                        alunos.addAll(cods);
                        array.put(clone);
                    }
                }
            }
            ObjectMapper mapperr = new ObjectMapper();

            Map<String, Map<String, Map<String, Object>>> mapa = mapperr.readValue(mapaProfessoresSubstituidos, Map.class);
            for (Map.Entry<String, Map<String, Map<String, Object>>> turmaEntry : mapa.entrySet()) {
                String codigoHorarioTurma = turmaEntry.getKey();
                Map<String, Map<String, Object>> datas = turmaEntry.getValue();

                for (Map.Entry<String, Map<String, Object>> dataEntry : datas.entrySet()) {
                    String dataChave = dataEntry.getKey(); // "Sat Jun 07 00:00:00 BRT 2025"
                    Map<String, Object> atributos = dataEntry.getValue();

                    Date date = sdf.parse(dataChave);
                    if(date.equals(dia) && atributos.get("codigoProfessorSubstituto").equals(codigoProfessor)){
                        StringBuilder sqlSub = new StringBuilder();
                        sqlSub.append(" select \n");
                        sqlSub.append(" ht.codigo, \n");
                        sqlSub.append(" ht.horainicial, \n");
                        sqlSub.append(" ht.horafinal, \n");
                        sqlSub.append(" t.aulacoletiva, \n");
                        sqlSub.append(" ht.diasemana, \n");
                        sqlSub.append(" nrmaximoaluno as capacidade, \n");
                        sqlSub.append(" t.descricao, \n");
                        sqlSub.append(" t.meta, \n");
                        sqlSub.append(" t.bonificacao, \n");
                        sqlSub.append(" c.codigo as professor, \n");
                        sqlSub.append(" m.codigo as modalidade, \n");
                        sqlSub.append(" m.nome as nomeModalidade, \n");
                        sqlSub.append(" pes.nome as nomePessoa, \n");
                        sqlSub.append(" t.datainicialvigencia, \n");
                        sqlSub.append(" t.datafinalvigencia \n");
                        sqlSub.append(" from horarioturma ht \n");
                        sqlSub.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
                        sqlSub.append(" inner join colaborador c on c.codigo = ht.professor \n");
                        sqlSub.append(" inner join pessoa pes on pes.codigo = c.pessoa \n");
                        sqlSub.append(" inner join modalidade m on m.codigo = t.modalidade \n");
                        sqlSub.append(" and ht.situacao = 'AT' \n");
                        sqlSub.append(" and t.datainicialvigencia <= '"+Uteis.getDataJDBC(Calendario.getDataComHoraZerada(fimDt))+" 23:59' \n");
                        sqlSub.append(" AND t.datafinalvigencia >= '"+Uteis.getDataJDBC(Calendario.getDataComHoraZerada(inicioDt))+" 00:00' \n");
                        sqlSub.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
                        sqlSub.append(" AND ht.codigo = ").append(codigoHorarioTurma);

                        JSONObject json = new JSONObject();

                        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlSub.toString(), con)) {
                            while (rs.next()) {
                                DiaSemana diasemana = DiaSemana.getDiaSemana(rs.getString("diasemana"));
                                if (diasemana != null) {

                                    json.put("codigo", rs.getInt("codigo"));
                                    json.put("capacidade", rs.getInt("capacidade"));
                                    json.put("diaSemana", diasemana.getDescricao());
                                    json.put("aulacoletiva", rs.getBoolean("aulacoletiva"));
                                    json.put("professor", atributos.get("codigoProfessorSubstituto"));
                                    json.put("meta", rs.getInt("meta"));
                                    json.put("bonificacao", rs.getDouble("bonificacao"));
                                    json.put("horario", rs.getString("horainicial"));
                                    json.put("modalidade", rs.getInt("modalidade"));
                                    json.put("aula", rs.getString("descricao"));
                                    json.put("nomeModalidade", rs.getString("nomeModalidade"));
                                    json.put("nomePessoa", atributos.get("nomeProfessorSubstituto"));
                                    json.put("turma_datainicialvigencia", rs.getString("datainicialvigencia"));
                                    json.put("turma_datafinalvigencia", rs.getString("datafinalvigencia"));

                                }
                            }
                        }

                        JSONObject clone = new JSONObject(json, JSONObject.getNames(json));
                        clone.put("dia", dia.getTime());
                        boolean aulacoletiva = json.getBoolean("aulacoletiva");
                        StringBuilder sqlOcupacao = new StringBuilder();
                        StringBuilder sqlPresenca = new StringBuilder();
                        StringBuilder sqlPresencaReposicao = new StringBuilder();

                        if (aulacoletiva) {
                            sqlOcupacao.append("select p.codigo as pessoa, ");
                            sqlOcupacao.append("exists ( ");
                            sqlOcupacao.append("select cliente  from aulaconfirmada ");
                            sqlOcupacao.append("WHERE diaaula ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' ");
                            sqlOcupacao.append("and horario ="+json.getInt("codigo")+" and cliente = c.codigo ");
                            sqlOcupacao.append(") as presente ");
                            sqlOcupacao.append("from alunohorarioturma aht ");
                            sqlOcupacao.append("inner join cliente c on c.codigo = aht.cliente ");
                            sqlOcupacao.append("inner join pessoa p on p.codigo = c.pessoa ");
                            sqlOcupacao.append("WHERE aht.dia = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  and aht.horarioturma = "+json.getInt("codigo")+" ");

                        } else {
                            sqlOcupacao.append("select p.codigo as pessoa, ");
                            sqlOcupacao.append("exists ( ");
                            sqlOcupacao.append("select p.codigo  from presenca p ");
                            sqlOcupacao.append("inner join matriculaalunohorarioturma maht2 on maht2.codigo = p.dadosturma ");
                            sqlOcupacao.append("where p.datapresenca ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' and  p.dadosturma = maht.codigo ");
                            sqlOcupacao.append(") as presente ");
                            sqlOcupacao.append("from matriculaalunohorarioturma  maht ");
                            sqlOcupacao.append("join pessoa p on p.codigo = maht.pessoa ");
                            sqlOcupacao.append("join cliente c on c.pessoa = p.codigo ");
                            sqlOcupacao.append("WHERE '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  BETWEEN datainicio and datafim  and horarioturma = "+json.getInt("codigo")+" ");
                            sqlOcupacao.append("and c.codigo not in (select cliente  from auladesmarcada a where horarioturma = "+json.getInt("codigo")+" and dataorigem = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"') ");

                            sqlPresencaReposicao.append(" select c.pessoa ,r.datapresenca from reposicao r ");
                            sqlPresencaReposicao.append(" inner join cliente c on c.codigo = r.cliente ");
                            sqlPresencaReposicao.append(" where r.horarioturma = ").append(json.getInt("codigo"));;
                            sqlPresencaReposicao.append(" and r.datareposicao = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
                        }
                        Integer ocupacao = 0;
                        Integer presentes = 0;
                        Set<Integer> codAlunos = new HashSet<>();
                        try (ResultSet rsOcupacao = SuperFacadeJDBC.criarConsulta(sqlOcupacao.toString(), con)) {
                            while (rsOcupacao.next()){
                                ocupacao++;
                                if (totalizarAlunos) {
                                    codAlunos.add(rsOcupacao.getInt("pessoa"));
                                }
                                if(rsOcupacao.getBoolean("presente")){
                                    presentes++;
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (!UteisValidacao.emptyString(sqlPresencaReposicao.toString())) {
                            ResultSet rsPresencaReposicao = SuperFacadeJDBC.criarConsulta(sqlPresencaReposicao.toString(), con);
                            while (rsPresencaReposicao.next()){
                                ocupacao ++;
                                if (totalizarAlunos) {
                                    codAlunos.add(rsPresencaReposicao.getInt("pessoa"));
                                }
                                if(rsPresencaReposicao.getDate("datapresenca")!=null){
                                    presentes++;
                                }
                            }
                        }

                        Integer capacidade = clone.getInt("capacidade");

                        try {
                            Double taxa = (ocupacao.doubleValue() / capacidade.doubleValue()) * 100.0;
                            clone.put("taxa", taxa);
                            Double meta = clone.getDouble("meta");
                            if (meta <= taxa) {
                                clone.put("valorBonus", clone.getDouble("bonificacao"));
                            } else {
                                clone.put("valorBonus", 0.0);
                            }
                        } catch (Exception e) {
                            clone.put("valorBonus", 0.0);
                        }

                        clone.put("ocupacao", ocupacao);
                        clone.put("presentes", presentes);

                        Set<Integer> cods = codAlunos;
                        Set<Integer> alunos = mapaAlunos.get(json.getInt("codigo"));
                        if (alunos == null) {
                            alunos = new HashSet<>();
                            mapaAlunos.put(json.getInt("codigo"), alunos);
                        }
                        alunos.addAll(cods);
                        array.put(clone);
                    }
                }
            }

        }

        if (semAgrupar) {
            return array;
        }

        Map<Integer, JSONObject> agrupamento = new HashMap<>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject aulaDia = array.getJSONObject(i);
            JSONObject aulaAgrupada = agrupamento.get(aulaDia.getInt("codigo"));
            if (aulaAgrupada == null) {
                aulaDia.put("aulas", 1);
                aulaDia.put("alunos", new JSONArray(totalizarAlunos ? mapaAlunos.get(aulaDia.getInt("codigo")) : new HashSet()));
                aulaDia.put("ocupacaoAcumulada", aulaDia.getInt("ocupacao"));
                aulaDia.put("presentesAcumulada", aulaDia.getInt("presentes"));
                aulaDia.put("capacidadeAcumulada", aulaDia.getInt("capacidade"));
                aulaDia.put("valorBonusAcumulado", aulaDia.getDouble("valorBonus"));
                agrupamento.put(aulaDia.getInt("codigo"), aulaDia);
            } else {
                aulaAgrupada.put("aulas", aulaAgrupada.getInt("aulas") + 1);
                aulaAgrupada.put("capacidadeAcumulada", aulaAgrupada.getInt("capacidadeAcumulada") + aulaDia.getInt("capacidade"));
                aulaAgrupada.put("presentesAcumulada", aulaAgrupada.getInt("presentesAcumulada") + aulaDia.getInt("presentes"));
                aulaAgrupada.put("ocupacaoAcumulada", aulaAgrupada.getInt("ocupacaoAcumulada") + aulaDia.getInt("ocupacao"));
                aulaAgrupada.put("valorBonusAcumulado", aulaAgrupada.getDouble("valorBonusAcumulado") + aulaDia.getDouble("valorBonus"));
            }
        }
        return new JSONArray(agrupamento.values());
    }

    public JSONArray ocupacaoAulaDia(Long inicio,
                                     Long fim,
                                     Integer empresa,
                                     Integer professor,
                                     String quicksearchValue, Boolean totalizarAlunos, Boolean semAgrupar) throws Exception {

        Date inicioDt = new Date(inicio);
        Date fimDt = Calendario.getDataComHora(new Date(fim), "23:59");

        StringBuilder sql = new StringBuilder();
        sql.append(" select \n");
        sql.append(" ht.codigo, \n");
        sql.append(" ht.horainicial, \n");
        sql.append(" ht.horafinal, \n");
        sql.append(" t.aulacoletiva, \n");
        sql.append(" ht.diasemana, \n");
        sql.append(" nrmaximoaluno as capacidade, \n");
        sql.append(" t.descricao, \n");
        sql.append(" t.meta, \n");
        sql.append(" t.bonificacao, \n");
        sql.append(" c.codigo as professor, \n");
        sql.append(" m.codigo as modalidade, \n");
        sql.append(" m.nome as nomeModalidade, \n");
        sql.append(" pes.nome as nomePessoa \n");
        sql.append(" from horarioturma ht \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append(" inner join colaborador c on c.codigo = ht.professor \n");
        sql.append(" inner join pessoa pes on pes.codigo = c.pessoa \n");
        sql.append(" inner join modalidade m on m.codigo = t.modalidade \n");
        sql.append(" and ht.situacao = 'AT' \n");
        sql.append(" and t.datainicialvigencia <= '"+fimDt+"' \n");
        sql.append(" AND t.datafinalvigencia >= '"+inicioDt+"' \n");
        sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" AND ht.professor = ").append(professor);
        }
        if (semAgrupar) {
            sql.append(" AND t.aulacoletiva ");
        }
        if (quicksearchValue != null && !quicksearchValue.equals("")) {
            sql.append(" AND (UPPER(t.descricao) LIKE UPPER('%").append(quicksearchValue).append("%') ");
            sql.append(" OR UPPER(m.nome) LIKE UPPER ('%").append(quicksearchValue).append("%') ");
            sql.append(" OR UPPER(pes.nome) LIKE UPPER ('%").append(quicksearchValue).append("%') ");
            sql.append(" ) ");
        }
        Map<DiaSemana, List<JSONObject>> mapaDiasSemana = new HashMap() {{
            for (DiaSemana dia : DiaSemana.values()) {
                put(dia, new ArrayList<>());
            }
        }};

        int contador = 0;
        Integer presencas = 0;
        Integer faltas = 0;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                DiaSemana diasemana = DiaSemana.getDiaSemana(rs.getString("diasemana"));
                if (diasemana != null) {
                    JSONObject json = new JSONObject();
                    json.put("codigo", rs.getInt("codigo"));
                    json.put("capacidade", rs.getInt("capacidade"));
                    json.put("diaSemana", diasemana.getDescricao());
                    json.put("aulacoletiva", rs.getBoolean("aulacoletiva"));
                    json.put("professor", rs.getInt("professor"));
                    json.put("meta", rs.getInt("meta"));
                    json.put("bonificacao", rs.getDouble("bonificacao"));
                    json.put("horario", rs.getString("horainicial"));
                    json.put("modalidade", rs.getInt("modalidade"));
                    json.put("aula", rs.getString("descricao"));
                    json.put("nomeModalidade", rs.getString("nomeModalidade"));
                    json.put("nomePessoa", rs.getString("nomePessoa"));
                    mapaDiasSemana.get(diasemana).add(json);
                }
            }
        }
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicioDt, fimDt);
        JSONArray array = new JSONArray();
        Map<Integer, Set<Integer>> mapaAlunos = new HashMap<>();
        for (Date dia : diasEntreDatas) {
            Calendar cal = Calendario.getInstance();
            cal.setTime(dia);
            DiaSemana diaSemana = DiaSemana.getDiaSemanaNumeral(cal.get(Calendar.DAY_OF_WEEK));
            List<JSONObject> aulas = mapaDiasSemana.get(diaSemana);
            for (JSONObject aula : aulas) {
                JSONObject clone = new JSONObject(aula, JSONObject.getNames(aula));
                clone.put("dia", dia.getTime());
                boolean aulacoletiva = aula.getBoolean("aulacoletiva");
                StringBuilder sqlOcupacao = new StringBuilder();
                StringBuilder sqlPresenca = new StringBuilder();
                StringBuilder sqlPresencaReposicao = new StringBuilder();
                if (aulacoletiva) {
                    sqlOcupacao.append("select p.codigo as pessoa, ");
                    sqlOcupacao.append("exists ( ");
                    sqlOcupacao.append("select cliente  from aulaconfirmada ");
                    sqlOcupacao.append("WHERE diaaula ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' ");
                    sqlOcupacao.append("and horario ="+aula.getInt("codigo")+" and cliente = c.codigo ");
                    sqlOcupacao.append(") as presente ");
                    sqlOcupacao.append("from alunohorarioturma aht ");
                    sqlOcupacao.append("inner join cliente c on c.codigo = aht.cliente ");
                    sqlOcupacao.append("inner join pessoa p on p.codigo = c.pessoa ");
                    sqlOcupacao.append("WHERE aht.dia = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  and aht.horarioturma = "+aula.getInt("codigo")+" ");

                } else {
                    sqlOcupacao.append("select p.codigo as pessoa, ");
                    sqlOcupacao.append("exists ( ");
                    sqlOcupacao.append("select p.codigo  from presenca p ");
                    sqlOcupacao.append("inner join matriculaalunohorarioturma maht2 on maht2.codigo = p.dadosturma ");
                    sqlOcupacao.append("where p.datapresenca ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' and  p.dadosturma = maht.codigo ");
                    sqlOcupacao.append(") as presente ");
                    sqlOcupacao.append("from matriculaalunohorarioturma  maht ");
                    sqlOcupacao.append("join pessoa p on p.codigo = maht.pessoa ");
                    sqlOcupacao.append("join cliente c on c.pessoa = p.codigo ");
                    sqlOcupacao.append("WHERE '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  BETWEEN datainicio and datafim  and horarioturma = "+aula.getInt("codigo")+" ");
                    sqlOcupacao.append("and c.codigo not in (select cliente  from auladesmarcada a where horarioturma = "+aula.getInt("codigo")+" and dataorigem = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"') ");

                    sqlPresencaReposicao.append(" select c.pessoa ,r.datapresenca from reposicao r ");
                    sqlPresencaReposicao.append(" inner join cliente c on c.codigo = r.cliente ");
                    sqlPresencaReposicao.append(" where r.horarioturma = ").append(aula.getInt("codigo"));;
                    sqlPresencaReposicao.append(" and r.datareposicao = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
                }
                Integer ocupacao = 0;
                Integer presentes = 0;
                Set<Integer> codAlunos = new HashSet<>();
                try (ResultSet rsOcupacao = SuperFacadeJDBC.criarConsulta(sqlOcupacao.toString(), con)) {
                    while (rsOcupacao.next()){
                        ocupacao++;
                        if (totalizarAlunos) {
                            codAlunos.add(rsOcupacao.getInt("pessoa"));
                        }
                        if(rsOcupacao.getBoolean("presente")){
                            presentes++;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (!UteisValidacao.emptyString(sqlPresencaReposicao.toString())) {
                    ResultSet rsPresencaReposicao = SuperFacadeJDBC.criarConsulta(sqlPresencaReposicao.toString(), con);
                    while (rsPresencaReposicao.next()){
                        ocupacao ++;
                        if (totalizarAlunos) {
                            codAlunos.add(rsPresencaReposicao.getInt("pessoa"));
                        }
                        if(rsPresencaReposicao.getDate("datapresenca")!=null){
                            presentes++;
                        }
                    }
                }

                Integer capacidade = clone.getInt("capacidade");

                try {
                    Double taxa = (ocupacao.doubleValue() / capacidade.doubleValue()) * 100.0;
                    clone.put("taxa", taxa);
                    Double meta = clone.getDouble("meta");
                    if (meta <= taxa) {
                        clone.put("valorBonus", clone.getDouble("bonificacao"));
                    } else {
                        clone.put("valorBonus", 0.0);
                    }
                } catch (Exception e) {
                    clone.put("valorBonus", 0.0);
                }

                clone.put("ocupacao", ocupacao);
                clone.put("presentes", presentes);

                Set<Integer> cods = codAlunos;
                Set<Integer> alunos = mapaAlunos.get(aula.getInt("codigo"));
                if (alunos == null) {
                    alunos = new HashSet<>();
                    mapaAlunos.put(aula.getInt("codigo"), alunos);
                }
                alunos.addAll(cods);
                array.put(clone);
            }
        }

        if (semAgrupar) {
            return array;
        }

        Map<Integer, JSONObject> agrupamento = new HashMap<>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject aulaDia = array.getJSONObject(i);
            JSONObject aulaAgrupada = agrupamento.get(aulaDia.getInt("codigo"));
            if (aulaAgrupada == null) {
                aulaDia.put("aulas", 1);
                aulaDia.put("alunos", new JSONArray(totalizarAlunos ? mapaAlunos.get(aulaDia.getInt("codigo")) : new HashSet()));
                aulaDia.put("ocupacaoAcumulada", aulaDia.getInt("ocupacao"));
                aulaDia.put("presentesAcumulada", aulaDia.getInt("presentes"));
                aulaDia.put("capacidadeAcumulada", aulaDia.getInt("capacidade"));
                aulaDia.put("valorBonusAcumulado", aulaDia.getDouble("valorBonus"));
                agrupamento.put(aulaDia.getInt("codigo"), aulaDia);
            } else {
                aulaAgrupada.put("aulas", aulaAgrupada.getInt("aulas") + 1);
                aulaAgrupada.put("capacidadeAcumulada", aulaAgrupada.getInt("capacidadeAcumulada") + aulaDia.getInt("capacidade"));
                aulaAgrupada.put("presentesAcumulada", aulaAgrupada.getInt("presentesAcumulada") + aulaDia.getInt("presentes"));
                aulaAgrupada.put("ocupacaoAcumulada", aulaAgrupada.getInt("ocupacaoAcumulada") + aulaDia.getInt("ocupacao"));
                aulaAgrupada.put("valorBonusAcumulado", aulaAgrupada.getDouble("valorBonusAcumulado") + aulaDia.getDouble("valorBonus"));
            }
        }
        return new JSONArray(agrupamento.values());
    }

    private void addAula(Map<String, AulaOcupacao> mapa, String chave, Integer capacidade, Integer ocupacao, String nome) {
        AulaOcupacao aulaOcupacao = mapa.get(chave);
        if (aulaOcupacao == null) {
            aulaOcupacao = new AulaOcupacao();
            aulaOcupacao.nome = nome;
            mapa.put(chave, aulaOcupacao);
        }
        aulaOcupacao.capacidade += capacidade;
        aulaOcupacao.ocupacao += ocupacao;
    }

    class AulaOcupacao {
        int capacidade = 0;
        int ocupacao = 0;
        String nome = "";
    }

    public JSONArray aulasBi(Long inicio, Long fim, Integer empresa, Integer professor, Integer codigoRelatorio, String quicksearchValue) throws Exception {
        JSONArray array = ocupacaoAulaDia(inicio, fim, empresa, professor, quicksearchValue, false);
        array = processarAulasPorCodRelatorio(array, codigoRelatorio);
        return array;
    }

    public JSONArray aulasBonus(Long inicio, Long fim, Integer empresa, Integer professor, Integer codigoRelatorio, String quicksearchValue) throws Exception {
        return ocupacaoAulaDia(inicio, fim, empresa, professor, quicksearchValue, false, true);
    }

    public void reset(Integer codAula) throws Exception {
        SuperFacadeJDBC.executarConsulta("update turma set idclassegympass = null, produtogympass = null where codigo = " + codAula, con);
    }

    public JSONArray processarAulasPorCodRelatorio(JSONArray array, Integer codigoRelatorio) {
        JSONArray retorno = new JSONArray();
        for (int i = 0; i < array.length(); i++) {
            JSONObject aula = array.getJSONObject(i);
            Integer capacidade = aula.optInt("capacidadeAcumulada");
            Integer ocupacao = aula.optInt("ocupacaoAcumulada");
            if (capacidade > 0) {
                if (codigoRelatorio == -1) {
                    retorno.put(aula);
                } else {
                    Double taxa = (ocupacao.doubleValue() / capacidade.doubleValue()) * 100.0;
                    if (codigoRelatorio == 0 && taxa < 25.0) {
                        retorno.put(aula);
                    } else if (codigoRelatorio == 1 && taxa >= 25.0 && taxa < 50.0) {
                        retorno.put(aula);
                    } else if (codigoRelatorio == 2 && taxa >= 50.0 && taxa < 75.0) {
                        retorno.put(aula);
                    } else if (codigoRelatorio == 3 && taxa >= 75.0) {
                        retorno.put(aula);
                    }
                }
            }
        }
        return retorno;
    }

    public JSONArray obterModalidades(Long inicio, Long fim, Integer empresa, Integer professor,
                                      Integer codigoRelatorio, String quicksearchValue) throws Exception {
        JSONArray array = ocupacaoAulaDia(inicio, fim, empresa, professor, quicksearchValue, false);

        Map<String, JSONObject> agrupamento = new HashMap<>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject aulaDia = array.getJSONObject(i);
            JSONObject modalidadeAgrupada = agrupamento.get(aulaDia.getString("nomeModalidade"));
            String prof = Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(Uteis.getNomeAbreviado(aulaDia.getString("nomePessoa")).toLowerCase());
            if (modalidadeAgrupada == null) {
                modalidadeAgrupada = new JSONObject();
                modalidadeAgrupada.put("professores", prof);
                modalidadeAgrupada.put("nome", aulaDia.getString("nomeModalidade"));
                modalidadeAgrupada.put("aulas", aulaDia.getInt("aulas"));
                modalidadeAgrupada.put("capacidadeAcumulada", aulaDia.getInt("capacidadeAcumulada"));
                modalidadeAgrupada.put("presentesAcumulada", aulaDia.getInt("presentesAcumulada"));
                modalidadeAgrupada.put("ocupacaoAcumulada", aulaDia.getInt("ocupacaoAcumulada"));
                agrupamento.put(aulaDia.getString("nomeModalidade"), modalidadeAgrupada);
            } else {
                if (!modalidadeAgrupada.getString("professores").contains(prof)) {
                    modalidadeAgrupada.put("professores", modalidadeAgrupada.getString("professores") + ", " + prof);
                }
                modalidadeAgrupada.put("aulas", modalidadeAgrupada.getInt("aulas") + aulaDia.getInt("aulas"));
                modalidadeAgrupada.put("capacidadeAcumulada", modalidadeAgrupada.getInt("capacidadeAcumulada") + aulaDia.getInt("capacidadeAcumulada"));
                modalidadeAgrupada.put("presentesAcumulada", modalidadeAgrupada.getInt("presentesAcumulada") + aulaDia.getInt("presentesAcumulada"));
                modalidadeAgrupada.put("ocupacaoAcumulada", modalidadeAgrupada.getInt("ocupacaoAcumulada") + aulaDia.getInt("ocupacaoAcumulada"));
            }
        }

        return processarAulasPorCodRelatorio(new JSONArray(agrupamento.values()), codigoRelatorio);
    }

    public JSONArray obterProfessoresBiDashAgenda(Long inicio, Long fim, Integer empresa,
                                                  Integer professor, Integer codigoRelatorio, String quicksearchValue, String mapaAulasExcluidas, String mapaProfessoresSubstituidos) throws Exception {
        JSONArray array = ocupacaoAulaDiaV2(inicio, fim, empresa, professor, quicksearchValue, false, false, mapaAulasExcluidas,mapaProfessoresSubstituidos);

        Map<String, JSONObject> agrupamento = new HashMap<>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject aulaDia = array.getJSONObject(i);
            JSONObject modalidadeAgrupada = agrupamento.get(aulaDia.getString("nomePessoa"));
            String modalidade = Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(aulaDia.getString("nomeModalidade").toLowerCase());
            if (modalidadeAgrupada == null) {
                modalidadeAgrupada = new JSONObject();
                modalidadeAgrupada.put("professor", aulaDia.getString("nomePessoa"));
                modalidadeAgrupada.put("modalidades", modalidade);
                modalidadeAgrupada.put("nome", aulaDia.getString("nomeModalidade"));
                modalidadeAgrupada.put("aulas", aulaDia.getInt("aulas"));
                modalidadeAgrupada.put("capacidadeAcumulada", aulaDia.getInt("capacidadeAcumulada"));
                modalidadeAgrupada.put("presentesAcumulada", aulaDia.getInt("presentesAcumulada"));
                modalidadeAgrupada.put("ocupacaoAcumulada", aulaDia.getInt("ocupacaoAcumulada"));
                agrupamento.put(aulaDia.getString("nomePessoa"), modalidadeAgrupada);
            } else {
                if (!modalidadeAgrupada.getString("modalidades").contains(modalidade)) {
                    modalidadeAgrupada.put("modalidades", modalidadeAgrupada.getString("modalidades") + ", " + modalidade);
                }
                modalidadeAgrupada.put("aulas", modalidadeAgrupada.getInt("aulas") + aulaDia.getInt("aulas"));
                modalidadeAgrupada.put("capacidadeAcumulada", modalidadeAgrupada.getInt("capacidadeAcumulada") + aulaDia.getInt("capacidadeAcumulada"));
                modalidadeAgrupada.put("presentesAcumulada", modalidadeAgrupada.getInt("presentesAcumulada") + aulaDia.getInt("presentesAcumulada"));
                modalidadeAgrupada.put("ocupacaoAcumulada", modalidadeAgrupada.getInt("ocupacaoAcumulada") + aulaDia.getInt("ocupacaoAcumulada"));
            }
        }

        return processarAulasPorCodRelatorio(new JSONArray(agrupamento.values()), codigoRelatorio);
    }

    public JSONArray processarListagemPorCodRelatorio(Map<String, AulaOcupacao> lista, Integer codigoRelatorio) {
        JSONArray retorno = new JSONArray();
        for (String chave : lista.keySet()) {
            AulaOcupacao aula = lista.get(chave);
            Integer capacidade = aula.capacidade;
            Integer ocupacao = aula.ocupacao;
            if (capacidade > 0) {
                if (codigoRelatorio == -1) {
                    retorno.put(setarValoresListagem(capacidade, ocupacao, chave, aula.nome));
                } else {
                    Double taxa = (ocupacao.doubleValue() / capacidade.doubleValue()) * 100.0;
                    if (codigoRelatorio == 0 && taxa < 25.0) {
                        retorno.put(setarValoresListagem(capacidade, ocupacao, chave, aula.nome));
                    } else if (codigoRelatorio == 1 && taxa >= 25.0 && taxa < 50.0) {
                        retorno.put(setarValoresListagem(capacidade, ocupacao, chave, aula.nome));
                    } else if (codigoRelatorio == 2 && taxa >= 50.0 && taxa < 75.0) {
                        retorno.put(setarValoresListagem(capacidade, ocupacao, chave, aula.nome));
                    } else if (codigoRelatorio == 3 && taxa >= 75.0) {
                        retorno.put(setarValoresListagem(capacidade, ocupacao, chave, aula.nome));
                    }
                }
            }
        }
        return retorno;
    }

    public JSONObject setarValoresListagem(Integer capacidade, Integer ocupacao, String chave, String nome) {
        JSONObject itemLista = new JSONObject();
        itemLista.put("codigo", chave);
        itemLista.put("capacidade", capacidade);
        itemLista.put("ocupacao", ocupacao);
        itemLista.put("nome", nome);
        return itemLista;
    }

    public JSONArray obterFrequenciaAlunosBiDashAgenda(Long inicio, Long fim, Integer empresa, Integer professor,
                                                       Integer codigoRelatorio, String quicksearchValue) throws Exception {
        JSONArray array = processarFrequenciaAlunos(inicio, fim, empresa, professor, codigoRelatorio, quicksearchValue, true);
        return array;
    }

    public JSONArray processarFrequenciaAlunos(Long inicio,
                                               Long fim,
                                               Integer empresa,
                                               Integer professor,
                                               Integer codigoRelatorio,
                                               String quicksearchValue, Boolean avaliarPresencas) throws Exception {

        Date inicioDt = new Date(inicio);
        Date fimDt = Calendario.getDataComHora(new Date(fim), "23:59");
        StringBuilder sql = new StringBuilder();
        sql.append(" select \n");
        sql.append(" ht.codigo, \n");
        sql.append(" ht.horainicial, \n");
        sql.append(" ht.horafinal, \n");
        sql.append(" t.aulacoletiva, \n");
        sql.append(" ht.diasemana, \n");
        sql.append(" nrmaximoaluno as capacidade, \n");
        sql.append(" t.descricao, \n");
        sql.append(" c.codigo as professor, \n");
        sql.append(" m.codigo as modalidade, \n");
        sql.append(" m.nome as nomeModalidade, \n");
        sql.append(" pes.nome as nomePessoa, \n");
        sql.append(" pes.cfp as cpfPessoa \n");
        sql.append(" from horarioturma ht \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append(" inner join colaborador c on c.codigo = ht.professor \n");
        sql.append(" inner join pessoa pes on pes.codigo = c.pessoa \n");
        sql.append(" inner join modalidade m on m.codigo = t.modalidade \n");
        sql.append(" and ht.situacao = 'AT' \n");
        sql.append(" and t.datafinalvigencia > '").append(Calendario.getDataAplicandoFormatacao(inicioDt, "yyyy-MM-dd")).append("' ");
        sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" AND ht.professor = ").append(professor);
        }
        Map<DiaSemana, List<JSONObject>> mapaDiasSemana = new HashMap() {{
            for (DiaSemana dia : DiaSemana.values()) {
                put(dia, new ArrayList<>());
            }
        }};
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                DiaSemana diasemana = DiaSemana.getDiaSemana(rs.getString("diasemana"));
                if (diasemana != null) {
                    JSONObject json = new JSONObject();
                    json.put("codigo", rs.getInt("codigo"));
                    json.put("capacidade", rs.getInt("capacidade"));
                    json.put("aulacoletiva", rs.getBoolean("aulacoletiva"));
                    json.put("professor", rs.getInt("professor"));
                    json.put("horario", rs.getString("horainicial") + " - " + rs.getString("horafinal"));
                    json.put("modalidade", rs.getInt("modalidade"));
                    json.put("aula", rs.getString("descricao"));
                    json.put("nomeModalidade", rs.getString("nomeModalidade"));
                    json.put("nomePessoa", rs.getString("nomePessoa"));
                    json.put("cpf", rs.getString("cpfPessoa"));
                    mapaDiasSemana.get(diasemana).add(json);
                }
            }
        }
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicioDt, fimDt);
        JSONArray array = new JSONArray();
        for (Date dia : diasEntreDatas) {
            Calendar cal = Calendario.getInstance();
            cal.setTime(dia);
            DiaSemana diaSemana = DiaSemana.getDiaSemanaNumeral(cal.get(Calendar.DAY_OF_WEEK));
            List<JSONObject> aulas = mapaDiasSemana.get(diaSemana);

            for (JSONObject aula : aulas) {
                boolean aulacoletiva = aula.getBoolean("aulacoletiva");
                StringBuilder sqlOcupacao = new StringBuilder();
                StringBuilder sqlPresencaReposicao = new StringBuilder();
                if (aulacoletiva) {
                    sqlOcupacao.append(" select p.codigo, ");
                    sqlOcupacao.append(" exists (select cliente from aulaconfirmada ");
                    sqlOcupacao.append(" WHERE diaaula = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' ");
                    sqlOcupacao.append(" and horario = ").append(aula.getInt("codigo"));
                    sqlOcupacao.append(" and cliente = aht.cliente");
                    sqlOcupacao.append(" ) as presente,  p.nome as nomeAluno,p.cfp as cpf ");
                    sqlOcupacao.append("from alunohorarioturma aht ");
                    sqlOcupacao.append("inner join cliente c on c.codigo = aht.cliente ");
                    sqlOcupacao.append("inner join pessoa p on p.codigo = c.pessoa ");
                    sqlOcupacao.append("inner join situacaoclientesinteticodw cli on cli.codigopessoa = p.codigo ");
                    sqlOcupacao.append("WHERE aht.dia = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  and aht.horarioturma = "+aula.getInt("codigo")+" ");

                    if (quicksearchValue != null && !quicksearchValue.equals("")) {
                        int matricula = 0;
                        try {
                            matricula = Integer.parseInt(quicksearchValue);
                        } catch (Exception e) {
                        }

                        if (matricula == 0) {
                            sqlOcupacao.append(" AND cli.nomeconsulta LIKE remove_acento_upper('").append(quicksearchValue).append("%') ");
                        } else {
                            sqlOcupacao.append(" AND cli.matricula = ").append(matricula).append(" ");
                        }
                    }

                } else {
                    sqlOcupacao.append("select p.codigo, ");
                    sqlOcupacao.append("exists ( ");
                    sqlOcupacao.append("select p.codigo  from presenca p ");
                    sqlOcupacao.append("inner join matriculaalunohorarioturma maht2 on maht2.codigo = p.dadosturma ");
                    sqlOcupacao.append("where p.datapresenca ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' and  p.dadosturma = maht.codigo ");
                    sqlOcupacao.append(") as presente,p.nome as nomeAluno, p.cfp as cpf ");
                    sqlOcupacao.append("from matriculaalunohorarioturma  maht ");
                    sqlOcupacao.append("join pessoa p on p.codigo = maht.pessoa ");
                    sqlOcupacao.append("join cliente c on c.pessoa = p.codigo ");
                    sqlOcupacao.append("inner join situacaoclientesinteticodw cli on cli.codigopessoa = p.codigo  ");
                    sqlOcupacao.append("WHERE '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"'  BETWEEN datainicio and datafim  and horarioturma = "+aula.getInt("codigo")+" ");
                    sqlOcupacao.append("and c.codigo not in (select cliente  from auladesmarcada a where horarioturma = "+aula.getInt("codigo")+" and dataorigem = '"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"') ");

                    sqlPresencaReposicao.append("select p.codigo ,r.datapresenca IS not null as presente,p.nome as nomeAluno,p.cfp as cpf ");
                    sqlPresencaReposicao.append("from reposicao r  ");
                    sqlPresencaReposicao.append("inner join cliente c on c.codigo = r.cliente ");
                    sqlPresencaReposicao.append("inner join pessoa p on p.codigo = c.pessoa ");
                    sqlPresencaReposicao.append("inner join situacaoclientesinteticodw cli on cli.codigopessoa = p.codigo  ");
                    sqlPresencaReposicao.append("where r.horarioturma =  "+aula.getInt("codigo")+" and datareposicao ='"+Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")+"' ");

                    if (quicksearchValue != null && !quicksearchValue.equals("")) {
                        int matricula = 0;
                        try {
                            matricula = Integer.parseInt(quicksearchValue);
                        } catch (Exception e) {
                        }

                        if (matricula == 0) {
                            sqlOcupacao.append(" AND cli.nomeconsulta LIKE remove_acento_upper('").append(quicksearchValue).append("%') ");
                            sqlPresencaReposicao.append(" AND cli.nomeconsulta LIKE remove_acento_upper('").append(quicksearchValue).append("%') ");
                        } else {
                            sqlOcupacao.append(" AND cli.matricula = ").append(matricula).append(" ");
                            sqlPresencaReposicao.append(" AND cli.matricula = ").append(matricula).append(" ");
                        }
                    }
                }

                Set<Integer> processedAlunos = new HashSet<>();
                try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlOcupacao.toString(), con)) {
                    while (rs.next()) {
                        JSONObject clone2 = new JSONObject(aula, JSONObject.getNames(aula));
                        clone2.put("dia", dia.getTime());
                        if (avaliarPresencas) {
                            clone2.put("presente", rs.getBoolean("presente"));
                        } else {
                            clone2.put("presente", false);
                        }
                        clone2.put("codPessoa", rs.getInt("codigo"));
                        clone2.put("nomeAluno", rs.getString("nomeAluno"));
                        clone2.put("cpf", rs.getString("cpf"));
                        if (codigoRelatorio != -1) {
                            if (codigoRelatorio == 0) {//Presentes
                                if(rs.getBoolean("presente")){
                                    array.put(clone2);
                                    if (!processedAlunos.contains(rs.getInt("codigo"))) {
                                        processedAlunos.add(rs.getInt("codigo"));
                                    }
                                }

                            } else {//faltosos
                                if(!rs.getBoolean("presente")){
                                    array.put(clone2);
                                    if (!processedAlunos.contains(rs.getInt("codigo"))) {
                                        processedAlunos.add(rs.getInt("codigo"));
                                    }
                                }
                            }
                        }else{
                            array.put(clone2);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (!UteisValidacao.emptyString(sqlPresencaReposicao.toString())) {
                    ResultSet rsPresencaReposicao = SuperFacadeJDBC.criarConsulta(sqlPresencaReposicao.toString(), con);
                    while (rsPresencaReposicao.next()) {
                        JSONObject clone2 = new JSONObject(aula, JSONObject.getNames(aula));
                        clone2.put("dia", dia.getTime());
                        clone2.put("presente", rsPresencaReposicao.getBoolean("presente"));
                        clone2.put("codPessoa", rsPresencaReposicao.getInt("codigo"));
                        clone2.put("nomeAluno", rsPresencaReposicao.getString("nomeAluno"));
                        clone2.put("cpf", rsPresencaReposicao.getString("cpf"));
                        if (codigoRelatorio != -1) {
                            if (codigoRelatorio == 0) {//Presentes
                                if(rsPresencaReposicao.getBoolean("presente")){
                                    array.put(clone2);
                                    if (!processedAlunos.contains(rsPresencaReposicao.getInt("codigo"))) {
                                        processedAlunos.add(rsPresencaReposicao.getInt("codigo"));
                                    }
                                }
                            } else {//faltosos
                                if(!rsPresencaReposicao.getBoolean("presente")){
                                    array.put(clone2);
                                    if (!processedAlunos.contains(rsPresencaReposicao.getInt("codigo"))) {
                                        processedAlunos.add(rsPresencaReposicao.getInt("codigo"));
                                    }
                                }
                            }
                        }else{
                            array.put(clone2);
                        }
                    }
                }
            }
        }

        if (!avaliarPresencas) {
            return array;
        }

        Map<Integer, JSONObject> agrupamento = new HashMap<>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            String modalidade = Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(jsonObject.getString("nomeModalidade").toLowerCase());
            JSONObject pessoaAgrupada = agrupamento.get(jsonObject.getInt("codPessoa"));
            if (pessoaAgrupada == null) {
                pessoaAgrupada = new JSONObject();
                pessoaAgrupada.put("nome", jsonObject.getString("nomeAluno"));
                pessoaAgrupada.put("aulas", 1);
                pessoaAgrupada.put("modalidades", modalidade);
                pessoaAgrupada.put("presencas", jsonObject.getBoolean("presente") ? 1 : 0);
                agrupamento.put(jsonObject.getInt("codPessoa"), pessoaAgrupada);
            } else {
                if (!pessoaAgrupada.getString("modalidades").contains(modalidade)) {
                    pessoaAgrupada.put("modalidades", pessoaAgrupada.getString("modalidades") + ", " + modalidade);
                }
                pessoaAgrupada.put("aulas", pessoaAgrupada.getInt("aulas") + 1);
                pessoaAgrupada.put("presencas", pessoaAgrupada.getInt("presencas") + (jsonObject.getBoolean("presente") ? 1 : 0));
            }
        }
        return new JSONArray(agrupamento.values());
    }

    private Set<Integer> alunosAula(JSONObject aula, Date dia) throws Exception {
        Set<Integer> alunos = new HashSet<>();
        String sqlConsulta = sqlAlunosAula(false, aula, dia, -1, null);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlConsulta, con)) {
            while (rs.next()) {
                alunos.add(rs.getInt("codigo"));
            }
        }
        return alunos;
    }


    private String sqlAlunosAula(Boolean avaliarPresencas, JSONObject aula, Date dia, Integer codigoRelatorio, String quicksearchValue) {
        boolean aulacoletiva = aula.getBoolean("aulacoletiva");
        StringBuilder sqlConsulta = new StringBuilder();
        if (aulacoletiva) {
            sqlConsulta.append(" select p.codigo, ");

            if (avaliarPresencas) {
                sqlConsulta.append(" exists (select cliente from aulaconfirmada ");
                sqlConsulta.append(" WHERE diaaula = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' ");
                sqlConsulta.append(" and cliente = aht.cliente");
                sqlConsulta.append(" and horario = ").append(aula.getInt("codigo"));
                sqlConsulta.append(" ) as presente, ");
            }

            sqlConsulta.append(" p.nome as nomeAluno, p.cfp as cpf from alunohorarioturma aht ");
            sqlConsulta.append(" inner join cliente c on c.codigo = aht.cliente ");
            sqlConsulta.append(" inner join pessoa p on p.codigo = c.pessoa ");
            sqlConsulta.append(" inner join situacaoclientesinteticodw cli on cli.codigopessoa = p.codigo ");
            sqlConsulta.append(" inner join horarioturma ht on ht.codigo = aht.horarioturma ");
            sqlConsulta.append(" inner join colaborador c2 on c2.codigo = ht.professor ");
            sqlConsulta.append(" inner join pessoa p2 on p2.codigo = c2.pessoa ");
            sqlConsulta.append(" inner join turma t on t.codigo = ht.turma ");
            sqlConsulta.append(" inner join modalidade m on m.codigo = t.modalidade ");
            sqlConsulta.append(" WHERE aht.dia = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' ");
            sqlConsulta.append(" and horarioturma = ").append(aula.getInt("codigo"));
            if (codigoRelatorio != -1) {
                if (codigoRelatorio == 0) {
                    sqlConsulta.append(" and aht.cliente in ( ");
                } else {
                    sqlConsulta.append(" and aht.cliente not in ( ");
                }
                sqlConsulta.append(" select distinct cliente from aulaconfirmada ");
                sqlConsulta.append(" WHERE diaaula = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' ");
                sqlConsulta.append(" and horario = ").append(aula.getInt("codigo"));
                sqlConsulta.append(" ) ");
            }
            if (quicksearchValue != null && !quicksearchValue.equals("")) {
                int matricula = 0;
                try {
                    matricula = Integer.parseInt(quicksearchValue);
                } catch (Exception e) {
                }

                if (matricula == 0) {
                    sqlConsulta.append(" AND cli.nomeconsulta LIKE remove_acento_upper('").append(quicksearchValue).append("%') ");
                } else {
                    sqlConsulta.append(" AND cli.matricula = ").append(matricula).append(" ");
                }
            }
        } else {
            sqlConsulta.append(" select distinct p.codigo, ");

            if (avaliarPresencas) {
                sqlConsulta.append(" exists(select maht.codigo from presenca p ");
                sqlConsulta.append(" inner join matriculaalunohorarioturma maht on maht.codigo = p.dadosturma ");
                sqlConsulta.append(" where p.datapresenca  = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd"));
                sqlConsulta.append("' and p.dadosturma = m.codigo ");
                sqlConsulta.append("  and maht.horarioturma = ").append(aula.getInt("codigo"));
                sqlConsulta.append(" ) as presente, ");
            }

            sqlConsulta.append(" p.nome as nomeAluno, p.cfp as cpf from matriculaalunohorarioturma m ");
            sqlConsulta.append(" inner join pessoa p on p.codigo = m.pessoa  ");
            sqlConsulta.append(" inner join situacaoclientesinteticodw cli on cli.codigopessoa = p.codigo ");
            sqlConsulta.append(" inner join horarioturma ht on ht.codigo = m.horarioturma ");
            sqlConsulta.append(" inner join colaborador c on c.codigo = ht.professor  ");
            sqlConsulta.append(" inner join pessoa p2 on p2.codigo = c.pessoa ");
            sqlConsulta.append(" inner join turma t on t.codigo = ht.turma ");
            sqlConsulta.append(" inner join modalidade m2 on m2.codigo = t.modalidade ");
            if (codigoRelatorio != -1) {
                if (codigoRelatorio > 0) {
                    sqlConsulta.append(" right join reposicao r on r.cliente = cli.codigocliente  ");
                }
            }
            sqlConsulta.append(" WHERE '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' ");
            sqlConsulta.append(" BETWEEN datainicio and datafim ");
            sqlConsulta.append(" and m.horarioturma = ").append(aula.getInt("codigo"));
            if (codigoRelatorio != -1) {
                if (codigoRelatorio == 0) {
                    sqlConsulta.append(" and m.codigo in ( ");
                } else {
                    sqlConsulta.append(" and m.codigo not in ( ");
                }
                sqlConsulta.append(" select distinct maht.codigo from presenca p ");
                sqlConsulta.append(" inner join matriculaalunohorarioturma maht on maht.codigo = p.dadosturma ");
                sqlConsulta.append(" where p.datapresenca  = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd"));
                sqlConsulta.append("' and maht.horarioturma = ").append(aula.getInt("codigo")).append(" )");
            }
            if (quicksearchValue != null && !quicksearchValue.equals("")) {
                int matricula = 0;
                try {
                    matricula = Integer.parseInt(quicksearchValue);
                } catch (Exception e) {
                }

                if (matricula == 0) {
                    sqlConsulta.append(" AND cli.nomeconsulta LIKE remove_acento_upper('").append(quicksearchValue).append("%') ");
                } else {
                    sqlConsulta.append(" AND cli.matricula = ").append(matricula).append(" ");
                }
            }
        }
        return sqlConsulta.toString();
    }

    private String sqlReposicaoAula(JSONObject aula, Date dia, Integer codigoRelatorio) {
        StringBuilder sqlConsulta = new StringBuilder();
        sqlConsulta.append(" select distinct ");
        sqlConsulta.append(" p.codigo, ");
        sqlConsulta.append(" p.nome as nomeAluno, ");
        sqlConsulta.append(" p.cfp as cpf ");
        sqlConsulta.append(" from reposicao r ");
        sqlConsulta.append(" inner join situacaoclientesinteticodw s on s.codigocliente = r.cliente ");
        sqlConsulta.append(" inner join pessoa p on s.codigopessoa = p.codigo ");
        sqlConsulta.append(" where r.horarioturma = ").append(aula.getInt("codigo"));
        sqlConsulta.append(" and r.datapresenca = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("';");
        return sqlConsulta.toString();
    }


    private String sqlConsultaAulaExperimental(JSONObject aula, Date dia) {
        StringBuilder sqlConsulta = new StringBuilder();
        sqlConsulta.append(" select p.codigo, p.nome as nomeAluno, p.cfp as cpf ");
        sqlConsulta.append(" from matriculaalunohorarioturma m ");
        sqlConsulta.append(" inner join pessoa p on m.pessoa = p.codigo ");
        sqlConsulta.append(" inner join situacaoclientesinteticodw s on s.codigopessoa = p.codigo ");
        sqlConsulta.append(" right join reposicao r on r.cliente = s.codigocliente ");
        sqlConsulta.append(" where m.horarioturma = ").append(aula.getInt("codigo"));
        sqlConsulta.append(" and r.datapresenca is null ");
        sqlConsulta.append(" and r.datareposicao = '").append(Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' ");
        sqlConsulta.append(" order by p.nome asc ");
        return sqlConsulta.toString();
    }


    public String modalidadesTitular(String matricula) throws Exception {
        String modalidades = "";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select tit.modalidades from cliente cli\n" +
                "inner join situacaoclientesinteticodw tit on tit.codigocliente = cli.titularplanocompartilhado \n" +
                "where cli.titularplanocompartilhado is not null and cli.codigomatricula = " + matricula, con)) {
            if (rs.next()) {
                modalidades = rs.getString("modalidades");
            }
        }
        return modalidades;
    }

    public JSONArray confirmadosPeriodo(Date inicio, Date fim, Integer empresa,
                                        boolean buscarProfessorAcompanhou) throws Exception {
        if (Uteis.nrDiasEntreDatas(inicio, fim) > 31) {
            throw new Exception("intervalo não pode ser maior que 31 dias");
        }
        JSONArray array = new JSONArray();
        Boolean enviarCpfComoCodigoInterno = isUsarCpfCodigoInternoWeHelp(empresa);
        JSONArray jsonArray = processarFrequenciaAlunos(inicio.getTime(), fim.getTime(), empresa, null, 0, "", false);

        Map<Integer, Map<String, JSONObject>> mapaProfessorAcompanhou = new HashMap<>();
        if (buscarProfessorAcompanhou) {
            mapaProfessorAcompanhou = obterMapaProfessorAcompanhou(inicio, fim, empresa);
        }

        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonPessoa = jsonArray.getJSONObject(i);
            Integer codPessoa = jsonPessoa.getInt("codPessoa");
            JSONObject json = new JSONObject();
            if (enviarCpfComoCodigoInterno) {
                json.put("internal_code", jsonPessoa.getString("cpf"));
            }
            json.put("dia", Uteis.getData(new Date(jsonPessoa.getLong("dia"))));
            json.put("nome", jsonPessoa.getString("nomeAluno"));
            json.put("modalidade", jsonPessoa.getString("nomeModalidade"));
            json.put("horario", jsonPessoa.getString("horario"));
            json.put("turma", jsonPessoa.getString("aula"));
            json.put("professor", jsonPessoa.getString("nomePessoa"));
            StringBuilder sql = new StringBuilder();
            sql.append(" select distinct s.matricula,");
            sql.append(" s.telefonescliente,");
            sql.append(" s.datanascimento,");
            sql.append(" s.datacadastro,");
            sql.append(" pa.nome as pais,");
            sql.append(" es.descricao as estado,");
            sql.append("         s.codigocliente,");
            sql.append("         s.sexocliente,");
            sql.append("         pl.descricao as nomeplano,");
            sql.append(" pl.codigo as codigoplano,");
            sql.append("         (select email from email e where pessoa = pes.codigo limit 1) as email");
            sql.append(" from situacaoclientesinteticodw s");
            sql.append(" inner join pessoa pes on pes.codigo = s.codigopessoa");
            sql.append(" left join pais pa on pa.codigo = pes.pais");
            sql.append(" left join estado es on es.codigo = pes.estado");
            sql.append(" left join contrato ct on ct.codigo = s.codigocontrato");
            sql.append(" left join plano pl on pl.codigo = ct.plano");
            sql.append(" where s.codigopessoa = ").append(codPessoa);
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    json.put("matricula", rs.getString("matricula"));
                    json.put("email", rs.getString("email"));
                    json.put("telefone", rs.getString("telefonescliente"));
                    json.put("dataNascimento", Uteis.getData(rs.getDate("datanascimento")));
                    json.put("dataCadastro", Uteis.getData(rs.getDate("datacadastro")));
                    json.put("pais", rs.getString("pais"));
                    json.put("estado", rs.getString("estado"));
                    json.put("sexo", rs.getString("sexocliente"));
                    json.put("codigoCliente", rs.getString("codigocliente"));
                    json.put("nomePlano", rs.getString("nomeplano"));
                    json.put("idPlano", rs.getInt("codigoplano"));

                    if (buscarProfessorAcompanhou) {
                        String professorAcompanhou = "";
                        try {
                            String matricula = rs.getString("matricula");
                            Date dia = new Date(jsonPessoa.getLong("dia"));
                            String diaString = Calendario.getDataAplicandoFormatacao(dia, "yyyyMMdd");
                            Map<String, JSONObject> mapaC = mapaProfessorAcompanhou.get(Integer.parseInt(matricula));
                            if (mapaC != null && mapaC.containsKey(diaString)) {
                                JSONObject jsonItem = mapaC.get(diaString);
                                professorAcompanhou = jsonItem.getJSONObject("professor").optString("nome");
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        } finally {
                            json.put("professorAcompanhou", professorAcompanhou);
                        }
                    }
                    array.put(json);
                }
            }
        }
        return array;
    }

    private Map<Integer, Map<String, JSONObject>> obterMapaProfessorAcompanhou(Date inicio, Date fim, Integer empresa) {
        Map<Integer, Map<String, JSONObject>> mapaGeral = new HashMap<>();
        try {
            String key = DAO.resolveKeyFromConnection(this.con);
            String dtInicio = Calendario.getDataAplicandoFormatacao(inicio, "yyyyMMdd");
            String dtFim = Calendario.getDataAplicandoFormatacao(fim, "yyyyMMdd");
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
            String path = (urlTreino + "/prest/cliente/" + key + "/cliente-acompanhamento");

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            Map<String, String> params = new HashMap<>();
            params.put("dtInicio", dtInicio);
            params.put("dtFim", dtFim);
            params.put("empresa", empresa.toString());

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, params, null, MetodoHttpEnum.POST);
            service = null;

            JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
            JSONArray array = jsonResp.getJSONArray("content");
            for (int i = 0; i < array.length(); i++) {
                try {
                    JSONObject jsonItem = array.getJSONObject(i);
                    if (!jsonItem.has("cliente")) {
                        continue;
                    }
                    Integer matricula = jsonItem.getJSONObject("cliente").getInt("matricula");
                    Map<String, JSONObject> mapaDia = mapaGeral.get(matricula);
                    if (mapaDia == null) {
                        mapaDia = new HashMap<>();
                    }
                    Date inicioItem = new Date(jsonItem.getLong("inicio"));
                    String inicioItemString = Calendario.getDataAplicandoFormatacao(inicioItem, "yyyyMMdd");
                    if (!mapaDia.containsKey(inicioItemString)) {
                        mapaDia.put(inicioItemString, jsonItem);
                    }
                    mapaGeral.put(matricula, mapaDia);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return mapaGeral;
    }

    private Boolean isUsarCpfCodigoInternoWeHelp(Integer empresa) {
        try (ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT cpfCodigoInternoWeHelp FROM empresa WHERE codigo = " + empresa, con)) {
            if (resultSet.next()) {
                return resultSet.getBoolean("cpfCodigoInternoWeHelp");
            }
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return false;
    }

    public JSONObject dadosBooked(Integer idhorario, Date dia) {
        JSONObject obj = new JSONObject();
        try {
            ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select urlturmavirtual, idclassegympass, ht.horainicial, t.empresa from turma t" +
                    " inner join horarioturma ht on ht.turma = t.codigo" +
                    " where ht.codigo = " + idhorario, con);
            if (resultSet.next()) {
                obj.put("idclassegympass", resultSet.getString("idclassegympass"));
                obj.put("urlturmavirtual", resultSet.getString("urlturmavirtual"));
                obj.put("empresa", resultSet.getInt("empresa"));
                obj.put("horainicial", resultSet.getString("horainicial"));
                try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                        "SELECT COUNT(codigo) as ocupacao FROM alunohorarioturma WHERE horarioturma = " + idhorario
                                + " AND dia = '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "'", con)) {
                    int nrVagasPreenchidas = rs.next() ? rs.getInt("ocupacao") : 0;
                    obj.put("booked", nrVagasPreenchidas);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return obj;
    }

    public Boolean aulaColetiva(Integer codigoHorarioTurmas) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select t.aulacoletiva from horarioturma h \n" +
                "inner join turma t on t.codigo = h.turma\n" +
                "where h.codigo = " + codigoHorarioTurmas, con);
        return rs.next() ? rs.getBoolean("aulacoletiva") : false;
    }

    public Integer inserirAulaExperimentalAgenda(Integer aula, ClienteVO clienteVO, PassivoVO passivo, IndicadoVO indicado, Date dia, UsuarioVO usuario) throws Exception {
        Boolean aulaColetiva = aulaColetiva(aula);
        try {
            if (aulaColetiva) {
                ParamAlunoAulaCheiaTO param = new ParamAlunoAulaCheiaTO();
                if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    param.setCodigoCliente(clienteVO.getCodigo());
                } else if (passivo != null && !UteisValidacao.emptyNumber(passivo.getCodigo())) {
                    param.setCodigoPassivo(passivo.getCodigo());
                } else {
                    param.setCodigoIndicado(indicado.getCodigo());
                }
                param.setCodigoHorarioTurma(aula);
                param.setAulaExperimental(true);
                param.setData(dia);
                param.setCodigoUsuario(usuario.getCodigo());
                param.setOrigemSistema(OrigemSistemaEnum.CRM_META_DIARIA.getCodigo());
                inserirAlunoAulaCheiaBooking(param, "");
                return consultarUltimoAlunoHorarioTurmaInserido();
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public JSONArray desmarcacoes(Long inicio, Long fim, Integer cliente) throws Exception {
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select a.*, h.horainicial, t.descricao as turma, u.nome as usuarioresp from alunohorarioturmadesmarcado a " +
                " inner join horarioturma h on h.codigo = a.horarioturma " +
                " inner join turma t on t.codigo = h.turma " +
                " left join usuario u on u.codigo = a.usuario " +
                " where a.lancamento between '" + Calendario.getDataAplicandoFormatacao(new Date(inicio), "yyyy-MM-dd") +
                "' and '" + Calendario.getDataAplicandoFormatacao(new Date(fim), "yyyy-MM-dd") +
                " 23:59:59' and a.cliente = " + cliente, con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("dia", rs.getDate("dia").getTime());
                json.put("horario", rs.getString("horainicial"));
                json.put("turma", rs.getString("turma"));
                json.put("usuario", rs.getString("usuarioresp"));
                array.put(json);
            }
        }
        return array;
    }

    public Boolean verificarSeAulasAlunoECrossfit(JSONObject filtros) throws Exception {
        String matricula1 = filtros.getString("matricula");
        String dia = filtros.getString("dia");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ht.codigo FROM alunohorarioturma aht \n");
        sql.append("INNER JOIN cliente c ON c.codigo = aht.cliente \n");
        sql.append("INNER JOIN horarioturma ht ON aht.horarioturma = ht.codigo \n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append("INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        sql.append("WHERE c.codigomatricula = ").append(matricula1).append(" \n");
        sql.append("AND dia = '").append(dia).append("' \n");
        sql.append("AND m.crossfit IS TRUE");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return true;
            }
        }
        return false;
    }

    public JSONArray consultarAgendamentosBooking(Long inicio, Long fim, Integer empresa, Integer codCliente) throws Exception {
        JSONArray array = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ht.identificadorturma, aht.datalancamento, aht.origemsistema \n");
        sql.append(" FROM alunohorarioturma aht \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma \n");
        sql.append(" WHERE aht.cliente = " + codCliente + " \n");
        sql.append(" AND aht.bookingid NOTNULL \n");
        sql.append(" AND aht.datalancamento BETWEEN '" + Calendario.getDataComHoraZerada(new Date(inicio)) + "'");
        sql.append(" AND '" + Calendario.getDataComHora(new Date(fim), "23:59:59") + "' \n");
        sql.append(" ORDER BY aht.datalancamento DESC \n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("identificadorturma", rs.getString("identificadorturma"));
                json.put("origemsistema", OrigemSistemaEnum.getOrigemSistema(rs.getInt("origemsistema")).getDescricao());
                json.put("data", rs.getTimestamp("datalancamento").getTime());
                array.put(json);
            }
        }
        return array;
    }

    public String confirmarTodasPresencasTurma(String ctx, Integer horarioTurmaId, Date dia, Integer empresa,
                                               Integer codColaborador, OrigemSistemaEnum origem) throws Exception {
        try {
            if (Calendario.maior(dia, Calendario.hoje())) {
                return "ERRO: Não é permitido marcar presença para datas futuras";
            }
            List<String> alunosReposicao = new ArrayList<>();
            Boolean algumaConfirmacao = false;
            List<AgendadoJSON> agendadosReposicao = consultarReposicoesParaAgenda(
                    Calendario.getDataComHoraZerada(dia),
                    Calendario.getDataComHora(dia, "23:59:59"),
                    empresa);
            for (AgendadoJSON agendado : agendadosReposicao) {
                if (agendado.getId_agendamento().equals(horarioTurmaId.toString())) {
                    alunosReposicao.add(agendado.getMatricula());
                    if (!agendado.getConfirmado()) {
                        marcarPresenca(ctx, dia, horarioTurmaId, agendado.getCodigoCliente(), true, false, codColaborador, origem);
                        algumaConfirmacao = true;
                    }
                }
            }
            List<AgendadoJSON> agendados = consultarAlunosPorHorarioTurma(empresa, null, null, false, horarioTurmaId, dia);
            for (AgendadoJSON agendado : agendados) {
                if (!agendado.getConfirmado() && !alunosReposicao.contains(agendado.getMatricula())) {
                    try {
                        marcarPresenca(ctx, dia, horarioTurmaId, agendado.getCodigoCliente(), false, false, codColaborador, origem);
                        algumaConfirmacao = true;
                    } catch (Exception ex) {
                        //ignore
                    }
                }
            }
            return algumaConfirmacao ? "sucesso" : "Todos alunos já estão confirmados.";
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    public String confirmarTodasPresencasAulaCheia(String ctx, Integer horarioTurmaId, Date dia, Integer empresa,
                                                   Integer usuario, OrigemSistemaEnum origem, Boolean autorizadoGestaoRede,
                                                   String codAcessoAutorizado, Integer matriculaAutorizado) throws Exception {
        List<Integer> codAlunos = consultarAlunosNaoConfirmadosAulaCheia(horarioTurmaId, dia);
        Boolean algumaConfirmacao = false;
        for (Integer codAluno : codAlunos) {
            inserirConfirmacaoAulaCheiaAluno(ctx, codAluno, horarioTurmaId, usuario, dia, origem);
            algumaConfirmacao = true;
        }
        List<Integer> codAutorizados = consultarAutorizadosNaoConfirmadosAulaCheia(horarioTurmaId, dia);
        for (Integer codAutorizado : codAutorizados) {
            confirmacaoAulaCheiaAutorizado(ctx, codAutorizado, horarioTurmaId, usuario, dia, false, origem, autorizadoGestaoRede,
                    codAcessoAutorizado, matriculaAutorizado);
            algumaConfirmacao = true;
        }

        HashMap<String, Integer> alunosAutorizadosGestaoRede = consultarAutorizadosGestaoRedeNaoConfirmadosAulaCheia(horarioTurmaId, dia);

        for (Map.Entry<String,Integer> pair : alunosAutorizadosGestaoRede.entrySet()) {
            confirmacaoAulaCheiaAutorizado(ctx, 0, horarioTurmaId, usuario, dia, false, origem, true,
                    pair.getKey(), pair.getValue());
            algumaConfirmacao = true;
        }
        return algumaConfirmacao ? "sucesso" : "Todos alunos já estão confirmados.";
    }

    private Integer consultarClienteAulaCheia(Integer codAluno) throws Exception {
        Integer codigoAluno = 0;
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo FROM cliente c \n");
        sql.append(" WHERE c.codigomatricula = ").append(codAluno).append(" \n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                codigoAluno = rs.getInt("codigo");
            }
        }
        return codigoAluno;
    }

    private List<Integer> consultarAlunosNaoConfirmadosAulaCheia(Integer horarioTurmaId, Date dia) throws Exception {
        List<Integer> codAlunos = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT aht.cliente FROM alunohorarioturma aht \n");
        sql.append(" WHERE aht.cliente is not null and aht.horarioturma = ").append(horarioTurmaId).append(" \n");
        sql.append(" AND aht.dia = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
        sql.append(" AND aht.cliente NOT IN ( \n");
        sql.append("    SELECT ac.cliente FROM aulaconfirmada ac \n");
        sql.append("    WHERE ac.cliente is not null and ac.horario = ").append(horarioTurmaId).append(" \n");
        sql.append("    AND ac.diaaula = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
        sql.append(" ) ");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                codAlunos.add(rs.getInt("cliente"));
            }
        }
        return codAlunos;
    }

    @Override
    public String inserirAutorizadoAulaCheia(final ParamAlunoAulaCheiaTO param) {
        try {
            AlunoHorarioTurmaVO alunoHorario = new AlunoHorarioTurmaVO();
            alunoHorario.getHorarioTurma().setCodigo(param.getCodigoHorarioTurma());
            alunoHorario.setExperimental(param.isAulaExperimental());
            alunoHorario.setData(param.getData());
            alunoHorario.setUsuario(param.getCodigoUsuario());
            if (UteisValidacao.emptyNumber(alunoHorario.getUsuario())) {
                try {
                    UsuarioVO usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                    alunoHorario.setUsuario(usuarioVO.getCodigo());
                } catch (Exception e) {
                    Uteis.logar(e, TurmasServiceImpl.class);
                }
            }
            alunoHorario.setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(param.getOrigemSistema()));
            AgendaTotalJSON json = consultarUmaTurma(param.getCodigoHorarioTurma(), param.getData());
            ResultSet rsAutorizado = SuperFacadeJDBC.criarConsulta("select i.chave, a.codigomatricula, a.codAcesso, " +
                            "a.nomepessoa, a.codigopessoa, i.urlzillyonweb from autorizacaoacessogrupoempresarial a \n" +
                            "inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial\n" +
                            "where a.codigo = " + param.getAutorizado(),
                    con);
            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = new AutorizacaoAcessoGrupoEmpresarialVO();
            if (rsAutorizado.next()) {
                autorizacaoAcessoGrupoEmpresarialVO.setCodigoMatricula(rsAutorizado.getInt("codigomatricula"));
                autorizacaoAcessoGrupoEmpresarialVO.setNomePessoa(rsAutorizado.getString("nomepessoa"));
                autorizacaoAcessoGrupoEmpresarialVO.setCodigoPessoa(rsAutorizado.getInt("codigopessoa"));
                autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().setChave(rsAutorizado.getString("chave"));
                autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().setUrlZillyonWeb(rsAutorizado.getString("urlzillyonweb"));
                autorizacaoAcessoGrupoEmpresarialVO.setCodAcesso(rsAutorizado.getString("codAcesso"));
                alunoHorario.setAutorizadoGestaoRede(false);
                alunoHorario.setAutorizado(param.getAutorizado());
            } else {
                RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(param.getKey());
                if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                    autorizacaoAcessoGrupoEmpresarialVO = AcessoSistemaMSService.findById(param.getAutorizado(), redeEmpresa);
                    if (UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                        throw new Exception("Cadastro do autorizado não encontrado ");
                    }
                    alunoHorario.setCodAcessoAutorizado(autorizacaoAcessoGrupoEmpresarialVO.getCodAcesso());
                    alunoHorario.setMatriculaAutorizado(autorizacaoAcessoGrupoEmpresarialVO.getCodigoMatricula());
                    alunoHorario.setAutorizadoGestaoRede(true);
                } else {
                    throw new Exception("Cadastro do autorizado não encontrado ");
                }
            }

            json.setMatricula(autorizacaoAcessoGrupoEmpresarialVO.getCodigoMatricula());
            json.setNomeAluno(autorizacaoAcessoGrupoEmpresarialVO.getNomePessoa());
            Map<String, String> params = new HashMap<>();
            params.put("pessoa", String.valueOf(autorizacaoAcessoGrupoEmpresarialVO.getCodigoPessoa()));
            params.put("chave", autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().getChave());
            if (param.isValidarModalidade()) {
                params.put("modalidade", getHorarioDao().modalidadeHorario(param.getCodigoHorarioTurma()));
                params.put("tipo", getHorarioDao().tipoModalidadeHorario(param.getCodigoHorarioTurma()));
            }
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().getUrlZillyonWeb() + "/prest/aulacheia/validar-modalidade",
                    null, params, null, MetodoHttpEnum.GET);
            if (respostaHttpDTO.getHttpStatus() != 200) {
                throw new Exception("Problema ao validar o autorizado, verifique se existe um erro no cadastro da integração.");
            }
            JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
            if (!jsonObject.getBoolean("content")) {
                throw new Exception(param.isValidarModalidade() ? "Aluno não possui a modalidade em um contrato ativo." : "O aluno não possui um contrato ativo na unidade de origem");
            }

            validarToleranciaAula(param, json);

            validarToleranciaEmpresa(param, json);

            FilaDeEsperaTO filaDeEsperaParam = new FilaDeEsperaTO();
            filaDeEsperaParam.setCodigoAluno(param.getCodigoCliente());
            filaDeEsperaParam.setCodigoHorarioTurma(param.getCodigoHorarioTurma());
            filaDeEsperaParam.setDataEntrada(Calendario.hoje());
            filaDeEsperaParam.setDia(Uteis.getDataAplicandoFormatacao(alunoHorario.getData(), "dd/MM/yyyy HH:mm:ss"));
            validarAulaEstaCheia(json, false, filaDeEsperaParam);

            getHorarioDao().incluirAlunoAulaCheia(alunoHorario, json, param.getKey());
            String[] inicio = json.getInicio().split(" ");
            try {
                chamarNotificacaoPushEntrouNaAula(
                        param.getKey(),
                        alunoHorario.getCliente(),
                        "Tá na agenda " + String.valueOf(Character.toChars(9989)),
                        firstLetterUpper(json.getTitulo().toLowerCase()) + " agendada em " + inicio[0] + " às " + inicio[1] + ". Aproveite!"
                );
            } catch (Exception e) {
                Uteis.logar(e, TurmasServiceImpl.class);
            }

            json.setNrVagasPreenchidas(json.getNrVagasPreenchidas() + 1);
            logAluno(param.getCodigoHorarioTurma() + "_" + Calendario.getDataAplicandoFormatacao(param.getData(), "dd/MM/yyyy"),
                    param.getCodigoUsuario(),
                    null,
                    json,
                    param.getOrigemSistema(),
                    false,
                    true,
                    false, true, alunoHorario.getCliente());

            enviarInfoOutraUnidade(autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().getChave(), autorizacaoAcessoGrupoEmpresarialVO.getCodigoPessoa(), false,
                    json.getEmpresa(), json.getTitulo(), json.getInicio(), json.getFim(), autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().getUrlZillyonWeb());


            return json.toJSON();
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    private void validarToleranciaEmpresa(ParamAlunoAulaCheiaTO param, AgendaTotalJSON json) throws Exception {
        Date agoraTimeZone = dataComTimezone(param.getEmpresa());

        //Valida se é possível e quanto pode agendar com antecedência
        Long diferencaEmMinutos = Uteis.minutosEntreDatas(agoraTimeZone, Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"));
        if (diferencaEmMinutos > param.getMinutosAgendarAntecedencia() && param.getMinutosAgendarAntecedencia() != 0) {
            throw new Exception("Você só pode marcar a aula com "
                    + (getMensagemAntecedencia(param.getMinutosAgendarAntecedencia()))
                    + " de antecedência ou menos");
        }
    }

    @Override
    public void confirmacaoAulaCheiaAutorizado(final String key, int autorizado, int horarioTurma, int codColaborador, Date dia,
                                               Boolean desmarcar, OrigemSistemaEnum origem, Boolean autorizadoGestaoRede,
                                               String codAcessoAutorizado, Integer matriculaAutorizado) throws Exception {

        String chavePrimaria = "";
        Boolean conteudoLogOk = false;
        UsuarioVO usuarioVO = new UsuarioVO();
        try {
            try {
                if (origem != null && origem.equals(OrigemSistemaEnum.APP_TREINO) && codColaborador == 0) {
                    usuarioVO = getUsuarioDao().getUsuarioRecorrencia();
                } else {
                    usuarioVO = getUsuarioDao().consultarPorColaborador(codColaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }
            chavePrimaria = horarioTurma + "_" + Calendario.getData(dia, "dd/MM/yyyy");
            conteudoLogOk = true;
        } catch (Exception ex) {
            Uteis.logar(ex, TurmasServiceImpl.class);
            conteudoLogOk = false;
        }

        if (desmarcar) {
            if (autorizadoGestaoRede){
                SuperFacadeJDBC.executarConsulta("delete from aulaconfirmada "
                        + " where codAcessoAutorizado = '" + codAcessoAutorizado + "' "
                        + " and horario = " + horarioTurma + " and diaaula =  '"
                        + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dia)) + "' "
                        + " AND matriculaAutorizado = " + matriculaAutorizado, con);
            } else {
                SuperFacadeJDBC.executarConsulta("delete from aulaconfirmada where autorizado = " + autorizado
                        + " and horario = " + horarioTurma + " and diaaula =  '"
                        + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(dia)) + "' ", con);
            }

            try {
                if (conteudoLogOk) {
                    StringBuilder descricao = new StringBuilder();
                    descricao.append("<br/>[matricula: ").append(autorizado).append("]<br/>");
                    descricao.append("[aluno autorizado: ").append(autorizado).append("]<br/>");
                    String descricaoOrigem = origem != null ? origem.getDescricao() : "Não informada";
                    descricao.append("[origem: ").append(descricaoOrigem).append("]");

                    incluirLog(chavePrimaria, "PRESENCA", "Presença Desconfirmada", usuarioVO, "ALTERAÇÃO",
                            "Presença Desconfirmada", descricao.toString(), "", null, autorizado);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }
        } else {
            AulaConfirmadaVO aulaConfirmadaVO = new AulaConfirmadaVO();
            aulaConfirmadaVO.setDia(dia);
            aulaConfirmadaVO.setColaboradorVO(new ColaboradorVO());
            if (codColaborador > 0) {
                aulaConfirmadaVO.getColaboradorVO().setCodigo(codColaborador);
            }
            if (autorizadoGestaoRede){
                aulaConfirmadaVO.setAutorizadoGestaoRede(autorizadoGestaoRede);
                aulaConfirmadaVO.setCodAcessoAutorizado(codAcessoAutorizado);
                aulaConfirmadaVO.setMatriculaAutorizado(matriculaAutorizado);
            } else {
                aulaConfirmadaVO.setAutorizado(autorizado);
            }
            aulaConfirmadaVO.setHorarioTurmaVO(new HorarioTurmaVO());
            aulaConfirmadaVO.getHorarioTurmaVO().setCodigo(horarioTurma);
            aulaConfirmadaVO.setDataLancamento(Calendario.hoje());
            aulaConfirmadaVO.setOrigemSistemaEnum(OrigemSistemaEnum.ZW);
            getAulaConfirmadaDao().incluir(aulaConfirmadaVO);

            try {
                if (conteudoLogOk) {
                    StringBuilder descricao = new StringBuilder();
                    descricao.append("<br/>[matricula: ").append(aulaConfirmadaVO.getClienteVO().getCodigoMatricula()).append("]<br/>");
                    String nome = aulaConfirmadaVO.getClienteVO() != null && aulaConfirmadaVO.getClienteVO().getPessoa() != null
                            ? aulaConfirmadaVO.getClienteVO().getPessoa().getNome()
                            : ("" + autorizado);
                    descricao.append("[aluno autorizado: ").append(nome).append("]<br/>");
                    String descricaoOrigem = origem != null ? origem.getDescricao() : "Não informada";
                    descricao.append("[origem: ").append(descricaoOrigem).append("]");

                    incluirLog(chavePrimaria, "PRESENCA", "Presença Confirmada", usuarioVO, "ALTERAÇÃO", "Presença Confirmada",
                            descricao.toString(), "", null, autorizado);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, TurmasServiceImpl.class);
            }
        }

    }

    private List<Integer> consultarAutorizadosNaoConfirmadosAulaCheia(Integer horarioTurmaId, Date dia) throws Exception {
        List<Integer> codAlunos = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT aht.autorizado FROM alunohorarioturma aht \n");
        sql.append(" WHERE aht.autorizado is not null and aht.horarioturma = ").append(horarioTurmaId).append(" \n");
        sql.append(" AND aht.dia = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
        sql.append(" AND aht.autorizado NOT IN ( \n");
        sql.append("    SELECT ac.autorizado FROM aulaconfirmada ac \n");
        sql.append("    WHERE ac.autorizado is not null and ac.horario = ").append(horarioTurmaId).append(" \n");
        sql.append("    AND ac.diaaula = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
        sql.append(" ) ");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                codAlunos.add(rs.getInt("autorizado"));
            }
        }
        return codAlunos;
    }

    private HashMap<String, Integer> consultarAutorizadosGestaoRedeNaoConfirmadosAulaCheia(Integer horarioTurmaId, Date dia) throws Exception {
        HashMap<String, Integer> codAlunos = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT aht.codacessoautorizado, aht.matriculaautorizado FROM alunohorarioturma aht \n");
        sql.append(" WHERE aht.autorizadoGestaoRede and aht.horarioturma = ").append(horarioTurmaId).append(" \n");
        sql.append(" AND aht.dia = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
        sql.append(" AND aht.autorizadogestaorede\n");
        sql.append(" AND aht.codacessoautorizado NOT IN ( \n");
        sql.append("    SELECT ac.codacessoautorizado FROM aulaconfirmada ac \n");
        sql.append("    WHERE ac.codacessoautorizado is not null and ac.horario = ").append(horarioTurmaId).append(" \n");
        sql.append("    AND ac.autorizadogestaorede\n");
        sql.append("    AND ac.diaaula = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
        sql.append(" ) ");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                codAlunos.put(rs.getString("codacessoautorizado"), rs.getInt("matriculaautorizado"));
            }
        }
        return codAlunos;
    }

    @Override
    public String excluirAutorizadoAulaCheia(Integer autorizado, Integer horarioturma, Date data, Integer usuario, Boolean autorizadoGestaoRede, String codAcessoAutorizado, Integer matriculaAutorizado) {
        try {
            if (autorizadoGestaoRede != null && autorizadoGestaoRede){
                SuperFacadeJDBC.executarConsulta("delete from alunohorarioturma "
                        + " where codacessoautorizado = '" + codAcessoAutorizado + "'"
                        + " and horarioturma = " + horarioturma + " and dia =  '"
                        + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(data)) + "' "
                        + " AND matriculaautorizado = " + matriculaAutorizado, con);
            } else {
                SuperFacadeJDBC.executarConsulta("delete from alunohorarioturma where autorizado = " + autorizado
                        + " and horarioturma = " + horarioturma + " and dia =  '"
                        + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(data)) + "' ", con);
            }
            ResultSet rsAutorizado = SuperFacadeJDBC.criarConsulta("select i.chave, a.codigomatricula," +
                            "a.nomepessoa, a.codigopessoa, i.urlzillyonweb from autorizacaoacessogrupoempresarial a \n" +
                            "inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial\n" +
                            "where a.codigo = " + autorizado,
                    con);
            if (rsAutorizado.next()) {
                AgendaTotalJSON json = consultarUmaTurma(horarioturma, data);
                json.setMatricula(rsAutorizado.getInt("codigomatricula"));
                json.setNomeAluno(rsAutorizado.getString("nomepessoa"));
                logAluno(horarioturma + "_" + Calendario.getDataAplicandoFormatacao(data, "dd/MM/yyyy"),
                        usuario,
                        null,
                        json,
                        null, true, true, false, true, null);
                enviarInfoOutraUnidade(rsAutorizado.getString("chave"), rsAutorizado.getInt("codigopessoa"), true,
                        json.getEmpresa(), json.getTitulo(), json.getInicio(), json.getFim(), rsAutorizado.getString("urlzillyonweb"));
            }

            return "OK";
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    public boolean validarModalidadeOutraUnidade(Integer pessoa, String modalidade,
                                                 String tipoModalidade) throws Exception {

        String sqlConsulta = "select con.codigo "
                + " from contrato con "
                + " inner join plano p on p.codigo = con.plano "
                + " inner join contratomodalidade cm on cm.contrato = con.codigo "
                + " inner join modalidade m on cm.modalidade = m.codigo"
                + " left join tipomodalidade tm on tm.codigo = m.tipo";
        sqlConsulta += " where con.pessoa = " + pessoa;
        if (!UteisValidacao.emptyString(modalidade)) {
            sqlConsulta += " and ( upper(trim(m.nome)) like trim('" + modalidade.toUpperCase() + "')";
            sqlConsulta += UteisValidacao.emptyString(tipoModalidade) ? ")" : (" or upper(t.nome) like '" + tipoModalidade.toUpperCase() + "')");
        }
        sqlConsulta += " and con.vigenciaateajustada >=  '" + Uteis.getDataFormatoBD(Calendario.hoje()) + "' order by con.vigenciade limit 1 ";

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlConsulta, con)) {
            return rs.next();
        }
    }

    private void enviarInfoOutraUnidade(String chave, Integer pessoa, Boolean desmarcacao,
                                        Integer unidade, String aula, String hora, String fim, String url) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("pessoa", String.valueOf(pessoa));
            params.put("desmarcacao", desmarcacao.toString());
            params.put("aula", aula);
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select nome from empresa where codigo = " + unidade, con);
            if (rs.next()) {
                params.put("unidade", rs.getString("nome"));
            }
            params.put("hora", hora);
            params.put("fimAula", fim);
            params.put("chave", chave);
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url + "/prest/aulacheia/gravar-log-outra-unidade",
                    null, params, null, MetodoHttpEnum.GET);
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
    }

    public JSONArray logAlunoAulaOutraUnidade(Long inicio, Long fim, Integer pessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select dataalteracao, operacao, valorcampoalterado from log l ");
        sql.append(" where nomeentidade = 'ALUNO_AULA_OUTRA_UNIDADE' and pessoa = ").append(pessoa);
        sql.append(" AND dataalteracao BETWEEN '" + Calendario.getDataAplicandoFormatacao(new Date(inicio), "yyyy-MM-dd") + "'");
        sql.append(" AND '" + Calendario.getDataAplicandoFormatacao(new Date(fim), "yyyy-MM-dd") + " 23:59:59' \n");
        ResultSet rsLogs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        JSONArray array = new JSONArray();
        while (rsLogs.next()) {
            JSONObject json = new JSONObject();
            json.put("data", rsLogs.getTimestamp("dataalteracao").getTime());
            json.put("tipo", rsLogs.getString("operacao"));
            json.put("descricao", rsLogs.getString("valorcampoalterado"));
            array.put(json);
        }
        return array;
    }

    public JSONArray ambientesAgendados(Long inicio, Long fim, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select dia, t.ambiente, h.horainicial, h.horafinal, a.horarioturma from alunohorarioturma a \n");
        sql.append(" inner join horarioturma h ON h.codigo = a.horarioturma \n");
        sql.append(" inner join turma t on t.codigo = h.turma \n");
        sql.append(" WHERE dia BETWEEN '" + Calendario.getDataAplicandoFormatacao(new Date(inicio), "yyyy-MM-dd") + "'");
        sql.append(" AND '" + Calendario.getDataAplicandoFormatacao(new Date(fim), "yyyy-MM-dd") + " 23:59:59' \n");
        sql.append(" and t.empresa = ").append(empresa);
        sql.append(" group by dia, t.ambiente, h.horainicial, h.horafinal, a.horarioturma \n");
        ResultSet rsLogs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        JSONArray array = new JSONArray();
        while (rsLogs.next()) {
            JSONObject json = new JSONObject();
            json.put("dia", rsLogs.getTimestamp("dia").getTime());
            json.put("ambiente", rsLogs.getInt("ambiente"));
            json.put("horarioturma", rsLogs.getInt("horarioturma"));
            json.put("horainicial", rsLogs.getString("horainicial"));
            json.put("horafinal", rsLogs.getString("horafinal"));
            array.put(json);
        }

        sql.delete(0, sql.length());
        sql.append(" select h.ambiente, h.horainicial, h.horafinal, a.horarioturma from matriculaalunohorarioturma a \n");
        sql.append(" inner join horarioturma h ON h.codigo = a.horarioturma \n");
        sql.append(" inner join turma t on t.codigo = h.turma \n");
        sql.append(" INNER JOIN cliente c ON c.pessoa = a.pessoa  \n");
        sql.append(" LEFT JOIN auladesmarcada ad ON ad.cliente = c.codigo AND ad.contrato = a.contrato " +
                "AND ad.dataorigem between '" + Calendario.getDataAplicandoFormatacao(new Date(inicio), "yyyy-MM-dd HH:mm:ss") +
                "' AND '" + Calendario.getDataAplicandoFormatacao(new Date(fim), "yyyy-MM-dd HH:mm:ss") + "'  \n");
        sql.append(" WHERE datainicio <= '" + Calendario.getDataAplicandoFormatacao(new Date(inicio), "yyyy-MM-dd") + "'");
        sql.append(" AND datafim >= '" + Calendario.getDataAplicandoFormatacao(new Date(fim), "yyyy-MM-dd") + "' \n");
        sql.append(" and t.empresa = ").append(empresa);
        sql.append(" and ad.codigo is null \n");
        if (Calendario.igual(new Date(inicio), new Date(fim))) {
            sql.append(" AND h.diasemana = '").append(Calendario.getDiaDaSemanaAbreviado(new Date(inicio))).append("' \n");
        }
        sql.append(" group by h.ambiente, h.horainicial, h.horafinal, a.horarioturma \n");
        ResultSet rsLogs2 = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rsLogs2.next()) {
            if (!UteisValidacao.emptyNumber(rsLogs2.getInt("ambiente"))) {
                JSONObject json = new JSONObject();
                json.put("dia", inicio);
                json.put("ambiente", rsLogs2.getInt("ambiente"));
                json.put("horarioturma", rsLogs2.getInt("horarioturma"));
                json.put("horainicial", rsLogs2.getString("horainicial"));
                json.put("horafinal", rsLogs2.getString("horafinal"));
                array.put(json);
            }
        }

        sql.delete(0, sql.length());
        sql.append(" SELECT ht.ambiente, ht.horainicial, ht.horafinal, ht.codigo as horarioturma \n");
        sql.append(" FROM reposicao r INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sql.append(" INNER JOIN turma t ON t.codigo = r.turmadestino \n");
        sql.append(" INNER JOIN modalidade m on m.codigo = t.modalidade \n");
        sql.append(" INNER JOIN cliente ON cliente.codigo = r.cliente \n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocliente = r.cliente \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sc.codigopessoa \n");
        sql.append(" LEFT JOIN controlecreditotreino cc ON cc.reposicao = r.codigo \n");
        sql.append(" LEFT JOIN agenda  ag ON ag.reposicao = r.codigo \n");
        sql.append(" WHERE r.datareposicao >= '" + Calendario.getDataAplicandoFormatacao(new Date(inicio), "yyyy-MM-dd") + "'");
        sql.append(" AND r.datareposicao <= '" + Calendario.getDataAplicandoFormatacao(new Date(fim), "yyyy-MM-dd") + "' \n");
        sql.append(" AND ht.situacao = 'AT' ");
        sql.append(" AND t.empresa = ").append(empresa);
        sql.append(" group by ht.ambiente, ht.horainicial, ht.horafinal, ht.codigo \n");
        ResultSet rsLogs3 = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rsLogs3.next()) {
            if (!UteisValidacao.emptyNumber(rsLogs3.getInt("ambiente"))) {
                JSONObject json = new JSONObject();
                json.put("dia", inicio);
                json.put("ambiente", rsLogs3.getInt("ambiente"));
                json.put("horarioturma", rsLogs3.getInt("horarioturma"));
                json.put("horainicial", rsLogs3.getString("horainicial"));
                json.put("horafinal", rsLogs3.getString("horafinal"));
                array.put(json);
            }
        }
        return array;
    }

    public void logAlunoAulaOutraUnidade(Boolean desmarcacao, Integer pessoa,
                                         String unidade, String aula, String inicio, String fim) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(pessoa.toString());
        obj.setNomeEntidade("ALUNO_AULA_OUTRA_UNIDADE");
        obj.setValorCampoAlterado((desmarcacao ? "Desmarcou" : "Agendou") + " aula na unidade " + unidade + ", aula " + aula + " com início às " + inicio);
        obj.setOperacao(desmarcacao ? "EXCLUSÃO" : "INCLUSÃO");
        obj.setResponsavelAlteracao("integracao");
        obj.setNomeCampo("aula");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setPessoa(pessoa);
        try {
            this.logDAO.incluirSemCommit(obj);
            if (desmarcacao) {
                SuperFacadeJDBC.executarConsulta("delete from aulaoutraunidade where pessoa = " + pessoa +
                        " and inicio = '" + Uteis.getDataJDBCTimestamp(Uteis.getDate(inicio, "dd/MM/yyyy HH:mm")) +
                        "' and fim = '" + Uteis.getDataJDBCTimestamp(Uteis.getDate(fim, "dd/MM/yyyy HH:mm")) + "'", con);
            } else {
                SuperFacadeJDBC.executarConsulta("insert into aulaoutraunidade (pessoa, inicio, fim) values (" + pessoa +
                        ", '" + Uteis.getDataJDBCTimestamp(Uteis.getDate(inicio, "dd/MM/yyyy HH:mm")) +
                        "', '" + Uteis.getDataJDBCTimestamp(Uteis.getDate(fim, "dd/MM/yyyy HH:mm")) + "')", con);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean checarAlunoEmAula(String matricula, Integer codigoHorarioTurma, Date dia) throws Exception {
        String sqlTurmaColetiva = "\n" +
                "select t.aulacoletiva from turma t\n" +
                "inner join horarioturma h on t.codigo = h.turma \n" +
                "where h.codigo = " + codigoHorarioTurma;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTurmaColetiva, con)) {
            if (rs.next()) {
                boolean turmaColetiva = rs.getBoolean("aulacoletiva");
                if (turmaColetiva) {
                    return checarAlunoEmAulaColetiva(matricula, codigoHorarioTurma, dia);
                } else {
                    return checarAlunoEmTurma(matricula, codigoHorarioTurma, dia);
                }
            }
        }
        return false;
    }

    public boolean checarAlunoGestaoRedeEmAula(Integer matriculaAutorizado, String codAcessoAutorizado, Integer codigoHorarioTurma, Date dia) throws Exception {
        String sqlTurmaColetiva = "\n" +
                "select t.aulacoletiva from turma t\n" +
                "inner join horarioturma h on t.codigo = h.turma \n" +
                "where h.codigo = " + codigoHorarioTurma;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTurmaColetiva, con)) {
            if (rs.next()) {
                boolean turmaColetiva = rs.getBoolean("aulacoletiva");
                if (turmaColetiva) {
                    return checarAlunoGestaoRedeEmAulaColetiva(matriculaAutorizado, codAcessoAutorizado, codigoHorarioTurma, dia);
                }
            }
        }
        return false;
    }

    private boolean checarAlunoGestaoRedeEmAulaColetiva(Integer matriculaAutorizado, String codAcessoAutorizado, Integer codigoHorarioTurma, Date dia) throws Exception {
        String sqlTurmaColetiva = "select a.codigo from alunohorarioturma a \n" +
                " where a.horarioturma = " + codigoHorarioTurma +
                " and a.dia = '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "'" +
                " and a.matriculaautorizado = " + matriculaAutorizado +
                " and a.codacessoautorizado = '" + codAcessoAutorizado + "'";

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTurmaColetiva, con)) {
            return rs.next();
        }
    }

    private boolean checarAlunoEmAulaColetiva(String matricula, Integer codigoHorarioTurma, Date dia) throws Exception {
        /*String sqlTurmaColetiva = "select a.codigo from alunohorarioturma a \n" +
                " inner join cliente cli on cli.codigo = a.cliente \n" +
                " where cli.codigomatricula = " + matricula +
                " and a.dia = '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") +
                "' and a.horarioturma = " + codigoHorarioTurma;*/

        String sqlTurmaColetiva = "select a.codigo from alunohorarioturma a \n" +
                " left join cliente cli on cli.codigo = a.cliente \n" +
                " left join autorizacaoacessogrupoempresarial aage ON aage.codigo = a.autorizado \n" +
                " where a.horarioturma = " + codigoHorarioTurma +
                " and a.dia = '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") +
                "' and (a.autorizado = (select aa.codigo from autorizacaoacessogrupoempresarial aa where aa.codigo = a.autorizado) \n" +
                " or cli.codigomatricula = " + matricula + " )";

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTurmaColetiva, con)) {
            return rs.next();
        }
    }

    private boolean checarAlunoEmReposicao(String matricula, Integer codigoHorarioTurma, Date dia) throws Exception {
        String sqlTurmaColetiva = "select r.codigo from reposicao r\n" +
                " inner join cliente cli on cli.codigo = r.cliente \n" +
                " where r.datareposicao = '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "' \n" +
                " and horarioturma =  " + codigoHorarioTurma +
                " and cli.codigomatricula = " + matricula;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTurmaColetiva, con)) {
            return rs.next();
        }
    }

    private boolean checarAlunoEmTurma(String matricula, Integer codigoHorarioTurma, Date dia) throws Exception {
        String sqlTurmaColetiva = "select m.codigo from matriculaalunohorarioturma m\n" +
                " inner join cliente cli on cli.pessoa = m.pessoa \n" +
                " where '" + Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "' between m.datainicio and m.datafim \n" +
                " and m.horarioturma = " + codigoHorarioTurma +
                " and cli.codigomatricula = " + matricula +
                " and not exists (select codigo from auladesmarcada a where a.dataorigem = '" +
                Calendario.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") +
                "' and a.cliente = cli.codigo and a.horarioturma = m.horarioturma)";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTurmaColetiva, con)) {
            if (rs.next()) {
                return true;
            } else {
                return checarAlunoEmReposicao(matricula, codigoHorarioTurma, dia);
            }
        }
    }

    /**
     * ESTA CONSULTA VERIFICA SE O ALUNO SE ENCONTRA EM OUTRAS FILAS NO MESMO HORÁRIO DA TURMA
     * QUE ELE ACABOU DE SER INSERIDO AUTOMATICAMENTE PELO SISTEMA POR ESTAR NA FILA DE ESPERA
     **/
    public boolean alunoEstaNaFilaDeEsperaNesteHorario(Integer codigoAluno, String dia, String horaInicio, String horaFim) throws Exception {
        Integer quantidade = 0;
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select count(f.*) from filaesperaturma f where f.cliente = " + codigoAluno + "\n"
                    + "and f.horarioturma in (select h.codigo \n"
                    + "from horarioturma h \n"
                    + "where h.diasemana ilike " + "'" + dia + "'" + " \n"
                    + "and h.horainicial >= " + "'" + horaInicio + "'" + " \n"
                    + "and h.horafinal <= " + "'" + horaFim + "'" + " \n"
                    + "and h.situacao ilike 'AT') \n", con);
            if (rs.next()) {
                quantidade = rs.getInt(1);
            }
        } catch (Exception e) {
            e.getMessage();
        }

        if (quantidade > 0) {
            return true;
        } else {
            return false;
        }

    }

    public String listarIdsHorarioTurma() throws Exception {
        String ids = "";
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT codigo FROM horarioturma");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                ids += "," + rs.getInt("codigo");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ids.substring(1);
    }

    public void chamarNotificacaoPushEntrouNaAula(final String key, Integer codigoCliente, String titulo, String mensagem) {
        if (PropsService.isTrue(PropsService.permitirEnvioNotificacaoPushAula)) {
            try {
                UsuarioMovel usuarioMovelDAO = new UsuarioMovel(getCon());
                UsuarioMovelVO usuarioMovelVO = usuarioMovelDAO.consultarPorCodigoCliente(
                        codigoCliente,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS
                );
                if (usuarioMovelVO != null && !UteisValidacao.emptyString(usuarioMovelVO.getNome())) {
                    PushMobileRunnable pushMobileRunnable = new PushMobileRunnable(getCon());
                    pushMobileRunnable.enviarNotificacaoPush(
                            key,
                            titulo,
                            mensagem,
                            usuarioMovelVO.getNome(),
                            "/manter/enviarPushPara"
                    );
                }
            } catch (Exception ex) {
                System.out.println("Ocorreu um problema ao enviar o push: " + ex.getMessage());
                Uteis.logar(ex.getMessage());
            }
        }
    }

    public JSONObject isAlunoContratoConcomitante(Integer empresa, Integer matricula, Integer modalidade, Integer horarioTurma, Date dia) throws Exception {
        JSONObject json = new JSONObject();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM cliente WHERE codigomatricula = " + matricula, con)) {
            if (rs.next()) {
                Integer cliente = rs.getInt("codigo");
                boolean contratoConcomitanteAlunoAtivo = contratoConcomitanteAlunoAtivo(cliente, dia);
                if (contratoConcomitanteAlunoAtivo) {
                    json.put("isContratoConcomitante", true);
                    json.put("dadosContrato", getSwDao().consultarDadosContratoClientePorModalidade(empresa, matricula, modalidade));
                    if (json.has("dadosContrato")) {
                        tratarDadosRetornoContrato(json.getJSONObject("dadosContrato"));
                    } else {
                        json.put("erro", "Não foram encontrados dados para consulta realizada");
                    }
                    return json;
                } else {
                    json.put("isContratoConcomitante", false);
                }
            } else {
                throw new Exception("Aluno não encontrado!");
            }
        }
        return json;
    }

    private void tratarDadosRetornoContrato(JSONObject json) {
        if (json != null) {
            try {
                Integer tipoHorario = json.getInt("tipohorario");
                json.put("usaTurma", TipoHorarioCreditoTreinoEnum.getTipo(tipoHorario).equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA));
            } catch (Exception e) {
                json.put("usaTurma", true);
            }
        }
    }


    private boolean limiteDeAulasPorDiaBookingGympassAtingido(Integer codCliente, Integer codEmpresa, Date diaAula) {
        try {
            // validado com a equipe da gympass que a regra é a seguinte:
            // "O aluno pode entrar em apenas uma aula por dia, mas ele pode no mesmo dia agendar UMA aula para hoje e UMA para amanhã"

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(aht.codigo) as qtdAulas FROM alunohorarioturma aht  \n");
            sql.append(" INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma \n");
            sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
            sql.append("WHERE aht.cliente = " + codCliente + " \n");
            sql.append("AND t.empresa = " + codEmpresa + " \n");
            sql.append("AND COALESCE(t.idclassegympass, 0) > 0 \n ");
            sql.append("AND aht.dia::date = '" + Uteis.getDataJDBC(diaAula) + "' \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);
            rs.next();
            Integer qtdAulas = rs.getInt("qtdAulas");

            return qtdAulas >= 1;
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
            return false;
        }
    }

    private boolean limiteDeAulasPorDiaTotalpassAtingido(Integer codCliente, Integer codEmpresa) {
        try {
            Integer limiteDeAulasPorDiaTotalpass = getTotalPassDao().obterLimiteDeAulasPorDiaTotalpass(codEmpresa);
            if (UteisValidacao.emptyNumber(limiteDeAulasPorDiaTotalpass)) {
                return false;
            }
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(aht.codigo) as qtdAulas FROM alunohorarioturma aht  \n");
            sql.append(" INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma \n");
            sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
            sql.append("WHERE aht.cliente = " + codCliente + " \n");
            sql.append("AND t.empresa = " + codEmpresa + " \n");
            sql.append("AND aht.dia::date = '" + Uteis.getDataJDBC(Calendario.hoje()) + "' \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);
            rs.next();
            Integer qtdAulas = rs.getInt("qtdAulas");

            return qtdAulas >= limiteDeAulasPorDiaTotalpass;
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
            return false;
        }
    }

    private void validarSeTemCreditoContratosConcomitantesPorModalidade(SituacaoClienteSinteticoDWVO st, Date diaAula, Integer codigoModalidade, Boolean validarSaldoCredito, Boolean contratoConcomitanteAlunoAtivo, Boolean isAlunoDependenteComPlanoCredito) throws Exception {
        if(contratoConcomitanteAlunoAtivo || isAlunoDependenteComPlanoCredito) {
            List<ContratoVO> listaContratos = new ArrayList<>();
            try (ResultSet rsContrato = SuperFacadeJDBC.criarConsulta(
                    "SELECT Contrato.codigo, Contrato.vendaCreditoTreino FROM Contrato, Pessoa WHERE Contrato.pessoa = Pessoa.codigo "
                            + "and Pessoa.codigo = " + st.getCodigoPessoa() + " and vigenciaDe <= '" + Uteis.getDataJDBC(diaAula)
                            + "' and vigenciaAteAjustada >= '" + Uteis.getDataJDBC(diaAula) + "'", getCon())) {
                while (rsContrato.next()) {
                    ContratoVO contrato = new ContratoVO();
                    contrato.setCodigo(rsContrato.getInt("codigo"));
                    contrato.setVendaCreditoTreino(rsContrato.getBoolean("vendaCreditoTreino"));
                    listaContratos.add(contrato);
                }
            }

            HashMap<Integer, List<Integer>> mapaContratoModalidade = new HashMap<>();
            for (ContratoVO cVO : listaContratos) {
                try (ResultSet rsCM = SuperFacadeJDBC.criarConsulta("select modalidade from contratomodalidade c where contrato = " + cVO.getCodigo(), getCon())) {
                    List<Integer> codModalidades = new ArrayList<>();
                    while (rsCM.next()) {
                        codModalidades.add(rsCM.getInt("modalidade"));
                    }
                    mapaContratoModalidade.put(cVO.getCodigo(), codModalidades);
                    if (cVO.isVendaCreditoTreino() &&
                            codModalidades.contains(codigoModalidade != null ? codigoModalidade : "") &&
                            !((!validarSaldoCredito) || (validarSaldoCredito && st.getSaldoCreditoTreino() > 0))) {
                        throw new Exception("Aluno não tem crédito para realizar o agendamento!");
                    }
                }
            }
        } else if (!((!validarSaldoCredito) || (validarSaldoCredito && st.getSaldoCreditoTreino() > 0))) {
            throw new Exception("Aluno não tem crédito para realizar o agendamento!");
        }
    }

    public JSONArray validarCreditos(boolean debitar) throws Exception{
        return getControleCreditoDao().validarCreditos(debitar);
    }

    public String marcarCancelarComparecimentoMetaAgendamentoPresencialCRM(Integer fecharMetaDetalhado, Integer codigoUsuario, boolean marcar) throws Exception {
        FecharMetaDetalhadoVO fecharMetaDetalhadoVO = getFecharMetaDetalhadoDao().consultarPorCodigo(fecharMetaDetalhado, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (marcar) {
            UsuarioVO us = new UsuarioVO();
            us.setCodigo(codigoUsuario);
            getAgendaDao().alterarAgendadosComparecido(fecharMetaDetalhadoVO.getAgenda(), us, true);
            getHistoricoContatoDao().inserirHistoricoConfirmarAgendamento(fecharMetaDetalhadoVO.getAgenda(), "AG", false);
        } else {
            getAgendaDao().cancelarAgendadosComparecido(fecharMetaDetalhadoVO.getAgenda());
            getHistoricoContatoDao().inserirHistoricoConfirmarAgendamento(fecharMetaDetalhadoVO.getAgenda(), "AG", true);
        }
        return "OK";
    }

    private int contarAulasPorModalidade(Integer codigoCliente, Integer codigoHorarioTurma, Date data, Integer modalidade) {
        try {
            String dataAtual = Uteis.getDataAplicandoFormatacao(new Date(), "yyyy-MM-dd");
            String horaAtual = Uteis.getDataAplicandoFormatacao(new Date(), "HH:mm:ss");
            String dataAula = Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd");

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(aht.codigo) ");
            sql.append("FROM alunohorarioturma aht ");
            sql.append("JOIN horarioturma ht ON aht.horarioturma = ht.codigo ");
            sql.append("JOIN turma t ON ht.turma = t.codigo ");
            sql.append("WHERE aht.cliente = ").append(codigoCliente).append(" ");
            sql.append("AND t.modalidade = ").append(modalidade).append(" ");
            sql.append("AND ( ");
            sql.append("  (aht.dia = '").append(dataAtual).append("' AND ht.horafinal > '").append(horaAtual).append("') ");
            sql.append("  OR ");
            sql.append("  (aht.dia > '").append(dataAtual).append("') ");
            sql.append(") ");
            sql.append("AND aht.aulaexperimental = false ");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return 0;
    }

    private Integer contarFaltasAluno(Integer codigoCliente, Integer codigoHorarioTurma, Integer qtdtempobloqueioaluno, Date dia) {
        Integer qtdFaltas = 0;
        try {

            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT al.cliente, h.codigo AS horarioturma_codigo, al.dia, a.codigo AS aulaconfirmada_codigo ")
                    .append(" FROM alunohorarioturma al ")
                    .append(" INNER JOIN horarioturma h ON h.codigo = al.horarioturma ")
                    .append(" INNER JOIN turma t ON t.codigo = h.turma ")
                    .append(" LEFT JOIN aulaconfirmada a ON a.horario = al.horarioturma ")
                    .append("     AND a.cliente = al.cliente ")
                    .append("     AND a.diaaula = al.dia ")
                    .append(" WHERE al.cliente = " + codigoCliente + " ")
                    .append(" AND al.dia BETWEEN (('" + Uteis.getDataFormatoBD(dia) + "'::date) - INTERVAL '" + qtdtempobloqueioaluno + " DAY') AND CURRENT_DATE ")
                    .append(" AND a.codigo IS NULL ");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            while (rs.next()) {
                if(rs.getString("aulaconfirmada_codigo")==null){
                    qtdFaltas++;
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, TurmasServiceImpl.class);
        }
        return qtdFaltas;
    }

    private void descontarCreditoContratoAulaMarcada(AlunoHorarioTurmaVO alunoHorarioTurmaVO) throws Exception {
        try {
            Cliente clienteDAO = new Cliente(con);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigo(alunoHorarioTurmaVO.getCliente(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteDAO = null;

            Contrato contratoDAO = new Contrato(this.con);
            List<ContratoVO> contratos = getContratoDao().consultarContratosVigentesPorPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDAO = null;

            if (UteisValidacao.emptyList(contratos)) {
                return;
            }

            ContratoVO contratoVO = contratos.stream().filter(c -> c.isVendaCreditoTreino()).findFirst().orElse(null);
            if (contratoVO == null) {
                return;
            }

            List<ContratoModalidadeVO> modalidadesContrato = getContratoModalidadeDao().consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_ROBO);
            TurmaVO turmaVO = getTurmaDao().consultarPorHorarioTurma(alunoHorarioTurmaVO.getHorarioTurma().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            for (ContratoModalidadeVO contratoModalidadeVO : modalidadesContrato) {
                if (contratoModalidadeVO.getModalidade().getCodigo().equals(turmaVO.getModalidade().getCodigo()) ||
                        (UteisValidacao.notEmptyNumber(turmaVO.getModalidade().getTipo()) && contratoModalidadeVO.getModalidade().getTipo().equals(turmaVO.getModalidade().getTipo()))) {
                    HorarioTurmaVO horarioTurmaVO = getHorarioDao().consultarPorChavePrimaria(alunoHorarioTurmaVO.getHorarioTurma().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    alunoHorarioTurmaVO.setHorarioTurma(horarioTurmaVO);
                    getControleCreditoDao().descontarCreditoAulaMarcada(clienteVO.getCodigo(), contratoVO.getCodigo(), alunoHorarioTurmaVO, turmaVO.getModalidade().getNome());
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, TurmasServiceImpl.class);
        }
    }

}
