package br.com.pactosolucoes.comuns.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

/**
 * ResourceBundle personalizado que força o uso de encoding UTF-8
 * para resolver problemas com caracteres especiais em arquivos de propriedades
 */
public class UTF8ResourceBundle extends ResourceBundle.Control {

    @Override
    public ResourceBundle newBundle(String baseName, Locale locale, String format,
                                  ClassLoader loader, boolean reload)
            throws IllegalAccessException, InstantiationException, IOException {
        
        // Só processa arquivos de propriedades
        if (!"java.properties".equals(format)) {
            return null;
        }

        String bundleName = toBundleName(baseName, locale);
        String resourceName = toResourceName(bundleName, "properties");
        
        ResourceBundle bundle = null;
        InputStream stream = null;
        
        if (reload) {
            URL url = loader.getResource(resourceName);
            if (url != null) {
                URLConnection connection = url.openConnection();
                if (connection != null) {
                    connection.setUseCaches(false);
                    stream = connection.getInputStream();
                }
            }
        } else {
            stream = loader.getResourceAsStream(resourceName);
        }
        
        if (stream != null) {
            try {
                // Força o uso de UTF-8 para ler o arquivo de propriedades
                bundle = new PropertyResourceBundle(new InputStreamReader(stream, StandardCharsets.UTF_8));
            } finally {
                stream.close();
            }
        }
        
        return bundle;
    }
}
