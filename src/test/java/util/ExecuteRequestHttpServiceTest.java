package util;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.junit.Assert;
import org.junit.Test;
import servicos.util.ExecuteRequestHttpService;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class ExecuteRequestHttpServiceTest {

    @Test
    public void testTrustStore() {
        HttpPost post = new HttpPost("https://api.bb.com.br/pix/v1");
        try {
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("grant_type", "client_credentials"));
            params.add(new BasicNameValuePair("scope", "cob.read cob.write pix.read pix.write"));
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            post.addHeader("Content-Type", "application/x-www-form-urlencoded");
            String basicAuthtoken = "213213213221".replaceAll(" |Basic", "");
            post.addHeader("Authorization", "Basic " + basicAuthtoken);

            HttpResponse response = ExecuteRequestHttpService.createConnector().execute(post);

            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            System.out.println(responseJsonString);
            Assert.assertTrue(responseJsonString, true);
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            e.printStackTrace();
        }

    }
}
