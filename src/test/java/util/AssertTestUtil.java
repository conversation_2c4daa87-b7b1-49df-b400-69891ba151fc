package util;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonParser;

import javax.xml.transform.*;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.StringReader;
import java.io.StringWriter;

import static org.junit.Assert.assertEquals;

/**
 * Respons�vel por fornecer somente m�todos utilit�rios parar realiza��o de asser��es no <b>JUnit 4</b>.
 *
 * <AUTHOR>
 * @since 12/02/2019
 */
public class AssertTestUtil {

    private static final String SIMBOLO_FECHA_TAG_XML = ">";

    /**
     * Realiza um <b>pretty print</b> do JSON de ambos os par�metros, ou seja, realiza uma <b>indenta��o</b> dos JSONs, para que seja mais f�cil analisar ambos
     * os resultados, caso haja diferen�as.
     *
     * @param actualString   String do JSON atual.
     * @param expectedString Sring do JSON esperado.
     */
    public static void assertEqualsJSONString(String expectedString, String actualString) {
        String actualStringPrettyJSON = returnPrettyJSONString(actualString);
        String expectedStringPrettyJSON = returnPrettyJSONString(expectedString);

        assertEquals(expectedStringPrettyJSON, actualStringPrettyJSON);
    }

    /**
     * Realiza um <b>pretty print</b> do XML de ambos os par�metros, ou seja, realiza uma <b>indenta��o</b> dos XMLs, para que seja mais f�cil analisar ambos
     * os resultados, caso haja diferen�as.
     *
     * @param actualString   String do JSON atual.
     * @param expectedString Sring do JSON esperado.
     */
    public static void assertEqualsXMLString(String expectedString, String actualString) throws TransformerException {
        String expectedStringPrettyXML = returnPrettyXMLString(expectedString);
        String actualStringPrettyXML = returnPrettyXMLString(actualString);

        assertEquals(expectedStringPrettyXML, actualStringPrettyXML);
    }

    /**
     * Um exemplo do que seria um JSON indentado:
     * <pre>
     * {
     *    "nome": "Moises",
     *    "caravanaOrigem":" Cambuci",
     *    "desenhos": [
     *                  "raquete do guda",
     *                  "violao",
     *                  "arcu-iru"
     *                ]
     * }
     * </pre>
     *
     * @return a String do JSON indentado.
     */
    private static String returnPrettyJSONString(String json) {
        return new GsonBuilder().setPrettyPrinting().create().toJson(new JsonParser().parse(json));
    }

    /**
     * @param xml caso o XML contiver espa�o em branco ao final da declara��o de uma tag, use o m�todo {@link #removeWhiteSpaceFromXML(String)}.
     *
     * @return o xml indentado.
     */
    private static String returnPrettyXMLString(String xml) throws TransformerException {
        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

        Source xmlInput = new StreamSource(new StringReader(xml));
        StreamResult result = new StreamResult(new StringWriter());
        transformer.transform(xmlInput, result);

        return result.getWriter().toString();
    }

    /**
     * Veja detalhes de como � o processo para retirar o espa�o em branco de cada linha em {@link #splitarPorFinalTagXML(String)}.
     *
     * @return o XML sem espa�os nas extremidades.
     */
    public static String removeWhiteSpaceFromXML(String xmlValue) {
        final StringBuilder sb = new StringBuilder();

        for (String string : splitarPorFinalTagXML(xmlValue)) {
            sb.append(string.trim()).append(SIMBOLO_FECHA_TAG_XML);
        }

        return sb.toString().substring(
                0, sb.toString().lastIndexOf(SIMBOLO_FECHA_TAG_XML)
        );
    }

    /**
     * Dado a string XML:
     *
     * <pre>
     * &lt;AccptrAuthstnReq&gt;
     *    &lt;Hdr&gt;
     *       &lt;MsgFctn&gt;AUTQ&lt;/MsgFctn&gt;
     *    &lt;/Hdr&gt;
     * &lt;/AccptrAuthstnReq&gt;
     * </pre>
     *
     * <p>
     * Dado o <code>simboloFechaTag</code> sendo um <b>'&gt;'</b>
     * </p>
     *
     * <p>
     * Splitted:
     * <pre>
     *     [0] - &lt;AccptrAuthstnReq
     *     [1] - &lt;Hdr
     *     [2] - &lt;MsgFctn&gt;AUTQ&lt;/MsgFctn
     *     [3] - &lt;/Hdr
     *     [4] - &lt;/AccptrAuthstnReq
     * </pre>
     * </p>
     *
     * @return realiza um split no XML no ponto aonde termina a declara��o de uma tag e encontra um espa�o.
     */
    private static String[] splitarPorFinalTagXML(String xmlValue) {
        return xmlValue.split(SIMBOLO_FECHA_TAG_XML + " ");
    }

}
