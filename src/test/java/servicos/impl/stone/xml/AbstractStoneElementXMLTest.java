package servicos.impl.stone.xml;

/**
 * <AUTHOR>
 * @since 05/03/2019
 */
public abstract class AbstractStoneElementXMLTest<T extends AbstractStoneElementXML> {

    /**
     * Para garantir que todo filho de {@link AbstractStoneElementXML} possui um construtor sem argumentos e p�blico
     * para a execu��o do m�todo {@link AbstractStoneElementXML#tryConverterFromXMLByNameSpace(String)} devido ao uso do reflection!
     */
    protected abstract T construtorSemArgumentosPublico();

}
