package servicos.impl.stone.xml.cancellation.response;

import org.junit.Test;
import servicos.impl.stone.xml.AbstractStoneElementXML;
import servicos.impl.stone.xml.AbstractStoneElementXMLTest;
import servicos.impl.stone.xml.authorization.request.TransactionIdentificationTxId;
import servicos.impl.stone.xml.authorization.response.HeaderResponseHdr;
import servicos.impl.stone.xml.authorization.response.ResponseToAuthorisationRspnToAuthstn;
import servicos.impl.stone.xml.authorization.response.TransactionDetailsResponseTxDtls;
import servicos.impl.stone.xml.authorization.response.TransactionResponseTx;
import servicos.impl.stone.xml.cancellation.request.MerchantIdentificationCancellationId;

import static org.junit.Assert.*;
import static servicos.impl.stone.xml.ExpectedStoneXML.RESPOSTA_PEDIDO_CANCELAMENTO_ENDPOINT_CANCELLATION;

/**
 * Teste unit�rio da classe {@link DocumentAcceptorCancellationResponseV02_1}.
 *
 * <AUTHOR>
 * @since 01/03/2019
 */
public class DocumentAcceptorCancellationResponseV02_1BuilderTest extends AbstractStoneElementXMLTest<DocumentAcceptorCancellationResponseV02_1> {

    @Test
    public void deve__Converter__DocumentAcceptorAuthorisationResponse__A__Partir__De__Uma__String__XML() throws InstantiationException, IllegalAccessException {
        DocumentAcceptorCancellationResponseV02_1 documentAcceptorCancellationResponse = AbstractStoneElementXML
                .tryConverterFromXMLByNameSpace(RESPOSTA_PEDIDO_CANCELAMENTO_ENDPOINT_CANCELLATION.getXmlValue());

        assertEquals("urn:AcceptorCancellationResponseV02.1", documentAcceptorCancellationResponse.getXmlns());
        assertEquals("http://www.w3.org/2001/XMLSchema", documentAcceptorCancellationResponse.getXmlnsXsd());
        assertEquals("http://www.w3.org/2001/XMLSchema-instance", documentAcceptorCancellationResponse.getXmlnsXsi());

        AcceptorCancellationResponseAccptrCxlRspn accptrCxlRspn = documentAcceptorCancellationResponse.getAcceptorCancellationResponseAccptrCxlRspn();
        HeaderResponseHdr hdr = accptrCxlRspn.getHeaderHdr();
        assertEquals("CCAP", hdr.getMsgFctn());
        assertEquals("2.0", hdr.getPrtcolVrsn());
        assertEquals("2017-06-16T11:48:32", hdr.getCreationDateTimeCreDtTm());

        CancellationResponseCxlRspn cxlRspn = accptrCxlRspn.getCancellationResponseCxlRspn();
        MerchantIdentificationCancellationId id = cxlRspn.getEnvironmentCancellationResponseEnvt().getMerchantIdentificationCancellationId();
        assertEquals("EF1CC1A4FECE40EB8B29CA7328955C88", id.getId());

        TransactionCancellationResponseTxRspn txRspn = cxlRspn.getTransactionCancellationResponseTxRspn();
        CancellationAuthorisationResultAuthstnRslt authstnRslt = txRspn.getCancellationAuthorisationResultAuthstnRslt();
        ResponseToAuthorisationRspnToAuthstn rspnToAuthstn = authstnRslt.getResponseToAuthorisationRspnToAuthstn();
        assertEquals("APPR", rspnToAuthstn.getResponseRspn());
        assertEquals("0000", rspnToAuthstn.getResponseReasonRspnRsn());
        assertFalse(authstnRslt.isCompletionRequiredCmpltnReqrd());

        TransactionResponseTx tx = cxlRspn.getTransactionResponseTx();
        assertEquals("15990053395244", tx.getRecipientTransactionIdentificationRcptTxId());

        TransactionIdentificationTxId txId = tx.getTransactionIdentificationTxId();
        assertEquals("2019-02-28T19:55:32", txId.getTransactionDateTimeTxDtTm());
        assertEquals("00003628022019195532", txId.getTransactionReferenceTxRef());

        TransactionDetailsResponseTxDtls txDtls = tx.getTransactionDetailsResponseTxDtls();
        assertEquals("986", txDtls.getCurrencyCcy());
        assertEquals("100", txDtls.getTotalAmountTtlAmt());
        assertNull(txDtls.getAccountTypeAcctTp());
    }

    @Override
    protected DocumentAcceptorCancellationResponseV02_1 construtorSemArgumentosPublico() {
        return new DocumentAcceptorCancellationResponseV02_1();
    }
}
