package servicos.impl.stone.xml.cancellation.request;

import org.junit.Test;
import servicos.impl.stone.xml.AbstractStoneElementXMLTest;
import servicos.impl.stone.xml.authorization.request.HeaderRequestHdr;
import servicos.impl.stone.xml.authorization.request.POIIdentificationId;
import servicos.impl.stone.xml.authorization.request.PointInteractionPOI;
import servicos.impl.stone.xml.authorization.request.TransactionIdentificationTxId;
import servicos.impl.stone.xml.authorization.response.TransactionDetailsResponseTxDtls;

import javax.xml.transform.TransformerException;

import static servicos.impl.stone.xml.ExpectedStoneXML.ENVIO_PEDIDO_CANCELAMENTO_ENDPOINT_CANCELLATION;
import static servicos.impl.stone.xml.authorization.request.AccountTypeAcctTp.CREDITO;
import static servicos.impl.stone.xml.authorization.request.CurrencyCcy.REAL_BRASILEIRO_ISO_4217;
import static servicos.impl.stone.xml.authorization.request.MessageFunctionMsgFctn.CANCELLATION_REQUEST_CCAQ;
import static servicos.impl.stone.xml.authorization.request.ProtocolVersionPrtcolVrsn.V_2_0_EPAS_ISO_20022_CAPE;
import static util.AssertTestUtil.assertEqualsXMLString;
import static util.AssertTestUtil.removeWhiteSpaceFromXML;

/**
 * Teste unit�rio da classe {@link DocumentAcceptorCancellationRequestV0_21}.
 *
 * <AUTHOR> Cattany
 * @since 28/02/2019
 */
public class DocumentAcceptorCancellationRequestV0_21Test extends AbstractStoneElementXMLTest<DocumentAcceptorCancellationRequestV0_21> {

    @Test
    public void deve__Criar__AcceptorCancellationRequest__Para__Realizar__Pedido__Cancelamento() throws TransformerException {
        String xml = createDocumentAcceptorCancellationRequestV02_1().toXML();
        assertEqualsXMLString(removeWhiteSpaceFromXML(ENVIO_PEDIDO_CANCELAMENTO_ENDPOINT_CANCELLATION.getXmlValue()), xml);
    }

    private DocumentAcceptorCancellationRequestV0_21 createDocumentAcceptorCancellationRequestV02_1() {
        return new DocumentAcceptorCancellationRequestV0_21(
                createAcceptorCancellationRequestAccptrCxlReq()
        );
    }

    private AcceptorCancellationRequestAccptrCxlReq createAcceptorCancellationRequestAccptrCxlReq() {
        return new AcceptorCancellationRequestAccptrCxlReq(
                createHeaderHdr(),
                createCancellationRequestCxlReq()
        );
    }

    private HeaderRequestHdr createHeaderHdr() {
        return new HeaderRequestHdr(CANCELLATION_REQUEST_CCAQ, V_2_0_EPAS_ISO_20022_CAPE);
    }

    private CancellationRequestCxlReq createCancellationRequestCxlReq() {
        return new CancellationRequestCxlReq(
                createEnvironmentCancellationEnvt(),
                createTransactionCancellationTx()
        );
    }

    private EnvironmentCancellationEnvt createEnvironmentCancellationEnvt() {
        return new EnvironmentCancellationEnvt(
                createMerchantCancellationMrchnt(),
                createPointInteractionPOI()
        );
    }

    private MerchantCancellationMrchnt createMerchantCancellationMrchnt() {
        return new MerchantCancellationMrchnt(
                createMerchantIdentificationCancellationId()
        );
    }

    private MerchantIdentificationCancellationId createMerchantIdentificationCancellationId() {
        return new MerchantIdentificationCancellationId("EF1CC1A4FECE40EB8B29CA7328955C88");
    }

    private PointInteractionPOI createPointInteractionPOI() {
        POIIdentificationId poiIdentificationId = new POIIdentificationId("2FB4C89A");

        return new PointInteractionPOI(poiIdentificationId);
    }

    private TransactionCancellationTx createTransactionCancellationTx() {
        TransactionIdentificationTxId transactionIdentificationTxId = new TransactionIdentificationTxId("2014-03-12T15:09:00", "12345ABC");
        TransactionDetailsResponseTxDtls transactionDetailsTxDtls = createTransactionDetailsResponseTxDtls();

        return new TransactionCancellationTx(
                true,
                transactionIdentificationTxId,
                transactionDetailsTxDtls,
                createOriginalTransactionOrgnlTx()
        );
    }

    private OriginalTransactionOrgnlTx createOriginalTransactionOrgnlTx() {
        return new OriginalTransactionOrgnlTx("123123123", "00000034071000000215346");
    }

    private TransactionDetailsResponseTxDtls createTransactionDetailsResponseTxDtls() {
        return new TransactionDetailsResponseTxDtls(REAL_BRASILEIRO_ISO_4217, "100", CREDITO);
    }

    @Override
    protected DocumentAcceptorCancellationRequestV0_21 construtorSemArgumentosPublico() {
        return new DocumentAcceptorCancellationRequestV0_21();
    }
}