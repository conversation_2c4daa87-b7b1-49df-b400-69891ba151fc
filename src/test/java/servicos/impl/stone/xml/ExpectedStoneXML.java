package servicos.impl.stone.xml;

import static servicos.impl.stone.xml.ExpectedStoneXML.Constants.INNER_RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE;

/**
 * Reposit�rio de mensageria de XMLs com API de <b>E-Commerce da Stone</b>.
 *
 * <AUTHOR>
 * @since 17/02/2019
 */
public enum ExpectedStoneXML {

    /**
     * Exemplo de um XML de envio para autoriza��o de cobran�a.
     *
     * <p>
     * Endpoint: <b>Authorize</b>
     * </p>
     * <p>
     * --> Opera��o respons�vel pela requisi��o de autoriza��o e captura da transa��o. A captura pode ser executada de maneira autom�tica junto a autoriza��o, pode ser uma captura posteriormente.
     * </p>
     */
    ENVIO_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE(""
            + "<Document xmlns=\"urn:AcceptorAuthorisationRequestV02.1\">         "
            + "    <AccptrAuthstnReq>                                             "
            + "         <Hdr>                                                     "
            + "             <MsgFctn>AUTQ</MsgFctn>                               "
            + "             <PrtcolVrsn>2.0</PrtcolVrsn>                          "
            + "         </Hdr>                                                    "
            + "         <AuthstnReq>                                              "
            + "             <Envt>                                                "
            + "                 <Mrchnt>                                          "
            + "                     <Id>                                          "
            + "                         <Id>d8e7983a5dcd4a639f983708effe97b9</Id> "
            + "                         <ShrtNm>Pacote Workout teste</ShrtNm>     "
            + "                     </Id>                                         "
            + "                 </Mrchnt>                                         "
            + "                 <POI>                                             "
            + "                    <Id>                                           "
            + "                       <Id>2FB4C89A</Id>                           "
            + "                    </Id>                                          "
            + "                    <SysNm>PACTO SOLUCOES TECNOLOGICAS</SysNm>     "
            + "                 </POI>                                            "
            + "                 <Card>                                            "
            + "                     <PlainCardData>                               "
            + "                         <PAN>****************</PAN>               "
            + "                         <XpryDt>2019-10</XpryDt>                  "
            + "                         <CardSctyCd>                              "
            + "                             <CSCVal>849</CSCVal>                  "
            + "                         </CardSctyCd>                             "
            + "                     </PlainCardData>                              "
            + "                 </Card>                                           "
            + "             </Envt>                                               "
            + "             <Cntxt>                                               "
            + "                 <PmtCntxt>                                        "
            + "                     <CardDataNtryMd>PHYS</CardDataNtryMd>         "
            + "                     <TxChanl>ECOM</TxChanl>                       "
            + "                 </PmtCntxt>                                       "
            + "             </Cntxt>                                              "
            + "             <Tx>                                                  "
            + "                 <TxId>                                            "
            + "                     <TxDtTm>2019-02-14T14:27:05</TxDtTm>          "
            + "                     <TxRef>01010101</TxRef>                       "
            + "                 </TxId>                                           "
            + "                 <InitrTxId>00000126022019142705</InitrTxId>       "
            + "                 <TxCaptr>true</TxCaptr>                           "
            + "                 <TxDtls>                                          "
            + "                     <Ccy>986</Ccy>                                "
            + "                     <TtlAmt>100</TtlAmt>                          "
            + "                     <AcctTp>CRDT</AcctTp>                         "
            + "                     <Rsn>4000</Rsn>                               "
            + "                     <RcrngTx>                                     "
            + "                         <InstlmtTp>NONE</InstlmtTp>               "
            + "                         <TtlNbOfPmts>0</TtlNbOfPmts>              "
            + "                     </RcrngTx>                                    "
            + "                 </TxDtls>                                         "
            + "             </Tx>                                                 "
            + "         </AuthstnReq>                                             "
            + "    </AccptrAuthstnReq>                                            "
            + "</Document>                                                        "
    ),
    /**
     * Exemplo de um XML do retorno de cobran�a.
     *
     * <p>
     * Response do Endpoint: <b>Authorize</b>
     * </p>
     */
    RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE(
            String.format(INNER_RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE, ""
                    + "<Actn>                              "
                    + "    <ActnTp>DISP</ActnTp>           "
                    + "    <MsgToPres>                     "
                    + "        <MsgDstn>MDSP</MsgDstn>     "
                    + "        <MsgCntt>Aprovado</MsgCntt> "
                    + "    </MsgToPres>                    "
                    + "</Actn>                             "
            )
    ),
    /**
     * Um variante de {@link #RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE}.
     */
    RESPOSTA_PEDIDO_AUTORIZACAO_COM_DUAS_MESSAGE_DESTINATION_MSG_DSTN_ENDPOINT_AUTHORIZE(
            String.format(INNER_RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE, ""
                    + "<Actn>                                    "
                    + "    <ActnTp>DISP</ActnTp>                 "
                    + "    <MsgToPres>                           "
                    + "        <MsgDstn>MDSP</MsgDstn>           "
                    + "        <MsgCntt>Cart�o vencido</MsgCntt> "
                    + "    </MsgToPres>                          "
                    + "</Actn>                                   "
                    + "<Actn>                                    "
                    + "    <ActnTp>DISP</ActnTp>                 "
                    + "    <MsgToPres>                           "
                    + "        <MsgDstn>CDSP</MsgDstn>           "
                    + "        <MsgCntt>Cart�o vencido</MsgCntt> "
                    + "    </MsgToPres>                          "
                    + "</Actn>                                   "
            )
    ),
    /**
     * Exemplo de um XML do retorno de cobran�a, quando � rejeitado.
     *
     * <p>
     * Response do Endpoint: <b>Authorize</b>
     * </p>
     */
    RESPOSTA_REJEICAO_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE(""
            + "<Document xmlns=\"urn:AcceptorRejectionV02.1\"                                             "
            + " xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"                                            "
            + " xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">                                  "
            + "    <AccptrRjctn>                                                                          "
            + "        <Hdr>                                                                              "
            + "            <MsgFctn>RJCT</MsgFctn>                                                        "
            + "            <PrtcolVrsn>2.0</PrtcolVrsn>                                                   "
            + "            <CreDtTm>2019-02-28T19:07:35</CreDtTm>                                         "
            + "        </Hdr>                                                                             "
            + "        <Rjct>                                                                             "
            + "            <RjctRsn>IMSG</RjctRsn>                                                        "
            + "            <AddtlInf>Identification - GenericIdentification  Id cannot be null</AddtlInf> "
            + "            <MsgInErr/>                                                                    "
            + "        </Rjct>                                                                            "
            + "    </AccptrRjctn>                                                                         "
            + "</Document>                                                                                "
    ),
    /**
     * Exemplo de um XML de envio para cancelamento de uma cobran�a.
     *
     * <p>
     * Endpoint: <b>CANCELLATION</b>
     * </p>
     * <p>
     * --> Opera��o respons�vel pelo cancelamento de transa��es.
     * </p>
     */
    ENVIO_PEDIDO_CANCELAMENTO_ENDPOINT_CANCELLATION(""
            + "<Document xmlns=\"urn:AcceptorCancellationRequestV02.1\">         "
            + "    <AccptrCxlReq>                                                "
            + "        <Hdr>                                                     "
            + "            <MsgFctn>CCAQ</MsgFctn>                               "
            + "            <PrtcolVrsn>2.0</PrtcolVrsn>                          "
            + "        </Hdr>                                                    "
            + "        <CxlReq>                                                  "
            + "            <Envt>                                                "
            + "                <Mrchnt>                                          "
            + "                    <Id>                                          "
            + "                        <Id>EF1CC1A4FECE40EB8B29CA7328955C88</Id> "
            + "                    </Id>                                         "
            + "                </Mrchnt>                                         "
            + "               <POI>                                              "
            + "                   <Id>                                           "
            + "                       <Id>2FB4C89A</Id>                          "
            + "                   </Id>                                          "
            + "                    <SysNm>PACTO SOLUCOES TECNOLOGICAS</SysNm>    "
            + "               </POI>                                             "
            + "            </Envt>                                               "
            + "            <Tx>                                                  "
            + "                <TxId>                                            "
            + "                    <TxDtTm>2014-03-12T15:09:00</TxDtTm>          "
            + "                    <TxRef>12345ABC</TxRef>                       "
            + "                </TxId>                                           "
            + "                <TxCaptr>true</TxCaptr>                           "
            + "                <TxDtls>                                          "
            + "                    <Ccy>986</Ccy>                                "
            + "                    <TtlAmt>100</TtlAmt>                          "
            + "                    <AcctTp>CRDT</AcctTp>                         "
            + "                    <Rsn>4000</Rsn>                               "
            + "                </TxDtls>                                         "
            + "                <OrgnlTx>                                         "
            + "                    <InitrTxId>123123123</InitrTxId>              "
            + "                    <RcptTxId>00000034071000000215346</RcptTxId>  "
            + "                </OrgnlTx>                                        "
            + "            </Tx>                                                 "
            + "        </CxlReq>                                                 "
            + "    </AccptrCxlReq>                                               "
            + "</Document>                                                       "
    ),
    /**
     * Exemplo de um XML do retorno de pedido de cancelamento.
     *
     * <p>
     * Response do Endpoint: <b>CANCELLATION</b>
     * </p>
     */
    RESPOSTA_PEDIDO_CANCELAMENTO_ENDPOINT_CANCELLATION(""
            + " <Document xmlns=\"urn:AcceptorCancellationResponseV02.1\"       "
            + " xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"                  "
            + " xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">        "
            + "      <AccptrCxlRspn>                                            "
            + "          <Hdr>                                                  "
            + "              <MsgFctn>CCAP</MsgFctn>                            "
            + "              <PrtcolVrsn>2.0</PrtcolVrsn>                       "
            + "              <CreDtTm>2017-06-16T11:48:32</CreDtTm>             "
            + "          </Hdr>                                                 "
            + "          <CxlRspn>                                              "
            + "              <Envt>                                             "
            + "                  <MrchntId>                                     "
            + "                      <Id>EF1CC1A4FECE40EB8B29CA7328955C88</Id>  "
            + "                  </MrchntId>                                    "
            + "              </Envt>                                            "
            + "              <TxRspn>                                           "
            + "                 <AuthstnRslt>                                   "
            + "                     <RspnToAuthstn>                             "
            + "                         <Rspn>APPR</Rspn>                       "
            + "                         <RspnRsn>0000</RspnRsn>                 "
            + "                     </RspnToAuthstn>                            "
            + "                     <CmpltnReqrd>false</CmpltnReqrd>            "
            + "                 </AuthstnRslt>                                  "
            + "             </TxRspn>                                           "
            + "             <Tx>                                                "
            + "                 <TxId>                                          "
            + "                     <TxDtTm>2019-02-28T19:55:32</TxDtTm>        "
            + "                     <TxRef>00003628022019195532</TxRef>         "
            + "                 </TxId>                                         "
            + "                 <RcptTxId>15990053395244</RcptTxId>             "
            + "                 <TxDtls>                                        "
            + "                     <Ccy>986</Ccy>                              "
            + "                     <TtlAmt>100</TtlAmt>                        "
            + "                 </TxDtls>                                       "
            + "             </Tx>                                               "
            + "          </CxlRspn>                                             "
            + "     </AccptrCxlRspn>                                            "
            + " </Document>                                                     "
    );

    private final String xmlValue;

    ExpectedStoneXML(String xmlValue) {
        this.xmlValue = xmlValue;
    }

    public String getXmlValue() {
        return xmlValue;
    }

    static class Constants {

        static final String INNER_RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE = ""
                + "<Document xmlns=\"urn:AcceptorAuthorisationResponseV02.1\"          "
                + " xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"                     "
                + " xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">           "
                + "    <AccptrAuthstnRspn>                                             "
                + "          <Hdr>                                                     "
                + "              <MsgFctn>AUTQ</MsgFctn>                               "
                + "              <PrtcolVrsn>2.0</PrtcolVrsn>                          "
                + "              <CreDtTm>2019-02-26T14:38:40</CreDtTm>                "
                + "          </Hdr>                                                    "
                + "          <AuthstnRspn>                                             "
                + "              <Envt>                                                "
                + "                  <MrchntId>                                        "
                + "                      <Id>d8e7983a5dcd4a639f983708effe97b9</Id>     "
                + "                      <ShrtNm>Pacote Workout teste</ShrtNm>         "
                + "                  </MrchntId>                                       "
                + "              </Envt>                                               "
                + "              <Tx>                                                  "
                + "                  <TxId>                                            "
                + "                      <TxDtTm>2019-02-14T14:27:05</TxDtTm>          "
                + "                      <TxRef>01010101</TxRef>                       "
                + "                  </TxId>                                           "
                + "                  <RcptTxId>15790053392662</RcptTxId>               "
                + "                  <TxDtls>                                          "
                + "                      <Ccy>986</Ccy>                                "
                + "                      <TtlAmt>100</TtlAmt>                          "
                + "                      <AcctTp>CRDT</AcctTp>                         "
                + "                  </TxDtls>                                         "
                + "              </Tx>                                                 "
                + "              <TxRspn>                                              "
                + "                  <AuthstnRslt>                                     "
                + "                      <RspnToAuthstn>                               "
                + "                          <Rspn>APPR</Rspn>                         "
                + "                          <RspnRsn>0000</RspnRsn>                   "
                + "                      </RspnToAuthstn>                              "
                + "                      <AuthstnCd>392662</AuthstnCd>                 "
                + "                      <CmpltnReqrd>false</CmpltnReqrd>              "
                + "                  </AuthstnRslt>                                    "
                + "                 %s                                                 "
                + "              </TxRspn>                                             "
                + "          </AuthstnRspn>                                            "
                + "    </AccptrAuthstnRspn>                                            "
                + "</Document>                                                         ";
    }
}
