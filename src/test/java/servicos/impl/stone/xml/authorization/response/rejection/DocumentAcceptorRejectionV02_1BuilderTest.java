package servicos.impl.stone.xml.authorization.response.rejection;

import org.junit.Test;
import servicos.impl.stone.xml.AbstractStoneElementXML;
import servicos.impl.stone.xml.AbstractStoneElementXMLTest;
import servicos.impl.stone.xml.authorization.response.HeaderResponseHdr;

import static org.junit.Assert.assertEquals;
import static servicos.impl.stone.xml.ExpectedStoneXML.RESPOSTA_REJEICAO_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE;
import static servicos.impl.stone.xml.authorization.request.MessageFunctionMsgFctn.REJECTION_RJCT;

/**
 * Teste unit�rio da classe {@link DocumentAcceptorRejectionV02_1}.
 *
 * <AUTHOR>
 * @since 28/02/2019
 */
public class DocumentAcceptorRejectionV02_1BuilderTest extends AbstractStoneElementXMLTest<DocumentAcceptorRejectionV02_1> {

    @Test
    public void deve__Converter__DocumentAcceptorRejection__A__Partir__De__Uma__String__XML() throws InstantiationException, IllegalAccessException {
        DocumentAcceptorRejectionV02_1 documentAcceptorRejection = AbstractStoneElementXML
                .tryConverterFromXMLByNameSpace(RESPOSTA_REJEICAO_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE.getXmlValue());

        assertEquals("urn:AcceptorRejectionV02.1", documentAcceptorRejection.getXmlns());
        assertEquals("http://www.w3.org/2001/XMLSchema", documentAcceptorRejection.getXmlnsXsd());
        assertEquals("http://www.w3.org/2001/XMLSchema-instance", documentAcceptorRejection.getXmlnsXsi());

        AcceptorRejectionAccptrRjctn authstnRspn = documentAcceptorRejection.getAcceptorAuthorisationResponseAccptrAuthstnRspn();
        HeaderResponseHdr hdr = authstnRspn.getHeaderHdr();
        assertEquals(REJECTION_RJCT, hdr.getMsgFctnType());
        assertEquals("2.0", hdr.getPrtcolVrsn());
        assertEquals("2019-02-28T19:07:35", hdr.getCreationDateTimeCreDtTm());

        RejectionRjct rjct = authstnRspn.getRejectionRjct();
        assertEquals("IMSG", rjct.getRejectionReasonRjctRsn());
        assertEquals("Identification - GenericIdentification  Id cannot be null", rjct.getAdditionalInformationAddtlInf());
    }

    @Override
    protected DocumentAcceptorRejectionV02_1 construtorSemArgumentosPublico() {
        return new DocumentAcceptorRejectionV02_1();
    }
}