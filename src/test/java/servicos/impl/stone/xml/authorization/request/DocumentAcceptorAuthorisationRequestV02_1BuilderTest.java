package servicos.impl.stone.xml.authorization.request;

import org.junit.Test;
import servicos.impl.stone.xml.AbstractStoneElementXMLTest;

import javax.xml.transform.TransformerException;

import static servicos.impl.stone.xml.ExpectedStoneXML.ENVIO_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE;
import static servicos.impl.stone.xml.authorization.request.AccountTypeAcctTp.CREDITO;
import static servicos.impl.stone.xml.authorization.request.CurrencyCcy.REAL_BRASILEIRO_ISO_4217;
import static servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp.A_VISTA;
import static servicos.impl.stone.xml.authorization.request.MessageFunctionMsgFctn.AUTHORISATION_REQUEST_AUTQ;
import static servicos.impl.stone.xml.authorization.request.ProtocolVersionPrtcolVrsn.V_2_0_EPAS_ISO_20022_CAPE;
import static util.AssertTestUtil.assertEqualsXMLString;
import static util.AssertTestUtil.removeWhiteSpaceFromXML;

/**
 * Teste unit�rio da classe {@link DocumentAcceptorAuthorisationRequestV02_1}.
 *
 * <AUTHOR> Cattany
 * @since 11/02/2019
 */
public class DocumentAcceptorAuthorisationRequestV02_1BuilderTest extends AbstractStoneElementXMLTest<DocumentAcceptorAuthorisationRequestV02_1> {

    @Test
    public void deve__Criar__DocumentAcceptorAuthorisationRequest__Para__Realizar__Pedido__Autorizacao() throws TransformerException {
        String xml = createDocumentAcceptorAuthorisationRequestV02_1().toXML();
        assertEqualsXMLString(removeWhiteSpaceFromXML(ENVIO_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE.getXmlValue()), xml);
    }

    private DocumentAcceptorAuthorisationRequestV02_1 createDocumentAcceptorAuthorisationRequestV02_1() {
        return new DocumentAcceptorAuthorisationRequestV02_1(
                createAcceptorAuthorisationRequestAccptrAuthstnReq()
        );
    }

    private AcceptorAuthorisationRequestAccptrAuthstnReq createAcceptorAuthorisationRequestAccptrAuthstnReq() {
        return new AcceptorAuthorisationRequestAccptrAuthstnReq(
                createHeaderHdr(),
                createAuthorisationRequestAuthstnReq()
        );
    }

    private HeaderRequestHdr createHeaderHdr() {
        return new HeaderRequestHdr(AUTHORISATION_REQUEST_AUTQ, V_2_0_EPAS_ISO_20022_CAPE);
    }

    private AuthorisationRequestAuthstnReq createAuthorisationRequestAuthstnReq() {
        return new AuthorisationRequestAuthstnReq(
                createEnvironmentRequestEnvt(),
                createContextCntxt(),
                createTransactionTx()
        );
    }

    private EnvironmentRequestEnvt createEnvironmentRequestEnvt() {
        MerchantIdentificationId merchantIdentificationId = new MerchantIdentificationId("d8e7983a5dcd4a639f983708effe97b9", "Pacote Workout teste");
        MerchantMrchnt merchantMrchnt = new MerchantMrchnt(merchantIdentificationId);

        return new EnvironmentRequestEnvt(
                merchantMrchnt,
                createPointInteractionPOI(),
                createCard()
        );
    }

    private PointInteractionPOI createPointInteractionPOI() {
        POIIdentificationId poiIdentificationId = new POIIdentificationId("2FB4C89A");

        return new PointInteractionPOI(poiIdentificationId);
    }

    private Card createCard() {
        CardSecurityCodeCardSctyCd cardSecurityCodeCardSctyCd = new CardSecurityCodeCardSctyCd("849");
        PlainCardData plainCardData = new PlainCardData("****************", "2019-10", cardSecurityCodeCardSctyCd);

        return new Card(plainCardData);
    }

    private ContextCntxt createContextCntxt() {
        PaymentContextPmtCntxt paymentContextPmtCntxt = new PaymentContextPmtCntxt(CardDataEntryModeCardDataNtryMd.ECOMMERCE_OU_PRIMEIRA_RECORRENCIA, TransactionChannelTxChanl.ECOMMERCE_OU_DIGITADA);
        return new ContextCntxt(paymentContextPmtCntxt);
    }

    private TransactionTx createTransactionTx() {
        TransactionIdentificationTxId transactionIdentificationTxId = new TransactionIdentificationTxId("2019-02-14T14:27:05", "01010101");
        TransactionDetailsTxDtls transactionDetailsTxDtls = createTransactionDetailsTxDtls();
        return new TransactionTx(
                "00000126022019142705",
                true,
                transactionIdentificationTxId,
                transactionDetailsTxDtls
        );
    }

    private TransactionDetailsTxDtls createTransactionDetailsTxDtls() {
        return new TransactionDetailsTxDtls(REAL_BRASILEIRO_ISO_4217, "100", CREDITO, new RecurringTransactionRcrngTx(A_VISTA, "0"));
    }

    @Override
    protected DocumentAcceptorAuthorisationRequestV02_1 construtorSemArgumentosPublico() {
        return new DocumentAcceptorAuthorisationRequestV02_1();
    }
}
