package servicos.impl.stone.xml.authorization.response;

import org.junit.Test;
import servicos.impl.stone.xml.AbstractStoneElementXML;
import servicos.impl.stone.xml.AbstractStoneElementXMLTest;
import servicos.impl.stone.xml.authorization.request.MerchantIdentificationId;
import servicos.impl.stone.xml.authorization.request.TransactionIdentificationTxId;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static servicos.impl.stone.xml.ExpectedStoneXML.RESPOSTA_PEDIDO_AUTORIZACAO_COM_DUAS_MESSAGE_DESTINATION_MSG_DSTN_ENDPOINT_AUTHORIZE;
import static servicos.impl.stone.xml.ExpectedStoneXML.RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE;

/**
 * Teste unit�rio da classe {@link DocumentAcceptorAuthorisationResponseV02_1}.
 *
 * <AUTHOR>
 * @since 11/02/2019
 */
public class DocumentAcceptorAuthorisationResponseV02_1BuilderTest extends AbstractStoneElementXMLTest<DocumentAcceptorAuthorisationResponseV02_1> {

    @Test
    public void deve__Converter__DocumentAcceptorAuthorisationResponse__A__Partir__De__Uma__String__XML() throws InstantiationException, IllegalAccessException {
        DocumentAcceptorAuthorisationResponseV02_1 documentAcceptorAuthorisationResponse = AbstractStoneElementXML
                .tryConverterFromXMLByNameSpace(RESPOSTA_PEDIDO_AUTORIZACAO_ENDPOINT_AUTHORIZE.getXmlValue());

        assertResult(documentAcceptorAuthorisationResponse);

        ActionActn actn = getTxRspn(documentAcceptorAuthorisationResponse).getActionActn().get(0);
        assertEquals("DISP", actn.getActionTypeActnTp());

        MessageToPresentMsgToPres msgToPres = actn.getMessageToPresentMsgToPres();
        assertEquals("MDSP", msgToPres.getMessageDestinationMsgDstn());
        assertEquals("Aprovado", msgToPres.getMessageContentMsgCntt());
    }

    @Test
    public void deve__Converter__DocumentAcceptorAuthorisationResponse__A__Partir__De__Uma__String__XML__Com__Dois__MessageDestination__MsgDstn() throws InstantiationException, IllegalAccessException {
        DocumentAcceptorAuthorisationResponseV02_1 documentAcceptorAuthorisationResponse = AbstractStoneElementXML
                .tryConverterFromXMLByNameSpace(RESPOSTA_PEDIDO_AUTORIZACAO_COM_DUAS_MESSAGE_DESTINATION_MSG_DSTN_ENDPOINT_AUTHORIZE.getXmlValue());

        assertResult(documentAcceptorAuthorisationResponse);

        List<ActionActn> actionActn = getTxRspn(documentAcceptorAuthorisationResponse).getActionActn();
        for (ActionActn actn : actionActn) {
            MessageDestinationMsgDstn msgDstnType = actn.getMessageToPresentMsgToPres().getMessageDestinationMsgDstnType();
            MessageToPresentMsgToPres msgToPres = actn.getMessageToPresentMsgToPres();

            assertEquals("DISP", actn.getActionTypeActnTp());
            assertEquals("Cart�o vencido", msgToPres.getMessageContentMsgCntt());

            if (msgDstnType.isMensagemExibidaDisplayMDSP()) {
                assertEquals("MDSP", msgToPres.getMessageDestinationMsgDstn());
            } else if (msgDstnType.isMensagemExibidaDisplayClienteCDSP()) {
                assertEquals("CDSP", msgToPres.getMessageDestinationMsgDstn());
            }
        }
    }

    private void assertResult(DocumentAcceptorAuthorisationResponseV02_1 documentAcceptorAuthorisationResponse) {
        assertEquals("urn:AcceptorAuthorisationResponseV02.1", documentAcceptorAuthorisationResponse.getXmlns());
        assertEquals("http://www.w3.org/2001/XMLSchema", documentAcceptorAuthorisationResponse.getXmlnsXsd());
        assertEquals("http://www.w3.org/2001/XMLSchema-instance", documentAcceptorAuthorisationResponse.getXmlnsXsi());

        AcceptorAuthorisationResponseAccptrAuthstnRspn accptrAuthstnRspn = documentAcceptorAuthorisationResponse.getAcceptorAuthorisationResponseAccptrAuthstnRspn();
        HeaderResponseHdr hdr = accptrAuthstnRspn.getHeaderHdr();
        assertEquals("AUTQ", hdr.getMsgFctn());
        assertEquals("2.0", hdr.getPrtcolVrsn());
        assertEquals("2019-02-26T14:38:40", hdr.getCreationDateTimeCreDtTm());

        AuthorisationResponseAuthstnRspn authstnRspn = accptrAuthstnRspn.getAuthorisationResponseAuthstnRspn();
        MerchantIdentificationId id = authstnRspn.getEnvt().getMrchnt();
        assertEquals("d8e7983a5dcd4a639f983708effe97b9", id.getId());
        assertEquals("Pacote Workout teste", id.getShortNameShrtNm());

        TransactionResponseTx tx = authstnRspn.getTransactionResponseTx();
        assertEquals("15790053392662", tx.getRecipientTransactionIdentificationRcptTxId());

        TransactionIdentificationTxId txId = tx.getTransactionIdentificationTxId();
        assertEquals("2019-02-14T14:27:05", txId.getTransactionDateTimeTxDtTm());
        assertEquals("********", txId.getTransactionReferenceTxRef());

        TransactionDetailsResponseTxDtls txDtls = tx.getTransactionDetailsResponseTxDtls();
        assertEquals("986", txDtls.getCurrencyCcy());
        assertEquals("100", txDtls.getTotalAmountTtlAmt());
        assertEquals("CRDT", txDtls.getAccountTypeAcctTp());

        TransactionResponseTxRspn txRspn = authstnRspn.getTransactionResponseTxRspn();
        AuthorisationResultAuthstnRslt authstnRslt = txRspn.getAuthorisationResultAuthstnRslt();
        ResponseToAuthorisationRspnToAuthstn rspnToAuthstn = authstnRslt.getResponseToAuthorisationRspnToAuthstn();
        assertEquals("APPR", rspnToAuthstn.getResponseRspn());
        assertEquals("0000", rspnToAuthstn.getResponseReasonRspnRsn());
        assertEquals("392662", authstnRslt.getAuthorisationCodeAuthstnCd());
        assertFalse(authstnRslt.isCompletionRequiredCmpltnReqrd());
    }

    @Override
    protected DocumentAcceptorAuthorisationResponseV02_1 construtorSemArgumentosPublico() {
        return new DocumentAcceptorAuthorisationResponseV02_1();
    }

    private TransactionResponseTxRspn getTxRspn(DocumentAcceptorAuthorisationResponseV02_1 documentAcceptorAuthorisationResponse) {
        return documentAcceptorAuthorisationResponse
                .getAcceptorAuthorisationResponseAccptrAuthstnRspn()
                .getAuthorisationResponseAuthstnRspn()
                .getTransactionResponseTxRspn();
    }
}