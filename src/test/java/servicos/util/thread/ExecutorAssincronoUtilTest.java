package servicos.util.thread;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 * @since 20/03/19
 */
public class ExecutorAssincronoUtilTest {

    @Test
    public void deve_executar_acao() throws Exception {
        final List<String> mapaTeste = new ArrayList<String>();
        final String ACAO_EXECUTADA = "ACAO EXECUTADA";

        final Callback acao = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(ACAO_EXECUTADA);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        ExecutorAssincronoUtil.executarAssincrono(acao);
        await().atMost(5, SECONDS).until(new Callable<Boolean>() {
            @Override
            public Boolean call() {
                return mapaTeste.size() == 1 && mapaTeste.get(0).equals(ACAO_EXECUTADA);
            }
        });
    }

    @Test
    public void nao_deve_executar_callback_falha_quando_acao_executar() throws Exception {
        final List<String> mapaTeste = new ArrayList<String>();
        final String ACAO_EXECUTADA = "ACAO EXECUTADA";

        final Callback acao = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(ACAO_EXECUTADA);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        final Callback erro = new Callback() {
            @Override
            public void call() {
                fail("Callback de erro foi chamado!");
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        ExecutorAssincronoUtil.executarAssincrono(acao, null, erro);
        await().atMost(5, SECONDS).until(new Callable<Boolean>() {
            @Override
            public Boolean call() {
                return mapaTeste.size() == 1 && mapaTeste.get(0).equals(ACAO_EXECUTADA);
            }
        });
    }

    @Test
    public void deve_executar_acao_e_callback_sucesso() throws Exception {
        final List<String> mapaTeste = new ArrayList<String>();
        final String ACAO_EXECUTADA = "ACAO EXECUTADA";
        final String CALLBACK_SUCESSO_EXECUTADO = "CALLBACK SUCESSO EXECUTADO";

        final Callback acao = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(ACAO_EXECUTADA);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        final Callback sucesso = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(CALLBACK_SUCESSO_EXECUTADO);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        ExecutorAssincronoUtil.executarAssincrono(acao, sucesso, null);
        await().atMost(5, SECONDS).until(new Callable<Boolean>() {
            @Override
            public Boolean call() {
                return mapaTeste.size() == 2
                        && mapaTeste.contains(ACAO_EXECUTADA)
                        && mapaTeste.contains(CALLBACK_SUCESSO_EXECUTADO);
            }
        });
    }

    @Test
    public void deve_executar_callback_erro_quando_acao_lanca_excecao() throws Exception {
        final List<String> mapaTeste = new ArrayList<String>();
        final String CALLBACK_ERRO_EXECUTADO = "CALLBACK ERRO EXECUTADO";

        final Callback acao = new Callback() {
            @Override
            public void call() throws Exception {
                throw new Exception("Exce��o...");
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        final Callback erro = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(CALLBACK_ERRO_EXECUTADO);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        ExecutorAssincronoUtil.executarAssincrono(acao, null, erro);
        await().atMost(5, SECONDS).until(new Callable<Boolean>() {
            @Override
            public Boolean call() {
                return mapaTeste.size() == 1 && mapaTeste.get(0).equals(CALLBACK_ERRO_EXECUTADO);
            }
        });
    }

    @Test
    public void deve_executar_callback_erro_quando_callback_sucesso_lanca_excecao() throws Exception {
        final List<String> mapaTeste = new ArrayList<String>();
        final String ACAO_EXECUTADA = "ACAO EXECUTADA";
        final String CALLBACK_ERRO_EXECUTADO = "CALLBACK ERRO EXECUTADO";

        final Callback acao = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(ACAO_EXECUTADA);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        final Callback sucesso = new Callback() {
            @Override
            public void call() throws Exception {
                throw new Exception("Exce��o...");
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        final Callback erro = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(CALLBACK_ERRO_EXECUTADO);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        ExecutorAssincronoUtil.executarAssincrono(acao, sucesso, erro);
        await().atMost(5, SECONDS).until(new Callable<Boolean>() {
            @Override
            public Boolean call() {
                return mapaTeste.size() == 2 && mapaTeste.contains(ACAO_EXECUTADA) && mapaTeste.contains(CALLBACK_ERRO_EXECUTADO);
            }
        });
    }

    @Test
    public void deve_executar_callback_erro_quando_houver_timeout() throws Exception {
        final List<String> mapaTeste = new ArrayList<String>();
        final String CALLBACK_ERRO_EXECUTADO = "CALLBACK ERRO EXECUTADO";

        final Callback acao = new Callback() {
            @Override
            public void call() {
                for(;;);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        final Callback erro = new Callback() {
            @Override
            public void call() {
                mapaTeste.add(CALLBACK_ERRO_EXECUTADO);
            }

            @Override
            public Map<String, Object> getParametros() {
                return null;
            }
        };

        ExecutorAssincronoUtil.executarAssincrono(acao, null, erro, 1000L);
        await().atMost(5, SECONDS).until(new Callable<Boolean>() {
            @Override
            public Boolean call() {
                return mapaTeste.size() == 1 && mapaTeste.get(0).equals(CALLBACK_ERRO_EXECUTADO);
            }
        });
    }

}
