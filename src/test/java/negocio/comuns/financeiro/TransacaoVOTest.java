package negocio.comuns.financeiro;

import junitparams.JUnitParamsRunner;
import junitparams.Parameters;
import junitparams.naming.TestCaseName;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static java.util.Arrays.asList;
import static negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum.*;
import static negocio.comuns.financeiro.enumerador.TipoTransacaoEnum.NENHUMA;
import static negocio.comuns.financeiro.enumerador.TipoTransacaoEnum.*;
import static negocio.comuns.utilitarias.Calendario.hoje;
import static negocio.comuns.utilitarias.Calendario.ontem;
import static negocio.comuns.utilitarias.Uteis.voltarDiasAPartirHoje;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Teste unit�rio da classe {@link TransacaoVO}
 *
 * <AUTHOR> Cattany
 * @since 07/03/2019
 */
@RunWith(JUnitParamsRunner.class)
public class TransacaoVOTest {

    private static final String LABEL_QUANDO_A_TRANSACAO = "Quando a transa��o";
    private static final String LABEL_DEVE_PERMITIR_CANCELAR = "-> Deve ser permitido cancelar";
    private static final String LABEL_NAO_DEVE_PERMITIR_CANCELAR = "-> N�o deve ser permitido cancelar";

    @Parameters(method = "listarSituacoesPermitemCancelar")
    @TestCaseName("Teste com situa��o que deve permitir cancelar n� {index} - Situa��o: {0}")
    @Test
    public void test__Permitir__Pela__Situacao(SituacaoTransacaoEnum situacaoTransacaoEnum) {
        assertTrue(
                String.format("%s est� com a situa��o (%s) %s", LABEL_QUANDO_A_TRANSACAO, situacaoTransacaoEnum, LABEL_NAO_DEVE_PERMITIR_CANCELAR),
                retornarTransacaoComSituacao(situacaoTransacaoEnum).isPermiteCancelar()
        );
    }

    @Parameters(method = "listarSituacoesNaoPermitemCancelar")
    @TestCaseName("Teste com situa��o que n�o deve permitir cancelar n� {index} - Situa��o: {0}")
    @Test
    public void test__Nao__Deve__Permitir__Pela__Situacao(SituacaoTransacaoEnum situacaoTransacaoEnum) {
        assertFalse(
                String.format("%s est� com a situa��o (%s) %s", LABEL_QUANDO_A_TRANSACAO, situacaoTransacaoEnum, LABEL_DEVE_PERMITIR_CANCELAR),
                retornarTransacaoComSituacao(situacaoTransacaoEnum).isPermiteCancelar()
        );
    }

    @Parameters(method = "listarDatasPermitemCancelar")
    @TestCaseName("Teste com data de processamento que deve permitir cancelar n� {index} - Data de processamento: {0}")
    @Test
    public void test__deve__Permitir__Pela__DataProcessamento(Date dataProcessamento) {
        assertTrue(
                String.format("%s possui a data de processamento (%s) %s", LABEL_QUANDO_A_TRANSACAO, dataProcessamento, LABEL_NAO_DEVE_PERMITIR_CANCELAR),
                retornarTransacaoComSituacaoEData(APROVADA, dataProcessamento).isPermiteCancelar()
        );
    }

    @Parameters(method = "listarTiposTransacoesPermitemCancelarHoje")
    @TestCaseName("Teste com tipo de transa��o que deve permitir cancelar quando aprovada e data processamento de hoje n� {index} - Tipo da transa��o: {0}")
    @Test
    public void test__Deve__Permitir__Pelo__Tipo__Quando__Aprovada__E__Data__Processamento__Hoje(TipoTransacaoEnum tipoTransacaoEnum) {
        String mensagemCasoFalha = String.format("%s � do tipo (%s), com a situa��o %s e com a data de processamento de hoje %s",
                LABEL_QUANDO_A_TRANSACAO,
                tipoTransacaoEnum,
                APROVADA,
                LABEL_NAO_DEVE_PERMITIR_CANCELAR
        );

        assertTrue(mensagemCasoFalha, retornarTransacaoComValor(APROVADA, hoje(), tipoTransacaoEnum).isPermiteCancelar());
    }

    @Parameters(method = "listarTiposTransacoesPermitemApenasHoje")
    @TestCaseName("Teste com tipo de transa��o que deve permitir cancelar quando aprovada e apenas com a data processamento de hoje n� {index} - Tipo da transa��o: {0}")
    @Test
    public void test__Deve__Permitir__Pelo__Tipo__Apenas__Quando__Data__Processamento__De__Hoje(TipoTransacaoEnum tipoTransacaoEnum) {
        String mensagemCasoFalhaDataHoje = String.format("%s � do tipo (%s), com a situa��o %s e com a data de processamento de hoje %s",
                LABEL_QUANDO_A_TRANSACAO,
                tipoTransacaoEnum,
                APROVADA,
                LABEL_NAO_DEVE_PERMITIR_CANCELAR
        );

        String mensagemCasoFalhaDataUmAno = String.format("%s � do tipo (%s), com a situa��o %s e com a data de processamento de ontem %s",
                LABEL_QUANDO_A_TRANSACAO,
                tipoTransacaoEnum,
                APROVADA,
                LABEL_DEVE_PERMITIR_CANCELAR
        );

        assertTrue(mensagemCasoFalhaDataHoje, retornarTransacao(APROVADA, hoje(), tipoTransacaoEnum).isPermiteCancelar());
        assertFalse(mensagemCasoFalhaDataUmAno, retornarTransacao(APROVADA, ontem(), tipoTransacaoEnum).isPermiteCancelar());
    }

    @Parameters(method = "listarTiposTransacoesSemprePermitemCancelar")
    @TestCaseName("Teste quando aprovada e com tipo de transa��o que sempre deve permitir cancelar n� {index} - Tipo da transa��o: {0}")
    @Test
    public void test__Deve__Permitir____Quando__Aprovada__Pelo__Tipo__Independente__Da__Data__Processamento(TipoTransacaoEnum tipoTransacaoEnum) {
        String mensagemCasoFalha = String.format("%s � do tipo (%s), com a situa��o %s e com a data de processamento do ano passado %s",
                LABEL_QUANDO_A_TRANSACAO,
                tipoTransacaoEnum,
                APROVADA,
                LABEL_NAO_DEVE_PERMITIR_CANCELAR
        );

        assertTrue(mensagemCasoFalha, retornarTransacao(APROVADA, Uteis.voltarDias(Calendario.hoje(), 365), tipoTransacaoEnum).isPermiteCancelar());
    }

    @Parameters(method = "listarTiposTransacoesNaoPermitemCancelar")
    @TestCaseName("Teste com tipo de transa��o que n�o deve permitir cancelar n� {index} - Tipo da transa��o: {0}")
    @Test
    public void test__Nunca__Deve__Permitir__Pelo__Tipo(TipoTransacaoEnum tipoTransacaoEnum) {
        String mensagemCasoFalha = String.format("%s � do tipo (%s), com a situa��o %s e com a data de processamento de hoje %s",
                LABEL_QUANDO_A_TRANSACAO,
                tipoTransacaoEnum,
                APROVADA,
                LABEL_DEVE_PERMITIR_CANCELAR
        );

        assertFalse(mensagemCasoFalha, retornarTransacao(APROVADA, hoje(), tipoTransacaoEnum).isPermiteCancelar());
    }

    @Test
    public void test__Deve__Permitir__Tipo__Transacao__ERede() {
        assertTrue(
                String.format("%s � do tipo (%s), com a situa��o %s e com a data de 60 dias atr�s %s",
                        LABEL_QUANDO_A_TRANSACAO,
                        E_REDE,
                        APROVADA,
                        LABEL_DEVE_PERMITIR_CANCELAR
                ),
                retornarTransacao(APROVADA, voltarDiasAPartirHoje(60), E_REDE).isPermiteCancelar()
        );
    }

    @Test
    public void test__Nao__Deve__Permitir__Tipo__Transacao__ERede() {
        assertFalse(
                String.format("%s � do tipo (%s), com a situa��o %s e com a data de 61 dias atr�s %s",
                        LABEL_QUANDO_A_TRANSACAO,
                        E_REDE,
                        APROVADA,
                        LABEL_NAO_DEVE_PERMITIR_CANCELAR
                ),
                retornarTransacao(APROVADA, voltarDiasAPartirHoje(61), E_REDE).isPermiteCancelar()
        );
    }

    @Test
    public void test__Deve__Permitir__Tipo__Transacao__Stone__Online() {
        assertTrue(
                String.format("%s � do tipo (%s), com a situa��o %s e com a data de 30 dias atr�s %s",
                        LABEL_QUANDO_A_TRANSACAO,
                        STONE_ONLINE,
                        APROVADA,
                        LABEL_DEVE_PERMITIR_CANCELAR
                ),
                retornarTransacaoComValor(APROVADA, voltarDiasAPartirHoje(30), STONE_ONLINE).isPermiteCancelar()
        );
    }

    @Test
    public void test__Nao__Deve__Permitir__Tipo__Transacao__Stone__Online() {
        assertFalse(
                String.format("%s � do tipo (%s), com a situa��o %s e com a data de 351 dias atr�s %s",
                        LABEL_QUANDO_A_TRANSACAO,
                        STONE_ONLINE,
                        APROVADA,
                        LABEL_NAO_DEVE_PERMITIR_CANCELAR
                ),
                retornarTransacao(APROVADA, voltarDiasAPartirHoje(351), STONE_ONLINE).isPermiteCancelar()
        );
    }

    private static TransacaoVO retornarTransacaoComSituacao(SituacaoTransacaoEnum situacaoTransacaoEnum) {
        TransacaoVO transacaoVO = new TransacaoVO();
        transacaoVO.setSituacao(situacaoTransacaoEnum);
        return transacaoVO;
    }

    private static TransacaoVO retornarTransacaoComSituacaoEData(SituacaoTransacaoEnum situacaoTransacaoEnum, Date dataProcessamento) {
        TransacaoVO transacaoVO = retornarTransacaoComSituacao(situacaoTransacaoEnum);
        transacaoVO.setDataProcessamento(dataProcessamento);

        return transacaoVO;
    }

    private static TransacaoVO retornarTransacao(SituacaoTransacaoEnum situacaoTransacaoEnum, Date dataProcessamento, TipoTransacaoEnum tipoTransacaoEnum) {
        TransacaoVO transacaoVO = retornarTransacaoComSituacaoEData(situacaoTransacaoEnum, dataProcessamento);
        transacaoVO.setTipo(tipoTransacaoEnum);
        transacaoVO.setTipoOrigem(tipoTransacaoEnum);
        if (tipoTransacaoEnum.equals(E_REDE)) {
            transacaoVO.setValor(1.0);
        }
        return transacaoVO;
    }

    private static TransacaoVO retornarTransacaoComValor(SituacaoTransacaoEnum situacaoTransacaoEnum, Date dataProcessamento, TipoTransacaoEnum tipoTransacaoEnum) {
        TransacaoVO transacaoVO = retornarTransacaoComSituacaoEData(situacaoTransacaoEnum, dataProcessamento);
        transacaoVO.setValor(10.0);
        transacaoVO.setTipo(tipoTransacaoEnum);
        transacaoVO.setTipoOrigem(tipoTransacaoEnum);
        return transacaoVO;
    }

    @SuppressWarnings("unused")
    private static List<SituacaoTransacaoEnum> listarSituacoesPermitemCancelar() {
        return asList(
                APROVADA,
                CONCLUIDA_COM_SUCESSO
        );
    }

    @SuppressWarnings("unused")
    private static List<SituacaoTransacaoEnum> listarSituacoesNaoPermitemCancelar() {
        return asList(
                SituacaoTransacaoEnum.NENHUMA,
                COM_ERRO,
                NAO_APROVADA,
                ERRO_CAPTURA,
                CANCELADA,
                DESCARTADA,
                ESTORNADA,
                PENDENTE
        );
    }

    @SuppressWarnings("unused")
    private static List<Date> listarDatasPermitemCancelar() {
        return Collections.singletonList(hoje());
    }

    @SuppressWarnings("unused")
    private static List<TipoTransacaoEnum> listarTiposTransacoesPermitemCancelarHoje() {
        return asList(
                AprovaFacilCB,
                VINDI,
                CIELO_ONLINE,
                MAXIPAGO,
                E_REDE,
                FITNESS_CARD,
                GETNET_ONLINE,
                STONE_ONLINE
        );
    }

    @SuppressWarnings("unused")
    private static List<TipoTransacaoEnum> listarTiposTransacoesPermitemApenasHoje() {
        return asList(
                NENHUMA,
                AprovaFacilCB,
                MAXIPAGO,
                FITNESS_CARD
        );
    }

    @SuppressWarnings("unused")
    private static List<TipoTransacaoEnum> listarTiposTransacoesNaoPermitemCancelar() {
        return asList(
                CIELO_DEBITO_ONLINE,
                E_REDE_DEBITO
        );
    }

    @SuppressWarnings("unused")
    private static List<TipoTransacaoEnum> listarTiposTransacoesSemprePermitemCancelar() {
        return asList(
                VINDI,
                CIELO_ONLINE
        );
    }
}
