package br.com.pactosolucoes.comuns.notificacao;

import controle.arquitetura.FuncionalidadeSistemaEnum;
import junitparams.JUnitParamsRunner;
import junitparams.Parameters;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 12/02/19
 */
@RunWith(JUnitParamsRunner.class)
public class RecursoSistemaTest {

    @Test
    public void nao_deve_haver_descricao_duplicada() {
        final Set<String> descricaoRecursos = new HashSet<String>(RecursoSistema.values().length);
        for (RecursoSistema recursoSistema : RecursoSistema.values()) {
            if (descricaoRecursos.contains(recursoSistema.getDescricao())) {
                Assert.fail("O recurso " + recursoSistema.getDescricao() + " foi duplicado!");
            }
            descricaoRecursos.add(recursoSistema.getDescricao());
        }
    }

    @Test
    @Parameters(method = "listarFuncionalidadesSistema")
    public void todas_funcionalidades_sistemas_devem_existir_no_enum_recurso_sistema(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        final RecursoSistema recursoSistema = RecursoSistema.fromDescricao(funcionalidadeSistemaEnum.name());
        if (recursoSistema == null) {
            Assert.fail("A funcionalidade de sistema " + funcionalidadeSistemaEnum.name() + " não está mapeada no enum " + RecursoSistema.class.getSimpleName());
        }
    }

    @SuppressWarnings("unused")
    private FuncionalidadeSistemaEnum[] listarFuncionalidadesSistema() {
        return FuncionalidadeSistemaEnum.values();
    }

}
